.DS_Store
__pycache__/
.pytest_cache
log.txt
.vscode
.vscode/
catalogs_pull/
products_pull/
.DS_Store
test/response*
test/venv*
track_*.json
*times.png
testfile_*.csv
test/*.png
test/failed*.log
test/*list.json
test/utils/chromedriver.DS_Store
test_ui/venv*
test_ui/.idea
test_ui/flight_save_latency.png
test_ui/flight_save_latency.json
test/extracted*

brands.json
Store_user_login_latency_list.json
Store_user_login_latency.png
test_*.png
test_ui/*.png
Results.csv
test_ui/product_configs/chromedriver
.idea
