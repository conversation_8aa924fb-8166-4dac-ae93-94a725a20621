---
include:
  - project: devops/autodevops
    file: templates/Security/SAST.gitlab-ci.yml
  - project: devops/autodevops
    file: templates/Security/Secret-Detection.gitlab-ci.yml
  - local: .*********************.yml

stages:
  - test
  - API Test
  - UI Test

RoleARN:
  stage: test
  script:
    - |
      echo "Setting variable AWS_ROLE_ARN"
      if [ "$TRIGGERED_JOB" = "dev" ] || [ "$TRIGGERED_JOB" = "pac" ]; then
        echo "Testing in DEV Account. Setting AWS_ROLE_ARN for that account"
        echo "AWS_ROLE_ARN=arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-$TRIGGERED_JOB" >> variables.env
      elif [ "$TRIGGERED_JOB" = "qa" ] || [ "$TRIGGERED_JOB" = "test" ]; then
        echo "Testing in TEST Account. Setting AWS_ROLE_ARN for that account"
        echo "AWS_ROLE_ARN=arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-$TRIGGERED_JOB" >> variables.env
      else
        echo "Unknown environment variable set! Value: $TRIGGERED_JOB"
        exit 1
      fi 
  allow_failure: true
  artifacts:
    reports:
      dotenv: variables.env
  rules:
    - if: $TRIGGERED_JOB != ""

API Product Python:
  extends: multi_currency_ui
  variables:
    MULTI_LANGUAGES_DISABLED: "1"
  stage: API Test
  script:
    - cd ./test/
    - pytest -vvv test_product_apis.py --junitxml=test_product_apis.xml
  rules:
    - if: $TRIGGERED_JOB != ""
  allow_failure: true

API Product Python Multi-language:
  extends: API Product Python
  variables:
    MULTI_LANGUAGES_DISABLED: ""
  allow_failure: false

API Brand Multi-language:
  extends: API Product Python
  script:
    - cd test
    - pytest -vvv test_brand_api_multi_language.py --junitxml=test_api_multi_language.xml
  variables:
    MULTI_LANGUAGES_DISABLED: ""
  allow_failure: false

API Brand Default-language:
  extends: API Product Python
  script:
    - cd test
    - pytest -vvv test_brand_api_default_language.py --junitxml=test_api_default_language.xml
  variables:
    MULTI_LANGUAGES_DISABLED: "1"
  allow_failure: false


API Airline Category Python:
  extends: multi_currency_ui
  stage: API Test
  script:
    - cd ./test/
    - pytest -vvv test_airline_category.py --junitxml=test_airline_category_apis.xml
  rules:
    - if: $TRIGGERED_JOB == "dev" || $TRIGGERED_JOB == "qa"
  allow_failure: true

Postman collection:
  id_tokens:
    AWS_OIDC_JTW:
      aud: ************.aws-oidc-provider.git.panasonic.aero
  stage: API Test
  image:
    name: gitdock.panasonic.aero:4567/pac/tools/marketplace-ground-pim-server-test/newman-python3.12
  variables:
    CI_PROJECT_DIR: /builds/pac/tools/marketplace-ground-pim-server-test
    POSTMAN_COLLECTION_DIR: $CI_PROJECT_DIR/postman-collection
    ARTIFACT_DATE: $(date +'%d%m%Y')
  before_script:
    - apk update && apk add --no-cache aws-cli jq libxml2-utils
    - newman --version
    - npm install -g newman-reporter-htmlextra
    - npm install -g newman-reporter-json
    - npm install -g moment
    - npm install -g newman-reporter-xunit
    - DATE=$(date +'%Y-%m-%d')
    - echo "*** Getting AWS credentials using role arn, $AWS_ROLE_ARN ***"
    - |
      echo "TEST... role arn from env variable, $AWS_ROLE_ARN  ***"
      echo "Setting variable AWS_ROLE_ARN"
      if [ "$TRIGGERED_JOB" = "dev" ] || [ "$TRIGGERED_JOB" = "pac" ]; then
        echo "Testing in DEV Account. Setting AWS_ROLE_ARN for that account"
        AWS_ROLE_ARN="arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-${TRIGGERED_JOB}"
      elif [ "$TRIGGERED_JOB" = "qa" ] || [ "$TRIGGERED_JOB" = "test" ]; then
        echo "Testing in TEST Account. Setting AWS_ROLE_ARN for that account"
        AWS_ROLE_ARN="arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-${TRIGGERED_JOB}"
      fi 
    - >
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s" 
      $(aws sts assume-role-with-web-identity 
      --role-arn $AWS_ROLE_ARN 
      --role-session-name "GitLabSession" 
      --web-identity-token $AWS_OIDC_JTW
      --duration-seconds 3600 
      --query "Credentials.[AccessKeyId,SecretAccessKey,SessionToken]" 
      --output text)) 2>/dev/null
    - |
      # Running the flask script
      cd test
      pip install -r requirements.txt --trusted-host artifinity.nextcloud.aero --index-url https://${ARTIFACTORY_USER}:${ARTIFACTORY_API_KEY}@artifinity.nextcloud.aero/artifactory/api/pypi/pypi/simple
      pip3 install flask
      nohup python3.12 flask_server.py > flask.log 2>&1 &
      sleep 5
  script:
  - |
    set +e
    exit_code=0
    # Checking triggered job
    if [[ "$TRIGGERED_JOB" == "pac" ]]; then
        echo "Running PAC Stack collections"
        newman run $POSTMAN_COLLECTION_DIR/Regression_tests_auto-populate-On_1.postman_collection.json -e $POSTMAN_COLLECTION_DIR/PAC_PAC_SIN-Flip.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_DEV_execution_report-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_DEV_execution_report-enf-${DATE}.html --reporter-xunit-export newman/junit.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Regression_tests_auto-populate-On_2.postman_collection.json -e $POSTMAN_COLLECTION_DIR/PAC_PAC_SIN-Flip.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_DEV_execution_report-2-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_DEV_execution_report-enf-2-enf-${DATE}.html --reporter-xunit-export newman/junit2.xml || exit_code=$?
      elif [[ "$TRIGGERED_JOB" == "dev" ]]; then
        echo "Running DEV collections"
        newman run $POSTMAN_COLLECTION_DIR/Regression_tests_auto-populate-On_1.postman_collection.json -e $POSTMAN_COLLECTION_DIR/PAC-DEV-Lewandowski.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_DEV_execution_report-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_DEV_execution_report-enf${DATE}.html --reporter-xunit-export newman/junit.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Regression_tests_auto-populate-On_2.postman_collection.json -e $POSTMAN_COLLECTION_DIR/PAC-DEV-Lewandowski.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_DEV_execution_report-2-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_DEV_execution_report-2-${DATE}.html --reporter-xunit-export newman/junit2.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Panasonic_APIs_Unique_Milestone1_OFF_Cases.json -e $POSTMAN_COLLECTION_DIR/PAC_DEV_Postman_API_environment_OFF.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_AUTOMAPP_OFF_execution_report-1-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_AUTOMAPP_OFF_execution_report-1-${DATE}.html --reporter-xunit-export newman/junit4.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Panasonic_APIs_Unique_Milestone1_ON_Cases.json -e $POSTMAN_COLLECTION_DIR/PAC-DEV-Lewandowski.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_AUTOMAPP_ON_execution_report-2-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_AUTOMAPP_ON_execution_report-2-${DATE}.html --reporter-xunit-export newman/junit5.xml || exit_code=$?
      elif [[ "$TRIGGERED_JOB" == "qa" ]]; then
        echo "Running QA collection"
        newman run $POSTMAN_COLLECTION_DIR/Regression_tests_auto-populate-On_1.postman_collection.json -e $POSTMAN_COLLECTION_DIR/PAC_QA_airMax.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_QA_execution_report-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_QA_execution_report-${DATE}.html --reporter-xunit-export newman/junit.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Regression_tests_auto-populate-On_2.postman_collection.json -e $POSTMAN_COLLECTION_DIR/PAC_QA_airMax.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_QA_execution_report-2-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_QA_execution_report-2-${DATE}.html --reporter-xunit-export newman/junit2.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Panasonic_APIs_Unique_Milestone1_OFF_Cases.json -e $POSTMAN_COLLECTION_DIR/PAC_QA_Postman_API_environment_OFF.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_AUTOMAPP_OFF_execution_report-3-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_AUTOMAPP_OFF_execution_report-3-${DATE}.html --reporter-xunit-export newman/junit4.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Panasonic_APIs_Unique_Milestone1_ON_Cases.json -e $POSTMAN_COLLECTION_DIR/PAC_QA_Postman_API_environment_ON.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_AUTOMAPP_ON_execution_report-4-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_AUTOMAPP_ON_execution_report-4-${DATE}.html --reporter-xunit-export newman/junit5.xml || exit_code=$?
      elif [[ "$TRIGGERED_JOB" == "test" ]]; then
        echo "Running TEST collection"
        newman run $POSTMAN_COLLECTION_DIR/Regression_tests_auto-populate-On_1.postman_collection.json -e $POSTMAN_COLLECTION_DIR/PAC_Test_env_CI-CD.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_TEST_execution_report-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_TEST_execution_report-${DATE}.html --reporter-xunit-export newman/junit.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Regression_tests_auto-populate-On_2.postman_collection.json -e $POSTMAN_COLLECTION_DIR/PAC_Test_env_CI-CD.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_TEST_execution_report-2-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_TEST_execution_report-2-${DATE}.html --reporter-xunit-export newman/junit2.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Panasonic_APIs_Unique_Milestone1_OFF_Cases.json -e $POSTMAN_COLLECTION_DIR/PAC_DEV_Postman_API_environment_OFF.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_AUTOMAPP_OFF_execution_report-1-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_AUTOMAPP_OFF_execution_report-1-${DATE}.html --reporter-xunit-export newman/junit4.xml || exit_code=$?
        newman run $POSTMAN_COLLECTION_DIR/Panasonic_APIs_Unique_Milestone1_ON_Cases.json -e $POSTMAN_COLLECTION_DIR/PAC-DEV-Lewandowski.postman_environment.json --reporters cli,xunit,json,htmlextra --reporter-json-export PAC_Core_Commerce_API_AUTOMAPP_ON_execution_report-2-${DATE}.json --reporter-htmlextra-export PAC_Core_Commerce_API_AUTOMAPP_ON_execution_report-2-${DATE}.html --reporter-xunit-export newman/junit5.xml || exit_code=$?
      fi
    if [ $exit_code -ne 0 ]; then
      echo "One of the tests failed!!!"
      exit $exit_code
    fi
  allow_failure: true
  artifacts:
    when: always
    paths:
      - "**/PAC_Core_Commerce_*_execution_report-*"
      - "**/*.xml"
      - test/flask.log
    reports:
      junit: test/newman/*.xml
      dotenv: variables.env
  rules:
    - if: $TRIGGERED_JOB != ""

JAMA - Publish Postman Results:
  stage: API Test
  image: artifinity.nextcloud.aero/docker/python:3.9
  variables:
    CI_PROJECT_DIR: /builds/pac/tools/marketplace-ground-pim-server-test
  script:
    - pip3 install --upgrade pip
    - pip3 install requests
    - pip3 install pytest
    - pip3 install py_jama_rest_client
    - pip3 install jamatest --trusted-host artifinity.nextcloud.aero --index-url
      https://${ARTIFACTORY_USER}:${ARTIFACTORY_API_KEY}@artifinity.nextcloud.aero/artifactory/api/pypi/pypi/simple
    - DATE=$(date +'%Y-%m-%d')
    - REPORT_FILE_DEV="PAC_Core_Commerce_API_DEV_execution_report-${DATE}.json"
    - REPORT_FILE_QA="PAC_Core_Commerce_API_QA_execution_report-${DATE}.json"
    - REPORT_FILE=""
    - |
      if [[ "$TRIGGERED_JOB" == "dev" ]]; then
        REPORT_FILE=$REPORT_FILE_DEV
      elif [[ "$TRIGGERED_JOB" == "qa" ]]; then
        REPORT_FILE=$REPORT_FILE_QA
      fi
    - |
      if [[ "$REPORT_FILE" != "" ]]; then
        python3 "$CI_PROJECT_DIR/marketplace.py" "$REPORT_FILE"
      fi
  needs:
    - job: Postman collection
  rules:
    - if: $TRIGGERED_JOB != "" && $SKIP_JAMA != "1"
      when: manual
  allow_failure: true

API - GET Schema validation:
  stage: API Test
  image: gitdock.panasonic.aero:4567/pac/tools/ate-ci-jobs/jobs/python_bdt:ubuntu20
  script:
    - cd test/
    - pip install -r requirements.txt --trusted-host artifinity.nextcloud.aero
      --index-url
      https://${ARTIFACTORY_USER}:${ARTIFACTORY_API_KEY}@artifinity.nextcloud.aero/artifactory/api/pypi/pypi/simple
    - pytest -vvv api_schema/test_validate.py
  needs:
    - job: Postman collection
  rules:
    - if: $TRIGGERED_JOB != ""
      when: manual
  allow_failure: true

Multi-currency - 4 currencies:
  extends: multi_currency_ui
  when: always
  needs: ['API Product Python Multi-language']
  timeout: 2h
  script:
    - cd ./test/
    - >
      if [[ -n "$TESTS_MARKER" ]]; then
        echo "Runing tests with marker : $TESTS_MARKER"
        echo "--------------------------------------------------- Starting to test_2_additional_display_currencies.py with marker : $TESTS_MARKER ---------------------------------------------------------"
        pytest -vvv multi_currency_ui/test_2_additional_display_currencies.py -m "$TESTS_MARKER" --junitxml=test_additional_display_currencies.xml
      else  
        echo "Running all tests!" 
        echo "--------------------------------------------------- Starting to test_2_additional_display_currencies.py ---------------------------------------------------------"
        pytest -vvv multi_currency_ui/test_2_additional_display_currencies.py --junitxml=test_additional_display_currencies.xml
      fi
  rules:
    - if: $TRIGGERED_JOB != ""
  artifacts:
    reports:
      dotenv: variables.env

Multi-currency - Default currency:
  extends: Multi-currency - 4 currencies
  script:
    - cd ./test/
    - pytest -vvv multi_currency_ui/test_1_default_currency.py -m reg
      --junitxml=test_default_currency.xml
  needs:
    - job: Multi-currency - 4 currencies
  rules:
    - if: $TRIGGERED_JOB != ""
      when: manual

Multi-currency - All Currencies:
  extends: Multi-currency - Default currency
  script:
    - cd ./test/
    - >
      if [[ -n "$TESTS_MARKER" ]]; then
        echo "Runing tests with marker : $TESTS_MARKER"
        echo "--------------------------------------------------- Starting to test_3_additional_display_currencies.py with marker : $TESTS_MARKER ---------------------------------------------------------"
        pypytest -vvv multi_currency_ui/test_3_all_display_currencies.py -m "$TESTS_MARKER" --junitxml=test_all__currencies.xml 
      else  
        echo "Running all tests!" 
        echo "--------------------------------------------------- Starting to test_3_additional_display_currencies.py ---------------------------------------------------------"
        pytest -vvv multi_currency_ui/test_3_all_display_currencies.py -m reg --junitxml=test_all__currencies.xml
      fi
  needs:
    - job: Multi-currency - Default currency



Brands - MultiLanguage:
  extends: multi_currency_ui
  when: always
  timeout: 2h
  script:
    - cd ./test_ui/
    - >
      if [[ -n "$TESTS_MARKER" ]]; then
        echo "Running tests with marker : $TESTS_MARKER"
        echo "--------------------------------------------------- Starting to test_brand_multi_language.py with marker : $TESTS_MARKER ---------------------------------------------------------"
        pytest -vvv test_modules/test_brand_multi_language.py --env $TRIGGERED_JOB -m "$TESTS_MARKER" --junitxml=test_brand_multi-language.xml
      else  
        echo "Running all tests!" 
        echo "--------------------------------------------------- Starting to test_brand_multi_language.py ---------------------------------------------------------"
        pytest -vvv test_modules/test_brand_multi_language.py --env $TRIGGERED_JOB --junitxml=test_brand_multi-language.xml
      fi
  rules:
    - if: $TRIGGERED_JOB != ""
  artifacts:
    reports:
      dotenv: variables.env
      junit: test_ui/test_*.xml
    paths:
      - test_ui/PAC_UI_AUTOMATION_execution_report.html
      - "**/test_*.png"
      - test_ui/test_*.xml
  
Brands - Default Language:
  extends: Brands - MultiLanguage
  variables:
    MULTI_LANGUAGES_DISABLED: "1"
  script:
    - cd ./test_ui/
    - >
      if [[ -n "$TESTS_MARKER" ]]; then
        echo "Running tests with marker : $TESTS_MARKER"
        echo "--------------------------------------------------- Starting to test_brand_default_language.py with marker : $TESTS_MARKER ---------------------------------------------------------"
        pytest -vvv test_modules/test_brand_default_language.py --env $TRIGGERED_JOB  -m "$TESTS_MARKER" --junitxml=test_brand_default_language.xml
      else
        echo "Running all tests!" 
        echo "--------------------------------------------------- Starting to test_brand_default_language.py ---------------------------------------------------------"
        pytest -vvv test_modules/test_brand_default_language.py --env $TRIGGERED_JOB  --junitxml=test_brand_default_language.xml
      fi

########################################## UI AUTOMATION FOR NIGHTLY RUNS ########################################## 
# PDC GUI Automation code
# -----------------------
UI/CSV PAC Sector:
  extends: multi_currency_ui
  when: always
  timeout: 12h
  variables:
    UI_AUTOMATION_SCRIPTS_DIR: /builds/pac/tools/marketplace-ground-pim-server-test/test_ui/
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_sector_pac_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_sector.xml
  rules:
    - if: $SCHEDULED_NIGHTLY != ""
  artifacts:
    when: always
    reports:
      junit: test_ui/test_*.xml
    paths:
      - test_ui/PAC_UI_AUTOMATION_execution_report.html
      - '**/*.png'

#contains only 2 testcases for below module
UI PAC Store:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_store_pac_ui.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_store.xml

UI/CSV Speciality Attribute:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_specialityattribute_pac_store_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_speciality_attribute.xml
  needs:
    - UI PAC Store
  
UI/CSV Product:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_product_pac_store_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_product.xml
  needs:
    - job: UI/CSV Speciality Attribute ## using needs to make the jobs that need sns validation in series

UI/CSV Flight:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_flight_pac_airline_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_flight.xml
  needs:
    - job: UI/CSV Product

UI PAC Airline:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_airline_pac_ui.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_airline.xml
  needs:
    - UI/CSV Flight

UI/CSV Airline Category:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_airlinecategory_pac_airline_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_airlinecategory.xml
  needs:
    - UI PAC Airline

UI/CSV Catalog Assignment ON cases:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_catalogAssignment_pac_airline_ui_ON_Cases.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_catalogassignment_ONcases.xml
  needs:
    - UI/CSV Airline Category

UI/CSV Catalog Assignment OFF cases:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_catalogAssignment_pac_airline_ui_OFF_Cases.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_catalogassignment_OFFcases.xml
  needs:
    - UI/CSV Catalog Assignment ON cases

#contains only 1 testcase for below module
UI PAC Monthly Catalog Load:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_monthlycatalogloads_pac_ui.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_monthlycatalogloads.xml
  needs:
    - UI/CSV Catalog Assignment OFF cases

# jobs without sns
UI/CSV PAC Aircraft:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_aircraft_pac_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_aircraft.xml
  
#contains only 2 testcases for below module
UI/CSV PAC Airport:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_airport_pac_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_airport.xml
  needs:
    - UI/CSV PAC Aircraft

#contains only 2 testcases for below module
UI PAC Cabinclass:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_cabinclass_pac_ui.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_cabinclass.xml
  needs:
    - UI/CSV PAC Airport

UI/CSV Catalog:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_catalog_pac_store_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_catalog.xml
  needs:
    - UI PAC Cabinclass

UI/CSV Category:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_category_pac_store_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_category.xml
  needs:
    - UI/CSV Catalog

#contains only 2 testcases for below module
UI PAC Fulfillment:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_fulfillment_pac_ui.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_fulfillment.xml
  needs:
    - UI/CSV Category

#contains only 2 testcases for below module
UI PAC Mealcode:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_mealcode_pac_ui.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_mealcode.xml
  needs:
    - UI PAC Fulfillment

#contains only 2 testcases for below module
UI PAC RootType:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_roottype_pac_ui.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_roottype.xml
  needs:
    - UI PAC Mealcode

#contains only 2 testcases for below module
UI/CSV PAC RouteGroup:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_routegroup_pac_ui_csv.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_routegroup.xml
  needs:
    - UI PAC RootType

UI StoreLocation:
  extends: UI/CSV PAC Sector
  script:
    - pip install -r test_ui/requirements.txt
    - cd test_ui
    - python -m pytest test_modules/test_storelocation_pac_store_ui.py --env $ENVIRONMENT -s --html=PAC_UI_AUTOMATION_execution_report.html --log-level=INFO --self-contained-html --junitxml=test_storelocation.xml
  needs:
    - UI/CSV PAC RouteGroup


# Credencys GUI Automation code
# -----------------------------
Credencys UI Automation:
  extends: multi_currency_ui
  when: always
  timeout: 9h
  variables:
    UI_AUTOMATION_DIR: /builds/pac/tools/marketplace-ground-pim-server-test/test_ui/credencys_test_ui
    ARTIFACT_DATE: $(date +'%d%m%Y')
    ENV: DEV
    SCOPE: P1.1
    JAMA_ENABLED: "true"
    MAX_RETRIES: 5
  script:
    - echo "$ENV"
    - echo "$SCOPE"
    - python -m pip install -U pip
    - pip install --upgrade setuptools==67.0.0
    - pip install -r $UI_AUTOMATION_DIR/requirements.txt
    - pip install jamatest --trusted-host artifinity.nextcloud.aero --index-url https://<EMAIL>:<EMAIL>/artifactory/api/pypi/pypi/simple
    - |
      if [[ "$SCOPE" == "ALL" ]]; then
        pytest --junitxml=junit.xml $UI_AUTOMATION_DIR/ --env $ENV
      else
        pytest --junitxml=junit.xml $UI_AUTOMATION_DIR/$SCOPE/ --env $ENV
      fi
  rules:
    - if: $SCHEDULED_NIGHTLY != ""
  artifacts:
    when: always
    reports:
      junit: test_ui/test_*.xml
    paths:
      - test_ui/PAC_UI_AUTOMATION_execution_report.html
      - '**/*.png'


####################################################### Mapper
Mapper Full build:
  extends: multi_currency_ui
  stage: test
  variables:
    TEST_MARKER: full_build
  script:
    - cd ./test/
    - pytest -vvs mapper/test_mapper_data_generator.py -m $TEST_MARKER --junitxml=test_mapper_data_generator.xml
    - pytest -vvs mapper/test_mapper_data_fetcher.py --junitxml=test_mapper_data_fetcher.xml
    - pytest -vvs mapper/test_data_validation.py --junitxml=test_mapper_data_validation.xml
  rules:
    - if: $MAPPER_TEST_ENV == "dev"
  allow_failure: false

Mapper Delta - Metadata change:
  extends: "Mapper Full build"
  stage: test
  variables:
    TEST_MARKER: update_metadata
  allow_failure: true
  needs:
    - job: "Mapper Full build"
  

Mapper Delta - New product on existing Catalog:
  extends: "Mapper Full build"
  stage: test
  variables:
    TEST_MARKER: new_product
  allow_failure: true
  needs:
    - job: "Mapper Delta - Metadata change"

  
Mapper Delta - New Menus:
  extends: "Mapper Full build"
  stage: test
  variables:
    TEST_MARKER: new_menu
  allow_failure: true
  needs:
    - job: Mapper Delta - New product on existing Catalog


