import test_mapper
import conftest
import json

def main():
    airline_session = conftest.airline_session()
    airports = conftest.airports(airline_session)
    sectors = conftest.sectors(airline_session)
    store_http_session = conftest.store_http_session()
    
    data = test_mapper.test_mapped_get_data(airline_session, airports, sectors, store_http_session)
    print("Data:", data)
    
    with open('data_mapped.json', 'w') as f:
        json.dump(data, f, indent=4)
    
    print("Data written to data_mapped.json")

if __name__ == "__main__":
    main()