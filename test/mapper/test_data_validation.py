import os
import json
import logging
import pytest
from pathlib import Path
from utils.data_validation_helpers import *

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_data_validation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Environment variable for the airline code, default to 00000WN5
# AIRLINE_CODE = os.environ.get('AIRLINE_CODE', '00000VVW')
# PRODUCT_INDENT = 'mktplvvw'
# KIT_VERSION = os.environ.get('KIT_VERSION', '193')
# SIZE_SELECTORS = ["15", "20", "25", "30"]
# MAPPER_CONFIGURATION = {"poster-1_1": {"product-image-1",  "product-image-2", "product-image-3", "product-image-4", "product-image-5", "product-image-6", "product-image-7"}, "gallery-1_1": {"gallery-image-1"}}

AIRLINE_CODE = os.environ.get('AIRLINE_CODE', '00000V5V')
PRODUCT_INDENT = 'mktplv5v'
SIZE_SELECTORS = ["15", "20", "25"]
MAPPER_CONFIGURATION = {"poster-2_1": {"product-image-1",  "product-image-4"}, "poster-4_3": {"product-image-2", "product-image-3"}}
CATEGORY_MAPPER_CONFIG = {"main-1_1":{"category-image-3", "category-image-4"}, "main-2_1":{"category-image-2"}, "main-4_3":{"category-image-1"}, "banner-4_1": {"category-image-5"}, "background-16_9": {"category-image-6"}}
SMOKE_TEST = os.environ.get('SMOKE_TEST', 'False').lower() in ('true', '1', 't')

# Path to the output tgz file
EXTRACT_DIR = f'extracted_{AIRLINE_CODE}'


@pytest.fixture(scope="session")
def input_products():
    return load_data("products_s3.json")

@pytest.fixture(scope="session")
def input_catalog_assignments():
    return load_data("applicableCatalogAssignment_s3.json")

@pytest.fixture(scope="session")
def input_categories():
    return load_data("Categories_s3.json")

@pytest.fixture(scope="session")
def input_speciality_atributes():
    return load_data("speciality_atributes_s3.json")



def test_catalog_assignments_count( input_catalog_assignments):
    """Test that the number of catalog assignments matches."""
    filter_ids = get_filter_ids_from_catalog_assignments(input_catalog_assignments)
    logger.info(f"Found {len(filter_ids)} filter IDs in catalog assignments: {filter_ids}")
    essentialsManifest = get_essential_manifest(EXTRACT_DIR, AIRLINE_CODE)

    errors = []

    for filter_id in filter_ids:
        file_path_json = os.path.join(EXTRACT_DIR,AIRLINE_CODE, "filters" ,  str(filter_id)+".json")
        file_path_bson = os.path.join(EXTRACT_DIR,AIRLINE_CODE, "filters" ,  str(filter_id)+".bson")

        ## check json
        if not os.path.exists(file_path_json):
            error_msg = f"filterId json file not found: {file_path_json} in the kit..."
            print(error_msg)
            errors.append(error_msg)
        else:
            print(f"Found filterID in file in filesystem...... {file_path_json}")
        ## check bson 
        if not os.path.exists(file_path_bson):
            error_msg = f"filterID bson file not found: {file_path_bson} in the kit..."
            print(error_msg)
            errors.append(error_msg)
        else:
            print(f"found the filter bson in file in filesystem...... {file_path_bson}")

        ### check if the file exists in the nbdManifest
        if check_image_path_in_nbd_manifest(essentialsManifest, file_path_bson, f"extracted_{AIRLINE_CODE}/{AIRLINE_CODE}/"):
            print(f"Found manifest including the filter id: {filter_id}. {file_path_bson}")
        else:
            print(f"filterId {filter_id} not found in manifest")
            errors.append(f"filterID: {filter_id} not found in nbd Essential Manifest")
    if errors:
        print("all errors")
        print(errors)
        for error in errors:
            pytest.fail(f"Validation failed: {error}")

def test_catalog_assignments_match_origin_destination( input_catalog_assignments): ### needs work...
    """Test that catalog assignments match origin-destinations from sectors data."""
    catalog_assignments = input_catalog_assignments

def test_products_in_catalog_assignments(  input_catalog_assignments):
    """Test that all products in catalog assignments are present in the extracted data."""
    product_ids = extract_product_ids(input_catalog_assignments)
    
    # If in smoke test mode, just check a sample of products
    if SMOKE_TEST:
        sample_size = min(5, len(product_ids))
        product_ids = list(product_ids)[:sample_size]
        logger.info(f"Using {sample_size} products for smoke test")

    # Check that all product bson files are in the list...
    missing_products = []
    missing_1_product_file = []
    for product_id in product_ids:
        product_id_cd =  PRODUCT_INDENT+str(product_id)
        product_data_kit = find_and_load_bson_files(EXTRACT_DIR, product_id_cd )
        product_data_kit_json = find_and_load_json_files(EXTRACT_DIR, product_id_cd)
        if len(product_data_kit) == 0:
            missing_products.append(product_id_cd)
            print(f"Product json file not found {product_id_cd}")
        elif len(product_data_kit) == 1:
            missing_1_product_file.append(product_id_cd)
        elif len(product_data_kit) == 2:
            pass
            # print("succesfully found 2 bson files:")
        else:
            pytest.fail("found extra files.....?")
    assert len(missing_products) == 0 and len(missing_1_product_file) == 0, f"\nMissing both bson files for product {missing_products} \nMissing 1 bson file for (-var or regular product file) {missing_1_product_file}"

    """ TODO
    Check the json data contents as per expected... Filters, Categories
    Check the manifests contain the bson files
    """  
    

def test_product_images_present(input_catalog_assignments, input_products):
    """Test that product images are present and referenced correctly."""
    products = input_products
    applicable_catalog = input_catalog_assignments
    # Extract product IDs from the applicable catalog assignments
    applicable_product_ids = extract_product_ids(input_catalog_assignments)
    print(applicable_product_ids)
    # Filter the products to only include those in the applicable catalog assignments
    applicable_products = {k: v for k, v in products.items() if k in applicable_product_ids}
    logger.info(f"Found {len(applicable_products)} of {len(applicable_product_ids)} applicable products in input data")
    # If in smoke test mode, just check a sample of catalog assignments and their products
    if SMOKE_TEST:
        if len(applicable_catalog) > 3:
            sample_catalog = applicable_catalog[:3]
            logger.info(f"In Skoke test mode, using 3 of {len(applicable_catalog)} catalog assignments for smoke test. Reducing the number of products to check")
            # Extract product IDs from the sample catalog assignments
            smoke_product_ids = set()
            for assignment in sample_catalog:
                if 'products' in assignment:
                    for product in assignment['products']:
                        if 'id' in product:
                            smoke_product_ids.add(str(product['id']))
            # Filter the applicable products to only include those in the sample catalog assignments
            applicable_products = {k: v for k, v in applicable_products.items() if k in smoke_product_ids}
            logger.info(f"Using {len(applicable_products)} products from 3 catalog assignments for smoke test")
        else:
            logger.info(f"Using all {len(applicable_catalog)} catalog assignments for smoke test")
    total_products_with_images = 0
    total_products_checked = 0

    for product_id, product_data in applicable_products.items():
        if 'images' not in product_data or len(product_data['images']) == 0:
            # print(f"image not available for id {product_id}")
            continue

        total_products_with_images += 1
        component_id = f"nbdManifest_{product_id}-images.xml"
        product_data_kit = find_and_load_bson_files(EXTRACT_DIR, PRODUCT_INDENT+str(product_id))
        if not product_data or len(product_data_kit) == 0:
            pytest.fail(f"Product json file not found {product_id}")
        # print("product_data_kit::")
        # print(json.dumps(product_data_kit, indent=5))
        for image in product_data['images']:
            errors = validate_product_images2(
                payloadData=product_data_kit[0],
                required_selectors=SIZE_SELECTORS,
                extracted_data_path=EXTRACT_DIR,
                component_id=component_id,
                AIRLINE_CODE = AIRLINE_CODE,
                image_types=MAPPER_CONFIGURATION,
                image=image)
            if errors:
                print("all errors")
                print(errors)
                for error in errors:
                    pytest.fail(f"Validation failed: {error}")
        total_products_checked += 1
    if total_products_checked == 0:
        pytest.skip("Products were not checked for images")


def test_categories_in_catalog_assignments(input_catalog_assignments):
    """Test that categories in catalog assignments are present in the extracted data."""
    applicable_catalog = input_catalog_assignments
    
    if SMOKE_TEST and len(applicable_catalog) > 3:
        logger.info(f"Limiting to 3 of {len(applicable_catalog)} catalog assignments for smoke test")
        applicable_catalog = applicable_catalog[:3]

    applicable_category_ids = extract_category_ids(applicable_catalog)

    logger.info(f"Found {len(applicable_category_ids)} category IDs in applicable catalog assignments")

    missing_categories = []
    for catId in applicable_category_ids:
        catIdCd = f"CAT{catId}"
        category_data = find_and_load_bson_files(EXTRACT_DIR, catIdCd)
        product_data_kit_json = find_and_load_json_files(EXTRACT_DIR, catIdCd)
        if len(category_data) == 0:
            missing_categories.append(catIdCd)
            print(f"Category json file not found {catIdCd}")
        elif len(category_data) == 1:
            pass
            # print("succesfully found 2 bson files:")
        else:
            pytest.fail("found extra files.....?")
    assert len(missing_categories) == 0, f"\nMissing both bson files for Category {missing_categories}"


def test_category_images_present(input_catalog_assignments, input_categories):
    """Test that category images are present and referenced correctly."""
    total_categories_with_images = 0
    total_categories_checked = 0

    for cat_id, cat_data in input_categories.items():
        if 'images' not in cat_data or len(cat_data['images']) == 0:
            # print(f"image not available for id {cat_id}")
            continue
        total_categories_with_images += 1
        manifest_root = get_nbdManifest_category(EXTRACT_DIR, cat_id, AIRLINE_CODE)
        if manifest_root is None:
            pytest.fail(f"No manifest found for product {cat_id}, file")
            continue
        total_categories_checked += 1
        product_data_kit = find_and_load_bson_files(EXTRACT_DIR, "CAT"+cat_id)
        if not product_data_kit or len(product_data_kit) == 0:
            raise Exception(f"Category json file not found {cat_id}")
        # print("product_data_kit::")
        # print(json.dumps(product_data_kit, indent=5))
        for image in cat_data['images']:
            errors = validate_category_images(
                payloadData=product_data_kit[0],
                required_selectors=SIZE_SELECTORS,
                extracted_data_path=EXTRACT_DIR,
                component_id=cat_id,
                AIRLINE_CODE = AIRLINE_CODE,
                image_types=CATEGORY_MAPPER_CONFIG,
                image=image)
            if errors:
                print("all errors")
                print(errors)
                for error in errors:
                    pytest.fail(f"Validation failed: {error}")
    if total_categories_checked == 0:
        pytest.skip("Categories were not checked for images")


def test_speciality_attributes_present(input_products):
    """Test that specialty attributes are present for products referencing them."""
    all_attr_ids = get_specialty_attribute_ids_from_products(input_products)
    logger.info(f"Found {len(all_attr_ids)} specialty attribute IDs in applicable products")

    if not all_attr_ids:
        logger.warning("No specialty attributes referenced in applicable products, skipping test")
        pytest.skip("No specialty attributes referenced in applicable products")

    total_products_with_attrs = 0
    total_products_checked = 0
    total_attrs_found = 0
    total_attrs_missing = 0

    specialty_attr_json_path = os.path.join(EXTRACT_DIR, AIRLINE_CODE, 'descriptors', 'specialityAttributeIDs.json')
    if not os.path.exists(specialty_attr_json_path):
        logger.error(f"specialityAttributeIDs.json file not found at {specialty_attr_json_path}")
        pytest.fail("specialityAttributeIDs.json file not found in the kit descriptors directory")
    
    try:
        with open(specialty_attr_json_path, 'r') as f:
            specialty_attr_data = json.load(f)
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Error loading specialityAttributeIDs.json: {e}")
        pytest.fail("Could not load specialityAttributeIDs.json")
    cached_attr_ids = set() ### for caching
    for product_id, product_data in input_products.items():
        if ('specialityAttributes' not in product_data or
            not product_data['specialityAttributes']):
            continue
        attribute_ids_input = [spa["id"] for spa in product_data["specialityAttributes"]]
        total_products_with_attrs += 1

        # Get the product JSON from the extracted data
        product_id_cd =  PRODUCT_INDENT+str(product_id)
        extracted_products = find_and_load_bson_files(EXTRACT_DIR, product_id_cd )
        extracted_product   = extracted_products[0]
        if extracted_product is None:
            pytest.fail(f"No JSON found for product {product_id}")
            continue
        total_products_checked += 1
        if 'descriptors' in extracted_product and 'specialityAttributeIDs' in extracted_product['descriptors']:
            # Get the specialty attribute IDs from the input data
            for attr in product_data['specialityAttributes']:
                attr_id = int(attr['id'])
                assert attr_id in cached_attr_ids or check_id_exist(specialty_attr_data, attr_id), f"Failed to find {attr_id} in the kit's specialityAttributeID.json file"
                cached_attr_ids.add(attr_id) ## add now after checked file
            extracted_attr_ids = set(extracted_product['descriptors']['specialityAttributeIDs'])
            assert set(attribute_ids_input) == extracted_attr_ids, "failed to find all spa for product"            
        else:
            pytest.fail(f"No specialityAttributeIDs found in product {product_id}. Expected {attribute_ids_input}")

    logger.info(f"Checked {total_products_checked} of {total_products_with_attrs} products with specialty attributes")
    logger.info(f"Found {total_attrs_found} attributes, {total_attrs_missing} attributes missing")
    # If no products with attributes were checked, skip the test
    if total_products_checked == 0:
        logger.warning("No products with specialty attributes could be checked, skipping test")
        pytest.skip("No products with specialty attributes could be checked")

def test_speciality_attribute_images_present(input_speciality_atributes, input_catalog_assignments, input_products):
    """Test that specialty attribute images are present, referenced correctly and exist in the nbdManifest.xlm"""
    total_images = 0
    found_images = 0
    missing_images = []
    found_images_manifest = 0
    missing_images_manifest = []

    # Check if the specialityAttributeIDs.json file exists
    specialty_attr_json_path = os.path.join(EXTRACT_DIR, AIRLINE_CODE, 'descriptors', 'specialityAttributeIDs.json')
    with open(specialty_attr_json_path, 'r') as f:
            specialty_attr_data = json.load(f)

    manifest = parse_xml_file(os.path.join(EXTRACT_DIR, AIRLINE_CODE,  'nbdManifest_essential.xml') )
    for attr in specialty_attr_data:
        attr_id = attr.get('id', 'unknown')
        
        if 'images' in attr:
            for image_type, sizes in attr['images'].items():
                for size, image_list in sizes.items():
                    for image_info in image_list:
                        if 'filename' in image_info:
                            total_images += 1
                            image_path = image_info['filename']
                            
                            full_path = os.path.join(EXTRACT_DIR, image_path)
                            print("the full path:", full_path)
                            if os.path.exists(full_path) and os.path.isfile(full_path):
                                found_images += 1
                                logger.info(f"Found image {image_path} for specialty attribute {attr_id}")
                            else:
                                missing_images.append((attr_id, image_path))
                                logger.error(f"Missing image {image_path} for specialty attribute {attr_id}")

                            print("Checking if the image exists in the nbdManifest")
                            if check_image_path_in_nbd_manifest(manifest, image_path, AIRLINE_CODE+"/"):
                                found_images_manifest += 1
                            else:
                                missing_images_manifest.append((attr_id, image_path))
                                logger.warning(f"Image {image_path} spa not found in manifest")
    
    logger.info(f"Found {found_images} of {total_images} specialty attribute images")
    
    if missing_images:
        for attr_id, image_path in missing_images:
            logger.error(f"Missing image path from specialityAttributeIDs.json: {image_path} for attribute {attr_id}")
        
        assert False, f"{len(missing_images)} specialty attribute images are missing"
    else:
        logger.info("All specialty attribute images from specialityAttributeIDs.json are present")


    if missing_images_manifest:
        for attr_id, image_path in missing_images_manifest:
            logger.error(f"Missing image path from specialityAttributeIDs.json: {image_path} for attribute {attr_id}")
        
        assert False, f"{len(missing_images_manifest)} specialty attribute images are missing"
    else:
        logger.info("All specialty attribute images from specialityAttributeIDs.json are present in nbdMAnifest essentials")

