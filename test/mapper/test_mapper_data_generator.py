import os
import json
import logging
import pytest
from pathlib import Path
from utils.data_validation_helpers import *
from utils.helper import *
from datetime import datetime, timezone, timedelta
import random
from requests.adapters import HTTPAdapter
from enum import Enum
from time import sleep


### ID DEV SIA
EXISTING_SPA_ID = 10086
EXISTING_CATEGORY_ID = 61077
EXISTING_PRODUCT_ID = 10066
AIRLINE_CLIENT_ID = "itp12rdhan"
AIRLINE_CLIENT_SECRET = "wif0xzrbk#a2iy2p5m4n"
STORE_CLIENT_ID = "2isczlx0sx"
STORE_CLIENT_SECRET = "z5n9ge-$pr$5$$bnz##m"
TESTING_CATEGORY_ID = 123
S3_AIRLINE_BUCKET = "sia-catalog-kit-files-924166462419"
S3_PRODUCTS_BUCKET = "catalog-kit-product-files-924166462419"
STORE_CATEGORY = 10005
AIRLINE_STACK_NAME = 'marketplace-ground-catalog-infrastructure-resources-SIAStack-11DANFXYYS859' ### need to find a way to use this to determine data below
LOGICAL_ID_TASK_DEF = 'CatalogKitGenEcsTask'   
TASK_DEF = "arn:aws:ecs:us-west-2:924166462419:task-definition/marketplace-ground-catalog-infrastructure-resources-SIAStack-11DANFXYYS859-CatalogKitGenEcsTask-CapfdS8jMvbm:1" 
ECS_CLUSTER = 'marketplace-ground-catalog-infrastructure-resources-ECSCluster-wksrrS6zauVn'
ECS_TASK_ROLE_ARN = 'arn:aws:iam::924166462419:role/sia-ecs-task-role-catalog-kit-gen'
ECS_EXCECUTION_ROLE_ARN = 'arn:aws:iam::924166462419:role/marketplace-ground-catalo-CatalogKitGenECSTaskExec-1IVQ5O9VQSX1J'


class pFiles(Enum):
    PRODUCTS = "products"
    SPECIALITY_ATTRIBUTES = "speciality_attributes"
    BRANDS = "brands" ## NEEDS Confirmation

class aFiles(Enum):
    CATEGORIES = "categories" 
    CATALOG_ASSIGNMENTS = "catalog_assignments"
     
def getEventHandlerS3ProductsFile(folder: pFiles, id, retries=3, delayFirstFetch=5):
    sleep(delayFirstFetch) 
    s3 = boto3.client('s3')
    for i in range(retries):
        try:
            return fetch_json_from_s3(s3, S3_PRODUCTS_BUCKET,  folder.value + "/" + str(id) + "/" +str(id) + ".json" )
        except:
            if i == retries-1:
                raise
            else: 
                print(f"Could not find the sns in retry {retries-1}. Retrying after 5 seconds")
                sleep(5)

def getEventHandlerS3AirlineFile(folder: aFiles, id, retries=3, delayFirstFetch=5):
    sleep(delayFirstFetch)
    s3 = boto3.client('s3')
    for i in range(retries):
        try:
            if aFiles.CATEGORIES == folder:
                file_path = folder.value + "/CAT" + str(id) + "/CAT" +str(id) + ".json" 
            elif aFiles.CATALOG_ASSIGNMENTS == folder:
                file_path = folder.value + "/" +str(id) + ".json" 
            return fetch_json_from_s3(s3, S3_AIRLINE_BUCKET, file_path  )
        except:
            if i == retries-1:
                raise
            else: 
                print(f"Could not find the sns in retry {retries-1}. Retrying after 5 seconds")
                sleep(5)

def create_catalog_assigment_helper( flight_id, cabin_class, from_date, to_date, all_products, store_http_session, airline_session):
    leaf_cats = [ 61078, 61079, 18947, 18063, 18062, 18038, 61199, 61201, 61192, 61191, 61137, 61138, 61139, 61140 ]    # 14 
    selected_products = random.sample(all_products, 70)
    product_ids=[]
    product_skus = []
    for i in selected_products:
        product_ids.append(i['id'])
        product_skus.append(i['sku'])
        
    products_per_category = len(product_ids)//len(leaf_cats)
    products_airline_category_mapping = []
    
    for i, val in enumerate(product_ids):
        category_index = i // products_per_category
        products_airline_category_mapping.append({"productId": val, "airlineCategory": [leaf_cats[category_index]]})

    ps = []
    cur_group = []
    for i, val in enumerate(product_ids):
        cur_group.append({
                "productId": val,
                "sequenceNumber": i % products_per_category +1
        })
        if (i+1) % products_per_category == 0 or i == len(product_ids) -1:
            ps.append(cur_group)
            cur_group = []
            
    cat_data = {
        "products": [
        "TEST_01"
        ]
    }
    cat_data['products'] = product_skus
    name = "api_auto_route_"+ uuid_random_name()
    catalog_response = post_catalog(store_http_session, cat_data, name  )        
    if catalog_response.status_code != 200:
        print("failed to create a catalog...")
        return
    catalog_id = catalog_response.json()['id'] 


    data = {
        "name": "api_test_benchmark_ca_" + uuid_random_name(),
        "catalog": {
            "id": catalog_id,
            "products": products_airline_category_mapping
        },
        "flight":[{
            "id": flight_id
        }],
        "catalogFromDate": from_date,
        "catalogToDate": to_date,
        "cabinClass": [
            cabin_class
        ]
    }    
    leaf_cats = [ 61078, 61079, 18947, 18063, 18062, 18038, 61199, 61201, 61192, 61191, 61137, 61138, 61139, 61140 ]  ## Need to create these categories using POST Category api
    route_sequence = {
                        "routeSequences":{
                            "categories": [
                                {
                                    "id":  61077, ### Dinner
                                    "sequenceNumber": 1,
                                    "subCategories": [
                                        {
                                            "id": 61078,  ## Non Vegetarian
                                            "sequenceNumber": 1,
                                            "products": ps[0]
                                        },
                                        {
                                            "id": 61079, ## Vegetarian
                                            "sequenceNumber": 2,
                                            "products": ps[1]
                                        },
                                    ]
                                },
                                {
                                    "id": 15532, ## level 0 
                                    "sequenceNumber": 2,
                                    "subCategories": [
                                        {
                                            "id": 18947,  ## Amenities
                                            "sequenceNumber": 1,
                                            "products": ps[2]
                                        },
                                        {
                                            "id": 17949, ## Beverages & more
                                            "sequenceNumber": 2,
                                            "subCategories": [
                                                {
                                                    "id": 18037, ## Snacks
                                                    "sequenceNumber": 1,
                                                   "subCategories": [
                                                        {
                                                            "id": 18063, ## noddles
                                                            "sequenceNumber": 1,
                                                            "products": ps[3]
                                                        },
                                                        {
                                                            "id": 18062, ## Snacks
                                                            "sequenceNumber": 2,
                                                            "products": ps[4]
                                                        },
                                                    ]
                                                },
                                                {
                                                    "id": 18038, ## Amenities
                                                    "sequenceNumber": 2,
                                                    "products": ps[5]
                                                },
                                                {
                                                    "id": 32529, ## Non-Alcoholic
                                                    "sequenceNumber": 3,
                                                    "subCategories": [
                                                        {
                                                            "id": 61199, ## Featured mix
                                                            "sequenceNumber": 1,
                                                            "products":ps[6]
                                                        },
                                                        {
                                                            "id": 61201, ## Soft drinks
                                                            "sequenceNumber": 2,
                                                            "products":ps[7]
                                                        },
                                                    ]
                                                },
                                                {
                                                    "id":  32497, ## Spririts and beer
                                                    "sequenceNumber": 4,
                                                   "subCategories": [
                                                        {
                                                            "id": 61192, ## Beer
                                                            "sequenceNumber": 1,
                                                            "products": ps[8]
                                                        },
                                                        {
                                                            "id": 61191, ## Liquors
                                                            "sequenceNumber": 2,
                                                            "products": ps[9]
                                                        },
                                                    ]
                                                },
                                                 {
                                                    "id":  32494, ## Champagne and wine
                                                    "sequenceNumber": 5,
                                                   "subCategories": [
                                                        {
                                                            "id": 61137, ## champane
                                                            "sequenceNumber": 1,
                                                            "products": ps[10]
                                                        },
                                                        {
                                                            "id": 61138, ## fortified
                                                            "sequenceNumber": 2,
                                                            "products": ps[11]
                                                        },
                                                        {
                                                            "id": 61139, ## red
                                                            "sequenceNumber": 3,
                                                            "products": ps[12]
                                                        },
                                                        {
                                                            "id": 61140, ## white
                                                            "sequenceNumber": 4,
                                                            "products": ps[13]
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                    ]
                                }
                            ]
                        }
                    }
    data.update(route_sequence)
    
    ca = post_route_catalog(session=airline_session, data=data)
    assert ca.status_code == 200
    route_catalog_workflow_helper(airline_session, ca.json()['id'])
    return ca.json()['id']


@pytest.fixture(scope="session")
def airline_session():
    token, expires_at = get_token('airline', base_url_env, True, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET)
    session = requests.Session()
    adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({"Content-Type": "application/json", "Authorization": token})
    original_request = session.request
    def refresh_token_if_needed():
        nonlocal token, expires_at 
        if datetime.now() >= expires_at - timedelta(seconds=45):
            token, expires_at = get_token('airline', base_url_env, True, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET)
            session.headers.update({"Content-Type": "application/json", "Authorization": token})
    def request_with_refresh(*args, **kwargs):
        refresh_token_if_needed()
        return original_request(*args, **kwargs)
    session.request = request_with_refresh
    yield session
    session.close()


@pytest.fixture(scope="session")
def store_http_session():
    token, expires_at = get_token('store', base_url_env, True, STORE_CLIENT_ID, STORE_CLIENT_SECRET)
    session = requests.Session()
    adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({"Content-Type": "application/json", "Authorization": token, "x-api-key": "3WpbQyO0p05GRNo3PxBAI8crlRI623eU3oxxNsWp"})
    original_request = session.request
    def refresh_token_if_needed():
        nonlocal token, expires_at 
        if datetime.now() >= expires_at - timedelta(seconds=45):
            token, expires_at = get_token('store', base_url_env, True, STORE_CLIENT_ID, STORE_CLIENT_SECRET)
            session.headers.update({"Content-Type": "application/json", "Authorization": token, "x-api-key": "3WpbQyO0p05GRNo3PxBAI8crlRI623eU3oxxNsWp"})
    def request_with_refresh(*args, **kwargs):
        refresh_token_if_needed()
        return original_request(*args, **kwargs)
    session.request = request_with_refresh
    yield session
    session.close()

@pytest.fixture
def full_build(request):
    value = 0
    # Get the marker expression used on the command line
    marker_expression = request.config.getoption("-m")
    # Check if your_marker_name was in the expression used to select tests
    if marker_expression and "full_build" in marker_expression:
        value = 1
    return value



@pytest.mark.new_product
def test_new_product(store_http_session, airline_session):
    uuid_name = uuid_random_name()
    sku = "api_auto_mapper" + uuid_name
    payload = {
        "formatVersion": 2,
        "sku": sku,
        "name": {"en": "new product"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            STORE_CATEGORY
        ],
        "assets": {
            "images": [
                {
                    "image1_1": "https://placehold.jp/1200x1200.png",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image2_3": "https://placehold.jp/944x1416.png",
                    "image4_3": "https://placehold.jp/760x570.png",
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ],
            "gallery": [
                {
                    "url": "https://placehold.jp/1480x740.png",
                    "imagePosition": 1
                },
                {
                    "url": "https://placehold.jp/1200x1200.png",
                    "imagePosition": 2
                },
            ]
        },
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    product_id = product_response.json()['id']





    ##Add the product to a particular catalog/catalog assignment / approve delta
    catalog_id = 69282095
    route_catalog_id = 69282103
    catalog_data = {
    "products": [
        "DcNPaddd1dds3as0asd07",
        "API_auto_vRJuSGnJGt",
        "API_auto_LUrDxKWbbP",
        "API_auto_zWfgMjHjeL",
        "API_auto_BFqHfhAXqc",
        "API_auto_hdIMGqYJWI",
        "API_auto_dzDUGkeQIO",
        "API_auto_WgfbeKQedJ",
        "API_auto_rxDjRPeLxY",
        "API_auto_rTgKnrHTzP",
        "API_auto_hqEmrbHCpY",
        "API_auto_AEgnTBvmmZ",
        "Mocha",
        "API_auto_wdoXPiAMoG",
        "API_auto_ExETWWeAts",
        "API_auto_HhmOssHCiR",
        "API_auto_TIJVfRbREO",
        "API_auto_yLvdIarjlj",
        "API_auto_SziYbUXPyV",
        "412312",
        "API_auto_HLqGBWCZOd",
        "API_auto_qENXMsmfkL",
        "Testyodasdsdsbdjddsdddsehct-703",
        "API_auto_jZbaeVzNoq",
        "API_auto_YtqgWnDFyX",
        "API_auto_SYSngmeqaw",
        "API_auto_KkAigusqXQ",
        "API_auto_NQCbWcuyxV",
        "API_auto_QugNSXiydZ",
        "API_auto_kmtLwoitlB",
        "API_auto_bSVwdCaGFu",
        "api_auto_benchmarkfae03a87_9777_4475_ba3d_d2854ed0cade",
        "API_auto_IbtCNfevoU",
        "API_auto_MXELetJXrY",
        "API_auto_MTvCEDIqqf",
        "API_auto_BmerTdUmwH",
        "API_auto_FggPerxOJD",
        "API_auto_DEiyrljPPY",
        "API_auto_UAXaAJyVmv",
        "API_auto_XfWzhyIast",
        "f00-10",
        "API_auto_ogFNAwVuGQ",
        "API_auto_NSuSvIpPBJ",
        "API_auto_WCGDHvAszd",
        "API_auto_vRJTGUPlrP",
        "API_auto_jDgmFmMaQh",
        "API_auto_FnCLxyJlWY",
        "API_auto_VUtkvphivS",
        "API_auto_EqLGksgGYP",
        "API_auto_RRMGSubpEU",
        "API_auto_OdZvzRPwiZ",
        "API_auto_tItOuPyRcg",
        "API_auto_hvXOFDpUda",
        "Testyodsbdjddsdddsehct-861",
        "api_auto_benchmark9fcbcdb7_cb30_4e32_af88_99a0960c0ef7",
        "API_auto_wRpUloUgnH",
        "API_auto_zyqpFzsvNV",
        "API_auto_BzRxvdDKcj",
        "API_auto_yQkRAXqjAL",
        "API_auto_KNmcuuTlFG",
        "API_auto_CpoWvdEZTK",
        "cl30",
        "API_auto_evJmURTwCF",
        "API_auto_gXkEWnHddv",
        "API_auto_gRjysGDXeW",
        "API_auto_CpKFYtGUzd",
        "API_auto_gIKoEYTgDy",
        "API_auto_cXMtfquqGu",
        "API_auto_nvHsPAcuYO",
        "API_auto_SLWFqGeBlI",
        sku
    ]
    }
    put_catalog_r = patch_catalog(catalog_data, catalog_id, store_http_session )
    assert put_catalog_r.status_code ==200
    get_d = get_delta(69282103, airline_session)
    assert get_d.status_code == 200

    approve_d = approve_delta(route_catalog_id, airline_session)
    assert approve_d.status_code == 200

    route_catalog_payload = {
    "name": "api_test_benchmark_ca_831e26c3_ebf0_4364_b5ae_41d4810cea28",
    "catalog": {
        "id": 69282095,
        "products": [
            {
                "productId": product_id,
                "airlineCategory": [
                61078
                ]
            },
            {
                "productId": 69000755,
                "airlineCategory": [
                61078
                ]
            },
            {
                "productId": 69179561,
                "airlineCategory": [
                61078
                ]
            },
            {
                "productId": 69179433,
                "airlineCategory": [
                61078
                ]
            },
            {
                "productId": 69179796,
                "airlineCategory": [
                61078
                ]
            },
            {
                "productId": 69182222,
                "airlineCategory": [
                61078
                ]
            },
            {
                "productId": 69182105,
                "airlineCategory": [
                61079
                ]
            },
            {
                "productId": 69180836,
                "airlineCategory": [
                61079
                ]
            },
            {
                "productId": 69180886,
                "airlineCategory": [
                61079
                ]
            },
            {
                "productId": 69180661,
                "airlineCategory": [
                61079
                ]
            },
            {
                "productId": 69181795,
                "airlineCategory": [
                61079
                ]
            },
            {
                "productId": 69179965,
                "airlineCategory": [
                18947
                ]
            },
            {
                "productId": 69181806,
                "airlineCategory": [
                18947
                ]
            },
            {
                "productId": 18585,
                "airlineCategory": [
                18947
                ]
            },
            {
                "productId": 69181317,
                "airlineCategory": [
                18947
                ]
            },
            {
                "productId": 69182640,
                "airlineCategory": [
                18947
                ]
            },
            {
                "productId": 69181628,
                "airlineCategory": [
                18063
                ]
            },
            {
                "productId": 69180930,
                "airlineCategory": [
                18063
                ]
            },
            {
                "productId": 69180858,
                "airlineCategory": [
                18063
                ]
            },
            {
                "productId": 69181491,
                "airlineCategory": [
                18063
                ]
            },
            {
                "productId": 17581,
                "airlineCategory": [
                18063
                ]
            },
            {
                "productId": 69178607,
                "airlineCategory": [
                18062
                ]
            },
            {
                "productId": 69182594,
                "airlineCategory": [
                18062
                ]
            },
            {
                "productId": 82131,
                "airlineCategory": [
                18062
                ]
            },
            {
                "productId": 69179003,
                "airlineCategory": [
                18062
                ]
            },
            {
                "productId": 69180241,
                "airlineCategory": [
                18062
                ]
            },
            {
                "productId": 69179512,
                "airlineCategory": [
                18038
                ]
            },
            {
                "productId": 69179117,
                "airlineCategory": [
                18038
                ]
            },
            {
                "productId": 69179334,
                "airlineCategory": [
                18038
                ]
            },
            {
                "productId": 69180786,
                "airlineCategory": [
                18038
                ]
            },
            {
                "productId": 69182262,
                "airlineCategory": [
                18038
                ]
            },
            {
                "productId": 69180011,
                "airlineCategory": [
                61199
                ]
            },
            {
                "productId": 69240474,
                "airlineCategory": [
                61199
                ]
            },
            {
                "productId": 69180452,
                "airlineCategory": [
                61199
                ]
            },
            {
                "productId": 69180619,
                "airlineCategory": [
                61199
                ]
            },
            {
                "productId": 69181922,
                "airlineCategory": [
                61199
                ]
            },
            {
                "productId": 69182373,
                "airlineCategory": [
                61201
                ]
            },
            {
                "productId": 69179704,
                "airlineCategory": [
                61201
                ]
            },
            {
                "productId": 69178432,
                "airlineCategory": [
                61201
                ]
            },
            {
                "productId": 69178355,
                "airlineCategory": [
                61201
                ]
            },
            {
                "productId": 69179327,
                "airlineCategory": [
                61201
                ]
            },
            {
                "productId": 10295,
                "airlineCategory": [
                61192
                ]
            },
            {
                "productId": 69181886,
                "airlineCategory": [
                61192
                ]
            },
            {
                "productId": 69178430,
                "airlineCategory": [
                61192
                ]
            },
            {
                "productId": 69179920,
                "airlineCategory": [
                61192
                ]
            },
            {
                "productId": 69180937,
                "airlineCategory": [
                61192
                ]
            },
            {
                "productId": 69181097,
                "airlineCategory": [
                61191
                ]
            },
            {
                "productId": 69178120,
                "airlineCategory": [
                61191
                ]
            },
            {
                "productId": 69179852,
                "airlineCategory": [
                61191
                ]
            },
            {
                "productId": 69182610,
                "airlineCategory": [
                61191
                ]
            },
            {
                "productId": 69178551,
                "airlineCategory": [
                61191
                ]
            },
            {
                "productId": 69182741,
                "airlineCategory": [
                61137
                ]
            },
            {
                "productId": 69178137,
                "airlineCategory": [
                61137
                ]
            },
            {
                "productId": 69178319,
                "airlineCategory": [
                61137
                ]
            },
            {
                "productId": 81948,
                "airlineCategory": [
                61137
                ]
            },
            {
                "productId": 69228471,
                "airlineCategory": [
                61137
                ]
            },
            {
                "productId": 69182559,
                "airlineCategory": [
                61138
                ]
            },
            {
                "productId": 69178621,
                "airlineCategory": [
                61138
                ]
            },
            {
                "productId": 69179297,
                "airlineCategory": [
                61138
                ]
            },
            {
                "productId": 69178780,
                "airlineCategory": [
                61138
                ]
            },
            {
                "productId": 69180560,
                "airlineCategory": [
                61138
                ]
            },
            {
                "productId": 69182525,
                "airlineCategory": [
                61139
                ]
            },
            {
                "productId": 79325,
                "airlineCategory": [
                61139
                ]
            },
            {
                "productId": 69182499,
                "airlineCategory": [
                61139
                ]
            },
            {
                "productId": 69179586,
                "airlineCategory": [
                61139
                ]
            },
            {
                "productId": 69178786,
                "airlineCategory": [
                61139
                ]
            },
            {
                "productId": 69179709,
                "airlineCategory": [
                61140
                ]
            },
            {
                "productId": 69179081,
                "airlineCategory": [
                61140
                ]
            },
            {
                "productId": 69179137,
                "airlineCategory": [
                61140
                ]
            },
            {
                "productId": 69179078,
                "airlineCategory": [
                61140
                ]
            },
            {
                "productId": 69181284,
                "airlineCategory": [
                61140
                ]
            }
        ]
    },
    "flight": [
        {
            "id": 69178054
        }
    ],
    "catalogFromDate": "2025-05-02",
    "catalogToDate": "2030-11-08",
    "cabinClass": [
        867
    ],
    "routeSequences": {
        "categories": [
            {
                "id": 61077,
                "sequenceNumber": 1,
                "subCategories": [
                {
                    "id": 61078,
                    "sequenceNumber": 1,
                    "products": [
                        {
                            "productId": product_id,
                            "sequenceNumber": 1
                        },
                        {
                            "productId": 69000755,
                            "sequenceNumber": 2
                        },
                        {
                            "productId": 69179561,
                            "sequenceNumber": 3
                        },
                        {
                            "productId": 69179433,
                            "sequenceNumber": 4
                        },
                        {
                            "productId": 69179796,
                            "sequenceNumber": 5
                        },
                        {
                            "productId": 69182222,
                            "sequenceNumber": 6
                        }
                    ]
                },
                {
                    "id": 61079,
                    "sequenceNumber": 2,
                    "products": [
                        {
                            "productId": 69182105,
                            "sequenceNumber": 1
                        },
                        {
                            "productId": 69180836,
                            "sequenceNumber": 2
                        },
                        {
                            "productId": 69180886,
                            "sequenceNumber": 3
                        },
                        {
                            "productId": 69180661,
                            "sequenceNumber": 4
                        },
                        {
                            "productId": 69181795,
                            "sequenceNumber": 5
                        }
                    ]
                }
                ]
            },
            {
                "id": 15532,
                "sequenceNumber": 2,
                "subCategories": [
                {
                    "id": 18947,
                    "sequenceNumber": 1,
                    "products": [
                        {
                            "productId": 69179965,
                            "sequenceNumber": 1
                        },
                        {
                            "productId": 69181806,
                            "sequenceNumber": 2
                        },
                        {
                            "productId": 18585,
                            "sequenceNumber": 3
                        },
                        {
                            "productId": 69181317,
                            "sequenceNumber": 4
                        },
                        {
                            "productId": 69182640,
                            "sequenceNumber": 5
                        }
                    ]
                },
                {
                    "id": 17949,
                    "sequenceNumber": 2,
                    "subCategories": [
                        {
                            "id": 18037,
                            "sequenceNumber": 1,
                            "subCategories": [
                            {
                                "id": 18063,
                                "sequenceNumber": 1,
                                "products": [
                                    {
                                        "productId": 69181628,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69180930,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69180858,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69181491,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 17581,
                                        "sequenceNumber": 5
                                    }
                                ]
                            },
                            {
                                "id": 18062,
                                "sequenceNumber": 2,
                                "products": [
                                    {
                                        "productId": 69178607,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69182594,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 82131,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69179003,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69180241,
                                        "sequenceNumber": 5
                                    }
                                ]
                            }
                            ]
                        },
                        {
                            "id": 18038,
                            "sequenceNumber": 2,
                            "products": [
                            {
                                "productId": 69179512,
                                "sequenceNumber": 1
                            },
                            {
                                "productId": 69179117,
                                "sequenceNumber": 2
                            },
                            {
                                "productId": 69179334,
                                "sequenceNumber": 3
                            },
                            {
                                "productId": 69180786,
                                "sequenceNumber": 4
                            },
                            {
                                "productId": 69182262,
                                "sequenceNumber": 5
                            }
                            ]
                        },
                        {
                            "id": 32529,
                            "sequenceNumber": 3,
                            "subCategories": [
                            {
                                "id": 61199,
                                "sequenceNumber": 1,
                                "products": [
                                    {
                                        "productId": 69180011,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69240474,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69180452,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69180619,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69181922,
                                        "sequenceNumber": 5
                                    }
                                ]
                            },
                            {
                                "id": 61201,
                                "sequenceNumber": 2,
                                "products": [
                                    {
                                        "productId": 69182373,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69179704,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69178432,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69178355,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69179327,
                                        "sequenceNumber": 5
                                    }
                                ]
                            }
                            ]
                        },
                        {
                            "id": 32497,
                            "sequenceNumber": 4,
                            "subCategories": [
                            {
                                "id": 61192,
                                "sequenceNumber": 1,
                                "products": [
                                    {
                                        "productId": 10295,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69181886,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69178430,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69179920,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69180937,
                                        "sequenceNumber": 5
                                    }
                                ]
                            },
                            {
                                "id": 61191,
                                "sequenceNumber": 2,
                                "products": [
                                    {
                                        "productId": 69181097,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69178120,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69179852,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69182610,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69178551,
                                        "sequenceNumber": 5
                                    }
                                ]
                            }
                            ]
                        },
                        {
                            "id": 32494,
                            "sequenceNumber": 5,
                            "subCategories": [
                            {
                                "id": 61137,
                                "sequenceNumber": 1,
                                "products": [
                                    {
                                        "productId": 69182741,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69178137,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69178319,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 81948,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69228471,
                                        "sequenceNumber": 5
                                    }
                                ]
                            },
                            {
                                "id": 61138,
                                "sequenceNumber": 2,
                                "products": [
                                    {
                                        "productId": 69182559,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69178621,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69179297,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69178780,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69180560,
                                        "sequenceNumber": 5
                                    }
                                ]
                            },
                            {
                                "id": 61139,
                                "sequenceNumber": 3,
                                "products": [
                                    {
                                        "productId": 69182525,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 79325,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69182499,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69179586,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69178786,
                                        "sequenceNumber": 5
                                    }
                                ]
                            },
                            {
                                "id": 61140,
                                "sequenceNumber": 4,
                                "products": [
                                    {
                                        "productId": 69179709,
                                        "sequenceNumber": 1
                                    },
                                    {
                                        "productId": 69179081,
                                        "sequenceNumber": 2
                                    },
                                    {
                                        "productId": 69179137,
                                        "sequenceNumber": 3
                                    },
                                    {
                                        "productId": 69179078,
                                        "sequenceNumber": 4
                                    },
                                    {
                                        "productId": 69181284,
                                        "sequenceNumber": 5
                                    }
                                ]
                            }
                            ]
                        }
                    ]
                }
                ]
            }
        ]
    }
    }
    ### send route_catalog sequence with the new product 
    sleep(1)
    patch_payload = {"catalog": route_catalog_payload['catalog'] }
    patch_route_catalog(airline_session, route_catalog_id, patch_payload)

    ## POST WORKFLOW TO  airlineCategoryMapped...
    request_data = {"catalogAssignmentId": route_catalog_id, "status": 'airlineCategoryMapped'}
    r = post_workflow(request_data, session=airline_session)
    assert  r.status_code == 200


    request_data = {"catalogAssignmentId": route_catalog_id, "status": 'editAirlineCategory'}
    r = post_workflow(request_data, session=airline_session)
    assert  r.status_code == 200

    route_p = put_route_catalog(airline_session, route_catalog_id, route_catalog_payload)
    assert route_p.status_code == 200

    request_data = {"catalogAssignmentId": route_catalog_id, "status": 'airlineCategoryMapped'}
    r = post_workflow(request_data, session=airline_session)
    assert  r.status_code == 200


@pytest.mark.new_menu
def test_new_menu(store_http_session, airline_session):
    products_response = get_product(session=store_http_session, limit=100)
    assert products_response.status_code == 200
    products = products_response.json()['data']['products']
    today_utc = datetime.now(timezone.utc)
    tomorrow_utc = today_utc + timedelta(days=1)
    formatted_date = tomorrow_utc.strftime('%Y-%m-%d')

    cabin_classes = [ 192, 195, 867 ]
    ca_ids = []
    for cabin_class in cabin_classes:
        ca_id = create_catalog_assigment_helper(69177656, cabin_class, formatted_date, formatted_date, products, store_http_session, airline_session)
        ca_ids.append(ca_id)
    for id in ca_ids:
        sns = getEventHandlerS3AirlineFile(aFiles.CATALOG_ASSIGNMENTS, id)
        validate_catalogAssigment(sns)
@pytest.mark.update_metadata
def test_update_metadata(store_http_session, airline_session):
    print("update an existing product...")
    patch_data ={
        "description": f"This is a testing updated description for product aa '{EXISTING_PRODUCT_ID}' -" + uuid_random_name()
    }
    p_s = patch_product(store_http_session,EXISTING_PRODUCT_ID, patch_data )
    assert p_s.status_code == 200
    sns = getEventHandlerS3ProductsFile(pFiles.PRODUCTS, EXISTING_PRODUCT_ID)
    try:
        assert patch_data["description"] == sns['description']['en']
    except:
        sns = getEventHandlerS3ProductsFile(pFiles.PRODUCTS, EXISTING_PRODUCT_ID)
        assert patch_data["description"] == sns['description']['en']
    validate_product(sns)

    print("udpdate an existing category")
    patch_data ={
        "formatVersion":2,
        "description": { "en":f"This is a testing updated description for category '{EXISTING_CATEGORY_ID}' -" + uuid_random_name()}
    }
    p_s = patch_airline_category(airline_session, patch_data, EXISTING_CATEGORY_ID )
    assert p_s.status_code == 200

    sns = getEventHandlerS3AirlineFile(aFiles.CATEGORIES, EXISTING_CATEGORY_ID)
    validate_airlineCategory(sns)
    sleep(1)
    print(json.dumps(sns, indent=5))
    assert patch_data["description"]['en'] == sns['longDescription']['en']

    print("update and existing spa")
    patch_data ={
        "formatVersion": 2,
        "shortDescription": {"en":f"This is a testing updated description for SPA '{EXISTING_SPA_ID}' -" + uuid_random_name()}
    }
    p_s = patch_speciality_attibute(EXISTING_SPA_ID, patch_data, session=store_http_session )
    assert p_s.status_code == 200
    sns = getEventHandlerS3ProductsFile(pFiles.SPECIALITY_ATTRIBUTES, EXISTING_SPA_ID)
    validate_specialityAttribute(sns)
    assert patch_data["shortDescription"]['en'] == sns['description']['en']


@pytest.mark.new_product
@pytest.mark.new_menu
@pytest.mark.update_metadata
@pytest.mark.full_build
def test_generate_kit(airline_session, full_build):
    s3 = boto3.client('s3')
    latest_file = get_latest_file(s3, S3_AIRLINE_BUCKET, "kit_ready/")
    existing_version = extract_version(latest_file)
    print("The existing version:", existing_version)
    kit_ready = post_prepare_kit(airline_session) ## trigger new kit
    assert kit_ready.status_code == 200
    # task_def = getAwsPhysicalResourceId(AIRLINE_STACK_NAME, LOGICAL_ID_TASK_DEF)
    sleep(60) ## allow some propagation delay to allow the prepare kit sns 
    task_def = TASK_DEF
    trigger_mapper_kit_generation(ECS_CLUSTER, task_def, 'sia', full_build, ECS_EXCECUTION_ROLE_ARN, ECS_TASK_ROLE_ARN) 
    ### Track until the kit is generated...
    retries = 240 ## 40 minutes
    wait_time = 10 ## seconds
    foundNewKit = False
    for retry in range(retries):
        latest_file = get_latest_file(s3, S3_AIRLINE_BUCKET, "kits/")
        kit_version = extract_version(latest_file)
        print("the kit version:", kit_version)
        if kit_version <= existing_version:
            print("wait more time..")
            sleep(wait_time)
        else:
            os.environ['KIT_VERSION'] = str(kit_version)
            foundNewKit = True
            break
    assert foundNewKit == True, "New kit generation was not completed in under 40 minutes"
    ### download to start in the data_fetcher


