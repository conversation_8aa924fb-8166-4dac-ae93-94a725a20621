
import pytest 
import json
from utils.helper import *
from utils.data_validation_helpers import *


S3_AIRLINE_BUCKET = "sia-catalog-kit-files-924166462419"
S3_PRODUCTS_BUCKET = "catalog-kit-product-files-924166462419"
AIRLINE_CODE = os.environ.get('AIRLINE_CODE', '00000V5V')
EXTRACT_DIR = f'extracted_{AIRLINE_CODE}'


def test_download_kit():
    s3 = boto3.client('s3')
    latest_file = get_latest_file(s3, S3_AIRLINE_BUCKET, "kits/")
    kit_version = extract_version(latest_file)

    print("the new version:", kit_version)
    os.environ['KIT_VERSION'] = str(kit_version)
    download_tgz_file(S3_AIRLINE_BUCKET, latest_file)

    TGZ_FILE = f'{AIRLINE_CODE}_marketplace_content_{kit_version}.tgz'
    logger.info(f"Extracting {TGZ_FILE} to {EXTRACT_DIR}")
    if not extract_tgz(TGZ_FILE, EXTRACT_DIR):
        pytest.skip(f"Failed to extract {TGZ_FILE}")
    # Extract all inner tgz files
    logger.info("Extracting inner TGZ files")
    success_count, total_count = extract_inner_tgz_files(EXTRACT_DIR)
    logger.info(f"Extracted {success_count} of {total_count} inner TGZ files")


def test_fetch_latest_s3_data():
    s3 = boto3.client('s3')
    
    latest_file_key = get_latest_file(s3, S3_AIRLINE_BUCKET, "kit_ready/")
    kit_ready = fetch_json_from_s3(s3, S3_AIRLINE_BUCKET, latest_file_key)

    all_ca_data = []
    for i in kit_ready['catalogAssignments']:
        ca_key = f'catalog_assignments/{i}.json'
        ca_d = fetch_json_from_s3(s3, S3_AIRLINE_BUCKET, ca_key)
        all_ca_data.append(ca_d)
    
    applicable_category_ids = extract_category_ids(all_ca_data)
    all_categories_data = {}
    for id in applicable_category_ids:
        cat_key = f"categories/CAT{id}/CAT{id}.json"
        cat_data = fetch_json_from_s3(s3, S3_AIRLINE_BUCKET, cat_key)
        all_categories_data[id] = cat_data


    all_proudcts_data = {}
    applicable_product_ids = extract_product_ids(all_ca_data)
    for id in applicable_product_ids:
        cat_key = f"products/{id}/{id}.json"
        cat_data = fetch_json_from_s3(s3, S3_PRODUCTS_BUCKET, cat_key)
        all_proudcts_data[id] = cat_data


    applicable_attr_ids = extract_spa_ids(all_proudcts_data)
    all_spaciality_attributes_data = {}
    for id in applicable_attr_ids:
        key = f"speciality_attributes/{id}/{id}.json"
        data = fetch_json_from_s3(s3, S3_PRODUCTS_BUCKET, key)
        all_spaciality_attributes_data[id] = data

    with open('speciality_atributes_s3.json', 'w') as f:
       json.dump(all_spaciality_attributes_data, f, indent=4)
       
    with open('applicableCatalogAssignment_s3.json', 'w') as f:
       json.dump(all_ca_data, f, indent=4)

    with open('Categories_s3.json', 'w') as f:
       json.dump(all_categories_data, f, indent=4)

    with open('products_s3.json', 'w') as f:
       json.dump(all_proudcts_data, f, indent=4)

    



