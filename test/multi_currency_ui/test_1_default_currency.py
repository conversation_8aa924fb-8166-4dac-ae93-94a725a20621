import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
import os
from utils.helper import *
from utils.ui_helper import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from jamatest import jamatest
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains

ALL_DISPLAY_CURRENCIES = ["USD"]


@pytest.fixture(scope="session", autouse=True)
def setup_store_for_usd_default_only(store_driver):
    configure_store_display_currencies(store_driver)
    close_active_tab(store_driver)
    
@jamatest.test(15986919)
@pytest.mark.reg
@pytest.mark.csv
def test_csv_import_default_currency( store_http_session, track_product_input_data_map, store_driver, pac_user_driver):
   curr_dir = os.path.dirname(os.path.realpath(__file__))
   csv_reference = os.path.join(curr_dir,"csv_files", 'import_default_currency.csv'  )
   logger.debug("Full path of the CSV file: " + str(csv_reference))
   test_filename = generate_test_csv(csv_reference)
   logger.debug("Generated the test csv")
   test_json_products_list = product_csv_to_json(test_filename)
   logger.debug("Before uploading the csv")
   upload_csv_product(store_driver, test_filename)
   logger.debug("After uploading the csv")
   sleep(3)
   close_active_tab(store_driver)
   num_products = len(test_json_products_list)
   temp_list = test_json_products_list
   logger.debug("Number of products created to verify: %s", num_products)
   sleep(25)
   all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
   for product in all_get_product_list:
      for p_in in test_json_products_list:
         if product['sku'] == p_in['sku']:
            track_product_input_data_map[product['id']] = p_in
            logger.info("Found a matching product %s", product['sku'] )
            sns = download_sns(pac_user_driver, product["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
            validate_product(sns)
            assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in))
            assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
            num_products= num_products-1
            temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
         elif "fails" in product['sku']: # to make sure failed products are not getting created
            product_id = product["id"]
            raise Exception(f"There was a product with fails {product_id}")
   if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")

@pytest.mark.product_c
@pytest.mark.reg
@jamatest.test(15585926, 1)
def test_create_product_default_price_store_no_price(pac_user_driver, store_driver, json_reference_product, store_http_session, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
    
@pytest.mark.product_c
@pytest.mark.reg
@jamatest.test(15585926, 2)
def test_create_product_default_price_only_price(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, price="random", prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15585926, 3)
@pytest.mark.reg
def test_create_product_default_price_only_price_isTaxable(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, price="random", isTaxable=True, prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
@pytest.mark.product_c
# @jamatest.test(15585926, 5)
def test_create_product_price_and_specialPrice(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer): 
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']))
    enter_product_price( store_driver, price="random", special_price="random", prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15585926, 4)
@pytest.mark.reg
def test_create_product_default_price_specialPrice_isTaxable(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer): 
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, price="random", special_price="random", isTaxable=True, prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

    
# @jamatest.test(15585926, 7)
# def test_create_product_no_price_speciality_attribute(store_driver, json_reference_product): #working here
#     prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
#     speciality_at = add_speciality_attribute_1st(store_driver)   ####  CANNOT GET THIS FUNTINALITY TO ADD SPECIALITY ATTRIBUTES
#     print(str(speciality_at))

@pytest.mark.product_c
# @jamatest.test(15585926, 6)
def test_create_product_simple_variant_disabled(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, price="random", special_price="random", isTaxable=True, prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer, close_tab_on_save=False)
    product_variants_button_state_check(store_driver)
    close_active_tab(store_driver)
    sleep(2)
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15585926, 5)
def test_create_product_configurable_without_price(pac_user_driver, store_driver, json_reference_product, store_http_session, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, product_type="Configurable",json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    product_save_and_publish(store_driver, close_tab_on_save=False, timer_arr=product_save_timer)
    product_variants_button_state_check(store_driver, expected_state='false')
    parent_box = store_driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
    parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
    
@pytest.mark.product_c
@pytest.mark.reg
@jamatest.test(15585926,6)
def test_create_product_configurable_price_specialPrice(pac_user_driver, store_driver, json_reference_product, store_http_session, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, product_type="Configurable",json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random", isTaxable=True, prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, timer_arr=product_save_timer)
    product_variants_button_state_check(store_driver, expected_state='false')
    parent_box = store_driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
    parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

    
@pytest.mark.reg
@pytest.mark.product_c
@jamatest.test(15585926, 7)
def test_create_product_3_decimal_points_restriction_price_special_price(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer): 
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']))
    enter_product_price( store_driver, price="-234.2334")
    prod_input_data['PriceTaxation']['price'].insert(0, {"value":234.233, "currency": "USD"})
    detect_4_decimals_popup(store_driver)
    enter_product_price( store_driver, special_price="-224.54321")
    detect_4_decimals_popup(store_driver)
    prod_input_data['PriceTaxation']['specialPrice'].insert(0, {"value":224.543, "currency": "USD"})
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

    
    
@pytest.fixture(scope='session')
def create_catalog(store_driver, track_product_input_data_map, track_catalog_input_data, Catalog_save_timer):
    dismiss_notification(store_driver, dismiss_all=True)
    create_catalog_helper(store_driver, track_product_input_data_map, track_catalog_input_data, Catalog_save_timer=Catalog_save_timer)

@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16038421)
def test_CA_no_prices(pac_user_driver, store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 

    print("before create_catalog_assigment" )
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR__DEFAULT_CURR_1, AirlineName=AIRLINE_NAME )
    print(" before CA_request_for_association")
    CA_request_for_association(airline_driver)
    print("before store_open_catalog_assigment")
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    print("store_CA_approve_for_association")
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve...
    
    # get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    # assert get_route_catalog_response.status_code == 200
    # compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], all_products)
    # sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    # assert compare_input_to_expected_data(sns['products'], all_products)
    
    close_active_tab(store_driver)
    close_active_tab(airline_driver)
    close_active_tab(store_driver, not_sure=True)
    dismiss_notification(store_driver, dismiss_all=True)
    dismiss_notification(airline_driver, dismiss_all=True)

@pytest.mark.reg
@jamatest.test(16040124)
@pytest.mark.usefixtures("create_catalog")
def test_CA_a_few_products_with_all_prices(pac_user_driver, store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR__DEFAULT_CURR_2, AirlineName=AIRLINE_NAME )
    CA_request_for_association(airline_driver) # the catalog assigment needs to be in request for associalition for store to be able to see it
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[1], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products = [{}, product ]
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[3], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products.append({})
    all_products.append(product) 
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[6], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products.append({}) 
    all_products.append({})
    all_products.append(product) 
    CA_save(store_driver, timer_arr=CA_save_timer); 
    sleep(2)
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE)
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], all_products)
    try:
        sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )  #### need to download the last one
        assert compare_input_to_expected_data(sns['products'], product_sns_convert(all_products))
    except:
        logger.info("the verification for the SNS contents failed on the first attempt.")
        sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )  #### need to download the last one
        assert compare_input_to_expected_data(sns['products'], product_sns_convert(all_products))
        logger.info("the verification for the SNS contents succeded on the second attemp... likely the correct sns was not picked up initially")
    close_active_tab(store_driver)
    close_active_tab(airline_driver)


    
@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16041027)
def test_CA_all_product_prices(pac_user_driver, store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR__DEFAULT_CURR_3, AirlineName=AIRLINE_NAME )
    CA_request_for_association(airline_driver) 
    
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE)
    download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )   
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    all_products = []
    for sku in products_skus:
        product = sku_random_prices_for_all_currencies(store_driver, sku, ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
        all_products.append(product)
    CA_save(store_driver, timer_arr=CA_save_timer); 
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], all_products)
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(all_products))
    close_active_tab(store_driver)
    close_active_tab(airline_driver)
    dismiss_notification(store_driver, dismiss_all=True)
    dismiss_notification(airline_driver, dismiss_all=True)
    

    
    
@pytest.mark.reg
@pytest.mark.csv
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16375502)
def test_CA_csv_prices_all( store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, pac_user_driver, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_3, AirlineName=AIRLINE_NAME ) 
    CA_request_for_association(airline_driver)
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)    
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve...   
    download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )   
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
 
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus, ALL_DISPLAY_CURRENCIES )
    close_active_tab(store_driver)
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))

@pytest.mark.reg
@pytest.mark.csv
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16461759)
def test_CA_csv_prices_additions( store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, pac_user_driver, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_3, AirlineName=AIRLINE_NAME ) 
    CA_request_for_association(airline_driver)
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve...    
    download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )   
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    
    
    
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus[1:-3], ALL_DISPLAY_CURRENCIES,{products_skus[4]: [ALL_DISPLAY_CURRENCIES[0]]} )
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus[:-1], ALL_DISPLAY_CURRENCIES,{products_skus[-3]: [ALL_DISPLAY_CURRENCIES[0]]} )
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))
    close_active_tab(store_driver)
    close_active_tab(store_driver, not_sure=True)