import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
import os
from utils.helper import *
from utils.ui_helper import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from jamatest import jamatest
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from jsonschema import validate
import yaml
from jsonschema.validators import RefResolver
from datetime import date, datetime

ALL_DISPLAY_CURRENCIES = ["USD", "GBP", "ARS", "AUD", "PTS"]
# @pytest.fixture(scope="session", autouse=True)
@pytest.mark.reg
def test_setup_store_4_display_currencies(store_driver):
    configure_store_display_currencies(store_driver, ["GBP", "ARS", "AUD", "PTS"  ])
    close_active_tab(store_driver)

@jamatest.test(15986921) 
@pytest.mark.reg
@pytest.mark.product_c
@pytest.mark.qt
@pytest.mark.csv
def test_csv_import_a_few_display_currencies(store_http_session, track_product_input_data_map, store_driver):
   curr_dir = os.path.dirname(os.path.realpath(__file__))
   csv_reference = os.path.join(curr_dir,"csv_files", 'import_a_few_currencies_pre_validations.csv'  )
   test_filename = generate_test_csv(csv_reference)
   test_json_products_list = product_csv_to_json(test_filename)
   upload_csv_product(store_driver, test_filename)
   sleep(3)
   close_active_tab(store_driver)
   num_products = len(test_json_products_list)
   temp_list = test_json_products_list
   logger.debug("Number of products created to verify: %s", num_products)
   sleep(25)
   temp_list = test_json_products_list
#    all_get_product_list = get_all_helper(store_http_session, product_url)
   all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
   for product in all_get_product_list:
      for p_in in test_json_products_list:
         if product['sku'] == p_in['sku']:
            track_product_input_data_map[product['id']] = p_in
            logger.info("Found a matching product %s", product['sku'] )
            sns = get_sns_message_by_id_catalog(product["id"], retries=25) # incase the message was read in previous
            validate_product(sns)
            assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in))
            num_products= num_products-1
            temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
            temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
            assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
         elif "fails" in product['sku']: # to make sure failed products are not getting created
            product_id = product["id"]
            raise Exception(f"There was a product with fails {product_id}")
   if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")

@pytest.mark.product_c
@jamatest.test(15856188, 1)
def test_create_product_4_display_currencies_no_price( store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data


@pytest.mark.product_c
@jamatest.test(15856188, 2)
def test_create_product_4_display_currencies_default_price_only(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price='random', prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer, close_tab_on_save=True)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
        
@pytest.mark.product_c
@jamatest.test(15856188, 3)
def test_create_product_4_display_currencies_default_price_only_isTaxable(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", prod_input_data=prod_input_data, isTaxable=True)
    product_save_and_publish(store_driver, timer_arr=product_save_timer, close_tab_on_save=True)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c   
@jamatest.test(15856188, 4)
def test_create_product_4_display_currencies_default_price_specialPrice(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random", prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer, close_tab_on_save=True)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 5)
def test_create_product_4_display_currencies_1_display_price(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer, close_tab_on_save=True)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 6)
def test_create_product_4_display_currencies_1_display_price_special_price(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer, close_tab_on_save=True)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 7)####
def test_create_product_4_display_currencies_2_display_price_1specialPrice(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random",  prod_input_data=prod_input_data)  
    enter_product_price( store_driver, currency="ARS",price="567",  )
    prod_input_data['PriceTaxation']['price'].insert(2, {"value":567, "currency": "ARS"})

    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 8)   
def test_create_product_4_display_currencies_price_only(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random",   prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 9)   
def test_create_product_4_display_currencies_price_only_AUD(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random",   prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random",   prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 10)   
def test_create_product_4_display_currencies_price_special_price(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random", special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 12)
def test_create_product_3_display_currencies_simple_product_variants_button_disabled(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random", special_price="random",  prod_input_data=prod_input_data)
    product_variants_button_state_check(store_driver)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data


@pytest.mark.product_c
@jamatest.test(15856188, 13)
def test_create_product_4_currencies_configurable_with_price_only(store_driver, json_reference_product, store_http_session, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, product_type="Configurable",json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD",price="random",  isTaxable=True, prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random",   prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random",   prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random",   prod_input_data=prod_input_data)

    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    # product_variants_button_state_check(store_driver, expected_state='false')
    # parent_box = store_driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
    # parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()
    product_compare_get(store_http_session,prod_input_data )
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)    
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
    
@pytest.mark.product_c
@jamatest.test(15856188, 15)
def test_create_product_4_currencies_configurable_price_specialPrice(store_driver, json_reference_product, store_http_session, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, product_type="Configurable",json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random", isTaxable=True, prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random", special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    # product_variants_button_state_check(store_driver, expected_state='false')
    # parent_box = store_driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
    # parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()
    product_compare_get(store_http_session,prod_input_data )
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data


@pytest.mark.product_c
@jamatest.test(15856188, 17) 
def test_create_product_display_currencies_message_pop_up_default_price_required(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)

    enter_product_price( store_driver, currency="GBP",price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver)
    
    enter_product_price( store_driver, currency="ARS",price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver)
    
    enter_product_price( store_driver, currency="AUD",price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver)
    
    enter_product_price( store_driver, currency="PTS",price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver)

    
    enter_product_price( store_driver, currency="USD", price="7765")  ### USD default price
    prod_input_data['PriceTaxation']['price'].insert(0, {"value":7765, "currency": "USD"})
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 18) 
def test_create_product_display_currencies_message_pop_up_default_specialPrice_required(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD",price="random",  isTaxable=True, prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random",   prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random",   prod_input_data=prod_input_data)

    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver, "specialPrice")
    
    enter_product_price( store_driver, currency="ARS",special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver, "specialPrice")
    
    enter_product_price( store_driver, currency="PTS",special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver, "specialPrice")    

    enter_product_price( store_driver, currency="USD", special_price="2333")  ### USD default price
    prod_input_data['PriceTaxation']['specialPrice'].insert(0, {"value":2333, "currency": "USD"})
    product_save_and_publish(store_driver, timer_arr=product_save_timer)

    # product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15856188, 19)
@pytest.mark.reg
def test_modify_product_add_specialPrice(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True) 
    enter_product_price( store_driver, currency="USD",price="random",  isTaxable=True, prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random",   prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random",   prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)

    sleep(5)
    product_compare_get(store_http_session,prod_input_data)
    enter_product_price( store_driver, currency="USD",special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)

    product_compare_get(store_http_session,prod_input_data)
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@jamatest.test(15856188, 20)
@pytest.mark.product_c
@pytest.mark.reg
def test_create_product_decimal_points_restriction_price_special_prices_more_currencies(store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer): 
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']))
    enter_product_price( store_driver, price="-456.9334")
    prod_input_data['PriceTaxation']['price'].append({"value":456.933, "currency": "USD"})
    detect_4_decimals_popup(store_driver)
    
    enter_product_price( store_driver, special_price="456.54321")
    detect_4_decimals_popup(store_driver)
    prod_input_data['PriceTaxation']['specialPrice'].append({"value":456.543, "currency": "USD"})
    
    enter_product_price( store_driver, currency="GBP", price="12345.1234567")
    detect_4_decimals_popup(store_driver)
    prod_input_data['PriceTaxation']['price'].append({"value":12345.123, "currency": "GBP"})
    
    enter_product_price( store_driver, currency="GBP", special_price="-9878.2345666666")
    detect_4_decimals_popup(store_driver)
    prod_input_data['PriceTaxation']['specialPrice'].append( {"value":9878.234, "currency": "GBP"})
    
    
    enter_product_price( store_driver, currency="PTS", price="98780.2")
    detect_PTS_decimals_pop_up(store_driver)
    prod_input_data['PriceTaxation']['price'].append({"value":98780, "currency": "PTS"})
    
    product_save_and_publish(store_driver, timer_arr=product_save_timer)    
    product_compare_get(store_http_session,prod_input_data )
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.reg
def test_create_product_api(store_http_session, track_product_input_data_map, product_save_timer):
    prod_input_data = {
        "sku": "api_auto_" + uuid_random_name(),
        "name": " API Bottled WatsderDasani® Bottled™",
        "description": "Dasani Bottled Wate Impossible Breakfast Dasani® Bottled™Sandwich \n with a new line added",
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "PriceTaxation": {
            "price": [
            {
                "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
                "currency": "USD"
            },
            {
                "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
                "currency": "ARS"
            },
            {
                "value": random.randint(1000, 100000),
                "currency": "PTS"
            },
            ],
            "specialPrice": [
            {
                "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
                "currency": "USD"
            },
            {
                "value": random.randint(1000, 100000),
                "currency": "PTS"
            }
            ],
            "isTaxable": True,
            "orderMaxQuantityAllowed": 1000,
            "orderMinQuantityAllowed": 1
        },
        "brand": "Spark1",
        "productSet": "Simple",
        "storeProductId": "134A34",
        "productType": "Duty Free",
        "spotLight": True,
        }
    p_response = post_product(session=store_http_session, data= prod_input_data)
    assert p_response.status_code == 200
    id = p_response.json()["id"]
    prod_input_data["id"] = id
    sleep(5)
    product_compare_get(store_http_session,prod_input_data )
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[id] = prod_input_data

    prod_input_data["PriceTaxation"]["price"][0]['value'] = round(random.uniform(50, 100000.999), random.randint(0, 3))
    prod_input_data["PriceTaxation"]["price"][1]['value'] = round(random.uniform(50, 100000.999), random.randint(0, 3))
    del prod_input_data["PriceTaxation"]['specialPrice'][-1] #remove a price
    p_response_put = put_product(session=store_http_session, id=id, data=prod_input_data)
    assert p_response_put.status_code ==200
    
    sleep(5)
    product_compare_get(store_http_session,prod_input_data )
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[id] = prod_input_data
    
    patch_t = {
           "PriceTaxation": {
                "price": [
                {
                    "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
                    "currency": "USD"
                },
                {
                    "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
                    "currency": "GBP"
                },
                
                {
                    "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
                    "currency": "ARS"
                },
                {
                    "value": random.randint(1000, 10000000),
                    "currency": "PTS"
                },
                ] 
            }
        }
    pathc_r = patch_product(store_http_session, id, patch_t)
    assert pathc_r.status_code   == 200
    prod_input_data["PriceTaxation"]["price"] = patch_t["PriceTaxation"]['price']
    
     
    #test failures
    
    bad_data = copy.deepcopy(prod_input_data)
    bad_data['sku'] = "api_auto_" + uuid_random_name()
    bad_data["PriceTaxation"]["price"][1]['value'] = 123.1234
    put_r = put_product(store_http_session, id, bad_data)              ### PUT
    assert put_r.status_code == 400
    assert "Allowed up to 3 decimal places for price" in put_r.json()['message']
  
    p_response = post_product(session=store_http_session, data= bad_data)
    assert p_response.status_code == 400
    assert "Allowed up to 3 decimal places for price" in p_response.json()['message'] 
    
    bad_data["PriceTaxation"]["price"][1]['value'] = 123.12
    del bad_data["PriceTaxation"]["price"][0] ## delete the fault price
    p_response = post_product(session=store_http_session, data= bad_data)
    assert p_response.status_code == 400
    assert "price value required for default currency " in p_response.json()['message'] 
    
    bad_data_points = copy.deepcopy(prod_input_data)  #### PTS restriction
    bad_data_points['sku'] = "api_auto_" + uuid_random_name()
    bad_data_points["PriceTaxation"]["price"][-1]['value'] = 10000.2
    
    p_response = post_product(session=store_http_session, data= bad_data_points)
    assert p_response.status_code == 400
    assert "price value must be integer for PTS" in p_response.json()['message'] 
    
    p_response = put_product(session=store_http_session, data= bad_data_points, id=id)
    assert p_response.status_code == 400
    assert "price value must be integer for PTS" in p_response.json()['message'] 
    
    sleep(3)
    product_compare_get(store_http_session,prod_input_data )
    sns = get_sns_message_by_id_catalog(prod_input_data["id"], queue_url=CATALOG_SQS_QUEUE_URL)
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[id] = prod_input_data
    

    
#### CATALOG ASSIGMENT
@pytest.fixture(scope='session')
def create_catalog(store_driver, track_product_input_data_map, track_catalog_input_data, store_http_session, Catalog_save_timer):
    dismiss_notification(store_driver, dismiss_all=True)
    create_catalog_helper(store_driver, track_product_input_data_map, track_catalog_input_data, store_http_session, Catalog_save_timer=Catalog_save_timer)

@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16237251)
def test_CA_no_prices(store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_1, AirlineName=AIRLINE_NAME )
    CA_request_for_association(airline_driver)
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve...
    close_active_tab(store_driver)
    close_active_tab(store_driver, not_sure=True)
    g_response = get_route_catalog(session=airline_session, id=cat_id)
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    assert g_response.status_code == 200
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    validate_catalogAssigment(sns)
    assert compare_input_to_expected_data(sns['products'], product_ids)  
    
    
@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16237252)
def test_CA_a_few_products_with_all_prices(store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 

    
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_2, AirlineName=AIRLINE_NAME )
    CA_request_for_association(airline_driver) # the catalog assigment needs to be in request for associalition for store to be able to see it
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[1], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products = [{}, product ]
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[3], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products.append({}) 
    all_products.append(product)
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[4], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products.append(product)
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id)
    CA_save(store_driver, timer_arr=CA_save_timer); 
    sleep(2)
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE)
    close_active_tab(store_driver)
    combined_product_list = [{**a, **b} for a, b in zip(all_products, product_ids)]
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    validate_catalogAssigment(sns)
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(combined_product_list))   
    dismiss_notification(store_driver, dismiss_all=True)
    dismiss_notification(airline_driver, dismiss_all=True)


    
@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16237258)
def test_CA_all_product_prices_and_validation(store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    
    
    # catalog_id = 69020012  #### HARD CODED TEST DATA FROM TEST
    # products_skus = ["uiAuto_LGbA2024_02_22__00_15_05", "uiAuto_KkRH2024_02_22__00_15_05", "uiAuto_LGTQ2024_02_22__00_15_05", "uiAuto_RYbp2024_02_22__00_15_05", "uiAuto_vQvn2024_02_22__00_15_05", "uiAuto_piHU2024_02_22__00_15_05" ]
    
    # catalog_id = 70639 #### HARD CODED TEST DATA FROM TEST
    # products_skus = ["SOK001", "SOK002", "Beer01", "Beer02", "sake01", "sake02"]
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 

    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_3, AirlineName=AIRLINE_NAME ) 
    CA_request_for_association(airline_driver) 
    close_active_tab(airline_driver)
    random_price_value = round(random.uniform(50, 100000.999), random.randint(0, 3))
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    all_products = []
    Enter_CA_price(store_driver, products_skus[0], all_currencies_configured=ALL_DISPLAY_CURRENCIES, currency=ALL_DISPLAY_CURRENCIES[1], price=random_price_value )
    CA_save(store_driver, False);  ### used for multi-currency default price validation
    catalog_assigment_default_currency_validation(store_driver)
    random_price_value_default = round(random.uniform(50, 100000.999), random.randint(0, 3))
    Enter_CA_price(store_driver, products_skus[0], all_currencies_configured=ALL_DISPLAY_CURRENCIES, currency=ALL_DISPLAY_CURRENCIES[0], price=random_price_value_default )
    product = {}
    product['productPriceList'] = [{
            "value": random_price_value_default,
            "currency": ALL_DISPLAY_CURRENCIES[0]
        },
        {
            "value": random_price_value,
            "currency":ALL_DISPLAY_CURRENCIES[1]
        }]
    if track_product_input_data_map:
        product['id'] = find_product_id(track_product_input_data_map, products_skus[0]) ## add id to the body
        product.pop('id', "") if 'id' in product and product['id'] is None else None  
    all_products.append(product)
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    CA_save(store_driver, True, timer_arr=CA_save_timer); sleep(3)  ###### NEED TO TROUBLESHOOT SAVING SPEED
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(all_products))
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    validate_catalogAssigment(sns)
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(all_products))
    
    
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[1], ALL_DISPLAY_CURRENCIES[:-2], track_product_input_data_map)
    all_products.append(product)
    
    for sku in products_skus[2:-1]:
        product = sku_random_prices_for_all_currencies(store_driver, sku, ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
        all_products.append(product)
    
    
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[-1], ALL_DISPLAY_CURRENCIES[:-1], track_product_input_data_map)
    # catalog_assigment_default_currency_validation(store_driver)
    
    #maybe we can check the sns twice??
    CA_save(store_driver, True, timer_arr=CA_save_timer); sleep(5)
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(all_products))
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    try:
        validate_catalogAssigment(sns)
        assert compare_input_to_expected_data(sns['products'], product_sns_convert(all_products))
        print("The correct SNS was verified correctly in the first try!")
    except:
        print("Warning. Failed to find the correct SNS or there was bad schema. Will check again in 5 seconds")
        sleep(5)
        sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
        validate_catalogAssigment(sns)
        print("the SNS was veridied in the second time!")
    random_price_value = random.randint(1, 10000000)
    Enter_CA_price(store_driver, products_skus[-1], all_currencies_configured=ALL_DISPLAY_CURRENCIES, currency=ALL_DISPLAY_CURRENCIES[-1], price=random_price_value )
    product['productPriceList'].append( {
            "value": random_price_value,
            "currency": ALL_DISPLAY_CURRENCIES[-1]
        })
    all_products.append(product)
    
    
    CA_save(store_driver, timer_arr=CA_save_timer)
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE)
    close_active_tab(store_driver) 
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    combined_product_list = [{**a, **b} for a, b in zip(all_products, product_ids)]
    compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    try:
        validate_catalogAssigment(sns)
        assert compare_input_to_expected_data(sns['products'], product_sns_convert(combined_product_list))
        print("SNS Validation was succesfull on the first try!")
    except:
        print("Trying one more time as the sns downloaded may not be the latest one....")
        sleep(3)
        sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
        validate_catalogAssigment(sns)
        assert compare_input_to_expected_data(sns['products'], product_sns_convert(combined_product_list))
    dismiss_notification(store_driver, dismiss_all=True)
    dismiss_notification(airline_driver, dismiss_all=True)

@pytest.mark.reg
@pytest.mark.csv
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16375521)
def test_CA_csv_prices_all( store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 


    # catalog_id = 41303 #### HARD CODED TEST DATA FROM QA ENV
    # products_skus = ["sku_noPrice_configurablefew_currencies__2024_04_09__20_17_11", "simpleProduct_noPrice__2024_04_09__20_17_11", "sku_default_price_few_currencies__2024_04_09__20_17_11", "skuPrice_specialPrice_few_currencies__2024_04_09__20_17_11", "sku_all_prices_only_few_currencies__2024_04_09__20_17_11", "sku_all_priceSpecialPrice_few_currencies__2024_04_09__20_17_11", "sku_price_configurablefew_currencies__2024_04_09__20_17_11", "sku_priceSpecialPrics_configurablefew_currencies__2024_04_09__20_17_11", "sku_afew_priceSpecialPrice1_few_currencies__2024_04_09__20_17_11", "sku_afew_priceSpecialPrice2_few_currencies__2024_04_09__20_17_11", "sku_afew_priceSpecialPrice3_few_currencies__2024_04_09__20_17_11", "sku_afew_priceSpecialPrice4_few_currencies__2024_04_09__20_17_11", "uiAuto_MZDW2024_04_09__19_58_52", "uiAuto_FShP2024_04_09__19_58_52"]
    # product_ids = [{"id": 41289}, {"id": 41290 }, {"id": 41291}, {"id": 41292}, {"id": 41293}, {"id": 41294}, {"id": 41295}, {"id": 41296}, {"id": 41297}, {"id": 41298}, {"id": 41299}, {"id": 41300}, {"id": 41301}, {"id": 41302} ] 
    
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_3, AirlineName=AIRLINE_NAME ) 
    CA_request_for_association(airline_driver)
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)    
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve...    
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus, ALL_DISPLAY_CURRENCIES )
    close_active_tab(store_driver)
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    validate_catalogAssigment(sns)
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))

@pytest.mark.reg
@pytest.mark.csv
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16461776)
def test_CA_csv_prices_afew_additions( store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_3, AirlineName=AIRLINE_NAME ) 
    CA_request_for_association(airline_driver)
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)

    
    
    
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus[1:-3], ALL_DISPLAY_CURRENCIES,{products_skus[4]: [ALL_DISPLAY_CURRENCIES[1],ALL_DISPLAY_CURRENCIES[-2]]} )
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    validate_catalogAssigment(sns)
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))

    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id)
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus[:-2], ALL_DISPLAY_CURRENCIES[:-2],{products_skus[-3]: [ALL_DISPLAY_CURRENCIES[1],ALL_DISPLAY_CURRENCIES[2]]} )
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve...    
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    validate_catalogAssigment(sns)
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))
    dismiss_notification(airline_driver, dismiss_all=True)
    dismiss_notification(store_driver, dismiss_all=True)
    sleep(5)
sns_validate = {}
@pytest.mark.qt
@pytest.mark.reg
# @pytest.mark.xfail
@pytest.mark.usefixtures("create_catalog")
def test_api_route_catalog_price(airline_session, track_catalog_input_data, track_product_input_data_map, store_http_session, json_schema_file, ref_resolver):
    skus=[]
    for id, product in track_product_input_data_map.items():
        skus.append(product["sku"]) 
    cat_json =  {
            "name": f"catalog_auto_{uuid_random_name()}",
            "products": skus
            }
    
    post_catalog_response = post_catalog(store_http_session, cat_json)
    assert post_catalog_response.status_code == 200
    catalog_id = post_catalog_response.json()['id']
    product_ids = [{"productId": int(i), "airlineCategory": [airlinecategory_1var]} for i in track_product_input_data_map.keys()]
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # product_ids = [{"productId": 96, "airlineCategory": [airlinecategory_1var]}, {"productId": 101, "airlineCategory": [airlinecategory_1var]}, {"productId": 104, "airlineCategory": [airlinecategory_1var]}, {"productId": 108, "airlineCategory": [airlinecategory_1var]}, {"productId": 176, "airlineCategory": [airlinecategory_1var]}, {"productId": 689, "airlineCategory": [airlinecategory_1var]}, {"productId": 679, "airlineCategory": [airlinecategory_1var]}, {"productId": 675, "airlineCategory": [airlinecategory_1var]}, {"productId": 669, "airlineCategory": [airlinecategory_1var]}, {"productId": 670, "airlineCategory": [airlinecategory_1var]}] 
        
    catalog_id = int(catalog_id)
    route_catalog_post_data = {
    "name": f"ca_workflow_{uuid_random_name()}",
    "catalog": {
        "id": catalog_id,
        "products": product_ids,
    },
    "sector":[{"id":sector_id}]
    }
    req = post_route_catalog(session=airline_session, data=route_catalog_post_data )
    assert req.status_code == 200
    cat_id = req.json()['id']
    
    route_catalog_workflow_helper(airline_session, cat_id)
    
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    validate_catalogAssigment(sns)
    
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    validate(instance=get_route_catalog_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponseId'], resolver=ref_resolver)
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, id=cat_id)
    
    for product in product_ids:  ### MISSING USD (DEFAULT CURRENCY)
        default_currency = round(random.uniform(50, 100000.999), random.randint(0, 3))
        product['productPriceList'] = [
            {
               "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
               "currency": "GBP"
            },
            {
               "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
               "currency": "ARS"
            },
            {
               "value": round(random.randint(0, 10000)),
               "currency": "PTS"
            }
        ]
    put_price = {
        "catalog": {
            "id": catalog_id,
            "products": product_ids,
        }
    }
    put_response = put_route_catalog_price(store_http_session, cat_id, put_price)
    assert put_response.status_code == 400
    
    for product in product_ids:
        product['productPriceList'] = [
            {
               "value": 1.2323,
               "currency": "USD"
            }
        ]
    put_response = put_route_catalog_price(store_http_session, cat_id, put_price)
    assert put_response.status_code == 400
    
    for product in product_ids:
        default_currency = round(random.uniform(50, 100000.999), random.randint(0, 3))
        product['productPriceList'] = [
            {
               "value": default_currency,
               "currency": "USD"
            }
        ]
    put_response = put_route_catalog_price(store_http_session, cat_id, put_price)
    assert put_response.status_code == 200
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    validate(instance=get_route_catalog_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponseId'], resolver=ref_resolver)
    # assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(product_ids))
    
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    validate_catalogAssigment(sns)
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_ids, True))
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, id=cat_id)
    
    for product in product_ids:
        default_currency = round(random.uniform(50, 100000.999), random.randint(0, 3))
        product['productPriceList'] = [
            {
               "value": default_currency,
               "currency": "USD"
            },
            {
               "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
               "currency": "GBP"
            },
            {
               "value": round(random.uniform(50, 100000.999), random.randint(0, 3)),
               "currency": "ARS"
            },
            {
               "value": round(random.randint(0, 10000)),
               "currency": "PTS"
            }
        ]
    put_price = {
        "catalog": {
            "id": catalog_id,
            "products": product_ids,
        }
    }
    put_response = put_route_catalog_price(store_http_session, cat_id, put_price)
    assert put_response.status_code == 200
    
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    validate(instance=get_route_catalog_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponseId'], resolver=ref_resolver)
    # assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(product_ids))
    
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    global sns_validate 
    sns_validate = sns
    validate_catalogAssigment(sns)
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_ids, True))
    global api_sns_payload
    api_sns_payload = sns

@pytest.mark.reg
@pytest.mark.xfail
def test_validate_full_payload_data():
    validate_catalogAssigment(sns_validate)
    assert api_sns_payload["associateFlight"][0]['sectors'][0]['id'] == sector_id
    assert api_sns_payload["associateFlight"][0]['sectors'][0]['name']['en'] == TEST_SECTOR__DEFAULT_CURR_1
    assert api_sns_payload["associateFlight"][0]['sectors'][0]['originAirportIATACode'] == ORIGIN_AIRPORT
    assert api_sns_payload["associateFlight"][0]['sectors'][0]['destinationAirportIATACode'] == DESTINATION_AIRPORT

@pytest.fixture(scope="session")
def sector_ids(airline_session):
    get_se = get_sector(airline_session)
    assert get_se.status_code == 200
    sector_id1 = get_se.json()['data']['sectors'][0]['id']
    sector_id2 = get_se.json()['data']['sectors'][1]['id']
    sector_id3 = get_se.json()['data']['sectors'][3]['id']
    return [sector_id1, sector_id2, sector_id3]
 
@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
def test_route_catalog_flight(airline_session, track_catalog_input_data, track_product_input_data_map, store_http_session, json_schema_file, ref_resolver, sector_ids):
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # catalog_id = 69080327 #### HARD CODED TEST DATA FROM DEV ENV
    data_flight = {
    "flightRouteNumber": "Flight_Num_auto_" + uuid_random_name(),
    "flightBeginDateTime": "2026-01-05 21:00",
    "flightEndDateTime": "2050-01-06 17:35",
    "sectors": [
            {
            "id": sector_ids[0],
            "sequence": 1
            },
            {
            "id": sector_ids[1],
            "sequence": 2
            },
            {
            "id": sector_ids[2],
            "sequence": 3
            }
        ]
    }
    
    post_f = post_flight(airline_session, data_flight)
    assert post_f.status_code == 200
    flight_id = post_f.json()['id']
    sns = get_sns_message_by_id_catalog(flight_id, FLIGHT_SQS_QUEUE_URL)
    
    catalog_id = int(catalog_id)
    route_catalog_post_data = {
    "name": f"ca_workflow_{uuid_random_name()}",
    "catalog": {
        "id": catalog_id
    },
    "flight":[{"id":flight_id, "sectors":[sector_ids[0], sector_ids[1]]}]
    }
    
    req = post_route_catalog(session=airline_session, data=route_catalog_post_data )
    assert req.status_code == 200
    cat_id = req.json()['id']
    



    route_catalog_workflow_helper(airline_session, cat_id)
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    validate(instance=get_route_catalog_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponseId'], resolver=ref_resolver)    
    
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    assert "associateFlight" in sns
    validate_catalogAssigment(sns)
    assert len(sns['associateFlight'][0]['sectors']) == 2
    
    
    # assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_ids, True))
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, id=cat_id)
    
    edit_data = {
    "catalogAssignmentId" : cat_id,
    "status" : "editBaseData"
    }
    P_w = post_workflow(session=airline_session, data=edit_data)
    P_w.status_code == 200
    
    route_catalog_post_data = {
    "name": f"ca_workflow_{uuid_random_name()}",
    "catalog": {
        "id": catalog_id
    },
    "flight":[{"id":flight_id, "sectors":[sector_ids[0], sector_ids[1],sector_ids[2]]}]
    }
    
    req = put_route_catalog(id=cat_id, session=airline_session, data=route_catalog_post_data )
    assert req.status_code == 200
    
    
    edit_data = {
    "catalogAssignmentId" : cat_id,
    "status" : "request"
    }
    P_w = post_workflow(session=airline_session, data=edit_data)
    P_w.status_code == 200
    
    get_workflow(session=airline_session, id=cat_id)
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    assert "associateFlight" in sns
    validate_catalogAssigment(sns)
    assert len(sns['associateFlight'][0]['sectors']) == 3
    
    
@pytest.mark.reg
@pytest.mark.xfail
@pytest.mark.usefixtures("create_catalog")
def test_route_catalog_sector_with_flight(airline_session, track_catalog_input_data, track_product_input_data_map, store_http_session, json_schema_file, ref_resolver, sector_ids):
    
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # catalog_id = 69080327 #### HARD CODED TEST DATA FROM DEV ENV
    
    
    catalog_id = int(catalog_id)
    route_catalog_post_data = {
    "name": f"ca_workflow_{uuid_random_name()}",
    "catalog": {
        "id": catalog_id
    },
    "sector":[
        {
            "id":sector_ids[0]
        },
        {
            "id":sector_ids[1] 
        }]
    }
    
    req = post_route_catalog(session=airline_session, data=route_catalog_post_data )
    assert req.status_code == 200
    cat_id = req.json()['id']
    



    route_catalog_workflow_helper(airline_session, cat_id)
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    validate(instance=get_route_catalog_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponseId'], resolver=ref_resolver)    
    
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    assert "associateFlight" in sns
    validate_catalogAssigment(sns)
    
    
    # assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_ids, True))
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, id=cat_id)
    
    edit_data = {
    "catalogAssignmentId" : cat_id,
    "status" : "editBaseData"
    }
    P_w = post_workflow(session=airline_session, data=edit_data)
    P_w.status_code == 200
    
    route_catalog_post_data = {
    "name": f"ca_workflow_{uuid_random_name()}",
    "catalog": {
        "id": catalog_id
    },
    "sector":[
        {
            "id":sector_ids[0]
        },
        {
            "id":sector_ids[1] 
        },
        {
            "id":sector_ids[2] 
        }]
    }    
    req = put_route_catalog(id=cat_id, session=airline_session, data=route_catalog_post_data )
    assert req.status_code == 200
    
    
    edit_data = {
    "catalogAssignmentId" : cat_id,
    "status" : "request"
    }
    P_w = post_workflow(session=airline_session, data=edit_data)
    P_w.status_code == 200
    
    get_workflow(session=airline_session, id=cat_id)
    sleep(10)
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    assert "associateFlight" in sns
    validate_catalogAssigment(sns)
    
@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
def test_route_catalog_sector_without_flights(airline_session, track_catalog_input_data, track_product_input_data_map, store_http_session, json_schema_file, ref_resolver):

    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # catalog_id = 69080327 #### HARD CODED TEST DATA FROM DEV ENV
    get_air = get_airports(airline_session)
    assert get_air.status_code ==200 
    airport1 = get_air.json()['data']['airports'][0]['icaoCode']
    airport2 = get_air.json()['data']['airports'][1]['icaoCode']
    
    
    
    sector_ids = []
    for i in range(2):
        sec_data = {
        "sectorName": "api_auto_sector_"+uuid_random_name(),
        "summary": "summary",
        "destination": airport1,
        "origin": airport2,
        "distance": 900
        }
        post_sec = post_sector(airline_session, sec_data )
        assert post_sec.status_code == 200
        sector_ids.append(post_sec.json()['id'])
    
    

    catalog_id = int(catalog_id)
    route_catalog_post_data = {
    "name": f"ca_workflow_{uuid_random_name()}",
    "catalog": {
        "id": catalog_id
    },
    "sector":[
        {
            "id":sector_ids[0]
        }]
    }

    req = post_route_catalog(session=airline_session, data=route_catalog_post_data )
    assert req.status_code == 200
    cat_id = req.json()['id']
    route_catalog_workflow_helper(airline_session, cat_id)
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    validate(instance=get_route_catalog_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponseId'], resolver=ref_resolver)    

    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    assert len(sns['associateSector']) == 1
    validate_catalogAssigment(sns)


    # assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_ids, True))
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, id=cat_id)

    edit_data = {
    "catalogAssignmentId" : cat_id,
    "status" : "editBaseData"
    }
    P_w = post_workflow(session=airline_session, data=edit_data)
    P_w.status_code == 200

    route_catalog_post_data = {
    "name": f"ca_workflow_{uuid_random_name()}",
    "catalog": {
        "id": catalog_id
    },
    "sector":[
        {
            "id":sector_ids[0]
        },
        {
            "id":sector_ids[1] 
        }]
    }    
    req = put_route_catalog(id=cat_id, session=airline_session, data=route_catalog_post_data )
    assert req.status_code == 200


    edit_data = {
    "catalogAssignmentId" : cat_id,
    "status" : "request"
    }
    P_w = post_workflow(session=airline_session, data=edit_data)
    P_w.status_code == 200

    get_workflow(session=airline_session, id=cat_id)
    sns = get_sns_message_by_id_catalog(cat_id, queue_url=CATALOG_SQS_QUEUE_URL)
    assert "associateSector" in sns
    assert len(sns['associateSector']) == 2
    validate_catalogAssigment(sns)