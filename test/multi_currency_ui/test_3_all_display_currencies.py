import pytest, math
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
import os
from utils.helper import *
from utils.ui_helper import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from jamatest import jamatest
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains


ALL_DISPLAY_CURRENCIES = ["USD", "GBP", "ARS", "AUD", "BRL", "CAD", "CLP", "CNY", "COP", "CZK", "DKK", "EUR", "HKD", "HUF", "ISK", "INR", "ILS", "JPY", "KRW", "MYR","MXN", "MAD", "NZD", "NOK", "PHP", "PLN", "SAR", "RUB" , "SGD", "ZAR", "SEK", "CHF", "TWD", "THB", "TRY", "VND", "PTS" ]
@pytest.fixture(scope="session", autouse=True)
def setup_store_all_display_currencies(store_driver):
    configure_store_display_currencies(store_driver, ALL_DISPLAY_CURRENCIES)
    close_active_tab(store_driver)

@jamatest.test(16071056) 
@pytest.mark.qt
@pytest.mark.reg
@pytest.mark.csv
def test_csv_import_a_few_display_currencies( store_http_session, track_product_input_data_map, store_driver, pac_user_driver):
   curr_dir = os.path.dirname(os.path.realpath(__file__))
   csv_reference = os.path.join(curr_dir,"csv_files", 'import_all_pre_validations.csv'  )
#    logger.debug("Full path of the CSV file: " + str(csv_reference))
   test_filename = generate_test_csv(csv_reference)
   test_json_products_list = product_csv_to_json(test_filename)
   upload_csv_product(store_driver, test_filename)
   sleep(3)
   close_active_tab(store_driver)
   num_products = len(test_json_products_list)
   temp_list = test_json_products_list
   logger.debug("Number of products created to verify: %s", num_products)
   sleep(25)
   all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
   for product in all_get_product_list:
      for p_in in test_json_products_list:
         if product['sku'] == p_in['sku']:
            track_product_input_data_map[product['id']] = p_in
            logger.info("Found a matching product %s", product['sku'] )
            sns = download_sns(pac_user_driver, product["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
            validate_product(sns)
            assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in))
            num_products= num_products-1
            temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
            assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
         elif "fails" in product['sku']: # to make sure failed products are not getting created
            product_id = product["id"]
            raise Exception(f"There was a product with fails {product_id}")
   if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")

@pytest.mark.qt
@pytest.mark.product_c
@pytest.mark.reg
@jamatest.test(15862744, 1)
def test_create_product_all_display_currencies_no_price(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data


@pytest.mark.product_c
@jamatest.test(15862744, 2)
@pytest.mark.reg
def test_create_product_all_display_currencies_default_price_only( pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, price="random", isTaxable=True, prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15862744, 3)
@pytest.mark.reg
def test_create_product_all_display_currencies_defaults_price_special_price(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, price="random", special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15862744, 4)
@pytest.mark.reg
def test_create_product_all_display_currencies_price_only_default_display_currencies(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random",   prod_input_data=prod_input_data) ##MYR
    enter_product_price( store_driver, currency="HKD",price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="MYR",price="random", prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
    
@pytest.mark.product_c
@jamatest.test(15862744, 5) 
@pytest.mark.reg
def test_create_product_all_display_currencies_price_special_price_mix(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random",  special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random", special_price="random",  prod_input_data=prod_input_data) ##MYR
    enter_product_price( store_driver, currency="HKD",price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="MYR",price="random", prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

    
@pytest.mark.product_c
@jamatest.test(15862744, 6)
@pytest.mark.reg
def test_create_product_all_display_currencies_price_special_price(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random",  special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random", special_price="random",  prod_input_data=prod_input_data) ##MYR
    enter_product_price( store_driver, currency="HKD",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="MYR",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random", special_price="random", isTaxable=True, prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15862744, 7)
@pytest.mark.reg
def test_create_product_all_display_currencies_all_price(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    for i in ALL_DISPLAY_CURRENCIES:
        enter_product_price( store_driver, currency=i, price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@pytest.mark.product_c
@jamatest.test(15862744, 8)
@pytest.mark.reg
def test_create_product_all_display_currencies_all_price_special_price_and_variants_disable_check(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    for i in ALL_DISPLAY_CURRENCIES:
        enter_product_price( store_driver, currency=i, price="random", special_price="random", prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer, close_tab_on_save=False)
    product_variants_button_state_check(store_driver, expected_state='true')
    close_active_tab(store_driver)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
    
    
@pytest.mark.product_c
# @jamatest.test(15862744, 11) #"USD", "GBP", "ARS", "AUD", "BRL", "CAD", "CLP", "CNY",
def test_create_product_all_currencies_configurable_with_price_only(pac_user_driver, store_driver, json_reference_product, store_http_session, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, product_type="Configurable",json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD",price="random",  isTaxable=True, prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random",   prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random",   prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random",   prod_input_data=prod_input_data)

    product_save_and_publish(store_driver, close_tab_on_save=False, timer_arr=product_save_timer)
    product_variants_button_state_check(store_driver, expected_state='false')
    parent_box = store_driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
    parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

    
@pytest.mark.product_c
# @jamatest.test(15862744, 13)
def test_create_product_4_currencies_configurable_price_specialPrice(pac_user_driver, store_driver, json_reference_product, store_http_session, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, product_type="Configurable",json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random", isTaxable=True, prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="GBP",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="ARS",price="random", special_price="random", prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="AUD",price="random", special_price="random",  prod_input_data=prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random", special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, timer_arr=product_save_timer)
    product_variants_button_state_check(store_driver, expected_state='false')
    parent_box = store_driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
    parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()
    product_compare_get(store_http_session,prod_input_data )
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

    
@pytest.mark.product_c
@pytest.mark.reg
@jamatest.test(15862744, 9)
def test_create_product_all_display_currencies_message_pop_up_default_price_required(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="GBP",price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver, "price")
    enter_product_price( store_driver, currency="USD", price="2453")  ### USD default price
    prod_input_data['PriceTaxation']['price'].insert(0, {"value":2453, "currency": "USD"})
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
    
@pytest.mark.product_c
@pytest.mark.reg
@jamatest.test(15862744, 10)
def test_create_product_display_all_currencies_message_pop_up_default_specialPrice_required(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", prod_input_data=prod_input_data)  ### USD default price
    enter_product_price( store_driver, currency="GBP", price="random", special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    default_currency_validation(store_driver, field="specialPrice")
    enter_product_price( store_driver, currency="USD", special_price="20")  ### USD special price
    # prod_input_data['PriceTaxation']['price'].insert(0, {"value":22, "currency": "USD"})
    prod_input_data['PriceTaxation']['specialPrice'].insert(0, {"value":20, "currency": "USD"})
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

@jamatest.test(15862744, 11)
@pytest.mark.reg
def test_all_currencies_modify_product_add_display_currency(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer):
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']), spotLight=True)
    enter_product_price( store_driver, currency="USD", price="random", special_price="random", prod_input_data=prod_input_data)  ### USD default price
    enter_product_price( store_driver, currency="ARS",price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, close_tab_on_save=False, save_succesfull_check=False)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    product_compare_get(store_http_session,prod_input_data)
    enter_product_price( store_driver, currency="PTS",price="random", special_price="random",  prod_input_data=prod_input_data)
    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    product_compare_get(store_http_session,prod_input_data)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data
@pytest.mark.product_c
@pytest.mark.reg
@jamatest.test(15862744, 12)
def test_create_product_3_decimal_points_restriction_price_special_prices_all_currencies(pac_user_driver, store_http_session, store_driver, json_reference_product, track_product_input_data_map, product_save_timer): 
    prod_input_data = create_product(store_driver, json_data=copy.deepcopy(json_reference_product['v1']))
    for i in ALL_DISPLAY_CURRENCIES:
        price_20_decimials = round(random.uniform(50, 100000.999999), random.randint(7, 10))
        specialPrice_20_decimals = round(random.uniform(50, 100.9999999), random.randint(7, 10))
        enter_product_price( store_driver, currency=i, price=price_20_decimials)
        if i == 'PTS':
            detect_PTS_decimals_pop_up(store_driver)
            price_3_decimials = int(price_20_decimials)
        else:
            detect_4_decimals_popup(store_driver)
            price_3_decimials = troucate(price_20_decimials, 3 )
        enter_product_price( store_driver, currency=i, special_price=specialPrice_20_decimals)
        if i == 'PTS':
            detect_PTS_decimals_pop_up(store_driver)
            specialPrice_3_decimials = int(specialPrice_20_decimals)
        else:
            detect_4_decimals_popup(store_driver)
            specialPrice_3_decimials = troucate(specialPrice_20_decimals, 3 )
        prod_input_data['PriceTaxation']['price'].append({"value":price_3_decimials, "currency": i})
        prod_input_data['PriceTaxation']['specialPrice'].append({"value":specialPrice_3_decimials, "currency": i})

    product_save_and_publish(store_driver, timer_arr=product_save_timer)
    sns = download_sns(pac_user_driver, prod_input_data["id"], download_path=DOWNLOAD_PATH, aws_sns_creator='store' )
    validate_product(sns)
    product_compare_get(store_http_session, prod_input_data )
    assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=prod_input_data))
    track_product_input_data_map[prod_input_data['id']] = prod_input_data

def troucate(number, decimals=3):
    factor = 10 ** decimals
    shifter_num = number * factor
    tracated_number = math.floor(shifter_num)
    return tracated_number/factor


######## CATALOG ASSIGMENT    
@pytest.fixture(scope='session')
def create_catalog(store_driver, track_product_input_data_map, track_catalog_input_data, Catalog_save_timer):
    create_catalog_helper(store_driver, track_product_input_data_map, track_catalog_input_data, Catalog_save_timer=Catalog_save_timer)
    dismiss_notification(store_driver, dismiss_all=True)

@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16237272)
def test_CA_no_prices(pac_user_driver, store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session):
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    # catalog_id = 69020012  #### TEST DATA FROM DEV
    # products_skus = ["uiAuto_LGbA2024_02_22__00_15_05", "uiAuto_LGbA2024_02_22__00_15_05", "uiAuto_LGTQ2024_02_22__00_15_05", "uiAuto_LGTQ2024_02_22__00_15_05", "uiAuto_vQvn2024_02_22__00_15_05", "uiAuto_vQvn2024_02_22__00_15_05" ]
    print("before create_catalog_assigment" )
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_ALL_DISPLAY_CURR_1, AirlineName=AIRLINE_NAME )
    print(" before CA_request_for_association")
    CA_request_for_association(airline_driver)
    close_active_tab(airline_driver)
    print("before store_open_catalog_assigment")
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    print("store_CA_approve_for_association")
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve...
    download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )   
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    close_active_tab(store_driver)
    close_active_tab(store_driver, not_sure=True)
    # get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    # assert get_route_catalog_response.status_code == 200
    # compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], all_products)
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert "price" not in sns
    assert "specialPrice" not in sns
    dismiss_notification(airline_driver, dismiss_all=True)
    dismiss_notification(store_driver, dismiss_all=True)

    
    
@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16237266)
def test_CA_a_few_products_with_all_prices(pac_user_driver, store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    
    # catalog_id = 69002866  #### TEST DATA FROM DEV
    # products_skus = ["headphones", "Baguette", "CaliforniaBurrito", "FringeCrossbody_sku", "LeatherTote_sku", "QuiltedShoulder_sku", "969699", "969701", "969703", "969803", "969804", "Pepsi", "moet-chandon", "csv_dew" ]

    
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_ALL_DISPLAY_CURR_2, AirlineName=AIRLINE_NAME )
    CA_request_for_association(airline_driver) # the catalog assigment needs to be in request for associalition for store to be able to see it
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE)
    download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )   
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[1], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products = [{}, product ]
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[3], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products.append({}) ### not sure if its needed
    all_products.append(product)
    product = sku_random_prices_for_all_currencies(store_driver, products_skus[4], ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
    all_products.append(product)
    CA_save(store_driver, timer_arr=CA_save_timer); 
    sleep(2)
    close_active_tab(store_driver)
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], all_products)
    try:
        sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )  #### need to download the last one
        assert compare_input_to_expected_data(sns['products'], product_sns_convert(all_products))
    except:
        logger.info("the verification for the SNS contents failed on the first attempt.")
        sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )  #### need to download the last one
        assert compare_input_to_expected_data(sns['products'], product_sns_convert(all_products))
        logger.info("the verification for the SNS contents succeded on the second attemp... likely the correct sns was not picked up initially")
    
    
    
@pytest.mark.reg
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16237267)
def test_CA_all_product_prices(pac_user_driver, store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, CA_save_timer):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    
    # catalog_id = 69002866  #### TEST DATA FROM DEV
    # products_skus = ["headphones", "Baguette", "CaliforniaBurrito", "FringeCrossbody_sku", "LeatherTote_sku", "QuiltedShoulder_sku", "969699", "969701", "969703", "969803", "969804", "Pepsi", "moet-chandon", "csv_dew" ]
    
    # catalog_id = 55 #### HARD CODED TEST DATA FROM local env
    # products_skus = ["skuxxx1", "skuxxx2", "skuxxx3"]
    # cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value="sec1" )

    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_ALL_DISPLAY_CURR_3, AirlineName=AIRLINE_NAME ) ## needs to be uncommented
    CA_request_for_association(airline_driver) 
    close_active_tab(airline_driver)
    random_price_value = round(random.uniform(50, 100000.999), random.randint(0, 3))
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE)
    download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )   
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    Enter_CA_price(store_driver, products_skus[0], all_currencies_configured=ALL_DISPLAY_CURRENCIES, currency=ALL_DISPLAY_CURRENCIES[0], price=random_price_value )
    CA_save(store_driver, False); 
    catalog_assigment_default_currency_validation(store_driver)
    all_products = []
    for sku in products_skus:
        product = sku_random_prices_for_all_currencies(store_driver, sku, ALL_DISPLAY_CURRENCIES, track_product_input_data_map)
        all_products.append(product)
    CA_save(store_driver, timer_arr=CA_save_timer); 
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    close_active_tab(store_driver)
    
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    combined_product_list = [{**a, **b} for a, b in zip(all_products, product_ids)]
    compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(combined_product_list))
    dismiss_notification(store_driver, dismiss_all=True)
    dismiss_notification(airline_driver, dismiss_all=True)
    
    
    
@pytest.mark.reg
@pytest.mark.csv
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16375533)
def test_CA_csv_prices_all( store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, pac_user_driver):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 
    
    # catalog_id = 69002866  #### TEST DATA FROM DEV
    # products_skus = ["headphones", "Baguette", "CaliforniaBurrito", "FringeCrossbody_sku", "LeatherTote_sku", "QuiltedShoulder_sku", "969699", "969701", "969703", "969803", "969804", "Pepsi", "moet-chandon", "csv_dew" ]
    # product_ids = [ {"id": 38005}, {"id": 18597}, {"id": 51711}, {"id":80258}, {"id":80248}, {"id":80256}, {"id":52689}, {"id":52691}, {"id":52693},{ "id":52702}, {"id":52703}, {"id":38009}, {"id":38000}, {"id":69027956} ]
    
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_3, AirlineName=AIRLINE_NAME ) 
    CA_request_for_association(airline_driver)
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)    
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus, ALL_DISPLAY_CURRENCIES )
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve... 
    download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )   
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    close_active_tab(store_driver)
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))
    dismiss_notification(airline_driver, dismiss_all=True)
    dismiss_notification(store_driver, dismiss_all=True)


@pytest.mark.reg
@pytest.mark.qt
@pytest.mark.csv
@pytest.mark.usefixtures("create_catalog")
@jamatest.test(16461797)
def test_CA_csv_prices_afew_additions( store_driver, airline_driver, track_product_input_data_map, track_catalog_input_data, airline_session, pac_user_driver):
    store_driver.refresh()
    airline_driver.refresh()
    sleep(10)
    catalog_id, products_skus = list(track_catalog_input_data.items())[-1]
    products_skus = products_skus['products']
    product_ids = [{"id": int(i)} for i in track_product_input_data_map.keys()]
    
    
    # catalog_id = 112 #### HARD CODED TEST DATA FROM PAC ENV
    # products_skus = ["skuxxx4", "skuxxx5", "skuxxx6", "skuxxx7", "productTomasxxc1-TG", "productMason48", "uiAuto_qrWH2024_03_21__15_29_51", "uiAuto_niHJ2024_03_21__15_29_51", "uiAuto_bWFF2024_03_21__15_29_51", "uiAuto_KRTH2024_03_21__15_29_51"]
    # product_ids = [{"id": 96}, {"id": 101 }, {"id": 104}, {"id": 108}, {"id": 176}, {"id": 689}, {"id": 679}, {"id": 675}, {"id": 669}, {"id": 670}] 
    
    cat_id = create_catalog_assigment(airline_driver, catalog_id, products_skus=products_skus, filter_value=TEST_SECTOR_4_DISPLAY_CURR_3, AirlineName=AIRLINE_NAME ) 
    CA_request_for_association(airline_driver)
    close_active_tab(airline_driver)
    store_open_catalog_assigment(store_driver, cat_id, products_skus=products_skus, airline_name=AIRLINE_NAME, store_name=STORE_NAME)
        
    store_CA_approve_for_association(store_driver, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE) ## in case airlines don't have auto approve... 
    download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, cat_id, retries=1)
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus[1:-3], ALL_DISPLAY_CURRENCIES,{products_skus[4]: [ALL_DISPLAY_CURRENCIES[1],ALL_DISPLAY_CURRENCIES[-2]]} )
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))
    dismiss_notification(store_driver)
    
    product_list = csv_ca_modify_price_helper(store_driver, DOWNLOAD_PATH, products_skus[:-2], ALL_DISPLAY_CURRENCIES[:-2],{products_skus[-3]: [ALL_DISPLAY_CURRENCIES[1],ALL_DISPLAY_CURRENCIES[2]]} )
    combined_product_list = [{**a, **b} for a, b in zip(product_list, product_ids)] ## to add IDs
    get_route_catalog_response = get_route_catalog(session=airline_session, id=cat_id)
    assert get_route_catalog_response.status_code == 200
    assert compare_input_to_expected_data(get_route_catalog_response.json()['data']['catalogsAssignments']['catalog']['products'], route_catalog_transform(combined_product_list))
    sns = download_sns(pac_user_driver, cat_id, download_path=DOWNLOAD_PATH )
    assert compare_input_to_expected_data(sns['products'], product_sns_convert(product_list))
    dismiss_notification(airline_driver, dismiss_all=True)
    dismiss_notification(store_driver, dismiss_all=True)
    sleep(5)
    close_active_tab(store_driver)
    close_active_tab(store_driver, not_sure=True)