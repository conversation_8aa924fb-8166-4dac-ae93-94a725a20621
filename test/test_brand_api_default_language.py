import logging
from io import BytesIO

import pytest
from PIL import Image
# from networkx.algorithms.tree.branchings import random_string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
import os

#from traits.trait_types import true, false

from utils.helper import *
from utils.ui_helper import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from jamatest import jamatest
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hai<PERSON>
from jsonschema import validate
import yaml
from jsonschema.validators import RefResolver
from datetime import date, datetime


@pytest.fixture(scope='session')
def speciality_attribute_id(store_http_session):
    r = get_speciality_attribute(session=store_http_session, limit=5)
    return r.json()['data']['SpecialityAttributes'][0]['id']

### -------------------- BRAND APIs - POST, GET by ID, GET ALL ---------------------------------------------

@pytest.mark.reg
@jamatest.test(21314959)
def test_post_brand_no_images_SNS(store_http_session, json_schema_file, ref_resolver):
    """ Test payload version 2, without images. Validate name propagation to other languages"""
    payload = {
        "name": {"en": "api_auto - " + uuid_random_name()}
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']

    ### SNS Schema validation placeholder line
    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    # ------VALIDATE SNS FOR BRAND NAME-----
    sns = get_sns_message_by_id_catalog(id)
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"SNS VALIDATED SUCCESSFULLY {sns['name']}")


@pytest.mark.reg
@jamatest.test(21314955)
def test_post_brand_with_images_SNS(store_http_session, json_schema_file, ref_resolver):
    """ Test payload version 2, with images. Validate name propagation to other languages"""
    payload = {
        "name": {"en": "api_auto - " + uuid_random_name()},
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }

    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']

    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    #------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert sns['image'], "SNS received an empty image list"
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"Images received in SNS {sns['image']}")
    print(f"Images sent in Payload {payload['assets']['images']}")


@pytest.mark.reg
@jamatest.test(21314960)
def test_post_brand_wrong_image_size(store_http_session, json_schema_file, ref_resolver):
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name()
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x1000.png",
                 "brand_image16_9": "https://placehold.jp/1780x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    #assert "Invalid brand_image1_1 image resolution" in p_json['message']
    assert "Invalid brand_image1_1 resolution. Resolution should be at least 100x100 with 1:1 ratio." in p_json['message']



@pytest.mark.reg
@jamatest.test(21314961)
def test_post_brand_invalid_image_url(store_http_session, json_schema_file, ref_resolver):
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name()
        },
        "assets": {

            "images": [
                {"brand_image1_1": "https://placfsdfsdehold.jp/100x100.png",
                 "brand_image16_9": "https://placdfdsfehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Image download: https://placfsdfsdehold.jp/100x100.png failed with HTTP code 0" in p_json['message']


@pytest.mark.reg
@jamatest.test(21314965)
def test_post_brand_unicode_name_SNS(store_http_session, json_schema_file, ref_resolver):
    payload = {
        "name": {
            "en": "api_auto-ナ" + uuid_random_name()
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Passed.  200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']

    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    # assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    # validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    # sns = get_sns_message_by_id_catalog(id)
    # ### GET Schema validation placeholder line
    # validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)

    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert sns['image'], "SNS received an empty image list"
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"Images received in SNS {sns['image']}")
    print(f"Images sent in Payload {payload['assets']['images']}")


@pytest.mark.reg
@jamatest.test(21314966)
def test_post_brand_large_payload(store_http_session, json_schema_file, ref_resolver):
    payload = {
        "name": {
            "en": "api_auto -dsaddsdsdsdsdds " + uuid_random_name(),
            "ja": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "ms": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "es": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "fr": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "de": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "it": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "pt_BR": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "ru": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "fil": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "el": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "is": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "mn": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "id": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e",
            "zh_Hans": "api_auto - 8d6e983d_f37c_447f_8488_2ac3c82dce2e"

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand name field [en] should not exceed 50 characters." in p_json['message']


@pytest.mark.reg
@jamatest.test(21314967)
def test_post_brand_invalid_image_format_xml(store_http_session, json_schema_file, ref_resolver):
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name()
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.xml",
                 "brand_image16_9": "https://placehold.jp/178x100.xml"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    #assert "File format is not valid/Enter valid format for image Ex PNG, JPG" in p_json['message']
    assert "File format is not valid. Enter valid format for Ex PNG, JPG" in p_json['message']


@pytest.mark.reg
@jamatest.test(21314958)
### Validations
def test_post_brand_same_name_validation_SNS(store_http_session, json_schema_file, ref_resolver):
    """ Test payload version 2, without images. Validate name propagation to other languages"""
    random_string = uuid_random_name()
    payload = {
        "name": {
            "en": "api_auto_B " + random_string
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    sns = get_sns_message_by_id_catalog(id)
    assert payload['name'] == sns["name"]

    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "A brand already exists with same name." == p_json['message']
    assert p_json["success"] == False, "'success' is missing in json response if it's not True"
    assert id == p_json['id']

    ##try with lowercase
    payload['name']['en'] = "api_auto_b " + random_string
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand validation failed for duplicate name..."
    p_json = p_brand.json()
    assert "A brand already exists with same name." in p_json['message']
    id = p_json['id']
    assert p_json["success"] == False, "'success' is missing in json response if it's not True"

    ## create a new brand and try to update the name using put/patch
    payload_new = copy.deepcopy(payload)
    payload_new['name']['en'] = "api_auto_a " + random_string
    p_brand = post_brand(store_http_session, payload_new)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    id_new = p_json['id']

    put_brand_response = put_brand(store_http_session, payload, id_new)
    assert put_brand_response.status_code == 400, f"POST Brand validation failed for duplicate name..."
    put_brand_json = put_brand_response.json()
    put_brand_json['id'] == id

    # patch_payload = payload = {
    #     "name": {
    #         "en": "api_auto_B " + random_string
    #     }
    # }
    # patch_brand_response = patch_brand(store_http_session, patch_payload, id_new)  #### Remove comments once patch is ready
    # assert patch_brand_response.status_code == 400, f"POST Brand validation failed for duplicate name..."
    # put_brand_json = patch_brand_response.json()
    # put_brand_json['id'] == id

    ### validate the brand that was tried to be updated still has its original name
    g_brand = get_brand(store_http_session, id_new)
    assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    sns = get_sns_message_by_id_catalog(id_new)
    ### GET Schema validation placeholder line
    assert payload_new['name'] == get_json_response['data']['brand']['name']
    assert payload_new['name'] == sns['name']


@pytest.mark.reg
@jamatest.test(21314956)
def test_post_brand_name_missing(store_http_session, ref_resolver, json_schema_file):
    """ Test payload without name or blank name"""
    payload = {
        "name": {
            "en": ""
        },
        "assets": {
            "images":
                {
                    "image1_1": "https://placehold.jp/100x100.png",
                }
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Please enter valid name for en as it is mandatory field" in p_json['message']


#--------------------------------MKTPL-9300 - PUT BRAND API CASES ----------------------------------------------

@pytest.mark.reg
@jamatest.test(21556430)
def test_put_brand_with_valid_name_valid_images_SNS(store_http_session,json_schema_file,ref_resolver):

    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)
    payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    g_brand = get_brand(session=store_http_session,id=id)
    print(p_brand.json())
    g_brand_json = g_brand.json()
    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert sns['image'], "SNS received an empty image list"
    validate_multi_lang(payload_put['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"Images received in SNS {sns['image']}")
    print(f"Images sent in Payload {payload_put['assets']['images']}")

@pytest.mark.reg
@jamatest.test(21556431)
def test_put_brand_with_invalid_name_with_special_chars_valid_images_SNS(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto"+ uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)

    #----------SPECIAL CHARACTERS UPDATED USING PUT-----------------

    payload_put['name']['en'] = "!@#$%^&*()"+ uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    g_brand = get_brand(session=store_http_session,id=id)
    print(p_brand.json())
    g_brand_json = g_brand.json()
    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert sns['image'], "SNS received an empty image list"
    validate_multi_lang(payload_put['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"Images received in SNS {sns['image']}")
    print(f"Images sent in Payload {payload_put['assets']['images']}")

@pytest.mark.reg
@jamatest.test(21556432)
def test_put_brand_with_longname_with_valid_images(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)
    payload_put['name']['en'] = "YGDYGSYGDYDGSGDD42343434423423" + uuid_random_name()


    # ----------LONG PAYLOAD IN NAME UPDATED USING PUT-----------------

    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    assert p_brand.status_code==400, f"200 Received"
    p_json=p_brand.json()
    assert "Brand name field [en] should not exceed 50 characters." in p_json['message']


@pytest.mark.reg
@jamatest.test(21556433)
def test_put_brand_with_samename_with_valid_images(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = payload
    #payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    assert p_brand.status_code==400, f"200 Code Received"
    p_json = p_brand.json()
    assert "A brand already exists with same name." in p_json['message']


@pytest.mark.reg
@jamatest.test(21556434)
def test_put_brand_with_invalid_size_images_valid_name(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)
    #-----PASSING INVALID IMAGE RESOLUTION FOR 1_1-----------

    payload_put['name']['en'] = "api_autoupdate"+ uuid_random_name()
    payload_put['assets']['images'][0]['brand_image1_1'] = "https://placehold.jp/10x10.png"
    # payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    assert p_brand.status_code == 400, f"200 Code Received"
    p_json = p_brand.json()
    #assert "Invalid brand_image1_1 image resolution. Resolution should be at least 100x100 with 1:1 ratio." in p_json['message']
    assert "Invalid brand_image1_1 resolution. Resolution should be at least 100x100 with 1:1 ratio." in p_json['message']



    #-----PASSING INVALID IMAGE RESOLUTION FOR 16_9-----------
    payload_put_16_9 = copy.deepcopy(payload)
    payload_put_16_9['name']['en'] = "api_autoupdate"+ uuid_random_name()

    payload_put_16_9['assets']['images'][0]['brand_image16_9'] = "https://placehold.jp/10x100.png"
    p_brand = put_brand(session=store_http_session, data=payload_put_16_9, id=id)
    assert p_brand.status_code == 400, f"200 Code Received"
    p_json = p_brand.json()
    #assert "Invalid brand_image16_9 image resolution. Resolution should be at least 178x100 with 16:9 ratio." in p_json['message']
    assert "Invalid brand_image16_9 resolution. Resolution should be at least 178x100 with 16:9 ratio." in p_json['message']

@pytest.mark.reg
@jamatest.test(21556435)
def test_put_brand_with_invalid_images_url_valid_name(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)
    # -----PASSING INVALID IMAGE URL FOR 1_1-----------

    payload_put['name']['en'] = "api_autoupdate"+ uuid_random_name()
    payload_put['assets']['images'][0]['brand_image1_1'] = "https://placeholsdsadd.jp/100x100.png"
    # payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    assert p_brand.status_code == 400, f"200 Code Received"
    p_json = p_brand.json()
    assert "Image download: https://placeholsdsadd.jp/100x100.png failed with HTTP code 0" in p_json[
        'message']

    # -----PASSING INVALID IMAGE URL FOR 16_9-----------
    payload_put_16_9 = copy.deepcopy(payload)
    payload_put_16_9['name']['en'] = "api_autoupdate" + uuid_random_name()

    payload_put_16_9['assets']['images'][0]['brand_image16_9'] = "https://placeholsdasdsad.jp/178x100.png"
    p_brand = put_brand(session=store_http_session, data=payload_put_16_9, id=id)
    assert p_brand.status_code == 400, f"200 Code Received"
    p_json = p_brand.json()
    assert "Image download: https://placeholsdasdsad.jp/178x100.png failed with HTTP code 0" in p_json[
        'message']

#-------------------GET ALL & GET BY ID BRAND CASES -------------------------------

@pytest.mark.reg
@jamatest.test(21313086)
def test_get_all_brands(store_http_session, json_schema_file, ref_resolver):
    g_brand = get_brand(store_http_session, params={"limit": 10})
    # assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    assert g_brand.status_code == 200, f"GET Brand API Passed"
    assert get_json_response["success"] == True
    logging.info(get_json_response)
    #print(get_json_response)

@pytest.mark.reg
@jamatest.test(21313088)
def test_get_brand_with_valid_id(store_http_session, json_schema_file, ref_resolver):
    """ Test Get brand with Valid ID"""
    payload = {
        "name": {"en": "api_auto - " + uuid_random_name()},
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }

    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']

    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    assert g_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"

@pytest.mark.reg
@jamatest.test(21313089)
def test_get_brand_with_invalid_id_with_integer(store_http_session, json_schema_file, ref_resolver):
    invalid_id = 222
    g_brand = get_brand(store_http_session, id=invalid_id, params={"limit": 10})
    assert g_brand.status_code == 400, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    logging.info(get_json_response)
    print(get_json_response)


@pytest.mark.reg
@jamatest.test(21274409)
def test_get_brand_with_invalid_id_with_string(store_http_session, json_schema_file, ref_resolver):
    """ Test payload version 2, with invalid brand ID. Validate response"""
    invalid_id = "abc112"
    g_brand = get_brand(store_http_session, id=invalid_id, params={"limit": 10})
    assert g_brand.status_code == 400, f"GET Brand API Failed for ID '{id}"
    # assert g_brand.message == "No Result Found."
    get_json_response = g_brand.json()
    assert "No results found." == get_json_response["message"]
    assert get_json_response["success"] == False
    logging.info(get_json_response)

#------------------- DELETE BRAND CASES -------------------------------

@jamatest.test(21458230)
def test_delete_brand(store_http_session):
    payload = {
        "name": {"en": "api_auto - " + uuid_random_name()}
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']

    g_brand = get_brand(store_http_session, id)
    assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    delete_response = delete_brand(store_http_session, id)
    assert delete_response.status_code == 200, f"Failed to delete product ID '{id}'"
    assert "Brand deleted successfully" == delete_response.json()['message']
    sns = get_sns_message_by_id_catalog(id)
    assert sns["id"] == id and 'name'not in sns, "Failed to find the SNS Unpublish SNS for brand"


@jamatest.test(21458231)
def test_delete_brand_validation_when_used_in_product(store_http_session, json_schema_file, ref_resolver):
    payload =  {
        "name": {"en": "api_auto - " + uuid_random_name()}
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']
    sns = get_sns_message_by_id_catalog(id)
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)

    print(f"Created the brand with ID '{id}'. Creating the product with the brand")
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh" + uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "brand_id": id,
        "productSet": "Simple",
        "productType": "Duty Free"
    }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id_product = product_response.json()["id"]
    product_get_r = get_product(id=id_product, session=store_http_session)
    assert product_get_r.status_code == 200
    # validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    # get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    # assert get_v2_product.status_code == 200
    # validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    sns_prd = get_sns_message_by_id_catalog(id_product)
    assert sns_prd['brand_id'] == id

    delete_response = delete_brand(store_http_session, id)
    assert delete_response.status_code == 400, f"Failed to receive validation for brand delete while its attached to a product'{id}'"
    assert "Brand cannot be  deleted because is associated with products." == delete_response.json()['message'] 
    payload.update({"brand_id": None})
    product_response = put_product(store_http_session, id_product, payload)
    assert product_response.status_code == 200
    assert "Product updated successfully" == product_response.json()['message']
    product_get_r = get_product(id=id_product, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id_product, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id_product)
    validate_product(sns)
    assert 'images' not in sns
    # assert "brand_id" not in sns, "brand_id was found where it should have been removed from the product"  ##### TO-DO NEEDS to be remove
    assert payload['name']['en']== sns['title']['en']

    r = delete_brand(store_http_session, id)
    assert r.status_code == 200
    sns = get_sns_message_by_id_catalog(id)
    assert "name" not in sns
    assert id == sns['id']

#---------------BRAND API PATCH CASES ------------------------------
@pytest.mark.reg
@jamatest.test(21599925)
def test_patch_brand_with_valid_name_valid_images_SNS(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_patch = copy.deepcopy(payload)
    payload_patch['name']['en'] = "api_autoupdate" + uuid_random_name()
    p_brand = patch_brand(session=store_http_session, data=payload_patch, id=id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(p_brand.json())
    g_brand_json = g_brand.json()
    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert sns['image'], "SNS received an empty image list"
    validate_multi_lang(payload_patch['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"Images received in SNS {sns['image']}")
    print(f"Images sent in Payload {payload_patch['assets']['images']}")

@pytest.mark.reg
@jamatest.test(21599926)
def test_patch_brand_with_invalid_long_name_valid_images(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    #sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_patch = copy.deepcopy(payload)
    payload_patch['name']['en'] = "api_autoupdateddasdasdasdasd" + uuid_random_name()
    # ----------LONG PAYLOAD IN NAME UPDATED USING PATCH-----------------

    p_brand = patch_brand(session=store_http_session, data=payload_patch, id=id)
    assert p_brand.status_code == 400, f"200 Received"
    p_json = p_brand.json()
    assert "Brand name field [en] should not exceed 50 characters." in p_json['message']

@pytest.mark.reg
@jamatest.test(21599927)
def test_patch_brand_with_valid_name_blank_images_SNS(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name()

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_patch = copy.deepcopy(payload)
    payload_patch['name']['en'] = "api_autoupdate" + uuid_random_name()
    # FIRST IMAGE AS BLANK
    payload_patch['assets']['images'][0]['brand_image1_1'] = ""
    p_brand = patch_brand(session=store_http_session, data=payload_patch, id=id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(p_brand.json())
    g_brand_json = g_brand.json()
    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert sns['image'], "SNS received an empty image list"
    validate_multi_lang(payload_patch['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"Images received in SNS {sns['image']}")
    print(f"Images sent in Payload {payload_patch['assets']['images']}")

    # SECOND IMAGE AS BLANK
    payload_patch_again = copy.deepcopy(payload)
    payload_patch_again['name']['en'] = "api_autoupdate" + uuid_random_name()
    # FIRST IMAGE AS BLANK
    payload_patch_again['assets']['images'][0]['brand_image16_9'] = ""
    payload_patch_again['assets']['images'][0]['brand_image1_1'] = ""

    p_brand = patch_brand(session=store_http_session, data=payload_patch_again, id=id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(p_brand.json())
    g_brand_json = g_brand.json()
    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    #assert not sns.get('image'), "SNS contains an unexpected image key"
    assert 'image' not in sns or sns['image'] is None, "SNS contains an unexpected image key"

    #assert sns['image'][0]['brand_image1_1'] == "", "SNS received an empty image list"
    #assert sns['image'][0]['brand_image16_9'] == "", "SNS received an empty image list"

    validate_multi_lang(payload_patch_again['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)












### -------------------- PRODUCT INTEGRATION WITH BRAND MKTPL-9507 ---------------------------------------------
@pytest.mark.reg
@jamatest.test(21274406)
def test_post_product_with_valid_brand_SNS(store_http_session, json_schema_file):
    #create brand
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name()
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']

    #Get category from Product get all
    g_product = get_product(session=store_http_session, id=None, limit=10)
    g_products = g_product.json()
    #print(json.dumps(request.json(), indent=2))
    product = g_products['data']['products'][0]  # Get the first product

    # Extract required fields
    category_id = product['category'][0]['id'] if product['category'] else None
    product_type = product['productType']
    product_set = product['productSet']
    brand_id = id

    # Optional: If currency is inside price structure (adjust based on actual data)
    # For now, let's assume currency comes like below
    # currency = None
    # if product.get("PriceTaxation", {}).get("price"):
    #     currency = product["PriceTaxation"]["price"][0].get("currencyCode")

    #category_id = product['category'][0]  # or product.get('category', [])[0]
    product_type = product['productType']
    product_set = product['productSet']
    #currency = product['currency_c']

    dynamic_fields = {
        "category": [category_id],
        "productType": product_type,
        "productSet": product_set,
        "brand_id": brand_id
    }

    response, payload_used = post_product(
        session=store_http_session,
        additionalData=dynamic_fields, returnData=True
    )
    print("✅ Status Code:", response.status_code)
    print("📦 Final Payload Used:\n", json.dumps(payload_used, indent=2))

    try:
        print("🆔 Product ID:", response.json().get("id"))
    except Exception as e:
        print("❌ Could not extract product ID. Error:", str(e))
        #print("Raw Response:", response.text)
    print(response.status_code)
    #print(response.json())
    product_id = response.json().get("id")
    product_details = get_product(id=product_id, session=store_http_session).json()
    brand_id_received = product_details['data']['product']['brand_id']
    print(brand_id_received)
    print(brand_id)
    assert int(brand_id_received) == brand_id, f"Brand ID: Expected {brand_id}, Got {brand_id_received}"
    # print("✅ Brand ID correctly Integrated.")

    # SNS VALIDATION FOR BRAND ID INTEGRATE TO PRODUCT

    sns = get_sns_message_by_id_catalog(product_id)
    ### GET Schema validation placeholder line
    assert payload_used['name'] == sns['title']['en']
    assert payload_used['brand_id'] == sns[
        'brand_id'], f"Brand ID: Expected {payload_used['brand_id']}, Got {sns['brand_id']}"
    print(f"payload brand ID is {payload_used['brand_id']}")
    print(f"SNS brand ID is {sns['brand_id']}")


@jamatest.test(21274407)
@pytest.mark.reg
def test_post_product_with_invalid_brand(store_http_session, json_schema_file):
    # Get category from Product get all
    g_product = get_product(session=store_http_session, id=None, limit=10)
    g_products = g_product.json()
    # print(json.dumps(request.json(), indent=2))
    product = g_products['data']['products'][0]  # Get the first product

    # Extract required fields
    category_id = product['category'][0]['id'] if product['category'] else None
    product_type = product['productType']
    product_set = product['productSet']
    brand_id = 23456

    # Optional: If currency is inside price structure (adjust based on actual data)
    # For now, let's assume currency comes like below
    # currency = None
    # if product.get("PriceTaxation", {}).get("price"):
    #     currency = product["PriceTaxation"]["price"][0].get("currencyCode")

    # category_id = product['category'][0]  # or product.get('category', [])[0]
    product_type = product['productType']
    product_set = product['productSet']
    # currency = product['currency_c']

    dynamic_fields = {
        "category": [category_id],
        "productType": product_type,
        "productSet": product_set,
        "brand_id": brand_id
    }

    response, payload_used = post_product(
        session=store_http_session,
        additionalData=dynamic_fields, returnData=True
    )

    assert response.status_code == 400, f"POST Brand Failed. Non 200 status code"
    response_json = response.json()
    assert "Invalid brand ID." in response_json['message']

    print("✅ Status Code:", response.status_code)


@pytest.mark.reg
@jamatest.test(21274409)
def test_put_product_with_valid_brand_SNS(store_http_session, json_schema_file):
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name()
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']

    # Get category from Product get all
    g_product = get_product(session=store_http_session, id=None, limit=10)
    g_products = g_product.json()
    # print(json.dumps(request.json(), indent=2))
    product = g_products['data']['products'][0]  # Get the first product

    # Extract required fields
    category_id = product['category'][0]['id'] if product['category'] else None
    product_type = product['productType']
    product_set = product['productSet']
    brand_id = id

    product_type = product['productType']
    product_set = product['productSet']
    # currency = product['currency_c']

    dynamic_fields = {
        "category": [category_id],
        "productType": product_type,
        "productSet": product_set,
        "brand_id": brand_id,
        # "name": "api_auto - " + uuid_random_name(),
        # "sku": "api_auto" + uuid_random_name()
    }

    response, payload_used = post_product(
        session=store_http_session,
        additionalData=dynamic_fields, returnData=True
    )
    print("✅ Status Code:", response.status_code)
    print("📦 Final Payload Used:\n", json.dumps(payload_used, indent=2))

    try:
        print("🆔 Product ID:", response.json().get("id"))
    except Exception as e:
        print("❌ Could not extract product ID. Error:", str(e))
        print("Raw Response:", response.text)
    print(response.status_code)
    print(response.json())
    product_id = response.json().get("id")
    sns = get_sns_message_by_id_catalog(product_id)
    payload_new = copy.deepcopy(payload)
    payload_new['name']['en'] = "api_auto_a " + uuid_random_name()
    new_brand = post_brand(store_http_session, payload_new)
    assert new_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = new_brand.json()
    assert "Brand added successfully" in p_json['message']
    new_brand_id = p_json['id']
    payload_put = copy.deepcopy(payload_used)
    payload_put["brand_id"] = new_brand_id

    # or any updated value

    update_product = put_product(
        session=store_http_session,
        id=product_id,
        data=payload_put  # Use full original payload with modification
    )
    # assert update_product.status_code == 200
    # update_product = put_product(session=store_http_session,id=product_id,data=dynamic_fields)
    assert update_product.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = update_product.json()
    assert "Product updated successfully" in p_json['message']
    product_details = get_product(id=product_id, session=store_http_session).json()
    brand_id_received = product_details['data']['product']['brand_id']
    print(brand_id_received)
    assert int(brand_id_received) == new_brand_id, f"Brand ID: Expected {brand_id}, Got {brand_id_received}"

    # SNS VALIDATION FOR BRAND ID UPDATE IN ANY PRODUCT
    sns = get_sns_message_by_id_catalog(product_id)
    ### GET Schema validation placeholder line
    #payload_put = payload_used["brand_id"]
    assert payload_put['brand_id'] == sns[
        'brand_id'], f"Brand ID: Expected {payload_put['brand_id']}, Got {sns['brand_id']}"
    print(f"payload brand ID is {payload_put['brand_id']}")
    print(f"SNS brand ID is {sns['brand_id']}")
    #print("SNS brand ID is" + sns['brand_id'])


@pytest.mark.reg
@jamatest.test(21472939)
def test_put_product_with_invalid_brand(store_http_session, json_schema_file):
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name()
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']

    # Get category from Product get all
    g_product = get_product(session=store_http_session, id=None, limit=10)
    g_products = g_product.json()
    # print(json.dumps(request.json(), indent=2))
    product = g_products['data']['products'][0]  # Get the first product

    # Extract required fields
    category_id = product['category'][0]['id'] if product['category'] else None
    product_type = product['productType']
    product_set = product['productSet']
    brand_id = id

    product_type = product['productType']
    product_set = product['productSet']
    # currency = product['currency_c']

    dynamic_fields = {
        "category": [category_id],
        "productType": product_type,
        "productSet": product_set,
        "brand_id": brand_id,
        # "name": "api_auto - " + uuid_random_name(),
        # "sku": "api_auto" + uuid_random_name()
    }

    response, payload_used = post_product(
        session=store_http_session,
        additionalData=dynamic_fields, returnData=True
    )
    print("✅ Status Code:", response.status_code)
    print("📦 Final Payload Used:\n", json.dumps(payload_used, indent=2))

    try:
        print("🆔 Product ID:", response.json().get("id"))
    except Exception as e:
        print("❌ Could not extract product ID. Error:", str(e))
        print("Raw Response:", response.text)
    print(response.status_code)
    print(response.json())
    product_id = response.json().get("id")
    # payload_new = copy.deepcopy(payload)
    # payload_new['name']['en'] = "api_auto_a " + random_string()
    # new_brand = post_brand(store_http_session, payload_new)
    # assert new_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    # p_json = new_brand.json()
    # assert "Brand added successfully" in p_json['message']
    new_brand_id = 12112
    payload_used["brand_id"] = new_brand_id  # or any updated value

    update_product = put_product(
        session=store_http_session,
        id=product_id,
        data=payload_used  # Use full original payload with modification
    )
    # assert update_product.status_code == 200
    # update_product = put_product(session=store_http_session,id=product_id,data=dynamic_fields)
    assert update_product.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = update_product.json()
    assert "Invalid brand ID." in p_json['message']
    product_details = get_product(id=product_id, session=store_http_session).json()
    brand_id_received = product_details['data']['product']['brand_id']
    print(brand_id_received)


@jamatest.test(21472950)
@pytest.mark.reg
def test_patch_product_with_valid_brand_SNS(store_http_session, json_schema_file):
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name()
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        } 
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']
    sns = get_sns_message_by_id_catalog(id)

    # Get category from Product get all
    g_product = get_product(session=store_http_session, id=None, limit=10)
    g_products = g_product.json()
    # print(json.dumps(request.json(), indent=2))
    product = g_products['data']['products'][0]  # Get the first product

    # Extract required fields
    category_id = product['category'][0]['id'] if product['category'] else None
    product_type = product['productType']
    product_set = product['productSet']
    brand_id = id

    product_type = product['productType']
    product_set = product['productSet']
    # currency = product['currency_c']

    dynamic_fields = {
        "category": [category_id],
        "productType": product_type,
        "productSet": product_set,
        "brand_id": brand_id,
        # "name": "api_auto - " + uuid_random_name(),
        # "sku": "api_auto" + uuid_random_name()
    }

    response, payload_used = post_product(
        session=store_http_session,
        additionalData=dynamic_fields, returnData=True
    )
    print("✅ Status Code:", response.status_code)
    print("📦 Final Payload Used:\n", json.dumps(payload_used, indent=2))

    try:
        print("🆔 Product ID:", response.json().get("id"))
    except Exception as e:
        print("❌ Could not extract product ID. Error:", str(e))
        print("Raw Response:", response.text)
    print(response.status_code)
    print(response.json())
    product_id = response.json().get("id")
    sns = get_sns_message_by_id_catalog(product_id)
    payload_new = copy.deepcopy(payload)
    payload_new['name']['en'] = "api_auto_a " + uuid_random_name()
    new_brand = post_brand(store_http_session, payload_new)
    assert new_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = new_brand.json()
    assert "Brand added successfully" in p_json['message']
    new_brand_id = p_json['id']
    payload_patch = {
        "name": "api_auto - " + uuid_random_name()
        ,
        "brand_id": new_brand_id

    }
    update_product = patch_product(
        session=store_http_session,
        id=product_id,
        data=payload_patch  # Use full original payload with modification
    )
    # assert update_product.status_code == 200
    # update_product = put_product(session=store_http_session,id=product_id,data=dynamic_fields)
    assert update_product.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = update_product.json()
    assert "Product updated successfully" in p_json['message']
    product_details = get_product(id=product_id, session=store_http_session).json()
    brand_id_received = product_details['data']['product']['brand_id']
    print(brand_id_received)
    assert int(brand_id_received) == new_brand_id, f"Brand ID: Expected {brand_id}, Got {brand_id_received}"

    # SNS VALIDATION FOR BRAND ID UPDATED USING PATCH IN PRODUCT

    sns = get_sns_message_by_id_catalog(product_id)
    ### GET Schema validation placeholder line
    # payload_put = payload_used["brand_id"]
    assert payload_patch['name'] == sns['title']['en']
    assert payload_patch['brand_id'] == sns[
        'brand_id'], f"Brand ID: Expected {payload_patch['brand_id']}, Got {sns['brand_id']}"
    print(f"payload brand ID is {payload_patch['brand_id']}")
    print(f"SNS brand ID is {sns['brand_id']}")


@pytest.mark.reg
@jamatest.test(21274410)
def test_patch_product_with_invalid_brand(store_http_session, json_schema_file):
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name()
        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']

    # Get category from Product get all
    g_product = get_product(session=store_http_session, id=None, limit=10)
    g_products = g_product.json()
    # print(json.dumps(request.json(), indent=2))
    product = g_products['data']['products'][0]  # Get the first product

    # Extract required fields
    category_id = product['category'][0]['id'] if product['category'] else None
    product_type = product['productType']
    product_set = product['productSet']
    brand_id = id

    product_type = product['productType']
    product_set = product['productSet']
    # currency = product['currency_c']

    dynamic_fields = {
        "category": [category_id],
        "productType": product_type,
        "productSet": product_set,
        "brand_id": brand_id,
        # "name": "api_auto - " + uuid_random_name(),
        # "sku": "api_auto" + uuid_random_name()
    }

    response, payload_used = post_product(
        session=store_http_session,
        additionalData=dynamic_fields, returnData=True
    )
    print("✅ Status Code:", response.status_code)
    print("📦 Final Payload Used:\n", json.dumps(payload_used, indent=2))

    try:
        print("🆔 Product ID:", response.json().get("id"))
    except Exception as e:
        print("❌ Could not extract product ID. Error:", str(e))
        print("Raw Response:", response.text)
    print(response.status_code)
    print(response.json())
    product_id = response.json().get("id")
    payload_new = copy.deepcopy(payload)
    payload_new['name']['en'] = "api_auto_a " + uuid_random_name()
    new_brand = post_brand(store_http_session, payload_new)
    assert new_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = new_brand.json()
    assert "Brand added successfully" in p_json['message']
    new_brand_id = 2323422
    payload_patch = {
        "name": "api_auto - " + uuid_random_name()
        ,
        "brand_id": new_brand_id

    }
    update_product = patch_product(
        session=store_http_session,
        id=product_id,
        data=payload_patch  # Use full original payload with modification
    )
    assert update_product.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = update_product.json()
    assert "Invalid brand ID." in p_json['message']
    product_details = get_product(id=product_id, session=store_http_session).json()
    brand_id_received = product_details['data']['product']['brand_id']
    print(brand_id_received)











