import json
import yaml
import os
from openapi_spec_validator import validate_spec
from openapi_spec_validator.readers import read_from_filename
# from openapi_spec_validator.exceptions import OpenAPIVersionNotFound
from datetime import date, datetime
class CustomJSONEncoder(json.JSONEncoder):
   def default(self, obj):
       if isinstance(obj, (date, datetime)):
           return obj.isoformat()
       return super().default(obj)
def load_openapi_file(filepath):
   with open(filepath, 'r') as file:
       if filepath.endswith('.yaml') or filepath.endswith('.yml'):
           openapi_spec = yaml.safe_load(file)
       elif filepath.endswith('.json'):
           openapi_spec = json.load(file)
       else:
           raise ValueError("File format not supported. Use JSON or YAML.")
       print(json.dumps(openapi_spec, indent=2, cls=CustomJSONEncoder))  # Print the loaded OpenAPI document for debugging
       return openapi_spec
def extract_schemas(openapi_spec):
   if 'components' in openapi_spec and 'schemas' in openapi_spec['components']:
       return openapi_spec['components']['schemas']
   else:
       raise ValueError("No schemas found in the OpenAPI specification.")
def save_schemas_to_files(schemas, output_dir):
   os.makedirs(output_dir, exist_ok=True)
   for schema_name, schema in schemas.items():
       with open(os.path.join(output_dir, f"{schema_name}.json"), 'w') as schema_file:
           json.dump(schema, schema_file, indent=2, cls=CustomJSONEncoder)
   print(f"Schemas have been saved to {output_dir}")
def main(openapi_file, output_dir):
   try:
       # Load OpenAPI spec
       openapi_spec = load_openapi_file(openapi_file)
       # Check OpenAPI version explicitly
    #    if 'openapi' not in openapi_spec:
    #        raise OpenAPIVersionNotFound("OpenAPI version field ('openapi') is missing in the document.")
    #    # Validate OpenAPI spec
    #    validate_spec(read_from_filename(openapi_file))
       # Extract schemas
       schemas = extract_schemas(openapi_spec)
       # Save schemas to files
       save_schemas_to_files(schemas, output_dir)
#    except Exception as e:
#        print(f"OpenAPI version not found: {e}")
   except Exception as e:
       print(f"An error occurred: {e}")
if __name__ == "__main__":
   import argparse
   parser = argparse.ArgumentParser(description="Convert OpenAPI schemas to JSON schemas.")
   parser.add_argument("openapi_file", help="Path to the OpenAPI specification file (JSON or YAML).")
   parser.add_argument("output_dir", help="Directory to save the JSON schemas.")
   args = parser.parse_args()
   main(args.openapi_file, args.output_dir)