import pytest, uuid
from collections import Counter
from time import sleep
import os
from utils.helper import *
from utils.ui_helper import *
from jamatest import jamatest
from jsonschema import validate



def lists_of_assigment_type(assigment_type, catalog_assigments):
    flights = {}
    ensure_assigment_type = "Sector" if assigment_type == 'sector' else "Flight"
    for ca in catalog_assigments:
        if assigment_type in ca:
            if isinstance(ca[assigment_type], list) and assigment_type != "routeGroup":   
                if len(ca[assigment_type]) > 0:
                    for flight_obj in ca[assigment_type]:
                        if flight_obj['id'] in flights:
                            flights[flight_obj['id']].append(ca['id'])
                        else:
                            print(f"first time finding the assigment_type in a catalog assigmnt")
                            flights[flight_obj['id']] = [ca['id']]
                else: 
                    print("There was a catalog assigment with flights list empty")
                    # print("The catalog assigment: ")
                    # print(json.dumps(ca, indent=3) )
            elif assigment_type == "routeGroup" and isinstance(ca[assigment_type], dict):
                # print("looking for routeGroups")
                # print(json.dumps(ca, indent=3) )
                if isinstance(ca[assigment_type]['id'], int):
                    if ca[assigment_type]['id'] in flights:
                        ca[assigment_type]['id'].append(ca['id'])
                    else:
                        print("first time finding the routeGroup in a catalog assigmnt")
                        ca[assigment_type]['id'] = [ca['id']]
                else: #### Shouldnt be going in here... 
                    print("Found a bad routeGroup data type...")
                    print(json.dumps(ca, indent=5 ))
                    raise Exception("Found a different type than int for the ca[routeGroup]['id']")
            else: 
                print(f"{assigment_type} was not a list or routeGroup")
                # print("The catalog assigment: ")
                # print(json.dumps(ca, indent=3) )
        else: 
            continue
            # print(f"{assigment_type} key was not in the catalog assigment")
            # print("The catalog assigment: ")
            # print(json.dumps(ca, indent=3) )
    return flights

def find_item_id(obj, specific_id):
    for item in obj:
        if item['id'] == specific_id:
            print("foudn the ID")
            return True
    print("there was no matching Id ")
    return False

def func_route_catalog_filter_helper(airline_session, assigment_type_filter, sector_ids_fixture, json_schema_file, ref_resolver):
    print("the filters to verify: ", json.dumps(sector_ids_fixture, indent=3))
    params_filter = "flight" if assigment_type_filter == "flights" else assigment_type_filter
    found_ids = []
    # schema = load_schema("routeCatalogResponse")
    for key in sector_ids_fixture.keys():
        params = {params_filter: key, "limit": 200}
        route_catalog_response = get_route_catalog(session=airline_session, params=params)
        assert route_catalog_response.status_code == 200
        validate(instance=route_catalog_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponse'], resolver=ref_resolver)
        temp_list_ca=[]
        for ca in route_catalog_response.json()['data']['catalogsAssignments']:
            assert find_item_id(ca[assigment_type_filter],key ) ### to ensure the filter worked
            found_ids.append(key)
            temp_list_ca.append(ca['id'])
        assert Counter(temp_list_ca) == Counter(sector_ids_fixture[key])
    assert set(found_ids) == set(sector_ids_fixture.keys())


#### Testing in dev
#### Product
@pytest.fixture(scope="module")
def catalog_50_products(store_http_session):
    r = get_catalog(session=store_http_session, limit=50)
    assert r.status_code == 200
    return r.json()
@pytest.fixture(scope="module")
def get_100_products(store_http_session):
    r = get_product(session=store_http_session, limit=100)
    assert r.status_code == 200
    return r.json()

@pytest.fixture(scope="module")
def product_ids(get_100_products):
    products = []
    for item_in_list in get_100_products['data']['products']:
        products.append(item_in_list['id'])
    return products
@pytest.fixture(scope="module")
def product_skus(get_100_products):
    products = []
    for item_in_list in get_100_products['data']['products']:
        products.append(item_in_list['sku'])
    return products

#### Catalog
@pytest.fixture(scope="module")
def catalog_ids(catalog_50_products):
    catalog_s = []
    for catalog in catalog_50_products['data']['catalogs']:
        catalog_s.append(catalog['id'])
    return catalog_s

@pytest.fixture(scope="module")
def catalog_skus(catalog_50_products):
    catalog_skus = []
    for catalog in catalog_50_products['data']['catalogs']:
        catalog_skus.append(catalog['sku'])
    return catalog_skus


### Catalog assigments / route-catalog
@pytest.fixture(scope="module")
def catalog_assigments(airline_session):
    all_catalog_assigments =  get_all_helper(session=airline_session, endpoint=route_catalog)
    assert len(all_catalog_assigments) > 0
    return all_catalog_assigments

@pytest.fixture(scope="module")
def flightIDs_to_CA_map(catalog_assigments):
    return lists_of_assigment_type("flights", catalog_assigments)
@pytest.fixture(scope="module")
def sectorIDs_to_CA_map(catalog_assigments):
    return lists_of_assigment_type("sector", catalog_assigments)
@pytest.fixture(scope="module")
def routeGroup_IDs_to_CA_map(catalog_assigments):
    return lists_of_assigment_type("routeGroup", catalog_assigments)
    
########################## Product schemas test ##########################
def test_product_get_id_all_functionality(store_http_session, product_ids, json_schema_file, ref_resolver):
    for i in product_ids[:10]:
        get_resp = get_product(id=i, session=store_http_session)
        assert get_resp.status_code == 200
        validate(instance=get_resp.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    
def test_product_get_by_ids(store_http_session, product_ids, json_schema_file, ref_resolver):
    comma_separated_string = ','.join([str(number) for number in product_ids])
    params = {'ids': comma_separated_string, "limit": 100}
    get_resp = get_product(session=store_http_session, params=params)
    validate(instance=get_resp.json(), schema=json_schema_file['components']['schemas']['productResponse'], resolver=ref_resolver)

    
def test_product_get_by_skus(store_http_session, product_skus, json_schema_file, ref_resolver):
    comma_separated_string = ','.join([str(number) for number in product_skus])
    params = {'skus': comma_separated_string, "limit": 100}
    get_resp = get_product(session=store_http_session, params=params)
    validate(instance=get_resp.json(), schema=json_schema_file['components']['schemas']['productResponse'], resolver=ref_resolver)

def test_product_unpublished(store_http_session,json_reference_product, json_schema_file, ref_resolver):
    product_data = copy.deepcopy(json_reference_product['v1'])
    product_data.pop("productCustomizers", "")
    product_data['sku'] = "api_auto_" + uuid_random_name()
    post_r = post_product(category_id=TESTING_CATEGORY_ID, data=(product_data), session=store_http_session,)
    assert post_r.status_code == 200
    id = post_r.json()['id']
    delete_r = delete_product(session=store_http_session, id=id)
    assert delete_r.status_code == 200
    
    all_products = fetch_last_n_products(store_http_session, product_url, search_params={"unpublished": True}, n=5, items_per_page=5)
    unpublished_product_found = False
    for product in  all_products:
        if product['id'] ==  id:
            logger.info("Found the deleted product in the GET api")
            logger.info(json.dumps(product, indent=3))
            unpublished_product_found = True
            break
        else:
            print("Didnt find the correct ID... Found:",product['id'])
    assert unpublished_product_found, "Failed to find the unpublished product in the get api..."




def test_product_by_id_formatVersion2(store_http_session,json_reference_product, json_schema_file, ref_resolver):
    product_data = copy.deepcopy(json_reference_product['v2'])
    product_data.pop("productCustomizers", "")
    product_data['sku'] = "api_auto_" + uuid_random_name()
    post_r = post_product(category_id=TESTING_CATEGORY_ID, data=(product_data), session=store_http_session)
    assert post_r.status_code == 200
    id = post_r.json()['id']

    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)


def test_product_10_formatVersion2(store_http_session,json_reference_product, json_schema_file, ref_resolver):
    get_v2_product = get_product(session=store_http_session, formatVersion2=True, limit=10)
    # get_v2_product = fetch_last_n_products(store_http_session, product_url, search_params={"formatVersion":2}, items_per_page=100, n=100)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponse'], resolver=ref_resolver)

############### Catalog schemas test ###############
def test_get_existing_catalog_id(store_http_session, catalog_ids, json_schema_file, ref_resolver):
    get_catalog_reponse = get_catalog(id=catalog_ids[0], session=store_http_session)
    assert get_catalog_reponse.status_code == 200
    validate(instance=get_catalog_reponse.json(), schema=json_schema_file['components']['schemas']['CatalogResponseId'], resolver=ref_resolver)
    get_catalog_formatVersion2 = get_catalog(id=catalog_ids[0], session=store_http_session, formatVersion2=True)
    assert get_catalog_formatVersion2.status_code == 200
    validate(instance=get_catalog_formatVersion2.json(), schema=json_schema_file['components']['schemas']['CatalogResponseMultiId'], resolver=ref_resolver)
    
def test_get_multiple_catalog_assigments_by_ids(store_http_session, catalog_ids, json_schema_file, ref_resolver):
    comma_separated_string = ','.join([str(number) for number in catalog_ids])
    params = {'ids': comma_separated_string, "limit": 200}
    get_response = get_catalog(session=store_http_session, params=params)
    assert get_response.status_code == 200
    validate(instance=get_response.json(), schema=json_schema_file['components']['schemas']['CatalogResponse'], resolver=ref_resolver)
    
    params = {'ids': comma_separated_string, "limit": 200, "formatVersion": 2}
    get_response = get_catalog(session=store_http_session, params=params)
    assert get_response.status_code == 200
    validate(instance=get_response.json(), schema=json_schema_file['components']['schemas']['CatalogResponseMulti'], resolver=ref_resolver)

    

########################## Route-catalog / Catalog assigmnet testing ##########################
def test_route_catalog_by_id_path(airline_session, catalog_assigments, json_schema_file, ref_resolver):
    ca_response = get_route_catalog(session=airline_session, id=catalog_assigments[-1]['id'])
    assert ca_response.status_code == 200
    validate(instance=ca_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponseId'], resolver=ref_resolver)

def test_catalog_assigment_get_limit_100(airline_session, json_schema_file, ref_resolver):
    ca_response = get_route_catalog(session=airline_session, limit=100)
    assert ca_response.status_code == 200
    validate(instance=ca_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponse'], resolver=ref_resolver)
    

def test_route_catalog_get_filter_by_flight_ID(flightIDs_to_CA_map, airline_session, json_schema_file, ref_resolver):
    func_route_catalog_filter_helper(airline_session, "flights",flightIDs_to_CA_map, json_schema_file, ref_resolver )

def test_route_catalog_get_filter_by_sector_id(sectorIDs_to_CA_map, airline_session, json_schema_file, ref_resolver):
    func_route_catalog_filter_helper(airline_session, "sector",sectorIDs_to_CA_map, json_schema_file, ref_resolver )
    
def test_route_catalog_get_filter_by_routeGroup_id(routeGroup_IDs_to_CA_map, airline_session, json_schema_file, ref_resolver):  ### route catalog get all api is failing to return correct schema for routeGroup object
    func_route_catalog_filter_helper(airline_session, "routeGroup",routeGroup_IDs_to_CA_map, json_schema_file, ref_resolver )





def test_speciality_attribute(store_http_session, json_schema_file, ref_resolver):
    get_all_response = get_speciality_attribute(session=store_http_session, limit=100)
    assert get_all_response.status_code == 200
    validate(instance=get_all_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributesResponse'], resolver=ref_resolver)
    
    
    id = get_all_response.json()['data']['SpecialityAttributes'][0]['id']
    
    get_single = get_speciality_attribute(session=store_http_session, id=id)
    assert get_single.status_code == 200
    validate(instance=get_all_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributesResponse'], resolver=ref_resolver)

# @pytest.mark.xfail #MKTPL-8616
def test_speciality_attribyte_v2(store_http_session, json_schema_file, ref_resolver):
    random_name = uuid_random_name()
    data = {
        "formatVersion": 2,
        "shortDescription": {
            "en": f"SP - short description for Shampoos zijn - {random_name}",
            "ar": f"Shampoos zijn een essentieel ondl {random_name}",
            "el": f"Shampoos zijn eenverwijderen en je schoon fsdf fsdf {random_name}",
            "nl": f"Shampoos zij onderdeel van alle haarverze hoofdhuid reinigen haarophoping {random_name}",
            "he": f"Shampnhoofdhuid reinigen haarophopinon fsdf dffsd {random_name}",
            "hi": f"यह सुनिश्चित करने के लिए कि बालों क नेपांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।{random_name}"
        },
        "disclaimer": {
            "en": f"sp en disclaimer - {random_name}",
            "ja": f"sp ja disclaimer - {random_name}"
        },
        "image": "https://placehold.jp/256x256.png"
    }
    post_response = post_speciality_attibute(session=store_http_session, data=data)
    assert post_response.status_code == 200
    s_id = post_response.json()['id']
    
    params={"ids": s_id, "formatVersion": 2}
    get_s = get_speciality_attribute(session=store_http_session, params=params)
    assert get_s.status_code == 200
    validate(instance=get_s.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributesMultiResponse'], resolver=ref_resolver)
    
    
def test_airline_category_limit_10(airline_session, json_schema_file, ref_resolver):
    get_all = get_airline_category(airline_session)
    assert get_all.status_code == 200
    validate(instance=get_all.json(), schema=json_schema_file['components']['schemas']['airlineCategoryResponse'], resolver=ref_resolver)
    
    
    get_all_v2 = get_airline_category(airline_session, formatVersion2=True)
    assert get_all_v2.status_code == 200
    validate(instance=get_all_v2.json(), schema=json_schema_file['components']['schemas']['airlineCategoryMultiResponse'], resolver=ref_resolver)
    
    
def test_airline_category_by_id_path(airline_session, json_schema_file, ref_resolver):
    get_all = get_airline_category(airline_session, limit=20)
    assert get_all.status_code == 200
    list_ids= [ ]
    id = get_all.json()['data']['airlineCategories'][0]['id']
    
    for item_in_list in get_all.json()['data']['airlineCategories']:
        list_ids.append(item_in_list['id'])
    
    for i in list_ids:
        print(f"Requesting for ID: {i}")
        req = get_airline_category(airline_session, id=i)
        assert req.status_code == 200
        validate(instance=req.json(), schema=json_schema_file['components']['schemas']['airlineCategoryResponseId'], resolver=ref_resolver)
        print("validating formatVersion 2")
        
        req = get_airline_category(airline_session, id=i, formatVersion2=True)
        assert req.status_code == 200
        validate(instance=req.json(), schema=json_schema_file['components']['schemas']['airlineCategoryMultiResponseId'], resolver=ref_resolver)

def test_store_category(store_http_session, json_schema_file, ref_resolver):
    get_categories = get_categories_store(store_http_session)
    assert get_categories.status_code == 200
    validate(instance=get_categories.json(), schema=json_schema_file['components']['schemas']['category'], resolver=ref_resolver)

@pytest.mark.xfail
def test_store_categoryMulti(store_http_session, json_schema_file, ref_resolver):
    get_categories = get_categories_store(store_http_session, formatVersion2=True)
    assert get_categories.status_code == 200
    validate(instance=get_categories.json(), schema=json_schema_file['components']['schemas']['categoryMulti'], resolver=ref_resolver)

