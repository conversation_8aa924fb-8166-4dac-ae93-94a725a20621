import json
from deepdiff import DeepDiff
import os
from datetime import datetime
from utils.store_data import *
from utils.helper import *
import pytest


def print_json_diff(diff):

    def format_value(value):
        if isinstance(value, (dict, list)):
            return json.dumps(value, indent=2)
        return repr(value)

    def print_change(path, old_value, new_value):
        print(f"  {path}:")
        print("    From:")
        print(f"{format_value(old_value)}")
        print("    To:")
        print(f"{format_value(new_value)}")

    if 'values_changed' in diff:
        print("Values changed:")
        for item, change in diff['values_changed'].items():
            print_change(item, change['old_value'], change['new_value'])
    
    if 'dictionary_item_added' in diff:
        print("\nItems added:")
        for item, value in diff['dictionary_item_added'].items():
            print(f"  {item}:")
            print(format_value(value))
    
    if 'dictionary_item_removed' in diff:
        print("\nItems removed:")
        for item, value in diff['dictionary_item_removed'].items():
            print(f"  {item}:")
            print(format_value(value))

    if 'iterable_item_added' in diff:
        print("\nList items added:")
        for item, value in diff['iterable_item_added'].items():
            print(f"  At {item}:")
            print(format_value(value))

    if 'iterable_item_removed' in diff:
        print("\nList items removed:")
        for item, value in diff['iterable_item_removed'].items():
            print(f"  At {item}:")
            print(format_value(value))



def read_latest_json_file(directory='.', prefix="test_data"):
    # Get a list of all JSON files in the directory that start with the prefix
    json_files = [f for f in os.listdir(directory) if f.startswith(prefix) and f.endswith('.json')]
    
    if not json_files:
        return None, f"No JSON files found in the directory starting with '{prefix}'."
    # Sort the files by modification time (most recent first)
    latest_file = max(json_files, key=lambda f: os.path.getmtime(os.path.join(directory, f)))
    
    file_path = os.path.join(directory, latest_file)
    print("Reading data from: ", file_path)
    try:
        with open(file_path, 'r') as file:
            data = json.load(file)
        return data
    except json.JSONDecodeError:
        return None, f"Error decoding JSON from file: {file_path}"
    except IOError:
        return None, f"Error reading file: {file_path}"

ca_no_changes=[]
def compare_data(old_data, new_data):
    required_states = {"Approved for Association", "Airline Category Mapped"}
    for key, value in old_data.items():
        diffs = DeepDiff(old_data[key], new_data[key], ignore_order=True)
        if diffs:
            if "Expired" in value.get("workflowStatus"):
                print("\n Expired catalog assigment Differences: ", key)
            elif required_states.issubset(set(value.get("workflowStatus",[]))):
                print("\n DIFFERENCES FOUND IN CA ID IN: ", key)
            else: 
                print("\n Differences in CA not in use: ", key)
            print_json_diff(diffs)
            print("\n---------------------------------------------------------------\n")
        else:
            ca_no_changes.append(key)
        # if int(key) == 11624:
        #     print("the differences")
        #     print(diffs)
            
        
def change_category_id_to_int(data):
    for key, value in data.items():
        for product in value['catalog']['products']:
            if 'airlineCategory' in product:
                if product['airlineCategory']:
                    product['airlineCategory'] = [int(cat) for cat in product['airlineCategory']]
            if "productPrice" in product:  #### When comparing with dev newest code
                if product["productPrice"] == "":
                    product["productPrice"] = None
            if "productQuantity" in product:
                if product["productQuantity"] == "":
                    product["productQuantity"] = None
                elif isinstance(product["productQuantity"], str):
                    product["productQuantity"] = int(product["productQuantity"])
        if "productSubstitutions" in value and value["productSubstitutions"] == {}:
            value["productSubstitutions"] = []
                     
def write_json_to_file(data, prefix):
    now = datetime.now()
    filename = f"{prefix}_{now.strftime('%Y-%m-%d_%H-%M-%S')}.json"
    # Write the JSON data to the file
    with open(filename, 'w') as file:
        json.dump(data, file, indent=4)
    print("The data is stored in", filename)
    return filename 

def get_all_data(session, url):
    all_products = get_all_helper(session, url )
    new_dic = {}
    for i in all_products:
        # print("Mapping CA ID", i['id'])
        new_dic[str(i['id'])] = i        
    return new_dic

def create_unique_brands(all_products):
    brands = {}
    def add_id(key, id_value, data):
        if key not in brands:
            brands[key] = { 
                'products': [],
                'data': data
            }
        else:
            logger.info(f"Found a brand in more than 1 product... {json.dumps(data, indent=5)} " )
        brands[key]['products'].append(id_value)

    for id, product in all_products.items():
        if product['brand']['en'] :
            # print("data is not none")
            # print(product['brand'])
            add_id(product['brand']['en'], id, product['brand'])
    return brands

def test_validate_data_after(store_http_session):
    old_product_data = read_latest_json_file(prefix=STORE_NAME_MIGRARTION)
    original_brands =  create_unique_brands(old_product_data )

    # all_products_new = get_all_data(store_http_session, product_url) ## NEEDS UNCOMMENT
    # write_json_to_file(all_products_new, "new"+STORE_NAME_MIGRARTION)
    all_products_new = read_latest_json_file(prefix= "new"+STORE_NAME_MIGRARTION)  ## NEEDS COMMENTS

    # all_brands_new = get_all_data(store_http_session, brand_url)## NEEDS UNCOMMENT
    # write_json_to_file(all_brands_new, "brands"+STORE_NAME_MIGRARTION)
    all_brands_new = read_latest_json_file(prefix= "brands"+STORE_NAME_MIGRARTION)  ## NEEDS COMMENTS

    ## Make sure all brands were created 
        ## go through the brands returned and ensure data matches
    brand_all_data_mismatch = []
    new_fantom_brands = []
    for brand in all_brands_new.values():
        print("the brand: ", brand)
        key = brand['name']['en'] 
        if key in original_brands:
            original_brands[key]['new_id'] = brand['id']
            if brand['name'] != original_brands[key]['data']:
                brand_all_data_mismatch.append(brand['id'])
                logger.error(
                    f"Error: Found a brand data missmatch \n"
                    f"Old brand used in products: {original_brands[key]['products']}:\n"
                    f"{json.dumps(original_brands[key]['data'], indent=4)} \n"
                    f"New brand id '{brand['id']}': "
                    f" s{json.dumps(brand['name'], indent=4)} \n")
        else:
            new_fantom_brands.append(brand['id'])
            logger.warning(f"WARNING: Found a brand that didn't exist before... \n NAME: {brand['name']['en']} \nID: {brand['id']}")

    brands_not_created = []
    for name, og_data in original_brands.items():
        if "new_id" not in og_data:
            brands_not_created.append(name)
            logger.error(
                    f"Error: Found a brand not migrated \n"
                    f"Old brand '{name}' used in products: {og_data['products']}:\n"
                    f"{json.dumps(og_data['data'], indent=4)} \n")
    print(f"Brands not created succesfully: {brands_not_created}")

    ## go through all the products and make sure data is migrated correctly
    product_matched_correctly = 0
    product_matched_incorrectly = 0
    product_not_migrated = []
    for id, old_product in old_product_data.items():
        old_brand = old_product['brand']['en']
        if old_brand:
            print("Found a product with brand, validating migration for it")
            if "new_id" in original_brands[old_brand] and  all_products_new[id]['brand_id'] == original_brands[old_brand]['new_id']:
                product_matched_correctly+=1
            elif "new_id" not in original_brands[old_brand]:
                logger.error(f"Error: Found a product not migrated to brand correctly; Likely different spealing... id, Product ID '{id}' \n"
                    f"Original brand en: {old_brand} \n"
                    f"Receive a Brand_id: {all_products_new[id]['brand_id']}")
                product_matched_incorrectly +=1
                product_not_migrated.append(id)
            else:
                logger.error(f"Error: Found a product not migrated correctly. Either without brand or wrong brand... id, Product ID '{id}' \n"
                    f"Original brand en: {old_brand} \n "
                    f"Expected brand id {original_brands[old_brand].get('new_id')} \n" 
                    f"Receive Brand_id: {all_products_new[id]['brand_id']}")
                product_matched_incorrectly +=1
                product_not_migrated.append(id)
        else:
            print("Product without a brand, validating null brand")
            if all_products_new[id]['brand_id'] == None:
                product_matched_correctly +=1
            else:
                logger.error(f"Error: Found a product without brand not set to null, Product id '{id}' \n"
                             f"New product contains brand_id: {all_products_new[id]['brand_id']}")
                product_matched_incorrectly +=1
                product_not_migrated.append(id)

    if product_not_migrated:
        pytest.fail(f"{len(product_not_migrated)} Products failed to be migrated correctly: {product_not_migrated}")
    assert product_matched_correctly == len(old_product_data)
    assert len(brands_not_created)  == 0, "Brands failed to be created"
    assert product_matched_incorrectly == 0

    assert len(all_brands_new) == len(original_brands), "the size in the list is not the same"
    ### create all the unique brands based on its english names

