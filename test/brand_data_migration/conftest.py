import pytest
from utils.helper import *
from utils.ui_helper import *
from requests.adapters import HTTPAdapter
from utils.store_data import *



@pytest.fixture(scope="session")
def store_http_session():
    token, expires_at = get_token('store', base_url_env, True, STORE_CLIENT_ID, STORE_CLIENT_SECRET)
    session = requests.Session()
    adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({"Content-Type": "application/json", "Authorization": token})
    original_request = session.request
    def refresh_token_if_needed():
        nonlocal token, expires_at 
        if datetime.now() >= expires_at - timedelta(seconds=45):
            token, expires_at = get_token('store', base_url_env, True, STORE_CLIENT_ID, STORE_CLIENT_SECRET)
            session.headers.update({"Content-Type": "application/json", "Authorization": token})   
    def request_with_refresh(*args, **kwargs):
        refresh_token_if_needed()
        return original_request(*args, **kwargs)
    
    session.request = request_with_refresh
    yield session
    session.close()
    
