from utils.store_data import *
from utils.helper import *

def write_json_to_file(data):
    now = datetime.now()
    filename = f"{STORE_NAME_MIGRARTION}_{now.strftime('%Y-%m-%d_%H-%M-%S')}.json"
    # Write the JSON data to the file
    with open(filename, 'w') as file:
        json.dump(data, file, indent=4)
    print("The data is stored in", filename)
    return filename    


def get_all_products(session):
    all_products = get_all_helper(session, product_url, aditional_params={"formatVersion": 2} )
    new_dic = {}
    for i in all_products:
        print("Mapping CA ID", i['id'])
        new_dic[str(i['id'])] = i        
    return new_dic


def test_fetch_products_before(store_http_session):
    all_products = get_all_products(store_http_session)
    write_json_to_file(all_products)



