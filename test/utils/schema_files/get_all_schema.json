{"type": "object", "properties": {"code": {"type": "integer"}, "success": {"type": "boolean"}, "data": {"type": "object", "properties": {"customizerGroups": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "object", "properties": {"en": {"type": "string"}}, "required": ["en"]}, "isMultiSelectable": {"type": "boolean"}, "maximumSelection": {"type": "integer"}, "required": {"type": "boolean"}, "customizerOptions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "displaySequence": {"type": "integer"}, "isDefaultSelected": {"type": "boolean"}, "additionalCharge": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "number"}, "currency": {"type": "string"}}, "required": ["value", "currency"]}}, "label": {"type": "object", "properties": {"en": {"type": "string"}}, "required": ["en"]}}, "required": ["id", "isDefaultSelected", "label"]}}}, "required": ["id", "title", "isMultiSelectable", "required", "customizerOptions"]}}}}}}