import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
import os
from utils.helper import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from jamatest import jamatest
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException
import time, fnmatch, shutil
import traceback
from dateutil.relativedelta import relativedelta

selenium_logger = logging.getLogger('selenium')
selenium_logger.setLevel(logging.INFO)

def capture_failed_details(driver, name, e):
    current_date = datetime.now()
    date_string_p = current_date.strftime('%Y_%m_%d__%H_%M_%S')
    screenshot_name = "failed_"+date_string_p +"_"+name+".png"
    driver.save_screenshot(screenshot_name)
    logger.critical(f"There was a failure in the test. Screenshot saved for reference: {screenshot_name}")
    console_logs = driver.get_log("browser")
    log_file = "Failed_"+date_string_p +"_"+name+".log"
    with open(log_file, "w") as file:
        for i in console_logs:
            file.write(f"{i['level']} - {i['timestamp']} - {i['message']}\n")
    sleep(3)
    try:
        close_active_tab(driver, try_catch=False)
        close_active_tab(driver, try_catch=False) ### In case there was an active tab before due to a pop-up
    except:
        logger.debug("After the failure. it was not able to close the active tab. A pop-up may need to be dismissed...")
        element_modified_pop_up(driver, 5)

    driver.refresh()  ### New for future test to be in correct state
    check_succesfull_login(driver)
    sleep(5)
    element_modified_pop_up(driver, 10) 
    tb = traceback.format_exc()
    logger.critical("The line that triggered the exception: \n %s", tb.strip().split('/n')[-1])
    if name == "PAC_USER_download_sns":
        try:
            navigate_to_sns(driver) ## need to make sure the page is reopened since it gets closed on failure
        except:
            logger.critical("Failure opening the SNS folder")
    raise e

PRODUCT_CATEGORY_NAME='Bev'
def create_product(store_driver, name=None, product_type="Simple", category="Bev", variant_theme_name="Dark", deliveryMethod='Onboard', productType='Duty Free', brand="test_Brand ", spotLight=False, json_data=None):
    try:
        if name is None:
            date_string_p = current_date.strftime('%Y_%m_%d__%H_%M_%S')
            result = 'uiAuto_'+ ''.join(random.choices(string.ascii_letters, k=10)) +"_"
        random_sku = result[0:11] + date_string_p
        random_product_name = result + date_string_p
        random_short_description = f"Short description for {result + date_string_p}"
        random_long_description = f"Long description for {result + date_string_p}"
        try:
            store_driver.find_element(By.ID, 'pimcore_menu_massupdate').click()
        except Exception as e:
            raise Exception("there was an error in clicking the button to add products... likely an error in previous test")
        store_driver.find_element(By.XPATH, '//span[text()="Create New Product"]').click()
        sleep(0.5)
        store_driver.find_element(By.NAME, "sku").send_keys(random_sku, Keys.TAB)
        sleep(1)
        store_driver.find_element(By.XPATH,'//input[@name="category"]').click()
        sleep(0.5)
        for i in range(3):
            try:
                store_driver.find_element(By.XPATH,f"//li[contains(text(),'{category}')]").click()    ### INCOSISTANT ISSUE HERE or coulbe in line before...
                logger.debug("Clicked the category succesfully!")
                break
            except:
                logger.critical(f"there was an issue bringing up the category lists in try #{i+1}... trying to bring up the category list one more time in 3 seconds")
                sleep(3)
                store_driver.find_element(By.XPATH,'//input[@name="category"]').click()
                # store_driver.find_element(By.XPATH,f"//li[contains(text(),'{category}')]").click()    ### INCOSISTANT ISSUE HERE or coulbe in line before...
                

        wait = WebDriverWait(store_driver, 10)
        product_set = wait.until(EC.presence_of_element_located((By.XPATH, '//input[@name="type"]//../following-sibling::div')))
        store_driver.execute_script("arguments[0].click();", product_set)
        sleep(0.5)
        p_set = wait.until(EC.presence_of_element_located((By.XPATH, f"//li[text()='{product_type}']")))
        store_driver.execute_script("arguments[0].click();", p_set)
        if product_type == "Configurable":
            logger.debug("Its a configurable product, need to enter a theme ")
            variant_theme_in = wait.until(EC.presence_of_element_located((By.XPATH, '//input[@name="variantTheme"]//../following-sibling::div')))
            store_driver.execute_script("arguments[0].click();", variant_theme_in)
            variant_theme_li = wait.until(EC.presence_of_element_located((By.XPATH, f"//li[contains(text(),'{variant_theme_name}')]")))
            store_driver.execute_script("arguments[0].click();", variant_theme_li)
        #to press OK need to find the parent alement first    
        parent_box = store_driver.find_element(By.XPATH,f"//div[contains(@class,'x-window x-layer x-window-default x-closable x-window-closable x-window-default-closable')]")
        parent_box.find_element(By.XPATH, '(.//span[text()="OK"])').click()
        
        wait.until(EC.visibility_of_element_located((By.NAME, "name"))).send_keys(random_product_name)
        short_description = store_driver.find_element(By.XPATH, "(//div[contains(@class, 'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr' )])[1]")
        short_description.click()
        short_description.send_keys(random_short_description) 
        long_description = store_driver.find_element(By.XPATH, "(//div[contains(@class, 'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr' )])[2]")
        long_description.click()
        long_description.send_keys(random_long_description) 
                
        scroll = wait.until(EC.presence_of_element_located((By.XPATH, '//span[text()="SKU "]')))
        store_driver.execute_script("arguments[0].scrollIntoView();", scroll)
        sleep(0.5)
        
        if spotLight:
            spotlight_div = store_driver.find_element(By.XPATH, "(//div[contains(@class, 'x-container object_field object_field_type_checkbox object_field_name_spotLight x-form-item x-form-item-default x-container-default x-anchor-form-item' )])")
            spotlight_div.find_element(By.XPATH,f"//input[contains(@name,'spotLight')]").click()
            json_data['spotLight'] = True  
        else:
            json_data.pop('spotLight','')

        #  -x-window x-layer x-window-default x-closable x-window-closable x-window-default-closable
        dropdown = wait.until(EC.visibility_of_element_located((By.XPATH,'//input[@name="deliveryType"]//..//..//..//../following-sibling::div',)))
        # store_driver.execute_script("arguments[0].click();", dropdown)
        dropdown.click()
        try:
            add_gate = wait.until(EC.visibility_of_element_located( (By.XPATH, f"(//li[text()='{deliveryMethod}'])[1]")))
            action = ActionChains(store_driver)
            action.move_to_element(add_gate).click().perform() ###### INCOSISTANT ISSUE HERE OR LINE BEFORE
        except TimeoutException:
            try:
                logger.critical("the click didnt bring the dropdown to selelct the delivery method... ")
                dropdown.click()
                add_gate = wait.until(EC.visibility_of_element_located( (By.XPATH, f"(//li[text()='{deliveryMethod}'])[1]")))
                action = ActionChains(store_driver)
                action.move_to_element(add_gate).click().perform() ###### INCOSISTANT ISSUE HERE OR LINE BEFORE
            except TimeoutException: 
                logger.critical("the click didnt bring the dropdown to selelct the delivery method after a SECOND time...  ")
                dropdown.click()
                add_gate = wait.until(EC.visibility_of_element_located( (By.XPATH, f"(//li[text()='{deliveryMethod}'])[1]")))
                action = ActionChains(store_driver)
                action.move_to_element(add_gate).click().perform() ###### INCOSISTANT ISSUE HERE OR LINE BEFORE
        sleep(0.5)
        store_driver.execute_script("arguments[0].click();", dropdown)
        #product_type
        store_driver.find_element(By.NAME, 'productType').send_keys(productType)
        try:
            store_driver.find_element(By.XPATH, f"//li[contains(text(),'{productType}')]").click()  ### GOT ERROR ONCE: stale element reference: stale element not found
        except StaleElementReferenceException:
            logger.critical("RECEIVED STEAL ELEMENET ERROR")
            store_driver.find_element(By.NAME, 'productType').send_keys(Keys.CLEAR)
            sleep(1)
            store_driver.find_element(By.NAME, 'productType').send_keys(productType)
            store_driver.find_element(By.XPATH, f"//li[contains(text(),'{productType}')]").click()
            logger.debug("it should have finished succesfully selecting the product type")
            

        product_page = store_driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_Products x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        id_str = product_page.find_element(By.XPATH,".//div[contains(@class,'x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default')]").text
        
        logger.info("the text from the product ID: "+ id_str)
        numberic_string = "".join(char for char in id_str if char.isdigit())
        if json_data:
            json_data['id'] = int(numberic_string)
            json_data["productSet"] = product_type
            json_data['deliveryMethod'] = [deliveryMethod]
            json_data['productType'] = productType
            json_data['sku'] = random_sku
            json_data['name'] = random_product_name
            json_data['shortDescription'] = random_short_description
            json_data['description'] = random_long_description
            json_data.pop('barcode','')
            json_data.pop('category','')
            json_data.pop('Attributes','')
            json_data.pop('notApplicableCountries','')
            json_data.pop('storeProductId','')
            json_data['PriceTaxation']['price']= []
            json_data['PriceTaxation']['specialPrice'] = []
            json_data['PriceTaxation'].pop('isTaxable', "")
            json_data['PriceTaxation'].pop('orderMaxQuantityAllowed', "")
            json_data['PriceTaxation'].pop('cost', "")
            return json_data
        else:
            return int(numberic_string)
    except Exception as e:
        capture_failed_details(store_driver, "create_product", e )
        
        
def element_modified_pop_up(driver, timeout_seconds=10):
    driver.implicitly_wait(timeout_seconds)
    try:
        if driver.find_element(By.XPATH, f"//div[contains(text(),'The desired element is currently opened by another person')]").is_displayed():
            logger.debug('Someone else must of been modifing the element')
            driver.find_element("xpath", '//span[text()="Yes"]').click()
    except:
        # logger.debug("no one was modifying the store configuration") 
        pass   
    driver.implicitly_wait(10)
    

def configure_store_display_currencies(store_driver, displayCurrencies=None):
    try:
        # store_driver.find_element(By.XPATH, '(//div[text()="Store Profile"]//..//following-sibling::div)[2]').click()  ## at loggin the button is already pre-selected
        sleep(0.5)
        WebDriverWait(store_driver, 80).until(EC.visibility_of_element_located((By.CLASS_NAME, "x-tree-node-text"))).click()
        # store_driver.find_element(By.CLASS_NAME, "x-tree-node-text").click()
        element_modified_pop_up(store_driver)
        store_driver.find_element(By.XPATH,f"//span[contains(text(),'Configuration')]").click()
        ## delete all currencies already in display
        currencries_parent_div = store_driver.find_element(By.XPATH,f"//div[contains(@class,'x-field object_field object_field_type_multiselect object_field_name_storeCurrency')]")
        num_currency_element = len(currencries_parent_div.find_elements(By.XPATH,f".//div[contains(@class,'x-tagfield-item-close')]"))
        print('num_currency_element:  ', num_currency_element)
        if num_currency_element:
            for _ in range(num_currency_element):
                print("closing and element")
                currencries_parent_div.find_element(By.XPATH,f".//div[contains(@class,'x-tagfield-item-close')]").click()
                sleep(0.1)        

        if displayCurrencies:
            parent_div_text_input = store_driver.find_element(By.XPATH,f"//div[contains(@class,'x-field object_field object_field_type_multiselect object_field_name_storeCurrency x-form-item x-form-item-default x-form-type-text x-field-default x-anchor-form-item')]")
            store_driver.execute_script("arguments[0].scrollIntoView();", parent_div_text_input.find_element(By.XPATH,f".//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')]"))
            sleep(0.5)
            parent_div_text_input.find_element(By.XPATH,f".//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')]").click()
            currency_text_fields = store_driver.find_element(By.XPATH,f"//div[contains(@class,'x-boundlist x-boundlist-floating x-layer x-boundlist-default x-border-box')]")
            for i in displayCurrencies:
                sleep(0.3)
                logger.debug("display lenguage to add: "+ str(i))
                curr = currency_text_fields.find_element(By.XPATH,f".//li[contains(text(),'({i})')]")
                try: 
                    curr.click()
                except:
                    logger.debug("Need to navigate to click currecncy " + i)
                    store_driver.execute_script("arguments[0].scrollIntoView();", curr)
                    sleep(0.5)
                    curr.click()

        if num_currency_element or displayCurrencies:   ### means currency was changed for the store
            store_driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            assert WebDriverWait(store_driver, 50).until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed(), "there was an error saving the store configuration"
            sleep(2)
        else:
            logger.debug("nothing changed in the store per the parameters passed...")
    except Exception as e:
        capture_failed_details(store_driver, "STORE_configure_store_display_lenguages", e )
        
        
def product_save_and_publish(driver, close_tab_on_save=True, save_succesfull_check=True, timer_arr=None):
    try:
        product_page = driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_Products x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        product_page.find_element(By.XPATH, './/span[text()="Save & Publish"]').click()
        if save_succesfull_check:
            start_time = time.time()
            assert WebDriverWait(driver, 50).until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
            end_time = time.time() 
            if timer_arr is not None:
                save_time = end_time-start_time
                logger.debug("Product saving timer is " + str(save_time))
                pytest.assume(save_time<20, f"Product took more than 10 seconds to save... Total save time: {str(save_time)}")
                timer_arr.append(save_time) 
        if close_tab_on_save:
            sleep(1)
            parent_box = driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
            parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()
            sleep(5)
    except Exception as e:
        capture_failed_details(driver, "STORE_product_save_and_publish", e )
        
        
def catalog_save_and_publish(driver, close_tab_on_save=True): 
    try:   
        product_page = driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        product_page.find_element(By.XPATH, './/span[text()="Save & Publish"]').click()
        assert WebDriverWait(driver, 50).until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
        sleep(1)
        if close_tab_on_save:
            close_active_tab(driver)
    except Exception as e:
        capture_failed_details(driver, "STORE_catalog_save_and_publish", e )
        
def close_active_tab(driver, not_sure=False, try_catch=True ):
    def close_tab():
        parent_box = driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
        parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()
        logger.debug("Tab was closed succesfully")

    if not_sure:
        try:
            close_tab()
        except:
            logger.debug("Tab was closed succesfully")
    elif try_catch: ## this would cause infinite loop when capture_failed_details if there's a an error cloging the tab
        try:
            close_tab()    
        except Exception as e:
            capture_failed_details(driver, "close_active_tab", e ) 
    else:
       close_tab()  

def enter_product_price(store_driver, currency="", price=None, special_price=None, isTaxable=False, MinimumQuantityAllowed=1, MaximumQuantityAllowed=None, prod_input_data=None): #do not pass anything for default currency
    try:
        store_driver.find_element(By.XPATH, '//span[text()="Pricing & Taxation"]').click()
        curr_found = False
        if currency:
            currency = currency.upper()
            logger.info("pressing the currency to the right:"+ currency)
            sleep(0.1)
            arrow_div = store_driver.find_element(By.XPATH, "//div[contains(@class,'x-panel object_field x-panel-default')]")  #x-panel objectlayout_element_Layout objectlayout_element_tabpanel x-fit-item x-panel-default, 
            arrow_button = arrow_div.find_element(By.XPATH, ".//div[contains(@class,'x-box-scroller x-box-scroller-right x-box-scroller-tab-bar')]")
            for i in range(500):
                try:
                    parent_box = store_driver.find_element(By.XPATH,f"//div[contains(@class,'x-panel object_field x-panel-default')]")
                    currency_element = parent_box.find_element(By.XPATH,f".//span[contains(text(),'({currency})')]")
                    if currency_element.is_displayed():
                        logger.debug("the currency button was clicked: " + currency)
                        currency_element.click()
                        curr_found = True
                        break
                    else:
                        try:
                            arrow_button.click()
                        except:
                            logger.warning("There was an error finding the currency after cliking the right arrow button")
                except NoSuchElementException:
                    try:
                        arrow_button.click()
                    except:
                        logger.warning("There was an error finding the currency after cliking the right arrow button")
                # logger.debug("didnt find the currency" + currency+". Shifting right number " + str(i))
                sleep(0.1)
            if curr_found is False:
                logger.info("failed to find the currency after going all the way to the right with 500 clicks. Now going to the LEFT")
                sleep(0.1)
                arrow_div = store_driver.find_element(By.XPATH, "//div[contains(@class,'x-panel object_field x-panel-default')]")  #x-panel objectlayout_element_Layout objectlayout_element_tabpanel x-fit-item x-panel-default, 
                arrow_button = arrow_div.find_element(By.XPATH, ".//div[contains(@class,'x-box-scroller x-box-scroller-left x-box-scroller-tab-bar')]")
                for i in range(500):
                    try:
                        parent_box = store_driver.find_element(By.XPATH,f"//div[contains(@class,'x-panel object_field x-panel-default')]")
                        currency_element = parent_box.find_element(By.XPATH,f".//span[contains(text(),'({currency})')]")
                        if currency_element.is_displayed():
                            logger.debug("the currency button was clicked: " + currency)
                            currency_element.click()
                            break
                        else:
                            arrow_button.click()
                    except NoSuchElementException:
                            arrow_button.click()
                    # logger.debug("didnt find the currency" + currency+". Shifting right number " + str(i))
                    sleep(0.1)
        else:
            logger.debug("since currency was not passed entrying default currency")
        actions = ActionChains(store_driver)
        if price:
            if  currency != "PTS":
                price = str(round(random.uniform(50, 100000.999), random.randint(0, 3))) if price == "random" else price
            else:
                price = str(round(random.randint(50, 100000000))) if price == "random" else price
            print("the currency is ", currency)
            logger.debug("sending keys for currency input field: " + currency)
            logger.debug("with price: %s", price)
            price_field = store_driver.find_element(By.XPATH,f"//input[contains(@name,'price_{currency}')]")   ### this didnt once work when any currency was not passed
            actions.click(price_field).pause(0.5).send_keys(str(price)).perform()
            if prod_input_data:
                prod_input_data['PriceTaxation']['price'].append({"value": convert_string_to_number(price)})
                if currency:
                    prod_input_data['PriceTaxation']['price'][-1]['currency'] = currency

        if special_price:
            if  currency != "PTS":
                special_price = str(round(random.uniform(50, 100.999), random.randint(0, 3))) if special_price == "random" else special_price
            else:
                special_price = str(round(random.randint(50, 1000))) if special_price == "random" else special_price
                logger.debug("specialPrice generated for PTS: %s", special_price)
            special_price_field = store_driver.find_element(By.XPATH,f"//input[contains(@name,'specialPrice_{currency}')]")
            actions.click(special_price_field).pause(0.5).send_keys(str(special_price)).perform()
            if prod_input_data:
                prod_input_data['PriceTaxation']['specialPrice'].append({"value": convert_string_to_number(special_price)})
                if currency:
                    prod_input_data['PriceTaxation']['specialPrice'][-1]["currency"]= currency            

        if isTaxable:
            store_driver.find_element(By.NAME, "isTaxable").click()
            prod_input_data['PriceTaxation']["isTaxable"] = True if prod_input_data else None
        else:
            prod_input_data['PriceTaxation'].pop('isTaxable','') if prod_input_data else None
        
        # Min_Max quantities
        if prod_input_data:
            prod_input_data['PriceTaxation']['orderMinQuantityAllowed'] = 1 if prod_input_data else None
            prod_input_data['PriceTaxation'].pop('orderMaxQuantityAllowed','') if prod_input_data else None
            prod_input_data['PriceTaxation'].pop('cost','') if prod_input_data else None  ### cost is not used, but returned in the api
    except Exception as e:
        capture_failed_details(store_driver, "STORE_enter_product_price", e )
    
def convert_string_to_number(value:str):
    try:
        return int(value)
    except ValueError:
        try:
            return float(value)
        except:
            raise Exception("Could not convert the number from string to int/float in convert_string_to_number function")
        
def add_speciality_attribute_1st(driver): #will select first speciality attribute
    # try:
    # except 
    arrow_div = driver.find_element(By.XPATH, "//div[contains(@class,'x-tab-bar x-docked x-tab-bar-default x-horizontal x-tab-bar-horizontal x-tab-bar-default-horizontal x-top x-tab-bar-top x-tab-bar-default-top x-docked-top x-tab-bar-docked-top x-tab-bar-default-docked-top x-noborder-trl x-scroller x-tab-bar-scroller x-tab-bar-default-scroller')]")
    arrow_button = arrow_div.find_element(By.XPATH, ".//div[contains(@class,'x-box-scroller x-box-scroller-right x-box-scroller-tab-bar x-box-scroller-tab-bar-default x-unselectable')]")

    for i in range(20):
        try:
            Speciality_attribute_tab = driver.find_element(By.XPATH, '//span[text()="Speciality Attributes"]')

            if Speciality_attribute_tab.is_displayed():
                Speciality_attribute_tab.click()
                break
            else:
                arrow_button.click()
        except NoSuchElementException:
                arrow_button.click()
        logger.debug("try number" + str(i))
    driver.find_element(By.XPATH,"//div[contains(@class,'x-tagfield x-form-field x-form-text x-form-text-default x-form-empty-field x-form-empty-field-default')]").click()
    # id = driver.find_element(By.XPATH,"//div[contains(@class,'x-list-plain')]/li[1]").text
    # sleep(0.5)
    # list_elements = driver.find_element(By.XPATH,"//*[contains(@class,'x-list-plain')]")
    # sleep(0.5)
    # list_elements.find_element(By.XPATH,f".//li[contains(text(),'Veg')]").click()
    
    wait = WebDriverWait(driver, 10)
    # div_element = wait.until(EC.presence_of_element_located((By.XPATH,"//div[contains(@class,'x-boundlist x-boundlist-floating x-layer x-boundlist-default x-border-box')]")))
    div_element = driver.find_element(By.XPATH,"//div[contains(@class,'x-boundlist x-boundlist-floating x-layer x-boundlist-default x-border-box')]")

    # pppp = ul_element.find_element(By.XPATH,f".//li[contains(text(),'SPA')]")  
    sleep(1)
    pp = div_element.find_element(By.XPATH,".//*[contains(@class,'x-list-plain')]/li[3]")
    # driver.execute_script("arguments[0].scrollIntoView();", pp)
    driver.execute_script("arguments[0].style.height = 'auto'; arguments[0].style.visibility = 'visible';", pp)
    sleep(5)
    driver.execute_script("arguments[0].click();", pp)

    pp.click()
    # action = ActionChains(driver)
    # action.move_to_element(pp).click().perform()
    
    # first_element = wait.until(EC.presence_of_element_located((By.XPATH,"//*[contains(@class,'x-list-plain')]/li[1]")))
    # action = ActionChains(driver)
    # action.move_to_element(first_element).perform()
    # action.click(first_element).perform()    
    # return id

    sleep(0.5)
    # p_set = wait.until(EC.presence_of_element_located((By.XPATH, "//*[contains(@class,'x-list-plain')]/li[2]")))
    # driver.execute_script("arguments[0].click();", p_set)
    # pp = p_set.find_element(By.XPATH, ".//*[contains(@class,'x-boundlist-item')]")
    sleep(1)
    # driver.execute_script("arguments[0].click();", p_set)
    print("pressssed")
    sleep(1000)



def navigate_to_sns(driver):
    WebDriverWait(driver, 20).until((EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'Assets')]"))))
    sleep(2)
    driver.find_element(By.XPATH, "//div[contains(text(),'Assets')]").click()
    logexpander = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.XPATH, "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]")))
    driver.execute_script("arguments[0].scrollIntoView();", logexpander)
    sleep(1)
    driver.find_element(By.XPATH,"//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]",).click()
    logging.info("Clicked on Log")
    WebDriverWait(driver, 10).until(EC.visibility_of_element_located((By.XPATH, "//span[(text()='SNS')]")))
    driver.find_element(By.XPATH, "//span[(text()='SNS')]").click()
    WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "//span[contains(text(),'List')]")))
    driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
    logging.info("Navigated to triggered SNS - List View")
    # sleep(5)
    try:
        WebDriverWait(driver, 15).until(EC.visibility_of_element_located((By.XPATH,"(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]")))
        driver.find_element(By.XPATH,"(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]").click()
        logging.info("Navigated to last page")
    except Exception as e:
        logging.info("last page error")
        logging.info(e)
        pass
    # sleep(100)
    

def download_sns(driver=None, id=None, download_path=None, aws_sns_creator=None):
    if AWS_SNS_CONFIGURE:   #### Airline would be the defult
        sleep(3)
        if  aws_sns_creator == 'store':
            return get_sns_message_by_id_catalog(id)
        else:
            return get_sns_message_by_id_catalog(id, queue_url=CATALOG_SQS_QUEUE_URL)
    def check_last_sns_list(driver):
        current_page = driver.find_element(By.XPATH,"//div[contains(@class,'x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]") # reload the page
        current_page.find_elements(By.XPATH, f".//span[contains(@class,'x-btn-icon-el x-btn-icon-el-plain-toolbar-small x-tbar-loading')]")[-1].click() #### its a partial generic name.. full name: x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable
        try:
            current_page = driver.find_element(By.XPATH,"//div[contains(@class,'x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
            if current_page.find_element(By.XPATH, f".//a[contains(@class,'x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small x-item-disabled x-btn-disabled')]").is_displayed(): #### its a partial generic name.. full name: x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable
                logger.debug("the button is disabled. No need to go to next page")
                try:
                    driver.find_element(By.XPATH,"(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]").click() ## need to try
                except:
                    pass
            else:
                logger.info("the go to next button is grayed out... need to click the next button")
                driver.find_element(By.XPATH,"(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]").click()
                logger.debug("on the else block, clicked the last view")
        except:
            logger.info("the go to next button is not grayed out... need to click the next button")
            driver.find_element(By.XPATH,"(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]").click()
            logger.debug("on the except block, clicked the last view")
    
    def open_sns(driver,sns, id, retries=10, delay=1):
        attempt = 0
        while attempt < retries:
            try:
                # Move to the element to ensure it's in view
                actions = ActionChains(driver)
                sns = find_last_visible_sns(driver, id)
                actions.move_to_element(sns).perform()
                sleep(1)
                actions.double_click(sns).perform()
                logger.debug(f"SNS ID {id} was double clicked")
                element_modified_pop_up(driver,3)
                WebDriverWait(driver, 20).until((EC.visibility_of_element_located((By.XPATH, f"//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_download pimcore_material_icon')]")))).click()    ## to improve speed change from  -- element_to_be_clickable
                return
            except Exception as e:
                logger.critical(f"Double-click FAILED to open the SNS on attempt {attempt + 1}: {str(e)}")
                sleep(delay)  # Slow down retry attempts
                attempt += 1
        raise Exception(f"Failed to double-click after {retries} attempts.")
    def find_last_visible_sns(driver, id):
        elements = driver.find_elements(By.XPATH, f"//div[contains(text(),'_{id}_')]")
        visible_elements = [element for element in elements if element.is_displayed()]
        list_of_files = []
        if visible_elements:
                logger.debug("there was these number of visible elements: %s", len(visible_elements))
                visible_elements.reverse()
                for element in visible_elements:
                    logger.debug("the element text: \n %s", element.text)
                    if '/' in element.text:
                        logger.debug("found an element: %s", element.text)
                        list_of_files.append(element)
                        return element
        ### its was not able to find it... need to go back up to 10 pages
        for i in range(10):
            try:
                driver.find_element(By.XPATH,"(//a[@data-qtip='Previous Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])").click()
            except:
                logger.debug(f"the previous button may have been preselected in try {str(i+i)}")
                driver.find_element(By.XPATH,"(//a[@data-qtip='Previous Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small x-btn-over'])").click()
                logger.debug("Succesfully pressed the prevous button")
            elements = driver.find_elements(By.XPATH, f"//div[contains(text(),'_{id}_')]")
            visible_elements = [element for element in elements if element.is_displayed()]
            list_of_files = []
            if visible_elements:
                    logger.debug("there was these number of visible elements: %s", len(visible_elements))
                    visible_elements.reverse()
                    for element in visible_elements:
                        logger.debug("the element text: \n %s", element.text)
                        if '/' in element.text:
                            logger.debug("found an element: %s", element.text)
                            list_of_files.append(element)
                            return element
            logger.debug(f"Not able to find the sns in page(try): {str(i+1)}")
        raise Exception(f"Not able to find the sns payload of the product ID: {id}")

                
    
    try:
        try:
            check_last_sns_list(driver)
            logger.debug("right before looking for the sns")
            sleep(2)
            sns = find_last_visible_sns(driver, id)
            open_sns(driver, sns, id)
        except:
            sleep(5)
            check_last_sns_list(driver)
            sleep(2)
            logger.debug("the SNS was not found using the ID... refreshed and trying one more time")
            try: 
                sns = sns = find_last_visible_sns(driver, id)
                open_sns(driver, sns, id)
            except:  # just in case it doesnt find it after one reload
                sleep(8)
                check_last_sns_list(driver)
                sleep(1)
                logger.info("the SNS was not found using the ID... refreshed and trying last time")
                sns = find_last_visible_sns(driver, id)
                logger.info("it was able to find it on the last try...")
                open_sns(driver, sns, id)
        
        # actions = ActionChains(driver)
        # actions.context_click(sns).perform()
        # # driver.execute_script("arguments[0].dblclick();", sns)
        # logger.debug("double clicked the first time. The text of this element: %s", sns.text)
        # sleep(10)
        # # actions.double_click(sns).perform()
        # element_modified_pop_up(driver)
        # WebDriverWait(driver, 20).until((EC.visibility_of_element_located((By.XPATH, f"//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_download pimcore_material_icon')]")))).click()    ## to improve speed change from  -- element_to_be_clickable
        # driver.find_element(By.XPATH, f"//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_download pimcore_material_icon')]").click()

        logger.info(f"element with id {id} was downloaded" )
        close_active_tab(driver)
    except Exception as e:
        capture_failed_details(driver, "PAC_USER_download_sns", e )
    if download_path:
        logger.debug("download path was provided. Returning the json payload")
        return open_sns_payload(download_path, id )
     

    
def product_compare_get(session, input_json):
    sleep(4)
    get_response = get_product(id=str(input_json['id']),session= session)
    assert get_response.status_code == 200
    get_json_response = get_response.json()
    assert compare_input_to_expected_data(get_json_response["data"]["product"], product_get_api_data_transform(json_data=input_json))
    
def product_variants_button_state_check(driver, expected_state:string='true'):
    try:
        driver.implicitly_wait(10)  
        current_page = driver.find_element(By.XPATH, "//div[@class='x-panel pimcore_class_Products x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")

        current_page.find_element(By.XPATH, './/span[text()="Variants"]').click()  ### failed onece
        sleep(0.3)
        add_variant_button = driver.find_element(By.XPATH, '//span[text()="Add variant"]/../../..')
        unselectable_button = add_variant_button.get_attribute('aria-disabled')
        assert unselectable_button == expected_state
    except Exception as e:
        capture_failed_details(driver, "product_variants_button_state_check", e )
    
    
    
def product_add_variant(driver, track_product_data=None):
    try:
        driver.find_element(By.XPATH, '//span[text()="Variants"]').click()
        sleep(0.3)
    except:
        logger.debug("there was an error clicking the driver button")
    add_variant_button = driver.find_element(By.XPATH, '//span[text()="Add variant"]/../../..')
    unselectable_button = add_variant_button.get_attribute('aria-disabled')
    assert unselectable_button == "false"
    try:
        add_variant_but = driver.find_element(By.XPATH, '//span[text()="Add variant"]/..').click()
        driver.execute_script("arguments[0].click();", add_variant_but)

    except Exception as E:
        logger.debug("error adding variants: " + str(E))
        sleep(1)
    pop_up = driver.find_element(By.XPATH, f"//div[contains(@class,'x-window x-message-box x-layer x-window-default x-closable x-window-closable x-window-default-closable x-border-box')]")
    pop_up.find_element(By.XPATH, ".//input[contains(@class, 'x-form-field x-form-text x-form-text-default  x-form-empty-field x-form-empty-field-default')]").send_keys("asdfasdf")
    pop_up.find_element(By.XPATH, './/span[text()="OK"]').click()
    sleep(1)
    fill_first_variant_page(driver)
    
def fill_first_variant_page(store_driver, name=None, text_append="var_1", deliveryMethod='Onboard', productType='Duty Free', brand="test_Brand ", spotLight=False, json_data=None):
    if name is None:
        date_string_p = current_date.strftime('%Y_%m_%d__%H_%M_%S')
        result = 'uiAuto_'+ ''.join(random.choices(string.ascii_letters, k=10)) +"_"
    random_sku = text_append + "_" + result[0:11] + date_string_p 
    random_product_name = text_append + "_"+ result + date_string_p
    random_short_description = f"Short description for {result + date_string_p}"
    random_long_description = f"Long description for {result + date_string_p}"
    wait = WebDriverWait(store_driver, 10)
    name = wait.until(EC.presence_of_element_located((By.NAME, "name")))
    actions = ActionChains(store_driver)
    try:
        # store_driver.find_element(By.XPATH, f"//input[@name='name']/../..").click()
        # store_driver.find_element(By.XPATH, f"//input[@name='name']/..").send_keys('aa')
        actions.move_to_element(name).click().perform()
        store_driver.execute_script("arguments[0].focus();", name)
        logger.debug("focused")
        store_driver.execute_script("arguments[0].click();", name)
        logger.debug("clicked")
        store_driver.execute_script("arguments[0].value='aaa';", name)
        logger.debug("send keys with javascript")

        store_driver.find_element(By.XPATH, f"//input[@name='name']").send_keys('aa')
        logger.debug("send aa")

    except:
        logger.debug("the send keys threw an exeptions")
        # store_driver.find_element(By.XPATH, f"//input[@name='name']/../../..").click()
        store_driver.find_element(By.XPATH, f"//input[@name='name']").send_keys('bb')

        sleep(10)
    #not interactible:
    # <input id="textfield-1614-inputEl" data-ref="inputEl" type="text" size="1" name="name" value="uiAuto_oEUgwKhBhy_2024_01_29__10_16_38" maxlength="190" aria-hidden="false" aria-disabled="false" role="textbox" aria-invalid="false" aria-readonly="false" aria-describedby="textfield-1614-ariaStatusEl" aria-required="false" class="x-form-field x-form-text x-form-text-default " autocomplete="off" data-componentid="textfield-1614">
    
    
    ##sku
    short_description = store_driver.find_element(By.XPATH, "(//div[contains(@class, 'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr' )])[1]")
    short_description.click()
    short_description.send_keys(text_append) 
    long_description = store_driver.find_element(By.XPATH, "(//div[contains(@class, 'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr' )])[2]")
    long_description.click()
    long_description.send_keys(text_append) 
    
    # brand_div = store_driver.find_element(By.XPATH, "(//div[contains(@class, 'x-field object_field object_field_type_input object_field_name_brand x-form-item x-form-item-default x-form-type-text x-field-default x-autocontainer-form-item' )])")
    # brand_text_field = brand_div.find_element(By.XPATH, "(.//input[contains(@class, 'x-form-field x-form-text x-form-text-default  x-form-empty-field x-form-empty-field-default' )])")
    # brand_text_field.click()
    # brand_text_field.send_keys(brand + random_sku)
    
    scroll = wait.until(EC.presence_of_element_located((By.XPATH, '//span[text()="SKU "]')))
    store_driver.execute_script("arguments[0].scrollIntoView();", scroll)
    sleep(0.5)
    
    if spotLight:
        spotlight_div = store_driver.find_element(By.XPATH, "(//div[contains(@class, 'x-container object_field object_field_type_checkbox object_field_name_spotLight x-form-item x-form-item-default x-container-default x-anchor-form-item' )])")
        spotlight_div.find_element(By.XPATH,f"//input[contains(@name,'spotLight')]").click()
        json_data['spotLight'] = True  
    else:
        json_data.pop('spotLight','')

    #  -x-window x-layer x-window-default x-closable x-window-closable x-window-default-closable
    dropdown = wait.until(EC.visibility_of_element_located((By.XPATH,'//input[@name="deliveryType"]//..//..//..//../following-sibling::div',)))
    # store_driver.execute_script("arguments[0].click();", dropdown)
    dropdown.click()
    try:
        add_gate = wait.until(EC.visibility_of_element_located( (By.XPATH, f"(//li[text()='{deliveryMethod}'])[1]")))
        action = ActionChains(store_driver)
        action.move_to_element(add_gate).click().perform() ###### INCOSISTANT ISSUE HERE OR LINE BEFORE
    except TimeoutException:
        try:
            logger.critical("the click didnt bring the dropdown to selelct the delivery method on first click... ")
            dropdown.click()
            add_gate = wait.until(EC.visibility_of_element_located( (By.XPATH, f"(//li[text()='{deliveryMethod}'])[1]")))
            action = ActionChains(store_driver)
            action.move_to_element(add_gate).click().perform() ###### INCOSISTANT ISSUE HERE OR LINE BEFORE
        except TimeoutException: 
            logger.critical("the click didnt bring the dropdown to selelct the delivery method after a SECOND time clicked...  ")
            dropdown.click()
            add_gate = wait.until(EC.visibility_of_element_located( (By.XPATH, f"(//li[text()='{deliveryMethod}'])[1]")))
            action = ActionChains(store_driver)
            action.move_to_element(add_gate).click().perform() ###### INCOSISTANT ISSUE HERE OR LINE BEFORE
    sleep(0.5)
    store_driver.execute_script("arguments[0].click();", dropdown)
    #product_type
    store_driver.find_element(By.NAME, 'productType').send_keys(productType)
    store_driver.find_element(By.XPATH, f"//li[contains(text(),'{productType}')]").click()  ### GOT ERROR ONCE: stale element reference: stale element not found

    product_page = store_driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_Products x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
    id_str = product_page.find_element(By.XPATH,".//div[contains(@class,'x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default')]").text
    
    logger.info("the text from the product ID: "+ id_str)
    numberic_string = "".join(char for char in id_str if char.isdigit())
    if json_data:
        json_data['id'] = int(numberic_string)
        json_data['deliveryMethod'] = [deliveryMethod]
        json_data['productType'] = productType
        json_data['sku'] = random_sku
        json_data['name'] = random_product_name
        json_data['shortDescription'] = random_short_description
        json_data['description'] = random_long_description
        json_data['brand'] = brand + random_sku
        json_data.pop('barcode','')
        json_data.pop('category','')
        json_data.pop('Attributes','')
        json_data.pop('notApplicableCountries','')
        json_data.pop('storeProductId','')
        return json_data
    else:
        return int(numberic_string)
    
    

    
def create_catalog(driver, name=None, productIDs:list=["69014418", "69014417", "69014416", "69014415", "69014414" ], timer_arr=None):
    try:
        logger.debug("Creating a catalog with the following product IDs")
        logger.debug(str(productIDs))
        if name is None:
            date_string_p = current_date.strftime('%Y_%m_%d__%H_%M_%S')
            result = 'uiAuto_catalog_'+ ''.join(random.choices(string.ascii_letters, k=3)) +"_"
            random_name = result + date_string_p
        wait = WebDriverWait(driver, 30)
        actions = ActionChains(driver)
        wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Catalog Management"]'))).click()  ### failed....
        catalog_button = wait.until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Catalog"]')))
        actions.context_click(catalog_button).perform()
        wait.until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Add Object"]'))).click()
        wait.until(EC.visibility_of_element_located((By.XPATH, "//span[text()='Catalog' and contains(@class, 'x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator')]"))).click()
        catalog_name_pop_up = driver.find_element(By.XPATH, "//div[contains(@class, 'x-window x-message-box x-layer x-window-default x-closable x-window-closable x-window-default-closable x-border-box')]")
        catalog_name_pop_up.find_element(By.XPATH, ".//input[contains(@class, 'x-form-field x-form-text x-form-text-default  x-form-empty-field x-form-empty-field-default')]").send_keys(random_name)
        catalog_name_pop_up.find_element(By.XPATH, './/span[text()="OK"]').click()
        sleep(4)
        # wait.until(EC.visibility_of_element_located((By.NAME, "name"))).send_keys(random_name)
        # spotlight_div.find_element(By.XPATH,f"//*[@name,'spotLight')]").click()
        current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
        current_page.find_element(By.XPATH, './/input[@name="name"]').send_keys(random_name); sleep (0.5)


        product_page = driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        product_page.find_element(By.XPATH, ".//span[text()='Save' and contains(@class, 'x-btn-inner x-btn-inner-default-toolbar-medium')]").click()  
        start_time = time.time()
        try:
            assert WebDriverWait(driver, 50).until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
            end_time = time.time() 
            if timer_arr is not None:
                    save_time = end_time-start_time
                    logger.debug("The catalog save timer is " + str(save_time))
                    pytest.assume(save_time<20, f"Catalog took more than 10 seconds to save... Total save time: {str(save_time)}")
                    timer_arr.append(save_time) 
        except Exception as e:
            assert WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
            end_time = time.time() 
            logger.info("It failed to find saved succesfully after 90 seconds. The value of time from timer fuction: " + str(end_time-start_time))
            timer_arr.append(end_time-start_time) 
            logger.info("Found saved succesfully with precence_of_element located ")
        
        
        sleep(1)
        driver.find_element(By.XPATH, "//span[text()='Products' and contains(@class, 'x-tab-inner x-tab-inner-default')]").click()   
        product_page = driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        product_page.find_element(By.XPATH, ".//span[contains(@class, 'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search')]").click() ### issue here
        product_pop_up = driver.find_element(By.XPATH, "//div[contains(@class, 'x-window-body x-window-body-default x-closable x-window-body-closable x-window-body-default-closable x-noborder-trbl x-resizable x-window-body-resizable x-window-body-default-resizable')]")
        text_box = product_pop_up.find_element(By.XPATH, ".//input[contains(@class, 'x-form-field x-form-text x-form-text-default  x-form-empty-field x-form-empty-field-default')]")
        driver.execute_script("arguments[0].scrollIntoView();", text_box)
        for i in productIDs:
            id = str(i)
            text_box.send_keys(id)
            text_box.send_keys(Keys.ENTER)
            product_id = product_pop_up.find_element(By.XPATH, f".//div[text()={id}]")
            actions.double_click(product_id).perform()
            text_box.clear()
            sleep(0.2)
        product_page = driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        product_pop_up.find_element(By.XPATH, ".//span[text()='Select']").click()
        product_page = driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        id_str = product_page.find_element(By.XPATH,".//div[contains(@class,'x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default')]").text
        logger.debug("the text from the product ID: "+ id_str)
        id_number = "".join(char for char in id_str if char.isdigit())
        return random_name, id_number
    except Exception as e:
        capture_failed_details(driver, "STORE_create_catalog", e )


def default_currency_validation(driver, field='price'):   ##### OLD
    try:
        wait = WebDriverWait(driver, 15)
        if field == "price":
            message_pop_up = wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Validation failed: Value required for default currency [ price_USD ]"]')))
            assert message_pop_up.is_displayed()
            logger.debug("the price validation message was displayed")
        elif field == "specialPrice":
            message_pop_up =  wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Validation failed: Value required for default currency [ specialPrice_USD ]"]')))
            assert message_pop_up.is_displayed()
            logger.debug("the Special price validation message was displayed")
        sleep(2)
        message_pop_up.find_element(By.XPATH, ".//..//..//..//span[text()='OK']").click()
    except Exception as e:
        capture_failed_details(driver, "STORE_price_validation_error", e )
        
        
        
def create_catalog_assigment(airline_driver, catalog_id, name=None, FromDate=None, ToDate=None, assignmentType="Sector", filter_value="sec1", products_skus:list=None, AirlineName="Airline" ):
    airline_driver.implicitly_wait(20)
    try:
        if name is None:
            date_string_p = current_date.strftime('%Y_%m_%d__%H_%M_%S')
            result = 'uiAuto_CA_'+ ''.join(random.choices(string.ascii_letters, k=3)) +"_"
            name = result + date_string_p
        wait = WebDriverWait(airline_driver, 15)
        actions = ActionChains(airline_driver)
        
        catalogAssigment_view = airline_driver.find_element(By.XPATH, '//div[text()="Catalog Assignment"]/..')
        if catalogAssigment_view.get_attribute("aria-expanded") != "true":
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Catalog Assignment"]'))).click() 
            logger.debug("Catalog assigment page was not expanded. Clicked the page to expand the section")
            
        catalogAssigment_button = wait.until(EC.visibility_of_element_located((By.XPATH, f'(//div[text()="Catalog Assignment"]//../ancestor::div)[9]//span[contains(text(),"{AirlineName}")]')))

        actions.context_click(catalogAssigment_button).perform()
        sleep(0.5)
        try:
            Add_object_button = wait.until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Add Object"]')))
            actions.move_to_element(Add_object_button).perform()
            sleep(0.5)
            Add_object_button.click()
            logger.debug("It was able to click the Add Object button on first try :) ")
        except Exception as e:
            sleep(1)
            logger.info("ERR: There was an add Object element that was not visible... %s", e)
            elements = airline_driver.find_elements(By.XPATH, '//span[text()="Add Object"]')
            visible_elements = [element for element in elements if element.is_displayed()]
            if visible_elements:
                Add_object_button = visible_elements[-1]
                actions.move_to_element(Add_object_button).perform()
                sleep(0.5)
                Add_object_button.click()
            else:
                sleep(1)
                Add_object_button = airline_driver.find_elements(By.XPATH, '//span[text()="Add Object"]')[-1]
                actions.move_to_element(Add_object_button).perform()
                sleep(0.5)
                Add_object_button.click()
                logger.critical("it was not able to find a visible 'Add Object' button, but clicked the last button found")

        try:
            wait.until(EC.visibility_of_element_located((By.XPATH, "//span[text()='CatalogAssignment' and contains(@class, 'x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator')]"))).click()  ## issue here... new elements are draw and need to click the last one..
        except:
            sleep(1)
            elements = airline_driver.find_elements(By.XPATH, "//span[text()='CatalogAssignment' and contains(@class, 'x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator')]")
            visible_elements = [element for element in elements if element.is_displayed()]
            if visible_elements:
                visible_elements[-1].click()
            else:
                sleep(1)
                airline_driver.find_elements(By.XPATH, "//span[text()='CatalogAssignment' and contains(@class, 'x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator')]")[-1].click() ## there's ocassions where the previous click did not expand for this button to be clicked
                logger.debug("it was not able to find a visible add Catalog assigment button")
        pop_up = airline_driver.find_element(By.XPATH, f"//div[contains(@class,'x-window x-message-box x-layer x-window-default x-closable x-window-closable x-window-default-closable x-border-box')]")
        pop_up.find_element(By.XPATH, ".//input[contains(@class, 'x-form-field x-form-text x-form-text-default  x-form-empty-field x-form-empty-field-default')]").send_keys(name)
        pop_up.find_element(By.XPATH, './/span[text()="OK"]').click()
            ## click search button to add the catalog
        airline_driver.find_element(By.XPATH, "//span[contains(@class, 'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search')]").click()
        sleep(2)
        try:
            product_pop_up = airline_driver.find_element(By.XPATH, "//div[@class='x-window-body x-window-body-default x-closable x-window-body-closable x-window-body-default-closable x-noborder-trbl x-resizable x-window-body-resizable x-window-body-default-resizable']") ## Failed once 3/22 9
        except:
            product_pop_up = airline_driver.find_element(By.XPATH, "//div[@class='x-window-body x-window-body-default x-closable x-window-body-closable x-window-body-default-closable x-noborder-trbl x-resizable x-window-body-resizable x-window-body-default-resizable']")
            logger.info("In Except - It was able to get the catalog pop-up")
        text_box = product_pop_up.find_element(By.XPATH, ".//input[contains(@class, 'x-form-field x-form-text x-form-text-default  x-form-empty-field x-form-empty-field-default')]")
        airline_driver.execute_script("arguments[0].scrollIntoView();", text_box)
        id = str(catalog_id)
        text_box.send_keys(id)
        text_box.send_keys(Keys.ENTER)
        sleep(2)
        try:
            product_id = product_pop_up.find_element(By.XPATH, f".//div[text()={id}]")
        except:
            raise Exception("Catalog could not be found. Was it created succesfully?")
        actions.double_click(product_id).perform()
        #fromDate  #name=catalogFromDate
        if FromDate is None:
            FromDate = current_date.strftime('%Y-%m-%d')
        # current_page = airline_driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        sleep(0.5)
        try:
            current_page = airline_driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
            current_page.find_element(By.XPATH, './/input[@name="catalogFromDate"]').send_keys(FromDate, Keys.TAB)
        except:
            logger.info("Exception sending catalogFromDate. Will try sending the date in 3 seconds.")
            sleep(3)
            current_page = airline_driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
            current_page.find_element(By.XPATH, './/input[@name="catalogFromDate"]').click(); sleep (0.5)
            current_page.find_element(By.XPATH, './/input[@name="catalogFromDate"]').send_keys(FromDate, Keys.TAB)
            logger.info("From date was sent successfully the 2nd time!")

        if ToDate is None:
            future_date = current_date  + relativedelta(years=1)
            ToDate = future_date.strftime('%Y-%m-%d')
        current_page = airline_driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
        current_page.find_element(By.XPATH, './/input[@name="catalogToDate"]').send_keys(ToDate, Keys.TAB)
        current_page.find_element(By.XPATH, './/input[@name="assignmentType"]').click(); sleep (0.3)
        current_page.find_element(By.XPATH, './/input[@name="assignmentType"]').send_keys(assignmentType, Keys.TAB)
        print("send the assigment type for sector")
            ### Sector is the only filter supported
        sectors = wait.until(EC.visibility_of_element_located( (By.XPATH, f'//div//b[text()="{assignmentType}"]')))
        assert sectors.is_displayed()
        flight = wait.until(EC.visibility_of_element_located((By.XPATH,'(//div//b[text()="Sector"]//..//..//..//..//../div)[11]//span[text()="Flight"]', ) )).is_displayed()
        assert flight
        search_sector = wait.until(EC.visibility_of_element_located((By.XPATH, '(//div//b[text()="Sector"]//..//..//../a)[3]')))
        airline_driver.execute_script("arguments[0].click();", search_sector)
        
        search_object = wait.until(EC.visibility_of_element_located((By.XPATH, '//input[@name="query"]')))
        search_object.send_keys(filter_value, Keys.ENTER)
        sleep(1)
        select_flight = wait.until(EC.element_to_be_clickable((By.XPATH,f'//td//div[contains(text(),"{filter_value}")]',)))
        actions.double_click(select_flight).perform()
        sleep(0.5)
        dismiss_notification(airline_driver, 2)
        try:
            wait.until( EC.element_to_be_clickable((By.XPATH, '//span[text()="Select"]'))).click()
        except:
            logger.info('there was an exception... it might have been intercepted by a notification... checking for notification')
            dismiss_notification(airline_driver, 1)
            sleep(2)
            wait.until( EC.element_to_be_clickable((By.XPATH, '//span[text()="Select"]'))).click()
            logger.info("clicking select was succesful in the except block")
            # sleep(10000)  ### in case theres issues with interceptions
        # ele_message = wait.until(EC.visibility_of_element_located((By.XPATH,'//div[text()="All unsaved changes will be lost, are you really sure?"]',) )).is_displayed()  ##these code was not needed locally
        # assert ele_message
        # wait.until(EC.visibility_of_element_located((By.XPATH, '//span[text()="No"]'))).click()
        # sleep(5)
        # flights = wait.until(EC.element_to_be_clickable((By.XPATH,f'((//div[contains(text(),"{filter_value}")]//..//../td)[3]//div)[1]',) ))
        ### navigate to products
        sleep(4)
        #### SHOULD WE VERIFY IF ALL PRODUCTS ASSIGNED TO THE CATALOG ARE DISPLAYED
        verify_products_in_catalog_assigment(airline_driver, products_skus) if products_skus else None
        verify_airline_price_view_is_disabled(airline_driver)  ### NEEDS TO BE UNCOMMENTED            
        product_page = airline_driver.find_element(By.XPATH,"//div[contains(@class,'x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        id_str = product_page.find_element(By.XPATH,".//div[contains(@class,'x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default')]").text
        logger.info("The catalog assigment ID: "+ id_str)
        ID = "".join(char for char in id_str if char.isdigit())
        return ID
    except Exception as e:
        capture_failed_details(airline_driver, "create_catalog_assigment", e )
        
def dismiss_notification(driver, timeout=3, dismiss_all=False):
    driver.implicitly_wait(timeout)
    if dismiss_all:
        try:
            while True:
                current_page = driver.find_element(By.XPATH,"//div[@class='x-window-header x-header x-docked x-unselectable x-window-header-default x-horizontal x-window-header-horizontal x-window-header-default-horizontal x-top x-window-header-top x-window-header-default-top x-box-layout-ct']")
                x = current_page.find_element(By.XPATH,".//div[@class='x-tool x-box-item x-tool-default x-tool-after-title']")
                if x.is_displayed():
                    x.click()
                    logger.info("Dismissed a notification pop-up")
                    sleep(1)
                else:
                    logger.debug("There was no notification visible. Breaking out")
                    break
        except Exception as e:
            logger.info("Checked for all notifications, but couldnt find anymore")
            pass
    else:
        try:
            current_page = driver.find_element(By.XPATH,"//div[@class='x-window-header x-header x-docked x-unselectable x-window-header-default x-horizontal x-window-header-horizontal x-window-header-default-horizontal x-top x-window-header-top x-window-header-default-top x-box-layout-ct']")
            current_page.find_element(By.XPATH,".//div[@class='x-tool x-box-item x-tool-default x-tool-after-title']").click()
            logger.info("dismissed a notification pop-up")
        except Exception as e:
            logger.info("Checked for notification, but couldnt find any")
            pass
    driver.implicitly_wait(15)    
    
def CA_request_for_association(driver, verify_action_applied_succesfully=True):
    try:
        driver.implicitly_wait(10)
        current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
        current_page.find_element(By.XPATH,".//a[contains(@class,'x-btn pimcore_workflow_button x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-medium')]").click()    
        action_button  = driver.find_element(By.XPATH, '(//span[text()="Request for Association"])')
        driver.execute_script("arguments[0].scrollIntoView();", action_button)
        action_button.click()
        try:
            ##check if the pop-up is displayed  
            print("checking if modified CA is diplayed")
            wait = WebDriverWait(driver, 10)
            wait.until( EC.visibility_of_any_elements_located((By.XPATH, f"//div[contains(text(),'All unsaved changes will be lost, are you really sure?')]")))    
            print("found the text fro the pop-up")
            current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
            current_page.find_element(By.XPATH, '(//span[text()="Yes"])').click()
            print("clicked yes")
        except:
            print("The pop-up was not displayed or failed to click yes")
        
        # sleep(1)
        # if verify_action_applied_succesfully:
        #     assert WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.XPATH, '//div[text()="Request For Association"]')))
        # Request for Association
    except Exception as e:
        capture_failed_details(driver, "AIRLINE_CA_request_for_association", e )

     
        
def verify_products_in_catalog_assigment(driver, products_skus, airline_view=False):
    wait = WebDriverWait(driver, 30)
    try:
        current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
        WebDriverWait(current_page, 30).until(EC.presence_of_element_located((By.XPATH, ".//span[text()='Products' and contains(@class,'x-tab-inner x-tab-inner-default')]"))).click() ## TIMEDOUT EXCEPTION HERE....
    except:
        logger.debug("element was stale to be clickable. Trying after 5 seconds")
        sleep(5)
        current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
        WebDriverWait(current_page, 10).until(EC.visibility_of_element_located((By.XPATH, ".//span[text()='Products' and contains(@class,'x-tab-inner x-tab-inner-default')]"))).click() 
    logger.info("verifying all the skus added to the catalog are displayed under Products")
    for sku in products_skus:
        logger.debug("verifying %s", sku )
                    #### may need ot iterate over all the elements to find if one is visible
        try:
            all_elements_with_sku = wait.until( EC.visibility_of_any_elements_located((By.XPATH, f'//div[text()="{sku}"]')))
        except Exception as e:
            logger.critical("ERROR Finding elemnts of sku: %s", sku)
            raise
        sku_elemet = [elem for elem in all_elements_with_sku if elem.is_displayed()]
        assert len(sku_elemet) > 0
    #verify prodcuts are displayed on the product price tab
    current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
    WebDriverWait(current_page, 10).until(EC.element_to_be_clickable((By.XPATH, ".//span[text()='Product Price']"))).click()  ##  Jul 11 - TIMEDOUT EXCEPTION HERE... picture of failure shows the button was there, but there was another CA in the background..

    # elements = current_page.find_elements(By.XPATH, "//span[text()='Product Price']")  ### leaving code in case need logic to click an element 
    # navigated_Product_Price = False
    # for element in elements:
    #     try:
    #         WebDriverWait(current_page, 2).until(EC.element_to_be_clickable(element)).click()
    #         navigated_Product_Price = True
    #         break
    #     except Exception as e:
    #         print(f"Failed to click the element: {e}")
    # assert navigated_Product_Price == True, "Failed to open the product Price tab"
    if airline_view:  ##### THIS MAY NOT BE NEEDED
        logger.info("On airline, verifying all the skus added to the catalog are displayed under Product Price")
        for sku in products_skus:   ### 
            logger.debug("verifying %s", sku )
            assert wait.until( EC.presence_of_element_located((By.XPATH, f'//div[text()="{sku}"]')))
    else:
        logger.info("On store, verifying all the skus added to the catalog are displayed under Product Price")
        for sku in products_skus:   ### 
            logger.debug("verifying %s", sku )
            #### may need ot iterate over all the elements to find if one is visible
            all_elements_with_sku = wait.until( EC.visibility_of_any_elements_located((By.XPATH, f'//div[text()="{sku}"]')))
            sku_elemet = [elem for elem in all_elements_with_sku if elem.is_displayed()]
            assert len(sku_elemet) > 0
        


    #### check the prices are disabled for airlines
def verify_airline_price_view_is_disabled(airline_driver, navigate_to_product_prices=False):
    if navigate_to_product_prices:
        wait = WebDriverWait(airline_driver, 10)
        try:
            wait.until(EC.element_to_be_clickable((By.XPATH, "//span[text()='Product Price' and contains(@class,'x-tab-inner x-tab-inner-default')]"))).click()
        except StaleElementReferenceException:
            logger.debug("element was stale to be clickable. Trying after 5 seconds")
            sleep(5)
            wait.until(EC.element_to_be_clickable((By.XPATH, "//span[text()='Product Price' and contains(@class,'x-tab-inner x-tab-inner-default')]"))).click()
    current_page = airline_driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")    
    current_page.find_element(By.XPATH, ".//div[contains(@class, 'x-panel-header x-header x-docked x-unselectable x-panel-header-default x-horizontal x-panel-header-horizontal x-panel-header-default-horizontal x-top x-panel-header-top x-panel-header-default-top x-docked-top x-panel-header-docked-top x-panel-header-default-docked-top x-box-layout-ct x-panel-default-outer-border-trl')]").is_displayed()
    current_page.find_element(By.XPATH, ".//div[contains(@class, 'x-mask x-border-box')]").is_displayed()
    current_page.find_element(By.XPATH, ".//div[contains(@class, 'x-panel x-item-disabled x-fit-item x-panel-default x-grid x-masked-relative x-masked')]").is_displayed()
    logger.debug("COMPLETED CHECKING Product price view for airline")
    
    
def store_open_catalog_assigment(store_driver, ca_id, products_skus:list=[], airline_name="", store_name="", creation_delay=True):
    if creation_delay:
        sleep(10)
    try:
        ### there is a expanded="true"
        ##x-title x-panel-header-title x-panel-header-title-default x-box-item x-title-default x-title-rotate-none x-title-align-left
        wait = WebDriverWait(store_driver, 40)
        actions = ActionChains(store_driver)
        # wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Catalog Assignment"]'))).click() 
        for i in range(3):  
            # catalogAssigment_view = store_driver.find_element(By.XPATH, '//div[text()="Catalog Assignment"]/..')
            catalogAssigment_view = WebDriverWait(store_driver, 30).until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Catalog Assignment"]/..')))
            if catalogAssigment_view.get_attribute("aria-expanded") != "true":
                logger.debug("the catalog assigment page is not expanded")
                wait.until(EC.element_to_be_clickable((By.XPATH, '//div[text()="Catalog Assignment"]'))).click() 
            try:
                # airline_tr = wait.until(EC.presence_of_element_located((By.XPATH, f'(//div[text()="Catalog Assignment"]//../ancestor::div)[9]//span[contains(text(),"{airline_name}")]/ancestor::tr[1]'))) ## FAILED 4! March 22: Increase wait time to 30s.
                sleep(1)
                left_panel = WebDriverWait(store_driver, 20).until(EC.presence_of_element_located((By.XPATH,"//div[@class='x-panel-body x-panel-body-default x-accordion-layout-ct x-panel-body-default x-noborder-trbl']"))) 
                airline_1 = WebDriverWait(left_panel, 20).until(EC.presence_of_element_located((By.XPATH, f'//div[text()="Catalog Assignment"]/ancestor::div[5]')))
                span_element = airline_1.find_element(By.XPATH, f'.//span[contains(text(),"{airline_name}")]')
                airline_tr = span_element.find_element(By.XPATH, f'.//ancestor::tr[1]')
                logger.debug("The catalog assigments folder was found successfully")  
            except:
                logger.info("In except - Store failure in the catalog assigments folder opened successfully")
                left_panel = WebDriverWait(store_driver, 20).until(EC.presence_of_element_located((By.XPATH,"//div[@class='x-panel-body x-panel-body-default x-accordion-layout-ct x-panel-body-default x-noborder-trbl']")))
                airline_1 = WebDriverWait(left_panel, 20).until(EC.presence_of_element_located((By.XPATH, f'//div[text()="Catalog Assignment"]/ancestor::div[5]')))
                span_element = airline_1.find_element(By.XPATH, f'.//span[contains(text(),"{airline_name}")]')
                airline_tr = span_element.find_element(By.XPATH, f'.//ancestor::tr[1]')
                logger.info("In except - It was able to find the catalog assigments folder (element) with store driver")
                
            if airline_tr.get_attribute("class") != "x-grid-tree-node-expanded  x-grid-row":
                logger.info("the airline catalog assigment was not expanded. Going to double click on the store name")
                sleep(1)
                airline_name_element = wait.until(EC.element_to_be_clickable((By.XPATH, f'(//div[text()="Catalog Assignment"]//../ancestor::div)[9]//span[contains(text(),"{airline_name}")]')))
                actions.double_click(airline_name_element).perform()
                logger.info("Double clicked on the airline name to expand the view")
            else:
                logger.debug("The airline folder for catalog assigments was already expanded. Not needed to do anything")
            
        # for i in range(5):
        #     try:
        #         logger.debug("chcking if page is opened")
        #         parent_box = store_driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
        #         parent_box.find_element(By.XPATH, f'(.//span[contains(text(),"{store_name}")]').is_displayed()
        #         logger.debug("Page is opened. Exiting for loop!!")
        #         break
        #     except:
        #         logger.info(f"Page is not opened. Double-click try {str(i+1)}")
        #         airline_name_element = wait.until(EC.element_to_be_clickable((By.XPATH, f'(//div[text()="Catalog Assignment"]//../ancestor::div)[9]//span[contains(text(),"{store_name}")]')))
        #         actions.double_click(airline_name_element).perform()
        #         logger.info("Double clicked on the airline name to expand the view")
        #         sleep(2)
        
            try:
                wait.until(EC.element_to_be_clickable((By.XPATH, f'//div[text()="Catalog Assignment"]/ancestor::div[5]//span[contains(text(),"{store_name}")]'))).click()
                logger.debug("The stores catalog assigments folder was clicked successfully")  
                break
            except Exception as e:
                logger.info("Failure on clicking the store catalog assigment folder! It may be opened, but needs to evaluated in case of test failure. Attempt #%s", str(i+1))
                logger.info(f"Error: \n {e}" )
                sleep(5)
                if i == 2:
                    raise Exception("On Store failed to open catalog assigments folder")
        current_folder_page = store_driver.find_element(By.XPATH,"//div[contains(@class,'x-panel x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        current_folder_page.find_element(By.NAME, "query").clear(); sleep(0.5)
        current_folder_page.find_element(By.NAME, "query").send_keys(str(ca_id)); sleep(1)
        current_folder_page.find_element(By.NAME, "query").send_keys(Keys.ENTER)
        sleep(1)
        cat_assigment = current_folder_page.find_element(By.XPATH, f"//div[text()='{ca_id}']")
        actions = ActionChains(store_driver)
        actions.double_click(cat_assigment).perform()
        # need to handle where the double click may not open the catalog assigment
        for i in range(3):
            try:
                element_modified_pop_up(store_driver, 3)
                ## need to see if the page is displayed
                sleep(2) ### this can be lowereed if perfomance improves
                current_page = store_driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
                id_str = current_page.find_element(By.XPATH,".//div[contains(@class,'x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default')]").text
                ID = "".join(char for char in id_str if char.isdigit())
                assert ID == str(ca_id), "Correct catalog assigment is not opened for the store"
                break
            except Exception as e:
                logger.info("Double clicking failed to open the catalog assigment in try: %s", i+1)
                logger.info("Error: %s", e)
                try:
                    cat_assigment = current_folder_page.find_element(By.XPATH, f"//div[text()='{ca_id}']")
                    actions.double_click(cat_assigment).perform()
                    print("Opening the catalog assigment was succesfull in the retry")
                except:
                    print("Opening the catalog assigment failed...")    
                if i == 2:
                    raise Exception("failed to open the catalog assigment after 3 retries %s", e)
        
        if products_skus:
            logger.debug("verifying all (skus) products in exist in store view verify_products_in_catalog_assigment %s", products_skus)
            sleep(2)
            verify_products_in_catalog_assigment(store_driver, products_skus, airline_view=False )
        else: 
            logger.info("There wasn't a list of skus not provided to verify Products, and product prices")
    except Exception as e:
        capture_failed_details(store_driver, "store_open_catalog_assigment", e )
        
    
def Enter_CA_price(driver, sku, all_currencies_configured:list, currency="AUD", price="99", currency_offset=0):
    try:
        wait = WebDriverWait(driver, 30)
        all_elements_with_sku = wait.until( EC.visibility_of_any_elements_located((By.XPATH, f'//div[text()="{sku}"]'))) #failed once march 26
        sku_elemet = [elem for elem in all_elements_with_sku if elem.is_displayed()]
        parent_cell_element = sku_elemet[0].find_element(By.XPATH, '../..')
        index = all_currencies_configured.index(currency) + 3 + currency_offset 
        cell = parent_cell_element.find_element(By.XPATH,f'.//td[{index}]')
        actions = ActionChains(driver)
        driver.execute_script("arguments[0].scrollIntoView();", cell)
        actions.double_click(cell).pause(0.5).send_keys(price).perform()
    except Exception as e:
        capture_failed_details(driver, "Enter_CA_price", e )

def store_CA_approve_for_association(driver, autoApprove=False):
    if autoApprove == False:
        driver.implicitly_wait(10)
        current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
        sleep(0.5)
        Action_button = current_page.find_element(By.XPATH,".//a[contains(@class,'x-btn pimcore_workflow_button x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-medium')]") 
        driver.execute_script("arguments[0].scrollIntoView();", Action_button)
        Action_button.click()
        approve_button  = driver.find_element(By.XPATH, '(//span[text()="Approve For Association"])')
        driver.execute_script("arguments[0].scrollIntoView();", approve_button)
        approve_button.click()
        sleep(3)


    # actions.context_click(catalogAssigment_button).perform()
    
def create_catalog_helper(driver, track_product_input_data_map, track_catalog_input_data, session=None, Catalog_save_timer=None):
    productIDs = []
    skus = []
    for id, product in track_product_input_data_map.items():
        productIDs.append(id)
        skus.append(product["sku"]) 
    
    # productIDs = ["444", "443", "442", "441"] # Test from pac-env
    # skus = ["sku_price&specialPrics_configurablefew_currencies__2024_03_20__21_39_27", "sku_price_configurablefew_currencies__2024_03_20__21_39_27", "sku_all_price&SpecialPrice_few_currencies__2024_03_20__21_39_27", "sku_all_prices_only_few_currencies__2024_03_20__21_39_27"]
    
    cat_name, catalog_id = create_catalog(driver, productIDs=productIDs, timer_arr=Catalog_save_timer)
    logger.debug('the catalog name'+ cat_name)
    logger.debug("the catalog id:"+ str(catalog_id))
    cat_json =  {
            "name": cat_name, 
            "products": skus
            }
    catalog_save_and_publish(driver, close_tab_on_save=True)
    track_catalog_input_data[str(catalog_id)] = cat_json
    
    temp_get_comp = { "id": int(catalog_id),
                       "name": cat_name, 
                     "products": [int(i) for i in productIDs ]}
    if session:
        sleep(5)
        catalog_response = get_catalog(session=session, id=catalog_id)
        assert catalog_response.status_code == 200
        assert compare_input_to_expected_data(catalog_response.json()['data']["catalog"] ,temp_get_comp)

def detect_4_decimals_popup(driver):
    sleep(1)  ## the below may need to change to wait element visibility_of_element
    driver.find_element(By.XPATH, f"//div[contains(text(),'Allowed up to 3 decimal places for')]").is_displayed()
    elements = driver.find_elements(By.XPATH, '//span[text()="OK"]')  ### This element is hard to find
    visible_elements = [element for element in elements if element.is_displayed()]
    if visible_elements:
        visible_elements[-1].click()
    else:
        sleep(1)
        driver.find_elements(By.XPATH, '//span[text()="OK"]')[-1].click()
        logger.critical("it was not able to find a visible OK Button, Tried again and clicked the last button")
    driver.implicitly_wait(10)

def detect_PTS_decimals_pop_up(driver):
    sleep(1)  ## the below may need to change to wait element visibility_of_element
    driver.find_element(By.XPATH, f"//div[contains(text(),'Allowed up to 0 decimal places for')]").is_displayed()
    # driver.find_element(By.XPATH, '(//span[text()="OK"])').click()
    elements = driver.find_elements(By.XPATH, '//span[text()="OK"]')  ### This element is hard to find
    visible_elements = [element for element in elements if element.is_displayed()]
    if visible_elements:
        visible_elements[-1].click()
    else:
        sleep(1)
        driver.find_elements(By.XPATH, '//span[text()="OK"]')[-1].click()
        logger.critical("it was not able to find a visible OK Button, Tried again and clicked the last button")
    driver.implicitly_wait(10)

def CA_save(driver, verify_succefull_save=True, timer_arr=None):
    try:
        current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
        current_page.find_element(By.XPATH, './/span[text()="Save"]').click()
        if verify_succefull_save:
            start_time = time.time()
            try:
                assert WebDriverWait(driver, 160).until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
                sleep(11) ### nedds to be remove
                end_time = time.time() 
                if timer_arr is not None:
                    save_time = end_time-start_time
                    logger.debug("CA Save the timer is " + str(save_time))
                    pytest.assume(save_time<30, f"The Catalog Assigment took more than 10 seconds to save... Total save time: {str(save_time)}")
                    timer_arr.append(save_time) 
            except Exception as e:
                end_time = time.time() 
                logger.info("It failed to find saved succesfully after 90 seconds. The value of time from timer fuction: " + str(end_time-start_time))
                assert WebDriverWait(driver, 50).until(EC.presence_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
                end_time2 = time.time() 
                if timer_arr is not None:
                    timer_arr.append(end_time2-start_time) 
                logger.info("Found saved succesfully with precence_of_element located ")
            sleep(3)
    except Exception as e:
        capture_failed_details(driver, "CA_save", e )

def sku_random_prices_for_all_currencies(store_driver, sku, all_currencies_configured, track_product_input_data_map, id=62, currency_offset=0):
    product_CA_obj= {}
    if track_product_input_data_map:
        product_CA_obj['id'] = find_product_id(track_product_input_data_map, sku)
        product_CA_obj.pop('id', "") if 'id' in product_CA_obj and product_CA_obj['id'] is None else None
    # product_CA_obj['productId'] = id ## needs to be commented
    product_CA_obj['productPriceList'] = []

    for i in all_currencies_configured:  ## for single product
        if i != "PTS":
            random_price_value = round(random.uniform(1, 100000.999), random.randint(0, 3))
        else:
            random_price_value = random.randint(10, 440000000)
        Enter_CA_price(store_driver, sku, all_currencies_configured=all_currencies_configured, currency=i, price=str(random_price_value), currency_offset=currency_offset )
        print("entered ", random_price_value, "  for currency: ", i)
        # if i == "USD": 
        #     product_CA_obj['productPrice'] = random_price_value
        product_CA_obj['productPriceList'].append({
            "value": random_price_value,
            "currency": i
        })
    return product_CA_obj
    
    
def find_product_id(product_data_map, sku):
    for parent_key, product in product_data_map.items():
        if product['sku'] == sku:
            return parent_key
        
def product_sns_convert(prouct_data, api_data=False):  
    new_list = copy.deepcopy(prouct_data)
    for i in new_list:
        logger.debug("in for loop for converting product to sns to CA.. i:")
        logger.debug(str(i))
        if "productPriceList" in i:
            i['price'] = i["productPriceList"]
        i.pop("productPriceList", "")
        i.pop("productPrice", "")
        if api_data:
            if "productId" in i:
                i['id'] = i['productId']
                i.pop("productId",'')
        # i.pop("id", "")  ### unless we can track ID tracking...
    logger.debug("product_sns_convert- the converted product to SNS :\n %s", json.dumps(new_list, indent=3))
    return new_list
    
def upload_csv_product(driver, test_filename, category="Beverages"):
    driver.implicitly_wait(20)
    driver.find_element(By.ID, 'pimcore_menu_massupdate').click()
    driver.find_element(By.XPATH, '//span[text()="CSV Import"]').click()  
    driver.find_element(By.XPATH, '//div[@id="importType-trigger-picker"]').click()
    driver.find_element(By.XPATH,'//li[text()="Products"]').click()
    sleep(0.5)
    #driver.find_element(By.ID, 'storeList-trigger-picker').click()
    driver.find_element(By.XPATH,'//*[@id="categoryField-listWrapper"]').click()  #this isnt bringing the categories up
    try:
        driver.find_element(By.XPATH, f"//li[contains(text(),'{category}')]").click()
    except:
        try:
            logger.critical("clicking the category dropdown failed the click time... Trying again after a second")
            sleep(2)
            driver.find_element(By.XPATH,'//*[@id="categoryField-listWrapper"]').click()
            driver.find_element(By.XPATH, f"//li[contains(text(),'{category}')]").click()
        except:
            logger.critical("clicking the category dropdown failed the click time... Trying again after a third")
            sleep(3)
            driver.find_element(By.XPATH,'//*[@id="categoryField-listWrapper"]').click()
            driver.find_element(By.XPATH, f"//li[contains(text(),'{category}')]").click()
    sleep(0.5)
    driver.find_element(By.XPATH, '//input[@name="csvFile"]').send_keys(test_filename)
    sleep(0.5)
    driver.find_element(By.XPATH, '//span[text()="Upload"]').click()


def open_sns_payload(directory, id):
    sleep(3)
    while True:  
        files = fnmatch.filter(os.listdir(directory), "*"+str(id)+"*")
        if files:
            file_sns = files[0]
            break
        logger.debug("the list of file in for")
        logger.debug(os.listdir(directory))
        sleep(1)

    full_path_file_sns = os.path.join(directory, file_sns  )
    logger.debug("the full path: %s", full_path_file_sns)
    with open(full_path_file_sns, 'r', encoding='utf-8') as file_contents:
        data = json.load(file_contents) 
    logger.debug('the contents:')
    logger.debug(json.dumps(data, indent=3))
    
    backup_dir = os.path.join(directory, "backup")
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    backup_file_path = os.path.join(backup_dir, file_sns)
    shutil.move(full_path_file_sns, backup_file_path)
    return data
    
    
def currencies_validation(store_driver):   
    assert WebDriverWait(store_driver, 50).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Validation failed: Value required for all currencies')]"))).is_displayed(), "there was an error detecting the pop-up"
    elements = store_driver.find_elements(By.XPATH, '//span[text()="OK"]')  ### This element is hard to find
    visible_elements = [element for element in elements if element.is_displayed()]
    if visible_elements:
        visible_elements[-1].click()
    else:
        sleep(1)
        store_driver.find_elements(By.XPATH, '//span[text()="OK"]')[-1].click()
        logger.critical("it was not able to find a visible OK Button, Tried again and clicked the last button")
    
def all_currencies_pop_up_helper(driver, currencies_arr, product_save_timer, prod_input_data, vefierType="price", verify_save_succesfully=True, close_on_save=True):
    for i, value in enumerate(currencies_arr):
        if vefierType == "price":
            if i == len(currencies_arr)-1 and verify_save_succesfully:
                logger.debug("in the last index")
                enter_product_price( driver, currency=value, price='random', prod_input_data=prod_input_data)
                product_save_and_publish(driver, timer_arr=product_save_timer, close_tab_on_save=close_on_save)
            else:
                enter_product_price( driver, currency=value, price='random', prod_input_data=prod_input_data)
                product_save_and_publish(driver, timer_arr=product_save_timer, save_succesfull_check=False, close_tab_on_save=False)
                currencies_validation(driver)
        elif vefierType == "specialPrice":
            if i == len(currencies_arr)-1 and verify_save_succesfully:
                logger.debug("in the last index")
                enter_product_price( driver, currency=value, special_price='random', prod_input_data=prod_input_data)
                product_save_and_publish(driver, timer_arr=product_save_timer, close_tab_on_save=close_on_save)
            else:
                enter_product_price( driver, currency=value, special_price='random', prod_input_data=prod_input_data)
                product_save_and_publish(driver, timer_arr=product_save_timer, save_succesfull_check=False, close_tab_on_save=False)
                currencies_validation(driver)
        elif vefierType == "both":
            if i == len(currencies_arr)-1 and verify_save_succesfully:
                logger.debug("in the last index")
                enter_product_price( driver, currency=value, price='random', special_price='random', prod_input_data=prod_input_data)
                product_save_and_publish(driver, timer_arr=product_save_timer, close_tab_on_save=close_on_save)
            else:
                enter_product_price( driver, currency=value, price='random', special_price='random', prod_input_data=prod_input_data)
                product_save_and_publish(driver, timer_arr=product_save_timer, save_succesfull_check=False, close_tab_on_save=False)
                currencies_validation(driver)




def catalog_assigment_default_currency_validation(driver):
    assert WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Default currency (USD) is required when other currencies have a value')]"))).is_displayed(), "there was an error detecting the pop-up"
    elements = driver.find_elements(By.XPATH, '//span[text()="OK"]')  ### This element is hard to find
    visible_elements = [element for element in elements if element.is_displayed()]
    if visible_elements:
        visible_elements[-1].click()
    else:
        sleep(1)
        driver.find_elements(By.XPATH, '//span[text()="OK"]')[-1].click()
        logger.critical("it was not able to find a visible OK Button, Tried again and clicked the last button")
        
        
        

def product_csv_to_json(file_path):
    json_array = []  # Initialize an empty list to hold JSON objects
    with open(file_path, mode='r', encoding='utf-8') as csv_file:
        reader = csv.reader(csv_file)
        csv_headers = next(reader)  # Reads the first line, which is the header

        dict_reader = csv.DictReader(csv_file, fieldnames=csv_headers)

        for row in dict_reader:
            json_data = {}
            prices = []
            specialPrices = []
            is_taxable_included = False; minQuantityAllowed = False; maxQuantityAllowed = False 
            
            if "name_en" in row and 'fails' in row['name_en'].lower():
                continue

            for header in csv_headers:
                if row[header]:  # Only process non-empty fields
                    key = header.split("_")[0]  # General approach for the key, before the first underscore
                    
                    if header in ["spotLight", "ageVerificationRequire", "isAvailableToSell", "requireShipping"]:
                        json_data[key] = row[header] == "1"
                        logger.debug(f"A {header}field found and its: {json_data[key]}... the csv: {row[header]}")

                    elif header in ["sku", "barcode", "productType", "productSet", "notApplicableCountries", "newFrom", "newTo", "isvariantDefault", "isPerishable"]:
                        json_data[key] = row[header]

                    elif header in ["name_en", "description_en", "brand_en", "shortDescription_en"]:
                        json_data[key] = row[header]

                    elif header == "minQuantityAllowed/Order":
                        minQuantityAllowed = True
                        minAmount = int(row[header])
                    elif header == "maxQuantityAllowed/Order":
                        maxQuantityAllowed = True
                        maxAmount = int(row[header])
                    elif "price_" in header:
                        currency = header.split("_")[-1]
                        prices.append({"value": float(row[header]), "currency": currency})

                    elif "specialPrice_" in header:
                        currency = header.split("_")[-1]
                        specialPrices.append({"value": float(row[header]), "currency": currency})

                    elif header.startswith("image") and row[header]:
                        if "assets" not in json_data:
                            json_data["assets"] = {"images": [{}], "gallery": []}
                        json_data["assets"]["images"][0][header] = row[header]

                    elif header == "gallery":
                        json_data["assets"] = {}
                        json_data["assets"]["gallery"] = row[header].split(",")

                    elif header == "isTaxable":
                        is_taxable_included = True
                        is_taxable = row[header] == "1"
                        logger.debug(f"an isTaxable field found and its: {is_taxable}... the csv: {row[header]}")

                    # elif header in ["weight", "shippingLength", "shippingWidth", "shippingHeight"]:
                    #     if "shippingAttributes" not in json_data:
                    #         json_data["shippingAttributes"] = {"weightUnit": "KG", "heightUnit": "CM", "widthUnit": "CM", "lengthUnit": "CM"}
                    #     dimension_type = key if key != "shippingLength" else "length"
                    #     json_data["shippingAttributes"][dimension_type] = float(row[header])

            if prices or specialPrices or is_taxable_included or minQuantityAllowed or maxQuantityAllowed:
                price_taxation = {}
                if prices:
                    price_taxation["price"] = prices
                if specialPrices:
                    price_taxation["specialPrice"] = specialPrices
                if is_taxable_included:
                    price_taxation["isTaxable"] = is_taxable 
                    # v=json_data["sku"]
                    # logger.debug(f"{v} ... Writing an isTaxable field: {is_taxable} ")
                if minQuantityAllowed:
                    price_taxation["orderMinQuantityAllowed"] = minAmount
                if maxQuantityAllowed:
                    price_taxation["orderMaxQuantityAllowed"] = maxAmount
                json_data["PriceTaxation"] = price_taxation
            if json_data:
                json_array.append(json_data)
    return json_array

def csv_ca_modify_price_helper(driver,download_path, skus, currencies_list, sku_price_exlussions={} ):    
    full_path_file = csv_ca_export(driver, download_path)
    csv_CA_generate_test_data(full_path_file, skus, currencies_list, sku_price_exlussions)
    p = convert_csvToJSON_CA_prices(full_path_file)
    csv_ca_upload(driver, full_path_file)
    return p

def csv_ca_export(driver, download_path):
    try:
        driver.find_element(By.XPATH, '//span[text()="Export CSV"]/..').click()
    except Exception as e:
        logger.critical("Error finding the download button")
        capture_failed_details(driver, "csv_ca_export", e )
    sleep(10) ## allow thte download to happen
    logger.debug("download path was provided. Returning the json payload")
    while True:  
        files = fnmatch.filter(os.listdir(download_path), "*CurrencyPrice*")
        if files:
            file_sns = files[0]
            logger.debug("found a file matching the name description ")
            break
        else:
            logger.debug("No files found")
    logger.debug("the list of file in for")
    logger.debug(os.listdir(download_path))
    sleep(0.5)
    full_path_file = os.path.join(download_path, file_sns  )
    logger.debug("Full path: %s", full_path_file) 
    return full_path_file
    
def csv_ca_upload(driver, full_path_file):
    try:
        driver.find_element(By.XPATH, '//span[text()="Import CSV"]/..').click()  
        sleep(0.5)
        driver.find_element(By.XPATH, '//input[@name="pricefile"]').send_keys(full_path_file)
        sleep(0.5)  
        driver.find_element(By.XPATH, '//span[text()="Upload"]').click()
        sleep(1)
        WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, '//span[text()="Close & Reload"]'))).click()
        unexpected_pop_up_detected = True
        try:  ### code below could be removed  
            ##check if the pop-up is displayed  
            print("checking if modified CA is diplayed")
            wait = WebDriverWait(driver, 5)
            wait.until( EC.visibility_of_any_elements_located((By.XPATH, f"//div[contains(text(),'All unsaved changes will be lost, are you really sure?')]")))    
            print("found the text fro the pop-up")
            current_page = driver.find_element(By.XPATH,"//div[@class='x-panel pimcore_class_CatalogAssignment x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']")
            current_page.find_element(By.XPATH, '(//span[text()="Yes"])').click()
            unexpected_pop_up_detected = False
            print("clicked yes")
        except:
            print("The pop-up was not displayed or failed to click yes")
        pytest.assume(unexpected_pop_up_detected, "MKTPL-8296 - unexpected modifed element pop-up was displayed") # 
        
    except Exception as e:
        capture_failed_details(driver, "csv_ca_upload", e )
    sleep(4)
    logger.debug("The CSV contents before its deleted:")
    with open(full_path_file, mode='r', encoding="utf-8") as file: 
        reader = csv.reader(file)
        for row in reader:
            logger.debug(",".join(row)) 
    os.remove(full_path_file)
    logger.debug(f"CSV File in {full_path_file} is removed")
    

def csv_CA_generate_test_data(full_path_file, skus, currencies_list, sku_price_exlussions={}):
    updated_data = []
    #read the existing rows,and generated test values
    with open(full_path_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        fieldnames = reader.fieldnames
        for row in reader:
            sku = row['SkuCode']
            for currency in currencies_list:
                currency_field =  f'price_{currency.upper()}'
                if currency_field in fieldnames and sku in skus:
                    if not (sku in sku_price_exlussions and currency in sku_price_exlussions[sku]):
                    # if current_value not in sku_price_exlussions.get(sku, []):
                        if "PTS" in currency_field:
                            rand_value = str(random.randint(10, 10000000))
                        else: 
                            rand_value = str(round(random.uniform(50, 100000.999), random.randint(0, 3))) #if price == "random" else price
                        row[currency_field] = rand_value
            updated_data.append(row)
    with open(full_path_file, 'w', newline='', encoding='utf-8') as file: #write the same file
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(updated_data)
    logger.info("Completed generating data in the TEST CSV: %s ", full_path_file)
        
def convert_csvToJSON_CA_prices(full_path_file):
    products = []
    with open(full_path_file, 'r', encoding='utf-8') as file_contents:
        reader = csv.DictReader(file_contents)
        for row in reader:
            product = {}
            product["sku"] = row['SkuCode']
            price_list = []
            
            for field in reader.fieldnames:
                if field.startswith("price_"):
                    currency = field.split("_")[1].upper()
                    price_str = row[field]
                    if price_str:
                        price = float(row[field])
                        price_list.append ({"currency": currency, "value": price})
                    else:
                        logger.debug(f"Price field for {currency} is empty. Not adding to the prices array")
            if len(price_list) > 0:
                product['price'] = price_list
            products.append(product)
        # data = json.load(products) 
    logger.debug('All the products contents:')
    logger.debug(json.dumps(products, indent=3))
    return products

def route_catalog_transform(product_list):
    new_list = copy.deepcopy(product_list)
    for i in new_list:
        if "id" in i:  
            i['productId'] = i.pop("id")
        i.pop("sku", "")
        if "price" in i:
            i['productPriceList'] = i.pop("price")
    return new_list

def check_succesfull_login(driver, wait_time=90, timer_arr=None, start_time=time.time(), user=""):
    LOGIN_PAGE_RETRIES_ENABLED = os.getenv("LOGIN_PAGE_RETRIES_ENABLED", "")
    if LOGIN_PAGE_RETRIES_ENABLED == "1" or LOGIN_PAGE_RETRIES_ENABLED.lower() == "yes" or LOGIN_PAGE_RETRIES_ENABLED.lower() == "true" or LOGIN_PAGE_RETRIES_ENABLED.lower() == "y":  
        maxRetries = 5
    else:
        maxRetries = 1
    for i in range(maxRetries):
        try:
            act_title = WebDriverWait(driver, wait_time).until(EC.visibility_of_element_located((By.XPATH, "//a[@id='pimcore_logout']"))).get_attribute("id")
            assert act_title == "pimcore_logout"
            logger.debug("Login succesfull")
            if timer_arr is not None:
                end_time = time.time()
                logger.info("Ended the timer: %s", end_time)
                save_time = end_time-start_time
                logger.debug("Login timer is " + str(save_time))
                pytest.assume(save_time<20, f"{user} user took more than 20 seconds to login... Total save time: {str(save_time)}")
                timer_arr.append(save_time) 
            break
        except Exception as e:
            logging.info("ERR: Failed to get the logged in page in try:" + str(i))
            logger.debug(e)
            end_time = time.time()
            save_time = end_time-start_time
            driver.save_screenshot("failed_"+user+"_driver_login_failure_try_"+str(i)+"_"+str(save_time)+"_sec"+ ".png")
            sleep(0.5)
            driver.refresh()
            start_time = time.time() # reset the timer
            if i+1 == maxRetries:
                logger.info("There was an issue loggin in... Please check credentials")
                raise



