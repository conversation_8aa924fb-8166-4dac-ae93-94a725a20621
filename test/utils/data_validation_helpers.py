import os, pytest
import json
import tarfile
import xml.etree.ElementTree as ET
from pathlib import Path
import fnmatch
import logging
from typing import List, Dict, Any, Tuple
from bson import BSON
from bson.errors import BSONError
import boto3
import sys
from operator import itemgetter
import re

def load_data(file_path):
    data = load_json_file(file_path)
    if not data:
        pytest.fail(f"Failed to load {file_path}")
    return data


def load_json_file(file_path):
    """Load a JSON file and return its contents."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return None
    except json.JSONDecodeError:
        return None

def extract_tgz(tgz_file, extract_dir):
    """Extract a TGZ file to the specified directory."""
    os.makedirs(extract_dir, exist_ok=True)
    try:
        with tarfile.open(tgz_file, 'r:gz') as tar:
            tar.extractall(path=extract_dir)
        return True
    except (FileNotFoundError, tarfile.ReadError):
        return False

def extract_inner_tgz_files(extract_dir):
    success_count = 0
    total_count = 0

    for inner_tgz in Path(extract_dir).glob('*.tgz'):
        total_count += 1
        try:
            with tarfile.open(inner_tgz, 'r:gz') as inner_tar:
                inner_tar.extractall(path=extract_dir)  # Extract directly to extract_dir
            success_count += 1
        except (FileNotFoundError, tarfile.ReadError) as e:
            print(f"Error extracting {inner_tgz}: {e}")
            continue
    return success_count, total_count

def parse_xml_file(xml_path):
    """Parse an XML file and return its root element."""
    try:
        tree = ET.parse(xml_path)
        return tree.getroot()
    except (FileNotFoundError, ET.ParseError):
        return None

def get_nbdManifest_for_product(extract_dir, baseFilename, AIRLINE_CODE):
    """Get the manifest file for a product."""
    actual_path = os.path.join(extract_dir, AIRLINE_CODE, "components", baseFilename)
    if os.path.exists(actual_path):
        return parse_xml_file(actual_path)
    print(f"Manifest file does not exist... '{actual_path}' ")
    return None


def get_nbdManifest_category(extract_dir, id, AIRLINE_CODE):
    """Get the manifest file for a product."""
    actual_path = os.path.join(extract_dir, AIRLINE_CODE, "categories", f"CAT{id}", "nbdManifest_CAT"+id+".xml")
    if os.path.exists(actual_path):
        return parse_xml_file(actual_path)
    print(f"Manifest file does not exist... '{actual_path}' ")
    return None



def check_image_path_in_nbd_manifest(manifest_root, imagePath, path_to_remove=None):
    """Check if an image URL is referenced in a manifest."""
    if manifest_root is None:
        return False
    if path_to_remove:
        imagePath = imagePath.replace(f"{path_to_remove}", "")
    else: 
        imagePath=imagePath

    for component in manifest_root.findall('.//component'):
        name = component.get('name', '')
        # print(f"Checking path {imagePath}")
        # print(f"Name: {name}")
        if imagePath == name:  #### Need to modify the search to compare exact text
            # print(f"found exact file in 'name' {name}" )
            return True
    return False

def get_essential_manifest(extract_dir, AIRLINE_CODE="00000WN5"):
    """Get the essential manifest file"""
    manifestPath = os.path.join(extract_dir, AIRLINE_CODE, f"nbdManifest_essential.xml")
    if os.path.exists(manifestPath):
        return parse_xml_file(manifestPath)
    print("Manifest file does not exist..")
    return None


def get_filter_ids_from_catalog_assignments(catalog_assignments):
    """Get all filter IDs from catalog assignments."""
    filter_ids = set()
    for assignment in catalog_assignments:
        filter_ids.add(assignment['id'])
    return filter_ids

def get_product_ids_from_catalog_assignments(catalog_assignments):
    """Get all product IDs from catalog assignments."""
    product_ids = set()
    for assignment in catalog_assignments:
        if 'catalog' in assignment and 'products' in assignment['catalog']:
            for product in assignment['catalog']['products']:
                product_ids.add(product['productId'])
    # logger.info(f"Found {len(product_ids)} product IDs in catalog assignments")
    return product_ids

def get_category_ids_from_catalog_assignments(catalog_assignments):
    """Get all category IDs from catalog assignments."""
    category_ids = set()
    for assignment in catalog_assignments:
        if 'routeSequences' in assignment and 'categories' in assignment['routeSequences']:
            for category in assignment['routeSequences']['categories']:
                category_ids.add(category['id'])

                # Also include subcategories if present
                if 'subCategories' in category:
                    for subcategory in category['subCategories']:
                        category_ids.add(subcategory['id'])

                        # Include sub-subcategories if present
                        if 'subCategories' in subcategory:
                            for subsubcategory in subcategory['subCategories']:
                                category_ids.add(subsubcategory['id'])
    return category_ids

def get_sector_ids_from_catalog_assignments(catalog_assignments):
    """Get all sector IDs from catalog assignments."""
    sector_ids = set()
    for assignment in catalog_assignments:
        if 'routeGroup' in assignment and 'sector' in assignment['routeGroup']:
            for sector_entry in assignment['routeGroup']['sector']:
                sector_ids.add(sector_entry['sectorId'])
    return sector_ids


def get_specialty_attribute_ids_from_products(products):
    """Get all specialty attribute IDs from products."""
    attr_ids = set()
    for product_id, product_data in products.items():
        if ('specialityAttributes' in product_data and
            product_data['specialityAttributes']):
            for attr in product_data['specialityAttributes']:
                if 'id' in attr and attr['id']:
                    attr_ids.add(attr['id'])
    return attr_ids

def get_image_urls_from_products(products):
    """Get all image URLs from products."""
    image_urls = set()
    for product_id, product_data in products.items():
        if 'assets' in product_data and 'images' in product_data['assets']:
            for image_dict in product_data['assets']['images']:
                for image_type, image_url in image_dict.items():
                    if image_url and image_url.startswith('http'):
                        image_urls.add(image_url)

        if 'assets' in product_data and 'gallery' in product_data['assets']:
            for gallery_item in product_data['assets']['gallery']:
                if 'url' in gallery_item and gallery_item['url'] and gallery_item['url'].startswith('http'):
                    image_urls.add(gallery_item['url'])
    return image_urls

def get_image_urls_from_categories(categories):
    """Get all image URLs from categories."""
    image_urls = set()
    for category_id, category_data in categories.items():
        if 'images' in category_data:
            for image_dict in category_data['images']:
                for image_type, image_url in image_dict.items():
                    if image_url and image_url.startswith('http'):
                        image_urls.add(image_url)

        for img_type in ['bannerImage', 'backgroundImage']:
            if img_type in category_data and category_data[img_type] and category_data[img_type].startswith('http'):
                image_urls.add(category_data[img_type])

        # Also check subcategories
        if 'subCategory' in category_data:
            for subcategory in category_data['subCategory']:
                if 'images' in subcategory:
                    for image_dict in subcategory['images']:
                        for image_type, image_url in image_dict.items():
                            if image_url and image_url.startswith('http'):
                                image_urls.add(image_url)

                for img_type in ['bannerImage', 'backgroundImage']:
                    if img_type in subcategory and subcategory[img_type] and subcategory[img_type].startswith('http'):
                        image_urls.add(subcategory[img_type])

                # Check sub-subcategories
                if 'subCategory' in subcategory:
                    for subsubcategory in subcategory['subCategory']:
                        if 'images' in subsubcategory:
                            for image_dict in subsubcategory['images']:
                                for image_type, image_url in image_dict.items():
                                    if image_url and image_url.startswith('http'):
                                        image_urls.add(image_url)

                        for img_type in ['bannerImage', 'backgroundImage']:
                            if img_type in subsubcategory and subsubcategory[img_type] and subsubcategory[img_type].startswith('http'):
                                image_urls.add(subsubcategory[img_type])
    return image_urls

def get_image_urls_from_specialty_attributes(specialty_attributes):
    """Get all image URLs from specialty attributes."""
    image_urls = set()
    for attr_id, attr_data in specialty_attributes.items():
        if 'Image' in attr_data and attr_data['Image'] and attr_data['Image'].startswith('http'):
            image_urls.add(attr_data['Image'])
    return image_urls



def find_and_load_json_files(root_dir, pattern):
    """
    Search for files matching the pattern in the root directory and its subdirectories,
    then load and return JSON data from those files.
    
    Args:
        root_dir (str): The root directory to search in
        pattern (str): The filename pattern to match (case insensitive)
    
    Returns:
        list: A list of loaded JSON data from matching files
    """
    json_data_list = []
    
    # Walk through the directory tree
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # Find files that match the pattern (case insensitive)
        for filename in fnmatch.filter(filenames, f"*{pattern}*"):
            # Check if the file has a .json extension (case insensitive)
            if filename.lower().endswith('.json'):
                file_path = os.path.join(dirpath, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        # Load the JSON data from the file
                        data = json.load(f)
                        # Add the data to our list
                        json_data_list.append(data)
                        # print(f"Loaded JSON from: {file_path}")
                except json.JSONDecodeError:
                    print(f"Error: {file_path} is not a valid JSON file")
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    return json_data_list



def find_and_load_bson_files(root_dir, pattern):
    """
    Search for files matching the pattern in the root directory and its subdirectories,
    then load and return BSON data from those files.
    
    Args:
        root_dir (str): The root directory to search in
        pattern (str): The filename pattern to match (case insensitive)
    
    Returns:
        list: A list of loaded BSON data from matching files
    """
    bson_data_list = []
    
    # Walk through the directory tree
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # Find files that match the pattern (case insensitive)
        for filename in fnmatch.filter(filenames, f"*{pattern}*"):
            # Check if the file has a .bson extension (case insensitive)
            if filename.lower().endswith('.bson'):
                file_path = os.path.join(dirpath, filename)
                try:
                    with open(file_path, 'rb') as f:  # Use binary mode for BSON
                        # Load the BSON data from the file
                        data = BSON(f.read()).decode()  # Read and decode BSON
                        # Add the data to our list
                        bson_data_list.append(data)
                        # print(f"Loaded BSON from: {file_path}")
                except BSONError:
                    print(f"Error: {file_path} is not a valid BSON file")
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    return bson_data_list


def validate_product_images2(
    payloadData: Dict[str, Any],
    required_selectors: List[str] = ["15", "20", "25", "30"],
    extracted_data_path: str = "extracted_data",
    component_id: str ="",
    AIRLINE_CODE:str="",
    image_types={},
    image={}
) ->  List[str]:
    """
    Helper function to validate product images in a payload:
    1. Checks if all product-image-X entries exist (up to required_image_count)
    2. Verifies each product-image has all required_selectors
    3. For the first imageCount images, checks if the files exist on disk
    
    Args:
        payloadData: The payload data containing picture object
        imageCount: Number of images that should actually exist on disk
        required_selectors: List of selectors that must exist for each image (default: ["15", "20", "25", "30"])
        extracted_data_path: Path to directory where image files should be found
        required_image_count: Total number of product-image entries that should exist (default: 6)
        
    Returns:
        errors
    """
    errors = []
    
    # Make sure we have a picture object in the payload

    if image['type'] not in image_types:
        # print(f"Found image not applicable for configuration '{image['type']}'. Skipping")
        return
    image_type = image['type']

    if "images" not in payloadData:
        errors.append("Missing 'picture' object in the payload coming from the kit...")
        return  errors
    
    images = payloadData["images"]
    
    for image_key in image_types[image_type]:
        # print("the image key")
        # print(image_key)
        if image_key not in images:
            errors.append(f"Missing {image_key} in images object, id {component_id} Input data(SNS): \n {json.dumps(image, indent=5)} \nBSON Data in kit: {json.dumps(payloadData, indent=5)}")
            continue
        
        # Check all required key selectors exist for this image
        for selector in required_selectors:
            if selector not in images[image_key]:
                errors.append(f"Missing selector {selector} in {image_key}, id: {component_id}")
                continue
            
            # Verify there's at least one item in the selector array
            if not images[image_key][selector] or len(images[image_key][selector]) == 0:
                errors.append(f"Empty array for {image_key}.{selector}, id: {component_id}")
                continue

            manifest_root = get_nbdManifest_for_product(extracted_data_path, component_id, AIRLINE_CODE)

            if manifest_root is None:
                errors.append(f"No manifest found for product {component_id}")
                continue
            
            item = images[image_key][selector][0]
            for prop in ["filename", "height", "width"]:
                if prop not in item:
                    errors.append(f"Missing {prop} in {image_key}.{selector}, id: {component_id}")
            
            filename = item["filename"]
            file_path = os.path.join(extracted_data_path, filename)
            
            if not os.path.exists(file_path):
                error_msg = f"Image file not found: {file_path} in the kit..."
                print(error_msg)
                errors.append(error_msg)

            if check_image_path_in_nbd_manifest(manifest_root, file_path, f"extracted_{AIRLINE_CODE}/{AIRLINE_CODE}/components/"):
                pass
                # print(f"Found image succesfully in the product nbdManifest: {component_id}. {file_path}")
            else:
                print(f"Image {file_path} product not found in manifest")
                errors.append(f"Image: {file_path} \nProduct not found in manifest for componentID: {component_id}")

    return errors

def print_xml_element(element, indent=0):
    print(" " * indent + f"Tag: {element.tag}")
    if element.attrib:
        print(" " * indent + f"Attributes: {element.attrib}")
    
    if element.text and element.text.strip():
        print(" " * indent + f"Text: {element.text.strip()}")
    for child in element:
        print_xml_element(child, indent + 2)

def extract_category_ids(applicable_catalog):
    """
    Extracts all category IDs from catalog assignemtns.
    Args:
        list of catalog assigments
    Returns:
        Set of category IDs 
    """
    applicable_category_ids = set()
    
    def process_categories(categories):
        """Recursively process categories and their subcategories to extract IDs"""
        if not categories:
            return
            
        for category in categories:
            if 'id' in category:
                applicable_category_ids.add(str(category['id']))
            
            # Recursively process subcategories if they exist
            if 'subCategories' in category:
                process_categories(category['subCategories'])
    
    for assignment in applicable_catalog:
        if 'routeSequences' in assignment and 'categories' in assignment['routeSequences']:
            process_categories(assignment['routeSequences']['categories'])
    print(f"Found {len(applicable_category_ids)} category IDs in applicable catalog assignments")
    return applicable_category_ids

def extract_product_ids(applicable_catalog):
    applicable_product_ids = set()
    for assignment in applicable_catalog:
        if 'products' in assignment:
            for product in assignment['products']:
                if 'id' in product:
                    applicable_product_ids.add(str(product['id']))

    print(f"Found {len(applicable_product_ids)} products in applicable catalog assignments")
    return applicable_product_ids

def extract_spa_ids(all_proudcts_data):
    applicable_attr_ids = set()
    for product_id, product_data in all_proudcts_data.items():
        if ('specialityAttributes' in product_data and
            product_data['specialityAttributes']):
            for attr in product_data['specialityAttributes']:
                attr_id = str(attr['id'])
                applicable_attr_ids.add(attr_id)
    print(f"Found {len(applicable_attr_ids)} specialty attribute IDs in applicable products")
    return applicable_attr_ids



def check_id_exist(data, target_id):
    for spa in data:
        if spa.get('id') == target_id:
            print("checked throught parsing data...")
            return True
    return False




def validate_category_images(
    payloadData: Dict[str, Any],
    required_selectors: List[str] = ["15", "20", "25", "30"],
    extracted_data_path: str = "extracted_data",
    component_id: str ="",
    AIRLINE_CODE:str="",
    image_types={},
    image={}
) ->  List[str]:
    """
    Helper function to validate product images in a payload:
    1. Checks if all product-image-X entries exist (up to required_image_count)
    2. Verifies each product-image has all required_selectors
    3. For the first imageCount images, checks if the files exist on disk
    
    Args:
        payloadData: The payload data containing picture object
        imageCount: Number of images that should actually exist on disk
        required_selectors: List of selectors that must exist for each image (default: ["15", "20", "25", "30"])
        extracted_data_path: Path to directory where image files should be found
        required_image_count: Total number of product-image entries that should exist (default: 6)
        
    Returns:
        errors
    """
    errors = []
    
    # Make sure we have a picture object in the payload

    if image['type'] not in image_types:
        print(f"Found image not applicable for configuration '{image['type']}'. Skipping")
        return
    image_type = image['type']

    if "images" not in payloadData:
        errors.append("Missing 'picture' object in the payload coming from the kit...")
        return  errors
    
    images = payloadData["images"]
    
    for image_key in image_types[image_type]:
        # print("the image key")
        # print(image_key)
        if image_key not in images:
            errors.append(f"Missing {image_key} in images object")
            continue
        
        # Check all required key selectors exist for this image
        for selector in required_selectors:
            if selector not in images[image_key]:
                errors.append(f"Missing selector {selector} in {image_key}")
                continue
            
            # Verify there's at least one item in the selector array
            if not images[image_key][selector] or len(images[image_key][selector]) == 0:
                errors.append(f"Empty array for {image_key}.{selector}")
                continue

            manifest_root = get_nbdManifest_category(extracted_data_path, component_id, AIRLINE_CODE)

            if manifest_root is None:
                errors.append(f"No manifest found for product {component_id}")
                continue
            
            item = images[image_key][selector][0]
            for prop in ["filename", "height", "width"]:
                if prop not in item:
                    errors.append(f"Missing {prop} in {image_key}.{selector}")
            
            filename = item["filename"]
            file_path = os.path.join(extracted_data_path, filename)
            
            if not os.path.exists(file_path):
                error_msg = f"Image file not found: {file_path} in the kit..."
                print(error_msg)
                errors.append(error_msg)

            if check_image_path_in_nbd_manifest(manifest_root, file_path, f"extracted_{AIRLINE_CODE}/{AIRLINE_CODE}/categories/CAT{component_id}/"):
                pass
                # print(f"Found image succesfully in the category nbdManifest: {component_id}. {file_path}")
            else:
                print(f"Image {file_path} product not found in manifest")
                errors.append(f"Image: {file_path} category not found in manifest for componentID: {component_id}")

    return errors






def trigger_mapper_kit_generation(cluster, task_definition, airline, full_build, ECSexecutionRoleArn, ECStaskRoleArn):
    """ Trigger the ECS generation """
    def runPartialBuild(ecs_client, cluster, task_definition, subnets):
        print("Running partial bulid")
        response = ecs_client.run_task(
            cluster=cluster,
            taskDefinition=task_definition,
            launchType='FARGATE',
            networkConfiguration={
                'awsvpcConfiguration': {
                    'subnets': subnets,
                    'assignPublicIp': 'DISABLED'
                }
            },
            overrides={
                'taskRoleArn': ECStaskRoleArn,
                'executionRoleArn': ECSexecutionRoleArn,
                'containerOverrides': [
                    {
                        'name': f'{airline}-catalog-kit-gen-job',
                        'environment': [
                            {
                                'name': 'CREATE_PAYLOAD',
                                'value': '0'
                            },
                        ],
                    },
                ],

            }
        )
        print(response)

    ecs_client = boto3.client('ecs', region_name='us-west-2')
    subnets = ['subnet-06bdd5f4c7b29cfd9', 'subnet-0980925fe9fbfc427', "subnet-0dfa3156ee437ea76"] ### 3 Bs subnets for dev
        
    print("task def: ", task_definition)
    if full_build == 1:
        print("Running full build!!")
        response = ecs_client.run_task(
        cluster=cluster,
        taskDefinition=task_definition,
        launchType='FARGATE',
        networkConfiguration={
            'awsvpcConfiguration': {
                'subnets': subnets,
                    'assignPublicIp': 'DISABLED'
                }
        },
        overrides={
                'taskRoleArn': ECStaskRoleArn,
                'executionRoleArn': ECSexecutionRoleArn,
                'containerOverrides': [
                    {
                        'name': f'{airline}-catalog-kit-gen-job',
                        'environment': [
                            {
                                'name': 'FULL_BUILD',
                                'value': '1'
                            },
                            {
                                'name': 'CREATE_PAYLOAD',
                                'value': '0'
                            },
                        ],
                    },
                ],
            },
        )
        print(response)
    else:
        runPartialBuild(ecs_client, cluster, task_definition, subnets)

def extract_version(filename):
    match = re.search(r'_(\d+)\.', filename)
    if match:
        return int(match.group(1))
    return None

def get_latest_file(s3, bucket_name, prefix=''):
    """
    Get the filename of the last uploaded file in an S3 bucket.
    Args:
        bucket_name (str): Name of the S3 bucket
        prefix (str): Optional prefix to filter objects (folder path)
    Returns:
        str: Key (filename) of the most recently uploaded object
    """
    response = s3.list_objects_v2( Bucket=bucket_name, Prefix=prefix )
    if 'Contents' not in response:
        return None
    sorted_objects = sorted(
        response['Contents'],
        key=itemgetter('LastModified'),
        reverse=True
    )
    if sorted_objects:
        return sorted_objects[0]['Key']
    else:
        return None

def download_tgz_file(bucket_name, key, destination_path=None):
    """
    Download a TGZ file from S3 based on its key.
    
    Args:
        bucket_name (str): The name of the S3 bucket
        key (str): The object key (path/filename) in S3
        destination_path (str, optional): Local path where the file should be saved.
                                         If None, saves to current directory with the same filename.
    
    Returns:
        str: Path to the downloaded file
        
    Raises:
        Exception: If the download fails
    """
    s3 = boto3.client('s3', "us-west-2")
    
    if destination_path is None:
        filename = os.path.basename(key)
        destination_path = filename
    
    try:
        print(f"Downloading {key} from bucket {bucket_name} to {destination_path}...")
        s3.download_file(bucket_name, key, destination_path)
        print(f"Download complete: {destination_path}")
        return destination_path
    except Exception as e:
        print(f"Error downloading file: {e}")
        raise


def fetch_json_from_s3(s3_client, bucket_name, file_key):    
    try:
        response = s3_client.get_object(Bucket=bucket_name, Key=file_key)
        json_content = response['Body'].read().decode('utf-8')        
        data = json.loads(json_content)
        return data
    except Exception as e:
        print(f"Error fetching JSON from S3 file_key '{file_key}'. \nError: {e}")
        raise e

def getAwsPhysicalResourceId(stack_name, logical_id):
    """
    Retrieve a resource ARN from a CloudFormation stack by its logical ID
    
    Args:
        stack_name (str): The name of the CloudFormation stack
        logical_id (str): The logical ID of the TaskDefinition resource in the stack
        
    Returns:
        str: The TaskDefinition ARN
    """
    cfn_client = boto3.client('cloudformation', region_name='us-west-2')
    
    # Retrieve the physical resource ID for the specified logical ID
    response = cfn_client.describe_stack_resource(
        StackName=stack_name,
        LogicalResourceId=logical_id
    )
    arn = response['StackResourceDetail']['PhysicalResourceId']
    return arn

