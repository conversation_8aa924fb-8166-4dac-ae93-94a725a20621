import json
import random
import string
import sys
import requests
import os
from datetime import datetime
from jsonschema.exceptions import ValidationError
from time import sleep
import copy
from utils import schemas
from concurrent.futures import ThreadPoolExecutor
from typing import TypedDict, List, Optional
import jsondiff
from requests.sessions import Session
import pandas as pd
import chardet
import csv
from collections import defaultdict
from .config import *
from datetime import datetime, timedelta
import boto3, uuid, math, time
import boto3
from enum import Enum
from jsonschema.exceptions import ValidationError
from jsonschema.exceptions import SchemaError
from openapi_schema_validator import validate
from jsonschema.validators import RefResolver
from typing import Dict, Any, List, Optional, Callable, Tuple


logging.getLogger("boto3").setLevel(logging.INFO)
logging.getLogger("botocore").setLevel(logging.INFO)
logging.getLogger('faker').setLevel(logging.INFO)
logging.getLogger("urllib3").setLevel(logging.INFO)



all_currencies = ['USD', 'GBP', 'ARS', 'AUD', 'BRL', 'CAD', 'CLP', 'CNY', 'COP', 'CZK', 'DKK', 'EUR', 'HKD', 'HUF', 'ISK', 'INR', 'ILS', 'JPY', 'KRW', 'MYR', 'MXN', 'MAD', 'NZD', 'NOK', 'PHP', 'PLN', 'RUB', 'SAR', 'SGD', 'ZAR', 'SEK', 'CHF', 'TWD', 'THB', 'TRY', 'VND', 'PTS']
custumizer_url = base_url_env + "marketplace/v1/pim/product-customizers"
product_url = base_url_env + "marketplace/v1/pim/product"
route_catalog = base_url_env + "marketplace/v1/pim/route-catalog"
airports_url = base_url_env + "marketplace/v1/ams/airports"
sectors_url = base_url_env + "marketplace/v1/ams/sector"
orders_url = base_url_env + "marketplace/v1/oms/order"
speciality_attribute_url = base_url_env + "marketplace/v1/pim/specialityattributes"
brand_url = base_url_env + "marketplace/v1/pim/brand"

class label_type(TypedDict):
    es: str
    en: str
class AdditionalCharge_type(TypedDict):
    value: float
    currency: str
class customizerOptions_type(TypedDict):
    isDefaultSelected: bool
    additionalCharge: List[AdditionalCharge_type]
    label: label_type
class title_type(TypedDict):
    en: str
    es: str
class CG_type(TypedDict):
    title: title_type
    isMultiSelectable: bool
    maximumSelections: int
    required: bool
    customizerOptions: List[customizerOptions_type]

def random_price():
    return random.randrange(0,sys.float_info.max,0.01)

def get_token(user, url_env="http://127.0.0.1/", return_expiration=False, clientID=None, clientSecret=None):
    if clientID and clientSecret:
        clientId = clientID
        clientSecret = clientSecret
    elif user == "store":
        clientId = STORE_CLIENT_ID
        clientSecret = STORE_CLIENT_SECRET
    elif user == "airline":
        clientId = AIRLINE_CLIENT_ID
        clientSecret = AIRLINE_CLIENT_SECRET
    else:
        raise Exception("you didn't enter correct parameter to get the credentials")
    try:
        headers = {"Content-Type": "application/json"}
        data = {'clientId': clientId, 'clientSecret': clientSecret}
        r = requests.post(url_env + "marketplace/v1/auth/token",
                        data=json.dumps(data), headers=headers)
        logger_func("POST",url_env + "marketplace/v1/auth/token", r.status_code, r.text, data=data )
        print("the response: \n" + r.text)
        token = r.json()['data']['token']
        token = 'Bearer '+ token
        print(r.text)
    except Exception as e:
        raise RuntimeError("Authentication failed, please check your keys: \n", e)
    if return_expiration:
        return token, datetime.now() + timedelta(minutes=5)
    else:
        return token


def logger_func(func_str:str,url:str, status_code :str, response_text:str, data=None, disableResponseOutput=False):
    try:
        formatted_str = json.dumps(json.loads(response_text), indent=4, ensure_ascii=False)
    except:
        formatted_str = response_text[0:20]
        logger.critical("Unable to convert response to json format. It could be a UI response")  ### Not sure if we want to keep it
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        str_url = url.replace(base_url_env+'/', '')
        str_url = str_url.replace('http://', ''); str_url = str_url.replace('https://', '')
        str_url = str_url.replace('/', '-')
        filepath = f"response__{func_str}__{str_url}__{timestamp}.html"
        with open(filepath, "w") as file:
            file.write(response_text)
    logger.info(func_str+", URL : " +  url )
    if data is not None:
        try:
            logger.info(f"data =")
            if isinstance(data, dict):
                logger.info(json.dumps(data, indent=3, ensure_ascii=False))
            else:
                logger.info(json.dumps(json.loads(data), indent=3, ensure_ascii=False))
        except Exception as e:
            logger.warning("there was an error converting the input data into json. \nDATA: %s", e)
            logger.info(data)
    logger.info(func_str+", STATUS CODE: " + str(status_code))
    if disableResponseOutput == False:
        logger.info(func_str+", RESPONSE: "+ formatted_str)

def put_customizer_group( data:CG_type, id, random_title_price=False, session=None, random_co_count=False):
    url = custumizer_url +"/"+ str(id)
    request = session.put(url, data=json.dumps(data))
    logger_func("PUT", url,request.status_code, request.text, data=data )
    return request

def get_customizer_group(headers, id=None):
    if id is None:
        url = custumizer_url
    else:
        url = custumizer_url+"/"+str(id)
    request = requests.get(url, headers=headers)
    logger_func("GET",url, request.status_code, request.text)
    return request

def delete_customizer_group(id, session):
    url = custumizer_url +"/"+ str(id)
    request = session.delete(url)
    logger_func("DELETE", url,request.status_code, request.text )
    return request


def patch_customizer_group( data:CG_type, id, session=None):
    url = custumizer_url +"/"+ str(id)
    request = session.patc(url, data=json.dumps(data))
    logger_func("PATCH", url,request.status_code, request.text, data=data )
    return request


def post_customizer_group(headers, data:CG_type, random_title_price=False, session=None, random_co_count=False):
    if random_title_price:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        result = ''.join(random.choices(string.ascii_letters, k=5))
        data['title']['en'] += result + timestamp
        for i in data['customizerOptions']:
            if "additionalCharge" in i:
                i["additionalCharge"][0]["value"] = round(random.randint(1,100) + i["additionalCharge"][0]["value"], 2)
    if random_co_count:
        random_number = random.randint(2, min(5,len(data["customizerOptions"])))
        data["customizerOptions"] = random.sample(data["customizerOptions"], random_number)
        logger.info("the number of Customizer options: %s", str(len(data["customizerOptions"])))
    if session is not None:
        request = session.post(custumizer_url, data=json.dumps(data))
        logger.debug(" using sessions...")
    else:
        request = requests.post(custumizer_url, data=json.dumps(data), headers=headers)
        logger.debug(" using request...")
    logger_func("POST",custumizer_url, request.status_code, request.text, data)
    return request

def create_CG_succesfully(header, data, customizer_json_data, customizers_IDs, random_title=False, session=None, random_co_count=False):
    if data is None:
        data = copy.deepcopy(customizer_json_data[0])
    else:
        data = data
    post_response = post_customizer_group(header, data, random_title,  session, random_co_count)
    if post_response.status_code == 200:
        json_post_response = post_response.json()
        assert json_post_response['success'] == True
        assert "Product Customizer added successfully" in json_post_response["message"]
        ID = json_post_response['id']
        customizer_json_data.append(data);
        customizers_IDs.append(ID)
    return post_response

def post_category(headers, data=None):
    if data is None:
        f = open('json_payloads/category.json', 'r')
        data = json.load(f)
        f.close()
    else:
        data = data
    categories_url = base_url_env + 'marketplace/v1/pim/categories'
    request = requests.post(categories_url, data=json.dumps(data), headers=headers)
    logger_func("POST - CATEGORY: ", categories_url, request.status_code, request.text, json.dumps(data) )
    return request

def get_categories_store(session: Session, id=None, limit=100, params=None, formatVersion2=None, disable_api_response=False):
    categories_url = base_url_env + 'marketplace/v1/pim/categories'
    if id:
        categories_url = base_url_env +"marketplace/v1/pim/categories/" + str(id)
        if formatVersion2:
            categories_url = categories_url +"?formatVersion=2"
    elif params:
        categories_url = base_url_env +"marketplace/v1/pim/categories"
    else:
        categories_url = base_url_env +f"marketplace/v1/pim/categories?limit={limit}"
        if formatVersion2:
            categories_url = categories_url +"&formatVersion=2"
    request = session.get(categories_url)
    logger_func("GET - CATEGORY: ", categories_url, request.status_code, request.text, disableResponseOutput=disable_api_response )
    return request


def get_product(headers=None, id=None, session=None, disable_api_response=False, limit=100, params=None, formatVersion2=None):
    if id:
        product_url = base_url_env + "marketplace/v1/pim/product/" + str(id)
        if formatVersion2:
            product_url = product_url +"?formatVersion=2"
    elif params:
        product_url = base_url_env + "marketplace/v1/pim/product"
    else:
        product_url = base_url_env + f"marketplace/v1/pim/product?limit={limit}"
        if formatVersion2:
            product_url = product_url +"&formatVersion=2"



    if session:
        request = session.get(product_url)
    else:
        request = requests.get(product_url, headers=headers)
    logger_func("GET PRODUCT", product_url, request.status_code, request.text, disableResponseOutput=disable_api_response)
    return request

def put_product(session, id, data):
    product_url = base_url_env + "marketplace/v1/pim/product/"+str(id)
    request = session.put(product_url, data=json.dumps(data))
    logger_func("PUT - PRODUCT: ", product_url, request.status_code, request.text, data=json.dumps(data))
    return request

    
def post_product(headers=None, category_id=None, data=None, sku=None, name=None, additionalData=None, keys_to_remove=None, product_json_data_track=None, session=None, track_ids=None, returnData=False):
    if data is None: #if not data is passed for product, use the default product
        f = open('json_payloads/product.json', 'r')
        data = json.load(f)
        f.close()
        if sku is None:
            result = ''.join(random.choices(string.ascii_letters, k=10))
            logger.info("generating a random string for the sku: " + result)
            data['sku'] = "API_auto_"+result
            if name is None:
                result = result[:4] + " - " + str(date_string)
                logger.info("A random string for the name: " + result)
                if str(data.get('formatVersion')) == "2" :
                    logger.debug("the name is: ", data['name'])
                    for k in data['name']:
                        data['name'][k] +=  "- " + result
                else:
                    data['name'] += "- " + result
    if category_id is not None: # if something is passed for ID override it
        print("category_id is: ", category_id)
        if isinstance(category_id, list):
             data['category']= category_id
        else:
            data['category']= [int(category_id)]
    if keys_to_remove is not None:
        for key in keys_to_remove:
            logger.debug("removing key: " + str(key))
            data.pop(key, None)
    if additionalData is not None:
        if isinstance(additionalData, list):
            for new_data in additionalData:
                recursive_update(data, new_data)
        else:
            recursive_update(data, additionalData)
    product_url = base_url_env + "marketplace/v1/pim/product"
    if session:
        request = session.post(product_url, data=json.dumps(data))
    else:
        request = requests.post(product_url, data=json.dumps(data), headers=headers)

    logger_func("POST - PRODUCT: ", product_url, request.status_code, request.text, data=json.dumps(data))
    if request.status_code == 200 and product_json_data_track is not None and track_ids is not None:
        data_to_add = json.loads(json.dumps(data))
        product_json_data_track.append(data_to_add)
        track_ids.append(request.json()['id'])
    if returnData:
        return request, data
    else:
        return request
    


def recursive_update(original, updates):
    for key, value in updates.items():
        if isinstance(value, dict) and key in original and isinstance(original[key], dict): #  incoming is a dict, and original key matches with a key
            recursive_update(original[key], value)
        else:
            original[key] = value


def patch_product(session, id, data):
    url = base_url_env + "marketplace/v1/pim/product/" + str(id)
    request = session.patch(url, data=json.dumps(data))
    logger_func("PATCH - PRODUCT: ", url, request.status_code, request.text, data)
    return request

def delete_product(session, id):
    url = base_url_env + "marketplace/v1/pim/product/" + str(id)
    request = session.delete(url)
    logger_func("DELETE - PRODUCT: ", url, request.status_code, request.text)
    return request


def post_speciality_attibute(headers=None, session=None, data=None, additionalData=None, keys_to_remove=None):
    speciality_attibute = base_url_env + "marketplace/v1/pim/specialityattributes"
    if data is None: #if not data is passed for product, use the default product
        f = open('json_payloads/speciality_attribute.json', 'r')
        data = json.load(f)
        f.close()
        for key in data['shortDescription']:
            data['shortDescription'][key] = str(data['shortDescription'][key] + date_string)
    if keys_to_remove:
        for key in keys_to_remove:
            data.pop(key, None)
    if additionalData:
        data.update(additionalData)
    if session:
        request = session.post(speciality_attibute, data=json.dumps(data))
    else:
        if headers is None: raise Exception("headers was not entered for the request")
        request = requests.post(speciality_attibute, data=json.dumps(data), headers=headers)
    logger_func("POST - SPECIALITY_ATTRIBUTE: ", speciality_attibute, request.status_code, request.text, data=json.dumps(data))
    return request

def put_speciality_attibute(id, data, headers=None, session=None):
    speciality_attibute = base_url_env + "marketplace/v1/pim/specialityattributes/" + str(id)
    if session:
        request = session.put(speciality_attibute, json=data)
    else:
        if headers is None: raise Exception("headers was not entered for the request")
        request = requests.put(speciality_attibute, data=json.dumps(data), headers=headers)
    logger_func("PUT - SPECIALITY_ATTRIBUTE: ", speciality_attibute, request.status_code, request.text, data=json.dumps(data))
    return request

def patch_speciality_attibute(id, data, headers=None, session=None):
    speciality_attibute = base_url_env + "marketplace/v1/pim/specialityattributes/" + str(id)
    if session:
        request = session.patch(speciality_attibute, json=data)
    else:
        if headers is None: raise Exception("headers was not entered for the request")
        request = requests.patch(speciality_attibute, data=json.dumps(data), headers=headers)
    logger_func("PATCH - SPECIALITY_ATTRIBUTE: ", speciality_attibute, request.status_code, request.text, data=json.dumps(data))
    return request

def get_speciality_attribute(headers=None, session=None, id=None, limit=100, params=None):
    if id:
        speciality_attribute = base_url_env + "marketplace/v1/pim/specialityattributes/" + str(id)
    else:
        speciality_attribute = base_url_env + f"marketplace/v1/pim/specialityattributes?limit={limit}"
    if session:
        request = session.get(speciality_attribute, params=params)
    elif headers:
        request = requests.get(speciality_attribute, headers=headers, params=params)
    logger_func("GET - SPECIALITY ATTRIBUTE", speciality_attribute, request.status_code, request.text, disableResponseOutput=False)
    return request

def delete_speciality_attribute(headers=None, session=None, id=None):
    speciality_attibute = base_url_env + "marketplace/v1/pim/specialityattributes/" + str(id)
    if session:
        request = session.delete(speciality_attibute)
    elif headers:
        request = request.delete(speciality_attibute, headers=headers)
    else:
        raise Exception("There was no session or headers provided")
    logger_func("DELETE - SPECIALITY_ATTRIBUTE: ", speciality_attibute, request.status_code, request.text)
    return request


def get_order(headers=None, id=None, session=None, disable_api_response=False):
    if id:
        order_url = base_url_env + "marketplace/v1/oms/order/" + str(id)
    else: order_url = base_url_env + "marketplace/v1/oms/order?limit=1000"
    if session:
        request = session.get(order_url)
    else:
        request = requests.get(order_url, headers=headers)
    logger_func("GET ORDER", order_url, request.status_code, request.text, disableResponseOutput=disable_api_response)
    return request


def assert_customizer_option_data(in_array, expected_arr, index, key, key_2=None ):
    if not(0<= index < len(expected_arr)):
        logger.debug("there was no element from this index " + str(index))
        return True

    if key in in_array[index] and key in expected_arr[index]:
        if isinstance(expected_arr[index][key], dict) and key_2 in expected_arr[index][key]:
            return in_array[index][key][key_2] == expected_arr[index][key][key_2]
        else:
            return in_array[index][key] == expected_arr[index][key]


def compare_input_to_expected_data(input_json, expected_json):
    differences = []
    try:
        jsondiff_result = jsondiff.diff(expected_json, input_json)
        if jsondiff_result:
            logger.info("Differences detected using jsondiff:")
            logger.info(json.dumps(jsondiff_result, indent=4, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Error using jsondiff: {e}")
    recursive_verification(input_json, expected_json, differences)
    if differences:
        logger.error(f"Found {len(differences)} differences between input and expected JSON:")
        for i, diff in enumerate(differences, 1):
            logger.error(f"Difference #{i}: {diff}")
        return False
    
    return True

def recursive_verification(input_json, expected_json, differences, path='root'):
    """
    Recursively verify JSON structures and collect all differences
    
    Args:
        input_json: The input JSON to verify
        expected_json: The expected JSON to compare against
        differences: List to collect all differences found
        path: Current path in the JSON structure
    """
    if isinstance(expected_json, dict):
        # Check for missing keys
        missing_keys = set(expected_json.keys()) - set(input_json.keys())
        if missing_keys:
            for key in missing_keys:
                differences.append(f"Missing key '{path}.{key}' in input")
        
        for key in set(expected_json.keys()) & set(input_json.keys()):
            new_path = f"{path}.{key}"
            recursive_verification(input_json[key], expected_json[key], differences, new_path)
    
    elif isinstance(expected_json, list):
        if len(input_json) != len(expected_json):
            differences.append(f"List length mismatch at '{path}': got {len(input_json)}, expected {len(expected_json)}")
        
        for i in range(min(len(input_json), len(expected_json))):
            recursive_verification(input_json[i], expected_json[i], differences, f"{path}[{i}]")
    else:
        if not (input_json is None and expected_json == ""):
            if input_json != expected_json:
                expected_repr = repr(expected_json) if isinstance(expected_json, str) else str(expected_json)
                input_repr = repr(input_json) if isinstance(input_json, str) else str(input_json)
                differences.append(f"Value mismatch at '{path}':\n  Expected: {expected_repr}\n  Actual  : {input_repr}")



def product_get_api_data_transform( session=None, category_id=None, json_data:dict=None):
    # get_cat_response = get_categories_store(session, category_id )
    # assert get_cat_response.status_code == 200
    # get_cat_json = get_cat_response.json()
    copy_data = copy.deepcopy(json_data)

    # if category_id is None:
    copy_data.pop('category', None)
    # else:
    #     category_id_get_structure = {
    #     "category": [
    #         {
    #         "id": category_id
    #         # "name": "some__name",
    #         # "description": "some__description",
    #         # "categoryImageUrl" : "some__url"
    #         }
    #     ]
    #     }
    #     copy_data.update(category_id_get_structure)

    if "barcode" in copy_data:
        copy_data['barCode'] = copy_data.pop('barcode')  ### The get api returns barCode
    if "spotLight" in copy_data:
        copy_data['spotlight'] = copy_data.pop('spotLight')  ### The get api returns spotlight
    if copy_data.get("formatVersion") == '2':
        copy_data["name"] = copy_data["name"]['en']
        copy_data["shortDescription"] = copy_data["shortDescription"]['en']
        copy_data["description"] = copy_data["description"]['en']
        copy_data["brand"] = copy_data["brand"]['en']

    copy_data.pop('shippingAttributes','')
    if "Attributes" in copy_data:
        del copy_data["Attributes"]["productCategoryAttributes"]
    copy_data.pop('formatVersion', None)
    if "PriceTaxation" in copy_data:
        copy_data['PriceTaxation'].pop('cost', None)
    return copy_data


def product_sns_data_transform( category_id=None, json_data:dict=None):
    copy_data = copy.deepcopy(json_data)

    if category_id is None:
        copy_data.pop('category', None)
    else:
        category_id_get_structure = {
        "category": [
            {
            "id": category_id
            # "name": "some__name",
            # "description": "some__description",
            # "categoryImageUrl" : "some__url"
            }
        ]
        }
        copy_data.update(category_id_get_structure)

    if "barCode" in copy_data:
        copy_data['barCode'] = copy_data.pop('barcode')  ### The get api returns barCode
    if "spotLight" in copy_data:
        copy_data['spotlight'] = copy_data.pop('spotLight')  ### The get api returns spotlight
    # if copy_data.get("formatVersion") == '2':
    # copy_data["name"] = copy_data["title"]['en']
    copy_data["title"] = {}
    copy_data["title"]['en'] = copy_data.pop("name")
    if "shortDescription" in copy_data:
        temp_data = copy_data["shortDescription"]
        copy_data["shortDescription"] = {}
        copy_data["shortDescription"]['en'] = temp_data
    if "description" in copy_data:
        # copy_data["description"] = copy_data["description"]['en']
        temp_data = copy_data["description"]
        copy_data["description"] = {}
        copy_data["description"]['en'] = temp_data
    if "brand" in copy_data:
        # copy_data["brand"] = copy_data["brand"]['en']
        temp_data = copy_data["brand"]
        copy_data["brand"] = {}
        copy_data["brand"]['en'] = temp_data
    if "PriceTaxation" in copy_data:
        if "price" in copy_data['PriceTaxation']:
            copy_data['price'] = copy_data['PriceTaxation']["price"]
            if  len(copy_data["price"]) == 0:
                del copy_data["price"] ### in case theres not prices set
            #     logger.debug("deleted empty array of price")
            # else:
            #     print("there was something in price")
        if "specialPrice" in copy_data['PriceTaxation']:
            copy_data['specialPrice'] = copy_data['PriceTaxation']["specialPrice"]
            if len(copy_data['specialPrice']) == 0:
                del copy_data["specialPrice"] ### in case theres not prices set
            #     logger.debug("deleted empty array of specialPrice")
            # else:
            #     print("there was something in specialPrice")
        del copy_data['PriceTaxation']
    if "Attributes" in copy_data:
        copy_data["Attributes"].pop(["productCategoryAttributes"], "")
    copy_data.pop('formatVersion', None)
    copy_data.pop('isPerishable', None)
    if "requireShipping" in copy_data and copy_data["requireShipping"] == False:
        copy_data.pop('shippingAttributes', None)
    logger.debug("the sns payload transformed: ")
    logger.debug(json.dumps(copy_data, indent=4))
    copy_data.pop("deliveryMethod", "")
    copy_data.pop("ageVerificationRequire", "")
    copy_data.pop('shippingAttributes','')

    return copy_data


##### CATALOG APIS
def post_catalog(session, data, catalog_name=None, track_catalog_ids=None, track_catalog_input_data=None):
    catalog_url = base_url_env + "marketplace/v1/pim/catalog"
    result = ''.join(random.choices(string.ascii_letters, k=10))
    logger.info("generating a random string for the catalog name: " + result)
    if catalog_name:
        if data.get('formatVersion') == "2":
                print("the data is: ", data['name'])
                for k in data['name']:
                    data['name'][k] +=  "- " + catalog_name +" - "+ result
        else:
            data['name'] = catalog_name + result
    else:
        print('no name was provided')

    request = session.post(catalog_url, json.dumps(data))
    logger_func("POST - CATALOG: ", catalog_url, request.status_code, request.text, data)
    if track_catalog_ids is not None and track_catalog_input_data is not None:
        if request.status_code == 200:
            json_response = request.json()
            track_catalog_ids.append(json_response['id'])
            track_catalog_input_data[str(json_response['id'])]= data
    return request


def put_catalog(id, session, data,):
    catalog_url = base_url_env + "marketplace/v1/pim/catalog/"+ str(id)        
    request = session.post(catalog_url, json.dumps(data))
    logger_func("PUT - CATALOG: ", catalog_url, request.status_code, request.text, data)
    return request

def patch_catalog(data, id, session=None):
    url = base_url_env + "marketplace/v1/pim/catalog/"+ str(id)
    request = session.patch(url, data=json.dumps(data))
    logger_func("PATCH Catalog", url,request.status_code, request.text, data=data )
    return request

def get_catalog(headers=None, session=None, id=None, disable_api_response=False, params = None, limit=100, formatVersion2=False):
    if id:
        catalog = base_url_env + "marketplace/v1/pim/catalog/" + str(id)
        if formatVersion2:
            catalog = catalog +"?formatVersion=2"
    elif params:
        catalog = base_url_env + "marketplace/v1/pim/catalog"
    else:
        catalog = base_url_env + f"marketplace/v1/pim/catalog?limit={limit}"
        if formatVersion2:
            catalog = catalog +"?formatVersion=2"

    if session:
        request = session.get(catalog, params=params)
    else:
        request = requests.get(catalog, headers=headers, params=params)
    logger_func("GET - CATALOG", catalog, request.status_code, request.text, disableResponseOutput=disable_api_response)
    return request

def delete_catalog(id, session=None, headers=None, url_env=base_url_env, output_logs=True):
    catalog_url = url_env + "marketplace/v1/pim/catalog/"+str(id)
    if session:    
        request = session.delete(catalog_url)
    else:
        request =requests.delete(catalog_url, headers=headers)
    if output_logs:
        logger_func("DELETE - CATALOG: ", catalog_url, request.status_code, request.text)
    return request

def get_delta( id, session=None, headers=None, env_url=base_url_env):
    delta_url = env_url + "marketplace/v1/pim/route-catalog/getdelta/"+str(id)
    if session:    
        request = session.get(delta_url)
    else:
        request =requests.get(delta_url, headers=headers)
    logger_func("GET Delta - route-catalog: ", delta_url, request.status_code, request.text)
    return request

def approve_delta(id, session=None, headers=None, env_url=base_url_env):
    approve_delta_url = env_url + "marketplace/v1/pim/route-catalog/approvedelta/"+str(id)
    if session:    
        request = session.put(approve_delta_url)
    else:
        request =requests.put(approve_delta_url, headers=headers)
    logger_func("PUT Approve delta - route-catalog: ", approve_delta_url, request.status_code, request.text)
    return request

#### Route catalog
def post_route_catalog(session, data):
    route_catalog = base_url_env + "marketplace/v1/pim/route-catalog"
    request = session.post(route_catalog, json.dumps(data))
    logger_func("POST - ROUTE CATALOG: ", route_catalog, request.status_code, request.text, data)
    return request

def put_route_catalog(session, id, data):
    route_catalog = base_url_env + "marketplace/v1/pim/route-catalog/" + str(id)
    request = session.put(route_catalog, data=json.dumps(data))
    logger_func("PUT - ROUTE CATALOG: ", route_catalog, request.status_code, request.text, data=json.dumps(data))
    return request

def patch_route_catalog(session, id, data):
    route_catalog = base_url_env + "marketplace/v1/pim/route-catalog/" + str(id)
    request = session.patch(route_catalog, json.dumps(data))
    logger_func("PATCH - ROUTE CATALOG: ", route_catalog, request.status_code, request.text, data)
    return request

def put_route_catalog_price(session, id, data):
    route_catalog = base_url_env + "marketplace/v1/pim/route-catalog/" + str(id) +"/price"
    request = session.put(route_catalog, data=json.dumps(data))
    logger_func("PUT - ROUTE CATALOG (PRICE): ", route_catalog, request.status_code, request.text, data=json.dumps(data))
    return request

def get_route_catalog(headers=None, id=None, session=None, disable_api_response=False):
    if id:
        route_catalog = base_url_env + "marketplace/v1/pim/route-catalog" + str(id)
    else: route_catalog = base_url_env + "marketplace/v1/pim/route-catalog?limit=1000"
    if session:
        request = session.get(route_catalog)
    else:
        request = requests.get(route_catalog, headers=headers)
    logger_func("GET - ROUTE CATALOG", route_catalog, request.status_code, request.text, disableResponseOutput=disable_api_response)
    return request

def get_route_catalog(headers=None, id=None, session=None, params=None, limit=100):
    if id:
        route_catalog = base_url_env + "marketplace/v1/pim/route-catalog/" + str(id)
    elif params:
        route_catalog = base_url_env + "marketplace/v1/pim/route-catalog"
    else: route_catalog = base_url_env + f"marketplace/v1/pim/route-catalog?limit={limit}"

    if session:
        request = session.get(route_catalog, params=params)
    elif headers:
        request = requests.get(route_catalog, headers=headers, params=params)
    else:
        raise Exception("a session or headers was not passed for: " +route_catalog )
    logger_func("GET ROUTE-CATALOG", route_catalog, request.status_code, request.text)
    return request

def post_workflow( data, headers=None, session=None):
    route_catalog = base_url_env + "marketplace/v1/pim/route-catalog-workflow"
    if session:
        request = session.post(route_catalog, data=json.dumps(data))
    elif headers:
        request = requests.post(route_catalog, headers=headers, data=json.dumps(data))
    else:
        raise Exception("a session or headers was not passed for: " +route_catalog )
    logger_func("POST - WORKFLOW", route_catalog, request.status_code, request.text, data)
    return request

def get_workflow(headers=None, id=None, session=None):
    if id:
        route_catalog = base_url_env + "marketplace/v1/pim/route-catalog-workflow/" + str(id)
    else: route_catalog = base_url_env + "marketplace/v1/pim/route-catalog-workflow?limit=1000"
    if session:
        request = session.get(route_catalog)
    elif headers:
        request = requests.get(route_catalog, headers=headers)
    else:
        raise Exception("a session or headers was not passed for: " +route_catalog )
    logger_func("GET ROUTE-CATALOG-WORKFLOW", route_catalog, request.status_code, request.text)
    return request

def post_prepare_kit(session):
    prepare_kit = base_url_env + "marketplace/v1/ams/prepare-kit"
    request = session.post(prepare_kit)
    logger_func("POST - PREPARE-KIT: ", prepare_kit, request.status_code, request.text)
    return request

def get_airports(session:Session):
    airports = base_url_env +"marketplace/v1/ams/airports"
    request = session.get(airports)
    logger_func("GET - Airports: ", airports, request.status_code, request.text)
    return request

###### SECTOR APIS
def get_sector(session:Session, id=None):
    if id:
        sector_url = base_url_env +"marketplace/v1/ams/sector/" +str(id)
    else:
        sector_url = base_url_env +"marketplace/v1/ams/sector"
    request = session.get(sector_url)
    logger_func("GET - SECTORS: ", sector_url, request.status_code, request.text)
    return request

def post_sector(session:Session, data):
    sector_url = base_url_env +"marketplace/v1/ams/sector"
    request = session.post(sector_url, data=json.dumps(data))
    logger_func("POST - SECTORS: ", sector_url, request.status_code, request.text, data=data)
    return request

def get_root_type(session=None, headers=None):
    url = base_url_env +"marketplace/v1/ams/root-type"
    if session:
        request = session.get(url)
    elif headers:
        request = requests.get(headers=headers, url=url)
    else:
        raise Exception("Please enter headers or session")
    logger_func("GET - RootType: ", url, request.status_code, request.text)
    return request

def get_ui_template(session=None, headers=None):
    url = base_url_env +"marketplace/v1/ams/ui-template"
    if session:
        request = session.get(url)
    elif headers:
        request = requests.get(headers=headers, url=url)
    else:
        raise Exception("Please enter headers or session")
    logger_func("GET - UI Template: ", url, request.status_code, request.text)
    return request


#### AIRLINE CATEGORY
def post_airline_category(session:Session, data):
    airline_category_url = base_url_env +"marketplace/v1/ams/airline-category"
    request = session.post(airline_category_url, data=json.dumps(data))
    logger_func("POST - AIRLINE CATEGORY: ", airline_category_url, request.status_code, request.text, data=data)
    return request

def put_airline_category(session:Session, data, id):
    airline_category_url = base_url_env +"marketplace/v1/ams/airline-category/"+ str(id)
    request = session.put(airline_category_url, data=json.dumps(data))
    logger_func("PUT - AIRLINE CATEGORY: ", airline_category_url, request.status_code, request.text, data=data)
    return request

def patch_airline_category(session:Session, data, id):
    airline_category_url = base_url_env +"marketplace/v1/ams/airline-category/"+ str(id)
    request = session.patch(airline_category_url, data=json.dumps(data))
    logger_func("PATCH - AIRLINE CATEGORY: ", airline_category_url, request.status_code, request.text, data=data)
    return request


def get_airline_category(session: Session, id=None, limit=100, params=None, formatVersion2=None, disable_api_response=False):
    airline_category_url = base_url_env +"marketplace/v1/ams/airline-category"
    if id:
        airline_category_url = base_url_env +"marketplace/v1/ams/airline-category/" + str(id)
        if formatVersion2:
            airline_category_url = airline_category_url +"?formatVersion=2"
    elif params:
        airline_category_url = base_url_env +"marketplace/v1/ams/airline-category"
    else:
        airline_category_url = base_url_env +f"marketplace/v1/ams/airline-category?limit={limit}"
        if formatVersion2:
            airline_category_url = airline_category_url +"&formatVersion=2"
    request = session.get(airline_category_url)
    logger_func("GET AIRLINE CATEGORY", airline_category_url, request.status_code, request.text, disableResponseOutput=disable_api_response)
    return request

def post_flight(session:Session, data):
    flight_url = base_url_env +"marketplace/v1/ams/flight"
    request = session.post(flight_url, data=json.dumps(data))
    logger_func("POST - Flight: ", flight_url, request.status_code, request.text, data=data)
    return request

def post_brand(session:Session, data):
    request = session.post(brand_url, data=json.dumps(data))
    logger_func("POST - BRAND: ", brand_url, request.status_code, request.text, data=data)
    return request

def put_brand(session:Session, data, id):
    url = brand_url + "/"+ str(id)
    request = session.put(url, data=json.dumps(data))
    logger_func("PUT - BRAND: ", url, request.status_code, request.text, data=data)
    return request

def patch_brand(session:Session, data, id):
    url = brand_url + "/"+ str(id)
    request = session.patch(url, data=json.dumps(data))
    logger_func("PATCH - BRAND: ", url, request.status_code, request.text, data=data)
    return request


def get_brand(session: Session, id=None, limit=100, params=None, formatVersion2=None, disable_api_response=False):
    if id:
        brand_url = base_url_env + "marketplace/v1/pim/brand/"+ str(id)
        if formatVersion2:
            brand_url = brand_url +"?formatVersion=2"
    elif params:
        brand_url = base_url_env + "marketplace/v1/pim/brand"
    else:
        brand_url = base_url_env + "marketplace/v1/pim/brand?limit={limit}"
        if formatVersion2:
            brand_url = brand_url +"&formatVersion=2"
    request = session.get(brand_url)
    logger_func("GET BRAND", brand_url, request.status_code, request.text, disableResponseOutput=disable_api_response)
    return request


def delete_brand(session:Session, id):
    url = brand_url + "/"+ str(id)
    request = session.delete(url)
    logger_func("DELETE - BRAND: ", url, request.status_code, request.text)
    return request


### CATEGORY STRUCTURE HELPER
class CategoryTree:
    def __init__(self, category_id):
        self.category_id = category_id
        self.sub_category = []
        self.assigned_product_id = []

    def add_sub_category(self, sub_cat):
        self.sub_category.append(sub_cat)

    def is_leaf(self):
        return len(self.sub_category) == 0

    def assigned_product_id(self, p_id):
        if self.is_leaf():
            self.assigned_product_id.append(p_id)
        else:
            raise Exception("You tried to assign a product to a parent category")

    def find_lowest_sub_categories(self):
        if not self.sub_category:
            return [self]
        else:
            leaves = []
            for child in self.sub_category:
                leaves.extend(child.find_lowest_sub_categories())
            return leaves

def get_all_helper( session, endpoint=custumizer_url, limit=100, delay_in_between=1, aditional_params=None, schema_validation=False):
    offset = 1
    groupedObjects = []
    logger.info("Fetching all for: "+ endpoint)
    while True:
        url = endpoint
        params = {"offset": offset, "limit": limit}
        if aditional_params:
            params.update(aditional_params)
            logger.info("Calling the api using params: \n"+ json.dumps(params, indent=3))
            request = session.get(url, params=params)
        else:
            url = f"{endpoint}?offset={offset}&limit={limit}"
            request = session.get(url=url)
        if request.status_code != 200:
            json_response = request.json()
            logger_func("GET",url, request.status_code, request.text, disableResponseOutput=False)
            if (json_response.get("message")  and "No results found." in json_response.get("message")):
                break
            logger.critical("The json response: " + json.dumps(json_response, indent=5))
            raise Exception(f"There was an error on the GET ALL of {endpoint}")
        json_response = request.json()
        if (json_response.get("message") and "No Record Found" in json_response.get("message")):
            break
        if endpoint==custumizer_url:
            validate(instance=json_response, schema=schemas.cg_get_all)
            groupedObjects.extend(json_response['data']['productCustomizers']) ## may need to check the enpoints if need to keep it generic
        elif endpoint==product_url:
            if schema_validation:
                schema = load_schema("productResponse")
                validate(instance=json_response, schema=schema)
            groupedObjects.extend(json_response['data']['products'])
        elif endpoint==route_catalog:
            groupedObjects.extend(json_response['data']['catalogsAssignments'])
        elif endpoint==airports_url:
            groupedObjects.extend(json_response['data']['airports'])
        elif endpoint==sectors_url:
            groupedObjects.extend(json_response['data']['sectors'])
        elif endpoint==speciality_attribute_url:
            logger_func("GET",url, request.status_code, request.text, disableResponseOutput=False)
            groupedObjects.extend(json_response['data']['SpecialityAttributes'])
        elif endpoint==brand_url:
            groupedObjects.extend(json_response['data']['brands'])
        offset +=1
        sleep(delay_in_between)
        logger.debug("offset is: "+ str(offset))
    return groupedObjects

def get_latest_set(session:Session, endpoint=custumizer_url, limit=10, params=None, schema_validation=False):
    initial_set = session.get(url=endpoint, params=params)
    assert initial_set.status_code == 200
    initial_data_json = initial_set.json()
    total_number = initial_data_json['data']['']
    print



def fetch_data(session, url, params=None):
    print("Request params")
    print(json.dumps(params, indent=5))
    response = session.get(url, params=params)
    if response.status_code == 200:
        return response.json()

    else:
        print(f"Error: Unable to fetch data. Status code: {response.status_code}")
        print(json.dumps(response.json(), indent=5))
        return None

def fetch_last_n_products(session, base_url, n=10, items_per_page=10, search_params=None):
    # Combine search parameters with pagination parameters
    params = search_params.copy() if search_params else {}
    params['limit'] = items_per_page

    # First, make an initial API call to get total_records
    initial_data = fetch_data(session, base_url, params)
    if not initial_data or 'data' not in initial_data:
        print("there was not inital data...")
        print(initial_data )
        return []

    total_records = initial_data['data'].get('totalRecords')
    if total_records is None:
        print("Error: Couldn't find totalRecords in the API response")
        return []

    # Calculate the last page
    last_page = math.ceil(total_records / items_per_page)
    # print("total records:" , total_records)
    # print("items per page: ", items_per_page)
    # print("last page val: ", last_page)
    # Calculate the offset for the last page
    # last_page_offset = last_page * items_per_page -1

    # Update params for the last page
    params['offset'] = last_page
    last_page_data = fetch_data(session, base_url, params)

    if not last_page_data or 'data' not in last_page_data:
        print("there was no data in last_page_data")
        print(last_page_data)
        return []

    last_page_items = last_page_data['data'].get('products', [])

    # If we have n or more items on the last page, return the last n
    if len(last_page_items) >= n:
        print("returning from last_page_items")
        return last_page_items[-n:]

    # If we have fewer than n items, fetch the previous page
    items_needed = n - len(last_page_items)
    previous_page_offset = max(1, last_page-1)

    # Update params for the previous page
    params['offset'] = previous_page_offset
    previous_page_data = fetch_data(session, base_url, params)

    if not previous_page_data or 'data' not in previous_page_data:
        print("returning in previous_page_data  or 'data' not in previous_page_data ")
        return last_page_items  # Return what we have if we can't fetch the previous page

    previous_page_items = previous_page_data['data'].get('products', [])

    # Combine items from both pages
    combined_items = previous_page_items + last_page_items

    # Return the last n items
    print("at end of fuctnion.. there should be datra return")
    return combined_items[-n:]


def generate_test_csv(in_file="multiple_options.csv", duplicate_rows=False, n=100):
    dir, filename = os.path.split(in_file)
    with open(in_file, 'rb') as f:
        result = chardet.detect(f.read())
        enconding = result['encoding']
    df = pd.read_csv(in_file, encoding=enconding)

    if duplicate_rows:
        new_rows = []
        for index, row in df.iterrows():
            contains_duplicate = any('_number' in str(value) for value in row)
            if contains_duplicate:
                for i in range(n):
                    new_row = row.copy()
                    # Replace '_duplicate' with an increasing number in all columns
                    new_row = new_row.apply(lambda x: x.replace('_number', str(i + 1)) if isinstance(x,str) else x)
                    new_rows.append(new_row)
            else:
                new_rows.append(row)
        new_df = pd.DataFrame(new_rows).reset_index(drop=True)
        logger.debug("the new duplicated rows:")
        logger.debug(new_df)
        if new_rows:
            df = pd.concat([df, pd.DataFrame(new_rows)]).reset_index(drop=True)


    for col in df.columns:
        current_date_t = datetime.now()
        date_string = current_date_t.strftime('%Y_%m_%d__%H_%M_%S')
        df[col] = df[col].apply(lambda x: x.replace('_test', "__"+date_string) if isinstance(x, str) else x)
        df[col] = df[col].apply(lambda x: x.replace('_pp', ""+str(round(random.uniform(50, 100000.999), random.randint(0, 3)))) if isinstance(x, str) else x)
        df[col] = df[col].apply(lambda x: x.replace('_spp', ""+str(round(random.uniform(50, 100.999), random.randint(0, 3)))) if isinstance(x, str) else x)
        df[col] = df[col].apply(lambda x: x.replace('_pts_int', ""+str( random.randint(10, 10000000000))) if isinstance(x, str) else x)


    df['isTaxable'] = df['isTaxable'].apply(lambda x:"1" if x ==1.0 else x)
    df['isTaxable'] = df['isTaxable'].apply(lambda x:"0" if x ==0.0 else x)
    df['isAvailableToSell'] = df['isAvailableToSell'].apply(lambda x:"1" if x ==1.0 else x)
    df['isAvailableToSell'] = df['isAvailableToSell'].apply(lambda x:"0" if x ==0.0 else x)
    df['spotLight'] = df['spotLight'].apply(lambda x:"1" if x ==1.0 else x)
    df['spotLight'] = df['spotLight'].apply(lambda x:"0" if x ==0.0 else x)
    df['ageVerificationRequire'] = df['ageVerificationRequire'].apply(lambda x:"1" if x ==1.0 else x)
    df['ageVerificationRequire'] = df['ageVerificationRequire'].apply(lambda x:"0" if x ==0.0 else x)
    df['requireShipping'] = df['requireShipping'].apply(lambda x:"0" if x ==0.0 else x)
    df['requireShipping'] = df['requireShipping'].apply(lambda x:"1" if x ==1.0 else x)
    df['minQuantityAllowed/Order'] = df['minQuantityAllowed/Order'].apply(lambda x: '' if pd.isna(x) or x == '' else str(int(x)) if isinstance(x, float) and x.is_integer() else str(x))
    # df['minQuantityAllowed/Order'] = df['minQuantityAllowed/Order'].apply(lambda x:"1" if x ==1.0 else x)
    df['maxQuantityAllowed/Order'] = df['maxQuantityAllowed/Order'].apply(lambda x: '' if pd.isna(x) or x == '' else str(int(x)) if isinstance(x, float) and x.is_integer() else str(x))
    # df['maxQuantityAllowed/Order'] = df['maxQuantityAllowed/Order'].apply(lambda x:"1" if x ==1.0 else x)






    test_file_generate = 'testfile_' + filename
    # curr_dir = os.path.dirname(os.path.realpath(__file__))
    path_to_generated_csv = os.path.join(dir,test_file_generate  )
    df.to_csv(path_to_generated_csv, index=False, encoding=enconding)
    logger.debug("the full path of the file is generate_test_csv: " + str(path_to_generated_csv))
    return path_to_generated_csv


def convert_csv_to_json(csv_filepath):
    products = defaultdict(lambda: {
        "title": {},
        "isMultiSelectable": None,
        "customizerOptions": []
    })
    excluded_titles = set()
    with open(csv_filepath, mode='r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        title_fields = [field for field in reader.fieldnames if field.startswith('title_') and field != 'id']
        label_fields = [field for field in reader.fieldnames if field.startswith('option_label_')]
        price_fields = [field for field in reader.fieldnames if field.startswith('price_')]
        for row in reader:
            # Normalize the title field for comparison
            title_en = row['title_en'].strip().lower()
            # If 'fail' is in the title_en, exclude it
            if 'fail' in title_en:
                excluded_titles.add(row['title_en'])
                continue
            if row['title_en'] in excluded_titles:
                continue
            for field in title_fields:
                lang_code = field[len('title_'):]
                products[row['title_en']]["title"][lang_code] = row[field]
            products[row['title_en']]["isMultiSelectable"] = bool(int(row["isMultiSelectable"]))

            if row['required'].strip(): #cheching if it exists
                products[row['title_en']]["required"] = bool(int(row["required"]))
            try:
                if row['maximumSelections'].strip():
                    products[row['title_en']]["maximumSelections"] = int(float(row["maximumSelections"]))
            except Exception as e:
                print('there was an error in maximumSelections: ', row['title_en'], "\n", e)
                raise

            option = {
                "label": {},
                "additionalCharge": []
            }

            try:
                if row["isDefaultSelected"].strip():
                    option["isDefaultSelected"] = bool(int(row["isDefaultSelected"]))
            except Exception as e:
                print('there was an error in isDefaultSelected: ', row['title_en'], "\n", e)
                raise

            # Labels in all available languages
            for field in label_fields:
                lang_code = field[len('option_label_'):]
                option['label'][lang_code] = row[field]
            # Prices in different currencies
            for field in price_fields:
                if row[field]:
                    currency = field.split('_')[-1].upper()
                    option['additionalCharge'].append({
                        "value": float(row[field]),
                        "currency": currency
                    })
            products[row['title_en']]["customizerOptions"].append(option)
    # Remove excluded titles from the products
    for title in excluded_titles:
        products.pop(title, None)
    # Convert the defaultdict to a regular list of dictionaries
    return list(products.values())


######## AWS     

def resetVisibilityTimeout(receipt_handles: list, queue_url, sqs):
    """
    Reset visibility timeout for a batch of messages to make them immediately available
    for processing again. Handles invalid receipt handles gracefully.
    """
    success_count = 0
    for receipt_handle in receipt_handles:
        try:
            sqs.change_message_visibility(
                QueueUrl=queue_url,
                ReceiptHandle=receipt_handle,
                VisibilityTimeout=0
            )
            success_count += 1
        except Exception as e:
            # Handle the case where the receipt handle is no longer valid
            logger.warning(f"Receipt handle is no longer valid: {str(e)}")
            continue
        except Exception as e:
            logger.warning(f"Error resetting visibility timeout: {str(e)}")
            continue

    logger.info(f"Successfully reset visibility timeout for {success_count}/{len(receipt_handles)} messages")


def receive_sqs_messages(sqs, queue_url):
    """
    Receive a batch of messages from SQS queue.
    
    Args:
        sqs: Boto3 SQS client
        queue_url: URL of the SQS queue
        
    Returns:
        List of messages or empty list if none available
    """
    messages = sqs.receive_message(
        QueueUrl=queue_url,
        MessageAttributeNames=["All"],
        MaxNumberOfMessages=10,
        WaitTimeSeconds=3,
    )

    return messages.get('Messages', [])


def is_target_message(message, target_id):
    """
    Check if the SQS message has the target ID.
    
    Args:
        message: SQS message
        target_id: ID to look for
        
    Returns:
        bool: True if this is the target message, False otherwise
    """
    try:
        sqs_body = json.loads(message['Body'])

        if 'MessageAttributes' not in sqs_body:
            return False

        sqs_attributes = sqs_body['MessageAttributes']

        if 'Id' not in sqs_attributes:
            return False

        message_id = int(sqs_attributes['Id']['Value'])
        return message_id == int(target_id)

    except (json.JSONDecodeError, KeyError, ValueError) as e:
        logger.warning(f"Error parsing message: {str(e)}")
        return False


def get_s3_content_from_message(s3, message):
    """
    Extract S3 content from an SQS message.
    Args:
        s3: Boto3 S3 client
        message: SQS message containing S3 references
    Returns:
        dict: Parsed JSON content from S3 or None if retrieval failed
    """
    try:
        sqs_body = json.loads(message['Body'])
        message_body = json.loads(sqs_body['Message'])

        if 'Bucket' not in message_body or 'Key' not in message_body:
            logger.warning("Message missing Bucket/Key information")
            return None
        response = s3.get_object(Bucket=message_body['Bucket'], Key=message_body['Key'])
        content = response['Body'].read().decode('utf-8')
        data = json.loads(content)
        logger.info(json.dumps(data, indent=4))
        return data

    except s3.exceptions.NoSuchKey:
        logger.error(f"S3 object not found: {message_body.get('Bucket', 'unknown')}/{message_body.get('Key', 'unknown')}")
        return None
    except (json.JSONDecodeError, KeyError) as e:
        logger.warning(f"Error processing message content: {str(e)}")
        return None


def delete_message(sqs, queue_url, receipt_handle):
    """
    Delete a message from the SQS queue.
    Args:
        sqs: Boto3 SQS client
        queue_url: URL of the SQS queue
        receipt_handle: Receipt handle of the message to delete
    Returns:
        bool: True if deletion was successful, False otherwise
    """
    try:
        sqs.delete_message(
            QueueUrl=queue_url,
            ReceiptHandle=receipt_handle
        )
        return True
    except Exception as e:
        logger.warning(f"Failed to delete message: {str(e)}")
        return False


def get_sns_message_by_id_catalog(id, queue_url=CATALOG_SQS_QUEUE_URL, retries=10):
    """
    Search for a specific message in an SQS queue by ID and retrieve its content from S3. 
    Args:
        id: Target message ID to find
        queue_url: URL of the SQS queue
        retries: Number of retry attempts
    Returns:
        dict: Content of the target message
    Raises:
        Exception: If the message cannot be found after all retries
    """
    sqs = boto3.client('sqs', region_name="us-west-2")
    s3 = boto3.client("s3")
    for attempt in range(retries):
        receipt_handles = []
        logger.debug(f"Starting attempt {attempt+1}/{retries} to find message with ID {id}")
        try:
            while True:
                messages = receive_sqs_messages(sqs, queue_url)
                if not messages:
                    logger.info("No more messages to process. Exiting while loop.")
                    break
                logger.debug(f"Processing batch of {len(messages)} messages")
                for msg in messages:
                    if is_target_message(msg, id):
                        logger.info(f"Found message with ID {id}")
                        content = get_s3_content_from_message(s3, msg)
                        if content:
                            delete_message(sqs, queue_url, msg["ReceiptHandle"])
                            resetVisibilityTimeout(receipt_handles, queue_url, sqs)
                            return content
                        else:
                            receipt_handles.append(msg["ReceiptHandle"])
                    else:
                        receipt_handles.append(msg["ReceiptHandle"])
            logger.info(f"Completed message batch processing for attempt {attempt+1}")
        except Exception as error:
            logger.exception(f"Error during message processing: {str(error)}")
        if receipt_handles:
            resetVisibilityTimeout(receipt_handles, queue_url, sqs)
        if attempt < retries - 1:
            random_delay = random.randint(5, 10)
            logger.debug(f"Waiting {random_delay}s before retry attempt {attempt+2}")
            sleep(random_delay)
    raise Exception(f"Could not find SQS/SNS message with ID: {id} after {retries} attempts")

class allowedSNSModules(Enum):
    catalogQueue = "catalogQueue"
    inventoryQueue = "inventoryQueue"
    flightQueue = "flightQueue"

def clear_id_in_sqs_queue(module:allowedSNSModules, id, retries=1):
        print("Clearing any duplicated messages...")
        while True:
            try:
                if module.value == "catalogQueue":
                    get_sns_message_by_id_catalog(id, retries=retries)
                else:
                    raise Exception(f"Wrong allowedSNSModules.value was provided {module}")
                logger.debug(f"DELETED AN ID INSTANCE {id}")
            except:
                logger.debug(f"Finished going through deleting all SQS instances of the ID: {id}")
                break

_schema = None
_ref_resolver = None
def get_sns_schmema_file():
    global _schema, _ref_resolver

    if _schema is None:
        # Load schema only on first call
        current_dir = os.path.dirname(os.path.abspath(__file__))
        for _ in range(2):
            current_dir = os.path.dirname(current_dir)

        schema_json_file_path = os.path.join(current_dir, 'sns_payload_definitions.json')
        with open(schema_json_file_path, 'r') as schema_json_file:
            schema_contents = schema_json_file.read()
            _schema = json.loads(schema_contents)
            _ref_resolver = RefResolver.from_schema(_schema)
    return _schema, _ref_resolver


def validate_product(sns_data):
    schema, ref_resolver = get_sns_schmema_file()
    validate(sns_data, schema['components']['schemas']['Product'], resolver=ref_resolver)
    logger.info("Product data validation passed")

def validate_catalogAssigment(sns_data):
    schema, ref_resolver = get_sns_schmema_file()
    validate(sns_data, schema['components']['schemas']['CatalogAssignment'], resolver=ref_resolver)
    logger.info("Catalog assigment data schema validation passed")

def validate_airlineCategory(sns_data):
    schema, ref_resolver = get_sns_schmema_file()
    validate(sns_data, schema['components']['schemas']['AirlineCategory'], resolver=ref_resolver)
    logger.info("Airline category schema validation passed")


def validate_flight(sns_data):
    schema, ref_resolver = get_sns_schmema_file()
    validate(sns_data, schema['components']['schemas']['Flights'], resolver=ref_resolver)
    logger.info("Flight schema validation passed")

def validate_specialityAttribute(sns_data):
    schema, ref_resolver = get_sns_schmema_file()
    validate(sns_data, schema['components']['schemas']['SpecialityAttribute'], resolver=ref_resolver)
    logger.info("SpecialityAttribute  schema validation passed")



def uuid_random_name():
    return str(uuid.uuid4()).replace('-', '_')

def load_schema(schema_name, levels_up=2):
        # Get the path to the current script
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Go up the specified number of directory levels
    for _ in range(levels_up):
        current_dir = os.path.dirname(current_dir)

    schemas_dir = os.path.join(current_dir, 'api_json_schemas')

    # Construct the full path to the JSON file
    file_path = os.path.join(schemas_dir, schema_name+'.json')
    with open(file_path) as schema_file:
        return json.load(schema_file)


def route_catalog_workflow_helper(airline_session, id, duration=200):
    start_time = time.time()
    while True:
        sleep(5)
        get_workflow_r = get_workflow(id=id, session=airline_session)
        assert get_workflow_r.status_code == 200
        print("get workflow call try:")
        if "Airline Category Mapped"  in get_workflow_r.json()['data']['catalogsAssignments']['workflowStatus']:
            print("the worklow was Airline Category Mapped")
            break
        if time.time() - start_time >= duration:
            raise Exception("Route-catalog failed to get a Airline Category Mapped")

def addTranslations(data, fields_to_check):
    for field in fields_to_check:
        if field in data and isinstance(data[field], dict):
            for lang in LANGUAGES_CODES_CONFIGURED:
                if lang not in data[field]:
                    data[field][lang] = f"api_auto {lang} - {field}" + uuid_random_name()
        else:
            raise Exception("Field added is not a dic for multilang")
    return data

def update_env_variable(file_path, variable_name, new_value):
    # Read all lines from the file
    with open(file_path, 'r') as file:
        lines = file.readlines()

    # Look for the variable and modify it
    modified = False
    for i, line in enumerate(lines):
        if line.strip().startswith(f"{variable_name}=") or line.strip().startswith(f"{variable_name} ="):
            lines[i] = f"{variable_name}={new_value}\n"
            modified = True
            break

    # If variable wasn't found, you might want to add it
    if not modified:
        lines.append(f"{variable_name}={new_value}\n")

    # Write all lines back to the file
    with open(file_path, 'w') as file:
        file.writelines(lines)
def validate_multi_lang(payload_entry, payload_sns, languages):
    if not languages:
        assert payload_entry['en'] == payload_sns['en']
    else:
        for language in  languages:
            if language not in payload_entry:
                assert payload_entry['en'] == payload_sns[language]
            else:
                assert payload_entry[language]  == payload_sns[language]



def validate_api_payloads(
    input_payload: Dict[str, Any],
    response_payload: Dict[str, Any],
    field_mappings: Dict[str, Tuple[str, Optional[Callable]]] = None,
    extra_validators: List[Callable[[Dict[str, Any], Dict[str, Any]], None]] = None,
    required_fields: List[str] = None,
    strict_mode: bool = False,
    debug: bool = False
) -> None:
    """
    Generic function to validate API request and response payloads.
    
    Args:
        input_payload: The input request payload
        response_payload: The response payload
        field_mappings: Dictionary mapping input field paths to (response_field_path, comparator_func) tuples
        extra_validators: Optional list of additional validator functions
        required_fields: List of input field paths that must be present and validated
        strict_mode: If True, fail validation for any mismatch, even for non-required fields
        debug: If True, print detailed validation information
        
    Raises:
        AssertionError: If validation fails
    """
    # Default field mappings
    if field_mappings is None:
        field_mappings = {
            "name.en": ("data.airlineCategory.name", None),
            "disclaimer.en": ("data.airlineCategory.disclaimer", None),
            "shortDescription.en": ("data.airlineCategory.shortDescription", None),
            "description.en": ("data.airlineCategory.description", None),
            "uiTemplate": ("data.airlineCategory.uiTemplate", None),
            "rootType": ("data.airlineCategory.rootType", None),
            "code": ("data.airlineCategory.code", None),
            "bannerImage": ("data.airlineCategory.bannerImage", lambda i, r: isinstance(r, str)),
            "backgroundImage": ("data.airlineCategory.backgroundImage", lambda i, r: isinstance(r, str)),
            "images": ("data.airlineCategory.images", lambda i, r: validate_images_has_data(r))
        }

    # Set default required fields if not provided
    if required_fields is None:
        required_fields = ["name.en"]

    # Basic response validation
    assert response_payload.get("success") is True, f"Response should indicate success, got: {response_payload.get('success')}"
    assert response_payload.get("code") == 200, f"Response should have 200 status code, got: {response_payload.get('code')}"

    validation_failures = []

    # Validate each field according to the mapping
    for input_path, (response_path, comparator) in field_mappings.items():
        input_value = get_nested_value(input_payload, input_path)
        response_value = get_nested_value(response_payload, response_path)

        if debug:
            print(f"Validating: {input_path} -> {response_path}")
            print(f"  Input value: {input_value}")
            print(f"  Response value: {response_value}")

        # Check if this is a required field
        is_required = input_path in required_fields

        # Skip validation if input value doesn't exist and it's not required and not in strict mode
        if input_value is None and not is_required and not strict_mode:
            if debug:
                print(f"  Skipping: Optional field not in input")
            continue

        # For required fields or in strict mode, verify response value exists
        if is_required or strict_mode or input_value is not None:
            if response_value is None:
                error = f"Field {response_path} missing in response"
                validation_failures.append(error)
                if debug:
                    print(f"  FAIL: {error}")
                continue

            # Use the provided comparator or default to equality check
            if comparator is not None:
                if not comparator(input_value, response_value):
                    error = f"Comparison failed for {input_path} -> {response_path}: {input_value} != {response_value}"
                    validation_failures.append(error)
                    if debug:
                        print(f"  FAIL: {error}")
            else:
                if input_value != response_value:
                    error = f"Value mismatch for {input_path}: {input_value} != {response_value}"
                    validation_failures.append(error)
                    if debug:
                        print(f"  FAIL: {error}")

    # Run additional validators if provided
    if extra_validators:
        for validator in extra_validators:
            try:
                validator(input_payload, response_payload)
            except AssertionError as e:
                validation_failures.append(f"Extra validator failed: {str(e)}")

    # If there were any validation failures, raise an assertion error with all the details
    if validation_failures:
        raise AssertionError("\n".join(validation_failures))

    if debug:
        print("All validations passed!")


def get_nested_value(data: Dict[str, Any], path: str) -> Any:
    """
    Get a value from a nested dictionary using a dot-separated path.
    
    Args:
        data: The dictionary to extract from
        path: The dot-separated path to the value
        
    Returns:
        The value at the specified path, or None if not found
    """
    keys = path.split('.')
    current = data

    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return None

    return current


def validate_images_has_data(response_images: List[Dict[str, Any]]) -> bool:
    """
    Simply validate that the response has image data.
    
    Args:
        response_images: List of image dictionaries from response payload
        
    Returns:
        True if there's at least one image with data, False otherwise
    """
    # Check if response_images is a non-empty list
    if not isinstance(response_images, list) or not response_images:
        return False

    # Check that at least one image has some non-null data
    for image_dict in response_images:
        if any(value and isinstance(value, str) for value in image_dict.values()):
            return True

    return False


def update_env_variable(file_path, variable_name, new_value):
    # Read all lines from the file
    with open(file_path, 'r') as file:
        lines = file.readlines()

    # Look for the variable and modify it
    modified = False
    for i, line in enumerate(lines):
        if line.strip().startswith(f"{variable_name}=") or line.strip().startswith(f"{variable_name} ="):
            lines[i] = f"{variable_name}={new_value}\n"
            modified = True
            break

    # If variable wasn't found, you might want to add it
    if not modified:
        lines.append(f"{variable_name}={new_value}\n")

    # Write all lines back to the file
    with open(file_path, 'w') as file:
        file.writelines(lines)



def generate_multi_language(payload, languages, fields=['name', 'shortDescription', 'description'], ):
     for field in fields:
        for language in languages:
            payload[field][language] = field + " " + language + " " +  uuid_random_name()
