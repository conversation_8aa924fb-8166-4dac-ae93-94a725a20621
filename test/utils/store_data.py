import os

def get_env_variable(key, env):
    secret = os.environ.get(key)
    if secret is None:
        raise Exception(f"{key} enviroment variable was not found in {env} enviroment")
    return secret



# STORE_NAME = 'Flipkart'
# STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_pac", "pac")                              ### PAC env ###
# STORE_CLIENT_SECRET = get_env_variable("STORE_CLIENT_SECRET_pac", "pac")

# STORE_NAME = 'PC Store'
# STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_pac_lang", "pac")                              ### PAC env ###
# STORE_CLIENT_SECRET = get_env_variable("STORE_CLIENT_SECRET_pac_lang", "pac")




STORE_NAME_MIGRARTION = 'JAL'
STORE_CLIENT_ID = "ndehwj224i"                        ### PAC env ###
STORE_CLIENT_SECRET = get_env_variable("JAL_CLIENT_SECRET_STORE", "pac")

# STORE_NAME_MIGRARTION = 'UAL'
# STORE_CLIENT_ID = "mfymhmzmax"                       
# STORE_CLIENT_SECRET = get_env_variable("UAL_CLIENT_SECRET_STORE", "pac")

# STORE_NAME_MIGRARTION = 'NORSE'
# STORE_CLIENT_ID = "cyxdaxinj7"                       
# STORE_CLIENT_SECRET = "b0tesgyop8o5thowoot9"
