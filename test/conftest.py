import logging
import sys
import json
from typing import List

import requests
import os
import pytest
from jamatest import jamatest
from requests.adapters import HTTPAdapter
from utils.helper import *
from utils.ui_helper import *
from requests.packages.urllib3.util.retry import Retry
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import matplotlib.pyplot as plt
import yaml
from jsonschema.validators import RefResolver
from datetime import date, datetime, timedelta

from utils.config import *

matplotlib_logger = logging.getLogger('matplotlib')
matplotlib_logger.setLevel(logging.INFO)

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)
    
def update_nullable(data):
    if isinstance(data, dict):
        nullable_value = data.get('nullable')
        if str(nullable_value).lower() == 'true' and 'type' in data:
            original_type = data['type']
            if not isinstance(original_type, list):
                original_type = [original_type]
            if 'null' not in original_type:
                original_type.append('null')
            data['type'] = original_type
            del data['nullable']
        for key, value in data.items():
            update_nullable(value)
    elif isinstance(data, list):
        for item in data:
            update_nullable(item)

def remove_field(schema, fieldRemove='productCategoryAttributes'):
    if isinstance(schema, dict):
        schema.pop(fieldRemove, None)
        for value in schema.values():
            remove_field(value)
    elif isinstance(schema, list):
        for item in schema:
            remove_field(item)

@pytest.fixture(scope="session")
def json_schema_file():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    for _ in range(1):
        current_dir = os.path.dirname(current_dir)
    schema_json_file_path = os.path.join(current_dir, 'groundside-openapi.yml')

    with open(schema_json_file_path, 'r') as file:
        openapi_spec = yaml.safe_load(file)
    update_nullable(openapi_spec)
    remove_field(openapi_spec)
    # print("data")
    # print(json.dumps(openapi_spec, indent=5, cls=CustomJSONEncoder))
    return openapi_spec

@pytest.fixture(scope="session")
def ref_resolver(json_schema_file):
    return RefResolver.from_schema(json_schema_file)

    
@pytest.fixture(scope="session", autouse=True)
def jama_init(request):
    print(f"{jama_init.__name__}: Checking if JAMA is being used or not...")
    CLIENT_ID = os.getenv("JAMA_CLIENT_ID")
    CLIENT_SECRET = os.getenv("JAMA_CLIENT_SECRET")
    TEST_PLAN_ID = 17494322

    if TEST_PLAN_ID and CLIENT_ID is not None and os.getenv("ENV") != "local" and os.getenv("SKIP_JAMA") != "1":
        cycle_id = os.getenv("TEST_CYCLE_ID")
        # cycle_id = "16463243" #it can be gathered inspecting the page
        print(f"JAMA_CLIENT: {cycle_id}")
        jamatest.init(CLIENT_ID, CLIENT_SECRET, TEST_PLAN_ID, cycle_id, cycle_name=JAMA_CYCLE_NAME)
        os.environ['TEST_CYCLE_ID'] = str(jamatest.JamaTest.test_cycle.ID)
        update_env_variable('variables.env', 'TEST_CYCLE_ID', str(jamatest.JamaTest.test_cycle.ID))
        request.addfinalizer(publish_results)

def publish_results():
    if os.getenv("ENV") != "local" and os.getenv("SKIP_JAMA") != "1":
        jamatest.publish()

@pytest.fixture(scope="session")
def token_airline():
    return get_token('airline', base_url_env)

@pytest.fixture(scope="session")
def token_store():
    print("getting token...")
    return get_token('store', base_url_env)

@pytest.fixture(scope="session")
def header_store(token_store):
    return {"Content-Type": "application/json", "Authorization": token_store}

@pytest.fixture(scope="session")
def header_airline(token_airline):
    return {"Content-Type": "application/json", "Authorization": token_airline}

@pytest.fixture(scope="session")
def store_http_session():
    token, expires_at = get_token('store', base_url_env, True)
    session = requests.Session()
    adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({"Content-Type": "application/json", "Authorization": token, "x-api-key": "3WpbQyO0p05GRNo3PxBAI8crlRI623eU3oxxNsWp"})
    
    original_request = session.request
    
    def refresh_token_if_needed():
        nonlocal token, expires_at 
        if datetime.now() >= expires_at - timedelta(seconds=45):
            token, expires_at = get_token('store', base_url_env, True)
            session.headers.update({"Content-Type": "application/json", "Authorization": token, "x-api-key": "3WpbQyO0p05GRNo3PxBAI8crlRI623eU3oxxNsWp"})
            
    def request_with_refresh(*args, **kwargs):
        refresh_token_if_needed()
        return original_request(*args, **kwargs)
    
    session.request = request_with_refresh
    yield session
    session.close()
    
    
@pytest.fixture(scope="session")
def airline_session():
    token, expires_at = get_token('airline', base_url_env, True)
    session = requests.Session()
    adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({"Content-Type": "application/json", "Authorization": token, "x-api-key": "3WpbQyO0p05GRNo3PxBAI8crlRI623eU3oxxNsWp"})
    
    original_request = session.request
    
    def refresh_token_if_needed():
        nonlocal token, expires_at 
        if datetime.now() >= expires_at - timedelta(seconds=45):
            token, expires_at = get_token('airline', base_url_env, True)
            session.headers.update({"Content-Type": "application/json", "Authorization": token, "x-api-key": "3WpbQyO0p05GRNo3PxBAI8crlRI623eU3oxxNsWp"})
            
    def request_with_refresh(*args, **kwargs):
        refresh_token_if_needed()
        return original_request(*args, **kwargs)
    
    session.request = request_with_refresh
    yield session
    session.close()
    
@pytest.fixture(scope="session")
def json_reference_p_customizer() -> CG_type:
    print("in json_reference_p_customizer...")

    json_file = open("json_payloads/product_customizer.json", 'r', encoding='utf-8')
    new_customizer = json.load(json_file) 
    json_file.close()
    #Setting some specific name for verifying in the GET api call
    new_customizer['title']['en'] = "Crust - " + date_string
    new_customizer['title']['es'] = "Crustá en español - " + date_string
    for option in new_customizer['customizerOptions']: #iterate over the options
        for lenguage in option['label']: #iterate over the lenguages
            option['label'][lenguage] += date_string    
    arr = [new_customizer]
    return new_customizer

@pytest.fixture(scope="session")
def track_customizer_groups_input_data() -> List[CG_type]:
    print("in track_customizer_groups_input_data...")

    json_file = open("json_payloads/product_customizer.json", 'r', encoding='utf-8')
    new_customizer = json.load(json_file) 
    json_file.close()
    #Setting some specific name for verifying in the GET api call
    new_customizer['title']['en'] += date_string
    new_customizer['title']['es'] += date_string
    new_customizer['title']['zh_Hans'] += date_string
    #change the labels:
    for option in new_customizer['customizerOptions']:
        for lenguage in option['label']:
            option['label'][lenguage] += date_string    
    arr = [new_customizer]
    # print("the json_data::", json.dumps(arr, indent=3, ensure_ascii=False))
    yield arr
    with open("track_customizer_groups_input_data.json", 'w', encoding='utf-8') as file: 
        json.dump(arr, file, ensure_ascii=False)

@pytest.fixture(scope="session")
def track_customizers_IDs():
    print("in track_customizers_IDs")
    tracking_arr = []
    yield tracking_arr
    logger.info(f"There was a total of {str(len(tracking_arr))} product customizers created during the test ")
    with open("track_customizers_IDs.json", 'w', encoding='utf-8') as file: 
        json.dump(tracking_arr, file, ensure_ascii=False)
    
@pytest.fixture(scope="session")
def schema_loader():
    schemas_dir = 'utils/schema_files'
    schemas = {}
    for filename in os.listdir(schemas_dir):
        if filename.endswith('.json'):
             with open(os.path.join(schemas_dir, filename), 'r') as file:
                schema_key = filename.replace(".json", "")
                schemas[schema_key] = json.load(file)
    def get_schema(key):
        print ("THE KEY PASSED:", key)
        print(schemas.get(key))
        return schemas.get(key)
    return get_schema


##### PRODUCT FIXTURES
@pytest.fixture(scope="session")
def json_reference_product():
    json_v2 = open("json_payloads/productV2.json", 'r', encoding='utf-8')
    data_v2 = json.load(json_v2) 
    json_v2.close()
    json_file = open("json_payloads/product.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    
    return {
            "v1": data,
            "v2": data_v2
            }

@pytest.fixture(scope="session")
def track_product_input_data():
    track_arr = []
    yield track_arr
    with open("track_product_input_data.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)
        
@pytest.fixture(scope="session")
def track_product_input_data_map():
    track_arr = {}
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} products created during the test ")
    with open("track_product_input_data_map.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)        


@pytest.fixture(scope="session")
def track_product_IDs():
    track_arr = []
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} products created during the test ")
    with open("track_product_IDs.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)
        
#### CATALOG FIXTURES
@pytest.fixture(scope="session")
def json_reference_catalog():
    json_file = open("json_payloads/catalog.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    
    json_file_v2 = open("json_payloads/catalogV2.json", 'r', encoding='utf-8')
    data_v2 = json.load(json_file_v2) 
    json_file_v2.close()

    return {
        "v1": data,
        "v2": data_v2
        }


@pytest.fixture(scope="session")
def track_catalog_input_data():
    track_list = {}
    yield track_list
    logger.info(f"There was a total of {str(len(track_list))} catalogs created during the test ")
    with open("track_catalog_input_data.json", 'w', encoding='utf-8') as file: 
        json.dump(track_list, file, ensure_ascii=False)


@pytest.fixture(scope="session")
def track_catalog_IDs():
    track_arr = []
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} catalogs created during the test ")
    with open("track_catalog_IDs.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)


#### Sector
@pytest.fixture(scope="session")
def json_reference_sector():
    json_file = open("json_payloads/sector.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    return data

@pytest.fixture(scope="session")
def track_sector_IDs():
    track_arr = []
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} catalogs created during the test ")
    with open("track_sectors_IDs.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)



##### category structure
@pytest.fixture(scope="session")
def track_airline_categories():
    category = CategoryTree(1)
    yield category

    category_structure = category.to_dict()
    with open("track_airline_categories.json", 'w', encoding='utf-8') as file:
        json.dump(category_structure, file, indent=4, ensure_ascii=False)

@pytest.fixture(scope="session")
def json_reference_airline_category():
    json_file = open("json_payloads/airline-category.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    return data

#### Route catalog
@pytest.fixture(scope="session")
def json_reference_route_catalog():
    json_file = open("json_payloads/route-catalog.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    return data



######### UI
def create_driver():
    for i in range(3):
        try:
            browser_options = Options()
            if os.getenv("ENV") == "local":
                logger.info("using the local driver setup on CRIS Computer")
                SELENIUM_DRIVER_PATH = '/Users/<USER>/Documents/GitLab Projects/marketplace-ground-pim-server-test/test/utils/chromedriver'
                browser_options.add_experimental_option( "prefs", {"download.default_directory": DOWNLOAD_PATH,
                #                                             "goog:loggingPrefs": {"browser":"ALL"}
                # #                                                 })
                # SELENIUM_DRIVER_PATH = '/Users/<USER>/automation_ui_brand/test_ui/project_configs/chromedriver'
                # browser_options.add_experimental_option( "prefs", {"download.default_directory": DOWNLOAD_PATH,
                                                            "goog:loggingPrefs": {"browser":"ALL"}
                                                                })
                # browser_options.add_argument("--headless")
                browser_options.add_argument("--window-size=1920,1080")
                browser_options.add_argument("--no-sandbox")
                browser_options.add_argument("--disable-dev-shm-usage")
                driver = webdriver.Chrome(service=Service(SELENIUM_DRIVER_PATH),options=browser_options)
            else:
                CI_PROJECT_DIR = os.getenv("CI_PROJECT_DIR")
                logger.info("The download path: %s", DOWNLOAD_PATH)
                SELENIUM_DRIVER_PATH = '/usr/local/bin/chromedriver'
                logger.info("The SELENIUM_DRIVER_PATH: %s ", SELENIUM_DRIVER_PATH)
                browser_options.add_experimental_option( "prefs", {"download.default_directory": DOWNLOAD_PATH, "goog:loggingPrefs": {"browser":"ALL"}})
                browser_options.add_argument("--headless")
                browser_options.add_argument("--window-size=1920,1080")
                browser_options.add_argument("--no-sandbox")
                browser_options.add_argument("--disable-dev-shm-usage")
                driver = webdriver.Chrome(service=Service(SELENIUM_DRIVER_PATH),options=browser_options)
            driver.implicitly_wait(20)
            return driver
        except Exception as e:
            logger.critical("Creating the driver failed on attempt %s", i)
            logger.critical("EXCEPTION:")
            logger.critical(e)
            sleep(20)
    raise Exception("Exceded all the retries without getting a succesfull driver connection")
    
   
# Fixture for logging into a website
@pytest.fixture(scope="session")
def store_driver(store_user_login_timer):
    try:
        driver = create_driver()
        driver.get(LOGIN_URL)
        sleep(2)
        username_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[1]')
        password_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[2]')  # Adjust the selector as needed
        login_button = driver.find_element(By.XPATH,'//*[@id="form-element"]/button')  # Adjust the selector as needed
        username_field.send_keys(STORE_USERNAME)
        password_field.send_keys(STORE_PASSWORD)
        start_time = time.time()
        login_button.click()
        check_succesfull_login(driver, timer_arr=store_user_login_timer, start_time=start_time, user="Store")
        sleep(5)
        return driver
    except Exception as e:
        capture_failed_details(driver, "store_driver", e )

@pytest.fixture(scope="session")
def airline_driver(airline_user_login_timer):
    try:
        driver = create_driver()
        driver.get(LOGIN_URL)
        sleep(2)
        username_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[1]')
        password_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[2]')  # Adjust the selector as needed
        login_button = driver.find_element(By.XPATH,'//*[@id="form-element"]/button')  # Adjust the selector as needed
        username_field.send_keys(AIRLINE_USERNAME)
        password_field.send_keys(AIRLINE_PASSWORD)
        start_time = time.time()
        login_button.click()
        check_succesfull_login(driver, timer_arr=airline_user_login_timer, start_time=start_time, user="Airline")
        sleep(5)
        return driver
    except Exception as e:
        capture_failed_details(driver, "airline_driver", e )

@pytest.fixture(scope="session")
def pac_user_driver(pac_login_timer):
    try:
        driver = create_driver()
        driver.get(LOGIN_URL)
        sleep(2)
        username_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[1]')
        password_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[2]')  # Adjust the selector as needed
        login_button = driver.find_element(By.XPATH,'//*[@id="form-element"]/button')  # Adjust the selector as needed
        sleep(1)
        username_field.send_keys(PAC_USER_USERNAME)
        password_field.send_keys(PAC_USER_PASSWORD)
        start_time = time.time()
        login_button.click()
        check_succesfull_login(driver, 200, pac_login_timer, start_time, user="PAC_ADMIN") ### Need to do this for 
        sleep(10)
        navigate_to_sns(driver)  ##### new change.. need to be commented
        return driver 
    except Exception as e:
        capture_failed_details(driver, "pac_user_driver", e )


@pytest.fixture(scope="session")
def pac_login_timer():
    user = "Pac_user"
    file_name = user+"_login_latency_list.json"
    results = read_data(file_name)
    yield results
    create_login_graph(user,file_name, results )
    
@pytest.fixture(scope="session")
def airline_user_login_timer():
    user = "Airline_user"
    file_name = user+"_login_latency_list.json"
    results = read_data(file_name)
    yield results
    create_login_graph(user,file_name, results )
    
@pytest.fixture(scope="session")
def store_user_login_timer():
    user = "Store_user"
    file_name = user+"_login_latency_list.json"
    results = read_data(file_name)
    yield results
    create_login_graph(user,file_name, results )

def create_login_graph(user, file_name, results):
    write_data(file_name, results)
    logger.debug("Number of latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title(user+" login latency")
    plt.xlabel("Login number")
    plt.ylabel("Time (Seconds)")
    plt.savefig(user+"_login_latency.png", dpi=300)
    
@pytest.fixture(scope="session")
def product_save_timer():
    file_name = "product_save_latency_list.json"
    results = read_data(file_name)
    yield results
    write_data(file_name, results)
    logger.debug("Number of latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title("Product save latency")
    plt.xlabel("Product number")
    plt.ylabel("Time (Seconds)")
    plt.savefig("product_save_latency.png", dpi=300)
    
@pytest.fixture(scope="session")
def CA_save_timer():
    file_name = "CA_save_latency_list.json"
    results = read_data(file_name)
    yield results
    write_data(file_name, results)
    logger.debug("Number of Catalog assigment latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title("Catalog Assigment Saving Latency")
    plt.xlabel("Catalog Assigment number")
    plt.ylabel("Time (Seconds)")
    plt.savefig("CA_save_latency.png", dpi=300)

@pytest.fixture(scope="session")
def Catalog_save_timer():
    file_name = "Catalog_save_latency_list.json"
    results = read_data(file_name)
    yield results
    write_data(file_name, results)
    logger.debug("Number of Catalogs latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title("Catalogs Saving Latency")
    plt.xlabel("Catalogs number")
    plt.ylabel("Time (Seconds)")
    plt.savefig("Catalog_save_latency.png", dpi=300)

def read_data(file_name):
    try:
        with open(file_name, 'r') as file:
            return json.load(file)
    except:
        logger.debug(f"File '{file_name}' does not exist")
        return []
def write_data(file_name, data):
    with open(file_name, "w") as file:
        json.dump(data, file, indent=4 )

##### category structure
@pytest.fixture(scope="session")
def track_catalog_assigments():
    track_arr = {}
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} catalog assigments created during the test ")
    with open("track_catalog_assigments.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)        


