import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
import os
from utils.helper import *
from utils.ui_helper import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from jamatest import jamatest
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from jsonschema import validate
import yaml
from jsonschema.validators import RefResolver
from requests.adapters import HTTPAdapter
import pytest
from typing import Dict, Any, List, Optional, Callable, Tuple



from datetime import date, datetime

    

### dev
if TRIGGERED_JOB == "dev":
    AIRLINE_CLIENT_ID = "6y77pnvxs5"
    AIRLINE_CLIENT_SECRET = "6mxeoi0wg31-xhp&exrx"
    # print("AIRLINE_CLIENT_ID", AIRLINE_CLIENT_ID)
elif TRIGGERED_JOB == "qa":
    AIRLINE_CLIENT_ID = "rpuwr0em8g"
    AIRLINE_CLIENT_SECRET = "ow3ev1xk7p2145sv0udb"


def airline_category_duplicate_helper_post(airline_session, existing_id, data):
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 400
    post_json_response = post_response.json()
    assert existing_id == post_json_response['id']
    assert "An airline category already exists with the same code" in post_json_response['message']



@pytest.fixture(scope="session")
def airline_session():
    token, expires_at = get_token('airline', base_url_env, True, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET)
    session = requests.Session()
    adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({"Content-Type": "application/json", "Authorization": token})
    
    original_request = session.request
    
    def refresh_token_if_needed():
        nonlocal token, expires_at 
        if datetime.now() >= expires_at - timedelta(seconds=45):
            print("AIRLINE_CLIENT_ID", AIRLINE_CLIENT_ID)
            token, expires_at = get_token('airline', base_url_env, True, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET)
            session.headers.update({"Content-Type": "application/json", "Authorization": token})
            
    def request_with_refresh(*args, **kwargs):
        refresh_token_if_needed()
        return original_request(*args, **kwargs)
    
    session.request = request_with_refresh
    yield session
    session.close()



duplicate_code = 'ui_auto_code' + uuid_random_name()
level_1_id = 0
level2_1_id = 0
level2_2_id = 0
level3_id = 0
data_ref = {}


@pytest.fixture(scope="session")
def rootType(airline_session):
    d = get_root_type(session=airline_session)
    assert d.status_code == 200
    return d.json()['data']['rootTypes'][0]['rootTypeName']

@pytest.fixture(scope="session")
def ui_template(airline_session):
    d = get_ui_template(session=airline_session)
    assert d.status_code == 200
    return d.json()['data']['uiTemplates'][0]['templateName']


def test_airline_catagory_no_code_backwards_compatability_v2(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    pytest.duplicate_name = "api_auto no code " + uuid_random_name()
    data = {
        "formatVersion": "2",
        "name": {
            "en": pytest.duplicate_name
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
        "shortDescription": {
            "en": "short description " + uuid_random_name()
        },
        "description": {
            "en": "description " + uuid_random_name()
        },
        "images": [
            {
            "image1_1": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA"
            }
        ],
        "uiTemplate": ui_template,
        "rootType": rootType,
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 3
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###FormatVersion2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    post_response = post_airline_category(airline_session, data) # allow creation of duplicates..
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully." in post_json_response['message']
    category_id = post_json_response['id']

    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) ==3
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])

    ### send same payload in patch...

    ## modify the description only



def test_post_with_name_only_v1_duplicate(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    data = {
        "name": pytest.duplicate_name,
        "disclaimer": "disc " + uuid_random_name(),
        "shortDescription": "short description " + uuid_random_name(),
        "uiTemplate": ui_template,
        "rootType": rootType,
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png"
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']['en'] 
    assert data["disclaimer"]== sns['disclaimer']['en'] 
    assert data["shortDescription"] == sns['shortDescription']['en'] 
    assert "description" not in sns
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###FormatVersion2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


def test_post_with_name_v2_duplicate(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    data = {
        "formatVersion": "2",
        "name": {
            "en": pytest.duplicate_name
        },
        "uiTemplate": ui_template,
        "rootType": rootType,
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    global level_1_id
    category_id = post_json_response['id']
    level_1_id = category_id
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert "description" not in sns
    assert "shortDescription" not in sns
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###FormatVersion2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


    
def test_post_airline_catagory_v2_code(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    global level_1_id
    data = {
        "formatVersion": "2",
        "code" : duplicate_code,
        "name": {
            "en": "root_category no code name"+ uuid_random_name()
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
        "shortDescription": {
            "en": "short description " + uuid_random_name()
        },
        "description": {
            "en": "description " + uuid_random_name()
        },
        "images": [
            {
            "image1_1": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA"
            }
        ],
        "uiTemplate": ui_template,
        "rootType": rootType,
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    level_1_id = category_id
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) ==3
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


    post_response = post_airline_category(airline_session, data) # NOT allow creation of duplicates codes
    assert post_response.status_code == 400
    post_json_response = post_response.json()
    # assert "Category added successfully." in post_json_response['message']
    # category_id = post_json_response['id']

    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)


def test_post_airline_catagory_v2_code_spaces_and_signs(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    code = "api_auto-code - "+ uuid_random_name() + "@ ʘ"
    data = {
        "formatVersion": "2",
        "code" : f" {code} ",
        "name": {
            "en": " root_category no code name"+ uuid_random_name() +" "
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
         "description": {
            "en": "description " + uuid_random_name()
        },
        "uiTemplate": ui_template,
        "rootType": rootType,
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    data['code'] = code
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" not in sns 
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    post_response = post_airline_category(airline_session, data) # NOT allow creation of duplicates codes
    assert post_response.status_code == 400
    post_json_response = post_response.json()
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)

    data['description']['en'] = "new description"
    put_response = put_airline_category(airline_session, data, category_id)
    data['code'] = f" {code} "
    assert put_response.status_code == 200
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    data['code'] = code
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" not in sns 

    ## patch test
    patch_data = {
        "formatVersion": 2,
        "code" : f" {code} ",
        "name": {
            "en": "new name"
        }
    }
    patch_r = patch_airline_category(airline_session, patch_data, category_id)
    patch_r.status_code == 200
    data['name']['en'] = "new name"
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    data['code'] = code
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" not in sns 

def test_post_airline_category_name_using_previous_code(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
        #It should allow backwards compatability.... code with have _1
    data_no_code = {
        "formatVersion": "2",
        "name": {
            "en": duplicate_code
        }
    }
    post_response = post_airline_category(airline_session, data_no_code)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    ### validate using GET api
    print("GET API created without a code.... will it have one?")
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data_no_code, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data_no_code["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert "templateName" not in sns
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" not in sns 

    data_no_code2_v1 = {
        "name": duplicate_code,
        'description': "description test",
        'shortDescription': "shortDescription test"
    }
    post_response = post_airline_category(airline_session, data_no_code2_v1)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    ### validate using GET api
    print("GET API created without a code.... will it have one?")
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data_no_code2_v1["name"] == sns['title']['en']
    assert data_no_code2_v1["description"] == sns['longDescription']['en']
    assert data_no_code2_v1["shortDescription"] == sns['shortDescription']['en']
    assert "disclaimer" not in sns
    assert "rootType" not in sns
    assert "templateName" not in sns
    assert category_id == sns['id']
    assert "parentId"  not in sns
    assert "images" not in sns

def test_post_airline_category_code_different_levels_allowed(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    """check that it is allowed at multiple levels"""
    data = {
        "formatVersion": "2",
        "code" : duplicate_code,
        "name": {
            "en": "root_category no code name"+ uuid_random_name(),
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
        "description": {
            "en": "description " + uuid_random_name()
        },
        "uiTemplate": ui_template,
        "rootType": rootType,
        "parentCategory": level_1_id
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    global level2_1_id
    category_id = post_json_response['id']
    level2_1_id = category_id
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert "shortDescription" not in sns
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" not in sns
    # assert "images" in sns and len(sns['images']) ==2
    # assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ###Version2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)
    pytest.data_duplicate = data

def test_post_airline_catagory_code_validations(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    #Exact same payload VALIDATION
    data = pytest.data_duplicate
    airline_category_duplicate_helper_post(airline_session, level2_1_id, data)
    #Exact same payload VALIDATION different name
    data2 = copy.deepcopy(data)
    data2['name']['en'] = "not created..." + uuid_random_name()
    airline_category_duplicate_helper_post(airline_session, level2_1_id, data2)
    ## same as above on formatversion1
    data3 = {
        "code" : duplicate_code,
        "name": "root_category no code name"+ uuid_random_name(),
        "disclaimer": "disc " + uuid_random_name(),
        "description": "description " + uuid_random_name(),
        "uiTemplate": ui_template,
        "rootType": rootType,
        "parentCategory": level_1_id
    }
    airline_category_duplicate_helper_post(airline_session, level2_1_id, data3)

    ## make sure original still has original data
    get_response = get_airline_category(airline_session, level2_1_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )

    ## creating at other lvel is good
    data_2nd_cat = copy.deepcopy(data3)
    data_2nd_cat['code'] = "api_auto" + uuid_random_name()
    post_response = post_airline_category(airline_session, data_2nd_cat)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    global level2_2_id
    category_id = post_json_response['id']
    level2_2_id = category_id
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    field_mappings = {
            "name": ("data.airlineCategory.name", None),
            "disclaimer": ("data.airlineCategory.disclaimer", None),
            "shortDescription": ("data.airlineCategory.shortDescription", None),
            "description": ("data.airlineCategory.description", None),
            "uiTemplate": ("data.airlineCategory.uiTemplate", None),
            "rootType": ("data.airlineCategory.rootType", None),
            "code": ("data.airlineCategory.code", None),
            "bannerImage": ("data.airlineCategory.bannerImage", lambda i, r: isinstance(r, str)),
            "backgroundImage": ("data.airlineCategory.backgroundImage", lambda i, r: isinstance(r, str)),
            "images": ("data.airlineCategory.images", lambda i, r: validate_images_has_data(r))
        }

    validate_api_payloads(data_2nd_cat, get_json, field_mappings )

    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data_2nd_cat["name"] == sns['title']['en']
    assert data_2nd_cat["disclaimer"] == sns['disclaimer']['en']
    assert "shortDescription" not in sns
    assert data_2nd_cat["description"] == sns['longDescription']['en']
    assert data_2nd_cat["uiTemplate"] == sns['templateName']
    assert data_2nd_cat["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" not in sns



def test_put_code_change(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    """ ensure put api can change the code; focusing at the level2 """ 
    global level2_1_id
    new_duplicate_code = "api_auto_"+uuid_random_name()
    print("original code", duplicate_code)
    print("new code", new_duplicate_code)
    pytest.data_global = {
        "formatVersion": "2",
        "code" : new_duplicate_code,
        "name": {
            "en": "root_category no code name"+ uuid_random_name(),
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
        "description": {
            "en": "description " + uuid_random_name()
        },
        "images": [
            {
            "image1_1": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA"
            }
        ],
        "uiTemplate": ui_template,
        "rootType": rootType,
        "parentCategory": level_1_id
    }
    post_response = put_airline_category(airline_session, pytest.data_global, level2_1_id)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category updated successfully" in post_json_response['message']
    category_id = level2_1_id
    ### validate using GET api v1
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(pytest.data_global, get_json)
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert pytest.data_global["name"] == sns['title']
    assert pytest.data_global["disclaimer"] == sns['disclaimer']
    assert "shortDescription" not in sns
    assert pytest.data_global["description"] == sns['longDescription']
    assert pytest.data_global["uiTemplate"] == sns['templateName']
    assert pytest.data_global["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" in sns and len(sns['images']) ==1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    #######Version2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


def test_put_code_name_change(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    """ Ensure put api can change the code; focusing at the level2 """ 
    pytest.data_global['disclaimer']['en'] = "disclaimer " + uuid_random_name()
    global level2_1_id
    post_response = put_airline_category(airline_session, pytest.data_global, level2_1_id)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category updated successfully" in post_json_response['message']
    category_id = level2_1_id
    ### validate using GET api v1
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(pytest.data_global, get_json)
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert pytest.data_global["name"] == sns['title']
    assert pytest.data_global["disclaimer"] == sns['disclaimer']
    assert "shortDescription" not in sns
    assert pytest.data_global["description"] == sns['longDescription']
    assert pytest.data_global["uiTemplate"] == sns['templateName']
    assert pytest.data_global["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" in sns and len(sns['images']) ==1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    #######Version2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


def test_patch_code_change_then_name(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    ''' Test needs the previous PUT API test to pass'''
    data = pytest.data_global
    new_duplicate_code = "api_auto_"+uuid_random_name()
    patch_data = {"code": new_duplicate_code}
    data['code'] = new_duplicate_code
    patch_response = patch_airline_category(airline_session, patch_data, level2_1_id)
    assert patch_response.status_code == 200
    patch_json_response = patch_response.json()
    assert "Category updated successfully" in patch_json_response['message']
    ### validate using GET api
    get_response = get_airline_category(airline_session, level2_1_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    sns1 = None
    try:
        sns1 = get_sns_message_by_id_catalog(level2_1_id, retries=1) 
    except:
        print("there was not sns")
    if sns1: 
        validate_airlineCategory(sns1)
    
    # update the name to trigger sns
    new_name = "api_auto_"+uuid_random_name()
    patch_data = {"name": new_name}
    data['name']['en'] = new_name
    patch_response = patch_airline_category(airline_session, patch_data, level2_1_id)
    assert patch_response.status_code == 200
    patch_json_response = patch_response.json()
    assert "Category updated successfully" in patch_json_response['message']
    ### validate using GET API
    get_response = get_airline_category(airline_session, level2_1_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )

    ## Validate sns
    sns = get_sns_message_by_id_catalog(level2_1_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert "shortDescription" not in sns
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert level2_1_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id 
    assert "images" in sns and len(sns['images']) ==1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])

    ### patch formatVersion 2
    new_duplicate_code = "api_auto_"+uuid_random_name()
    patch_data = {"code": new_duplicate_code, "formatVersion": "2", "name":{"en": "new name.. " + new_duplicate_code}}
    data['code'] = new_duplicate_code
    data['name']['en'] = "new name.. " + new_duplicate_code
    patch_response = patch_airline_category(airline_session, patch_data, level2_1_id)
    assert patch_response.status_code == 200
    patch_json_response = patch_response.json()
    assert "Category updated successfully" in patch_json_response['message']
    ### validate using GET api
    get_response = get_airline_category(airline_session, level2_1_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(level2_1_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert "shortDescription" not in sns
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert level2_1_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" in sns and len(sns['images']) ==1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])


    patch_data['name'] = {"en": "new name.."}
    data['name'] = {"en": "new name.."}
    patch_response = patch_airline_category(airline_session, patch_data, level2_1_id)
    assert patch_response.status_code == 200
    patch_json_response = patch_response.json()
    assert "Category updated successfully" in patch_json_response['message']
    ### validate using GET api
    get_response = get_airline_category(airline_session, level2_1_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(level2_1_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert "shortDescription" not in sns
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert level2_1_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" in sns and len(sns['images']) ==1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])


    # creating a new category using the orinals duplicate; now changed
def test_post_allowed_previous_code_after_renamed(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    ''' needs previous test to have passed'''
    global level2_2_id, level3_id
    data = {
        "formatVersion": "2",
        "code" : duplicate_code,
        "name": {
            "en": "root_category no code name"+ uuid_random_name(),
        },
        "parentCategory": level_1_id
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    level2_2_id = category_id
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert "templateName" not in sns
    assert category_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" not in sns 

    #It should allow backwards compatability.... code with have _1
    data_no_code = {
        "formatVersion": "2",
        "name": {
            "en": duplicate_code
        },
        "parentCategory": level_1_id
    }
    post_response = post_airline_category(airline_session, data_no_code)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    level3_id = category_id
    ### validate using GET api
    print("GET API created without a code.... will it have one?")
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data_no_code, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data_no_code["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert "templateName" not in sns
    assert category_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" not in sns 


def test_put_patch_code_validations(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    ''' needs previous test to have passed'''
    # global level2_2_id, level3_id
    data = {
        "formatVersion": "2",
        "code" : "api_auto_no_code_changes " + uuid_random_name(),
        "name": {
            "en": "root_category no code name"+ uuid_random_name(),
        },
        "parentCategory": level_1_id
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert "disclaimer" not in sns
    # assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert "templateName" not in sns
    assert category_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" not in sns 
    
    #### Starting PUT test
    temp_data = copy.deepcopy(data)
    temp_data['code'] = duplicate_code
    post_response = put_airline_category(airline_session, temp_data, category_id)
    assert post_response.status_code == 400
    post_json_response = post_response.json()
    assert "An airline category already exists with the same code" in post_json_response['message']
    assert level2_2_id == post_json_response['id']
    #ensure name remains the same
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Change the try using PATCH
    post_response = patch_airline_category(airline_session, temp_data, category_id)
    assert post_response.status_code == 400
    post_json_response = post_response.json()
    assert "An airline category already exists with the same code" in post_json_response['message']
    assert level2_2_id == post_json_response['id']
    #ensure name renamins the same
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )

    ### Change the name only using patch. Code remains the same...
    new_name = "api_auto" + uuid_random_name()
    d = {"description": new_name}
    data['description'] = {'en':new_name}
    post_response = patch_airline_category(airline_session, d, category_id)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "updated successfully" in post_json_response['message']
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert "templateName" not in sns
    assert category_id == sns['id']
    assert "parentId" in sns and sns['parentId'] == level_1_id
    assert "images" not in sns 


def test_airline_catagory_image_validations(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    data = {
        "formatVersion": "2",
        "name": {
            "en": "api_auto no code " + uuid_random_name()
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
        "shortDescription": {
            "en": "short description " + uuid_random_name()
        },
        "description": {
            "en": "description " + uuid_random_name()
        },
        "images": [
            {
            "image1_1": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA"
            }
        ],
        "uiTemplate": ui_template,
        "rootType": rootType,
        "backgroundImage": "https://placehold.jp/3160x632.png"
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 400
    post_json_response = post_response.json()
    assert "Invalid backgroundImage resolution. Resolution should be at least 3840x2160 with 16:9 ratio." in post_json_response['message']
    
    del data["backgroundImage"]
    data["bannerImage"] = "https://placehold.jp/3840x2160.png"
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 400
    # assert "Invalid bannerImage Image Resolution. Resolution Should be Greater Than or Equal to 3840x2160 with 16:9." in post_json_response['message']   ##### NEEEDS TO BE REMOVED

    del data["bannerImage"]
    data['images'][0]['image1_1'] = "https://placehold.jp/3840x2160.png"
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 400
    post_json_response = post_response.json()
    assert "Invalid image1_1 Image Resolution. Resolution Should be Greater Than or Equal to 1200x1200 with 1:1." in post_json_response['message']

    data['images'][0]['image1_1'] = "https://placehold.jp/1000x1000.png"
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 400
    post_json_response = post_response.json()
    assert "Invalid image1_1 Image Resolution. Resolution Should be Greater Than or Equal to 1200x1200 with 1:1." in post_json_response['message']

    data['images'][0]['image1_1'] = "https://placehold.jp/1300x1300.png"
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###FormatVersion2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])

def test_post_create_hierarchy_validations(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    # level2_2_id = 57277
    ''' needs previous test to have passed'''
    # global level2_2_id, level3_id
    data = {
        "formatVersion": "2",
        "code" : "api_auto_level3 " + uuid_random_name(),
        "name": {
            "en": "api_auto level3 "+ uuid_random_name(),
        },
        "parentCategory": level2_2_id,
        "uiTemplate": ui_template,
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png"
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id_1 = post_json_response['id']
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id_1)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_1)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert ui_template  == sns['templateName']
    assert category_id_1 == sns['id']
    assert "parentId" in sns and sns['parentId'] == level2_2_id
    assert "images" in sns and len(sns['images']) ==2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])

    get_response = get_airline_category(airline_session, level2_2_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ids = [item["id"] for item in get_json['data']['airlineCategory']["subCategory"]]
    # Assert that both specific IDs exist in the list
    assert category_id_1 in ids


def test_put_create_hierarchy_validations(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    print("TESTING PUT PAYLOAD, POST IS FIRST")
    data2 = {
        "formatVersion": "2",
        "name": {
            "en": "api_auto level3 "+ uuid_random_name(),
        },
        "uiTemplate": ui_template,
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png"
    }
    post_response = post_airline_category(airline_session, data2)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id_2 = post_json_response['id']
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id_2)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data2, get_json )
    # ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_2)
    validate_airlineCategory(sns)
    assert data2["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert ui_template  == sns['templateName']
    assert category_id_2 == sns['id']
    assert "images" in sns and len(sns['images']) ==2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    
    ## Modifiying location using put payload...
    data2['parentCategory'] = level2_2_id
    post_response = put_airline_category(airline_session, data2, category_id_2)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category updated successfully" in post_json_response['message']
    get_response = get_airline_category(airline_session, category_id_2)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data2, get_json )
    # ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_2)
    validate_airlineCategory(sns)
    assert data2["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert ui_template  == sns['templateName']
    assert category_id_2 == sns['id']
    assert "parentId" in sns and sns['parentId'] == level2_2_id
    assert "images" in sns and len(sns['images']) ==2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])

    get_response = get_airline_category(airline_session, level2_2_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ids = [item["id"] for item in get_json['data']['airlineCategory']["subCategory"]]
    # Assert that both specific IDs exist in the list
    assert category_id_2 in ids

    ## Modifiying something else in PUT payload...
    data2['disclaimer'] = {"en":"disc"}
    post_response = put_airline_category(airline_session, data2, category_id_2)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category updated successfully" in post_json_response['message']
    get_response = get_airline_category(airline_session, category_id_2)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data2, get_json )
    # ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_2)
    validate_airlineCategory(sns)
    assert data2["name"] == sns['title']
    assert "disclaimer" in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert ui_template  == sns['templateName']
    assert category_id_2 == sns['id']
    assert "parentId" in sns and sns['parentId'] == level2_2_id
    assert "images" in sns and len(sns['images']) ==2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])

    get_response = get_airline_category(airline_session, level2_2_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ids = [item["id"] for item in get_json['data']['airlineCategory']["subCategory"]]

        # Assert that both specific IDs exist in the list
    assert category_id_2 in ids

    ## Remove the parent using put payload... 
    del data2['parentCategory']
    post_response = put_airline_category(airline_session, data2, category_id_2)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category updated successfully" in post_json_response['message']
    get_response = get_airline_category(airline_session, category_id_2)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data2, get_json )
    # ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_2)
    validate_airlineCategory(sns)
    assert data2["name"] == sns['title']
    assert "disclaimer" in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert ui_template  == sns['templateName']
    assert category_id_2 == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) ==2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])

    get_response = get_airline_category(airline_session, level2_2_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ids = [item["id"] for item in get_json['data']['airlineCategory']["subCategory"]]
    # Assert that both specific IDs exist in the list
    assert category_id_2 not in ids


    ## Add the location back using put payload...
    data2['parentCategory'] = level2_2_id
    post_response = put_airline_category(airline_session, data2, category_id_2)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category updated successfully" in post_json_response['message']
    get_response = get_airline_category(airline_session, category_id_2)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data2, get_json )
    # ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_2)
    validate_airlineCategory(sns)
    assert data2["name"] == sns['title']
    assert data2["disclaimer"] == sns['disclaimer']
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert ui_template  == sns['templateName']
    assert category_id_2 == sns['id']
    assert "parentId" in sns and sns['parentId'] == level2_2_id
    assert "images" in sns and len(sns['images']) ==2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])

    get_response = get_airline_category(airline_session, level2_2_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ids = [item["id"] for item in get_json['data']['airlineCategory']["subCategory"]]
    # Assert that both specific IDs exist in the list
    assert category_id_2 in ids


def test_patch_create_hierarchy_validations(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    data3 = {
        "formatVersion": "2",
        "name": {
            "en": "api_auto level3 "+ uuid_random_name(),
        },
        "uiTemplate": ui_template,
    }
    post_response = post_airline_category(airline_session, data3)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id_3 = post_json_response['id']
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id_3)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data3, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_3)
    validate_airlineCategory(sns)
    assert data3["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert "parentId" not in sns 
    assert ui_template  == sns['templateName']
    assert category_id_3 == sns['id']
    
    print("TESTING PATCH PAYLOAD")
    #### Modifiying parentCategory using patch payload
    data3['parentCategory'] = level2_2_id
    patch_payload = {'parentCategory': level2_2_id}
    print("updating the location...")
    post_response = patch_airline_category(airline_session, patch_payload, category_id_3)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category updated successfully" in post_json_response['message']
    get_response = get_airline_category(airline_session, category_id_3)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data3, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_3)
    validate_airlineCategory(sns)

    assert data3["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert ui_template  == sns['templateName']
    assert category_id_3 == sns['id']
    assert "parentId" in sns and sns['parentId'] == level2_2_id

    get_response = get_airline_category(airline_session, level2_2_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ids = [item["id"] for item in get_json['data']['airlineCategory']["subCategory"]]
    assert category_id_3 in ids

    data3['name'] = {'en':"new name..."}
    patch_payload2 = {"name": "new name..."}
    p_request = patch_airline_category(airline_session, patch_payload2, category_id_3)
    assert p_request.status_code == 200
    get_response = get_airline_category(airline_session, category_id_3)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data3, get_json)
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id_3)
    validate_airlineCategory(sns)
    assert data3["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "longDescription" not in sns
    assert "shortDescription" not in sns
    assert "rootType" not in sns
    assert ui_template  == sns['templateName']
    assert category_id_3 == sns['id']
    assert "parentId" in sns and sns['parentId'] == level2_2_id

    get_response = get_airline_category(airline_session, level2_2_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ids = [item["id"] for item in get_json['data']['airlineCategory']["subCategory"]]
    assert category_id_3 in ids


def test_validate_heircky_GET(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    '''alidate level1 contains children level2 '''
    get_response = get_airline_category(airline_session, level_1_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    ids = [item["id"] for item in get_json['data']['airlineCategory']["subCategory"]]
    # Assert that both specific IDs exist in the list
    assert level2_1_id in ids
    assert level2_2_id in ids



@pytest.mark.perfomance
def test_same_airline_category_concurrently(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    data = {
        "formatVersion": "2",
        "code" : 'api_auto concurrent codesss' + uuid_random_name(),
        "name": {
            "en": "root_category no code name"+ uuid_random_name()
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
        "shortDescription": {
            "en": "short description " + uuid_random_name()
        },
        "description": {
            "en": "description " + uuid_random_name()
        },
        "images": [
            {
            "image1_1": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA"
            }
        ],
        "uiTemplate": ui_template,
        "rootType": rootType,
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
    }


    num_requests = 10
    def make_api_request(_):
        return post_airline_category(airline_session, data)
    
    with ThreadPoolExecutor(max_workers=5) as executor:
        responses = list(executor.map(make_api_request, range(num_requests)))

    # assert len(responses) == num_requests
    succesful_category_created = 0
    id_created = 0
    failed_to_create = 0
    for response in responses:
        post_json_response = response.json()
        if response.status_code == 200:
            succesful_category_created +=1
            if id_created == 0:
                id_created = post_json_response['id']
            else:
                 assert id_created == post_json_response['id']
            assert "Category added successfully" in post_json_response['message']
        else:
            if id_created == 0:
                id_created = post_json_response['id']
            else:
                assert id_created == post_json_response['id']
            assert "An airline category already exists with the same code" in post_json_response['message']
            failed_to_create +=1
    assert succesful_category_created == 1
    ### validate using GET api
    get_response = get_airline_category(airline_session, id_created)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(id_created)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert id_created == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) ==3
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, id_created, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)






def test_post_airline_catagory_remove_images_put(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    """Test removing the images using the put api "", and also fields like uiTemplate"""
    code = "api_auto_ca_code " +uuid_random_name()
    data = {
        "formatVersion": "2",
        "code" : code,
        "name": {
            "en": "root_category no code name"+ uuid_random_name()
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
        "shortDescription": {
            "en": "short description " + uuid_random_name()
        },
        "description": {
            "en": "description " + uuid_random_name()
        },
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image4_3": "https://placehold.jp/760x570.png",
                "image5_6": "https://placehold.jp/665x798.png"
            }
        ],
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
        "uiTemplate": ui_template,
        "rootType": rootType,
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) ==6
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    ### Remove the images
    data['backgroundImage'] = ""
    data['bannerImage'] = ""
    data['images'][0]['image1_1'] = ""
    data['images'][0]['image2_1'] = ""
    data['images'][0]['image4_3'] = ""
    data['images'][0]['image5_6'] = ""

    put_response = put_airline_category(airline_session, data, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "

    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    # validate_api_payloads(data, get_json )
    assert get_json['data']['airlineCategory']['images'][0]['image1_1'] == None
    assert get_json['data']['airlineCategory']['images'][0]['image2_1'] == None
    assert get_json['data']['airlineCategory']['images'][0]['image4_3'] == None
    assert get_json['data']['airlineCategory']['images'][0]['image5_6'] == None
    assert get_json['data']['airlineCategory']['bannerImage'] == None
    assert get_json['data']['airlineCategory']['backgroundImage'] == None
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" not in sns

    data.update({      
        "uiTemplate": "" ,
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image4_3": "https://placehold.jp/760x570.png",
                "image5_6": "https://placehold.jp/665x798.png"
            }
        ],
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
        })
    put_response = put_airline_category(airline_session, data, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    data["uiTemplate"] = None
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert "uiTemplate" not in sns
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) ==6
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


    #### Test only a few images removed
    data.update({        
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image4_3": "",
                "image5_6": "https://placehold.jp/665x798.png"
            }
        ],
        "bannerImage": "",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
        "uiTemplate": ui_template
        })
    put_response = put_airline_category(airline_session, data, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    data['bannerImage'] = None
    data['images'][0]['image4_3'] = None
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 4
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

def test_post_airline_catagory_remove_data_patch(airline_session, ui_template, rootType, json_schema_file, ref_resolver):
    code = "api_auto_ca_code " +uuid_random_name()
    data = {
        "formatVersion": "2",
        "code" : code,
        "name": {
            "en": "root_category no code name"+ uuid_random_name()
        },
        "disclaimer": {
            "en": "disc " + uuid_random_name()
        },
        "shortDescription": {
            "en": "short description " + uuid_random_name()
        },
        "description": {
            "en": "description " + uuid_random_name()
        },
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image4_3": "https://placehold.jp/760x570.png",
                "image5_6": "https://placehold.jp/665x798.png"
            }
        ],
        "bannerImage": "https://placehold.jp/3160x632.png",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
        "uiTemplate": ui_template,
        "rootType": rootType,
    }
    post_response = post_airline_category(airline_session, data)
    assert post_response.status_code == 200
    post_json_response = post_response.json()
    assert "Category added successfully" in post_json_response['message']
    category_id = post_json_response['id']
    ### validate using GET api
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) ==6
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    patch_payload = {        
        "images": [
            {
                "image1_1": "",
                "image2_1": "",
                "image4_3": "",
                "image5_6": ""
            }
        ],
        "bannerImage": "",
        "backgroundImage": "",
}
    put_response =  patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "

    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code == 200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    # validate_api_payloads(data, get_json )
    assert get_json['data']['airlineCategory']['images'][0]['image1_1'] == None
    assert get_json['data']['airlineCategory']['images'][0]['image2_1'] == None
    assert get_json['data']['airlineCategory']['images'][0]['image4_3'] == None
    assert get_json['data']['airlineCategory']['images'][0]['image5_6'] == None
    assert get_json['data']['airlineCategory']['bannerImage'] == None
    assert get_json['data']['airlineCategory']['backgroundImage'] == None
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" not in sns
    ### Addd images again    
    put_response = put_airline_category(airline_session, data, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert data["uiTemplate"] == sns['templateName']
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) ==6
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


    #### Test only a few images removed
    data["uiTemplate"] = None
    data["bannerImage"] = None
    data["images"][0]['image1_1'] = None
    data["images"][0]['image4_3'] = None
    patch_payload = { 
        "uiTemplate": "",
        "images": [
            {
                "image1_1": "",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image4_3": None,
            }
        ],
        "bannerImage": "",
        "backgroundImage": "https://placehold.jp/3840x2160.png",
        }
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert "uiTemplate" not in sns 
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 3
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)



    data["images"][0]['image2_1'] = None
    patch_payload = { 
        "uiTemplate": "",
        "images": [
            {
                "image2_1": "",
            }
        ]
        }
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert "uiTemplate" not in sns 
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    ## removing the background image
    data["backgroundImage"] = None
    patch_payload = { 
        "backgroundImage": "",
        }
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert "uiTemplate" not in sns 
    assert data["rootType"] == sns['rootType']
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


    data["rootType"] = None
    patch_payload = { 
        "rootType": "",
        }
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert data["description"] == sns['longDescription']
    assert "uiTemplate" not in sns 
    assert "rootType" not in sns
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    data["description"] = None
    patch_payload = { 
        "description": "",
        }
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert data["shortDescription"] == sns['shortDescription']
    assert "longDescription" not in sns
    assert "uiTemplate" not in sns 
    assert "rootType" not in sns
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    data["shortDescription"] = None
    patch_payload = { 
        "shortDescription": "",
        }
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert data["disclaimer"] == sns['disclaimer']
    assert "shortDescription" not in sns
    assert "longDescription" not in sns
    assert "uiTemplate" not in sns 
    assert "rootType" not in sns
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)


    data["disclaimer"] = None
    patch_payload = { 
        "disclaimer": "",
        }
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "shortDescription" not in sns
    assert "longDescription" not in sns
    assert "uiTemplate" not in sns 
    assert "rootType" not in sns
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 1
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    patch_payload = {
        "bannerImage": "https://placehold.jp/3160x632.png",
    }
    data["bannerImage"] = "https://placehold.jp/3160x632.png"
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "shortDescription" not in sns
    assert "longDescription" not in sns
    assert "uiTemplate" not in sns 
    assert "rootType" not in sns
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 2
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

    patch_payload = {
        "images": [
            {
                "image2_1": "https://placehold.jp/1480x740.png",
            }
        ]

    }
    data["images"][0]["image2_1"] = "https://placehold.jp/1480x740.png"
    put_response = patch_airline_category(airline_session, patch_payload, category_id)
    assert put_response.status_code == 200
    put_json = put_response.json()
    assert put_json['message'] == "Category updated successfully. "
    get_response = get_airline_category(airline_session, category_id)
    assert get_response.status_code ==200
    get_json = get_response.json()
    validate(instance=get_json['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategory'], resolver=ref_resolver)
    validate_api_payloads(data, get_json )
    ## Validate sns
    sns = get_sns_message_by_id_catalog(category_id)
    validate_airlineCategory(sns)
    assert data["name"] == sns['title']
    assert "disclaimer" not in sns
    assert "shortDescription" not in sns
    assert "longDescription" not in sns
    assert "uiTemplate" not in sns 
    assert "rootType" not in sns
    assert category_id == sns['id']
    assert "parentId" not in sns
    assert "images" in sns and len(sns['images']) == 3
    assert all("id" in i and "type" in i and "aspectRatio" in i and "S3bucketlocation" in i and "height" in i  and "bucketName" in i['S3bucketlocation'] and "key" in i['S3bucketlocation']  for i in sns['images'])
    ### validate using GET API V2
    get_response = get_airline_category(airline_session, category_id, formatVersion2=True)  ###Version2
    assert get_response.status_code ==200
    get_json_v2 = get_response.json()
    validate(instance=get_json_v2['data']['airlineCategory'], schema=json_schema_file['components']['schemas']['airlineCategoryMulti'], resolver=ref_resolver)

