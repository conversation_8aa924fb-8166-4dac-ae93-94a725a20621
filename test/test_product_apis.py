import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
from utils.helper import *
from utils.ui_helper import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from jamatest import jamatest
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from jsonschema import validate
from jsonschema.validators import RefResolver
from datetime import date, datetime


def catch_duplicate_sns(id):
    sleep(1)
    try:
        print("Looking for duplicated sns")
        sns = get_sns_message_by_id_catalog(id, retries=1) ##
        pytest.fail("Found duplicated sns after a patch request to remove all images... not bueno")
    except:
        print("didnt found the sns")

@pytest.fixture(scope='session')
def speciality_attribute_id(store_http_session):
    r = get_speciality_attribute(session=store_http_session, limit=5)
    return r.json()['data']['SpecialityAttributes'][0]['id']
    
def validate_multi_lang(payload_entry, payload_sns, languages):
    if not languages: 
        assert payload_entry['en'] == payload_sns['en']
    else:
        for language in  languages: 
            if language not in payload_entry:
                assert payload_entry['en'] == payload_sns[language] 
            else:
                assert payload_entry[language]  == payload_sns[language]   
    
##### PRODUCT IMAGE TESTCASES
@pytest.mark.reg
def test_post_product_images(store_http_session,json_schema_file, ref_resolver):
    payload = {
        "sku": "api_auto_" + uuid_random_name(),
        "name": "api_auto_",
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "assets": {
            "images": [
                {
                    "image1_1": "https://placehold.jp/1200x1200.png",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image2_3": "https://placehold.jp/944x1416.png",
                    "image4_3": "https://placehold.jp/760x570.png",
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ],
            "gallery": [
                {
                    "url": "https://placehold.jp/1480x740.png",
                    "imagePosition": 1
                },
                {
                    "url": "https://placehold.jp/1200x1200.png",
                    "imagePosition": 2
                },
            ]
        },
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    product_response = post_product(session=store_http_session, data=payload)
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    if len(LANGUAGES_CODES_CONFIGURED) < 2:
        validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    number_images = len(payload['assets']['images'][0])
    number_posters = len(payload['assets']['gallery'])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(product_get_r.json()["data"]['product']['assets']["gallery"]) == number_posters
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert len(sns['images']) == number_posters + number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
        
        
        
        
@pytest.mark.reg
def test_post_product_images_formatVersion2_images(store_http_session,json_schema_file, ref_resolver):
    ''''
    Create a product with formatVersion2 with only required fields and validate images
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "assets": {
            "images": [
                {
                    "image1_1": "https://placehold.jp/1200x1200.png",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image2_3": "https://placehold.jp/944x1416.png",
                    "image4_3": "https://placehold.jp/760x570.png",
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ],
            "gallery": [
                {
                    "url": "https://placehold.jp/1480x740.png",
                    "imagePosition": 1
                },
                {
                    "url": "https://placehold.jp/1200x1200.png",
                    "imagePosition": 2
                },
            ]
        },
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)

    number_images = len(payload['assets']['images'][0])
    number_posters = len(payload['assets']['gallery'])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(product_get_r.json()["data"]['product']['assets']["gallery"]) == number_posters
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert len(sns['images']) == number_posters + number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i

        
@pytest.mark.reg    
def test_product_images_put(store_http_session,json_schema_file, ref_resolver):
    payload = {
        "sku": "api_auto_" + uuid_random_name(),
        "name": "api_auto_",
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }

    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    payload = {
        "sku": "api_auto_" + uuid_random_name(),
        "name": "api_auto_",
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "assets": {
            "images": [
                {
                    "image1_1": "https://placehold.jp/1200x1200.png",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image2_3": "https://placehold.jp/944x1416.png",
                    "image4_3": "https://placehold.jp/760x570.png",
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ],
            "gallery": [
                {
                    "url": "https://placehold.jp/1480x740.png",
                    "imagePosition": 1
                },
                {
                    "url": "https://placehold.jp/1200x1200.png",
                    "imagePosition": 2
                },
            ]
        },
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, id=id, retries=1)
    put_re = put_product(store_http_session, id, payload)
    assert put_re.status_code ==200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    if len(LANGUAGES_CODES_CONFIGURED) < 2:
        validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    number_images = len(payload['assets']['images'][0])
    number_posters = len(payload['assets']['gallery'])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(product_get_r.json()["data"]['product']['assets']["gallery"]) == number_posters
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert len(sns['images']) == number_posters + number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i


@pytest.mark.reg
def test_product_images_patch(store_http_session, json_schema_file, ref_resolver, speciality_attribute_id):
    payload = {
        "sku": "api_auto_" + uuid_random_name(),
        "name": "api_auto_",
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    payload['Attributes'] = { "specialityAttributes" : [speciality_attribute_id]}

    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    if len(LANGUAGES_CODES_CONFIGURED) < 2:
        validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)

    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    assert "image" in sns['specialityAttributes'][0]
    
    payload = {
        "assets": {
            "images": [
                {
                    "image1_1": "https://placehold.jp/1200x1200.png",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image2_3": "https://placehold.jp/944x1416.png",
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ],
            "gallery": [
                {
                    "url": "https://placehold.jp/1480x740.png",
                    "imagePosition": 1
                },
                {
                    "url": "https://placehold.jp/1200x1200.png",
                    "imagePosition": 2
                },
            ]
        }        
    }
    print("clearing the queue in case of duplicates")
    clear_id_in_sqs_queue(allowedSNSModules.catalogQueue, id=id, retries=1)
    print("Finished clearning the queue in case of duplicates")
    put_re = patch_product(store_http_session, id, payload)
    assert put_re.status_code ==200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    number_images = len(payload['assets']['images'][0])
    number_posters = len(payload['assets']['gallery'])
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert len(sns['images']) == number_posters + number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i


def test_product_only_required_fields_formatVersion1(store_http_session, json_schema_file, ref_resolver):
    product_data = {
        "sku": "api_auto_"+uuid_random_name(),
        "name": "Bottled WatsderDasani® Bottled™",
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }

    post_r = post_product(category_id=TESTING_CATEGORY_ID, data=(product_data), session=store_http_session,)
    assert post_r.status_code == 200
    id = post_r.json()['id']
    get_resp = get_product(session=store_http_session, id=id)
    assert get_resp.status_code == 200
    validate(instance=get_resp.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    if len(LANGUAGES_CODES_CONFIGURED) < 2:
        validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "description" not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert product_data['name'] == sns['title']['en']
    assert product_data['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert product_data['productSet'] == sns['productSet']
    assert product_data['productType'] == sns['productType']
    assert sns['id'] == id


####### formatversion2 



@pytest.mark.reg
def test_post_product_images_formatVersion2_only_required_fields(store_http_session, json_schema_file, ref_resolver, speciality_attribute_id):
    ''''
    Create a product with formatVersion2 with only required fields 
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "shortDescription" not in sns
    assert "description" not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    
    payload['name']['en'] += "123"
    payload['description'] = {"en":"description "+uuid_name}
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "shortDescription" not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)

    payload['name']['en'] += "123"
    payload['shortDescription'] = {"en":"short description "+uuid_name}
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)

    payload['assets'] = {
            "images": [
                {
                    "image1_1": "https://placehold.jp/1200x1200.png",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image2_3": "https://placehold.jp/944x1416.png",
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ],
            "gallery": [
                {
                    "url": "https://placehold.jp/1480x740.png",
                    "imagePosition": 1
                }
            ]
        }
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    
    # payload['brand'] = {'en': "brand en"}
    payload['Attributes'] = { "specialityAttributes" : [speciality_attribute_id]}
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    assert "image" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)

    
    
@pytest.mark.reg
def test_post_product_images_formatVersion2_shortDescription(store_http_session,json_schema_file, ref_resolver):
    ''''
    Create a product with formatVersion2 with shortDescription after the required fields 
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh"+uuid_name},
        "shortDescription": {"en": "api_auto_ shortDescription engligsh"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }

    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    get_json = get_v2_product.json()
    validate(instance=get_json, schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    validate_multi_lang(payload['shortDescription'], get_json['data']['product']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['name'], get_json['data']['product']['name'], LANGUAGES_CODES_CONFIGURED)

    
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "description" not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)

    
@pytest.mark.reg
def test_post_product_images_formatVersion2_description(store_http_session, json_schema_file, ref_resolver):
    ''''
    Create a product with formatVersion2 with shortDescription after the required fields 
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh_"+uuid_name},
        "description": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    get_json = get_v2_product.json()
    validate(instance=get_json, schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    validate_multi_lang(payload['name'], get_json['data']['product']['name'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], get_json['data']['product']['description'], LANGUAGES_CODES_CONFIGURED)
    
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "shortDescription" not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)

@pytest.mark.reg
def test_post_product_images_formatVersion2_description_shortDesc(store_http_session, json_schema_file, ref_resolver):
    ''''
    Create a product with formatVersion2 with shortDescription after the required fields 
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh_"+uuid_name},
        "description": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "shortDescription": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    get_json = get_v2_product.json()
    validate(instance=get_json, schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    validate_multi_lang(payload['shortDescription'], get_json['data']['product']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['name'], get_json['data']['product']['name'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], get_json['data']['product']['description'], LANGUAGES_CODES_CONFIGURED)

    
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)


@pytest.mark.reg
def test_post_product_images_formatVersion2_description_shortDesc_brand(store_http_session,json_schema_file, ref_resolver):
    ''''
    Create a product with formatVersion2 with shortDescription after the required fields 
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh_"+uuid_name},
        "description": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "shortDescription": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }

    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    get_json = get_v2_product.json()
    validate(instance=get_json, schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    validate_multi_lang(payload['shortDescription'], get_json['data']['product']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['name'], get_json['data']['product']['name'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], get_json['data']['product']['description'], LANGUAGES_CODES_CONFIGURED)
    
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)


@pytest.mark.reg
def test_post_product_images_formatVersion2_description_shortDesc_brand_few_images(store_http_session,json_schema_file, ref_resolver):
    ''''
    Create a product with formatVersion2 with shortDescription after the required fields 
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh_"+uuid_name},
        "description": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "shortDescription": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free",
        "assets": {
            "images": [
                {
                    "image1_1": "https://placehold.jp/1200x1200.png",
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ]        
        },
        }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    get_json = get_v2_product.json()
    validate(instance=get_json, schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    validate_multi_lang(payload['shortDescription'], get_json['data']['product']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['name'], get_json['data']['product']['name'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], get_json['data']['product']['description'], LANGUAGES_CODES_CONFIGURED)
    
    
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    number_images = len(payload['assets']['images'][0])
    assert len(sns['images']) == number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)
        
        
            
@pytest.mark.reg
def test_post_product_images_formatVersion2_description_shortDesc_brand_all_images_gallery(store_http_session,json_schema_file, ref_resolver):
    ''''
    Create a product with formatVersion2 with shortDescription after the required fields 
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name engligsh_"+uuid_name},
        "description": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "shortDescription": {"en": "api_auto_ shortDescription engligsh_"+uuid_name},
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free",
        "assets": {
            "images": [
                {
                    "image1_1": "https://placehold.jp/1200x1200.png",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image2_3": "https://placehold.jp/944x1416.png",
                    "image4_3": "https://placehold.jp/760x570.png",
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ],
            "gallery": [
                {
                    "url": "https://placehold.jp/1480x740.png",
                    "imagePosition": 1
                }
            ]
        },
        }
    generate_multi_language(payload, LANGUAGES_CODES_CONFIGURED)
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    get_json = get_v2_product.json()
    validate(instance=get_json, schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    validate_multi_lang(payload['shortDescription'], get_json['data']['product']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['name'], get_json['data']['product']['name'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], get_json['data']['product']['description'], LANGUAGES_CODES_CONFIGURED)

    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    number_images = len(payload['assets']['images'][0])
    number_gal_images = len(payload['assets']['gallery'])
    assert len(sns['images']) == number_images + number_gal_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)
        

@pytest.mark.reg
@pytest.mark.skipif(len(LANGUAGES_CODES_CONFIGURED) < 2, reason=" Multi-language not configured")
def test_post_product_images_formatVersion2_multi_lang(store_http_session,json_schema_file, ref_resolver):    
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_auto_" + uuid_random_name(),
        "name": {"en": "api_auto_ name english "+uuid_name}, "ja": "ドーナツ name",
        "shortDescription": {"en": "api_auto_ shortDescription englienglish "+uuid_name},"ja": "ドーナツ short desc",
        "description": {"en": "api_auto_ shortDescription englienglish "+uuid_name}, "ja": "ドーナツ desc",
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"
        }
    
    payload = addTranslations(payload, ["name","shortDescription", "description" ] )
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    assert payload['shortDescription']["en"] == product_get_r.json()['data']['product']["shortDescription"]
    assert payload['name']["en"] == product_get_r.json()['data']['product']["name"]
    assert payload['description']["en"] == product_get_r.json()['data']['product']["description"]
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    get_json = get_v2_product.json()
    validate_multi_lang(payload['shortDescription'], get_json['data']['product']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['name'], get_json['data']['product']['name'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], get_json['data']['product']['description'], LANGUAGES_CODES_CONFIGURED)
    
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    
    payload['shortDescription']["en"] += " test"
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    get_json =get_v2_product.json()
    validate(instance=get_json, schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    validate_multi_lang(payload['name'], get_json['data']['product']['name'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], get_json['data']['product']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['shortDescription'], sns['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)

@pytest.mark.skipif(len(LANGUAGES_CODES_CONFIGURED) < 2, reason=" Multi-language not configured")
def test_speciality_attribute_new_multi_lang(store_http_session, json_schema_file, ref_resolver):
    random_data = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "shortDescription": {
            "en": "api_auto_speciality ドーナツ donuts_apidonuts_apidonuts_api" +random_data,
            "ja": "ドーナツ " + random_data,
        },
        "disclaimer": {
            "en": "api_auto_speciality disclaimer ドーナツ donuts_apidonuts_apidonuts_api" +random_data,
            "ja": "ドーナツ disc",
        },
        "image": "https://placehold.jp/256x256.png"
    }
    addTranslations(payload, ["shortDescription", "disclaimer"])
    p_sp = post_speciality_attibute(session=store_http_session, data=payload)
    assert p_sp.status_code == 200
    id = p_sp.json()["id"]
    get_response = get_speciality_attribute(id=id, session=store_http_session)
    assert get_response.status_code == 200
    validate(instance=get_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributes'], resolver=ref_resolver)
    get_v2_response = get_speciality_attribute(session=store_http_session,id=id, params={"formatVersion":2})
    assert get_v2_response.status_code == 200
    validate(instance=get_v2_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributesMulti'], resolver=ref_resolver)
    validate_multi_lang(payload['shortDescription'], get_v2_response.json()['data']['SpecialityAttribute']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['disclaimer'], get_v2_response.json()['data']['SpecialityAttribute']['disclaimer'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_specialityAttribute(sns)
    validate_multi_lang(payload['shortDescription'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['disclaimer'], sns['disclaimer'], LANGUAGES_CODES_CONFIGURED)
    #### PATCH
    print("Testing PATCH Speciality attribute")
    random_data = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "shortDescription": {
            "en": "api_auto_speciality ドーナツ patch donuts_apidonuts_apidonuts_api" +random_data,
            "ja": "ドーナツ " + random_data
        },
        "disclaimer": {
            "en": "api_auto_speciality disclaimer patch ドーナツ donuts_apidonuts_apidonuts_api" +random_data,
            "ja": "ドーナツ disc",
        }
    }
    addTranslations(payload, ["shortDescription", "disclaimer"])
    p_sp = patch_speciality_attibute(id, payload, session=store_http_session)
    assert p_sp.status_code == 200
    
    get_response = get_speciality_attribute(id=id, session=store_http_session)
    assert get_response.status_code == 200
    validate(instance=get_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributes'], resolver=ref_resolver)
    get_v2_response = get_speciality_attribute(session=store_http_session,id=id, params={"formatVersion":2})
    assert get_v2_response.status_code == 200
    validate(instance=get_v2_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributesMulti'], resolver=ref_resolver)
    validate_multi_lang(payload['shortDescription'], get_v2_response.json()['data']['SpecialityAttribute']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['disclaimer'], get_v2_response.json()['data']['SpecialityAttribute']['disclaimer'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_specialityAttribute(sns)
    
    validate_multi_lang(payload['shortDescription'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['disclaimer'], sns['disclaimer'], LANGUAGES_CODES_CONFIGURED)
    
    #### PUT
    print("Testing PUT Speciality attribute")
    random_data = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "shortDescription": {
            "en": "api_auto_speciality put ドーナツ donuts_apidonuts_apidonuts_api" +random_data,
            "ja": "ドーナツ"  + random_data,
        },
        "disclaimer": {
            "en": "api_auto_speciality disclaimer put ドーナツ donuts_apidonuts_apidonuts_api" +random_data,
            "ja": "ドーナツ disc2",
        },
        "image": "https://placehold.jp/256x256.png"
    }
    addTranslations(payload, ["shortDescription", "disclaimer"])
    p_sp = put_speciality_attibute(id, payload, session=store_http_session)
    assert p_sp.status_code == 200
    
    get_response = get_speciality_attribute(id=id, session=store_http_session)
    assert get_response.status_code == 200
    validate(instance=get_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributes'], resolver=ref_resolver)
    get_v2_response = get_speciality_attribute(session=store_http_session,id=id, params={"formatVersion":2})
    assert get_v2_response.status_code == 200
    validate(instance=get_v2_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributesMulti'], resolver=ref_resolver)
    validate_multi_lang(payload['shortDescription'], get_v2_response.json()['data']['SpecialityAttribute']['shortDescription'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['disclaimer'], get_v2_response.json()['data']['SpecialityAttribute']['disclaimer'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    try:
        validate_specialityAttribute(sns)
    except:
        print("the unpublished sns was taken care...")
        sns = get_sns_message_by_id_catalog(id)
        validate_specialityAttribute(sns)
    
    validate_multi_lang(payload['shortDescription'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['disclaimer'], sns['disclaimer'], LANGUAGES_CODES_CONFIGURED)
    
    ### DELETE
    del_response = delete_speciality_attribute(id=id, session=store_http_session)
    assert del_response.status_code == 200
    assert "Speciality Attribute deleted successfully" == del_response.json()['message']
    sns = get_sns_message_by_id_catalog(id)
    assert id == sns['id']


def test_speciality_attribute_default_lang(store_http_session, json_schema_file, ref_resolver):
    random_data = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "shortDescription": {
            "en": "api_auto_speciality ドーナツ donuts_apidonuts_apidonuts_api" +random_data,
        },
        "disclaimer": {
            "en": "api_auto_speciality disclaimer ドーナツ donuts_apidonuts_apidonuts_api" +random_data,
        },
        "image": "https://placehold.jp/256x256.png"
    }
    p_sp = post_speciality_attibute(session=store_http_session, data=payload)
    assert p_sp.status_code == 200
    id = p_sp.json()["id"]
    get_response = get_speciality_attribute(id=id, session=store_http_session)
    assert get_response.status_code == 200
    validate(instance=get_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributes'], resolver=ref_resolver)
    get_v2_response = get_speciality_attribute(session=store_http_session,id=id, params={"formatVersion":2})
    assert get_v2_response.status_code == 200
    validate(instance=get_v2_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributesMulti'], resolver=ref_resolver)
    assert payload['shortDescription']["en"] == get_v2_response.json()['data']['SpecialityAttribute']['shortDescription']["en"]
    assert payload['disclaimer']["en"] == get_v2_response.json()['data']['SpecialityAttribute']['disclaimer']["en"]
    sns = get_sns_message_by_id_catalog(id)
    validate_specialityAttribute(sns)
    assert payload['shortDescription']["en"] == sns['description']["en"]
    assert payload['disclaimer']["en"] == sns['disclaimer']["en"]
    #### PATCH
    print("Testing PATCH Speciality attribute")
    random_data = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "shortDescription": {
            "en": "api_auto_speciality ドーナツ patch donuts_apidonuts_apidonuts_api2" +random_data,
        },
        "disclaimer": {
            "en": "api_auto_speciality disclaimer patch ドーナツ donuts_apidonuts_apidonuts_api2" +random_data,
        }
    }
    p_sp = patch_speciality_attibute(id, payload, session=store_http_session)
    assert p_sp.status_code == 200
    get_response = get_speciality_attribute(id=id, session=store_http_session)
    assert get_response.status_code == 200
    validate(instance=get_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributes'], resolver=ref_resolver)
    get_v2_response = get_speciality_attribute(session=store_http_session,id=id, params={"formatVersion":2})
    assert get_v2_response.status_code == 200
    validate(instance=get_v2_response.json(), schema=json_schema_file['components']['schemas']['SpecialityAttributesMulti'], resolver=ref_resolver)
    assert payload['shortDescription']["en"] == get_v2_response.json()['data']['SpecialityAttribute']['shortDescription']["en"]
    assert payload['disclaimer']["en"] == get_v2_response.json()['data']['SpecialityAttribute']['disclaimer']["en"]
    sns = get_sns_message_by_id_catalog(id)
    validate_specialityAttribute(sns)
    
    assert payload['shortDescription']["en"] == sns['description']["en"]
    assert payload['disclaimer']["en"] == sns['disclaimer']["en"]

    #### PUT
    print("Testing PUT Speciality attribute")
    random_data = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "shortDescription": {
            "en": "api_auto_speciality put ドーナツ donuts_apidonuts_apidonuts_api3" +random_data,
        },
        "disclaimer": {
            "en": "api_auto_speciality disclaimer put ドーナツ donuts_apidonuts_apidonuts_api3" +random_data,
        },
        "image": ""
    }
    p_sp = put_speciality_attibute(id, payload, session=store_http_session)
    assert p_sp.status_code == 400
    assert "Image cannot be blank. It is a mandatory field" == p_sp.json()['message']
    
    patch_data = {
        "image": ""
    }
    p_sp = patch_speciality_attibute(id, patch_data, session=store_http_session)
    assert p_sp.status_code == 400
    assert "Image cannot be blank. It is a mandatory field" == p_sp.json()['message']

    ### DELETE
    del_response = delete_speciality_attribute(id=id, session=store_http_session)
    assert del_response.status_code == 200
    assert "Speciality Attribute deleted successfully" == del_response.json()['message']
    sns = get_sns_message_by_id_catalog(id)
    assert id == sns['id']
    

@pytest.mark.reg
@pytest.mark.skipif(len(LANGUAGES_CODES_CONFIGURED) < 2, reason=" Multi-language not configured")
def test_post_product_formatVersion2_specialityAttribute(store_http_session, json_schema_file, ref_resolver, speciality_attribute_id):
    ''''
    Create a product with formatVersion2 with only required fields 
    '''
    uuid_name = uuid_random_name()
    payload = {
    "formatVersion": 2,
    "sku": "api_auto" + uuid_name,
    "name": {
        "en": "Donuts ドーナツ donuts_apidonuts_apidonuts_api" + uuid_name,
        "is": "Donuts",
        "hi": "डोनट्स नाम हिंदी में Donuts",
        "ja": "ドーナツ",
        "ar": " الكعك  DonutsArabi ",
        "he": "وصف قصير عبري Donuts",
        "mn": "Donuts-ийн монгол нэр - Mongolian",
        "id": "Nama donat"
    },
    "shortDescription": {
        "en": "Short description of donuts in english",
        "is": "Short description of donuts in english",
        "hi": "डोनट्स नाम हिंदी में&nbsp;<br />\ndonuts short desc hindi",
        "ja": "生地を揚げた菓子で、グレーズや<br />\n(Japanese&nbsp; desc)",
        "ar": "حلويات العجين المقلية، غالبًا مع طبقة زجاجية أو طبقة. short desc arabic",
        "he": "وصف قصير عبري Long desc",
        "mn": "Donuts-ийн монгол нэр sh<br />\nMongolian short description.",
        "id": "Nama donat dalam bahasa indonesia short"
    },
    "description": {
        "en": "Fried dough confections, often with glaze or toppings.",
        "vi": "Fried dough confections, often with glaze or toppings.",
        "is": "Fried dough confections, often with glaze or toppings.",
        "hi": "डोनट्स नाम हिंदी में&nbsp;डोनट्स नाम हिंदी में&nbsp;<br />\ndonuts long desc hindi",
        "ja": "生地を揚げた菓子で、グレーズやトッピングが添えられることが多い<br />\n(Japanese Long desc)",
        "ar": "<br />\nحلويات العجين المقلية، غالبًا مع طبقة زجاجية أو طبقة. desc arabic",
        "he": "وصف قصير عبري Short desc",
        "mn": "Donuts-ийн монгол нэр d<br />\nMongolian long description.",
        "id": "Nama donat dalam bahasa indonesia long"
    },
    "deliveryMethod": [
        "Onboard"
    ],
    "category": [
        TESTING_CATEGORY_ID
    ],
    "Attributes": {
        "productCategoryAttributes": [],
        "specialityAttributes": [speciality_attribute_id]
    },
    "productSet": "Simple",
    "storeProductId": "134A34",
    "productType": "Food and Beverage",
    "spotLight": True,
    "isPerishable": False,
    "requireShipping": True
    }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "shortDescription"  in sns
    assert "description" in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    
    payload['name']['en'] += "123"
    payload['description'] = {"en":"description "+uuid_name}
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "shortDescription" in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)



@pytest.mark.reg
def test_post_product_formatVersion2_remove_images_put(store_http_session, json_schema_file, ref_resolver, speciality_attribute_id):
    ''''
    Create a product with formatVersion2 with only required fields
    '''
    uuid_name = uuid_random_name()
    payload = {
    "formatVersion": 2,
    "sku": "api_auto" + uuid_name,
    "name": {
        "en": "Donuts ドーナツ donuts_apidonuts_apidonuts_api" + uuid_name
    },
    "description": {
        "en": "Fried dough confections, often with glaze or toppings."
    },
    "deliveryMethod": [
        "Onboard"
    ],
    "category": [
        TESTING_CATEGORY_ID
    ],
    "Attributes": {
        "productCategoryAttributes": [],
        "specialityAttributes": [speciality_attribute_id]
    },
    "productSet": "Simple",
    "storeProductId": "134A34",
    "productType": "Food and Beverage",
    "assets": {
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image2_3": "https://placehold.jp/944x1416.png",
                "image4_3": "https://placehold.jp/760x570.png",
                "image5_6": "https://placehold.jp/670x804.png"
            }
        ]
    },
    }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)

    ## Validate the images are present in GET api, and SNS...
    number_images = len(payload['assets']['images'][0])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(sns['images']) == number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    catch_duplicate_sns(id)
    ### Remove the images   
    payload["assets"]['images'][0]['image1_1'] = ""
    payload["assets"]['images'][0]['image2_1'] = ""
    payload["assets"]['images'][0]['image2_3'] = ""
    payload["assets"]['images'][0]['image4_3'] = ""
    payload["assets"]['images'][0]['image5_6'] = ""
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)
    ## CASE: RE-ADD IMAGES
    payload.update({
        "assets": {
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image2_3": "https://placehold.jp/944x1416.png",
                "image4_3": "https://placehold.jp/760x570.png",
                "image5_6": "https://placehold.jp/670x804.png"
            }
        ]
        },
    })
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'].startswith("http")
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)

    ## Validate the images are present in GET api, and SNS...
    number_images = len(payload['assets']['images'][0])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(sns['images']) ==  number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    ## CASE 2: remove some images with "" and None
    payload.update({
        "assets": {
            "images": [
                {
                    "image1_1": "",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image2_3": "https://placehold.jp/944x1416.png",
                    "image4_3": None,
                    "image5_6": "https://placehold.jp/670x804.png"
                }
            ]
        },
    })
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'].startswith("http")
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns and len(sns['images']) == 3, "There is missing images"
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    ## Validate the images are present in GET api, and SNS...
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    catch_duplicate_sns(id)

    

@pytest.mark.reg
def test_post_product_put_remove_data_usecases(store_http_session, json_schema_file, ref_resolver, speciality_attribute_id):
    ''''
    Create a product with formatVersion2 with only required fields
    '''
    uuid_name = uuid_random_name()
    payload = {
    "formatVersion": 2,
    "sku": "api_auto" + uuid_name,
    "name": {
        "en": "Donuts ドーナツ donuts_apidonuts_apidonuts_api" + uuid_name
    },
    "description": {
        "en": "Fried dough confections, often with glaze or toppings."
    },
    "deliveryMethod": [
        "Onboard"
    ],
    "category": [
        TESTING_CATEGORY_ID
    ],
    "Attributes": {
        "productCategoryAttributes": [],
        "specialityAttributes": [speciality_attribute_id]
    },
    "productSet": "Simple",
    "storeProductId": "134A34",
    "productType": "Food and Beverage",
    "assets": {
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image2_3": "https://placehold.jp/944x1416.png",
                "image4_3": "https://placehold.jp/760x570.png",
                "image5_6": "https://placehold.jp/670x804.png"
            }
        ],
        "gallery": [
            {
                "url": "https://placehold.jp/1480x740.png",
                "imagePosition": 1
            }
        ]
    },
    }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    ## Validate the images are present in GET api, and SNS...
    number_images = len(payload['assets']['images'][0])
    number_posters = len(payload['assets']['gallery'])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(product_get_r.json()["data"]['product']['assets']["gallery"]) == number_posters
    assert len(sns['images']) == number_posters + number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i

    ### CASE: Remove the images   
    payload["assets"]['images'][0]['image1_1'] = ""
    payload["assets"]['images'][0]['image2_3'] = ""
    payload["assets"]['images'][0]['image4_3'] = ""
    payload["assets"]['images'][0]['image5_6'] = ""
    payload["assets"]['gallery'][0]['url'] = ""
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert product_get_r.json()["data"]['product']['assets']["gallery"][0]['url'].startswith("http")
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == speciality_attribute_id
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns and len(sns['images']) == 2
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    assert "specialityAttributes" in sns and len(sns['specialityAttributes']) == 1
    assert sns['specialityAttributes'][0]['id'] == speciality_attribute_id
    catch_duplicate_sns(id)

    ### CASE: lets modify spa
    r = get_speciality_attribute(session=store_http_session, limit=5)
    spa_data = r.json()
    spa_4 = spa_data['data']['SpecialityAttributes'][3]['id']

    payload.update({
        "Attributes": {
            "productCategoryAttributes": [],
            "specialityAttributes": [spa_4]
    },
    })
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == spa_4
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert "specialityAttributes" in sns and len(sns['specialityAttributes']) == 1
    assert sns['specialityAttributes'][0]['id'] == spa_4
    assert 'images' in sns
    assert len(sns['images']) == 2
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)

    ### CASE: remove spa
    payload.update({
        "Attributes": {
        "productCategoryAttributes": [],
        "specialityAttributes": []
    },
    })
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 0
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert "specialityAttributes" not in sns 
    assert 'images' in sns
    assert len(sns['images']) == 2
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)


def test_post_product_put_modify_remove_productCategoryAttributes(store_http_session, json_schema_file, ref_resolver, speciality_attribute_id):
    ''''
    Create a product with storeSpecificAttributes, then using put modify it and then remove it
    '''
    uuid_name = uuid_random_name()
    payload = {
    "formatVersion": 2,
    "sku": "api_auto" + uuid_name,
    "name": {
        "en": "Donuts ドーナツ donuts_apidonuts_apidonuts_api" + uuid_name
    },
    "description": {
        "en": "Fried dough confections, often with glaze or toppings."
    },
    "deliveryMethod": [
        "Onboard"
    ],
    "category": [
        TESTING_CATEGORY_ID
    ],
    "Attributes": {
        "storeSpecificAttributes": [
            {
                "key": "atribute",
                "value": "atribute value"
            }
        ],
        "specialityAttributes": [speciality_attribute_id]
    },
    "productSet": "Simple",
    "storeProductId": "134A34",
    "productType": "Food and Beverage",
    "assets": {
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image2_3": "https://placehold.jp/944x1416.png",
                "image4_3": "https://placehold.jp/760x570.png",
                "image5_6": "https://placehold.jp/670x804.png"
            }
        ],
        "gallery": [
            {
                "url": "https://placehold.jp/1480x740.png",
                "imagePosition": 1
            }
        ]
    },
    }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    assert product_get_r.json()["data"]['product']['Attributes']["storeSpecificAttributes"][0]['attributeName'] == payload['Attributes']['storeSpecificAttributes'][0]['key']
    assert product_get_r.json()["data"]['product']['Attributes']["storeSpecificAttributes"][0]['attributeValue'] == payload['Attributes']['storeSpecificAttributes'][0]['value']
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    ## Validate the images are present in GET api, and SNS...
    number_images = len(payload['assets']['images'][0])
    number_posters = len(payload['assets']['gallery'])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(product_get_r.json()["data"]['product']['assets']["gallery"]) == number_posters
    assert len(sns['images']) == number_posters + number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    assert sns['storeSpecificAttributes'][0]['attributeName'] == payload['Attributes']['storeSpecificAttributes'][0]['key']
    assert sns['storeSpecificAttributes'][0]['attributeValue'] == payload['Attributes']['storeSpecificAttributes'][0]['value']
    catch_duplicate_sns(id)
        ## modify attribute
    payload['Attributes']['storeSpecificAttributes'][0]['key'] = "test_key2"
    payload['Attributes']['storeSpecificAttributes'][0]['value'] = "test_value2"
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    assert product_get_r.json()["data"]['product']['Attributes']["storeSpecificAttributes"][0]['attributeName'] == payload['Attributes']['storeSpecificAttributes'][0]['key']
    assert product_get_r.json()["data"]['product']['Attributes']["storeSpecificAttributes"][0]['attributeValue'] == payload['Attributes']['storeSpecificAttributes'][0]['value']
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    ## Validate the images are present in GET api, and SNS...
    number_images = len(payload['assets']['images'][0])
    number_posters = len(payload['assets']['gallery'])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(product_get_r.json()["data"]['product']['assets']["gallery"]) == number_posters
    assert len(sns['images']) == number_posters + number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    assert sns['storeSpecificAttributes'][0]['attributeName'] == payload['Attributes']['storeSpecificAttributes'][0]['key']
    assert sns['storeSpecificAttributes'][0]['attributeValue'] == payload['Attributes']['storeSpecificAttributes'][0]['value']
    catch_duplicate_sns(id)

    ## remove it
    payload['Attributes']['storeSpecificAttributes'] = []
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200

    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    ## Validate the images are present in GET api, and SNS...
    number_images = len(payload['assets']['images'][0])
    number_posters = len(payload['assets']['gallery'])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(product_get_r.json()["data"]['product']['assets']["gallery"]) == number_posters
    assert len(sns['images']) == number_posters + number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    assert "storeSpecificAttributes" not in sns
    catch_duplicate_sns(id)

@pytest.mark.reg
def test_post_product_patch_data_removal_usecases(store_http_session, json_schema_file, ref_resolver, speciality_attribute_id):
    ''''
    Create a product with formatVersion2 with only required fields
    '''
    uuid_name = uuid_random_name()
    payload = {
    "formatVersion": 2,
    "sku": "api_auto" + uuid_name,
    "name": {
        "en": "Donuts ドーナツ donuts_apidonuts_apidonuts_api" + uuid_name
    },
    "description": {
        "en": "Fried dough confections, often with glaze or toppings."
    },
    "deliveryMethod": [
        "Onboard"
    ],
    "category": [
        TESTING_CATEGORY_ID
    ],
    "Attributes": {
        "productCategoryAttributes": [],
        "specialityAttributes": [speciality_attribute_id]
    },
    "productSet": "Simple",
    "storeProductId": "134A34",
    "productType": "Food and Beverage",
    "assets": {
        "images": [
            {
                "image1_1": "https://placehold.jp/1200x1200.png",
                "image2_1": "https://placehold.jp/1480x740.png",
                "image2_3": "https://placehold.jp/944x1416.png",
                "image4_3": "https://placehold.jp/760x570.png",
                "image5_6": "https://placehold.jp/670x804.png"
            }
        ]
    },
    }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)

    ## Validate the images are present in GET api, and SNS...
    number_images = len(payload['assets']['images'][0])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert len(sns['images']) ==  number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    catch_duplicate_sns(id)
    ### CASE 1: Remove the images   
    payload_patch = {
        "assets": {
            "images": [
                {
                    "image1_1": "",
                    "image2_1": "",
                    "image2_3": "",
                    "image4_3": "",
                    "image5_6": ""
                }
            ]
        }
    }
    put_product_r = patch_product(store_http_session, id=id, data=payload_patch)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == speciality_attribute_id
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)

    put_response = put_product(store_http_session, id, payload)
    assert put_response.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns
    assert "shortDescription" not in sns
    assert "description" in sns
    assert "brand" not in sns
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)

    ## Validate the images are present in GET api, and SNS...
    number_images = len(payload['assets']['images'][0])
    assert len(product_get_r.json()["data"]['product']['assets']["images"][0]) == number_images
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'].startswith("http")
    assert len(sns['images']) ==   number_images
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    catch_duplicate_sns(id)

    ### CASE 2: Remove some images   only
    payload_patch = {
        "assets": {
            "images": [
                {
                    "image1_1": "",
                    "image2_1": "",
                    "image4_3": "",
                }
            ]
        }
    }
    put_product_r = patch_product(store_http_session, id=id, data=payload_patch)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'].startswith("http")
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == speciality_attribute_id
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns 
    assert len(sns['images']) == 2
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)
    ### CASE 3: Remove some images using Null  
    payload_patch = {
        "assets": {
            "images": [
                {
                    "image2_3": None,
                }
            ]
        }
    }
    put_product_r = patch_product(store_http_session, id=id, data=payload_patch)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'].startswith("http")
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == speciality_attribute_id
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' in sns 
    assert len(sns['images']) == 1
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)
    ### CASE 4: Remove some images using Null  and ""
    payload_patch = {
        "assets": {
            "images": [
                {
                    "image1_1": "",
                    "image2_3": None,
                    "image5_6": None,
                }
            ]
        }
    }
    put_product_r = patch_product(store_http_session, id=id, data=payload_patch)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == speciality_attribute_id
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images' not in sns
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)
    ### CASE 4: Add images
    payload_patch = {
        "assets": {
            "images": [
                {
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image5_6": None,
                }
            ]
        }
    }
    put_product_r = patch_product(store_http_session, id=id, data=payload_patch)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == speciality_attribute_id
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert 'images' in sns
    assert len(sns['images']) == 1
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)
    ### CASE 5: Add gallery images
    payload_patch= {
        "assets":{ 
            "gallery": [
                    {
                        "url": "https://placehold.jp/1480x740.png",
                        "imagePosition": 1
                    }
                ]
            }
        }
    r = patch_product(store_http_session, id=id, data=payload_patch)
    assert r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == speciality_attribute_id
    validate_multi_lang(payload['name'], get_v2_product.json()["data"]['product']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert 'images' in sns
    assert len(sns['images']) == 2
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)

    ### CASE: lets add 2 spepciality attribute, then remove it
    r = get_speciality_attribute(session=store_http_session, limit=5)
    spa_data = r.json()
    spa_1 = spa_data['data']['SpecialityAttributes'][0]['id']
    spa_2 = spa_data['data']['SpecialityAttributes'][1]['id']
    spa_3 = spa_data['data']['SpecialityAttributes'][2]['id']
    spa_4 = spa_data['data']['SpecialityAttributes'][3]['id']

    payload_patch = {
            "Attributes": {
            "specialityAttributes": [spa_1, spa_2, spa_3],
            "productCategoryAttributes": []
        },
    }
    r = patch_product(store_http_session, id=id, data=payload_patch)
    assert r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 3
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert "specialityAttributes" in sns and len(sns['specialityAttributes']) == 3
    assert 'images' in sns
    assert len(sns['images']) == 2
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)

    ### CASE: remove 3 to 1 spa
    payload_patch = {
            "Attributes": {
                "specialityAttributes": [ spa_3],
                "productCategoryAttributes": []
            },
    }
    r = patch_product(store_http_session, id=id, data=payload_patch)
    assert r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == spa_3
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert "specialityAttributes" in sns
    assert len(sns['specialityAttributes']) == 1
    assert sns['specialityAttributes'][0]['id'] == spa_3
    assert 'images' in sns
    assert len(sns['images']) == 2
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)

    ### CASE: change spa
    payload_patch = {
            "Attributes": {
                "specialityAttributes": [ spa_4],
                "productCategoryAttributes": []
            },
    }
    r = patch_product(store_http_session, id=id, data=payload_patch)
    assert r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 1
    assert int(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"][0]['id']) == spa_4
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert "specialityAttributes" in sns 
    assert len(sns['specialityAttributes']) == 1
    assert sns['specialityAttributes'][0]['id'] == spa_4
    assert 'images' in sns
    assert len(sns['images']) == 2
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)

   ### CASE: Remove spa
    payload_patch = {
            "Attributes": {
                "specialityAttributes": [],
                "productCategoryAttributes": []
            },
    }
    r = patch_product(store_http_session, id=id, data=payload_patch)
    assert r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image1_1'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_1'].startswith("http")
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image2_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image4_3'] == None
    assert product_get_r.json()["data"]['product']['assets']["images"][0]['image5_6'] == None
    assert len(product_get_r.json()["data"]['product']['Attributes']["specialityAttributes"]) == 0 ### NEEDS To fix
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert "shortDescription" not in sns
    assert "brand" not in sns
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    assert "specialityAttributes" not in sns 
    assert 'images' in sns
    assert len(sns['images']) == 2
    for i in sns["images"]:
        assert 'S3bucketlocation' in i
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)
    catch_duplicate_sns(id)


def test_post_product_formatVersion2_specialityAttribute_sia(store_http_session, json_schema_file, ref_resolver, speciality_attribute_id):
    ''''
    Create a product with formatVersion2 similar to SIA
    '''
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_autoasdfasdf" +uuid_name,
        "name": {
            "en": "Donuts donuts_apidonuts_apidonuts_api" + uuid_name
        },
        "shortDescription": {
            "en": "Short description of donuts in english"
        },
        "description": {
            "en": "Fried dough confections, often with glaze or toppings"
        },
        "deliveryMethod": [
            "Onboard"
        ],
        "category": [
            TESTING_CATEGORY_ID
        ],
        "assets": {
            "images": [
                {
                    "image1_1": "",                
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image4_3": "https://placehold.jp/760x570.png"
                }
            ],
            "gallery": []
        },
        "Attributes": {
            "specialityAttributes": [speciality_attribute_id],
            "productCategoryAttributes": []
        },
        "productSet": "Simple",
        "productType": "Food and Beverage",
        "spotLight": True,
    }
    product_response = post_product(session=store_http_session, data=payload, sku="s")
    assert product_response.status_code == 200
    id = product_response.json()["id"]
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images'  in sns
    assert "shortDescription" in sns
    assert "description" in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert "specialityAttributes" in sns
    assert "id" in sns['specialityAttributes'][0]
    assert "description" in sns['specialityAttributes'][0]
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    
    payload['name']['en'] += "123"
    payload['description'] = {"en":"description "+uuid_name}
    put_product_r = put_product(store_http_session, id=id, data=payload)
    assert put_product_r.status_code == 200
    product_get_r = get_product(id=id, session=store_http_session)
    assert product_get_r.status_code == 200
    validate(instance=product_get_r.json(), schema=json_schema_file['components']['schemas']['productResponseId'], resolver=ref_resolver)
    get_v2_product = get_product(session=store_http_session,id=id, formatVersion2=True)
    assert get_v2_product.status_code == 200
    validate(instance=get_v2_product.json(), schema=json_schema_file['components']['schemas']['productMultiResponseId'], resolver=ref_resolver)
    assert product_get_r.status_code ==200
    sns = get_sns_message_by_id_catalog(id)
    validate_product(sns)
    assert 'images'  in sns
    assert "shortDescription"  in sns
    assert "brand_id" not in sns or sns["brand_id"] == None
    assert payload['sku'] == sns['sku']
    assert sns['deliveryMethod'][0]['name'] == 'inflightCurrent'
    assert payload['productSet'] == sns['productSet']
    assert payload['productType'] == sns['productType']
    validate_multi_lang(payload['name'], sns['title'], LANGUAGES_CODES_CONFIGURED)
    validate_multi_lang(payload['description'], sns['description'], LANGUAGES_CODES_CONFIGURED)



@pytest.mark.perfomance
@pytest.mark.reg
def test_same_sku_concurrently(store_http_session):
    uuid_name = uuid_random_name()
    payload = {
        "formatVersion": 2,
        "sku": "api_autoasdfasdf" +uuid_name,
        "name": {
            "en": "Honey Roasted Chicken Thigh with Tarragon Sauce"
        },
        "shortDescription": {
            "en": "Mashed potato, carrot and broccoli"
        },
        "description": {
            "en": "Mashed potato, carrot and broccoli"
        },
        "deliveryMethod": [
            "Onboard"
        ],
        "assets": {
            "images": [
                {
                    "image1_1": "",
                    "image2_1": "https://placehold.jp/1480x740.png",
                    "image4_3": "https://placehold.jp/760x570.png"
                }
            ],
            "gallery": []
        },
        "Attributes": {
            "specialityAttributes": [],
            "storeSpecificAttributes": []
        },
        "productSet": "Simple",
        "category": [
            TESTING_CATEGORY_ID
        ],
        "productType": "Food and Beverage"
    }

    num_requests = 10
    def make_api_request(_):
        return post_product(session=store_http_session, data=payload)
    
    with ThreadPoolExecutor(max_workers=8) as executor:
        responses = list(executor.map(make_api_request, range(num_requests)))
    succesful_category_created = 0
    id_created = 0
    failed_to_create = 0
    for response in responses:
        post_json_response = response.json()
        if response.status_code == 200:
            succesful_category_created +=1
            if id_created == 0:
                id_created = post_json_response['id']
                assert "Product added successfully" in post_json_response['message']
            else:
                 assert id_created == post_json_response['id']
        else:
            if id_created == 0:
                id_created = post_json_response['id']
            else:
                assert id_created == post_json_response['id']
                assert "A product already exists with same sku and store." in post_json_response['message']
            failed_to_create +=1
    assert succesful_category_created == 1
    get_sns_message_by_id_catalog(id_created, CATALOG_SQS_QUEUE_URL)

@pytest.fixture(scope="session")
def sector_ids(airline_session):
    get_se = get_sector(airline_session)
    assert get_se.status_code == 200
    sector_id1 = get_se.json()['data']['sectors'][0]['id']
    sector_id2 = get_se.json()['data']['sectors'][1]['id']
    sector_id3 = get_se.json()['data']['sectors'][3]['id']
    return [sector_id1, sector_id2, sector_id3]

def test_flight_concurrent(airline_session, sector_ids):
    unique_flight = "api_auto" + uuid_random_name()
    data_flight = {
    "flightRouteNumber": unique_flight,
    "flightBeginDateTime": "2026-01-02 02:00",
    "flightEndDateTime": "2050-01-07 17:35",
    "sectors": [
            {
            "id": sector_ids[0],
            "sequence": 1
            },
            {
            "id": sector_ids[1],
            "sequence": 2
            }
        ]
    }

    num_requests = 10
    def make_api_request(_):
        return post_flight(airline_session, data_flight)
    
    with ThreadPoolExecutor(max_workers=8) as executor:
        responses = list(executor.map(make_api_request, range(num_requests)))
    succesful_category_created = 0
    id_created = 0
    failed_to_create = 0
    for response in responses:
        jsonResponse = response.json()
        if response.status_code == 200:
            succesful_category_created +=1
            if id_created == 0:
                id_created = jsonResponse['id']
                assert "Flight added successfully" in jsonResponse['message']
            else:
                 assert id_created == jsonResponse['id']
        else:
            if id_created == 0:
                id_created = jsonResponse['id']
            else:
                assert id_created == jsonResponse['id']
                assert "Flight already present with the same route number and begin date-time." in jsonResponse['message']
            failed_to_create +=1
    assert succesful_category_created == 1
    get_sns_message_by_id_catalog(id_created, FLIGHT_SQS_QUEUE_URL)

@pytest.mark.reg
def test_flight_concurrent_no_dates(airline_session, sector_ids):
    unique_flight = "api_auto" + uuid_random_name()
    data_flight = {
    "flightRouteNumber": unique_flight,
    "sectors": [
            {
            "id": sector_ids[0],
            "sequence": 1
            },
            {
            "id": sector_ids[1],
            "sequence": 2
            }
        ]
    }

    num_requests = 10
    def make_api_request(_):
        return post_flight(airline_session, data_flight)
    with ThreadPoolExecutor(max_workers=8) as executor:
        responses = list(executor.map(make_api_request, range(num_requests)))
    succesful_category_created = 0
    id_created = 0
    failed_to_create = 0
    for response in responses:
        jsonResponse = response.json()
        if response.status_code == 200:
            succesful_category_created +=1
            if id_created == 0:
                id_created = jsonResponse['id']
                assert "Flight added successfully" in jsonResponse['message']
            else:
                 assert id_created == jsonResponse['id']
        else:
            if id_created == 0:
                id_created = jsonResponse['id']
            else:
                assert id_created == jsonResponse['id']
                assert "A published flight already exists with same route number for this airline" in jsonResponse['message']
            failed_to_create +=1
    assert succesful_category_created == 1
    get_sns_message_by_id_catalog(id_created, FLIGHT_SQS_QUEUE_URL)


@pytest.mark.reg
@pytest.mark.skipif(len(LANGUAGES_CODES_CONFIGURED) < 2, reason="SIA Configuration not setup for auto-map required for the test")
def test_route_catalog_concurrent_sector_with_flight(airline_session, store_http_session, json_schema_file, ref_resolver, sector_ids):
    get_catalog_response = get_catalog(session=store_http_session, limit=1 )
    assert get_catalog_response.status_code == 200
    get_catalog_response_json =  get_catalog_response.json()
    catalog_id = get_catalog_response_json['data']["catalogs"][0]['id']
    # catalog_id = 69270223 #### HARD CODED TEST DATA FROM DEV ENV

    catalog_id = int(catalog_id)
    route_catalog_post_data = {
    "name": f"api_auto{uuid_random_name()}",
    "catalog": {
        "id": catalog_id
    },
    "sector":[
        {
            "id":sector_ids[0]
        }]
    }

    num_requests = 10
    def make_api_request(_):
        return post_route_catalog(airline_session, route_catalog_post_data)
    with ThreadPoolExecutor(max_workers=8) as executor:
        responses = list(executor.map(make_api_request, range(num_requests)))
    succesful_category_created = 0
    id_created = 0
    failed_to_create = 0
    for response in responses:
        jsonResponse = response.json()
        if response.status_code == 200:
            succesful_category_created +=1
            if id_created == 0:
                id_created = jsonResponse['id']
                assert "Catalog assigned successfully. Please note that it may take some time for the Airline Categories to populate after creation." in jsonResponse['message']
            else:
                 assert id_created == jsonResponse['id']
        else:
            if id_created == 0:
                id_created = jsonResponse['id']
            else:
                assert id_created == jsonResponse['id']
                assert "Catalog Assignment with the same name already exists." in jsonResponse['message']
            failed_to_create +=1
    assert succesful_category_created == 1
    ca_mapped = False
    try:
        route_catalog_workflow_helper(airline_session, id_created, 50)
        ca_mapped = True
    except:
        logger.warning("Catalog asignment was not able to get Mapped")
    get_route_catalog_response = get_route_catalog(session=airline_session, id=id_created)
    assert get_route_catalog_response.status_code == 200
    validate(instance=get_route_catalog_response.json(), schema=json_schema_file['components']['schemas']['routeCatalogResponseId'], resolver=ref_resolver)
    if ca_mapped:
        sns = download_sns(id= id_created )
        assert "associateFlight" in sns
        validate_catalogAssigment(sns)

@pytest.mark.reg
def test_sectors_concurrent_same_name(airline_session):
    get_air = get_airports(airline_session)
    assert get_air.status_code ==200, "Fetching airports failed"
    airport1 = get_air.json()['data']['airports'][0]['icaoCode']
    airport2 = get_air.json()['data']['airports'][1]['icaoCode']
    sec_data = {
    "sectorName": "api_auto_sector_"+uuid_random_name(),
    "destination": airport1,
    "origin": airport2,
    }
    num_requests = 10
    def make_api_request(_):
        return post_sector(airline_session, sec_data)
    with ThreadPoolExecutor(max_workers=7) as executor:
        responses = list(executor.map(make_api_request, range(num_requests)))
    succesful_category_created = 0
    id_created = 0
    failed_to_create = 0
    for response in responses:
        jsonResponse = response.json()
        if response.status_code == 200:
            succesful_category_created +=1
            if id_created == 0:
                id_created = jsonResponse['id']
                assert "Sector added successfully." in jsonResponse['message']
            else:
                 assert id_created == jsonResponse['id']
        else:
            if id_created == 0:
                id_created = jsonResponse['id']
            else:
                assert id_created == jsonResponse['id']
                assert "Sector with name" in jsonResponse['message']
            failed_to_create +=1
    assert succesful_category_created == 1

@pytest.mark.xfail ## Currently not reported by SIA. Allowing to fail till fix is provided
def test_catalog_concurrent_same_name(store_http_session):
    get_product_response  = get_product(session=store_http_session, limit=3)
    assert get_product_response.status_code == 200
    products = get_product_response.json()['data']['products']
    skus = []
    for product in products:
        skus.append(product["sku"]) 

    payload = {
        "formatVersion": 2,
        "name": {
            "en": "api_auto " + uuid_random_name()
        },
        "products": skus
    }
    num_requests = 10
    def make_api_request(_):
        return post_catalog(store_http_session, payload)
    with ThreadPoolExecutor(max_workers=5) as executor:
        responses = list(executor.map(make_api_request, range(num_requests)))
    succesful_category_created = 0
    id_created = 0
    failed_to_create = 0
    for response in responses:
        jsonResponse = response.json()
        if response.status_code == 200:
            succesful_category_created +=1
            if id_created == 0:
                id_created = jsonResponse['id']
                assert "Catalog added successfully." in jsonResponse['message']
            else:
                 assert id_created == jsonResponse['id']
        else:
            if id_created == 0:
                id_created = jsonResponse['id']
            else:
                assert id_created == jsonResponse['id']
                assert "A published catalog already exists with same name." in jsonResponse['message']
            failed_to_create +=1
    assert succesful_category_created == 1
