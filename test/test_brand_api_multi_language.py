import time

import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from time import sleep
import os
from utils.helper import *
from utils.ui_helper import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from jamatest import jamatest
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from jsonschema import validate
import yaml
from jsonschema.validators import RefResolver
from datetime import date, datetime

@pytest.fixture(scope='session')
def speciality_attribute_id(store_http_session):
    r = get_speciality_attribute(session=store_http_session, limit=5)
    return r.json()['data']['SpecialityAttributes'][0]['id']
    

@pytest.mark.reg
def test_post_brand_no_images_default_language_only(store_http_session,json_schema_file, ref_resolver):
    """ Test payload version 2, without images. Validate name propagation to other languages"""
    payload =  {
        "name": {"en": "api_auto - " + uuid_random_name()}
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    ### GET Schema validation placeholder line
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    # assert 'image' not in sns

    ### SNS Schema validation placeholder line


def test_post_brand_no_images_some_languages(store_http_session,json_schema_file, ref_resolver):
    """Validate name propagation to other languages"""
    payload =  {
        "name": {
            "en": "api_auto - " + uuid_random_name(),
            LANGUAGES_CODES_CONFIGURED[1]: LANGUAGES_CODES_CONFIGURED[1] + "ソニー",
            LANGUAGES_CODES_CONFIGURED[3]: LANGUAGES_CODES_CONFIGURED[3] + "ソニー",
            }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    ###TODO GET Schema validation placeholder line
    sns = get_sns_message_by_id_catalog(id)
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    ### TODO: SNS Schema validation placeholder line


def test_post_brand_no_images_all_languages(store_http_session,json_schema_file, ref_resolver):
    """ Test payload version 2, without images. Validate name propagation to other languages"""
    payload =  {"name":{}}
    generate_multi_language(payload, LANGUAGES_CODES_CONFIGURED, ['name'])
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    ### TODO GET Schema validation placeholder line
    sns = get_sns_message_by_id_catalog(id)
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    ### SNS Schema validation placeholder line


def test_post_brand_1_1_image_some_languages(store_http_session,json_schema_file, ref_resolver):
    """ Test payload version 2, without images. Validate name propagation to other languages"""
    payload =  {
        "name": {
            "en": "api_auto - " + uuid_random_name(),
            LANGUAGES_CODES_CONFIGURED[1]: LANGUAGES_CODES_CONFIGURED[1] + "ソニー",
            LANGUAGES_CODES_CONFIGURED[3]: LANGUAGES_CODES_CONFIGURED[3] + "ソニー",
            },
        "assets": {
            "images": [
                {
                    "brand_image1_1": "https://placehold.jp/100x100.png",
                }
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    ### GET Schema validation placeholder line
    sns = get_sns_message_by_id_catalog(id)
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    ### SNS Schema validation placeholder line


def test_post_brand_all_images_all_languages(store_http_session,json_schema_file, ref_resolver):
    """ Test payload version 2, without images. Validate name propagation to other languages"""
    payload =  {"name":{}}
    generate_multi_language(payload, LANGUAGES_CODES_CONFIGURED, ['name'])
    payload['assets'] = {
            "images": [
                {
                    "brand_image1_1": "https://placehold.jp/100x100.png",
                    "brand_image16_9": "https://placehold.jp/178x100.png",
                }
            ]
        }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']
    assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    g_brand = get_brand(store_http_session, id)
    assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    ### GET Schema validation placeholder line
    sns = get_sns_message_by_id_catalog(id)
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    ### SNS Schema validation placeholder line

    #### TDOD add image validation in SNS and GTE




### Validations 




def test_post_brand_invalid_image_url(store_http_session,json_schema_file, ref_resolver):
    """ Test payload version 2, without images. Validate name propagation to other languages"""
    payload =  {"name":{}}
    generate_multi_language(payload, LANGUAGES_CODES_CONFIGURED, ['name'])
    payload .update({
        "assets": {
            "images": [
                {
                    "brand_image1_1": "lacehold.jp/100x100.png",
                }
            ]
        }
    })
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Image download: lacehold.jp/100x100.png failed with HTTP code 0" in p_json['message']
    # id = p_json['id']
    # assert p_json["success"] == True, "'success' is missing in json response if it's not True"
    # g_brand = get_brand(store_http_session, id)
    # assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    # get_json_response = g_brand.json()
    # validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    # ### GET Schema validation placeholder line
    # sns = get_sns_message_by_id_catalog(id)
    # validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    # ### SNS Schema validation placeholder line

def test_post_brand_invalid_image_resolution(store_http_session,json_schema_file, ref_resolver):
    """ Validate image when its less than required resolutino """
    payload =  {"name":{}}
    generate_multi_language(payload, LANGUAGES_CODES_CONFIGURED, ['name'])
    p_brand = post_brand(store_http_session, payload)
    payload = {
        "name":{
            "en" : "api_auto " + uuid_random_name()
        },
        "assets": {
            "images": [
                {
                    "brand_image1_1": "https://placehold.jp/90x90.png",
                }
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 400, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    #assert "Invalid brand_image1_1 image resolution. Resolution should be at least 100x100 with 1:1 ratio." == p_json['message']
    assert "Invalid brand_image1_1 resolution. Resolution should be at least 100x100 with 1:1 ratio." == p_json['message']



    assert p_json["success"] == False, "'success' is missing in json response or it's not False"


@jamatest.test(21458230)
def test_delete_brand(store_http_session):
    payload =  {
        "name": {"en": "api_auto - " + uuid_random_name()}
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully" in p_json['message']
    id = p_json['id']

    g_brand = get_brand(store_http_session, id)
    assert g_brand.status_code == 200, f"GET Brand API Failed for ID '{id}"
    get_json_response = g_brand.json()
    validate_multi_lang(payload['name'], get_json_response['data']['brand']['name'], LANGUAGES_CODES_CONFIGURED)
    sns = get_sns_message_by_id_catalog(id)
    validate_multi_lang(payload['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    delete_response = delete_brand(store_http_session, id)
    assert delete_response.status_code == 200, f"Failed to delete product ID '{id}'"
    assert "Brand deleted successfully" == delete_response.json()['message']
    sns = get_sns_message_by_id_catalog(id)
    assert sns["id"] == id and 'name' not in sns, "Failed to find the SNS Unpublish for brand"

@pytest.mark.reg
@jamatest.test(21314956)
def test_post_unsupported_language(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto - " + uuid_random_name(),
            "th": "api_auto - " + uuid_random_name()

        },
        "assets": {
            "images":[
                {
                    "brand_image1_1": "https://placehold.jp/100x100.png",
                    "brand_image16_9": "https://placehold.jp/178x100.png"
                }
            ]
        }
    }
    p_brand = post_brand(store_http_session, payload)
    assert p_brand.status_code == 200, f"POST Brand Failed. Non 200 status code"
    p_json = p_brand.json()
    assert "Brand added successfully [Warning]: Following languages [th] are not associated with this store. Information is not saved for these languages." in p_json['message']

    # TEST CASES PENDING TO AUTOMATE FOR PUT BRAND API-----

#--------------------------------MKTPL-9300 - PUT BRAND API CASES ----------------------------------------------

@pytest.mark.reg
@jamatest.test(21556437)
def test_put_brand_with_multilang_valid_names_valid_images_SNS(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name(),
            "zh_Hans": "api_自动化",
            "zh_Hant": "api_自動化",
            "ja": "API自動化",
            "es": "automatización"

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    g_brand = get_brand(session=store_http_session, id=id)
    sns = get_sns_message_by_id_catalog(id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)
    payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    payload_put['name']['es'] = "autotización" + uuid_random_name()
    payload_put['name']['ja'] = "自動自" + uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(p_brand.json())
    g_brand_json = g_brand.json()
    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert sns['image'], "SNS received an empty image list"
    validate_multi_lang(payload_put['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"Images received in SNS {sns['image']}")
    print(f"Images sent in Payload {payload['assets']['images']}")

@pytest.mark.reg
@jamatest.test(21556442)
def test_put_brand_with_invalid_images_url_with_valid_multilang_names(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name(),
            "zh_Hans": "api_自动化",
            "zh_Hant": "api_自動化",
            "ja": "API自動化",
            "es": "automatización"

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)
    # -----PASSING INVALID IMAGE URL FOR 1_1-----------

    payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()

    payload_put['assets']['images'][0]['brand_image1_1'] = "https://placeholsdsadd.jp/100x100.png"
    # payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    assert p_brand.status_code == 400, f"200 Code Received"
    p_json = p_brand.json()
    assert "Image download: https://placeholsdsadd.jp/100x100.png failed with HTTP code 0" in p_json[
        'message']

    # -----PASSING INVALID IMAGE URL FOR 16_9-----------
    payload_put_16_9 = copy.deepcopy(payload)
    payload_put_16_9['name']['en'] = "api_autoupdate" + uuid_random_name()

    payload_put_16_9['assets']['images'][0]['brand_image16_9'] = "https://placeholsdasdsad.jp/178x100.png"
    p_brand = put_brand(session=store_http_session, data=payload_put_16_9, id=id)
    assert p_brand.status_code == 400, f"200 Code Received"
    p_json = p_brand.json()
    assert "Image download: https://placeholsdasdsad.jp/178x100.png failed with HTTP code 0" in p_json[
        'message']
@pytest.mark.reg
@jamatest.test(21556439)
def test_put_brand_with_multilang_invalid_names_valid_images_SNS(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name(),
            "zh_Hans": "api_自动化fdsfsdf",
            "zh_Hant": "api_自動化fsdfsdf",
            "ja": "API自動化fdsfsd",
            "es": "automatizaciónfsdfsdfsdfsdf"

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)
    payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    payload_put['name']['ja'] = "API自動化自動化自動化自動化自動化"+ uuid_random_name()
    #payload_put['name']['es'] = "API自動化自動化自動化自動化自動化" + uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(p_brand.json())
    assert p_brand.status_code==400, f"Other than 400 found"
    p_json = p_brand.json()
    assert "Brand name field [ja] should not exceed 50 characters." in p_json['message']

@pytest.mark.reg
@jamatest.test(21556441)
def test_put_brand_with_multilang_invalid_names_invalid_images(store_http_session, json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name(),
            "zh_Hans": "api_自动化fdsfsdf",
            "zh_Hant": "api_自動化fsdfsdf",
            "ja": "API自動化fdsfsd",
            "es": "automatizaciónfsdfsdfsdfsdf"

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_put = copy.deepcopy(payload)
    # -----PASSING INVALID IMAGE URL FOR 1_1-----------
    payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    payload_put['name']['es'] = "api_autasdf" + uuid_random_name()
    payload_put['assets']['images'][0]['brand_image1_1'] = "https://placeholsdsadd.jp/100x100.png"
    # payload_put['name']['en'] = "api_autoupdate" + uuid_random_name()
    p_brand = put_brand(session=store_http_session, data=payload_put, id=id)
    assert p_brand.status_code == 400, f"200 Code Received"
    p_json = p_brand.json()
    assert "Image download: https://placeholsdsadd.jp/100x100.png failed with HTTP code 0" in p_json['message']

    # -----PASSING INVALID IMAGE URL FOR 16_9-----------
    payload_put_16_9 = copy.deepcopy(payload)
    payload_put_16_9['name']['en'] = "api_autoupdate" + uuid_random_name()

    payload_put_16_9['assets']['images'][0]['brand_image16_9'] = "https://placeholsdasdsad.jp/178x100.png"
    p_brand = put_brand(session=store_http_session, data=payload_put_16_9, id=id)
    assert p_brand.status_code == 400, f"200 Code Received"
    p_json = p_brand.json()
    assert "Image download: https://placeholsdasdsad.jp/178x100.png failed with HTTP code 0" in p_json[
        'message']

#----------------BRAND PATCH API CASES --------------------
@pytest.mark.reg
@jamatest.test(21599928)
def test_patch_brand_with_multilang_blank_name_valid_images(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name(),
            "ja": "japanese ",
            "es": "spanish"

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    # sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_patch = copy.deepcopy(payload)
    payload_patch['name']['en'] = "api_autoupdate" + uuid_random_name()
    payload_patch['name']['ja'] = ""

    # ----------LONG PAYLOAD IN NAME UPDATED USING PATCH-----------------

    p_brand = patch_brand(session=store_http_session, data=payload_patch, id=id)
    p_json = p_brand.json()
    #id = p_json['id']
    # sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    #p_json = p_brand.json()
    assert p_brand.status_code == 200, f"400 Received"
    p_json = p_brand.json()
    assert "Brand updated successfully" in p_json['message']

@pytest.mark.reg
@jamatest.test(21599930)
def test_patch_brand_with_multilang_valid_name_blank_images_SNS(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name(),
            "ja": "japanese",
            "es": "spanish"

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_patch = copy.deepcopy(payload)
    payload_patch['name']['en'] = "api_autoupdate" + uuid_random_name()
    # FIRST IMAGE AS BLANK
    payload_patch['assets']['images'][0]['brand_image1_1'] = ""
    p_brand = patch_brand(session=store_http_session, data=payload_patch, id=id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(p_brand.json())
    g_brand_json = g_brand.json()
    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert sns['image'], "SNS received an empty image list"
    validate_multi_lang(payload_patch['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    print(f"Images received in SNS {sns['image']}")
    print(f"Images sent in Payload {payload_patch['assets']['images']}")

    # SECOND IMAGE AS BLANK
    payload_patch_again = copy.deepcopy(payload)
    payload_patch_again['name']['en'] = "api_autoupdate" + uuid_random_name()
    # FIRST IMAGE AS BLANK
    payload_patch_again['assets']['images'][0]['brand_image16_9'] = ""
    payload_patch_again['assets']['images'][0]['brand_image1_1'] = ""

    p_brand = patch_brand(session=store_http_session, data=payload_patch_again, id=id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(p_brand.json())
    g_brand_json = g_brand.json()
    # ------VALIDATE SNS FOR BRAND IMAGES & NAME-----
    sns = get_sns_message_by_id_catalog(id)
    assert 'image' not in sns or sns['image'] is None, "SNS contains an unexpected image key"

    #assert sns['image'][0]['brand_image1_1'] == "", "SNS received an empty image list"
    #assert sns['image'][0]['brand_image16_9'] == "", "SNS received an empty image list"

    validate_multi_lang(payload_patch_again['name'], sns['name'], LANGUAGES_CODES_CONFIGURED)
    #print(f"Images received in SNS {sns['image']}")
    #print(f"Images sent in Payload {payload_patch_again['assets']['images']}")

@pytest.mark.reg
@jamatest.test(21599929)
def test_patch_brand_with_multilang_valid_names_valid_images_SNS(store_http_session,json_schema_file):
    payload = {
        "name": {
            "en": "api_auto" + uuid_random_name(),
            "ja": "japanese ",
            "es": "spanish"

        },
        "assets": {
            "images": [

                {"brand_image1_1": "https://placehold.jp/100x100.png",
                 "brand_image16_9": "https://placehold.jp/178x100.png"}
            ]
        }
    }
    p_brand = post_brand(session=store_http_session, data=payload)
    p_json = p_brand.json()
    id = p_json['id']
    # sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    payload_patch = copy.deepcopy(payload)
    payload_patch['name']['en'] = "api_autoupdate" + uuid_random_name()
    payload_patch['name']['ja'] = "apiautoj"+ uuid_random_name()
    payload_patch['name']['es'] = "apiautos"+ uuid_random_name()


    # ----------LONG PAYLOAD IN NAME UPDATED USING PATCH-----------------

    p_brand = patch_brand(session=store_http_session, data=payload_patch, id=id)
    p_json = p_brand.json()
    # id = p_json['id']
    # sns = get_sns_message_by_id_catalog(id)
    g_brand = get_brand(session=store_http_session, id=id)
    print(g_brand.json())
    # p_json = p_brand.json()
    assert p_brand.status_code == 200, f"400 Received"
    p_json = p_brand.json()
    assert "Brand updated successfully" in p_json['message']

