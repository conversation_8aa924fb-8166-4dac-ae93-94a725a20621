from flask import Flask, jsonify
from typing import <PERSON><PERSON>, Dict, Optional
from utils import helper as helper
from utils import config

app = Flask(__name__)


@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy'})

@app.route('/route-catalog/<id>')
def route_catalog_check(id):
    # found, error, response = find_message_by_id(item_id)
    try:
        sns = helper.get_sns_message_by_id_catalog(id)
        found_validation_error = False
        try: 
            helper.validate_catalogAssigment(sns)
        except Exception as e:
            print("schema validatio failed...")
            print(e)
            return jsonify({'error': f'Schema validation failed for {id}: ', }), 402
        return jsonify(sns), 200
    except:
        return jsonify({'error': f'Unable to find SNS Message for {id}', }), 400

@app.route('/product/<id>')
def product_check(id):
    # found, error, response = find_message_by_id(item_id)
    try:
        sns = helper.get_sns_message_by_id_catalog(id)
        found_validation_error = False
        try: 
            helper.validate_product(sns)
        except Exception as e:
            print("schema validatio failed...")
            print(e)
            return jsonify({'error': f'Schema validation failed for {id}: ', }), 402
        return jsonify(sns), 200
    except:
        return jsonify({'error': f'Unable to find SNS Message for {id}', }), 400


@app.route('/flight/<id>')
def flight_check(id):
    try:
        sns = helper.get_sns_message_by_id_catalog(id, config.FLIGHT_SQS_QUEUE_URL)
        found_validation_error = False
        try: 
            helper.validate_flight(sns)
        except Exception as e:
            print("schema validation failed...")
            print(e)
            return jsonify({'error': f'Schema validation failed for {id}: ', }), 402
        return jsonify(sns), 200
    except:
        return jsonify({'error': f'Unable to find SNS Message for {id}', }), 400


@app.route('/catalogAssigment/<id>')
def catalogAssigment_check(id):
    try:
        sns = helper.get_sns_message_by_id_catalog(id)
        found_validation_error = False
        try: 
            helper.validate_catalogAssigment(sns)
        except Exception as e:
            print("schema validation failed...")
            print(e)
            return jsonify({'error': f'Schema validation failed for {id}: ', }), 402
        return jsonify(sns), 200
    except:
        return jsonify({'error': f'Unable to find SNS Message for {id}', }), 400
    
    
@app.route('/airlineCategory/<id>')
def airlineCategory_check(id):
    try:
        sns = helper.get_sns_message_by_id_catalog(id)
        found_validation_error = False
        try: 
            helper.validate_airlineCategory(sns)
        except Exception as e:
            print("schema validation failed...")
            print(e)
            return jsonify({'error': f'Schema validation failed for {id}: ', }), 402
        return jsonify(sns), 200
    except:
        return jsonify({'error': f'Unable to find SNS Message for {id}', }), 400


@app.route('/specialityAttribute/<id>')
def specialityAttribute_check(id):
    try:
        sns = helper.get_sns_message_by_id_catalog(id)
        found_validation_error = False
        try: 
            helper.validate_specialityAttribute(sns)
        except Exception as e:
            print("schema validation failed...")
            print(e)
            return jsonify({'error': f'Schema validation failed for {id}: ', }), 402
        return jsonify(sns), 200
    except:
        return jsonify({'error': f'Unable to find SNS Message for {id}', }), 400
    
    
@app.route('/unpublish/<id>')
def unpublish_handler(id):
    try:
        sns = helper.get_sns_message_by_id_catalog(id)
        assert id == sns['id']
        return jsonify(sns), 200
    except Exception as e:
        print("Exception: ", e)
        return jsonify({'error': f'Error when finding unpublish for {id}', }), 400

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001)