{"openapi": "3.0.1", "info": {"title": "SNS Payload Definitions", "description": "JSON Schema definition for SNS Message payload These schemas will be followed while sending a message payload in SNS on pimcore events.", "version": "42"}, "paths": {"/airside/v1/store/config/": {"summary": "Get Store Config", "description": "Ignore this path as one path is required for swagger doc."}}, "components": {"schemas": {"Airline": {"required": ["id", "iataCode", "icaoCode", "airlineName", "operationStatus"], "properties": {"id": {"type": "integer", "example": 1, "description": "Returns the internal  id of an Airline"}, "iataCode": {"type": "string", "description": "Returns the IATA code associated with the airline"}, "icaoCode": {"type": "string", "description": "Returns the ICAO code associated with the airline"}, "airlineName": {"type": "object", "properties": {"en": {"type": "string", "description": "Airline Name in English language"}, "ja": {"type": "string", "description": "Airline Name in Japanese language"}, "it": {"type": "string", "description": "Airline Name in Italian language"}, "fr": {"type": "string", "description": "Airline Name in French language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "associatedStores": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 76}, "name": {"type": "object", "properties": {"en": {"type": "string", "example": "Walmart"}}, "required": ["en"], "additionalProperties": {"type": "string"}}}}}, "operationStatus": {"type": "string", "description": "Returns the airline's operation status such as active/inactive.", "enum": ["active", "inactive"]}, "associatedLanguages": {"type": "array", "example": ["en", "ja", "de"], "description": "associated airline store languages", "items": {"type": "string"}}}}, "Flights": {"type": "object", "require": ["flights"], "properties": {"flights": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["internalId", "flightRouteNumber", "sectors"], "properties": {"internalId": {"type": "string", "example": "67526267"}, "flightRouteNumber": {"type": "string", "example": "SQ11"}, "flightBeginDateTime": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-15 12:30", "description": "Returns the new from date time. It will be used to display new Tag in UI", "nullable": true}, "flightEndDateTime": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-25 17:30", "description": "Returns the new to date time . It will be used to remove new Tag in UI", "nullable": true}, "sectors": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["id", "sectorName", "origin", "destination", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 20437, "description": "Returns the internal  id of an sector"}, "sectorName": {"type": "string", "example": "AirIndia_sector", "description": "Sector Name"}, "summary": {"type": "string", "example": "AirIndia_sector"}, "routeGroup": {"type": "string", "example": "routegroup001"}, "origin": {"type": "string", "example": "OAIX"}, "destination": {"type": "string", "example": "OMDW,"}, "distance": {"type": "string", "example": "0 Miles,"}, "sequenceNumber": {"type": "integer", "example": 1}}}}}}}}}, "Sector": {"type": "object", "required": ["sequence", "origin", "destination"], "properties": {"origin": {"type": "string", "example": "LAX", "description": ""}, "destination": {"type": "string", "example": "NRT", "description": ""}, "startDateTime": {"type": "string", "example": "", "description": "scheduled start datetime of sector in UTC"}, "endDateTime": {"type": "string", "example": "", "description": "scheduled estimated arrival datetime of sector in UTC"}, "distance": {"type": "integer", "example": 1234, "description": "distance of the sector in miles"}, "sequence": {"type": "integer", "example": 1, "description": "the sequence order part of the flight plan"}}}, "AirlineCategory": {"required": ["airlineId", "id", "title", "createdAt", "publishedAt"], "properties": {"airlineId": {"type": "integer", "description": "Returns the ID number of the airline."}, "id": {"type": "integer", "description": "Returns the ID number of the airline category."}, "title": {"type": "object", "properties": {"en": {"type": "string", "description": "Title in English language"}, "ja": {"type": "string", "description": "Title in Japanese language"}, "it": {"type": "string", "description": "Title in Italian language"}, "fr": {"type": "string", "description": "Title in French language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "images": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 756, "description": "Returns the internal id of the image."}, "aspectRatio": {"type": "string", "example": "16:9", "description": "Returns the aspect ratio (width / height) of the image."}, "height": {"type": "integer", "example": "130", "description": "Returns the height of the image in pixels."}, "width": {"type": "integer", "example": 190, "description": "Returns the width of the image in pixels."}, "type": {"type": "string", "example": "main-1_1", "description": "Returns the type of the image on the basis of its aspect ratio and the associated module."}, "mediaType": {"type": "string", "example": "image", "description": "Returns the media type of the object. For the image object, this value will always be image."}, "S3bucketlocation": {"type": "object", "properties": {"bucketName": {"type": "string", "description": "Bucket name where the asset file is hosted"}, "key": {"type": "string", "description": "Key for the asset file"}}}}}}, "disclaimer": {"type": "object", "properties": {"en": {"type": "string", "description": "disclaimer in English language"}, "ja": {"type": "string", "description": "disclaimer in Japanese language"}, "it": {"type": "string", "description": "disclaimer in Italian language"}, "fr": {"type": "string", "description": "disclaimer in French language"}}}, "shortDescription": {"type": "object", "properties": {"en": {"type": "string", "description": "shortDescription in English language"}, "ja": {"type": "string", "description": "shortDescription in Japanese language"}, "it": {"type": "string", "description": "shortDescription in Italian language"}, "fr": {"type": "string", "description": "shortDescription in French language"}}}, "longDescription": {"type": "object", "properties": {"en": {"type": "string", "description": "longDescription in English language"}, "ja": {"type": "string", "description": "longDescription in Japanese language"}, "it": {"type": "string", "description": "longDescription in Italian language"}, "fr": {"type": "string", "description": "longDescription in French language"}}}, "url": {"type": "string", "example": "food", "description": "Returns the URL of the airline category."}, "templateName": {"type": "string", "example": "uitemplate", "description": "Returns the UI Template Name associated with the Airline Category"}, "rootType": {"type": "string", "example": "root", "description": "Returns the Root Type associated with the Airline Category"}, "sequenceNumber": {"type": "integer", "example": 4, "description": "Returns the Sequence Number associated with the Airline Category"}, "parentId": {"type": "integer", "description": "Returns the ID of the airline's Category Parent If its parent airline Category then value will be null.", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-13 17:56", "description": "Returns a timestamp for when a Airline Category was created on the Ground Tool"}, "publishedAt": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-13 00:00", "description": "Returns a timestamp for when a Airline Category was published from the Ground Tool          "}}}, "CatalogAssignment": {"required": ["id", "associateCatalog", "airline", "products"], "anyOf": [{"required": ["associateFlight"]}, {"required": ["associateSector"]}], "properties": {"id": {"type": "integer", "example": 1, "description": "Return internal ID of catalog asignment"}, "associateCatalog": {"type": "object", "required": ["id", "name", "storeId"], "properties": {"id": {"type": "integer", "example": 1, "description": "retrurn internal Id of catalog"}, "name": {"type": "object", "properties": {"en": {"type": "string", "description": "Catalog Name in English language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "storeId": {"type": "integer", "example": 2, "description": "Return internal ID of owner store"}}}, "fromDate": {"type": "string", "format": "date", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2})", "example": "2022-02-13", "description": "Returns the from date of Catalog"}, "toDate": {"type": "string", "format": "date", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2})", "example": "2022-02-19", "description": "Returns the to date of Catalog"}, "airline": {"required": ["id", "icaoCode", "name"], "properties": {"id": {"type": "integer", "example": 1, "description": "Returns the internal  id of an Airline"}, "icaoCode": {"type": "string", "example": "SIA", "description": "Returns ICAO Code for Airline,"}, "name": {"type": "object", "properties": {"en": {"type": "string", "description": "Airline Name in English language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}}}, "cabinClass": {"type": "array", "items": {"type": "object", "required": ["id", "name"], "properties": {"id": {"type": "integer", "example": 1, "description": "Returns the internal  id of the airport"}, "name": {"type": "object", "required": ["en"], "properties": {"en": {"type": "string", "description": "Cabin Class Name in English language"}}, "additionalProperties": {"type": "string"}}, "code": {"type": "string", "description": "Returns the cabin class unique human readable code", "nullable": true}}}}, "associateFlight": {"type": "array", "description": "Selected Flights for Route, Routegroup and Flight with Sectors Data. This field will be present when assignment Type is Flight or when associated Sector has some associated flights. One of associateFlight and associateSector is mandatory.", "minItems": 1, "items": {"type": "object", "required": ["id", "flightRouteNumber", "sectors", "statusCode", "airlineIATACode"], "properties": {"id": {"type": "integer", "description": "Returns the internal  id of the flights"}, "flightRouteNumber": {"type": "string", "description": "Returns the Flight Route number"}, "flightBeginDateTime": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-15T12:30:00.000Z", "description": "Returns the new from date time. It will be used to display new Tag in UI", "nullable": true}, "flightEndDateTime": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-25T17:30:00.000Z", "description": "Returns the new to date time . It will be used to remove new Tag in UI", "nullable": true}, "sectors": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["id", "name", "originAirportIATACode", "originAirportName", "destinationAirportIATACode", "destinationAirportName", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 1, "description": "Returns the internal  id of the sector"}, "name": {"type": "object", "properties": {"en": {"type": "string", "description": "Sector Name in English language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "originAirportIATACode": {"type": "string", "example": "SIN", "description": "Returns the origin airport IATA Code"}, "originAirportName": {"description": "Returns the origin airport Name", "type": "object", "properties": {"en": {"type": "string", "description": "Returns the origin airport Name in English language", "example": "Singapore International Airport"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "destinationAirportIATACode": {"type": "string", "example": "DXB", "description": "Returns the destination airport IATA Code"}, "destinationAirportName": {"description": "Returns the destination airport Name", "type": "object", "properties": {"en": {"type": "string", "description": "Returns the destination airport Name in English language", "example": "Dubai International Airport"}}, "required": ["en"], "additionalProperties": {"type": "string"}}}}}, "statusCode": {"type": "boolean", "description": "Returns the flight status code of the flights an optional field"}, "airlineIATACode": {"type": "string", "description": "Returns the airline IATA code associated with this flight."}}}}, "associateSector": {"type": "array", "description": "Selected Sectors.This parameter will be present when associated Sector does not have any flights assoicated with it. One of associateFlight and associateSector is mandatory.", "items": {"required": ["id", "name", "originAirportIATACode", "originAirportName", "destinationAirportIATACode", "destinationAirportName"], "properties": {"id": {"type": "integer", "example": 1, "description": "Returns the internal  id of the sector"}, "name": {"type": "object", "properties": {"en": {"type": "string", "description": "Sector Name in English language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "originAirportIATACode": {"type": "string", "example": "SIN", "description": "Returns the origin airport IATA Code"}, "originAirportName": {"description": "Returns the origin airport Name", "type": "object", "properties": {"en": {"type": "string", "description": "Returns the origin airport Name in English language", "example": "Singapore International Airport"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "destinationAirportIATACode": {"type": "string", "example": "DXB", "description": "Returns the destination airport IATA Code"}, "destinationAirportName": {"description": "Returns the destination airport Name", "type": "object", "properties": {"en": {"type": "string", "description": "Returns the destination airport Name in English language", "example": "Dubai International Airport"}}, "required": ["en"], "additionalProperties": {"type": "string"}}}}}, "products": {"type": "array", "description": "Returns the list of products from the related catalog with their optional metadata (quantity - used when a product is deployed for inflight sales and price - used to set a price that is different than price defined on the product object)", "items": {"properties": {"id": {"type": "integer", "example": 100, "description": "Unique ID of Product"}, "sku": {"type": "string", "example": "98763645ur234", "description": "Returns the SKU received from store"}, "pacSku": {"type": "string", "example": "PAC_Walmart_98763645ur234", "description": "PACSKU contains the unique identifier for the product object. The value for PACSKU is auto-generated by the Ground Tool when the object is created. The format of the value is PAC_STORENAME_SKU, where PAC is a static value which is appended before the “Store name” and the “SKU” provided by the store for the product."}, "price": {"oneOf": [{"$ref": "#/components/schemas/Price"}, {"$ref": "#/components/schemas/PriceList"}]}, "airlineCategory": {"type": "array", "items": {"type": "integer", "example": 2, "description": "Return Uniquie ID of Airline Category."}}}}}, "productSubstitutions": {"type": "object", "description": "ORProducts", "properties": {"<\"airline-category-id\">": {"type": "array", "description": "productId's", "items": {"type": "array", "items": {"type": "integer"}, "example": ["productId", "productId"]}}}, "additionalProperties": {"type": "array", "description": "productId's", "items": {"type": "array", "items": {"type": "integer"}, "example": ["productId", "productId"]}}}, "routeSequences": {"type": "object", "properties": {"categories": {"type": "array", "description": "Selected categories of catalog assignment.", "items": {"type": "object", "required": ["id", "sequenceNumber"], "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}], "properties": {"id": {"type": "integer", "example": 250, "description": "Selected category id"}, "sequenceNumber": {"type": "integer", "example": 1}, "subCategories": {"type": "array", "description": "selected sub categories", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 251}, "sequenceNumber": {"type": "integer", "example": 3}, "subCategories": {"type": "array", "items": {"$ref": "#/components/schemas/CatalogAssignment/properties/routeSequences/properties/categories/items/properties/subCategories/items"}}, "products": {"type": "array", "description": "Selected products of catalog assignment.", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 9568, "description": "selected product id"}, "sequenceNumber": {"type": "integer", "example": 2}}}}}, "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}]}}, "products": {"type": "array", "items": {"type": "object", "properties": {"productId": {"type": "integer"}, "sequenceNumber": {"type": "integer"}}, "required": ["productId", "sequenceNumber"]}}}}}}, "required": ["categories"]}, "createdAt": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-13 17:56", "description": "Returns a timestamp for when a Catalog Assignment was created on the Ground Tool"}, "publishedAt": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-13 00:00", "description": "Returns a timestamp for when a Catalog Assignment was published from the Ground Tool          "}}}, "Product": {"required": ["id", "title", "associateStore", "pacSku", "sku", "deliveryMethod", "isVariant", "productType", "productSet", "createdAt", "publishedAt", "url"], "properties": {"id": {"type": "integer", "example": 344, "description": "This is the Unique Identifier of the product. This id is auto-generated by Pimcore (Ground tool) when the object is created."}, "title": {"type": "object", "properties": {"en": {"type": "string", "example": "title  in English language"}, "ja": {"type": "string", "example": "title  in Japanese language"}, "it": {"type": "string", "example": "title  in Italian language"}, "fr": {"type": "string", "example": "title  in French language"}}, "required": ["en"], "additionalProperties": {"type": "string"}, "description": "Returns the title of the product"}, "shortDescription": {"type": "object", "properties": {"en": {"type": "string", "example": "shortDescription  in English language"}, "ja": {"type": "string", "example": "shortDescription  in Japanese language"}, "it": {"type": "string", "example": "shortDescription  in Italian language"}, "fr": {"type": "string", "example": "shortDescription  in French language"}}}, "description": {"type": "object", "properties": {"en": {"type": "string", "example": "description  in English language"}, "ja": {"type": "string", "example": "description  in Japanese language"}, "it": {"type": "string", "example": "description  in Italian language"}, "fr": {"type": "string", "example": "description  in French language"}}}, "associateStore": {"type": "integer", "example": 14, "description": "Return internal ID of associate store"}, "pacSku": {"type": "string", "example": "PAC_Walmart_A9574", "description": "PACSKU contains the unique identifier for the product object. The value for PACSKU is auto-generated by the Ground Tool when the object is created. The format of the value is PAC_STORENAME_SKU, where PAC is a static value which is appended before the “Store name” and the “SKU” provided by the store for the product."}, "sku": {"type": "string", "example": "98763645ur234", "description": "Returns the SKU received from store"}, "barcode": {"type": "string", "example": 7654389765428538, "description": "Returns barcode of product", "nullable": true}, "deliveryMethod": {"type": "array", "minItems": 1, "uniqueItems": true, "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 68, "description": "Unique Identifier of delivery type"}, "name": {"type": "string", "description": "Delivery Type", "example": "Shipping"}, "code": {"type": "string", "example": "IF", "description": "Delivery type code"}}, "required": ["name"]}}, "brand": {"type": "object", "properties": {"en": {"type": "string", "example": "brand  in English language"}, "ja": {"type": "string", "example": "brand  in Japanese language"}, "it": {"type": "string", "example": "brand  in Italian language"}, "fr": {"type": "string", "example": "brand  in French language"}}}, "nextFlightLeadTime": {"type": "integer", "example": 1, "description": "Lead time of order for next flight delivery in hours.", "nullable": true}, "gatePickupLeadTime": {"type": "integer", "example": 1, "description": "Lead time of order for Gate Pickup in hours.", "nullable": true}, "productType": {"type": "string", "example": "physical", "description": "The type of product", "enum": ["Food and Beverage", "Duty Free", "Amenities"]}, "notApplicableCountries": {"type": "array", "items": {"type": "string", "example": "IN"}, "description": "This list will contain a list of destination countries  where this product is not applicable to sell. This attribute overrides the attribute called \"notApplicableCountries\" defined in the catalog."}, "newFrom": {"type": "integer", "format": "epoch", "example": 1693917540, "description": "Returns the new from date time. It will be used to display new Tag in UI", "nullable": true}, "newTo": {"type": "integer", "format": "epoch", "example": 1757075940, "description": "Returns the new to date time . It will be used to remove new Tag in UI", "nullable": true}, "isVariant": {"type": "boolean", "example": true, "description": "Returns true if this product a Variant i.e. Actual Product", "nullable": true}, "spotlight": {"type": "boolean", "example": true, "description": "Returns true if this product is marked to be used for spotlight."}, "ageVerificationRequired": {"type": "boolean", "example": false, "description": "Returns true if age verification is required to purchase this product"}, "isAvailableToSell": {"type": "boolean", "example": true, "description": "Returns true if the product is available for sale"}, "requireShipping": {"type": "boolean", "example": false, "description": "Returns true if the product has to be shipped"}, "productSet": {"type": "string", "example": "Simple", "description": "Retruns Simple if Product type is simple product and does not have any variant. Returns Configurable if Product type is configurable and have variants.", "enum": ["Simple", "Configurable"]}, "images": {"type": "array", "items": {"type": "object", "required": ["id", "aspectRatio", "height", "width", "type", "mediaType", "S3bucketlocation"], "properties": {"id": {"type": "integer", "example": 756, "description": "Returns the internal id of the image."}, "aspectRatio": {"type": "string", "example": "16:9", "description": "Returns the aspect ratio (width / height) of the image."}, "height": {"type": "integer", "example": 130, "description": "Returns the height of the image in pixels."}, "width": {"type": "integer", "example": 190, "description": "Returns the width of the image in pixels."}, "type": {"type": "string", "example": "main-1_1", "description": "Returns the type of the image. Available types for product are image and gallery"}, "mediaType": {"type": "string", "example": "image", "description": "Returns the media type of the object. For the image object, this value will always be image."}, "position": {"type": "integer", "example": 1, "nullable": true, "description": "Returns the position of the image in the gallery. This field is applicable only for gallery type."}, "S3bucketlocation": {"type": "object", "required": ["bucketName", "key"], "properties": {"bucketName": {"type": "string", "description": "Bucket name where the asset file is hosted"}, "key": {"type": "string", "description": "Key for the asset file"}}}}}}, "price": {"$ref": "#/components/schemas/PriceList"}, "priceMax": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "number", "example": 55}, "currency": {"type": "string", "example": "USD"}}}, "description": "Returns the highest price of all the product's variants."}, "priceMin": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "number", "example": 55, "description": "Price Of Object"}, "currency": {"type": "string", "example": "USD"}}}, "description": "Returns the lowest price of all the product's varaints."}, "specialPrice": {"type": "array", "items": {"type": "object", "required": ["value", "currency"], "properties": {"value": {"type": "number", "example": 55, "description": "Special Price Of Product"}, "currency": {"type": "string", "example": "USD"}}}}, "isTaxable": {"type": "boolean", "example": true, "description": "Returns true if any tax is applicable to the product"}, "shippingWeight": {"type": "object", "properties": {"value": {"type": "integer", "example": "14", "description": "Returns numerical value for the Weight of the product, used for shipping"}, "unit": {"type": "string", "example": "KG", "description": "Returns the Unit of the Weight"}}}, "shippingLength": {"type": "object", "properties": {"value": {"type": "integer", "example": "12", "description": "Returns numerical value for the Length of the product, used for shipping"}, "unit": {"type": "string", "example": "CM", "description": "Returns the Unit of the Length"}}}, "shippingWidth": {"type": "object", "properties": {"value": {"type": "integer", "example": "13", "description": "Returns numerical value for the Width of the product, used for shipping"}, "unit": {"type": "string", "example": "CM", "description": "Returns the Unit of the Width"}}}, "shippingHeight": {"type": "object", "properties": {"value": {"type": "integer", "example": "14", "description": "Returns numerical value for the Height of the product, used for shipping"}, "unit": {"type": "string", "example": "KG", "description": "Returns the Unit of the Height"}}}, "isAlwaysInStock": {"type": "boolean", "example": true, "description": "Returns true if Product is always in stock. If this is true than no checks on inventory will be done."}, "maxPurchaseQuantity": {"type": "integer", "example": 5, "description": "Return the maximum allowed quantity a user can purchase in a single order", "nullable": true}, "minimumPurchaseQuantity": {"type": "integer", "example": 1, "description": "Return the minimum allowed quantity a user can purchase in one order", "nullable": true}, "categorySpecificAttributes": {"description": "Attributes specific to a category.", "type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/CategorySpecificNonLocalisedValue"}, {"$ref": "#/components/schemas/CategorySpecificLocalisedValue"}]}}, "variantAttributes": {"description": "Attributes specific for Variants.", "type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/CategorySpecificNonLocalisedValue"}, {"$ref": "#/components/schemas/CategorySpecificLocalisedValue"}]}}, "storeSpecificAttributes": {"description": "Custom Attributes specific for Store.", "type": "array", "items": {"type": "object", "properties": {"attributeName": {"type": "string"}, "attributeValue": {"type": "string"}}}}, "airlineSpecificAttributes": {"type": "array", "items": {"type": "object", "properties": {"icaoCode": {"type": "string", "example": "SIA"}, "mealCode": {"type": "array", "items": {"type": "string"}, "example": ["WMPL", "EVFG"]}}}}, "specialityAttributes": {"description": "Attributes specific to a product Speciality.", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 68, "description": "Unique Identifier of product SpecialityAttribute"}, "description": {"type": "object", "properties": {"en": {"type": "string", "example": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla non ipsum ligula. Ut quis turpis in mi fermentum auctor. Donec vel tincidunt velit. Donec ut ligula ac nisl ornare fringilla sit amet in lacus.", "description": "Description in English language"}, "jp": {"type": "string", "description": "Description in Japanese language"}, "it": {"type": "string", "description": "Description in Italian language"}, "fr": {"type": "string", "description": "Description in French language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "disclaimer": {"type": "object", "properties": {"en": {"type": "string", "example": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla non ipsum ligula. Ut quis turpis in mi fermentum auctor. Donec vel tincidunt velit. Donec ut ligula ac nisl ornare fringilla sit amet in lacus.", "description": "Disclaimer in English language"}, "jp": {"type": "string", "description": "Disclaimer in Japanese language"}, "it": {"type": "string", "description": "Disclaimer in Italian language"}, "fr": {"type": "string", "description": "Disclaimer in French language"}}}, "image": {"type": "array", "items": {"type": "object", "required": ["id", "aspectRatio", "height", "width", "mediaType", "S3bucketlocation"], "properties": {"id": {"type": "integer", "example": 756, "description": "Returns the internal id of the image."}, "aspectRatio": {"type": "string", "example": "16:9", "description": "Returns the aspect ratio (width / height) of the image."}, "height": {"type": "integer", "example": 130, "description": "Returns the height of the image in pixels."}, "width": {"type": "integer", "example": 190, "description": "Returns the width of the image in pixels."}, "mediaType": {"type": "string", "example": "image", "description": "Returns the media type of the object. For the image object, this value will always be image."}, "S3bucketlocation": {"type": "object", "required": ["bucketName", "key"], "properties": {"bucketName": {"type": "string", "description": "Bucket name where the asset file is hosted"}, "key": {"type": "string", "description": "Key for the asset file"}}}}}}}}}, "storeProductId": {"type": "string", "example": 123, "nullable": true, "description": "Returns Unique identifier for product received from store"}, "url": {"type": "string", "example": "https://panasonic.qa.credencys.net/admin/login/deeplink?object_24_object", "description": "Returns the relative URL of the product that can be accessed on the ground servers"}, "createdAt": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-13 17:56", "description": "Returns a timestamp for when a Product was created on the Ground Tool"}, "publishedAt": {"type": "string", "format": "date-time", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2}) ([01]?[0-9]|2[0-3]):[0-5][0-9]", "example": "2022-02-13 00:00", "description": "Returns a timestamp for when a Product was published from the Ground Tool          "}, "baseProductId": {"type": "string", "description": "Returns Unique ID of base product if it is variant product or it will return null"}}}, "Store": {"required": ["id", "basedata"], "properties": {"id": {"type": "integer", "example": 7}, "basedata": {"type": "object", "required": ["name", "address", "email"], "properties": {"name": {"type": "object", "properties": {"en": {"type": "string", "example": "name  in English language"}, "ja": {"type": "string", "example": "name  in Japanese language"}, "it": {"type": "string", "example": "name  in Italian language"}, "fr": {"type": "string", "example": "name  in French language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "address": {"properties": {"firstName": {"type": "string", "description": "Return FirstName"}, "lastName": {"type": "string", "description": "Return LastName"}, "addressLine1": {"type": "string", "description": "Return Address Line 1"}, "addressLine2": {"type": "string", "description": "Return Address Line 2"}, "city": {"type": "string", "description": "Return City"}, "state": {"type": "string", "description": "Return State"}, "zipCode": {"type": "string", "description": "Return ZIP code"}, "country": {"type": "string", "description": "Return Country"}}, "type": "object"}, "description": {"type": "object", "properties": {"en": {"type": "string", "example": "description  in English language"}, "ja": {"type": "string", "example": "description  in Japanese language"}, "it": {"type": "string", "example": "description  in Italian language"}, "fr": {"type": "string", "example": "description  in French language"}}}, "email": {"type": "string", "description": "Return email address of store"}, "phone": {"type": "string", "example": 0, "description": "Return phone number of store"}, "orderProvider": {"type": "string", "description": "Returns PAC OMS for the store"}, "orderThreshold": {"type": "string", "description": "Returns Order Thershold Value", "example": "50 USD"}, "bypassPACPayment": {"type": "boolean"}}}, "deliveryRules": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "example": "shipping", "description": "Type of shipping. Only shipping is available in current Scope", "enum": ["shipping", "inflightFuture"]}, "shippingRule": {"type": "array", "items": {"type": "object", "properties": {"grouping": {"type": "string", "example": "fulfillment_type", "description": "Type of grouping. Only fulfillment_type is available in current Scope", "enum": ["fulfillment_type", "basket", "basket_item"]}, "shippingDestinations": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "conditions": {"type": "object", "properties": {"deliveryType": {"type": "object", "properties": {"any": {"type": "array", "items": {"type": "string", "example": "homeShippingDomesticStandard"}}}}}}}}}, "methods": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "example": "standard"}, "title": {"type": "array", "items": {"type": "object", "properties": {"lang": {"type": "string"}, "value": {"type": "string"}}}}, "description": {"type": "array", "items": {"type": "object", "properties": {"lang": {"type": "string"}, "value": {"type": "string"}}}}, "price": {"type": "object", "properties": {"USD": {"type": "number", "description": "USD is currency Code. It is fixed to USD in current Scope"}}}, "duration": {"type": "object", "properties": {"days": {"type": "integer", "description": "days/weeks is allowed option here"}}}}}}}}}}}}, "taxRules": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "example": "shipping_rule_tax"}, "title": {"type": "array", "items": {"type": "object", "properties": {"lang": {"type": "string", "example": "eng"}, "value": {"type": "string", "example": "Domestic Shipping Tax"}}}}, "percentage": {"type": "array", "items": {"type": "object", "properties": {"USD": {"type": "number", "example": 0.04}}}}, "conditions": {"type": "array", "items": {"type": "object"}, "example": []}}}}, "taxConfig": {"type": "object", "properties": {"shippingTaxable": {"type": "boolean", "example": true}}}, "pnrConfig": {"type": "object", "properties": {"firstnameRequired": {"type": "boolean", "example": false}, "operationWindowEnabled": {"type": "boolean", "example": false}, "operationWindowDays": {"type": "string", "example": "Monday,Tuesday,Wednesday,Thursday,Friday"}, "operationWindowStartTime": {"type": "string", "example": "0130"}, "operationWindowEndTime": {"type": "string", "example": "0930"}, "pSixtyUpperLimit": {"type": "number", "example": 86400000}}}, "localeConfig": {"type": "object", "properties": {"languageRules": {"type": "object", "properties": {"default": {"type": "string", "example": "eng"}}}, "currencyRules": {"type": "object", "properties": {"default": {"type": "string", "example": "SGD"}, "available": {"type": "array", "items": {"type": "string", "example": ["SGD", "USD"]}}}}}}, "associatedAirlines": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 55}, "icaoCode": {"type": "string", "example": "SIA"}}}}, "associatedLanguages": {"type": "array", "example": ["en", "ja", "de"], "description": "associated airline store languages", "items": {"type": "string"}}}}, "KITReady": {"required": ["airlineId", "catalogAssignments"], "properties": {"airlineId": {"type": "integer", "example": 10, "description": "Returns internal id of the airline"}, "catalogAssignments": {"type": "array", "items": {"type": "integer"}, "example": [17, 15]}}}, "DeploymentInventory": {"required": ["id", "associateCatalog", "products"], "properties": {"id": {"type": "integer", "example": 1, "description": "Return internal ID of catalog asignment"}, "associateCatalog": {"type": "object", "required": ["id", "name", "storeId"], "properties": {"id": {"type": "integer", "example": 1, "description": "retrurn internal Id of catalog"}, "name": {"type": "object", "properties": {"en": {"type": "string", "description": "Catalog Name in English language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "storeId": {"type": "integer", "example": 2, "description": "Return internal ID of owner store"}}}, "fromDate": {"type": "string", "format": "date", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2})", "example": "2022-02-13", "description": "Returns the from date of Catalog"}, "toDate": {"type": "string", "format": "date", "pattern": "([0-9]{4})-(?:[0-9]{2})-([0-9]{2})", "example": "2022-02-18", "description": "Returns the to date of Catalog"}, "cabinClass": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1, "description": "Returns the internal  id of the airport"}, "name": {"type": "object", "properties": {"en": {"type": "string", "description": "Cabin Class Name in English language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "code": {"type": "string", "description": "Returns the cabin class unique human readable code", "nullable": true}}}}, "associateFlight": {"type": "array", "description": "Selected Flights for Route, Routegroup and Flight with Sectors Data. This field will be present when assignment Type is Flight or when associated Sector has some associated flights. One of associateFlight and associateSector is mandatory.", "minItems": 1, "items": {"type": "object", "required": ["id", "flightRouteNumber", "sectors", "statusCode", "airlineIATACode"], "properties": {"id": {"type": "integer", "description": "Returns the internal  id of the flights"}, "flightRouteNumber": {"type": "string", "description": "Returns the Flight Route number"}, "sectors": {"type": "array", "items": {"type": "object", "required": ["id", "name", "originAirportIATACode", "originAirportName", "destinationAirportIATACode", "destinationAirportName"], "properties": {"id": {"type": "integer", "example": 1, "description": "Returns the internal  id of the sector"}, "name": {"type": "object", "properties": {"en": {"type": "string", "description": "Sector Name in English language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "originAirportIATACode": {"type": "string", "example": "SIN", "description": "Returns the origin airport IATA Code"}, "originAirportName": {"description": "Returns the origin airport Name", "type": "object", "properties": {"en": {"type": "string", "description": "Returns the origin airport Name in English language", "example": "Singapore International Airport"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "destinationAirportIATACode": {"type": "string", "example": "DXB", "description": "Returns the destination airport IATA Code"}, "destinationAirportName": {"description": "Returns the destination airport Name", "type": "object", "properties": {"en": {"type": "string", "description": "Returns the destination airport Name in English language", "example": "Dubai International Airport"}}, "required": ["en"], "additionalProperties": {"type": "string"}}}}}, "fullfilmentCutOffTimeLimit": {"type": "string", "description": "Returns the time limit in hours for fulfillment Cut Off  where the goods targeted to be delivered on this flight should be ordered before this time limit."}, "statusCode": {"type": "boolean", "description": "Returns the flight status code of the flights an optional field"}, "airlineIATACode": {"type": "string", "description": "Returns the airline IATA code associated with this flight."}}}}, "associateSector": {"type": "array", "description": "Selected Sectors.This parameter will be present when associated Sector does not have any flights assoicated with it. One of associateFlight and associateSector is mandatory.", "items": {"type": "object", "required": ["id", "name", "originAirportIATACode", "originAirportName", "destinationAirportIATACode", "destinationAirportName"], "properties": {"id": {"type": "integer", "example": 1, "description": "Returns the internal  id of the sector"}, "name": {"type": "object", "properties": {"en": {"type": "string", "description": "Sector Name in English language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "originAirportIATACode": {"type": "string", "example": "SIN", "description": "Returns the origin airport IATA Code"}, "originAirportName": {"description": "Returns the origin airport Name", "type": "object", "properties": {"en": {"type": "string", "description": "Returns the origin airport Name in English language", "example": "Singapore International Airport"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "destinationAirportIATACode": {"type": "string", "example": "DXB", "description": "Returns the destination airport IATA Code"}, "destinationAirportName": {"description": "Returns the destination airport Name", "type": "object", "properties": {"en": {"type": "string", "description": "Returns the destination airport Name in English language", "example": "Dubai International Airport"}}, "required": ["en"], "additionalProperties": {"type": "string"}}}}}, "products": {"type": "array", "description": "Returns the list of products from the related catalog with their optional metadata (quantity - used when a product is deployed for inflight sales and price - used to set a price that is different than price defined on the product object)", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 100, "description": "Unique ID of Product"}, "sku": {"type": "string", "example": "98763645ur234", "description": "Returns the SKU received from store"}, "pacSku": {"type": "string", "example": "PAC_Walmart_A9574", "description": "PACSKU contains the unique identifier for the product object. The value for PACSKU is auto-generated by the Ground Tool when the object is created. The format of the value is PAC_STORENAME_SKU, where PAC is a static value which is appended before the “Store name” and the “SKU” provided by the store for the product.  "}, "quantity": {"type": "integer", "example": 231, "description": "Available quantity for sell for this catalog assignment. Deployment Inventory Number applies as same to all the flights/routes assigned in a catalog assignement"}, "isAvailableToSale": {"type": "boolean", "example": true, "description": "Returns true if the product is available for sale"}}}}}}, "Promotion": {"properties": {"id": {"type": "number", "example": 1, "description": "Returns ID of Promotion"}, "name": {"type": "string", "example": "Buy One Get One", "description": "Returns Promotion Name"}, "description": {"type": "string", "description": "Returns Description of Promotion ", "example": "Buy One Get One Promotion For Sandwich"}, "disclaimer": {"type": "string", "example": "promotion disclaimer to be displayed on front devices", "description": "Returns promotion description"}, "airlineId": {"type": "number", "example": 10, "description": "Returns Airline ID on which promotion is applicable"}, "storeId": {"type": "number", "example": 1, "description": "Returns Store Id Of Promotion"}, "isActive": {"type": "boolean", "description": "Returns true if Promotion is active, Returns false if promotion is inactive"}, "validity": {"type": "object", "properties": {"startDate": {"type": "string", "description": "Returns Start date of promotion in MM-DD-YYYY Format", "example": "12-14-2022"}, "endDate": {"type": "string", "description": "Returns Start date of promotion in MM-DD-YYYY Format", "example": "12-20-2022"}}}, "coupon": {"type": "object", "properties": {"couponType": {"type": "string", "description": "Returns coupon type applicable for promotion", "enum": ["One Time", "No Limit", "None"]}, "couponCode": {"type": "string", "description": "Returns Coupon code for promotion"}, "couponQuantityLimit": {"type": "number", "description": "Returns total coupon quantity available"}, "oneRedumptionPerCustomer": {"type": "boolean", "description": "Returns true if Only One time redumption per coustomer is enable else returns false"}}}, "promotionType": {"type": "object", "description": "Returns promotion configuration condtition and action fields", "properties": {"id": {"type": "number", "description": "Returns Promotion type ID"}, "promotionTypeName": {"type": "string", "description": "Returns Promotion Type Name"}, "condition": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string", "description": "Retruns Field Name on which condition is applicable"}, "nextFieldCondition": {"type": "string", "description": "Returns Condition type for next field if applicable", "enum": ["AND", "OR"]}}}}, "action": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string", "description": "Retruns Field Name on which condition is applicable"}, "nextFieldCondition": {"type": "string", "description": "Returns Condition type for next field if applicable", "enum": ["AND", "OR"]}}}}}}, "promotionApplicableOn": {"type": "string", "description": "Returns Promotion applicable On", "enum": ["Product", "Catalog", "Category", "Product Bundle"]}, "applicableProducts": {"type": "array", "items": {"type": "number", "description": "Returns Unique ID of Product"}}, "applicableCatalog": {"type": "number", "description": "Returns Unique ID of Catalog"}, "applicableCategory": {"type": "number", "description": "Returns Unique ID of category"}, "applicableProductDisclaimer": {"type": "string", "description": "Returns Disclaimer for applicable products"}, "productBundle": {"type": "array", "items": {"type": "number", "description": "Returns Array Of Product ID on which Promotion is Applicable Cart Must have all products to apply promotion"}}, "exclusionList": {"type": "array", "items": {"type": "number", "description": "Returns Array Of Product ID on which Promotion is not applicable"}}, "conditions": {"type": "object", "properties": {"minimumAmountSpent": {"type": "number", "example": 10.2, "description": "Returns Minimum Amount Condition Value in store's Currency "}, "minimumQuantityBought": {"type": "number", "example": 1, "description": "Returns Minimum Quantity Baught condition"}, "maximumQuantityBought": {"type": "number", "example": 10, "description": "Returns Maximum Quantity Baught condition"}, "maxQuantityPassengersCanPurchase": {"type": "number", "example": 10, "description": "Returns Minimum Quantity passengerCanPurchase condition"}}}, "action": {"type": "object", "properties": {"qualifyingItem": {"type": "number", "example": 1, "description": "Returns unique Id of promoted product"}, "qualifyingItemDisclaimer": {"type": "string", "description": "Returns disclaimer of promoted product"}, "itemDiscount": {"type": "object", "properties": {"value": {"type": "number", "description": "Returns Item discount value"}, "type": {"type": "string", "description": "Returns Item Discount type", "enum": ["Amount", "Percentage"]}}}, "cartDiscount": {"type": "object", "properties": {"value": {"type": "number", "description": "Returns Cart discount value"}, "type": {"type": "string", "description": "Returns Cart Discount type", "enum": ["Amount", "Percentage"]}}}, "freeProduct": {"type": "boolean", "description": "Returns true if Qualifying Items is completely free."}}}, "promotionCombinability": {"type": "boolean", "description": "Returns true if promotion can be combined with another promotion"}}}, "Campaign": {"properties": {"id": {"type": "number", "description": "Returns unique ID of campaign"}, "name": {"type": "string", "description": "Retuns name of product"}, "description": {"type": "string", "description": "Returns description of campaign"}, "startDate": {"type": "string", "description": "Returns start date of promotion"}, "endDate": {"type": "string", "description": "Returns end date of promotion"}, "isActive": {"type": "boolean", "description": "Returns true if campaign is active"}, "airlineId": {"type": "number", "description": "Returns AirlineId on which promotion is applicable"}, "storeId": {"type": "number", "description": "Returns StoreId of campaign"}, "campaignType": {"type": "string", "description": "Returns campaing type", "enum": ["Product", "Category", "Catalog", "Promotion"]}, "products": {"type": "array", "items": {"type": "number", "description": "Returns array of product Id on which campaign is created"}}, "catalogs": {"type": "array", "items": {"type": "number", "description": "Returns array of catalog Id on which campaign is created"}}, "categories": {"type": "array", "items": {"type": "number", "description": "Returns array of category Id on which campaign is created"}}, "promotion": {"type": "array", "items": {"type": "number", "description": "Returns array of promotion Id on which campaign is created"}}, "campaignBanners": {"type": "array", "items": {"type": "object", "required": ["id", "aspectRatio", "height", "width", "mediaType", "bannerPositon", "S3bucketlocation"], "properties": {"id": {"type": "integer", "example": 756, "description": "Returns the internal id of the image."}, "aspectRatio": {"type": "string", "example": "16:9", "description": "Returns the aspect ratio (width / height) of the image."}, "height": {"type": "integer", "example": 130, "description": "Returns the height of the image in pixels."}, "width": {"type": "integer", "example": 190, "description": "Returns the width of the image in pixels."}, "mediaType": {"type": "string", "example": "image", "description": "Returns the media type of the object. For the image object, this value will always be image."}, "bannerPositon": {"type": "string", "description": "Returns Banner position", "enum": ["Top", "Middle", "Bottom"]}, "S3bucketlocation": {"type": "object", "required": ["bucketName", "key"], "properties": {"bucketName": {"type": "string", "description": "Bucket name where the asset file is hosted"}, "key": {"type": "string", "description": "Key for the asset file"}}}}}}, "displayDeviecs": {"type": "string", "description": "Returns Display device name", "enum": ["PED", "<PERSON><PERSON><PERSON>"]}}}, "SpecialityAttribute": {"required": ["id", "description", "image"], "properties": {"id": {"type": "integer", "example": 1277}, "description": {"type": "object", "properties": {"en": {"type": "string", "description": "SpecialityAttribute description in English language"}, "ja": {"type": "string", "description": "SpecialityAttribute description in Japanese language"}, "it": {"type": "string", "description": "SpecialityAttribute description in Italian language"}, "fr": {"type": "string", "description": "SpecialityAttribute description in French language"}}, "required": ["en"], "additionalProperties": {"type": "string"}}, "disclaimer": {"type": "object", "properties": {"en": {"type": "string", "description": "SpecialityAttribute disclaimer in English language"}, "ja": {"type": "string", "description": "SpecialityAttribute disclaimer in Japanese language"}, "it": {"type": "string", "description": "SpecialityAttribute disclaimer in Italian language"}, "fr": {"type": "string", "description": "SpecialityAttribute disclaimer in French language"}}}, "image": {"type": "array", "items": {"type": "object", "required": ["id", "aspectRatio", "height", "width", "mediaType", "S3bucketlocation"], "properties": {"id": {"type": "integer", "example": 756, "description": "Returns the internal id of the image."}, "aspectRatio": {"type": "string", "example": "1:1", "description": "Returns the aspect ratio (width / height) of the image."}, "height": {"type": "integer", "example": 256, "description": "Returns the height of the image in pixels."}, "width": {"type": "integer", "example": 256, "description": "Returns the width of the image in pixels."}, "mediaType": {"type": "string", "example": "image", "description": "Returns the media type of the object. For the image object, this value will always be image."}, "S3bucketlocation": {"type": "object", "required": ["bucketName", "key"], "properties": {"bucketName": {"type": "string", "description": "Bucket name where the asset file is hosted"}, "key": {"type": "string", "description": "Key for the asset file"}}}}}}, "associateStore": {"type": "integer", "example": 14, "description": "Return internal ID of associate store"}}}, "Order": {"properties": {"orderKey": {"type": "string", "description": "Identifier for this order.  This is meant to be used internally within the Marketplace Server.", "format": "uuid", "example": "80406add-a4d7-43cd-be31-df0ba2bcec3f"}, "externalId": {"type": "string", "description": "Identifier for this order.  This is meant to be used outside of the Marketplace Server."}, "basketKey": {"type": "string", "description": "Identifier of the basket associated with this order", "format": "uuid", "example": "c18220ea-7ee7-4695-9fca-98f3135ba5e1"}, "orderProvider": {"type": "string", "description": "order provider for this order whether it is on-board / e-Commerce", "example": "e-Commerce | on-board"}, "orderState": {"type": "string", "description": "order status in ground tool", "enum": ["Processing", "Shipped", "Cancelled", "Delivered", "Complete", "Failed", "Pending", "Paid"]}, "orderdate": {"type": "string", "description": "Timestamp of when the order was created.", "format": "date-time"}, "orderModificationDate": {"type": "string", "description": "Timestamp of when the order was last modified.", "format": "date-time"}, "fulfillments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "orderItems": {"type": "array", "items": {"type": "object", "properties": {"productId": {"type": "string", "description": "PAC use this to identifier to identify this item Product ID", "example": "2134"}, "storeId": {"type": "string", "description": "Store ID", "example": 1}, "unitPrice": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "quantity": {"type": "number", "description": "Quantity of the item", "example": 5}, "status": {"type": "string", "example": "DELIVERED"}, "discountTotal": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "taxTotal": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "discountAdjustments": {"type": "array", "items": {"type": "object", "properties": {"discountAmount": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "adjustType": {"type": "string", "enum": ["DISCOUNT"]}, "promotionCode": {"type": "string"}, "promotionName": {"type": "string"}, "rate": {"type": "number"}}}}, "taxAdjustments": {"type": "array", "items": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}}, "salePrice": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "unitTax": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "unitDiscount": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "unitGross": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "unitNet": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}}, "minLength": 1}}}}}, "payment": {"type": "array", "description": "A listing of payment instruments used", "items": {"$ref": "#/components/schemas/Payment"}, "minLength": 1, "maxLength": 1}, "shipments": {"type": "array", "description": "An array of shipments associated with this order.", "items": {"$ref": "#/components/schemas/Shipment"}, "minLength": 1}, "orderSummary": {"$ref": "#/components/schemas/OrderSummary"}, "itinerary": {"$ref": "#/components/schemas/Itinerary"}, "meta": {"$ref": "#/components/schemas/OffloadMeta"}, "user": {"$ref": "#/components/schemas/User"}}}, "Shipment": {"type": "object", "required": ["id", "mode", "address"], "properties": {"id": {"type": "number", "description": "Shipment ID in Groundtool"}, "rate": {"$ref": "#/components/schemas/OrderPrice"}, "shippingMethod": {"type": "string", "description": "This can be EXPRESS or STANDARD", "example": "EXPRESS", "enum": ["EXPRESS", "STANDARD"]}, "carrier": {"type": "string", "description": "The shipping carrier\tstring", "example": "FEDEX"}, "address": {"oneOf": [{"$ref": "#/components/schemas/Address"}, {"$ref": "#/components/schemas/OrderFlight"}]}, "item": {"type": "array", "items": {"type": "string", "description": "Id of the order item included in this shipment"}}, "mode": {"type": "string", "enum": ["next_flight", "home_delivery", "next_flight_short_notice"]}, "taxTotal": {"type": "array", "items": {"$ref": "#/components/schemas/OrderPrice"}}}}, "Payment": {"type": "object", "required": ["paymentId", "technicalServiceProviderTransactionId", "gatewayTransactionId", "identifier", "authAmount", "billing<PERSON><PERSON>ress"], "properties": {"paymentMethod": {"type": "string", "enum": ["CARD"]}, "authAmount": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "paymentId": {"type": "string", "description": "The unique payment identifier or token", "example": "ajsd687362"}, "status": {"type": "string", "description": "Payment authorization status", "example": "AUTHORIZED"}, "billingAddress": {"$ref": "#/components/schemas/Address", "description": "paymentToken or paymentId"}, "technicalServiceProviderTransactionId": {"type": "string", "description": "Technical Service Provider Transaction ID"}, "gatewayTransactionId": {"type": "string", "description": "Gateway Transaction ID"}}}, "Address": {"type": "object", "required": ["firstName", "lastName", "address1", "city", "state", "postalCode", "countryCode", "email"], "properties": {"salutation": {"type": "string", "description": "Salutation of the recipient on the shipping label", "example": "Ms."}, "title": {"type": "string", "description": "title of the recipient", "example": "Ms."}, "firstName": {"type": "string", "description": "The recipient's first name", "example": "<PERSON>"}, "middleName": {"type": "string", "description": "The recipient's middle name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "The recipient's last name", "example": "<PERSON><PERSON>"}, "address1": {"type": "string", "description": "The first line of the address", "example": "11200 S. Jerusaleum Street"}, "address2": {"type": "string", "description": "The second line of the address"}, "city": {"type": "string", "description": "City of where this address is located", "example": "New Haven"}, "state": {"type": "string", "description": "State of where this address is located", "example": "Connecticut"}, "postalCode": {"type": "string", "description": "The Postal Code associated with this address", "example": "06501"}, "countryCode": {"type": "string", "description": "Country Code of this address", "example": "USA"}, "email": {"type": "string"}, "phone": {"type": "string"}}}, "OrderStatus": {"type": "string", "description": "Order Status\n  PROCESSING - The order is being processed\n  CANCELLED  - The order got cancelled\n  PAID       - The order is paid\n  COMPLETE   - The order is finalized\n  FAILED     - The order failed to complete\n", "enum": ["PROCESSING", "CANCELLED", "PAID", "COMPLETE", "FAILED"]}, "User": {"type": "object", "required": ["firstName", "lastName", "email"], "properties": {"id": {"type": "string", "description": "Unique identifier identifying this user.", "format": "uuid", "example": "bdf02f29-be17-4a54-ae41-cd9698cf6d56"}, "userName": {"type": "string", "description": "This can be just the seat number.", "example": "33A"}, "salutation": {"type": "string", "description": "Salutation.", "example": "Mr."}, "firstName": {"type": "string", "description": "User's first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User's last name", "example": "<PERSON><PERSON>"}, "middleName": {"type": "string", "description": "User's middle name", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "description": "Email address associated with this user", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "Phone number associated with the user", "example": 66038888}, "memberships": {"type": "array", "description": "Loyalty memberships", "items": {"$ref": "#/components/schemas/Membership"}}}}, "Tax": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of tax", "example": "VAT"}, "taxAmount": {"$ref": "#/components/schemas/OrderPrice"}, "rate": {"type": "number"}}, "required": ["type", "taxAmount", "rate"]}, "OrderPrice": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "OffloadMeta": {"type": "object", "properties": {"airlineCodeICAO": {"type": "string", "example": "SQ"}, "flightNumber": {"type": "string", "example": "SQ37"}, "departureAirportCodeIATA": {"type": "string", "example": "LAX"}, "arrivalAirportCodeIATA": {"type": "string", "example": "DFW"}, "departTimeStamp": {"type": "string", "format": "date-time"}, "arrivalTimeStamp": {"type": "string", "format": "date-time"}}}, "Adjustment": {"type": "object", "properties": {"discountAmount": {"$ref": "#/components/schemas/OrderPrice"}, "adjustType": {"type": "string", "enum": ["DISCOUNT"]}, "promotionCode": {"type": "string"}, "promotionName": {"type": "string"}, "rate": {"type": "number"}}}, "OrderSummary": {"type": "object", "required": ["netTotal", "grossTotal", "taxes", "shippingTotal", "totalTaxAmount", "adjustmentTotal"], "properties": {"grossTotal": {"$ref": "#/components/schemas/OrderPrice"}, "discounts": {"type": "array", "items": {"$ref": "#/components/schemas/Adjustment"}}, "adjustmentTotal": {"$ref": "#/components/schemas/OrderPrice"}, "taxes": {"type": "array", "items": {"$ref": "#/components/schemas/Tax"}}, "totalTaxAmount": {"$ref": "#/components/schemas/OrderPrice"}, "shippingTotal": {"$ref": "#/components/schemas/OrderPrice"}, "currency": {"type": "string", "example": "EUR"}, "netTotal": {"$ref": "#/components/schemas/OrderPrice"}}, "description": "netTotal = grossTotal - adjustmentTotal + taxTotal + shippingTotal"}, "Membership": {"type": "object", "properties": {"loyaltyNumber": {"type": "string"}, "loyaltyType": {"type": "string"}}}, "OrderFlight": {"type": "object", "required": ["flightNumber", "scheduledDepartureTime", "departureAirportCodeIATA", "arrivalAirportCodeIATA"], "properties": {"flightNumber": {"type": "string"}, "scheduledDepartureTime": {"type": "string", "format": "date-time"}, "departureAirportCodeIATA": {"type": "string"}, "arrivalAirportCodeIATA": {"type": "string"}}, "description": "OrderFlight details for the specific flights selected for inflight delivery."}, "Itinerary": {"type": "object", "properties": {"identifier": {"type": "string"}, "confirmationCode": {"type": "string"}, "airline": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}}}, "CategorySpecificNonLocalisedValue": {"type": "object", "properties": {"attributeName": {"type": "string", "example": "size"}, "attributeValue": {"oneOf": [{"type": "string"}, {"type": "integer"}, {"type": "boolean"}, {"type": "array"}]}}}, "CategorySpecificLocalisedValue": {"type": "object", "properties": {"attributeName": {"type": "string", "example": "size"}, "attributeValue": {"type": "object", "properties": {"en": {"type": "string", "example": "Panasonic Store", "description": "Return Value of attribute in English language"}}, "additionalProperties": {"description": "Value in Multiple languages", "type": "string"}}}}, "AuthTokenUpdate": {"type": "object", "properties": {"aud": {"description": "identifer used to uniquely identify the tenant for which this authorization token is issued.", "type": "string", "example": "SIA"}, "sub": {"description": "identifer used to uniquely identify the tenant for which this authorization token is issued.", "type": "string", "example": "airline_end_user"}, "iat": {"type": "number", "example": 1516239022, "description": "The \"Creation\" timestamp when <PERSON><PERSON> was created"}, "exp": {"type": "number", "example": 1516239022, "description": "The \"Expiration\" timestamp at which the Authorization Token expires"}}}, "Price": {"type": "object", "required": ["value", "currency"], "properties": {"value": {"type": "number", "format": "float", "example": 1.99, "description": "Price value"}, "currency": {"type": "string", "example": "USD", "description": "Price currency"}}}, "PriceList": {"type": "array", "items": {"$ref": "#/components/schemas/Price"}}}}}