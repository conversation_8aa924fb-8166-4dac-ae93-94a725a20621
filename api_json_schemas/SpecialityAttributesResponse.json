{"properties": {"code": {"type": "integer", "example": 200, "description": ""}, "success": {"type": "boolean", "example": true, "description": ""}, "data": {"properties": {"SpecialityAttributes": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1, "description": "Returns Unique ID of SpecialityAttributes"}, "shortDescription": {"type": "string", "example": "exclusively cooked food"}, "disclaimer": {"type": "string", "example": "exclusively cooked food"}, "image": {"type": "string", "example": "/SpecialityAttributes/update%20451-1653167615.png", "description": "Return Image of SpecialityAttributes"}}, "required": ["id"]}}}}}}