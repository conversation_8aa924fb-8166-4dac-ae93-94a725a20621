{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"catalog": {"properties": {"id": {"type": "integer", "example": 123, "description": "return unique identifier of Catalog"}, "name": {"type": "string", "example": "Adidas Cricket"}, "products": {"type": "array", "items": {"type": "integer", "example": 250, "description": "Array of Product ID"}}, "store": {"type": "string", "example": "Walmart", "description": "Name of Store from which this catalog belongs to"}}, "required": ["id", "name", "products"]}}}}}