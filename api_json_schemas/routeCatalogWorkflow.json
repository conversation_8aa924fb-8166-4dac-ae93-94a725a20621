{"properties": {"catalogAssignmentId": {"type": "integer", "example": 1, "description": "Unique ID of catalog Assignment"}, "status": {"type": "string", "description": "Allowed States for Airline are - [request, requestDisassociation, approveDisassociation, rejectDisassociation, airlineCategoryMapped, editAirlineCategory]. States allowed for store are [approve, reject, requestDisassociation, approveDisassociation, rejectDisassociation]\n", "enum": ["request", "approve", "requestDisassociation", "approveDisassociation", "rejectDisassociation", "airlineCategoryMapped", "editAirlineCategory", "editBaseData"]}}}