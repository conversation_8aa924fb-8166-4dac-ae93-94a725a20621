{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"route_groups": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 123, "description": "returns unique identifier of routegroup"}, "name": {"type": "string", "example": "Star Alliance", "description": "code of route group"}}}}, "totalRecords": {"type": "integer", "example": 1}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}