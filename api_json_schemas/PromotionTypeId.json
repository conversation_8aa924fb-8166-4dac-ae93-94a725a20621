{"properties": {"code": {"type": "integer", "example": 200, "description": ""}, "success": {"type": "boolean", "example": true, "description": ""}, "data": {"properties": {"promotionType": {"properties": {"id": {"type": "integer", "example": 123, "description": ""}, "promotionTypeName": {"type": "string", "example": "Buy One Get One", "description": ""}, "conditionFields": {"type": "array", "items": {"properties": {"conditionField": {"type": "string", "example": "minimumAmountSpent", "description": ""}, "conditionType": {"type": "string", "enum": ["AND", "OR", ""], "description": ""}}}}, "actionFields": {"type": "array", "items": {"properties": {"conditionField": {"type": "string", "example": "cartDiscount", "description": ""}, "conditionType": {"type": "string", "enum": ["AND", "OR", ""], "description": ""}}}}}}}}}}