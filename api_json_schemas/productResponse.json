{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"products": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 123, "description": "returns unique identifier of product"}, "sku": {"type": "string", "example": "WNCTXLBL42", "description": "SKU of the Product"}, "pacSKU": {"type": "string", "example": "PAC_StoreName_Sku", "description": "PAC SKU of the Product. Uniquely identifies the Product."}, "name": {"type": "string", "example": "WNCTXLBL42"}, "shortDescription": {"type": "string", "example": "Lorem <PERSON> is simply dummy text of the printing"}, "description": {"type": "string", "example": "Lorem <PERSON> is simply dummy text of the printing"}, "barCode": {"type": ["string", "null"], "example": "TCGA-02-0001-01B", "description": "Product barcode"}, "deliveryMethod": {"type": "array", "description": "Delivery method of Product", "items": {"type": "string", "example": "Onboard", "enum": ["Next Flight", "Onboard", "Gate Pick Up", "Shipping"]}}, "nextFlightLeadTime": {"type": "number", "description": "Input Next Flight lead time (Business Hrs) for the Product", "example": 12}, "gatePickupLeadTime": {"type": "number", "description": "Input Gate Pickup lead time (Business Hrs) for the Product", "example": 1}, "category": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 193}, "name": {"type": "string", "example": "Food & Beverages"}, "description": {"type": ["string", "null"]}, "categoryImageUrl": {"type": "string", "description": "Category Image Url"}}}}, "shippingAttributes": {"type": "object", "properties": {"weight": {"type": ["number", "null"], "example": 0, "description": "weight of product"}, "weightUnit": {"type": ["string", "null"], "example": "KG", "description": "weight unit of product"}, "height": {"type": ["number", "null"], "example": 0, "description": "Height of product"}, "heightUnit": {"type": ["string", "null"], "example": "CM", "description": "height unit of product"}, "width": {"type": ["number", "null"], "example": 0, "description": "widht of product"}, "widthUnit": {"type": ["string", "null"], "example": "CM", "description": "Width unit of product"}, "length": {"type": ["number", "null"], "example": 0, "description": "lenght of product"}, "lengthUnit": {"type": ["string", "null"], "example": "CM", "description": "length unit of product"}}}, "associateStore": {"type": "integer", "example": 1, "description": "returns store id of product"}, "newFrom": {"type": ["string", "null"], "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-06-22", "description": "Product Start Selling Date"}, "newTo": {"type": ["string", "null"], "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-08-22", "description": "Product End Selling Date"}, "brand": {"type": ["string", "null"], "example": "spark"}, "ageVerificationRequire": {"type": "boolean"}, "publishedAt": {"type": "string", "format": "date", "pattern": "^(\\d{4})-(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2})$", "example": "2021-06-07 04:37", "description": "returns published date time. Format is yyyy-mm-dd hh:ii"}, "isAvailableToSell": {"type": "boolean", "description": "Product availability"}, "uniqueResourceIdentifier": {"type": ["string", "null"], "example": "ADI98", "description": "returns products URI"}, "notApplicableCountries": {"type": ["array", "null"], "description": "Not applicable country to sell this product", "items": {"type": "string", "example": "IN"}}, "assets": {"type": "object", "properties": {"images": {"type": "array", "items": {"type": "object", "properties": {"image1_1": {"type": ["string", "null"], "example": "https://placehold.jp/1200x1200.png", "description": "Image URL of product. The Image Resolution Should be Greater Than or equalto 1200x1200 with 1x1 Aspect Ratio"}, "image2_1": {"type": ["string", "null"], "example": "https://placehold.jp/1480x740.png", "description": "Image URL of product. The Image Resolution Should be Greater Than 1480x740 with 2x1 Aspect Ratio"}, "image4_3": {"type": ["string", "null"], "example": "https://placehold.jp/760x570.png", "description": "Image URL of product. The Image Resolution Should be Greater Than or equalto 760x570 with 4x3 Aspect Ratio"}, "image2_3": {"type": ["string", "null"], "example": "https://placehold.jp/944x1416.png", "description": "Image URL of product. The Image Resolution Should be Greater Than or equalto 944x1416 with 2x3 Aspect Ratio"}, "image5_6": {"type": ["string", "null"], "example": "https://placehold.jp/670x804.png", "description": "Image URL of product. The Image Resolution Should be Greater Than or equalto 670x804 with 5x6 Aspect Ratio"}}}}, "gallery": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "example": "https://content.adidas.co.in/static/Product-EF3509/MEN_CRICKET_SHOES_EF3509_1.jpg", "description": "url of image"}, "imagePosition": {"type": "integer", "example": 1, "description": "position of image to display on PED and IFE"}}}}}}, "PriceTaxation": {"type": "object", "properties": {"price": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "number", "format": "float", "description": "Price of the Product"}, "currency": {"type": "string", "description": "Price Currency"}}, "required": ["value", "currency"]}}, "specialPrice": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "number", "format": "float", "description": "Special price of product to sell"}, "currency": {"type": "string", "description": "Special Price Currency"}}, "required": ["value", "currency"]}}, "cost": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "number", "format": "float", "description": "Cost of the Product"}, "currency": {"type": "string", "description": "Cost C<PERSON><PERSON><PERSON>"}}}}, "isTaxable": {"type": ["boolean", "null"], "example": true, "description": "boolean if product is taxable"}, "orderMaxQuantityAllowed": {"type": ["number", "null"], "example": 2, "description": "Maximum quantity allowed to purchase per order"}, "orderMinQuantityAllowed": {"type": "number", "example": 1, "description": "Minimum quantity to be allowed to purchase per order"}}}, "Attributes": {"type": "object", "properties": {"storeSpecificAttributes": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "value"}}}}, "specialityAttribute": {"type": "array", "items": {"type": "object", "properties": {"shortDescription": {"type": "string", "example": "spicy"}, "id": {"type": "integer", "example": 123}, "image": {"type": "string", "example": "https://pbs.twimg.com/profile_images/817389061564493825/c8wrPD8L_400x400.jpg        "}}, "required": ["id", "shortDescription"]}}}}, "productSet": {"type": "string", "example": "Configurable Or Simple"}, "storeProductId": {"type": ["string", "null"], "example": "134A34"}, "parentProduct": {"type": "number", "example": 13, "description": "Returns Parent Id of Variant Product. Mandatory for variants. Available only for variants"}, "productType": {"type": "string", "example": "Amenities", "enum": ["Food and Beverage", "Duty Free", "Amenities"]}, "spotLight": {"type": "boolean"}, "requireShipping": {"type": "boolean"}, "isPerishable": {"type": "boolean"}, "itemGroupId": {"type": "string"}}, "required": ["id", "name", "sku", "productSet", "productType", "category", "deliveryMethod", "pacSKU"]}}, "totalRecords": {"type": "integer", "example": 9}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}