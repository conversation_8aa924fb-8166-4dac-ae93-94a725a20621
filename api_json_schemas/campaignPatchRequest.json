{"type": "object", "properties": {"campaignName": {"type": "string", "example": "New Year Offer", "description": "Campaign Name"}, "description": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-04-12", "description": "Start Date in YYYY-MM-DD format", "nullable": true}, "endDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-08-22", "description": "End Date in YYYY-MM-DD format", "nullable": true}, "isActive": {"type": "boolean", "example": true, "nullable": true}, "airline": {"type": "integer"}, "campaignType": {"type": "string", "enum": ["Product", "Category", "Catalog", "Promotion"]}, "products": {"type": "array", "items": {"type": "integer", "example": 1}}, "category": {"type": "integer", "example": 1}, "catalog": {"type": "integer", "example": 1}, "promotion": {"type": "array", "items": {"type": "integer", "example": 1}}, "campaignBanners": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "example": "", "description": "URL of Image to upload"}, "bannerPosition": {"type": "string", "enum": ["Top", "Middle", "Bottom"]}}}}, "displayDevices": {"type": "array", "items": {"type": "string", "enum": ["PED", "<PERSON><PERSON><PERSON>"]}}}}