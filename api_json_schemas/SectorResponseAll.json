{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"sectors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 123, "description": "returns unique identifier of sector"}, "sectorName": {"type": "string", "example": "SIN-SYD", "description": "Name Of sector"}, "summary": {"type": "string", "example": "Singapore (SIN) to Sydney (SYD)", "description": "Description Of sector"}, "routeGroup": {"type": "string", "example": "Star Alliance", "description": "Name of Route Group"}, "origin": {"type": "string", "example": "VIDP", "description": "Origin Airport ICAO Code"}, "destination": {"type": "string", "example": "WSSS", "description": "Destination Airport ICAO Code"}, "distance": {"type": "string", "example": "3909 miles", "description": "Distance between two airport"}, "airline": {"type": "string", "example": "Singapore Airlines", "description": "Name of Airline from which this sector belongs to"}}}}, "totalRecords": {"type": "integer", "example": 1}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}