{"type": "object", "required": ["catalog"], "properties": {"catalog": {"type": "object", "required": ["id", "products"], "properties": {"id": {"type": "integer", "description": "Unique identifier of Catalog"}, "products": {"type": "array", "items": {"type": "object", "required": ["productId", "productQuantity"], "properties": {"productId": {"type": "integer", "description": "Unique identifier of product of catalog"}, "productQuantity": {"type": "integer", "description": "Available Quantity for this route-catalog assignment"}}}}}}}}