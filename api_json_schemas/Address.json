{"type": "object", "required": ["firstName", "lastName", "address1", "city", "state", "postalCode", "countryCode", "email"], "properties": {"salutation": {"type": "string", "description": "Salutation of the recipient on the shipping label", "example": "Ms."}, "title": {"type": "string", "description": "title of the recipient", "example": "Ms."}, "firstName": {"type": "string", "description": "The recipient's first name", "example": "<PERSON>"}, "middleName": {"type": "string", "description": "The recipient's middle name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "The recipient's last name", "example": "<PERSON><PERSON>"}, "address1": {"type": "string", "description": "The first line of the address", "example": "11200 S. Jerusaleum Street"}, "address2": {"type": "string", "description": "The second line of the address"}, "city": {"type": "string", "description": "City of where this address is located", "example": "New Haven"}, "state": {"type": "string", "description": "State of where this address is located", "example": "Connecticut"}, "postalCode": {"type": "string", "description": "The Postal Code associated with this address", "example": "06501"}, "countryCode": {"type": "string", "description": "Country Code of this address", "example": "USA"}, "email": {"type": "string"}, "phone": {"type": "string"}}}