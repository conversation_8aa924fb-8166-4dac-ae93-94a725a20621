{"type": "object", "required": ["name", "description", "airline", "startDate", "endDate", "isActive", "couponType", "promotionType", "promotionApplicableOn"], "properties": {"name": {"type": "string", "description": "Name of Promotion", "example": "Buy One Get One"}, "description": {"type": "string", "description": "Description of Promotion", "example": "Buy One Veg Sandwich and Get One Sandwich Free"}, "disclaimer": {"type": "string", "example": "You need to Order 2 Quantity", "description": "Disclaimer for Promotion", "nullable": true}, "airline": {"type": "integer", "example": 11, "description": "Airline ID in which promotion is applicable."}, "startDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-08-23", "description": "Date in YYYY-MM-DD Format, The Date on which promotion will be start"}, "endDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-08-23", "description": "Date in YYYY-MM-DD Format, The date on which promotion will end"}, "isActive": {"type": "boolean", "example": true, "description": "If true is provided then promotion will be marked as active, If false is provided then promotion will be marked as inactive"}, "couponType": {"type": "string", "enum": ["One Time", "No Limit", "None"], "description": "Provide coupon type if applicable, If not applicable then provide None"}, "couponCode": {"type": "string", "example": "BOGO", "description": "Coupon Code to avail this promotion", "nullable": true}, "couponQuantityLimit": {"type": "integer", "example": "10", "description": "Total Coupons available for this promotion", "nullable": true}, "oneRedemptionPerCustomer": {"type": "boolean", "example": true, "description": "Provide true if one customer can apply coupon only once", "nullable": true}, "PromotionCombinability": {"type": "boolean", "example": false, "description": "Provide True if this promotion can be combined with other pormotion", "nullable": true}, "promotionType": {"type": "number", "example": 1, "description": "ID of Promotion Type Master, Id Of promotion type will determine which field will be used for condition and actions."}, "promotionApplicableOn": {"type": "string", "enum": ["Product", "Catalog", "Category", "Product Bundle", "None"], "description": "determines on which entity promotion is applicable"}, "applicableProducts": {"type": "array", "items": {"type": "integer", "example": 1, "description": "accepts ID of products on which promotion is applicable Conditionally requred if promotionApplicableOb is selected as Product"}}, "productDisclaimer": {"type": "string", "description": "Product Disclaimer to be displayed in selected products Conditionally requred if promotionApplicableOb is selected as Product", "nullable": true}, "applicableCategory": {"type": "integer", "example": 1, "description": "accepts ID of category on which promiton is applicable, By Providing ID It will consider all products under that category. Conditionally requred if promotionApplicableOb is selected as Category"}, "applicableCatalog": {"type": "integer", "example": 1, "description": "accepts ID of catalog on which promiton is applicable, By Providing ID It will consider all products under that catalog. Conditionally requred if promotionApplicableOb is selected as Catalog"}, "exclusionList": {"type": "array", "items": {"type": "integer", "example": 1}, "description": "accepts ID of products which needs to be excluded in case of applicable on is selected as catalog or category."}, "productBundle": {"type": "array", "items": {"type": "integer", "example": 1}, "description": "Acceptes ID of Products If promotion is Like Bundle, Promotion is applicable on selected products only, So By Providing value in this attribute Promotion will be applicable if all products are there in cart. There has to be minimum 2 products Conditionally requred if promotionApplicableOb is selected as Product Bundle"}, "minimumAmountSpent": {"type": "integer", "example": 10.2, "description": "Additional promotion condition to be added on promotion for passenger has to spend x amount to avail this promotion Required depending on promotion Type configuration", "nullable": true}, "minimumQuantityBought": {"type": "integer", "example": 10.2, "description": "Additional promotion condition to be added on promotion for passenger has to purchase minimum x quantity to avail this promotion Required depending on promotion Type configuration", "nullable": true}, "maximumQuantityBought": {"type": "integer", "example": 10.2, "description": "Additional promotion condition to be added on promotion for passenger has to purchase Maximum x quantity to avail this promotion Required depending on promotion Type configuration", "nullable": true}, "maxQuantityPassengerCanPurchase": {"type": "integer", "example": 10.2, "description": "Additional promotion condition to be added on promotion for passenger can purchase maximum x quantity to avail this promotion Required depending on promotion Type configuration", "nullable": true}, "qualifyingItem": {"type": "integer", "example": 1, "description": "Promotion action to be added on promotion item, It accepts ID of Product Required depending on promotion Type configuration", "nullable": true}, "qualifyingItemDescription": {"type": "string", "description": "Qualifing Item Description for this promoiton which will be displayed in UI Required depending on promotion Type configuration", "nullable": true}, "qualifyingItemQuantity": {"type": "integer", "example": 1, "description": "Qualifing Item Quantity for this promotion For Example Buy One Get One free ofer willl have qualifyingItemQuantity 1 Required depending on promotion Type configuration", "nullable": true}, "freeProduct": {"type": "boolean", "description": "Determines wheter Qualifying item is completely free or not Required depending on promotion Type configuration", "nullable": true}, "itemDiscount": {"type": "object", "nullable": true, "properties": {"value": {"type": "integer"}, "discountType": {"type": "string", "enum": ["Amount", "Percentage"]}}, "description": "Amount or Percentage discount applicable on qualifying item Required depending on promotion Type configuration"}, "cartDiscount": {"type": "object", "nullable": true, "properties": {"value": {"type": "integer"}, "discountType": {"type": "string", "enum": ["Amount", "Percentage"]}}, "description": "Amount or Percentage discount applicable on cart amount for this store products Required depending on promotion Type configuration"}}}