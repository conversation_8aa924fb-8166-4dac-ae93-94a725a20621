{"required": ["name", "fulfillmentOption"], "properties": {"name": {"type": "string", "description": "Name of", "example": "<PERSON><PERSON><PERSON> Inventory"}, "fulfillmentOption": {"type": "array", "items": {"type": "integer", "example": 1, "description": "Unique ID of fullfillment Type."}}, "icaoCode": {"type": "string", "example": "PanaSky", "description": "Airport ICAO Code", "nullable": true}}}