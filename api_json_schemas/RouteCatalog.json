{"type": "object", "required": ["name", "catalog"], "properties": {"name": {"type": "string", "description": "Route catalog assignment Name"}, "catalog": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "Unique identifier of Catalog"}, "products": {"type": "array", "items": {"type": "object", "properties": {"productId": {"type": "integer", "description": "Unique identifier of product of catalog"}, "airlineCategory": {"type": "array", "items": {"type": "integer", "description": "Airline Category ID"}}}}}, "productSubstitutions": {"type": "object", "description": "ORProducts", "additionalProperties": {"type": "array", "description": "productId's", "items": {"type": "array", "items": {"type": "integer"}}, "example": [[0, 1, 4]]}}}}, "sector": {"type": "array", "description": "One of sector, routeGroup,Flight is required", "items": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "example": 123, "description": "unique identifier of sector"}, "flights": {"type": "array", "description": "flight ids associated with the sector, can be skipped if user wants to include all flights associated with the sectors", "items": {"type": "integer", "example": 56}}}}}, "routeGroup": {"type": "object", "description": "One of sector, routeGroup,Flight is required", "required": ["id"], "properties": {"id": {"type": "number", "description": "Unique identifier of route Group"}, "sectors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "flights": {"type": "array", "description": "Flights should only be added for sectors which are not excluded.", "items": {"type": "integer"}}, "excluded": {"description": "excludedSector should only be passed when they are to be excluded. Flights should only be added for sectors which are not excluded", "type": "boolean"}}}}}}, "flight": {"type": "array", "description": "One of sector, routeGroup,Flight is required", "items": {"type": "object", "required": ["id"], "properties": {"id": {"type": "number", "description": "Unique identifier of Flight"}, "sectors": {"type": "array", "items": {"type": "integer"}}}}}, "catalogFromDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "nullable": true, "description": "Date of catalog should be applicable to sell on this assignment"}, "catalogToDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "nullable": true, "description": "Date of catalog should be removed from sell on this assignment"}, "cabinClass": {"type": "array", "items": {"type": "integer", "description": "Cabin Class Unique identifier"}}, "routeSequences": {"type": "object", "properties": {"categories": {"type": "array", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 250, "description": "airline category id"}, "sequenceNumber": {"type": "integer", "example": 2, "description": "sequenceNumber for airline category"}, "subCategories": {"type": "array", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 251, "description": "sub category Id"}, "sequenceNumber": {"type": "integer", "example": 1, "description": "sequenceNumber for sub category"}, "products": {"type": "array", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 732, "description": "product id"}, "sequenceNumber": {"type": "integer", "example": 3, "description": "sequence for product"}}}}}}}}}}}}}}