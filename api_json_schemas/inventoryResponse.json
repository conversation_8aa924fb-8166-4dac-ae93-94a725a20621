{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"inventory": {"type": "array", "items": {"type": "object", "properties": {"productId": {"type": "integer", "example": 1, "description": "Unique identifier of product"}, "location": {"type": "array", "items": {"type": "object", "properties": {"locationId": {"type": "integer", "example": 1, "description": "Unique identifier of location"}, "qty": {"type": "integer", "example": 100, "description": "Product quantity for that product"}}}}, "qtyThreshold": {"type": "integer", "example": 10, "description": "Threshold quantity for that product for that particular location"}, "alwaysInStock": {"type": "boolean", "example": true, "description": "If that product is always available for that location then no need to check inventory for that location."}}}}, "totalRecords": {"type": "integer", "example": 9}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}