{"type": "object", "required": ["flightNumber", "scheduledDepartureTime", "departureAirportCodeIATA", "arrivalAirportCodeIATA"], "properties": {"flightNumber": {"type": "string"}, "scheduledDepartureTime": {"type": "string", "format": "date-time"}, "departureAirportCodeIATA": {"type": "string"}, "arrivalAirportCodeIATA": {"type": "string"}}, "description": "Flight details for the specific flights selected for inflight delivery."}