{"type": "object", "required": ["id", "mode", "address"], "properties": {"id": {"type": "number", "description": "Shipment ID in Groundtool"}, "rate": {"$ref": "#/components/schemas/OrderPrice"}, "shippingMethod": {"type": "string", "description": "This can be EXPRESS or STANDARD", "example": "EXPRESS", "enum": ["EXPRESS", "STANDARD"]}, "carrier": {"type": "string", "description": "The shipping carrier\tstring", "example": "FEDEX"}, "address": {"oneOf": [{"$ref": "#/components/schemas/Address"}, {"$ref": "#/components/schemas/OrderFlight"}]}, "item": {"type": "array", "items": {"type": "string", "description": "Id of the order item included in this shipment"}}, "mode": {"type": "string", "enum": ["current_flight", "next_flight", "home_delivery", "next_flight_short_notice"]}, "taxTotal": {"type": "array", "items": {"$ref": "#/components/schemas/OrderPrice"}}}}