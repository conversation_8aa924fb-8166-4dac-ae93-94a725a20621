{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"flights": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1, "description": "returns unique identifier of flight"}, "flightRouteNumber": {"type": "string", "example": "SQ11", "description": "returns filght route number"}, "flightBeginDateTime": {"type": "string", "format": "date-time", "pattern": "YYYY-MM-DD hh:mm", "example": "2023-01-05 21:00", "description": "flightBeginDateTime for First Sector/Leg's Scheduled Departure Datetime. Format is yyyy-mm-dd hh:mm"}, "flightEndDateTime": {"type": "string", "format": "date-time", "pattern": "YYYY-MM-DD hh:ii", "example": "2023-01-06 17:35", "description": "flightEndDateTime for  Last Sector/Leg's Estimated Arrival DateTime. Format is yyyy-mm-dd hh:mm"}, "sectors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id of sector", "example": 1}, "sectorName": {"type": "string", "description": "Name Of Route"}, "summary": {"type": "string", "description": "Description Of Route"}, "sequence": {"type": "integer", "description": "Sequence for sector", "example": 1}}}}, "statusCode": {"type": "boolean", "description": "Status of flight", "example": true}}}}, "totalRecords": {"type": "integer", "example": 1}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}