{"type": "object", "properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"token": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MjI3LCJ0eXBlIjoiiWlybGluZSIsImNyZWF0ZWQiOjE2MzU1OTQzOTcsImV4cGlyeSI6MTYzNTU5NTI5NywiZG9tYWluIjoiIn0.0M8MLyDrVxPrchmQ22MqYOmB7pvfCaVehCns47E7pNE", "description": "Authentication token valid till 15 minues. Token will be used as Bearer token in Authorization Header."}, "refreshToken": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MjI3LCJ0eXBltjoiYWlybGluZSIsImNyZWF0ZWQiOjE2MzU1OTQzOTcsImV4cGlyeSI6MTY2NzEzMDM5NywiZG9tYWluIjoiIn0.0XJrkwbVA70ZU088zwFhHiVr8FgkkhzSSu7Xymef5i0", "description": "Refresh token valid till 365 Days. It will be used as Bearer token in Authorization header in /auth/refresh/token API to Obtain new Token."}}}}}