{"required": ["lineItems", "payment", "shipments", "orderSummary", "createdTime", "externalId", "status", "orderProvider", "seatInfo", "groundSystem"], "type": "object", "properties": {"orderKey": {"type": "string", "description": "Identifier for this order.  This is meant to be used internally within the Marketplace Server.", "format": "uuid"}, "externalId": {"type": "string", "description": "Identifier for this order.  This is meant to be used outside of the Marketplace Server."}, "basketKey": {"type": "string", "description": "Identifier of the basket associated with this order", "format": "uuid"}, "orderProvider": {"type": "string", "description": "order provider for this order whether it is on-board / e-Commerce"}, "modifiedTime": {"type": "string", "description": "Timestamp of when the order was last modified.", "format": "date-time"}, "createdTime": {"type": "string", "description": "Timestamp of when the order was created.", "format": "date-time"}, "lineItems": {"minLength": 1, "type": "array", "description": "A listing of ordered items", "items": {"$ref": "#/components/schemas/OrderSyncItem"}}, "status": {"$ref": "#/components/schemas/OrderSyncOrderStatus"}, "payment": {"oneOf": [{"$ref": "#/components/schemas/ExternalPayment"}, {"$ref": "#/components/schemas/InternalPayment"}]}, "shipments": {"minLength": 1, "type": "array", "description": "An array of shipments associated with this order.", "items": {"$ref": "#/components/schemas/OrderSyncShipment"}}, "orderSummary": {"$ref": "#/components/schemas/OrderSyncOrderSummary"}, "user": {"$ref": "#/components/schemas/OrderSyncUser"}, "itinerary": {"$ref": "#/components/schemas/OrderSyncItinerary"}, "seatInfo": {"$ref": "#/components/schemas/OrderSyncSeatInfo"}, "groundSystem": {"$ref": "#/components/schemas/GroundSystem"}}}