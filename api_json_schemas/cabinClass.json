{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"cabinClasses": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "Retuns ID of Cabin Class", "example": 1}, "name": {"type": "string", "description": "Returns Name of Cabin Class"}, "code": {"type": "string", "description": "Returns code of Cabin Class"}}}}, "totalRecords": {"type": "integer", "example": 1}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}