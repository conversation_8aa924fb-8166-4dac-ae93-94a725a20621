{"type": "object", "properties": {"sectorName": {"type": "string", "example": "SIN-SYD", "description": "Name Of sector"}, "summary": {"type": "string", "example": "Singapore (SIN) to Sydney (SYD)", "description": "Description Of sector", "nullable": true}, "routeGroup": {"type": "string", "example": "Star Alliance", "description": "Name of Route Group", "nullable": true}, "origin": {"type": "string", "example": "VIDP", "description": "Origin Airport ICAO Code"}, "destination": {"type": "string", "example": "VIDP", "description": "Destination Airport ICAO Code"}, "distance": {"type": "integer", "example": 3909, "description": "Distance between two airport", "nullable": true}}}