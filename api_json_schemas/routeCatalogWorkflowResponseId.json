{"type": "object", "properties": {"code": {"type": "number", "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"catalogsAssignments": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "workflowStatus": {"type": "array", "items": {"type": "string", "enum": ["New", "Approved for Association", "Rejected For Association", "Edit Base Data", "Requested for Association", "Requested for Dissociation", "Approved for Dissociation", "Rejected For Dissociation", "Expired", "Airline Category Mapping Pending", "Airline Category Mapped", "Edit Airline Category", "Rejected For Dissociation + Airline Category Mapping Pending", "Rejected For Dissociation + Airline Category Mapped"]}}}}}}}}