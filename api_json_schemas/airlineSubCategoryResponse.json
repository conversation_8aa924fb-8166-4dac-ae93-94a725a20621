{"properties": {"id": {"type": "integer", "example": 1780, "description": "returns unique id for airline category"}, "name": {"type": "string", "example": "Emirates"}, "disclaimer": {"type": "string", "example": "not use in cold"}, "shortDescription": {"type": "string", "example": "Good"}, "description": {"type": "string", "example": "Very Good"}, "url": {"type": "string", "example": "http://test22.com", "description": "returns url for airline category"}, "uiTemplate": {"type": "string", "example": "Uitemplate", "description": "UI Template mapped to airline category"}, "rootType": {"type": "string", "example": "root", "description": "Root Type of airline category"}, "sequenceNumber": {"type": "number", "description": "Sequence number of Airline category"}, "images": {"type": "array", "items": {"type": "object", "properties": {"image1_1": {"type": "string", "example": "https://placehold.jp/1200x1200.png", "description": "Image URL of airline category. The Image Resolution Should be Greater Than or equalto 1200x1200 with 1x1 Aspect Ratio"}, "image2_1": {"type": "string", "example": "https://placehold.jp/1480x740.png", "description": "Image URL of airline category. The Image Resolution Should be Greater Than 1480x740 with 2x1 Aspect Ratio"}, "image4_3": {"type": "string", "example": "https://placehold.jp/760x570.png", "description": "Image URL of airline category. The Image Resolution Should be Greater Than or equalto 760x570 with 4x3 Aspect Ratio"}, "image5_6": {"type": "string", "example": "https://placehold.jp/665x798.png", "description": "Image URL of airline category. The Image Resolution Should be Greater Than or equalto 665x798 with 5x6 Aspect Ratio"}}}}, "bannerImage": {"type": "string", "example": "https://placehold.jp/3160x632.png", "description": "returns banner image of airline category"}, "backgroundImage": {"type": "string", "example": "https://placehold.jp/3840x2160.png", "description": "returns Background image of airline category"}, "subCategory": {"type": "array", "items": {"type": "object", "$ref": "#/components/schemas/airlineSubCategoryResponse"}}}}