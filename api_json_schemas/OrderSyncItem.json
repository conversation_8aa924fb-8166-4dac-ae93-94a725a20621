{"required": ["PACVariantID", "discountTotal", "key", "quantity", "retailerCode", "associateStore", "taxTotal", "title", "unitDiscount", "unitGross", "unitNet", "unitPrice", "unitTax", "vendorProductVariantID"], "type": "object", "properties": {"key": {"type": "string", "description": "Identifier for this line item.  This is meant to be used internally within the Marketplace Server.", "format": "uuid"}, "title": {"type": "string", "description": "Title associated with the item"}, "PACVariantID": {"type": "string", "description": "PAC use this to identifier to identify this item"}, "retailerCode": {"type": "string", "description": "the third-party retailer code"}, "associateStore": {"type": "string", "description": "Store Id for the product"}, "vendorProductVariantID": {"type": "string", "description": "Universal Resource Identifier associated with this item"}, "unitPrice": {"$ref": "#/components/schemas/OrderSyncPrice"}, "quantity": {"type": "number", "description": "Quantity of the item"}, "imageUrl": {"type": "string", "description": "the product's image"}, "status": {"$ref": "#/components/schemas/OrderLineitemSyncStatus"}, "discountTotal": {"$ref": "#/components/schemas/OrderSyncPrice"}, "taxTotal": {"$ref": "#/components/schemas/OrderSyncPrice"}, "discountAdjustments": {"type": "array", "items": {"$ref": "#/components/schemas/OrderSyncAdjustment"}}, "taxAdjustments": {"type": "array", "items": {"$ref": "#/components/schemas/OrderSyncTax"}}, "salePrice": {"$ref": "#/components/schemas/OrderSyncPrice"}, "unitTax": {"$ref": "#/components/schemas/OrderSyncPrice"}, "unitDiscount": {"$ref": "#/components/schemas/OrderSyncPrice"}, "unitGross": {"$ref": "#/components/schemas/OrderSyncPrice"}, "unitNet": {"$ref": "#/components/schemas/OrderSyncPrice"}}, "description": "salePrice = unitPrice - SUM(adjustment.price x count ) \n"}