{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"aircrafts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 543}, "oemname": {"type": "string", "example": "Boeing 787-9 Dreamliner"}, "description": {"type": "string", "example": "Boeing 787-9 Dreamliner"}, "familyName": {"type": "string", "example": "787-9 Dreamliner"}, "modelName": {"type": "string", "example": "787-9 Dreamliner"}, "image": {"type": "string", "example": "https://i.ytimg.com/vi/_gF0fLBYlZ8/maxresdefault.jpg"}}}}, "totalRecords": {"type": "integer", "example": 1}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}