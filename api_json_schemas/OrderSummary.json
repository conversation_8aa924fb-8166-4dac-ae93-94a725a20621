{"type": "object", "required": ["netTotal", "grossTotal", "taxes", "shippingTotal", "totalTaxAmount", "adjustmentTotal"], "properties": {"grossTotal": {"$ref": "#/components/schemas/OrderPrice"}, "discounts": {"type": "array", "items": {"$ref": "#/components/schemas/Adjustment"}}, "adjustmentTotal": {"$ref": "#/components/schemas/OrderPrice"}, "taxes": {"type": "array", "items": {"$ref": "#/components/schemas/Tax"}}, "totalTaxAmount": {"$ref": "#/components/schemas/OrderPrice"}, "shippingTotal": {"$ref": "#/components/schemas/OrderPrice"}, "currency": {"type": "string", "example": "EUR"}, "netTotal": {"$ref": "#/components/schemas/OrderPrice"}}, "description": "netTotal = grossTotal - adjustmentTotal + taxTotal + shippingTotal"}