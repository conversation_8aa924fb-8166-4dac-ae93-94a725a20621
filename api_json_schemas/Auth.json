{"type": "object", "required": ["clientId", "clientSecret"], "properties": {"clientId": {"type": "string", "example": "erXn1jairx", "description": "Client ID of Airline or Store, Can be obtained from Ground tool Portal or can be requested to PAC Ground Tool Admin team"}, "clientSecret": {"type": "string", "example": "c03xiJp&x1nurliccjx7", "description": "Client Secret of Airline or Store, Can be obtained from Ground tool Portal or can be requested to PAC Ground Tool Admin team"}}}