{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"storeLocation": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique ID of location"}, "name": {"type": "string", "description": "Name of location", "example": "<PERSON><PERSON><PERSON> Inventory"}, "fulfillmentOption": {"type": "array", "items": {"type": "integer", "description": "Unique ID of fullment type"}}, "icaoCode": {"type": "string", "example": "PanaSky", "description": "Airport ICAO Code"}}}}}}}