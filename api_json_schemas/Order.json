{"properties": {"orderKey": {"type": "string", "description": "Identifier for this order.  This is meant to be used internally within the Marketplace Server.", "format": "uuid", "example": "80406add-a4d7-43cd-be31-df0ba2bcec3f"}, "externalId": {"type": "string", "description": "Identifier for this order.  This is meant to be used outside of the Marketplace Server."}, "basketKey": {"type": "string", "description": "Identifier of the basket associated with this order", "format": "uuid", "example": "c18220ea-7ee7-4695-9fca-98f3135ba5e1"}, "orderProvider": {"type": "string", "description": "order provider for this order whether it is on-board / e-Commerce", "example": "e-Commerce | on-board"}, "orderState": {"type": "string", "description": "order status in ground tool", "enum": ["Processing", "Shipped", "Cancelled", "Delivered", "Complete", "Failed", "Pending", "Paid"]}, "orderdate": {"type": "string", "description": "Timestamp of when the order was created.", "format": "date-time"}, "orderModificationDate": {"type": "string", "description": "Timestamp of when the order was last modified.", "format": "date-time"}, "fulfillments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "orderItems": {"type": "array", "items": {"properties": {"productId": {"type": "string", "description": "PAC use this to identifier to identify this item Product ID", "example": "2134"}, "storeId": {"type": "string", "description": "Store ID", "example": 1}, "unitPrice": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "quantity": {"type": "number", "description": "Quantity of the item", "example": 5}, "status": {"type": "string", "example": "DELIVERED"}, "discountTotal": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "taxTotal": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "discountAdjustments": {"type": "array", "items": {"type": "object", "properties": {"discountAmount": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "adjustType": {"type": "string", "enum": ["DISCOUNT"]}, "promotionCode": {"type": "string"}, "promotionName": {"type": "string"}, "rate": {"type": "number"}}}}, "taxAdjustments": {"type": "array", "items": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}}, "salePrice": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "unitTax": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "unitDiscount": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "unitGross": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "unitNet": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}}, "minLength": 1}}}}}, "payment": {"type": "array", "description": "A listing of payment instruments used", "items": {"$ref": "#/components/schemas/Payment"}, "minLength": 1, "maxLength": 1}, "shipments": {"type": "array", "description": "An array of shipments associated with this order.", "items": {"$ref": "#/components/schemas/Shipment"}, "minLength": 1}, "orderSummary": {"$ref": "#/components/schemas/OrderSummary"}, "itinerary": {"$ref": "#/components/schemas/Itinerary"}, "meta": {"$ref": "#/components/schemas/OffloadMeta"}, "user": {"$ref": "#/components/schemas/User"}}}