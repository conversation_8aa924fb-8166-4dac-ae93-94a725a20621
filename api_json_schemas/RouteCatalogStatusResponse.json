{"properties": {"code": {"type": "number", "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"catalogsAssignmentAsyncStatus": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Queued", "In-progress", "Completed", "Failed"]}}}}}}}