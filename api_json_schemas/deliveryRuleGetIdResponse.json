{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"deliveryRule": {"type": "object", "properties": {"id": {"type": "integer", "example": 56}, "deliveryRuleType": {"type": "string", "example": "shipping", "description": "deliveryRuleType is restricted to shipping in current scope", "enum": ["shipping", "inflightFuture"]}, "shipmentGroupingType": {"type": "string", "example": "fulfilment_type", "enum": ["fulfilment_type", "basket", "basket_item"], "description": "shipmentGroupingType is restricted to fulfilment_type in current scope"}, "shippingDestination": {"type": "string", "example": "domestic", "enum": ["domestic", "international", "next_flight_short_notice", "next_flight"], "description": "shippingDestination is restricted to domestic & international  in current scope"}, "deliveryType": {"type": "array", "items": {"type": "string", "enum": ["homeShippingDomesticStandard", "homeShippingDomesticExpress", "homeShippingDomesticPriority", "homeShippingInternationalStandard", "homeShippingInternationalExpress", "homeShippingInternationalPriority", "inflightFutureExpress", "inflightFutureStandard"], "description": "inflightFutureExpress & inflightFutureStandard are not in our current scope"}}, "title": {"type": "string"}, "description": {"type": "string"}, "price": {"type": "number", "format": "float", "example": 45}, "priceUnit": {"type": "string", "example": "USD"}, "duration": {"type": "integer", "example": 44}, "durationType": {"type": "string", "enum": ["days", "week"], "example": "days"}}}}}}}