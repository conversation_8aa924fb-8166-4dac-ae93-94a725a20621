{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"rootTypes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 193}, "rootTypeName": {"type": "string", "example": "root"}, "description": {"type": "string"}}}}, "totalRecords": {"type": "integer", "example": 9}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}