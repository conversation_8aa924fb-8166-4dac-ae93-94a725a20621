{"type": "object", "properties": {"sku": {"type": "string", "example": "WNCTXLBL42", "description": "SKU of the Product"}, "name": {"type": "string", "example": "WNCTXLBL42"}, "parentProduct": {"type": "integer", "example": 23, "description": "Parent Product Id. Mandatory field when Product is Variant.", "nullable": true}, "shortDescription": {"type": "string", "example": "Lorem <PERSON> is simply dummy text of the printing", "nullable": true}, "description": {"type": "string", "example": "Lorem <PERSON> is simply dummy text of the printing", "nullable": true}, "barcode": {"type": "string", "example": "TCGA-02-0001-01B", "description": "Product barcode", "nullable": true}, "deliveryMethod": {"type": "array", "description": "Delivery Method of Product", "items": {"type": "string", "example": "Onboard", "enum": ["Next Flight", "Onboard", "Gate Pick Up", "Shipping"]}}, "nextFlightLeadTime": {"type": "number", "description": "Input Next Flight lead time (Business Hrs) for the Product. This field is only applicable when Delivery method is Next Flight.", "example": 12, "nullable": true}, "gatePickupLeadTime": {"type": "number", "description": "Input Gate Pickup lead time (Business Hrs) for the Product . This field is only applicable when Delivery method is Gate Pickup.", "example": 1, "nullable": true}, "category": {"type": "array", "description": "Product Category", "items": {"type": "integer", "example": 123}}, "notApplicableCountries": {"type": "array", "description": "Not applicable countries list. This field will accept country short codes", "items": {"type": "string", "example": "IN"}}, "shippingAttributes": {"type": "object", "description": "Product shipping attributes", "properties": {"weight": {"type": "number", "example": 0, "description": "weight of product", "nullable": true}, "weightUnit": {"type": "string", "example": "KG", "description": "weight unit of product", "nullable": true}, "height": {"type": "number", "example": 0, "description": "height of product", "nullable": true}, "heightUnit": {"type": "string", "example": "CM", "description": "height unit of product", "nullable": true}, "width": {"type": "number", "example": 0, "description": "widht of product", "nullable": true}, "widthUnit": {"type": "string", "example": "CM", "description": "width unit of product", "nullable": true}, "length": {"type": "number", "example": 0, "description": "length of product", "nullable": true}, "lengthUnit": {"type": "string", "example": "CM", "description": "length unit of product", "nullable": true}}}, "assets": {"type": "object", "description": "Product assets i.e image1_1, image2_1, image4_3", "properties": {"images": {"type": "array", "items": {"type": "object", "properties": {"image1_1": {"type": "string", "example": "https://placehold.jp/1200x1200.png", "description": "Image URL of product The Image Resolution Should be Greater Than or equalto 1200x1200 with 1x1 Aspect Ratio"}, "image2_1": {"type": "string", "example": "https://placehold.jp/1480x740.png", "description": "Image URL of product. The Image Resolution Should be Greater Than 1480x740 with 2x1 Aspect Ratio"}, "image4_3": {"type": "string", "example": "https://placehold.jp/760x570.png", "description": "Image URL of product. The Image Resolution Should be Greater Than or equalto 760x570 with 4x3 Aspect Ratio"}, "image2_3": {"type": "string", "example": "https://placehold.jp/944x1416.png", "description": "Image URL of product. The Image Resolution Should be Greater Than or equalto 944x1416 with 2x3 Aspect Ratio"}, "image5_6": {"type": "string", "example": "https://placehold.jp/670x804.png", "description": "Image URL of product. The Image Resolution Should be Greater Than or equalto 670x804 with 5x6 Aspect Ratio"}}}}, "gallery": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "example": "https://content.adidas.co.in/static/Product-EF3509/MEN_CRICKET_SHOES_EF3509_1.jpg", "description": "url of image. This image will be secondary images to be viewed in carousel or gallery. Invalid Image Resolution. Resolution Should be grater than or Equal to 1200x1200 with 1x1 Aspect Ratio/ 1480x740 with 2x1 Aspect Ratio/ 760x570 with 4x3 Aspect Ratio/ 944x1416 with 2x3 Aspect Ratio/ 670x804 with 5x6 Aspect Ratio"}, "imagePosition": {"type": "integer", "example": 1, "description": "position of image to display on PED and IFE"}}}}}}, "newFrom": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-12-20", "description": "Start Date Time for new Tag to be displayed on Product. Format is yyyy-mm-dd", "nullable": true}, "newTo": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2023-02-20", "description": "End Date Time for new Tag to be displayed on Product. Format is yyyy-mm-dd", "nullable": true}, "PriceTaxation": {"type": "object", "description": "Product Price Related Attributes", "properties": {"price": {"type": "array", "description": "Though Price is an array, it is restricted to only 1 element in current scope", "items": {"type": "object", "required": ["value", "currency"], "properties": {"value": {"type": "number", "format": "float", "description": "Price of the Product"}, "currency": {"type": "string", "example": "USD", "description": "Price Currency. Restricted to USD in current scope"}}}}, "specialPrice": {"type": "array", "description": "Though Special Price is an array, it is restricted to only 1 element in current scope", "items": {"type": "object", "properties": {"value": {"type": "number", "format": "float", "description": "Special price of product to sell"}, "currency": {"type": "string", "example": "USD", "description": "Special Price Currency. Restricted to USD in current scope"}}}}, "cost": {"type": "array", "description": "Though Cost is an array, it is restricted to only 1 element in current scope", "items": {"type": "object", "properties": {"value": {"type": "number", "format": "float", "description": "Cost of the Product"}, "currency": {"type": "string", "example": "USD", "description": "Cost Currency. Restricted to USD in current scope"}}}}, "isTaxable": {"type": "boolean", "example": true, "description": "boolean if product is taxable"}, "orderMaxQuantityAllowed": {"type": "number", "example": 2, "description": "Maximum quantity allowed to purchase per order"}, "orderMinQuantityAllowed": {"type": "number", "example": 1, "description": "Minimum quantity to be allowed to purchase per order"}}}, "brand": {"type": "string", "example": "spark"}, "ageVerificationRequire": {"type": "boolean", "example": false, "description": "This field Donates whether buyer's age verification is required for product to be sold"}, "isAvailableToSell": {"type": "boolean", "example": true, "description": "Product availability"}, "Attributes": {"type": "object", "properties": {"productCategoryAttributes": {"type": "array", "description": "These are field which are specific to the category. They are dervied  from the provided category. We have a attribute set field in category from which the fields are derived.", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "color", "description": "Key of Attribute as per attribute set"}, "value": {"anyOf": [{"type": "string", "example": "red"}, {"type": "number", "example": 123}, {"type": "boolean", "example": true}, {"type": "array", "items": {"type": "string", "example": "red"}, "example": ["red", "blue"]}, {"type": "object", "properties": {"key": {"type": "string", "example": "red"}, "value": {"type": "string", "example": "red"}}}]}, "isVariantAttribute": {"type": "boolean", "example": true, "description": "Used to define whether provided productCategoryAttribute is a variant Attribute. This will help differentiate the product variants. For example, clothing category can have multiple attributes like size,color & material, but there could be a case where variants will only vary on color & size, so we will have to set color and size as varaint attribute."}}}}, "storeSpecificAttributes": {"type": "array", "description": "Attributes specific to a store and not mappable to a default field. This is useful in custom integrations and custom adaptor implementations", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "string"}, "value": {"type": "string", "example": "string"}}}}, "specialityAttributes": {"type": "array", "items": {"type": "integer", "example": 284}}}}, "productSet": {"type": "string", "example": "Configurable Or Simple", "description": "Product Set"}, "storeProductId": {"type": "string", "example": "134A34", "description": "Product Unique identifier"}, "productType": {"type": "string", "description": "Product Type", "enum": ["Food and Beverage", "Duty Free", "Amenities"]}, "spotLight": {"type": "boolean", "example": true, "description": "Product Spotlight"}, "requireShipping": {"type": "boolean", "example": false, "description": "Does product require shipping"}, "isPerishable": {"type": "boolean", "example": true, "description": "Is Perishable"}}}