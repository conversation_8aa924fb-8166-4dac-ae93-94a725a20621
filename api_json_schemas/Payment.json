{"type": "object", "required": ["paymentId", "technicalServiceProviderTransactionId", "gatewayTransactionId", "identifier", "authAmount", "billing<PERSON><PERSON>ress"], "properties": {"paymentMethod": {"type": "string", "enum": ["CARD"]}, "authAmount": {"type": "object", "properties": {"currency": {"type": "string", "description": "The currency in which the price amount is based", "example": "EUR"}, "value": {"type": "number", "description": "The value of the price.", "example": 35.78}}}, "paymentId": {"type": "string", "description": "The unique payment identifier or token", "example": "ajsd687362"}, "status": {"type": "string", "description": "Payment authorization status", "example": "AUTHORIZED"}, "billingAddress": {"$ref": "#/components/schemas/Address", "description": "paymentToken or paymentId"}, "technicalServiceProviderTransactionId": {"type": "string", "description": "Technical Service Provider Transaction ID"}, "gatewayTransactionId": {"type": "string", "description": "Gateway Transaction ID"}}}