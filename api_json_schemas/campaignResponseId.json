{"properties": {"code": {"type": "integer", "example": 200, "description": ""}, "success": {"type": "boolean", "example": true, "description": ""}, "data": {"properties": {"campaigns": {"properties": {"id": {"type": "integer", "example": 1, "description": "Returns Unique ID of Campaign"}, "campaignName": {"type": "string", "example": "New Year Offer", "description": "Campaign Name"}, "description": {"type": "string"}, "startDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-04-12", "description": "Start Date in YYYY-MM-DD format"}, "endDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-08-22", "description": "End Date in YYYY-MM-DD format"}, "isActive": {"type": "boolean", "example": true}, "airline": {"type": "integer"}, "campaignType": {"type": "string", "enum": ["Product", "Category", "Catalog", "Promotion"]}, "products": {"type": "array", "items": {"type": "integer", "example": 1}}, "category": {"type": "integer", "example": 1}, "catalog": {"type": "integer", "example": 1}, "promotion": {"type": "array", "items": {"type": "integer", "example": 1}}, "campaignBanners": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "example": "", "description": "returns URL of Image Associated with campaign"}, "bannerPosition": {"type": "string", "enum": ["Top", "Middle", "Bottom"]}}}}, "displayDeviecs": {"type": "string", "enum": ["PED", "<PERSON><PERSON><PERSON>"]}}}}}}}