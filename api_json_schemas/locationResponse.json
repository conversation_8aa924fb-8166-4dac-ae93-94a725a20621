{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"storeLocation": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique ID of location"}, "name": {"type": "string", "description": "Name of location"}, "fulfillmentOption": {"type": "array", "items": {"type": "integer", "description": "Unique ID of fullment type"}}, "icaoCode": {"type": "string", "example": "PanaSky", "description": "Airport ICAO Code"}}}}, "totalRecords": {"type": "integer", "example": 9}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}