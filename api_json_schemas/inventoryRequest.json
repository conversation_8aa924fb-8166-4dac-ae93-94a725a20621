{"required": ["productId", "location", "alwaysInStock", "operation"], "properties": {"productId": {"type": "integer", "example": 1, "description": "Unique identifier of product"}, "location": {"type": "array", "items": {"type": "object", "required": ["locationId", "qty"], "properties": {"locationId": {"type": "integer", "example": 1, "description": "Unique identifier of location"}, "qty": {"type": "integer", "example": 100, "description": "Product quantity for that product"}}}}, "qtyThreshold": {"type": "integer", "example": 10, "description": "Threshold quantity for that product for that particular location"}, "alwaysInStock": {"type": "boolean", "example": true, "description": "If that product is always available for that location then no need to check inventory for that location."}, "operation": {"type": "string", "example": "add/update", "description": "for operation only two possible values which are add and update. If add is defined then quantity will be added to existing quantity if update is defined then quantity will be replaced by latest quantity."}}}