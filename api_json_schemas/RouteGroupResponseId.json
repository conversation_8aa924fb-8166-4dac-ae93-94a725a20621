{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"route_group": {"properties": {"id": {"type": "integer", "example": 123, "description": "returns unique identifier of routegroup"}, "name": {"type": "string", "example": "Star Alliance", "description": "code of route group"}}}}}}}