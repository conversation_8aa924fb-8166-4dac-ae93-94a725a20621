{"properties": {"flightRouteNumber": {"type": "string", "example": "SQ11", "description": "Flight Route number"}, "flightBeginDateTime": {"type": "string", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}$", "example": "2023-01-05 21:00", "description": "flightBeginDateTime for First Sector/Leg's Scheduled Departure Datetime. Format is yyyy-mm-dd hh:mm", "nullable": true}, "flightEndDateTime": {"type": "string", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}$", "example": "2023-01-06 17:35", "description": "flightEndDateTime for  Last Sector/Leg's Estimated Arrival DateTime. Format is yyyy-mm-dd hh:mm", "nullable": true}, "sectors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1, "description": "internal id of the sector"}, "sequence": {"type": "integer", "example": 1, "description": "sequence for sector"}}}}}}