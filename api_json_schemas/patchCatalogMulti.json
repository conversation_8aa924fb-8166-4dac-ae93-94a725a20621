{"type": "object", "properties": {"formatVersion": {"type": "string", "example": 2, "description": "formatVersion"}, "name": {"type": "object", "properties": {"en": {"type": "string", "example": "Furniture"}, "ja": {"type": "string", "example": "家具"}, "de": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}}}, "products": {"type": "array", "items": {"type": "string", "example": "TEST_01", "description": "Array of Product SKUs"}}}}