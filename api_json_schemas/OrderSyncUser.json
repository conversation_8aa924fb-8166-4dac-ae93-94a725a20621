{"required": ["email", "firstName", "lastName"], "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier identifying this user.", "format": "uuid"}, "userName": {"type": "string", "description": "This can be just the seat number."}, "salutation": {"type": "string", "description": "Salutation."}, "firstName": {"type": "string", "description": "User's first name"}, "lastName": {"type": "string", "description": "User's last name"}, "middleName": {"type": "string", "description": "User's middle name"}, "email": {"type": "string", "description": "Email address associated with this user", "format": "email"}, "phone": {"type": "string", "description": "Phone number associated with the user"}, "memberships": {"type": "array", "description": "Loyalty memberships", "items": {"$ref": "#/components/schemas/OrderSyncMembership"}}}}