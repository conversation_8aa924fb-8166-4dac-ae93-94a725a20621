{"properties": {"code": {"type": "integer", "example": 200, "description": ""}, "success": {"type": "boolean", "example": true, "description": ""}, "data": {"properties": {"promotions": {"type": "array", "items": {"properties": {"id": {"type": "integer", "description": "Id of Promotion", "example": 1}, "name": {"type": "string", "description": "Name of Promotion", "example": "Buy One Get One"}, "description": {"type": "string", "description": "Description of Promotion", "example": "Buy One Veg Sandwich and Get One Sandwich Free"}, "disclaimer": {"type": "string", "example": "You need to Order 2 Quantity", "description": "Disclaimer for Promotion"}, "airline": {"type": "integer", "example": 11, "description": "Airline ID in which promotion is applicable."}, "startDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2022-12-25", "description": "Date in YYYY-MM-DD Format, The Date on which promotion will be start"}, "endDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$", "example": "2023-02-15", "description": "Date in YYYY-MM-DD Format, The date on which promotion will end"}, "isActive": {"type": "boolean", "example": true, "description": "Returns true if promotion is active, reurns false if promotion is inactive"}, "couponType": {"type": "string", "enum": ["One Time", "No Limit", "None"], "description": "Returns coupon type if applicable"}, "couponCode": {"type": "string", "example": "BOGO", "description": "Returns Coupon Code to avail this promotion"}, "couponQuantityLimit": {"type": "integer", "example": "10", "description": "Returns Total Coupons available for this promotion"}, "oneRedemptionPerCustomer": {"type": "boolean", "example": true, "description": "returns true if one customer can apply coupon only once"}, "PromotionCombinability": {"type": "boolean", "example": false, "description": "returns True if this promotion can be combined with other pormotion"}, "promotionType": {"type": "number", "example": 1, "description": "Returns ID of Promotion Type Master"}, "promotionApplicableOn": {"type": "string", "enum": ["Product", "Catalog", "Category", "Product Bundle", "None"], "description": "returns on which entity promotion is applicable"}, "applicableProducts": {"type": "array", "items": {"type": "integer", "example": 1, "description": "returns array of product  IDs  on which promotion is applicable"}}, "productDisclaimer": {"type": "string", "description": "returns Product Disclaimer"}, "applicableCategory": {"type": "integer", "example": 1, "description": "returns ID of category on which promiton is applicable"}, "applicableCatalog": {"type": "integer", "example": 1, "description": "returns ID of catalog on which promiton is applicable"}, "exclusionList": {"type": "array", "items": {"type": "integer", "example": 1}, "description": "returns ID of products which is excluded in case of applicable on is selected as catalog or category."}, "productBundle": {"type": "array", "items": {"type": "integer", "example": 1}, "description": "returns ID of Products If promotion is Like Bundle, Promotion is applicable on selected products only"}, "minimumAmountSpent": {"type": "number", "example": 10.2, "description": "returns value if miminum amount spent is set as part of promotion condition"}, "mininumQuantityBought": {"type": "number", "example": 10, "description": "returns value if miminum quantity bought is set as part of promotion condition"}, "maximumQuantityBought": {"type": "number", "example": 20, "description": "returns value if maximum quantity bought is set as part of promotion condition"}, "maxQuantityPassengerCanPurchase": {"type": "number", "example": 10, "description": "returns value if maximum quantity passanger can purchase is set as part of promotion condition"}, "qualifyingItem": {"type": "integer", "example": 1, "description": "returns product id of qualifying item"}, "qualifyingItemDescription": {"type": "string", "description": "returns description of qualifying item"}, "qualifyingItemQuantity": {"type": "integer", "example": 1, "description": "retuns qualifying item quantity for order "}, "freeProduct": {"type": "boolean", "description": "returns true if qualifing item is completely free"}, "itemDiscount": {"properties": {"value": {"type": "integer"}, "discountType": {"type": "string", "enum": ["Amount", "Percentage"]}}, "description": "returns if Qualifying Item has discount amount or percentage"}, "cartDiscount": {"properties": {"value": {"type": "integer"}, "discountType": {"type": "string", "enum": ["Amount", "Percentage"]}}, "description": "returns if discount is applicable on cart total"}}}}, "totalRecords": {"type": "integer", "example": 9}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}