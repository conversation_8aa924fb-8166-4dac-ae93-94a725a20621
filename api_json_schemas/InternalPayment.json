{"type": "object", "required": ["paymentService", "paymentId", "technicalServiceProviderTransactionId", "gatewayTransactionId", "identifier", "authAmount", "billing<PERSON><PERSON>ress"], "properties": {"paymentService": {"type": "string", "enum": ["INTERNAL"]}, "paymentMethod": {"type": "string", "enum": ["CARD"]}, "authAmount": {"$ref": "#/components/schemas/Price"}, "paymentId": {"type": "string", "description": "The unique payment identifier or token", "example": "ajsd687362"}, "status": {"type": "string", "description": "Payment authorization status", "example": "AUTHORIZED"}, "billingAddress": {"$ref": "#/components/schemas/Address", "description": "paymentToken or paymentId"}, "technicalServiceProviderTransactionId": {"type": "string", "description": "Technical Service Provider Transaction ID"}, "gatewayTransactionId": {"type": "string", "description": "Gateway Transaction ID"}}}