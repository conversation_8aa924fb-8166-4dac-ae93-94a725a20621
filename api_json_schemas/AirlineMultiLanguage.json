{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"airline": {"type": "object", "properties": {"id": {"type": "integer", "example": 123, "description": "Unique identifier of airline"}, "ICAOCode": {"type": "string", "example": "PanaSky", "description": "Airline ICAO code"}, "IATACode": {"type": "string", "example": "PanaSky", "description": "Airline IATA Code"}, "operationStatus": {"type": "string", "example": "active or inactive", "description": "returns active if airline is operational else it will return inactive"}, "airlineName": {"type": "object", "properties": {"en": {"type": "string", "example": "Singapore Airlines"}, "ja": {"type": "string", "example": "シンガポール航空"}, "de": {"type": "string", "example": "Singapore Fluggesellschaft"}}}, "logo": {"type": "string", "example": "https://logos-download.com/wp-content/uploads/2016/03/Singapore_Airlines_logo_emblem_logotype_bright-700x256.png", "description": "Url of Airline Logo image"}, "firstName": {"type": "string", "example": "<PERSON>", "description": "First Name of airline administration user"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>", "description": "Last Name of airline administration user"}, "company": {"type": "string", "example": "Singapore Airlines Limited", "description": "company name of airline"}, "addressLine1": {"type": "string", "example": "Airline House 25 Airline Road", "description": "Address line 1 of airline company"}, "addressLine2": {"type": "string", "example": "501 Airmail Transit Centre", "description": "Address line 2 of airline company"}, "city": {"type": "string", "example": "Singapore", "description": "City of airline company"}, "state": {"type": "string", "example": "Central Singapore Community Development Council", "description": "state of airline company"}, "zipCode": {"type": "string", "example": 819829, "description": "zipcode of airline company"}, "country": {"type": "string", "example": "Singapore", "description": "country of airline company"}, "phone": {"type": "string", "example": "65 6329 7537", "description": "contact number of airline"}, "email": {"type": "string", "example": "<EMAIL>", "description": "email address of airline"}, "description": {"type": "object", "properties": {"en": {"type": "string", "example": "Description of Airline"}, "ja": {"type": "string", "example": "航空会社の説明"}, "de": {"type": "string", "example": "Beschreibung der Fluggesellschaft"}}}, "associatedLanguages": {"type": "array", "example": ["en", "ja", "de"], "description": "associated airline store languages", "items": {"type": "string"}}}}}}}}