{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"store": {"type": "object", "properties": {"id": {"type": "integer", "example": 958, "description": "returns unique identifier of store"}, "storeName": {"type": "string", "example": "walmart"}, "description": {"type": "string", "example": "clothing"}, "logo": {"type": "string", "example": "http://panasonic.com/_default_upload_bucket/phones.jpg", "description": "returns url of store logo image"}, "firstName": {"type": "string", "example": "<PERSON>", "description": "returns first name of store admin"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>", "description": "returns last name of store admin"}, "addressLine1": {"type": "string", "example": "ad1", "description": "returns address line 1 of store"}, "addressLine2": {"type": "string", "example": "ad2", "description": "returns address line 2 of store"}, "city": {"type": "string", "example": "city", "description": "returns city name where store belongs to"}, "state": {"type": "string", "example": "state", "description": "returns state name of store"}, "zipCode": {"type": "string", "example": 989898, "description": "returns zipcode of store location"}, "country": {"type": "string", "example": "Albania", "description": "returns country name of store location"}, "phone": {"type": "string", "example": 4354545454, "description": "returns phone number of store"}, "email": {"type": "string", "example": "ma<PERSON><EMAIL>", "description": "returns email address of phone"}, "url": {"type": "string", "format": "nullable", "description": "returns url of store"}, "domain": {"type": "string", "format": "nullable", "description": "return store domain name"}, "passwordMessage": {"type": "string", "format": "nullable", "description": "return store password message"}, "permanentDomain": {"type": "string", "format": "nullable", "description": "returns store permanent <PERSON><PERSON><PERSON> name"}, "types": {"type": "string", "format": "nullable", "description": "Returns an array of all the product types that we have"}, "vendors": {"type": "string", "format": "nullable", "description": "Returns an array of all unique vendors in a store."}, "policies": {"type": "string"}, "privacyPolicy": {"type": "string", "example": "pp"}, "refundPolicy": {"type": "string", "example": "rp"}, "shippingPolicy": {"type": "string", "example": "sp"}, "subscriptionPolicy": {"type": "string", "example": "sub-p"}, "termsOfService": {"type": "string"}, "currency": {"type": "string", "format": "nullable", "description": "Returns a default currency of store."}, "enabledCurrencies": {"type": "array", "items": {"type": "string", "format": "nullable", "description": "Returns a array of all enabled currencies for store."}}, "enabledPaymentTypes": {"type": "string", "format": "nullable"}, "categoriesCount": {"type": "string", "format": "nullable"}, "moneyFormat": {"type": "string", "format": "nullable"}, "moneyWithCurrencyFormat": {"type": "string", "format": "nullable"}, "taxPercentage": {"type": "number", "example": 7, "description": "Tax percent which is defined for store for all products for D2C store"}, "orderThresholdValue": {"type": "string", "example": "10 USD"}, "orderProvider": {"type": "string", "description": "PAC OMS for the store"}}}}}}}