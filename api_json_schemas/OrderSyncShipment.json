{"required": ["address", "id", "mode"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "rate": {"$ref": "#/components/schemas/OrderSyncPrice"}, "shippingMethod": {"type": "string", "description": "This can be EXPRESS or STANDARD", "enum": ["EXPRESS", "STANDARD"]}, "carrier": {"type": "string", "description": "The shipping carrier\tstring"}, "address": {"oneOf": [{"$ref": "#/components/schemas/OrderSyncAddress"}, {"$ref": "#/components/schemas/OrderSyncFlight"}, {"$ref": "#/components/schemas/OrderSyncSeatInfo"}]}, "item": {"type": "array", "items": {"type": "string", "description": "SKU of the order item included in this shipment"}}, "itemKeys": {"type": "array", "items": {"type": "string"}}, "mode": {"type": "string", "enum": ["next_flight", "home_delivery", "next_flight_short_notice"]}, "taxTotal": {"type": "array", "items": {"$ref": "#/components/schemas/OrderSyncPrice"}}}}