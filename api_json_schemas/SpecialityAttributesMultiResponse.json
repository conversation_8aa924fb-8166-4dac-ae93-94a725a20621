{"properties": {"code": {"type": "integer", "example": 200, "description": ""}, "success": {"type": "boolean", "example": true, "description": ""}, "data": {"properties": {"SpecialityAttributes": {"type": "array", "items": {"required": ["id", "shortDescription", "Image"], "properties": {"id": {"type": "integer", "example": 1, "description": "Returns Unique ID of SpecialityAttributes"}, "shortDescription": {"type": "object", "properties": {"en": {"type": "string", "example": "exclusively cooked food"}, "ja": {"type": "string", "example": "専ら調理済み食品"}, "de": {"type": "string", "example": "ausschließlich gekochtes Essen"}}, "required": ["en"]}, "disclaimer": {"type": "object", "properties": {"en": {"type": "string", "example": "exclusively cooked food"}, "ja": {"type": "string", "example": "専ら調理済み食品"}, "de": {"type": "string", "example": "ausschließlich gekochtes Essen"}}, "required": ["en"]}, "image": {"type": "string", "example": "/SpecialityAttributes/update%20451-1653167615.png", "description": "Return Image of SpecialityAttributes"}}}}}}}}