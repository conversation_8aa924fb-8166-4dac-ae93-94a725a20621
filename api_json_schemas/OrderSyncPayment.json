{"required": ["authAmount", "billing<PERSON><PERSON>ress", "gatewayTransactionId", "identifier", "paymentId", "technicalServiceProviderTransactionId"], "type": "object", "properties": {"paymentMethod": {"type": "string", "enum": ["CARD"]}, "authAmount": {"$ref": "#/components/schemas/OrderSyncPrice"}, "paymentId": {"type": "string", "description": "The unique payment identifier or token"}, "status": {"type": "string", "description": "Payment authorization status"}, "billingAddress": {"$ref": "#/components/schemas/OrderSyncAddress"}, "technicalServiceProviderTransactionId": {"type": "string", "description": "Technical Service Provider Transaction ID"}, "gatewayTransactionId": {"type": "string", "description": "Gateway Transaction ID"}}}