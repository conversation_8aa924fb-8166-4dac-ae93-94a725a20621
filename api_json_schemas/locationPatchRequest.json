{"properties": {"name": {"type": "string", "description": "Name of location", "example": "<PERSON><PERSON><PERSON> Inventory", "nullable": true}, "fulfillmentOption": {"type": "array", "nullable": true, "items": {"type": "integer", "example": 1, "description": "Unique ID of fullfillment Type."}}, "icaoCode": {"type": "string", "example": "PanaSky", "description": "Airport ICAO Code", "nullable": true}}}