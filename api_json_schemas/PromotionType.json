{"properties": {"code": {"type": "integer", "example": 200, "description": ""}, "success": {"type": "boolean", "example": true, "description": ""}, "data": {"properties": {"promotionType": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 123, "description": ""}, "promotionTypeName": {"type": "string", "example": "Buy One Get One", "description": ""}, "conditionFields": {"type": "array", "items": {"properties": {"conditionField": {"type": "string", "example": "minimumAmountSpent", "description": ""}, "conditionType": {"type": "string", "enum": ["AND", "OR", ""], "description": ""}}}}, "actionFields": {"type": "array", "items": {"properties": {"conditionField": {"type": "string", "example": "cartDiscount", "description": ""}, "conditionType": {"type": "string", "enum": ["AND", "OR", ""], "description": ""}}}}}}}, "totalRecords": {"type": "integer", "example": 9}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}