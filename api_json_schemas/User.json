{"type": "object", "required": ["firstName", "lastName", "email"], "properties": {"id": {"type": "string", "description": "Unique identifier identifying this user.", "format": "uuid", "example": "bdf02f29-be17-4a54-ae41-cd9698cf6d56"}, "userName": {"type": "string", "description": "This can be just the seat number.", "example": "33A"}, "salutation": {"type": "string", "description": "Salutation.", "example": "Mr."}, "firstName": {"type": "string", "description": "User's first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User's last name", "example": "<PERSON><PERSON>"}, "middleName": {"type": "string", "description": "User's middle name", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "description": "Email address associated with this user", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "Phone number associated with the user", "example": 66038888}, "memberships": {"type": "array", "description": "Loyalty memberships", "items": {"$ref": "#/components/schemas/Membership"}}}}