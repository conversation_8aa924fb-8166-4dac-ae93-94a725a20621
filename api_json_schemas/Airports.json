{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"airports": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 134, "description": "Unique identifier of airport"}, "airportName": {"type": "string", "example": "Singapore Changi Airport", "description": "returns airport name"}, "ICAOCode": {"type": "string", "example": "WSSS", "description": "returns airport ICAO code"}, "IATACode": {"type": "string", "example": "SIN", "description": "returns airport IATA code"}, "address": {"type": "string", "example": "PO Box 168", "description": "returns address of airport"}, "city": {"type": "string", "example": "Singapore", "description": "returns city of airport"}, "state": {"type": "string", "example": "Central Singapore Community Development Council", "description": "returns state of airport"}, "zipCode": {"type": "string", "example": 918146, "description": "returns zipcode of airport"}, "country": {"type": "string", "example": "Singapore", "description": "returns courntry of airport"}, "timeZone": {"type": "string", "example": "Pacific/Niue", "description": "returns timezone of airport"}}}}, "totalRecords": {"type": "integer", "example": 1}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}