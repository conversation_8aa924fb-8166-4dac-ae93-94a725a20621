{"required": ["deliveryRuleType", "shipmentGroupingType", "shippingDestination", "deliveryType", "price", "priceUnit", "duration", "durationType"], "properties": {"deliveryRuleType": {"type": "string", "example": "shipping", "description": "deliveryRuleType is restricted to shipping in current scope", "enum": ["shipping", "inflightFuture"]}, "shipmentGroupingType": {"type": "string", "example": "fulfillment_type", "enum": ["fulfillment_type", "basket", "basket_item"], "description": "shipmentGroupingType is restricted to fulfillment_type in current scope"}, "shippingDestination": {"type": "string", "example": "domestic", "enum": ["domestic", "international", "next_flight_short_notice", "next_flight"], "description": "shippingDestination is restricted to domestic & international  in current scope"}, "deliveryType": {"type": "array", "items": {"type": "string", "enum": ["homeShippingDomesticStandard", "homeShippingDomesticExpress", "homeShippingDomesticPriority", "homeShippingInternationalStandard", "homeShippingInternationalExpress", "homeShippingInternationalPriority", "inflightFutureExpress", "inflightFutureStandard"], "description": "inflightFutureExpress & inflightFutureStandard are not in our current scope"}}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "float", "example": 45}, "priceUnit": {"type": "string", "example": "USD"}, "duration": {"type": "integer", "example": 44}, "durationType": {"type": "string", "enum": ["days", "week"], "example": "days"}}}