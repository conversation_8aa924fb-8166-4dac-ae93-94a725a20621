{"type": "object", "required": ["sectorName", "origin", "destination"], "properties": {"sectorName": {"type": "string", "description": "SIN-SYD", "example": "SIN-SYD"}, "summary": {"type": "string", "example": "Singapore (SIN) to Sydney (SYD)", "description": "Description Of sector", "nullable": true}, "routeGroup": {"type": "string", "example": "Star Alliance", "description": "Name of Route Group", "nullable": true}, "origin": {"type": "string", "example": "VIDP", "description": "Origin Airport ICAO Code"}, "destination": {"type": "string", "example": "VIDP", "description": "Destination Airport ICAO Code"}, "distance": {"type": "integer", "example": 3909, "description": "Distance between two airport", "nullable": true}}}