{"required": ["adjustmentTotal", "grossTotal", "netTotal", "shippingTotal", "taxes", "totalTaxAmount"], "type": "object", "properties": {"grossTotal": {"$ref": "#/components/schemas/OrderSyncPrice"}, "discounts": {"type": "array", "items": {"$ref": "#/components/schemas/OrderSyncAdjustment"}}, "adjustmentTotal": {"$ref": "#/components/schemas/OrderSyncPrice"}, "taxes": {"type": "array", "items": {"$ref": "#/components/schemas/OrderSyncTax"}}, "totalTaxAmount": {"$ref": "#/components/schemas/OrderSyncPrice"}, "shippingTotal": {"$ref": "#/components/schemas/OrderSyncPrice"}, "currency": {"type": "string"}, "netTotal": {"$ref": "#/components/schemas/OrderSyncPrice"}}, "description": "netTotal = grossTotal - adjustmentTotal + taxTotal + shippingTotal"}