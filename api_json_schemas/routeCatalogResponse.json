{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "object", "properties": {"catalogsAssignments": {"type": "array", "items": {"type": "object", "required": ["routeSequences", "id", "workflowStatus", "catalog", "assignmentType"], "properties": {"id": {"type": "integer", "format": "int32", "example": 193}, "workflowStatus": {"type": "array", "items": {"type": "string", "enum": ["New", "Approved for Association", "Rejected For Association", "Edit Base Data", "Requested for Association", "Requested for Dissociation", "Approved for Dissociation", "Rejected For Dissociation", "Expired", "Airline Category Mapping Pending", "Airline Category Mapped", "Edit Airline Category", "Rejected For Dissociation + Airline Category Mapping Pending", "Rejected For Dissociation + Airline Category Mapped"]}}, "catalog": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "example": 192}, "name": {"type": "string", "example": "E Catalog"}, "products": {"type": "array", "items": {"type": "object", "properties": {"productId": {"type": "integer", "description": "Unique identifier of product of catalog"}, "productPrice": {"type": ["number", "null"], "format": "float", "example": 35.5, "description": "Price of product for this route-catalog assignment"}, "productQuantity": {"type": ["integer", "null"], "description": "Available Quantity for this route-catalog assignment"}, "airlineCategory": {"type": "array", "items": {"type": "integer", "description": "Airline Category ID"}}}}}}}, "productSubstitutions": {"type": ["object", "array"], "description": "ORProducts", "properties": {"airlineCategoryId": {"type": "array", "description": "productId's", "items": {"type": "array", "items": {"type": "integer"}, "example": [0, 1, 4]}}}}, "assignmentType": {"type": "string", "example": "string", "enum": ["RouteGroup", "Sector", "Flight"]}, "catalogFromDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$"}, "catalogToDate": {"type": "string", "format": "date", "pattern": "^$|^\\d{4}-\\d{2}-\\d{2}$"}, "cabinClass": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": "string"}, "name": {"type": "string", "example": "string"}}}}, "sector": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 123, "description": "returns unique identifier of sector"}, "sectorName": {"type": "string", "example": "SIN-SYD", "description": "Name Of sector"}, "summary": {"type": ["string", "null"], "example": "Singapore (SIN) to Sydney (SYD)", "description": "Description Of sector"}, "routeGroup": {"type": "string", "example": "Star Alliance", "description": "Name of Route Group"}, "origin": {"type": "string", "example": "Singapore (SIN)", "description": "Origin Airport IATA Code"}, "destination": {"type": "string", "example": "Sydney (SYD)", "description": "Destination Airport IATA Code"}, "distance": {"type": ["string", "null"], "example": "3909 miles", "description": "Distance between two airport"}, "airline": {"type": "string", "example": "Singapore Airlines", "description": "Name of Airline from which this sector belongs to"}, "flights": {"type": "array", "items": {"type": "integer"}}}}}, "routeGroup": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "code": {"type": ["string", "null"]}, "sector": {"type": "array", "items": {"type": "object", "properties": {"sectorId": {"type": "integer", "description": "Unique identifier of Sector"}, "flight": {"type": "array", "items": {"type": "integer"}}, "excluded": {"type": "boolean", "description": "Returns true if sector is excluded from this route group"}}}}}}, "flights": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "flightRouteNumber": {"type": "string", "description": "Flight Route number"}, "sector": {"type": "array", "items": {"type": "integer", "example": 1, "description": "internal id of the sector"}}}}}, "routeSequences": {"type": "object", "properties": {"categories": {"type": "array", "description": "Selected categories of catalog assignment.", "items": {"type": "object", "required": ["id", "sequenceNumber"], "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}], "properties": {"id": {"type": "integer", "example": 250, "description": "Selected category id"}, "sequenceNumber": {"type": "integer", "example": 1}, "subCategories": {"type": "array", "description": "selected sub categories", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 251}, "sequenceNumber": {"type": "integer", "example": 3}, "subCategories": {"type": "array", "description": "selected sub categories", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 251}, "sequenceNumber": {"type": "integer", "example": 3}, "subCategories": {"type": "array", "description": "selected sub categories", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 251}, "sequenceNumber": {"type": "integer", "example": 3}, "subCategories": {"type": "array", "description": "selected sub categories", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 251}, "sequenceNumber": {"type": "integer", "example": 3}, "subCategories": {"type": "array", "description": "selected sub categories", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 251}, "sequenceNumber": {"type": "integer", "example": 3}, "subCategories": {"type": "array", "description": "selected sub categories", "items": {"type": "object", "required": ["id", "sequenceNumber"], "properties": {"id": {"type": "integer", "example": 251}, "sequenceNumber": {"type": "integer", "example": 3}, "subCategories": {"type": "array", "description": "selected sub categories", "items": {"type": "object", "required": ["id", "sequenceNumber", "products"], "properties": {"id": {"type": "integer", "example": 251}, "sequenceNumber": {"type": "integer", "example": 3}, "products": {"type": "array", "description": "Selected products of catalog assignment.", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 9568, "description": "selected product id"}, "sequenceNumber": {"type": "integer", "example": 2}}}}}}}, "products": {"type": "array", "description": "Selected products of catalog assignment.", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 9568, "description": "selected product id"}, "sequenceNumber": {"type": "integer", "example": 2}}}}}, "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}]}}, "products": {"type": "array", "description": "Selected products of catalog assignment.", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 9568, "description": "selected product id"}, "sequenceNumber": {"type": "integer", "example": 2}}}}}, "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}]}}, "products": {"type": "array", "description": "Selected products of catalog assignment.", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 9568, "description": "selected product id"}, "sequenceNumber": {"type": "integer", "example": 2}}}}}, "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}]}}, "products": {"type": "array", "description": "Selected products of catalog assignment.", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 9568, "description": "selected product id"}, "sequenceNumber": {"type": "integer", "example": 2}}}}}, "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}]}}, "products": {"type": "array", "description": "Selected products of catalog assignment.", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 9568, "description": "selected product id"}, "sequenceNumber": {"type": "integer", "example": 2}}}}}, "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}]}}, "products": {"type": "array", "description": "Selected products of catalog assignment.", "items": {"type": "object", "required": ["productId", "sequenceNumber"], "properties": {"productId": {"type": "integer", "example": 9568, "description": "selected product id"}, "sequenceNumber": {"type": "integer", "example": 2}}}}}, "oneOf": [{"required": ["subCategories"]}, {"required": ["products"]}]}}, "products": {"type": "array", "items": {"type": "object", "properties": {"productId": {"type": "integer"}, "sequenceNumber": {"type": "integer"}}, "required": ["productId", "sequenceNumber"]}}}}}}, "required": ["categories"]}}}}}}}}