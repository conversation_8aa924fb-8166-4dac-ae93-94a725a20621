{"properties": {"code": {"type": "number", "example": 200, "description": "HTTP Response Code. For example 200"}, "success": {"type": "boolean", "example": true, "description": "It indicates success or failure or API request"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 285, "description": "returns unique identifier of category"}, "name": {"type": "object", "properties": {"en": {"type": "string", "example": "Mobiles Phones"}, "ja": {"type": "string", "example": "携帯電話"}, "de": {"type": "string", "example": "Mobiltelefone"}}}, "description": {"type": "object", "properties": {"en": {"type": "string", "example": "category Description"}, "ja": {"type": "string", "example": "カテゴリの説明"}, "de": {"type": "string", "example": "Kategorie beschreibung"}}}, "shortDescription": {"type": "object", "properties": {"en": {"type": "string", "example": "short Description"}, "ja": {"type": "string", "example": "簡単な説明"}, "de": {"type": "string", "example": "kurze beschreibung"}}}, "disclaimer": {"type": "object", "properties": {"en": {"type": "string", "example": "Mobiles Phones"}, "ja": {"type": "string", "example": "携帯電話"}, "de": {"type": "string", "example": "Mobiltelefone"}}}, "url": {"type": "string", "format": "nullable", "example": "https://www.example.com", "description": "returns URL of category"}, "publishedAt": {"type": "string", "example": "2021-06-07 04:37:10", "description": "returns published date time. Format is yyyy-mm-dd hh:ii:ss"}, "images": {"type": "array", "items": {"type": "object", "properties": {"image1_1": {"type": "string", "example": "https://placehold.jp/1200x1200.png", "description": "Image URL of airline category. The Image Resolution Should be Greater Than or equalto 1200x1200 with 1x1 Aspect Ratio"}, "image2_1": {"type": "string", "example": "https://placehold.jp/1480x740.png", "description": "Image URL of airline category. The Image Resolution Should be Greater Than 1480x740 with 2x1 Aspect Ratio"}, "image4_3": {"type": "string", "example": "https://placehold.jp/760x570.png", "description": "Image URL of airline category. The Image Resolution Should be Greater Than or equalto 760x570 with 4x3 Aspect Ratio"}, "image5_6": {"type": "string", "example": "hhttps://placehold.jp/665x798.png", "description": "Image URL of airline category. The Image Resolution Should be Greater Than or equalto 668x790 with 5x6 Aspect Ratio"}}}}, "bannerImage": {"type": "string", "example": "https://placehold.jp/3160x632.png", "description": "returns url of banner image"}, "backgroundImage": {"type": "string", "example": "https://placehold.jp/3840x2160.png", "description": "Background image url of category. Resolution Should be Greater Than 3840x2160"}, "attributes": {"type": "object", "properties": {"attributesets": {"type": "array", "description": "attribute sets represent the category specific attributes for a particular category. For example, Clothin category can have fields ike - color,size & material, whereas mobile category will have Ram, OS , Display, Processor etc. The fields set in attribute fields here will be reflected in product of that category.", "items": {"type": "object", "properties": {"attributeSetName": {"type": "string", "example": "Mobile"}, "attributes": {"type": "array", "items": {"type": "string", "example": "RAM"}}}}}, "variantThemes": {"type": "array", "description": "The variant theme will help differentiate the product variants. For example, colour and size two attributes that differentiate the clothing product variants.", "items": {"type": "object", "properties": {"themeName": {"type": "string", "example": "Memory"}, "themeAttributes": {"type": "array", "items": {"type": "string", "example": "RAM"}}}}}}}, "subCategory": {"type": "array", "items": {"type": "object", "$ref": "#/components/schemas/category"}}}}}, "totalRecords": {"type": "integer", "example": 9}, "offset": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}