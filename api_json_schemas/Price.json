{"type": "object", "required": ["catalog"], "properties": {"catalog": {"type": "object", "required": ["id", "products"], "properties": {"id": {"type": "integer", "description": "Unique identifier of Catalog"}, "products": {"type": "array", "items": {"type": "object", "required": ["productId", "productPrice"], "properties": {"productId": {"type": "integer", "description": "Unique identifier of product of catalog"}, "productPrice": {"type": "number", "format": "float", "example": 35.5, "description": "Price for this route-catalog assignment"}}}}}}}}