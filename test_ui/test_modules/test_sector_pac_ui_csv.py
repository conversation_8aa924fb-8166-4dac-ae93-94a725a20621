from jamatest import jamatest
from modules.sector import sector
from users.pacAdmin import pacAdmin
from selenium.webdriver.common.by import By
from project_configs.env_ui_constants import constant
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import get_screenshot, sector_add_csv, sector_update_csv, logger

@jamatest.test(19662743)
def test_pac_csv_add_sector(env, driver):
    function_name = "test_pac_csv_add_sector"
    print(f"\nRunning script: {function_name}")
    sector_add_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_sector = sector(driver, env)
        pacUser.import_csv()
        test_sector.upload_sector_csv()
        test_sector.get_latest_sector()
        sector_id = test_sector.get_id()
        test_sector.read_and_verify_sector_name_in_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Sector ID - {sector_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662744)
def test_pac_csv_update_sector(env, driver):
    function_name = "test_pac_csv_update_sector"
    print(f"\nRunning script: {function_name}")
    sector_update_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_sector = sector(driver, env)
        pacUser.import_csv()
        test_sector.upload_sector_csv()
        test_sector.get_latest_sector()
        sector_id = test_sector.get_id()
        test_sector.read_and_verfy_sector_summary_in_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Sector ID - {sector_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662741)
def test_pac_ui_add_sector(env, driver):
    function_name = "test_pac_ui_add_sector"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_sector = sector(driver, env)
        test_sector.add_sector_ui()
        sector_id = test_sector.get_id(add=True)
        test_sector.fill_sector_details()
        test_sector.select_airline()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Sector ID - {sector_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662742)
def test_pac_ui_update_sector(env, driver):
    function_name = "test_pac_ui_update_sector"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_sector = sector(driver, env)
        test_sector.get_latest_sector()
        sector_id = test_sector.get_id()
        test_sector.update_sector()
        pacUser.save_and_publish()
        print("Sector has been created successfully through UI and verified!")
        logger.info(f"Testcase - {function_name} passed successfully, Sector ID - {sector_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662745)
def test_pac_ui_unpublish_sector(env, driver):
    function_name = "test_pac_ui_unpublish_sector"
    print(f"\nRunning script: {function_name}")

    try:
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_sector = sector(driver, env)
        test_sector.get_latest_sector()
        sector_id = test_sector.get_id()
        WebDriverWait(driver, 90).until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.unpublish_btn_text))).click()
        # saw this popup in qa env - maybe newly added feature, not seen in dev env (last test on jan 16)
        assert WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Sector can not be Unpublished because it is associated with route catalog assignments or Flights')]"))).is_displayed()
        logger.info(f"Testcase - {function_name} passed successfully, Sector ID - {sector_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()