from jamatest import jamatest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from modules.airlineCategory import airlineCategory
from users.airlineAdmin import airlineAdmin
from users.pacAdmin import pacAdmin
from modules.category import category
from users.storeAdmin import storeAdmin
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, category_add_csv, category_update_csv, logger, category_add_csv_with_images_zip, category_add_multilevel_values_csv

@jamatest.test(16284371)
def test_pac_csv_add_category(env, driver):
    function_name = "test_pac_csv_add_category"
    print(f"\nRunning script: {function_name}")
    category_add_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_category = category(driver, env)
        pacUser.import_csv()
        test_category.pac_import_category_csv()
        test_category.pac_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.read_and_verify_category_name_in_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284372)
def test_pac_csv_update_category(env, driver):
    function_name = "test_pac_csv_update_category"
    print(f"\nRunning script: {function_name}")
    category_update_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_category = category(driver, env)
        pacUser.import_csv()
        test_category.pac_import_category_csv()
        test_category.pac_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.read_and_verify_category_updated_disclaimer()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284375)
def test_pac_ui_add_category(env, driver):
    function_name = "test_pac_ui_add_category"
    print(f"\nRunning script: {function_name}")

    try:
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_category = category(driver, env)
        test_category.pac_ui_add_category()
        category_id = test_category.category_get_id()
        test_category.category_add_basedata_details()
        test_category.add_store_details()
        test_category.category_add_attributes()
        test_category.add_asset_data()
        test_category.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284376)
def test_pac_ui_update_category(env, driver):
    function_name = "test_pac_ui_update_category"
    print(f"\nRunning script: {function_name}")

    try:
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_category = category(driver, env)
        test_category.pac_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.category_update_disclaimer()
        test_category.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284377)
def test_pac_ui_unpublish_category(env, driver):
    function_name = "test_pac_ui_unpublish_category"
    print(f"\nRunning script: {function_name}")

    try:
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_category = category(driver, env)
        test_category.pac_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

def test_pac_ui_add_category_and_verify_automap_feature(env, driver):
    function_name = "test_pac_ui_add_category_and_verify_automap_feature"
    print(f"\nRunning script: {function_name}")

    try:
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        airlineUser.airline_automap_on()
        WebDriverWait(driver, 90).until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        airlineUser.logout()
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_category = category(driver, env)
        test_category.pac_ui_add_category()
        test_category.category_add_basedata_details()
        test_category.add_store_details()
        test_category.category_add_attributes()
        test_category.add_asset_data()
        test_category.save_and_publish()
        category_name = test_category.read_name()
        test_airlinecategory = airlineCategory(driver, env)
        test_airlinecategory.navigate_to_airlinecategory_folder_pac()
        test_airlinecategory.search_category_by_name(name=category_name, obj_xpath=constant.latest_airlinecategory_xpath)
        logger.info(f"Testcase - {function_name} passed successfully")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284373)
def test_store_csv_add_category(env, driver):
    function_name = "test_store_csv_add_category"
    print(f"\nRunning script: {function_name}")
    category_add_csv()

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_category = category(driver, env)
        storeUser.import_csv()
        test_category.store_category_csv_import(csv_path="category_sample_csv_path")
        test_category.store_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.read_and_verify_category_name_in_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284374)
def test_store_csv_update_category(env, driver):
    function_name = "test_store_csv_update_category"
    print(f"\nRunning script: {function_name}")
    category_update_csv()

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_category = category(driver, env)
        storeUser.import_csv()
        test_category.store_category_csv_import(csv_path="category_sample_csv_path")
        test_category.store_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.read_and_verify_category_updated_disclaimer()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

def test_store_csv_add_category_with_images_through_zip(env, driver):
    function_name = "test_store_csv_add_category_with_images_through_zip"
    print(f"\nRunning script: {function_name}")
    category_add_csv_with_images_zip()

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_category = category(driver, env)
        storeUser.import_csv()
        test_category.store_category_csv_import_with_zip()
        test_category.store_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.read_and_verify_category_name_in_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

def test_store_csv_add_multilevel_categories(env, driver):
    function_name = "test_store_csv_add_multilevel_categories"
    print(f"\nRunning script: {function_name}")
    names = category_add_multilevel_values_csv()

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_category = category(driver, env)
        storeUser.import_csv()
        test_category.store_category_csv_import(csv_path="category_multilevel_path")
        test_category.store_open_category_folder()
        test_category.search_category(name_to_search=names)
        logger.info(f"Testcase - {function_name} passed successfully")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284378)
def test_store_ui_add_category(env, driver):
    function_name = "test_store_ui_add_category"
    print(f"\nRunning script: {function_name}")

    try:
        storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_category = category(driver, env)
        test_category.store_ui_add_category()
        category_id = test_category.category_get_id()
        test_category.category_add_basedata_details_store()
        test_category.category_add_attributes()
        test_category.add_asset_data()
        test_category.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284379)
def test_store_ui_update_category(env, driver):
    function_name = "test_store_ui_update_category"
    print(f"\nRunning script: {function_name}")

    try:
        storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_category = category(driver, env)
        test_category.store_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.category_update_disclaimer()
        test_category.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284380)
def test_store_ui_unpublish_category(env, driver):
    function_name = "test_store_ui_unpublish_category"
    print(f"\nRunning script: {function_name}")

    try:
        storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_category = category(driver, env)
        test_category.store_get_latest_category()
        category_id = test_category.category_get_id()
        test_category.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, Category ID - {category_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

