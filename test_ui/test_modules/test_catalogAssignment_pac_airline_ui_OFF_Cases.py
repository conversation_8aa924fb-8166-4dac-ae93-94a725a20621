from jamatest import jamatest
from modules.store import store
from modules.airline import airline
from users.pacAdmin import pacAdmin
from users.storeAdmin import storeAdmin
from users.airlineAdmin import airlineAdmin
from selenium.webdriver.common.by import By
from project_configs.aws_config import Config
from project_configs.env_ui_constants import constant
from modules.catalogAssignment import catalogAssignment
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import get_screenshot, logger, wait

def get_queue_url(env):
    config = Config(env)
    queue_url = config.env_aws_pdt_spa_ca()
    if not queue_url:
        print("Failed to get the queue URL")
        return None
    return queue_url

def test_keep_automap_off(env, driver):
    airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
    airlineUser.airline_automap_off()
    WebDriverWait(driver, 90).until(
        EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
    get_screenshot("test_keep_automap_off", driver)
    wait(10)
    driver.quit()

def test_keep_store_autoapprove_on(env, driver):
    pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
    test_catalogAssignment = catalogAssignment(driver, env)
    test_catalogAssignment.open_store_in_pac_admin()
    test_catalogAssignment.check_autoapprove_catalog_store()
    get_screenshot("test_keep_store_autoapprove_on", driver)
    wait(10)
    driver.quit()

def test_keep_airline_autoapprovecatalog_off(env, driver):
    pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
    test_catalogAssignment = catalogAssignment(driver, env)
    test_catalogAssignment.open_airline_in_pac_admin()
    test_catalogAssignment.uncheck_autoupdatecatalog()
    get_screenshot("test_keep_airline_autoapprovecatalog_off", driver)
    wait(10)
    driver.quit()

def test_update_catalog_delta(env, driver):
    pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
    test_catalogAssignment = catalogAssignment(driver, env)
    test_catalogAssignment.catalog_delta_change(update=True)
    get_screenshot("test_update_catalog_delta", driver)
    wait(10)
    driver.quit()

@jamatest.test(17835245)
def test_pac_ui_add_RouteGroup_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(pac=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835247)
def test_pac_ui_add_Flight_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_Flight_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_flight()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(pac=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835246)
def test_pac_ui_add_Sector_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_Sector_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_sector()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(pac=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835248)
def test_pac_ui_add_automapOFF_AirlineCategoryMappingPending_ApprovedForAssociation_withRouteGroup_catalogassignment(
        env, driver):
    function_name = "test_pac_ui_add_automapOFF_AirlineCategoryMappingPending_ApprovedForAssociation_withRouteGroup_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.check_airlinecategory_mapping_state_presence()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20873470)
def test_pac_ui_add_RouteGroup_automapOFF_EditAirlineCategory_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapOFF_EditAirlineCategory_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(pac=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.check_editairlinecategory_state_presence()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157883)
def test_pac_ui_add_RouteGroup_automapOFF_withlatestin5levelairlinecategory_sequences_substitutions_catalogassignment(
        env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapOFF_withlatestin5levelairlinecategory_sequences_substitutions_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.add_pdt_substitution_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157886)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updatecategoryseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updatecategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.update_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157887)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updateproductseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updateproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.update_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157889)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removecategoryseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removecategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.remove_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157891)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removeproductseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removeproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.remove_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157893)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_deletepdtfromcatalogupdatethroughdelta_catalogassignment(
        env, driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_deletepdtfromcatalogupdatethroughdelta_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True, deltachange=True)
        test_catalogAssignment.catalog_delta_change(delete=True)
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes()
        test_catalogAssignment.open_base_data_tab()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta, del_pdt=True)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()


@jamatest.test(20831993)
def test_pac_ui_add_RouteGroup_automapOFF_autoapprovecatalogOFF_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapOFF_autoapprovecatalogOFF_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        test_catalogAssignment.uncheck_autoapprove_catalog_store()
        storeUser.logout()
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment.open_airline_in_pac_admin()
        test_catalogAssignment.uncheck_autoupdatecatalog()
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        ca_to_check = test_catalogAssignment.get_ca_id(id=True)
        test_catalogAssignment.add_delta_catalog()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        pacUser.logout()
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        storeUser.open_latestcatalogassignment_in_store(ca_id=ca_to_check)
        storeUser.perform_action_approve_for_association_state()
        storeUser.logout()
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment.pac_open_latest_ca()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(pac_delta=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        pdt_list_beforechange = test_catalogAssignment.get_products(before_deltachange=True)
        print(pdt_list_beforechange)
        driver.refresh()
        test_catalogAssignment.catalog_delta_change(update=True, airlineOFF=True)
        test_catalogAssignment.open_ca_clear_others()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(delta_off=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        pdt_list_afterchange = test_catalogAssignment.get_products(after_deltachange=True)
        print(pdt_list_afterchange)
        extra_in_list = set(pdt_list_afterchange) - set(pdt_list_beforechange)
        if extra_in_list:
            print("Delta change Auto-Updated! The extra product added is - ", extra_in_list)
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        pacUser.logout()
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment.catalog_delta_change(delete=True)
        test_catalogAssignment.close_filter()
        test_catalogAssignment.open_store_in_pac_admin(store_only=True)
        test_catalogAssignment.check_autoapprove_catalog_store()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157894)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_addpdtfromcatalogupdatethroughdelta_catalogassignment(
        env, driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_addpdtfromcatalogupdatethroughdelta_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True, deltachange=True)
        test_catalogAssignment.catalog_delta_change(update=True)
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(delta_off=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157888)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updateproductsubstitution_catalogassignment(env,
                                                                                                                driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updateproductsubstitution_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.update_substitutions()
        test_catalogAssignment.change_values_in_substitutions()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157892)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removeproductsubstitution_catalogassignment(env,
                                                                                                                driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removeproductsubstitution_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.update_substitutions()
        test_catalogAssignment.remove_substitutions()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157895)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_invalidcategoryseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_invalidcategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.invalid_category_seq()
        test_catalogAssignment.find_error_msg_invalid_category_seq()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20831976)
def test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_invalidproductseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapOFF_alreadyhavingmappedstate_invalidproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.invalid_product_seq()
        test_catalogAssignment.find_error_msg_invalid_product_seq()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157884)
def test_pac_ui_add_RouteGroup_automapOFF_ApprovebyStore_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapOFF_ApprovebyStore_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        test_catalogAssignment.uncheck_autoapprove_catalog_store()
        storeUser.logout()
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        pacUser.logout()
        storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalogAssignment.check_autoapprove_catalog_store(store=True)
        test_catalogAssignment.close_tab()
        storeUser.open_latestcatalogassignment_in_store(ca_id=ca_to_check)
        storeUser.perform_action_approve_for_association_state()
        test_catalogAssignment.check_airlinecategory_mapping_state_presence()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157890)
def test_pac_ui_add_RouteGroup_automapOFF_airlineCategoryNameChange_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapOFF_airlineCategoryNameChange_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(automapOFF=True)
        # test_catalogAssignment.chk_delta_options()
        new_name = test_catalogAssignment.add_category_changes()
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.chk_delta_update(airlineOFF=True)
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.ca_read_category_name(name=new_name)
        test_catalogAssignment.open_category_make_old_name()
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_base_data_tab()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17834467)
def test_airline_ui_add_RouteGroup_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env,
                                                                                                             driver):
    function_name = "test_airline_ui_add_RouteGroup_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(airline=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17834767)
def test_airline_ui_add_Sector_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_Sector_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_sector()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(airline=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17834774)
def test_airline_ui_add_Flight_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_Flight_automapOFF_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_flight()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(airline=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17834775)
def test_airline_ui_add_automapOFF_AirlineCategoryMappingPending_ApprovedForAssociation_withRouteGroup_catalogassignment(
        env, driver):
    function_name = "test_airline_ui_add_automapOFF_AirlineCategoryMappingPending_ApprovedForAssociation_withRouteGroup_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.check_airlinecategory_mapping_state_presence()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157896)
def test_airline_ui_add_RouteGroup_automapOFF_withlatestin5levelairlinecategory_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_RouteGroup_automapOFF_withlatestin5levelairlinecategory_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.add_pdt_substitution_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157899)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updatecategoryseq_catalogassignment(env,
                                                                                                            driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updatecategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.update_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157900)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updateproductseq_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updateproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.update_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20872376)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removecategoryseq_catalogassignment(env,
                                                                                                            driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removecategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.remove_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20872378)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removeproductseq_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removeproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.remove_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157907)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_deletepdtfromcatalogupdatethroughdelta_catalogassignment(
        env, driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_deletepdtfromcatalogupdatethroughdelta_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True, deltachange=True,
                                                                                      airline_first=True)
        test_catalogAssignment.logout()
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment.catalog_delta_change(delete=True)
        pacUser.logout()
        airline_user = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        airline_user.open_latest_ca(id=ca_to_check[2:])
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes(airline_delta_update=True)
        test_catalogAssignment.open_products_tab()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta, del_pdt=True)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157908)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_addpdtfromcatalogupdatethroughdelta_catalogassignment(
        env, driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_addpdtfromcatalogupdatethroughdelta_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True, deltachange=True,
                                                                                      airline_first=True)
        test_catalogAssignment.logout()
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment.catalog_delta_change(update=True)
        pacUser.logout()
        airline_user = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        airline_user.open_latest_ca(id=ca_to_check[2:])
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes(airline_delta_update=True)
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.map_5level_airline_category(delta_off=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157902)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updateproductsubstitution_catalogassignment(env,
                                                                                                                    driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_updateproductsubstitution_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.update_substitutions(airline=True)
        test_catalogAssignment.change_values_in_substitutions()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20872382)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removeproductsubstitution_catalogassignment(env,
                                                                                                                    driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_removeproductsubstitution_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.update_substitutions(airline=True)
        test_catalogAssignment.remove_substitutions()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20873121)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_invalidcategoryseq_catalogassignment(env,
                                                                                                             driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_invalidcategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.invalid_category_seq()
        test_catalogAssignment.find_error_msg_invalid_category_seq()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20873122)
def test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_invalidproductseq_catalogassignment(env,
                                                                                                            driver):
    function_name = "test_airline_ui_update_latestca_automapOFF_alreadyhavingmappedstate_invalidproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.invalid_product_seq()
        test_catalogAssignment.find_error_msg_invalid_product_seq()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157903)
def test_airline_ui_add_RouteGroup_automapOFF_airlineCategoryNameChange_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_RouteGroup_automapOFF_airlineCategoryNameChange_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(automapOFF=True,
                                                                                      autoupdatecatalog=False,
                                                                                      airline_first=True)
        new_name = test_catalogAssignment.add_category_changes_through_airlineadmin()
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.chk_delta_update(airline=True)
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.ca_read_category_name(name=new_name)
        test_catalogAssignment.open_category_make_old_name()
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_base_data_tab()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()