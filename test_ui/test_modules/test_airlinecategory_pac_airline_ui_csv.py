from jamatest import jamatest
from users.pacAdmin import pacAdmin
from selenium.webdriver.common.by import By
from users.airlineAdmin import airlineAdmin
from project_configs.aws_config import Config
from modules.airlineCategory import airlineCategory
from project_configs.env_ui_constants import constant
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import wait, get_screenshot, airlinecategory_add_csv, airlinecategory_update_csv, logger

def get_queue_url(env):
    config = Config(env)
    queue_url = config.env_aws_pdt_spa_ca()
    if not queue_url:
        print("Failed to get the queue URL")
        return None
    return queue_url

def test_keep_automap_off(env, driver):
    airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
    airlineUser.airline_automap_off()
    WebDriverWait(driver, 90).until(
        EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
    wait(10)
    get_screenshot("test_keep_automap_off", driver)
    driver.quit()

@jamatest.test(16284353)
def test_airline_csv_add_airlinecategory(env, driver):
    function_name = "test_airline_csv_add_airlinecategory"
    print(f"\nRunning script: {function_name}")
    airlinecategory_add_csv()

    try:
        test_airlinecategory = airlineCategory(driver, env)
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        airlineUser.import_csv()
        test_airlinecategory.upload_airlinecategory_csv()
        test_airlinecategory.open_latest_airlinecategory()
        airlineUser.get_latest_from_folder(obj_xpath=constant.latest_airlinecategory_xpath)
        test_airlinecategory.read_and_verify_airlinecategory_name_in_csv()
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        ui_data = test_airlinecategory.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        airlineUser.validate_schema(sns_data=sns, object='AirlineCategory')
        result = test_airlinecategory.compare_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284354)
def test_airline_csv_update_airlinecategory(env, driver):
    function_name = "test_airline_csv_update_airlinecategory"
    print(f"\nRunning script: {function_name}")
    airlinecategory_update_csv()

    try:
        test_airlinecategory = airlineCategory(driver, env)
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        airlineUser.import_csv()
        test_airlinecategory.upload_airlinecategory_csv()
        test_airlinecategory.open_latest_airlinecategory()
        airlineUser.get_latest_from_folder(obj_xpath=constant.latest_airlinecategory_xpath)
        # test_airlinecategory.read_and_verify_airlinecategory_name_in_csv()
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        ui_data = test_airlinecategory.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        airlineUser.validate_schema(sns_data=sns, object='AirlineCategory')
        result = test_airlinecategory.compare_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284358)
def test_airline_ui_add_airlinecategory(env, driver):
    function_name = "test_airline_ui_add_airlinecategory"
    print(f"\nRunning script: {function_name}")

    try:
        test_airlinecategory = airlineCategory(driver, env)
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_airlinecategory.add_airlinecategory_ui()
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        test_airlinecategory.airlinecategory_basedatatab()
        test_airlinecategory.airlinecategory_addroottype()
        test_airlinecategory.airlinecategory_assettab()
        airlineUser.save_and_publish()
        test_airlinecategory.open_basedata()
        ui_data = test_airlinecategory.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        airlineUser.validate_schema(sns_data=sns, object='AirlineCategory')
        result = test_airlinecategory.compare_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284359)
def test_airline_ui_update_airlinecategory(env, driver):
    function_name = "test_airline_ui_update_airlinecategory"
    print(f"\nRunning script: {function_name}")

    try:
        test_airlinecategory = airlineCategory(driver, env)
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_airlinecategory.get_latest_airlinecategory()
        airlineUser.get_latest_from_folder(obj_xpath=constant.latest_airlinecategory_xpath)
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        test_airlinecategory.update_airlinecategory()
        airlineUser.save_and_publish()
        ui_data = test_airlinecategory.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        airlineUser.validate_schema(sns_data=sns, object='AirlineCategory')
        result = test_airlinecategory.compare_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284360)
def test_airline_ui_unpublish_airlinecategory(env, driver):
    function_name = "test_airline_ui_unpublish_airlinecategory"
    print(f"\nRunning script: {function_name}")

    try:
        test_airlinecategory = airlineCategory(driver, env)
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_airlinecategory.get_latest_airlinecategory()
        airlineUser.get_latest_from_folder(obj_xpath=constant.latest_airlinecategory_xpath)
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        airlineUser.unpublish()
        ui_data = test_airlinecategory.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        result = test_airlinecategory.compare_unpublished_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284351)
def test_pac_csv_add_airlinecategory(env, driver):
    function_name = "test_pac_csv_add_airlinecategory"
    print(f"\nRunning script: {function_name}")
    airlinecategory_add_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airlinecategory = airlineCategory(driver, env)
        pacUser.import_csv()
        test_airlinecategory.upload_airlinecategory_csv_pac()
        test_airlinecategory.navigate_to_airlinecategory_folder_pac()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_airlinecategory_xpath)
        test_airlinecategory.read_and_verify_airlinecategory_name_in_csv()
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        ui_data = test_airlinecategory.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='AirlineCategory')
        result = test_airlinecategory.compare_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284352)
def test_pac_csv_update_airlinecategory(env, driver):
    function_name = "test_pac_csv_update_airlinecategory"
    print(f"\nRunning script: {function_name}")
    airlinecategory_update_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airlinecategory = airlineCategory(driver, env)
        pacUser.import_csv()
        test_airlinecategory.upload_airlinecategory_csv_pac()
        test_airlinecategory.navigate_to_airlinecategory_folder_pac()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_airlinecategory_xpath)
        test_airlinecategory.read_and_verify_airlinecategory_name_in_csv()
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        ui_data = test_airlinecategory.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='AirlineCategory')
        result = test_airlinecategory.compare_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284355)
def test_pac_ui_add_airlinecategory(env, driver):
    function_name = "test_pac_ui_add_airlinecategory"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airlinecategory = airlineCategory(driver, env)
        test_airlinecategory.navigate_and_create_airlinecategory_pac()
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        test_airlinecategory.airlinecategory_basedatatab()
        test_airlinecategory.airlinecategory_choose_airline()
        test_airlinecategory.airlinecategory_addroottype()
        test_airlinecategory.airlinecategory_assettab()
        pacUser.save_and_publish()
        test_airlinecategory.open_basedata()
        ui_data = test_airlinecategory.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='AirlineCategory')
        result = test_airlinecategory.compare_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284356)
def test_pac_ui_update_airlinecategory(env, driver):
    function_name = "test_pac_ui_update_airlinecategory"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airlinecategory = airlineCategory(driver, env)
        test_airlinecategory.navigate_to_airlinecategory_folder_pac()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_airlinecategory_xpath)
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        test_airlinecategory.update_airlinecategory()
        pacUser.save_and_publish()
        ui_data = test_airlinecategory.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='AirlineCategory')
        result = test_airlinecategory.compare_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284357)
def test_pac_ui_unpublish_airlinecategory(env, driver):
    function_name = "test_pac_ui_unpublish_airlinecategory"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airlinecategory = airlineCategory(driver, env)
        test_airlinecategory.navigate_to_airlinecategory_folder_pac()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_airlinecategory_xpath)
        airlinecategory_to_check = test_airlinecategory.airlinecategory_id_check()
        pacUser.unpublish()
        ui_data = test_airlinecategory.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=airlinecategory_to_check[2:], queue_url=get_queue_url(env))
        result = test_airlinecategory.compare_unpublished_data(dict1=ui_data, dict2=sns)
        assert result, f"Airline Category data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Airline Category - {airlinecategory_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()