from jamatest import jamatest
from users.pacAdmin import pacAdmin
from modules.roottype import roottype
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, logger

def test_pac_ui_add_roottype(env, driver):
    function_name = "test_pac_ui_add_roottype"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_roottype = roottype(driver, env)
        pacUser.home_add_object()
        test_roottype.add_roottype()
        roottype_id = test_roottype.get_id()
        test_roottype.add_basedata()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, RootType ID - {roottype_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662704)
def test_pac_ui_unpublish_roottype(env, driver):
    function_name = "test_pac_ui_unpublish_roottype"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_roottype = roottype(driver, env)
        test_roottype.open_latest_roottype()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_roottype_xpath)
        roottype_id = test_roottype.get_id(unpublish=True)
        pacUser.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, RootType ID - {roottype_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()
