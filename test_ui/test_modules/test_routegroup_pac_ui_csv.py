from jamatest import jamatest
from users.pacAdmin import pacAdmin
from modules.routegroup import routegroup
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, routegrp_update_csv, logger

@jamatest.test(19662716)
def test_pac_csv_update_routegroup(env, driver):
    function_name = "test_pac_csv_update_routegroup"
    print(f"\nRunning script: {function_name}")
    routegrp_update_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_routegrp = routegroup(driver, env)
        pacUser.import_csv()
        test_routegrp.upload_csv()
        test_routegrp.open_latest_routgrp()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_routegroup_xpath)
        routegrp_id = test_routegrp.get_rg_id()
        test_routegrp.verify_values()
        logger.info(f"Testcase - {function_name} passed successfully, RouteGroup ID - {routegrp_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662717)
def test_pac_ui_unpublish_routegroup(env, driver):
    function_name = "test_pac_ui_unpublish_routgroup"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_routegrp = routegroup(driver, env)
        test_routegrp.open_latest_routgrp()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_routegroup_xpath)
        routegrp_id = test_routegrp.get_rg_id()
        pacUser.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, RouteGroup ID - {routegrp_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

