from time import sleep
from jamatest import jamatest
from users.pacAdmin import pacAdmin
from users.storeAdmin import storeAdmin
from users.airlineAdmin import airlineAdmin
from selenium.webdriver.common.by import By
from project_configs.aws_config import Config
from project_configs.env_ui_constants import constant
from modules.catalogAssignment import catalogAssignment
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import get_screenshot, logger, wait

def get_queue_url(env):
    config = Config(env)
    queue_url = config.env_aws_pdt_spa_ca()
    if not queue_url:
        print("Failed to get the queue URL")
        return None
    return queue_url

def test_keep_automap_on(env, driver):
    airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
    airlineUser.airline_automap_on()
    WebDriverWait(driver, 90).until(
        EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
    get_screenshot("test_keep_automap_on", driver)
    sleep(10)
    driver.quit()

def test_keep_store_autoapprove_on(env, driver):
    pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
    test_catalogAssignment = catalogAssignment(driver, env)
    test_catalogAssignment.open_store_in_pac_admin()
    test_catalogAssignment.check_autoapprove_catalog_store()
    get_screenshot("test_keep_store_autoapprove_on", driver)
    wait(10)
    driver.quit()

def test_keep_airline_autoapprovecatalog_off(env, driver):
    pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
    test_catalogAssignment = catalogAssignment(driver, env)
    test_catalogAssignment.open_airline_in_pac_admin()
    test_catalogAssignment.uncheck_autoupdatecatalog()
    get_screenshot("test_keep_airline_autoapprovecatalog_off", driver)
    wait(10)
    driver.quit()

def test_update_catalog_delta(env, driver):
    pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
    test_catalogAssignment = catalogAssignment(driver, env)
    test_catalogAssignment.catalog_delta_change(update=True)
    get_screenshot("test_update_catalog_delta", driver)
    wait(10)
    driver.quit()

@jamatest.test(17835795)
def test_pac_ui_add_RouteGroup_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        # test_catalogAssignment.open_ca_clear_others()
        test_catalogAssignment.add_catalog_5lvl_value()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835797)
def test_pac_ui_add_Flight_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_Flight_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        # test_catalogAssignment.open_ca_clear_others()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_flight()
        test_catalogAssignment.perform_action_request_for_association_state()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835796)
def test_pac_ui_add_Sector_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_Sector_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        # test_catalogAssignment.open_ca_clear_others()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_sector()
        test_catalogAssignment.perform_action_request_for_association_state()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

# def test_pac_ui_add_RouteGroup_automapON_RequestForAssociation_catalogassignment(env, driver):
#     function_name = "test_pac_ui_add_RouteGroup_automapON_RequestForAssociation_catalogassignment"
#     print(f"\nRunning script: {function_name}")
#
#     try:
#         pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
#         test_catalogAssignment = catalogAssignment(driver, env)
#         pacUser.home_add_object()
#         test_catalogAssignment.pac_add_ca()
#         test_catalogAssignment.open_ca_clear_others()
#         ca_to_check = test_catalogAssignment.get_ca_id()
#         test_catalogAssignment.add_catalog_5lvl_value()
#         test_catalogAssignment.add_basedata_details()
#         test_catalogAssignment.add_assignmenttype_routegroup()
#         test_catalogAssignment.perform_action_request_for_association_state()
#         test_catalogAssignment.perform_action_edit_airlinecategory_state()
#         ui_data = test_catalogAssignment.get_data()
#         sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
#         pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
#         result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
#         assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
#         print(f"Testcase - {function_name} passed successfully.")
#         logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
#     finally:
#         get_screenshot(function_name, driver)
#         print("Executed finally block - script end")
#         driver.quit()

@jamatest.test(17835803)
def test_pac_ui_add_RouteGroup_automapON_EditAirlineCategory_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapON_EditAirlineCategory_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        pacUser.home_add_object()
        test_catalogAssignment.pac_add_ca()
        # test_catalogAssignment.open_ca_clear_others()
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details()
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.check_editairlinecategory_state_presence()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157910)
def test_pac_ui_add_RouteGroup_automapON_withlatestin5levelairlinecategory_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapON_withlatestin5levelairlinecategory_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.add_pdt_substitution_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157913)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_updatecategoryseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_updatecategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.update_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level, categoryseq=True)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157914)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_updateproductseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_updateproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.update_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20874097)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_removecategoryseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_removecategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.update_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.remove_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20874100)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_removeproductseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_removeproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.update_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.remove_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157920)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_deletepdtfromcatalogupdatethroughdelta_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_deletepdtfromcatalogupdatethroughdelta_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(deltachange=True)
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.catalog_delta_change(delete=True)
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_ca_clear_others()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes()
        test_catalogAssignment.open_products_tab()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta, del_pdt=True)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157921)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_addpdtfromcatalogupdatethroughdelta_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_addpdtfromcatalogupdatethroughdelta_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(deltachange=True)
        test_catalogAssignment.catalog_delta_change(update=True)
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes()
        test_catalogAssignment.open_products_tab()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157915)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_updateproductsubstitution_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_updateproductsubstitution_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.update_substitutions()
        test_catalogAssignment.change_values_in_substitutions()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20874101)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_removeproductsubstitution_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_removeproductsubstitution_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.update_substitutions()
        test_catalogAssignment.remove_substitutions()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20874147)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_invalidcategoryseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_invalidcategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.invalid_category_seq()
        test_catalogAssignment.find_error_msg_invalid_category_seq()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20874148)
def test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_invalidproductseq_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_alreadyhavingmappedstate_invalidproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.invalid_product_seq()
        test_catalogAssignment.find_error_msg_invalid_product_seq()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157916)
def test_pac_ui_update_latestca_automapON_autoapprovecatalogON_addpdtfromcatalogupdatethroughdelta_add_catalogassignment(env, driver):
    function_name = "test_pac_ui_update_latestca_automapON_autoapprovecatalogON_addpdtfromcatalogupdatethroughdelta_add_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(autoupdatecatalog=True, deltachange=True, id_second=True)
        pdt_list_beforechange = test_catalogAssignment.get_products(before_deltachange=True)
        print(pdt_list_beforechange)
        test_catalogAssignment.catalog_delta_change(update=True, addpdt=True)
        sleep(5)
        test_catalogAssignment.refresh_after_catalog_save()
        test_catalogAssignment.open_latest_created_ca()
        pdt_list_afterchange = test_catalogAssignment.get_products(after_deltachange=True)
        print(pdt_list_afterchange)
        extra_in_list = set(pdt_list_afterchange) - set(pdt_list_beforechange)
        if extra_in_list:
            print("Delta change Auto-Updated! The extra product added is - ", extra_in_list)
        test_catalogAssignment.uncheck_autoupdatecatalog(airline_second=True)
        test_catalogAssignment.update_catalog_delta()
        test_catalogAssignment.open_ca_clear_others()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

# adding values not working from ui, tried multiple ways
# def test_pac_ui_add_RouteGroup_automapON_updatedeploymentinventory_catalogassignment(env, driver):
#     function_name = "test_pac_ui_add_RouteGroup_automapON_updatedeploymentinventory_catalogassignment"
#     print(f"\nRunning script: {function_name}")
#
#     try:
#         test_catalogAssignment = catalogAssignment(driver, env)
#         ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
#         test_catalogAssignment.perform_action_edit_airlinecategory_state()
#         test_catalogAssignment.open_products_tab()
#         test_catalogAssignment.open_update_deployment_inventory()
#         test_catalogAssignment.update_inventory_values()
#         test_catalogAssignment.perform_action_airlinecategory_mapped_state()
#         ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
#         sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
#         pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
#         result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
#         assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
#         print(f"Testcase - {function_name} passed successfully.")
#         logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
#     finally:
#         get_screenshot(function_name, driver)
#         print("Executed finally block - script end")
#         driver.quit()

@jamatest.test(18157919)
def test_pac_ui_add_RouteGroup_automapON_updatedeploymentinventorythroughcsv_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapON_updatedeploymentinventorythroughcsv_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.update_inventory_values_through_csv(ca_id = ca_to_check)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.open_products_tab()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157922)
def test_pac_ui_add_RouteGroup_automapON_DisassociationApproved_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapON_DisassociationApproved_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.perform_action_request_for_dissociation_state()
        test_catalogAssignment.logout()
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        storeUser.open_latestcatalogassignment_in_store(ca_id=ca_to_check)
        storeUser.perform_action_approve_for_dissociation_state()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157911)
def test_pac_ui_add_RouteGroup_automapON_ApprovebyStore_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapON_ApprovebyStore_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalogAssignment.uncheck_autoapprove_catalog_store()
        storeUser.logout()
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin()
        test_catalogAssignment.logout()
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalogAssignment.check_autoapprove_catalog_store(store=True)
        test_catalogAssignment.close_tab()
        storeUser.open_latestcatalogassignment_in_store(ca_id=ca_to_check)
        storeUser.perform_action_approve_for_association_state()
        ui_data = test_catalogAssignment.get_data()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

# for on case cant change name so changing uitemp
@jamatest.test(18157917)
def test_pac_ui_add_RouteGroup_automapON_airlineCategoryNameChange_catalogassignment(env, driver):
    function_name = "test_pac_ui_add_RouteGroup_automapON_airlineCategoryNameChange_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_pacadmin(autoupdatecatalog=False, id_second=True)
        test_catalogAssignment.add_category_changes_for_on_case()
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.chk_delta_update(airline_name_change=True)
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        test_catalogAssignment.update_uitemp_back_to_empty()
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835940)
def test_airline_ui_add_RouteGroup_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_RouteGroup_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835942)
def test_airline_ui_add_Flight_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_Flight_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_flight()
        test_catalogAssignment.perform_action_request_for_association_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835941)
def test_airline_ui_add_Sector_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_Sector_automapON_AirlineCategoryMapped_ApprovedForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_sector()
        test_catalogAssignment.perform_action_request_for_association_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835943)
def test_airline_ui_add_RouteGroup_automapON_EditAirlineCategory_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_RouteGroup_automapON_EditAirlineCategory_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.check_editairlinecategory_state_presence()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17835946)
def test_airline_ui_add_RouteGroup_automapON_RequestForAssociation_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_RouteGroup_automapON_RequestForAssociation_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment = catalogAssignment(driver, env)
        test_catalogAssignment.open_store_in_pac_admin()
        test_catalogAssignment.uncheck_autoapprove_catalog_store()
        pacUser.logout()
        airlineuser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_catalogAssignment.airline_add_ca(airline1=True)
        ca_to_check = test_catalogAssignment.get_ca_id()
        test_catalogAssignment.add_catalog_5lvl_value()
        test_catalogAssignment.add_basedata_details(airline=True)
        test_catalogAssignment.add_assignmenttype_routegroup()
        test_catalogAssignment.perform_action_request_for_association_state()
        test_catalogAssignment.check_requestforassociation_state_presence()
        airlineuser.logout()
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment.open_store_in_pac_admin()
        test_catalogAssignment.check_autoapprove_catalog_store()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157923)
def test_airline_ui_add_RouteGroup_automapON_withlatestin5levelairlinecategory_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_RouteGroup_automapON_withlatestin5levelairlinecategory_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.add_pdt_substitution_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_data()
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157926)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_updatecategoryseq_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_updatecategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.update_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157927)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_updateproductseq_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_updateproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.update_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157929)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_removecategoryseq_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_removecategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.add_category_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.remove_category_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_all_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20883044)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_removeproductseq_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_removeproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.add_product_seq_details()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.remove_product_seq()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157933)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_deletepdtfromcatalogupdatethroughdelta_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_deletepdtfromcatalogupdatethroughdelta_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(deltachange=True, airline_first=True)
        test_catalogAssignment.logout()
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment.catalog_delta_change(delete=True)
        test_catalogAssignment.logout()
        airline_user = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        airline_user.open_latest_ca(id=ca_to_check[2:])
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes(airline_delta_update=True)
        # ui_data = test_catalogAssignment.get_data()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta, del_pdt=True)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157934)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_addpdtfromcatalogupdatethroughdelta_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_addpdtfromcatalogupdatethroughdelta_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(deltachange=True, airline_first=True)
        test_catalogAssignment.logout()
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalogAssignment.catalog_delta_change(update=True)
        test_catalogAssignment.logout()
        airline_user=airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        airline_user.open_latest_ca(id=ca_to_check[2:])
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.delta_update_changes(airline_delta_update=True)
        test_catalogAssignment.open_products_tab()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_delta)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157928)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_updateproductsubstitution_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_updateproductsubstitution_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.update_substitutions(airline=True)
        test_catalogAssignment.change_values_in_substitutions(airline=True)
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(20883045)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_removeproductsubstitution_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_removeproductsubstitution_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_pdt_substitution_tab()
        test_catalogAssignment.update_substitutions(airline=True)
        test_catalogAssignment.remove_substitutions()
        test_catalogAssignment.perform_action_airlinecategory_mapped_state()
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        sns = airlineAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        airlineAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157932)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_invalidcategoryseq_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_invalidcategoryseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_categoryseq_tab()
        test_catalogAssignment.invalid_category_seq()
        test_catalogAssignment.find_error_msg_invalid_category_seq()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(18157935)
def test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_invalidproductseq_catalogassignment(env, driver):
    function_name = "test_airline_ui_update_latestca_automapON_alreadyhavingmappedstate_invalidproductseq_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(airline_first=True)
        test_catalogAssignment.perform_action_edit_airlinecategory_state()
        test_catalogAssignment.open_productseq_tab()
        test_catalogAssignment.invalid_product_seq()
        test_catalogAssignment.find_error_msg_invalid_product_seq()
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

# for on case cant change name so changing uitemp
@jamatest.test(18157930)
def test_airline_ui_add_RouteGroup_automapON_airlineCategoryNameChange_catalogassignment(env, driver):
    function_name = "test_airline_ui_add_RouteGroup_automapON_airlineCategoryNameChange_catalogassignment"
    print(f"\nRunning script: {function_name}")

    try:
        test_catalogAssignment = catalogAssignment(driver, env)
        ca_to_check = test_catalogAssignment.create_catalogassignment_by_airlineadmin(autoupdatecatalog=False, airline_first=True)
        test_catalogAssignment.add_category_changes_for_on_case(airline=True)
        test_catalogAssignment.open_latest_created_ca()
        test_catalogAssignment.open_products_tab()
        test_catalogAssignment.open_ca_clear_others()
        test_catalogAssignment.chk_delta_update(airline_delta=True)
        ui_data = test_catalogAssignment.get_ui_data(catalog=constant.ca_select_catalog_5level)
        test_catalogAssignment.open_category_make_uitemp_empty(airline=True)
        sns = pacAdmin.get_sns_message_by_object_id_ca(id=ca_to_check[2:], queue_url=get_queue_url(env))
        pacAdmin.validate_schema_ca(sns_data=sns, object='CatalogAssignment')
        result = test_catalogAssignment.compare_CatalogAssignment_data(dict1=ui_data, dict2=sns)
        assert result, f"Catalog Assignment data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Catalog Assignment ID - {ca_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()