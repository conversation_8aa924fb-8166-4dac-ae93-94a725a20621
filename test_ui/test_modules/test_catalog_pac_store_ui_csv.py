from jamatest import jamatest
from users.pacAdmin import pacAdmin
from modules.catalog import catalog
from users.storeAdmin import storeAdmin
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, catalog_add_csv, catalog_update_csv, logger

@jamatest.test(16284361)
def test_pac_csv_add_catalog(env, driver):
    function_name = "test_pac_csv_add_catalog"
    print(f"\nRunning script: {function_name}")
    catalog_add_csv(csv_path = "catalog_sample_csv_path")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalog = catalog(driver, env)
        pacUser.import_csv()
        test_catalog.pac_import_catalog_csv()
        test_catalog.pac_open_latest_catalog()
        catalog_id = test_catalog.get_catalog_id()
        test_catalog.read_and_verify_name_in_catalog_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284362)
def test_pac_csv_update_catalog(env, driver):
    function_name = "test_pac_csv_update_catalog"
    print(f"\nRunning script: {function_name}")
    catalog_update_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalog = catalog(driver, env)
        pacUser.import_csv()
        test_catalog.pac_import_catalog_csv()
        test_catalog.pac_open_latest_catalog()
        catalog_id = test_catalog.get_catalog_id()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284365)
def test_pac_ui_add_catalog(env, driver):
    function_name = "test_pac_ui_add_catalog"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalog = catalog(driver, env)
        test_catalog.pac_add_catalog_ui()
        catalog_id = test_catalog.get_catalog_id()
        test_catalog.add_basedatatab_details()
        test_catalog.add_store_details()
        test_catalog.add_producttab_details()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284366)
def test_pac_ui_update_catalog(env, driver):
    function_name = "test_pac_ui_update_catalog"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalog = catalog(driver, env)
        test_catalog.pac_open_latest_catalog()
        catalog_id = test_catalog.get_catalog_id()
        test_catalog.catalog_update_product()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284367)
def test_pac_ui_unpublish_catalog(env, driver):
    function_name = "test_pac_ui_unpublish_catalog"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_catalog = catalog(driver, env)
        test_catalog.pac_open_latest_catalog()
        catalog_id = test_catalog.get_catalog_id()
        pacUser.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284363)
def test_store_csv_add_catalog(env, driver):
    function_name = "test_store_csv_add_catalog"
    print(f"\nRunning script: {function_name}")
    catalog_add_csv(csv_path="catalog_sample_csv_path")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalog = catalog(driver, env)
        storeUser.import_csv()
        test_catalog.store_import_catalog_csv(csv_path="catalog_sample_csv_path")
        test_catalog.store_open_latest_catalog()
        catalog_id = test_catalog.get_catalog_id()
        test_catalog.read_and_verify_name_in_catalog_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284364)
def test_store_csv_update_catalog(env, driver):
    function_name = "test_store_csv_update_catalog"
    print(f"\nRunning script: {function_name}")
    catalog_update_csv()

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalog = catalog(driver, env)
        storeUser.import_csv()
        test_catalog.store_import_catalog_csv(csv_path="catalog_sample_csv_path")
        test_catalog.store_open_latest_catalog()
        catalog_id = test_catalog.get_catalog_id()
        test_catalog.read_and_verify_name_in_catalog_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

def test_store_csv_add_multiple_catalogs(env, driver):
    function_name = "test_store_csv_add_multiple_catalogs"
    print(f"\nRunning script: {function_name}")
    names, products = catalog_add_csv(csv_path="catalog_multiple_sample_csv_path", multiple_import=True)

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalog = catalog(driver, env)
        storeUser.import_csv()
        test_catalog.store_import_catalog_csv(csv_path="catalog_multiple_sample_csv_path")
        test_catalog.store_open_category_folder()
        test_catalog.search_catalog(name_to_search=names, products_to_search=products)
        logger.info(f"Testcase - {function_name} passed successfully")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284368)
def test_store_ui_add_catalog(env, driver):
    function_name = "test_store_ui_add_catalog"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalog = catalog(driver, env)
        test_catalog.store_add_catalog_ui()
        catalog_id = test_catalog.get_catalog_id()
        test_catalog.add_basedatatab_details()
        storeUser.save()
        test_catalog.add_producttab_details()
        storeUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284369)
def test_store_ui_update_catalog(env, driver):
    function_name = "test_store_ui_update_catalog"
    print(f"\nRunning script: {function_name}")

    try:
        storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalog = catalog(driver, env)
        test_catalog.store_open_latest_catalog()
        catalog_id = test_catalog.get_catalog_id()
        test_catalog.catalog_update_product()
        test_catalog.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284370)
def test_store_ui_unpublish_catalog(env, driver):
    function_name = "test_store_ui_unpublish_catalog"
    print(f"\nRunning script: {function_name}")

    try:
        storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_catalog = catalog(driver, env)
        test_catalog.store_open_latest_catalog()
        catalog_id = test_catalog.get_catalog_id()
        test_catalog.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, Catalog ID - {catalog_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()