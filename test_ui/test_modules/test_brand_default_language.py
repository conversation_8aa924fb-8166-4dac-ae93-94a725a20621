from conftest import storeAdminUser
from credencys_test_ui.xpath import refresh
from driver_setup.config import *
from project_configs.csv_config import Config
from users.storeAdmin import storeAdmin
from users.pacAdmin import pacAdmin
from project_configs.login_helpers import logger, brand_update_csv, product_csv_update_brand_id
from project_configs.login_helpers import brand_add_csv
from driver_setup.helper import *
from jamatest import jamatest
# from utils.ui_helper import *
import pytest

###### csv testcases
#----------csv import
@pytest.mark.reg
def test_store_brand_csv_fields_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_brand_csv_fields_with_default_language"
    print(f"\nRunning script: {function_name}")
    storeAdminUser.brand_csv_samplefile_download()
    storeAdminUser.verify_headers_for_csv(valid_headers=['name_en','brand_image1_1','brand_image16_9', 'brand_id'])
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_store_csv_brand_create_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_csv_brand_create_with_default_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_add_sample_csv_path")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.close_all_tabs()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1], brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id}")

@pytest.mark.reg
def test_store_csv_brand_update_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_csv_brand_update_with_default_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_add_sample_csv_path")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1], brand_name=name_in_csv)
    brand.close_all_tabs()
    name_in_csv = brand_update_csv(id=str(brand_id).split()[-1], csv_path="brand_update_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_update_sample_csv_path")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id_updated = brand.getObjectID()
    brand.close_all_tabs()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id_updated).split()[-1], brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id_updated}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id_updated}")

@pytest.mark.reg
def test_store_empty_image_in_csv_import(storeAdminUser:storeAdmin, env):
    function_name = "test_store_empty_image_in_csv_import"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_empty_image_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_empty_image_sample_csv_path")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.close_all_tabs()
    print(f"Testcase - {function_name} passed, Brand ID - {brand_id}")
    logger.info(f"Testcase - {function_name} passed, Brand ID - {brand_id}")

@pytest.mark.reg
def test_store_one_image_in_csv_import(storeAdminUser:storeAdmin, env):
    function_name = "test_store_one_image_in_csv_import"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_one_image_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_one_image_sample_csv_path")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.close_all_tabs()
    print(f"Testcase - {function_name} passed, Brand ID - {brand_id}")
    logger.info(f"Testcase - {function_name} passed, Brand ID - {brand_id}")

def test_store_with_default_language_brand_verify_csv_with_images(storeAdminUser:storeAdmin, env):
    function_name = "test_store_with_default_language_brand_verify_csv_with_images"
    print(f"\nRunning script: {function_name}")
    storeAdminUser.brand_csv_samplefile_download()
    storeAdminUser.verify_headers_for_csv(valid_headers=['name_en', 'brand_image1_1', 'brand_image16_9', 'brand_id'])
    storeAdminUser.read_csv_img_urls()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

def test_store_with_default_language_brand_verify_ui_with_images(storeAdminUser:storeAdmin, env):
    function_name = "test_store_with_default_language_brand_verify_ui_with_images"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    storeAdminUser.verify_images_in_ui()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

#----------csv export
@pytest.mark.reg
def test_store_brand_csv_export_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_brand_csv_export_with_default_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.select_brand(single_export=True)
    brand.click_export_csv_button(export=True)
    storeAdminUser.verify_headers_for_csv(valid_headers=['brand_id', 'name_en', 'brand_image1_1', 'brand_image16_9'])
    object_Data = storeAdminUser.read_brand_export_csv()
    brand.open_selected_brand(single_export=True)
    brand_id = brand.getObjectID()
    id = int(str(brand_id).split()[-1])
    for brand in object_Data:
        if id == brand['brand_id']:
            print("Brand present:", brand)
            storeAdminUser.verify_csv_ui_data(brand)
        else:
            assert False, f"Brand with ID {id} not present"
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_store_multiple_brand_csv_export_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_multiple_brand_csv_export_with_default_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.select_brand(multiple_export=True)
    brand.click_export_csv_button(export=True)
    storeAdminUser.verify_headers_for_csv(valid_headers=['brand_id', 'name_en', 'brand_image1_1', 'brand_image16_9'])
    object_Data = storeAdminUser.read_brand_export_csv()
    brand.click_reload_button()
    brand.open_multiple_selected_brand(object_Data)
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_store_unpublished_brand_csv_export_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_unpublished_brand_csv_export_with_default_language"
    print(f"\nRunning script: {function_name}")
    brandName="Pac_" + generate_short_uuid()
    brand = storeAdminUser.createBrand(brandName)
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    brand.unpublish()
    storeAdminUser.closeObjectOpened()
    storeAdminUser.navigate_to_brand_folder()
    brand.open_unpublished_brand_within_folder(brandName)
    brand.select_brand(single_export=True)
    brand.click_export_csv_button(export=True)
    brand.find_error_msg_for_unpublished()
    brand.click_ok_button()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_store_export_and_import_brand_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_export_and_import_brand_with_default_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.select_brand(single_export=True)
    brand.click_export_csv_button(export=True)
    brand.closeObjectOpened()
    brand_name = storeAdminUser.upload_exported_csv_with_change()
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=brand_name)
    brand.close_all_tabs()

@pytest.mark.reg
def test_store_brand_csv_export_all_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_brand_csv_export_all_with_default_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.click_export_csv_button(export_all=True)
    brand.click_ok_button()
    # brand.find_export_csv_message()
    brand.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

def test_store_csv_brand_create_and_export_to_verify_images_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_csv_brand_create_and_export_to_verify_images_with_default_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_add_sample_csv_path")
    brand_details = storeAdminUser.navigate_to_brand_folder()
    brand_details.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand_details.getObjectID()
    name_ui = brand_details.get_name()
    brand_details.open_brand_folder()
    brand_details.click_export_csv_button(export=True)
    storeAdminUser.verify_headers_for_csv(valid_headers=['brand_id', 'name_en', 'brand_image1_1', 'brand_image16_9'])
    object_Data = storeAdminUser.read_brand_export_csv(img_verify=True)
    id = int(str(brand_id).split()[-1])
    for brand in object_Data:
        if id == brand['brand_id']:
            storeAdminUser.verify_csv_ui_data(brand_details=brand, name=name_ui)
        else:
            assert False, f"Brand with ID {id} not present"
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    brand_details.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1], brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id}")

# -------------------cases yet to be done-------------------#
#
#
#
#
#
# def test_store_update_zipfile_images_for_brand_in_csv_import(storeAdminUser:storeAdmin, pacAdminUser:pacAdmin, env):
#     pass
# #     need to get workflow info from Ryan
#
# # Getting - No item key set. - msg in log.
# def test_store_empty_brand_name_in_csv_import(storeAdminUser:storeAdmin, pacAdminUser:pacAdmin, env):
#     function_name = "test_store_empty_brand_name_in_csv_import"
#     print(f"\nRunning script: {function_name}")
#     brand = storeAdminUser.store_brand_csv_upload(csv_path="brand_name_empty_sample_csv_path")
#     # assertion of error msg in log/email?
#     # Cristian to add code as discussed
#     print(f"Testcase - {function_name} passed")
#     logger.info(f"Testcase - {function_name} passed")
#
# def test_store_invalid_image_in_csv_import(storeAdminUser:storeAdmin, env):
#     function_name = "test_store_invalid_image_in_csv_import"
#     print(f"\nRunning script: {function_name}")
#     brand = storeAdminUser.store_brand_csv_upload(csv_path="brand_invalid_image_sample_csv_path")
#     # assertion of error msg in log/email?
#     # Cristian to add code as discussed
#     print(f"Testcase - {function_name} passed")
#     logger.info(f"Testcase - {function_name} passed")
#
# def test_store_check_duplicate_entry_in_csv_import(storeAdminUser:storeAdmin, env):
#     function_name = "test_store_check_duplicate_entry_in_csv_import"
#     print(f"\nRunning script: {function_name}")
#     brand = storeAdminUser.store_brand_csv_upload(csv_path="brand_duplicate_entry_sample_csv_path")
#     # assertion of error msg in log/email?
#     # Cristian to add code as discussed
#     print(f"Testcase - {function_name} passed")
#     logger.info(f"Testcase - {function_name} passed")
#
# def test_store_import_multiple_brands_in_single_csv_import_to_add(storeAdminUser:storeAdmin, pacAdminUser:pacAdmin, env):
#     pass
#     # to check Cristian's csv code implementation for multi-currency and do
#
# def test_store_import_multiple_brands_in_single_csv_to_update(storeAdminUser:storeAdmin, pacAdminUser:pacAdmin, env):
#     pass
#     # to check Cristian's csv code implementation for multi-currency and do
#
#
#
#
# -------------------cases yet to be done-------------------#


# admin user cases
@pytest.mark.reg
def test_pac_brand_csv_fields_with_default_language(pacAdminUser:pacAdmin, env):
    function_name = "test_pac_brand_csv_fields_with_default_language"
    print(f"\nRunning script: {function_name}")
    pacAdminUser.brand_csv_samplefile_download(pac=True, store=STORE_NAME)
    pacAdminUser.verify_headers_for_csv(valid_headers = ['name_en', 'brand_image1_1', 'brand_image16_9', 'brand_id'])
    pacAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_pac_csv_brand_create_with_default_language(pacAdminUser:pacAdmin, env):
    function_name = "test_pac_csv_brand_create_with_default_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_sample_csv_path")
    pacAdminUser.pac_brand_csv_upload(store=STORE_NAME, csv_path="brand_add_sample_csv_path")
    pacAdminUser.verify_env(store=STORE_NAME)
    brand = pacAdminUser.navigate_to_brand_folder_in_pac(store=STORE_NAME)
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.reload_screen()
    brand.closeObjectOpened()
    brand.closeObjectOpened()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1], brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id}")

@pytest.mark.reg
def test_pac_csv_brand_update_with_default_language(pacAdminUser:pacAdmin, env):
    function_name = "test_pac_csv_brand_update_with_default_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_sample_csv_path")
    pacAdminUser.pac_brand_csv_upload(csv_path="brand_add_sample_csv_path", store=STORE_NAME)
    pacAdminUser.verify_env(store=STORE_NAME)
    brand = pacAdminUser.navigate_to_brand_folder_in_pac(store=STORE_NAME)
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.close_all_tabs()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1],
                      brand_name=name_in_csv)
    name_in_csv = brand_update_csv(id=str(brand_id).split()[-1], csv_path="brand_update_sample_csv_path")
    pacAdminUser.pac_brand_csv_upload(csv_path="brand_update_sample_csv_path", store=STORE_NAME)
    # pacAdminUser.close_filter()
    pacAdminUser.closeObjectOpened()
    brand = pacAdminUser.navigate_to_brand_folder_in_pac(store=STORE_NAME, pac_update=True)
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id_updated = brand.getObjectID()
    pacAdminUser.closeObjectOpened()
    pacAdminUser.closeObjectOpened()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id_updated).split()[-1],
                      brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id_updated}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id_updated}")

###### UI testcases
@pytest.mark.reg
@pytest.mark.ui
@pytest.mark.skipif(len(LANGUAGES_CODES_CONFIGURED) > 1, reason=" Multi-language is configured")
def test_store_admin_brand_default_language(storeAdminUser:storeAdmin, track_brands):
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.uploadImage("100x100.png", "1:1" )
    brand.save_and_publish()
    brand.validateSNS()
    brand.reload_object()
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS()
    storeAdminUser.closeObjectOpened()

@jamatest.test(23)
@pytest.mark.reg
@pytest.mark.ui
@pytest.mark.skipif(len(LANGUAGES_CODES_CONFIGURED) > 1, reason=" Multi-language is configured")
def test_store_admin_unpublish(storeAdminUser:storeAdmin):
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    brand.validateSNS()
    brand.unpublish()
    brand.validateUnpublishSNS()
    brand.save_and_publish()
    brand.validateSNS()
    try:
        brand.delete_object()
        pytest.fail("the delete button was present in the brand of the store admin")
    except:
        print("delete was not found")

@pytest.mark.reg
@pytest.mark.ui
@pytest.mark.skipif(len(LANGUAGES_CODES_CONFIGURED) > 1, reason=" Multi-language is configured")
def test_store_admin_all_images(storeAdminUser:storeAdmin):
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.uploadImage("100x100.png", "1:1" )
    brand.uploadImage("178x100.png", "16:9")
    brand.save_and_publish()
    brand.validateSNS()
    brand.reload_object()
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    brand.validateSNS()

################ Product with brand cases
@pytest.mark.reg
@pytest.mark.ui
def test_store_product_created_with_brand_having_default_language(storeAdminUser:storeAdmin, track_brands):
    function_name = "test_store_product_created_with_brand_having_default_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand_id=brand.getObjectID()
    storeAdminUser.closeAllTabs()
    product = storeAdminUser.create_product("ui_auto" + uuid_random_name(), "Beverages")
    brandId = str(brand_id).split()[-1]
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf", brandId)
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.closeAllTabs()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
@pytest.mark.ui
def test_store_product_created_with_unpublished_brand_having_default_language(storeAdminUser:storeAdmin, track_brands):
    function_name = "test_store_product_created_with_unpublished_brand_having_default_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.unpublish()
    brand.validateUnpublishSNS()
    brandId=brand.id
    storeAdminUser.closeObjectOpened()
    product = storeAdminUser.create_product("ui_auto" + uuid_random_name(), "Beverages" )
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf")
    product.isBrandNotAvailable(brandId)
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
@pytest.mark.ui
def test_pac_product_created_with_brand_having_default_language(pacAdminUser:pacAdmin, track_brands):
    function_name = "test_pac_product_created_with_brand_having_default_language"
    print(f"\nRunning script: {function_name}")
    brand = pacAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.selectStore(store=STORE_ID)
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand_id=brand.getObjectID()
    pacAdminUser.closeObjectOpened()
    product = pacAdminUser.create_product_pac("ui_auto" + uuid_random_name(),"Beverages", store=STORE_NAME)
    brandId = str(brand_id).split()[-1]
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf", brandId)
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    pacAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
@pytest.mark.ui
def test_pac_product_created_with_unpublished_brand_having_default_language(pacAdminUser:pacAdmin, track_brands):
    function_name = "test_pac_product_created_with_unpublished_brand_having_default_language"
    print(f"\nRunning script: {function_name}")
    brand = pacAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.selectStore(STORE_ID)
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.unpublish()
    brand.reload_object()
    brand_id=brand.getObjectID()
    pacAdminUser.closeObjectOpened()
    product = pacAdminUser.create_product_pac("ui_auto" + uuid_random_name(),"Beverages", store=STORE_NAME)
    brandId = str(brand_id).split()[-1]
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf")
    product.isBrandNotAvailable(brandId)
    product.save_and_publish()
    sns = product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    assert "brandId" not in sns
    pacAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
@pytest.mark.ui
def test_pac_product_created_with_delete_brand(pacAdminUser:pacAdmin, track_brands):
    function_name = "test_pac_product_created_with_delete_brand"
    print(f"\nRunning script: {function_name}")
    brand = pacAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.selectStore(STORE_ID)
    brand.save_and_publish(data_tracker=track_brands)
    sleep(2)
    brand.delete_object()
    brandId=brand.id
    product = pacAdminUser.create_product_pac("ui_auto" + uuid_random_name(),"Beverages", store=STORE_NAME)
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf")
    product.isBrandNotAvailable(brandId)
    product.save_and_publish()
    sns = product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    assert "brandId" not in sns
    pacAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

# product csv import with brand
def test_store_product_csv_with_default_language_brand_headers_verify(storeAdminUser:storeAdmin, env):
    function_name = "test_store_product_csv_with_default_language_brand_headers_verify"
    print(f"\nRunning script: {function_name}")
    storeAdminUser.product_csv_samplefile_download(default_lang=True)
    storeAdminUser.verify_headers_for_csv(valid_headers=["name_en", "shortDescription_en", "description_en", "sku", "brand_id", "barcode", "deliveryMethod", "nextFlightLeadTime(Business Hrs)",
                                                         "gatePickupLeadTime(Business Hrs)", "productType", "productSet", "notApplicableCountries",
                                                         "newFrom", "newTo", "spotLight", "ageVerificationRequire", "isAvailableToSell", "requireShipping",
                                                         "isvariantDefault", "isPerishable", "image1_1", "image2_1", "image4_3", "image2_3", "image5_6",
                                                         "gallery", "price_USD", "specialPrice_USD", "isTaxable", "minQuantityAllowed/Order",
                                                         "maxQuantityAllowed/Order", "weight", "weightUnit", "shippingLength", "shippingLengthUnit",
                                                         "shippingWidth", "shippingWidthUnit", "shippingHeight", "shippingHeightUnit",
                                                         "customAttribute", "specialityAttribute", "productId", "isVariant", "parentSku", "variantAttributes"])
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

def test_store_csv_import_product_with_and_without_brand(store_http_session, track_product_input_data_map, storeAdminUser:storeAdmin, env,
                                                         track_brands):
    function_name = "test_store_csv_import_product_with_and_without_brand"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    sleep(2)
    brandId = brand.id
    storeAdminUser.closeObjectOpened()
    product_csv_update_brand_id(csv_path="product_csv_path_after_name_update", brand_id=brandId)
    storeAdminUser.store_product_csv_upload(csv_path="product_csv_path_after_name_update", categoryPath=CATEGORY_PATH)
    csv_reference = Config.get_path("product_with_brand_and_without_brand")
    test_filename = generate_test_csv(in_file=csv_reference)
    test_json_products_list = product_csv_to_json(test_filename)
    sleep(3)
    storeAdminUser.closeObjectOpened()
    num_products = len(test_json_products_list)
    temp_list = test_json_products_list
    logger.debug("Number of products created to verify: %s", num_products)
    sleep(25)
    temp_list = test_json_products_list
    all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
    for product in all_get_product_list:
        for p_in in test_json_products_list:
            if product['sku'] == p_in['sku']:
                track_product_input_data_map[product['id']] = p_in
                print(f"the id {product['id']}")
                logger.info("Found a matching product %s", product['sku'])
                sns = get_sns_message_by_id_catalog(product["id"],
                                                    retries=25)  # incase the message was read in previous
                validate_product(sns)
                assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in, default_lang=True))
                num_products = num_products - 1
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
            elif "fails" in product['sku']:  # to make sure failed products are not getting created
                product_id = product["id"]
                raise Exception(f"There was a product with fails {product_id}")
    if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")

def test_store_csv_import_two_products_with_same_brand(store_http_session, track_product_input_data_map, storeAdminUser:storeAdmin, env,
                                                         track_brands):
    function_name = "test_store_csv_import_two_products_with_same_brand"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    sleep(2)
    brandId = brand.getObjectID()
    storeAdminUser.closeObjectOpened()
    product_csv_update_brand_id(csv_path="product_csv_path_after_brand_update", brand_id=brandId, same_brand=True) #testfile_Products_same_brand.csv
    storeAdminUser.store_product_csv_upload(csv_path="product_csv_path_after_brand_update", categoryPath=CATEGORY_PATH)
    csv_reference = Config.get_path("product_csv_path_after_brand_update")  # Products_same_brand.csv
    test_filename = generate_test_csv(in_file=csv_reference)
    # print("test_filename=====", test_filename)
    test_json_products_list = product_csv_to_json(test_filename)
    sleep(3)
    storeAdminUser.closeObjectOpened()
    num_products = len(test_json_products_list)
    temp_list = test_json_products_list
    logger.debug("Number of products created to verify: %s", num_products)
    sleep(25)
    temp_list = test_json_products_list
    all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
    for product in all_get_product_list:
        for p_in in test_json_products_list:
            if product['sku'] == p_in['sku']:
                track_product_input_data_map[product['id']] = p_in
                print(f"the id {product['id']}")
                logger.info("Found a matching product %s", product['sku'])
                sns = get_sns_message_by_id_catalog(product["id"],
                                                    retries=25)  # incase the message was read in previous
                validate_product(sns)
                assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in, default_lang=True))
                num_products = num_products - 1
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
            elif "fails" in product['sku']:  # to make sure failed products are not getting created
                product_id = product["id"]
                raise Exception(f"There was a product with fails {product_id}")
    if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")

def test_store_csv_export_and_update_product_with_brand(store_http_session, track_product_input_data_map, storeAdminUser:storeAdmin, env,
                                                         track_brands):
    function_name = "test_store_csv_export_and_update_product_with_brand"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand_id = brand.getObjectID()
    storeAdminUser.closeObjectOpened()
    product = storeAdminUser.create_product("ui_auto" + uuid_random_name(), f"Food & Drinks ({STORE_NAME}/Food & Drinks)")
    brandId = str(brand_id).split()[-1]
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf")
    product.save_and_publish()
    pdt_id = product.getObjectID()
    storeAdminUser.closeObjectOpened()
    storeAdminUser.navigate_to_product_folder()
    product.search_pdt(id=pdt_id, after_export=True)
    product.click_export_csv_button(export=True)
    sleep(1)
    storeAdminUser.closeObjectOpened()
    storeAdminUser.verify_headers_for_csv(
        valid_headers=["name_en", "shortDescription_en", "description_en",
                                                         "sku", "brand_id", "barcode", "deliveryMethod", "nextFlightLeadTime(Business Hrs)",
                                                         "gatePickupLeadTime(Business Hrs)", "productType", "productSet", "notApplicableCountries",
                                                         "newFrom", "newTo", "spotLight", "ageVerificationRequire", "isAvailableToSell", "requireShipping",
                                                         "isvariantDefault", "isPerishable", "image1_1", "image2_1", "image4_3", "image2_3", "image5_6",
                                                         "gallery", "price_USD", "specialPrice_USD", "isTaxable", "minQuantityAllowed/Order",
                                                         "maxQuantityAllowed/Order", "weight", "weightUnit", "shippingLength", "shippingLengthUnit",
                                                         "shippingWidth", "shippingWidthUnit", "shippingHeight", "shippingHeightUnit",
                                                         "customAttribute", "specialityAttribute", "productId", "isVariant", "parentSku", "variantAttributes"])
    latest_csv = product.update_brand_and_product_csv_upload(brand_id=brandId)
    storeAdminUser.closeObjectOpened()
    product.search_pdt(id=pdt_id, after_export=True)
    product.open_product(default_lang=True)
    product.isBrandpresentinui(id=brandId)
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print("latest", latest_csv)
    csv_reference = latest_csv
    test_filename = generate_test_csv(in_file=csv_reference)
    test_json_products_list = product_csv_to_json(test_filename)
    num_products = len(test_json_products_list)
    temp_list = test_json_products_list
    logger.debug("Number of products created to verify: %s", num_products)
    sleep(25)
    temp_list = test_json_products_list
    all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
    for product in all_get_product_list:
        for p_in in test_json_products_list:
            if product['sku'] == p_in['sku']:
                track_product_input_data_map[product['id']] = p_in
                print(f"the id {product['id']}")
                logger.info("Found a matching product %s", product['sku'])
                sns = get_sns_message_by_id_catalog(product["id"],
                                                    retries=25)  # incase the message was read in previous
                validate_product(sns)
                assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in))
                num_products = num_products - 1
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
            elif "fails" in product['sku']:  # to make sure failed products are not getting created
                product_id = product["id"]
                raise Exception(f"There was a product with fails {product_id}")
    if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")