from conftest import driver
from jamatest import jamatest
from modules.flight import flight
from users.pacAdmin import pacAdmin
from users.airlineAdmin import airlineAdmin
from project_configs.aws_config import Config
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, flight_pac_add_csv, flight_pac_update_csv, flight_add_csv, \
    flight_update_csv, logger

def get_queue_url(env):
    config = Config(env)
    queue_url = config.env_aws_flight()
    if not queue_url:
        print("Failed to get the queue URL")
        return None
    return queue_url

@jamatest.test(17834183)
def test_pac_csv_add_flight(env, driver):
    function_name = "test_pac_csv_add_flight"
    print(f"\nRunning script: {function_name}")
    flight_pac_add_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_flight = flight(driver, env)
        pacUser.import_csv()
        test_flight.upload_flight_csv()
        test_flight.open_latest_flight()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_flight_xpath)
        test_flight.verify_flight_csv_uploaded()
        flight_to_check = test_flight.get_flight_id()
        ui_data = test_flight.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='Flights')
        result = test_flight.compare_flight_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17834184)
def test_pac_csv_update_flight(env, driver):
    function_name = "test_pac_csv_update_flight"
    print(f"\nRunning script: {function_name}")
    flight_pac_update_csv()
    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_flight = flight(driver, env)
        pacUser.import_csv()
        test_flight.upload_flight_csv()
        test_flight.open_latest_flight()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_flight_xpath)
        flight_to_check = test_flight.get_flight_id()
        test_flight.verify_flight_csv_uploaded_after_update()
        ui_data = test_flight.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='Flights')
        result = test_flight.compare_flight_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17834180)
def test_pac_ui_add_flight(env, driver):
    function_name = "test_pac_ui_add_flight"
    print(f"\nRunning script: {function_name}")
    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_flight = flight(driver, env)
        pacUser.home_add_object()
        test_flight.create_flight()
        flight_to_check = test_flight.get_flight_id()
        test_flight.enter_flight_information()
        test_flight.select_airline()
        test_flight.add_sector()
        test_flight.save_and_publish()
        ui_data = test_flight.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='Flights')
        result = test_flight.compare_flight_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        # ----to check sns in ui and download it by matching the ID----
        # pacUser.navigate_to_sns(add=True)
        # sns_found = pacUser.verify_sns(id=flight_to_check, save=True,
        #                                 sns_published=constant.flight_sns_latest_published_xpath,
        #                                 sns_id_chk=constant.sns_id_chk_xpath, flight=True)
        # pacUser.verify_sns_and_download(crct_sns=sns_found, id=flight_to_check,
        #                                   sns_published=constant.flight_sns_latest_published_xpath, save=True)
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17834181)
def test_pac_ui_update_flight(env, driver):
    function_name = "test_pac_ui_update_flight"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_flight = flight(driver, env)
        test_flight.open_latest_flight()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_flight_xpath)
        flight_to_check = test_flight.get_flight_id()
        test_flight.flight_ui_update()
        test_flight.save_and_publish()
        ui_data = test_flight.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='Flights')
        result = test_flight.compare_flight_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(17834182)
def test_pac_ui_unpublish_flight(env, driver):
    function_name = "test_pac_ui_unpublish_flight"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_flight = flight(driver, env)
        test_flight.open_latest_flight()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_flight_xpath)
        flight_to_check = test_flight.get_flight_id()
        test_flight.unpublish()
        ui_data = test_flight.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        result = test_flight.compare_flight_unpublished_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661612)
def test_airline_csv_add_flight(env, driver):
    function_name = "test_airline_csv_add_flight"
    print(f"\nRunning script: {function_name}")
    flight_add_csv()

    try:
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_flight = flight(driver, env)
        airlineUser.import_csv()
        test_flight.upload_airline_flight_csv()
        test_flight.open_latest_flight_airline()
        airlineUser.get_latest_from_folder(obj_xpath=constant.latest_flight_xpath)
        flight_to_check = test_flight.get_flight_id()
        ui_data = test_flight.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        airlineUser.validate_schema(sns_data=sns, object='Flights')
        result = test_flight.compare_flight_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661613)
def test_airline_csv_update_flight(env, driver):
    function_name = "test_airline_csv_update_flight"
    print(f"\nRunning script: {function_name}")
    flight_update_csv()
    try:
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_flight = flight(driver, env)
        airlineUser.import_csv()
        test_flight.upload_airline_flight_csv()
        test_flight.open_latest_flight_airline()
        airlineUser.get_latest_from_folder(obj_xpath=constant.latest_flight_xpath)
        flight_to_check = test_flight.get_flight_id()
        ui_data = test_flight.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        airlineUser.validate_schema(sns_data=sns, object='Flights')
        result = test_flight.compare_flight_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661609)
def test_airline_ui_add_flight(env, driver):
    function_name = "test_airline_ui_add_flight"
    print(f"\nRunning script: {function_name}")
    try:
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_flight = flight(driver, env)
        airlineUser.add_flight()
        flight_to_check = test_flight.get_flight_id()
        test_flight.enter_flight_information()
        test_flight.add_sector()
        test_flight.save_and_publish()
        ui_data = test_flight.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        airlineUser.validate_schema(sns_data=sns, object='Flights')
        result = test_flight.compare_flight_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661610)
def test_airline_ui_update_flight(env, driver):
    function_name = "test_airline_ui_update_flight"
    print(f"\nRunning script: {function_name}")
    try:
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_flight = flight(driver, env)
        test_flight.open_latest_flight_airline()
        airlineUser.get_latest_from_folder(obj_xpath=constant.latest_flight_xpath)
        flight_to_check = test_flight.get_flight_id()
        test_flight.flight_ui_update()
        test_flight.save_and_publish()
        ui_data = test_flight.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        airlineUser.validate_schema(sns_data=sns, object='Flights')
        result = test_flight.compare_flight_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661611)
def test_airline_ui_unpublish_flight(env, driver):
    function_name = "test_airline_ui_unpublish_flight"
    print(f"\nRunning script: {function_name}")
    try:
        airlineUser = airlineAdmin(driver, constant.airline_user_username, constant.airline_user_password, env)
        test_flight = flight(driver, env)
        test_flight.open_latest_flight_airline()
        airlineUser.get_latest_from_folder(obj_xpath=constant.latest_flight_xpath)
        flight_to_check = test_flight.get_flight_id()
        airlineUser.unpublish()
        ui_data = test_flight.get_data()
        sns = airlineUser.get_sns_message_by_object_id(id=flight_to_check[2:], queue_url=get_queue_url(env))
        result = test_flight.compare_flight_unpublished_data(dict1=ui_data, dict2=sns)
        assert result, f"Flight data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Flight ID - {flight_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()