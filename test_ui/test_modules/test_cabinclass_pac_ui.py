from jamatest import jamatest
from users.pacAdmin import pacAdmin
from modules.cabinClass import cabinClass
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, logger

def test_pac_ui_add_cabinclass(env, driver):
    function_name = "test_pac_ui_add_cabinclass"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_cabicls = cabinClass(driver,env)
        pacUser.home_add_object()
        test_cabicls.add_cabinclass()
        cabincls_id = test_cabicls.get_id()
        test_cabicls.add_basedata()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Cabin Class ID - {cabincls_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661453)
def test_pac_ui_unpublish_cabinclass(env, driver):
    function_name = "test_pac_ui_unpublish_cabinclass"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_cabicls = cabinClass(driver,env)
        test_cabicls.open_pac_latest_cabinclass()
        cabincls_id = test_cabicls.get_id(unpublish=True)
        pacUser.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, Cabin Class ID - {cabincls_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()