from jamatest import jamatest
from modules.product import product
from users.pacAdmin import pacAdmin
from users.storeAdmin import storeAdmin
from project_configs.aws_config import Config
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, product_add_csv, product_update_csv, logger

def get_queue_url(env):
    config = Config(env)
    queue_url = config.env_aws_pdt_spa_ca()
    if not queue_url:
        print("Failed to get the queue URL")
        return None
    return queue_url

@jamatest.test(16284384)
def test_pac_csv_add_product(env, driver):
    function_name = "test_pac_csv_add_product"
    print(f"\nRunning script: {function_name}")
    product_add_csv(env)

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_product = product(driver, env)
        pacUser.import_csv()
        test_product.upload_pdt_pac_csv(env)
        test_product.pac_open_latest_pdt()
        test_product.verify_product_csv_uploaded()
        pdt_to_check = test_product.get_pdt_id()
        ui_data = test_product.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='Product')
        result = test_product.compare_pdt_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284385)
def test_pac_csv_update_product(env, driver):
    function_name = "test_pac_csv_update_product"
    print(f"\nRunning script: {function_name}")
    product_update_csv(env)

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_product = product(driver, env)
        pacUser.import_csv()
        test_product.upload_pdt_pac_csv(env)
        test_product.pac_open_latest_pdt()
        test_product.verify_product_csv_uploaded_after_update()
        pdt_to_check = test_product.get_pdt_id()
        ui_data = test_product.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='Product')
        result = test_product.compare_pdt_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284388)
def test_pac_ui_add_product(env, driver):
    function_name = "test_pac_ui_add_product"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_product = product(driver, env)
        test_product.add_pdt_ui()
        pdt_to_check = test_product.get_pdt_id()
        test_product.add_pdt_basedata_details()
        test_product.add_pdt_asset_details()
        test_product.add_pdt_pricetaxtab_details()
        test_product.add_weightandshippingtab_details()
        test_product.open_inventory_tab()
        test_product.add_categoryspecifictab_details()
        test_product.open_storespecifictab()
        test_product.add_spatab_details()
        pacUser.save_and_publish()
        ui_data = test_product.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='Product')
        result = test_product.compare_pdt_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284389)
def test_pac_ui_update_product(env, driver):
    function_name = "test_pac_ui_update_product"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_product = product(driver, env)
        test_product.pac_open_latest_pdt()
        pdt_to_check = test_product.get_pdt_id()
        test_product.update_latest_pdt()
        pacUser.save_and_publish()
        ui_data = test_product.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='Product')
        result = test_product.compare_pdt_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284390)
def test_pac_ui_unpublish_product(env, driver):
    function_name = "test_pac_ui_unpublish_product"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_product = product(driver, env)
        test_product.pac_open_latest_pdt()
        pdt_to_check = test_product.get_pdt_id()
        pacUser.unpublish()
        ui_data = test_product.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        result = test_product.compare_pdt_unpublished_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284386)
def test_store_csv_add_product(env, driver):
    function_name = "test_store_csv_add_product"
    print(f"\nRunning script: {function_name}")
    product_add_csv(env)

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_product = product(driver, env)
        storeUser.import_csv()
        test_product.upload_pdt_store_csv(env)
        test_product.store_open_latest_pdt()
        test_product.verify_product_csv_uploaded()
        pdt_to_check = test_product.get_pdt_id()
        ui_data = test_product.get_data()
        sns = storeUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        storeUser.validate_schema(sns_data=sns, object='Product')
        result = test_product.compare_pdt_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")

@jamatest.test(16284391)
def test_store_ui_add_product(env, driver):
    function_name = "test_store_ui_add_product"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_product = product(driver, env)
        test_product.add_store_pdt_ui()
        pdt_to_check = test_product.get_pdt_id()
        test_product.add_pdt_basedata_details()
        test_product.add_store_pdt_asset_details(env)
        test_product.add_store_pdt_pricetaxtab_details()
        test_product.add_store_weightandshippingtab_details()
        test_product.open_inventory_tab()
        test_product.add_categoryspecifictab_details()
        test_product.open_storespecifictab()
        test_product.add_spatab_details()
        storeUser.save_and_publish()
        ui_data = test_product.get_data()
        sns = storeUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        storeUser.validate_schema(sns_data=sns, object='Product')
        result = test_product.compare_pdt_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284392)
def test_store_ui_update_product(env, driver):
    function_name = "test_store_ui_update_product"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_product = product(driver, env)
        test_product.store_open_latest_pdt()
        pdt_to_check = test_product.get_pdt_id()
        test_product.update_latest_pdt()
        storeUser.save_and_publish()
        ui_data = test_product.get_data()
        sns = storeUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        storeUser.validate_schema(sns_data=sns, object='Product')
        result = test_product.compare_pdt_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284393)
def test_store_ui_unpublish_product(env, driver):
    function_name = "test_store_ui_unpublish_product"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_product = product(driver, env)
        test_product.store_open_latest_pdt()
        pdt_to_check = test_product.get_pdt_id()
        storeUser.unpublish()
        ui_data = test_product.get_data()
        sns = storeUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        result = test_product.compare_pdt_unpublished_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284387)
def test_store_csv_update_product(env,driver):
    function_name = "test_store_csv_update_product"
    print(f"\nRunning script: {function_name}")
    product_update_csv(env)

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_product = product(driver, env)
        storeUser.import_csv()
        test_product.upload_pdt_store_csv(env)
        test_product.store_open_latest_pdt()
        test_product.verify_product_csv_uploaded_after_update()
        pdt_to_check = test_product.get_pdt_id()
        ui_data = test_product.get_data()
        sns = storeUser.get_sns_message_by_object_id(id=pdt_to_check[2:], queue_url=get_queue_url(env))
        storeUser.validate_schema(sns_data=sns, object='Product')
        result = test_product.compare_pdt_data(dict1=ui_data, dict2=sns)
        assert result, f"Product data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Product ID - {pdt_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()