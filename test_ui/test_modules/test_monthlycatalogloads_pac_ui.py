from jamatest import jamatest
from users.pacAdmin import pacAdmin
from project_configs.env_ui_constants import constant
from modules.monthlyCatalogLoads import monthlyCatalogLoads
from project_configs.login_helpers import get_screenshot, logger

@jamatest.test(19663067)
def test_pac_ui_add_monthlycatalogload(env, driver):
    function_name = "test_pac_ui_add_monthlycatalogload"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_monthlycatalogload = monthlyCatalogLoads(driver, env)
        test_monthlycatalogload.add_monthlycatalogload()
        mcl_id = test_monthlycatalogload.get_id()
        test_monthlycatalogload.open_airline()
        test_monthlycatalogload.get_data()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Monthly Catalog Load ID - {mcl_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()