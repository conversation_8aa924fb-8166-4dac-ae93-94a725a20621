from users.pacAdmin import pacAdmin
from modules.aircraft import aircraft
from jamatest import jamatest
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, aircraft_add_csv, aircraft_update_csv, logger

# @jamatest.test(19661397)
# def test_pac_csv_add_aircraft(env, driver):
#     function_name = "test_pac_csv_add_aircraft"
#     print(f"\nRunning script: {function_name}")
#     aircraft_add_csv()
#
#     try:
#         pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
#         test_aircraft = aircraft(driver, env)
#         pacUser.import_csv()
#         test_aircraft.upload_aircraft_csv()
#         test_aircraft.open_latest_aircraft()
#         pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath)
#         aircraft_id = test_aircraft.aircraft_id_check()
#         test_aircraft.csv_value_check()
#         test_aircraft.read_csv()
#         test_aircraft.verify_values()
#         logger.info(f"Testcase - {function_name} passed , Aircraft ID - {aircraft_id}")
#     finally:
#         get_screenshot(function_name, driver)
#         print("Executed finally block - script end")
#         driver.quit()
#
# @jamatest.test(19661398)
# def test_pac_csv_update_aircraft(env, driver):
#     function_name = "test_pac_csv_update_aircraft"
#     print(f"\nRunning script: {function_name}")
#     aircraft_update_csv()
#
#     try:
#         pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
#         test_aircraft = aircraft(driver, env)
#         pacUser.import_csv()
#         test_aircraft.upload_aircraft_csv()
#         test_aircraft.open_latest_aircraft()
#         pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath)
#         aircraft_id = test_aircraft.aircraft_id_check()
#         test_aircraft.csv_value_check_after_update()
#         test_aircraft.read_updated_csv()
#         test_aircraft.verify_updated_values()
#         logger.info(f"Testcase - {function_name} passed , Aircraft ID - {aircraft_id}")
#     finally:
#         get_screenshot(function_name, driver)
#         print("Executed finally block - script end")
#         driver.quit()

@jamatest.test(19661395)
def test_pac_ui_add_aircraft(env, driver):
    function_name = "test_pac_ui_add_aircraft"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_aircraft = aircraft(driver, env)
        pacUser.home_add_object()
        test_aircraft.add_aircraft_ui()
        aircraft_id = test_aircraft.aircraft_id_check(add=True)
        test_aircraft.aircraft_basedatatab()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed , Aircraft ID - {aircraft_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661396)
def test_pac_ui_update_aircraft(env, driver):
    function_name = "test_pac_ui_update_aircraft"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_aircraft = aircraft(driver, env)
        test_aircraft.open_latest_aircraft()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath)
        aircraft_id = test_aircraft.aircraft_id_check()
        test_aircraft.update_aircraft_ui()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Aircraft ID - {aircraft_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661399)
def test_pac_ui_unpublish_aircraft(env, driver):
    function_name = "test_pac_ui_unpublish_aircraft"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_aircraft = aircraft(driver, env)
        test_aircraft.open_latest_aircraft()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath)
        aircraft_id = test_aircraft.aircraft_id_check()
        test_aircraft.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, Aircraft ID - {aircraft_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()