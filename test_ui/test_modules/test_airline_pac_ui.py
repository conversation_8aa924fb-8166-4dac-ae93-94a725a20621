from modules.airline import airline
from users.pacAdmin import pacAdmin
from jamatest import jamatest
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import wait, get_screenshot, logger

@jamatest.test(16284348)
def test_pac_ui_add_airline(env, driver):
    function_name = "test_pac_ui_add_airline"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airline = airline(driver, env)
        pacUser.home_add_object()
        test_airline.add_airline()
        airline_to_check = test_airline.airline_id_check()
        test_airline.airlne_basedatatab()
        test_airline.airline_contactinfotab()
        test_airline.airline_aircrafttab()
        test_airline.airline_storetab()
        test_airline.airline_configtab()
        pacUser.save_and_publish()
        wait(15)
        pacUser.navigate_to_sns(add=True)
        sns_found = pacUser.verify_sns(id=airline_to_check, save=True,
                                        sns_published=constant.airline_sns_latest_published_xpath,
                                        sns_id_chk=constant.sns_id_chk)
        pacUser.verify_sns_and_download(crct_sns=sns_found, id=airline_to_check,
                                         sns_published=constant.airline_sns_latest_published_xpath, save=True)
        logger.info(f"Testcase - {function_name} passed successfully, Airline ID - {airline_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284349)
def test_pac_ui_update_airline(env, driver):
    function_name = "test_pac_ui_update_airline"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airline = airline(driver, env)
        test_airline.open_latest_airline()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update_airline)
        airline_to_check = test_airline.airline_id_check()
        test_airline.update_airline()
        pacUser.save_and_publish()
        wait(15)
        pacUser.navigate_to_sns(update_unpublish=True)
        sns_found = pacUser.verify_sns(id=airline_to_check, save=True,
                                       sns_published=constant.airline_sns_latest_published_xpath,
                                       sns_id_chk=constant.sns_id_chk)
        pacUser.verify_sns_and_download(crct_sns=sns_found, id=airline_to_check,
                                        sns_published=constant.airline_sns_latest_published_xpath, save=True)
        logger.info(f"Testcase - {function_name} passed successfully, Airline ID - {airline_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284350)
def test_pac_ui_unpublish_airline(env, driver):
    function_name = "test_pac_ui_unpublish_airline"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airline = airline(driver, env)
        test_airline.open_latest_airline()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update_airline)
        airline_to_check = test_airline.airline_id_check()
        pacUser.unpublish()
        wait(15)
        pacUser.navigate_to_sns(update_unpublish=True)
        sns_found = pacUser.verify_sns(id=airline_to_check, unpublish=True,
                                       sns_unpublished=constant.airline_sns_latest_unpublished_xpath,
                                       unpublish_sns_id_chk=constant.sns_id_chk)
        pacUser.verify_sns_and_download(crct_sns=sns_found, id=airline_to_check,
                                        sns_unpublished=constant.airline_sns_latest_unpublished_xpath, unpublish=True)
        logger.info(f"Testcase - {function_name} passed successfully, Airline ID - {airline_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()