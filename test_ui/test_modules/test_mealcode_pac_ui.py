from jamatest import jamatest
from users.pacAdmin import pacAdmin
from modules.mealcode import mealcode
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, logger

@jamatest.test(19663046)
def test_pac_ui_add_mealcode(env, driver):
    function_name = "test_pac_ui_add_mealcode"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_mealcode = mealcode(driver, env)
        pacUser.home_add_object()
        test_mealcode.add_mealcode()
        mealcode_id = test_mealcode.get_id()
        test_mealcode.mealcode_basedata_details()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Mealcode ID - {mealcode_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19663049)
def test_pac_mealcode_csv_import(env, driver):
    function_name = "test_pac_mealcode_csv_import"
    print(f"\nRunning script: {function_name}")

    try:
        pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_mealcode = mealcode(driver, env)
        test_mealcode.open_mealcode_module()
        test_mealcode.add_airline_details()
        test_mealcode.click_exportcsv_btn()
        test_mealcode.import_mealcode_csv()
        logger.info(f"Testcase - {function_name} passed successfully.")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()
