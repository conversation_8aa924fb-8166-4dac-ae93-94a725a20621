from jamatest import jamatest
from users.pacAdmin import pacAdmin
from users.storeAdmin import storeAdmin
from selenium.webdriver.common.by import By
from modules.storelocation import storelocation
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.support.wait import WebDriverWait
from project_configs.login_helpers import get_screenshot, logger
from selenium.webdriver.support import expected_conditions as EC

@jamatest.test(19662746)
def test_pac_ui_add_storelocation(env, driver):
    function_name = "test_pac_ui_add_storelocation"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_storelocation = storelocation(driver, env)
        pacUser.home_add_object()
        test_storelocation.add_storelocation()
        storeloc_id = test_storelocation.get_storeloc_id()
        test_storelocation.add_basedata_details(pac=True)
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, StoreLocation ID - {storeloc_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662747)
def test_pac_ui_update_storelocation(env, driver):
    function_name = "test_pac_ui_update_storelocation"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_storelocation = storelocation(driver, env)
        test_storelocation.pac_open_latest_storeloc()
        pacUser.get_latest_from_folder(obj_xpath=constant.inventorylocation_latest_xpath)
        storeloc_id = test_storelocation.get_storeloc_id(update_unpublish=True)
        test_storelocation.update_storeloc()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, StoreLocation ID - {storeloc_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662848)
def test_pac_ui_unpublish_storelocation(env, driver):
    function_name = "test_pac_ui_unpublish_storelocation"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_storelocation = storelocation(driver, env)
        test_storelocation.pac_open_latest_storeloc()
        pacUser.get_latest_from_folder(obj_xpath=constant.inventorylocation_latest_xpath)
        storeloc_id = test_storelocation.get_storeloc_id(update_unpublish=True)
        WebDriverWait(driver, 90).until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.unpublish_btn_text))).click()
        try:
            element = driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        logger.info(f"Testcase - {function_name} passed successfully, StoreLocation ID - {storeloc_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662918)
def test_store_ui_add_storelocation(env, driver):
    function_name = "test_store_ui_add_storelocation"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_storelocation = storelocation(driver, env)
        test_storelocation.store_add_storeloc()
        storeloc_id = test_storelocation.get_storeloc_id()
        test_storelocation.add_basedata_details()
        storeUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, StoreLocation ID - {storeloc_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662919)
def test_store_ui_update_storelocation(env, driver):
    function_name = "test_store_ui_update_storelocation"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_storelocation = storelocation(driver, env)
        test_storelocation.navigate_to_storelocation_folder()
        storeUser.get_latest_from_folder(obj_xpath=constant.inventorylocation_latest_xpath)
        storeloc_id = test_storelocation.get_storeloc_id(update_unpublish=True)
        test_storelocation.update_storeloc()
        storeUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, StoreLocation ID - {storeloc_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662920)
def test_store_unpublish_storelocation(env, driver):
    function_name = "test_store_unpublish_storelocation"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_storelocation = storelocation(driver, env)
        test_storelocation.navigate_to_storelocation_folder()
        storeUser.get_latest_from_folder(obj_xpath=constant.inventorylocation_latest_xpath)
        storeloc_id = test_storelocation.get_storeloc_id(update_unpublish=True)
        WebDriverWait(driver, 90).until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.unpublish_btn_text))).click()
        try:
            element = driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        logger.info(f"Testcase - {function_name} passed successfully, StoreLocation ID - {storeloc_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()