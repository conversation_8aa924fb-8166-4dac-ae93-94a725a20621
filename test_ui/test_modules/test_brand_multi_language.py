from conftest import storeAdminUser
from project_configs.login_helpers import brand_add_csv, brand_update_csv, product_csv_update_brand_id
from users.pacAdmin import pacAdmin
from users.storeAdmin import storeAdmin
from driver_setup.helper import *
from driver_setup.config import *
from jamatest import jamatest
import pytest
from project_configs.csv_config import Config

###### csv testcases
# store user cases
@pytest.mark.reg
def test_store_brand_csv_sameple_fields_validation_multi_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_brand_csv_sameple_fields_validation_multi_language"
    print(f"\nRunning script: {function_name}")
    storeAdminUser.store_select_languages(LANGUAGES_CODES_CONFIGURED, LANGUAGE_MAP)
    storeAdminUser.closeObjectOpened()
    storeAdminUser.brand_csv_samplefile_download()
    headers = storeAdminUser.fetch_csv_headers(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.verify_headers_for_csv(valid_headers=headers)
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_store_csv_brand_create_with_multi_language_using_sample(storeAdminUser:storeAdmin, env):
    function_name = "test_store_csv_brand_create_with_multi_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_multilang_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_add_multilang_sample_csv_path")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.close_all_tabs()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1], brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id}")

@pytest.mark.reg
def test_store_csv_brand_update_with_multi_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_csv_brand_update_with_multi_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_multilang_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_add_multilang_sample_csv_path")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1], brand_name=name_in_csv)
    brand.close_all_tabs()
    name_in_csv = brand_update_csv(id=str(brand_id).split()[-1], csv_path="brand_update_multilang_sample_csv_path")
    storeAdminUser.store_brand_csv_upload(csv_path="brand_update_multilang_sample_csv_path")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id_updated = brand.getObjectID()
    brand.close_all_tabs()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id_updated).split()[-1], brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id_updated}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id_updated}")

def test_store_with_multi_language_brand_verify_csv_with_images(storeAdminUser:storeAdmin, env):
    function_name = "test_store_with_multi_language_brand_verify_csv_with_images"
    print(f"\nRunning script: {function_name}")
    storeAdminUser.brand_csv_samplefile_download()
    headers = storeAdminUser.fetch_csv_headers(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.verify_headers_for_csv(valid_headers=headers)
    storeAdminUser.read_csv_img_urls()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

def test_store_with_multi_language_brand_verify_ui_with_images(storeAdminUser:storeAdmin, env):
    function_name = "test_store_with_multi_language_brand_verify_ui_with_images"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    storeAdminUser.verify_images_in_ui()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

# csv export cases
@pytest.mark.reg
def test_store_brand_csv_export_with_multi_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_brand_csv_export_with_multi_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.select_brand(single_export=True)
    brand.click_export_csv_button(export=True)
    headers = storeAdminUser.fetch_csv_headers(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.verify_headers_for_csv(valid_headers=headers)
    object_Data = storeAdminUser.read_multilang_brand_export_csv()
    brand.open_selected_brand(single_export=True)
    brand_id = brand.getObjectID()
    id = int(str(brand_id).split()[-1])
    for brand in object_Data:
        if id == brand['brand_id']:
            print("Brand present:", brand)
            storeAdminUser.verify_csv_ui_data(brand)
        else:
            assert False, f"Brand with ID {id} not present"
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_store_multiple_brand_csv_export_with_multi_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_multiple_brand_csv_export_with_multi_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.navigate_to_brand_folder()
    brand.select_brand(multiple_export=True)
    brand.click_export_csv_button(export=True)
    headers = storeAdminUser.fetch_csv_headers(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.verify_headers_for_csv(valid_headers=headers)
    object_Data = storeAdminUser.read_multilang_brand_export_csv()
    brand.click_reload_button()
    brand.open_multiple_selected_brand(object_Data)
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_store_unpublished_brand_csv_export_with_multi_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_unpublished_brand_csv_export_with_multi_language"
    print(f"\nRunning script: {function_name}")
    brandName = "Pac_" + generate_short_uuid()
    brand = storeAdminUser.createBrand(brandName)
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    sleep(5)
    brand.unpublish()
    storeAdminUser.closeObjectOpened()
    storeAdminUser.navigate_to_brand_folder()
    brand.open_unpublished_brand_within_folder(brandName)
    brand.select_brand(single_export=True)
    brand.click_export_csv_button(export=True)
    brand.find_error_msg_for_unpublished()
    brand.click_ok_button()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

# admin user cases
@pytest.mark.reg
def test_pac_brand_csv_fields_with_multi_language(pacAdminUser:pacAdmin, env):
    function_name = "test_pac_brand_csv_fields_with_multi_language"
    print(f"\nRunning script: {function_name}")
    pacAdminUser.navigate_to_store_folder_and_select_languages(LANGUAGES_CODES_CONFIGURED, LANGUAGE_MAP, dev=True, store=STORE_ID)
    pacAdminUser.closeObjectOpened()
    pacAdminUser.closeObjectOpened()
    pacAdminUser.brand_csv_samplefile_download(pac=True, store=STORE_NAME)
    headers = pacAdminUser.fetch_csv_headers(LANGUAGES_CODES_CONFIGURED)
    pacAdminUser.verify_headers_for_csv(valid_headers=headers)
    pacAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_pac_csv_brand_create_with_multi_language(pacAdminUser:pacAdmin, env):
    """ Creates a brand using a mutilanguage CSV """
    function_name = "test_pac_csv_brand_create_with_multi_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_multilang_sample_csv_path")
    pacAdminUser.pac_brand_csv_upload(store=STORE_NAME,csv_path="brand_add_multilang_sample_csv_path")
    pacAdminUser.verify_env(store=STORE_NAME)
    brand = pacAdminUser.navigate_to_brand_folder_in_pac(store=STORE_NAME)
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.close_all_tabs()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1], brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id}")

@pytest.mark.reg
def test_pac_csv_brand_update_with_multi_language(pacAdminUser:pacAdmin, env):
    function_name = "test_pac_csv_brand_update_with_multi_language"
    print(f"\nRunning script: {function_name}")
    name_in_csv = brand_add_csv(csv_path="brand_add_multilang_sample_csv_path")
    pacAdminUser.pac_brand_csv_upload(csv_path="brand_add_multilang_sample_csv_path", store=STORE_NAME)
    pacAdminUser.verify_env(store=STORE_NAME)
    brand = pacAdminUser.navigate_to_brand_folder_in_pac(store=STORE_NAME)
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id = brand.getObjectID()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id).split()[-1], brand_name=name_in_csv)
    brand.close_all_tabs()
    name_in_csv = brand_update_csv(id=str(brand_id).split()[-1], csv_path="brand_update_multilang_sample_csv_path")
    pacAdminUser.pac_brand_csv_upload(csv_path="brand_update_multilang_sample_csv_path", store=STORE_NAME)
    brand.closeObjectOpened()
    brand = pacAdminUser.navigate_to_brand_folder_in_pac(store=STORE_NAME, update=True)
    brand.search_brand_within_folder(brand_name=name_in_csv)
    brand_id_updated = brand.getObjectID()
    brand.closeObjectOpened()
    brand.closeObjectOpened()
    brand.validateSNS(csv=True, brand_id_csv=str(brand_id_updated).split()[-1], brand_name=name_in_csv)
    print(f"Testcase - {function_name} passed , Brand ID - {brand_id_updated}")
    logger.info(f"Testcase - {function_name} passed , Brand ID - {brand_id_updated}")

##### UI testcases
## perspectives 
@pytest.mark.reg
@pytest.mark.ui
@jamatest.test(20824254) 
def test_ui_store_manager_creation(storeManagerUser:storeAdmin, track_brands):
    brand_name = storeManagerUser.generate_faked_brand_name()
    brand = storeManagerUser.createBrand(brand_name)
    brand.enterName(name=brand_name)
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    brand.enterName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[1]],brand_name + "こんにちは" , LANGUAGES_CODES_CONFIGURED[1])
    brand.enterName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]],brand_name+" " + LANGUAGES_CODES_CONFIGURED[2], LANGUAGES_CODES_CONFIGURED[2])
    brand.enterName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[3]],brand_name+" " +  LANGUAGES_CODES_CONFIGURED[3], LANGUAGES_CODES_CONFIGURED[3])
    brand.uploadImage("100x100.png", "1:1" )
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeManagerUser.closeObjectOpened()
    
@jamatest.test(20941926)
@pytest.mark.reg
@pytest.mark.ui
def test_ui_store_manager_unpublish(storeManagerUser:storeAdmin):
    brand = storeManagerUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    brand.open_base_data()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    brand.unpublish()
    brand.validateUnpublishSNS()
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeManagerUser.closeObjectOpened()
    # brand.delete_object() ## req change
    # brand.validateUnpublishSNS()

@jamatest.test(20941920)
@pytest.mark.reg
@pytest.mark.ui
def test_ui_store_admin_unpublish(storeAdminUser:storeAdmin):
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    brand.unpublish()
    brand.validateUnpublishSNS()
    storeAdminUser.closeObjectOpened()

@jamatest.test(20941920)
@pytest.mark.reg
@pytest.mark.ui
def test_store_admin_unpublish(storeAdminUser:storeAdmin):
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    brand.unpublish()
    brand.validateUnpublishSNS()
    storeAdminUser.closeObjectOpened()
    # brand.delete_object() ## req change

@pytest.mark.reg
@pytest.mark.ui
def test_admin_creation(pacAdminUser:pacAdmin, track_brands):
    brand = pacAdminUser.createBrand("ui_auto_admin" + generate_short_uuid())
    brand.selectStore(STORE_ID)
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)      
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    pacAdminUser.closeObjectOpened()

@jamatest.test(21002272)
@pytest.mark.reg
@pytest.mark.ui
def test_admin_unpublish_delete(pacAdminUser:pacAdmin, track_brands):
    brand = pacAdminUser.createBrand("ui_auto_delete" + generate_short_uuid())
    brand.delete_object()
    sleep(1)
    brand = pacAdminUser.createBrand("ui_auto_test_admin_create_brand" + generate_short_uuid())
    brand.selectStore(STORE_ID)
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    brand.unpublish()
    brand.validateUnpublishSNS()
    brand.save_and_publish(data_tracker=track_brands)  
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.delete_object()
    brand.validateUnpublishSNS()

@jamatest.test(20906944)
@pytest.mark.reg
@pytest.mark.ui
def test_store_admin_brand_default_language_only(storeAdminUser:storeAdmin, track_brands):
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    storeAdminUser.closeObjectOpened()

@jamatest.test(20907213)
@pytest.mark.reg
@pytest.mark.ui
def test_store_admin_brand_some_languages(storeAdminUser:storeAdmin, track_brands):
    brand = storeAdminUser.createBrand("ui_auto SAd someLang" + generate_short_uuid())
    brand.enterName(name="ui_auto" + generate_short_uuid())
    brand.enterName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[1]],"ui_auto こんにちは" + generate_short_uuid(), LANGUAGES_CODES_CONFIGURED[1])
    brand.enterName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[3]],"ui_auto" + generate_short_uuid(), LANGUAGES_CODES_CONFIGURED[3])
    brand.enterName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[-1]],"ui_auto" + generate_short_uuid(), LANGUAGES_CODES_CONFIGURED[-1])
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    brand.enterName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]],"ui_auto" + generate_short_uuid(), LANGUAGES_CODES_CONFIGURED[2])
    brand.uploadImage("178x100.png", "16:9")
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.closeObjectOpened()

@jamatest.test(20907213)
@pytest.mark.reg
@pytest.mark.ui
def test_store_admin_all_languages_and_images(storeAdminUser:storeAdmin, track_brands):
    brand = storeAdminUser.createBrand("ui_auto all lang" + generate_short_uuid())
    brand.enterNameAllLanguages(LANGUAGES_CODES_CONFIGURED)
    brand.uploadImage("100x100.png", "1:1")
    brand.uploadImage("178x100.png", "16:9")
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.unpublish()
    brand.validateUnpublishSNS()
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.closeObjectOpened()

@jamatest.test(20907366)
@pytest.mark.reg
@pytest.mark.ui
def test_validation_store_admin_duplicate_name(storeAdminUser:storeAdmin, track_brands):
    duplicateName = "ui_auto_" + generate_short_uuid()
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name=duplicateName)
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    storeAdminUser.closeObjectOpened()
    ## Creating duplicate object
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name=duplicateName)
    brand.save_and_publish(validate_successful=False)
    brand.validationDuplicateNames()
    ## ensure that fixing the name will address the validation
    brand.enterName(name=duplicateName+"extra")
    brand.uploadImage("100x100.png", "1:1" )
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.closeObjectOpened()

@jamatest.test(20907366)
@pytest.mark.reg
@pytest.mark.ui
def test_validation_store_admin_char_limit(storeAdminUser:storeAdmin, track_brands):
    characters_51 = ''.join(random.choice(string.ascii_letters) for _ in range(51))
    characters_49 = ''.join(random.choice(string.ascii_letters) for _ in range(49))
    brand = storeAdminUser.createBrand("ui_auto " + generate_short_uuid())
    brand.enterName(name=characters_51)
    brand.save_and_publish(validate_successful=False)
    brand.validateCharaterNameLimit()
    brand.enterName(name=characters_49)
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang  = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[1]])
    assert unpopulated_lang == brand.data["name"]['en']
    #Updating brand validation
    brand.enterName(name=characters_51)
    brand.save_and_publish(validate_successful=False)
    brand.validateCharaterNameLimit()
    brand.enterName(name=characters_49)
    brand.uploadImage("178x100.png", "16:9")
    brand.save_and_publish(validate_successful=False)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.closeObjectOpened()

################ product with brand cases
@jamatest.test(123)
@pytest.mark.reg
@pytest.mark.ui
def test_store_product_created_with_brand_with_multi_language(storeAdminUser:storeAdmin, track_brands):
    function_name = "test_store_product_created_with_brand_with_multi_language"
    print(f"\nRunning script: {function_name}")
    brand_name = storeAdminUser.generate_faked_brand_name()
    brand = storeAdminUser.createBrand(brand_name)
    brand.enterNameAllLanguages(LANGUAGES_CODES_CONFIGURED, baseName=brand_name)
    brand.uploadImage("100x100.png", "1:1" )
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brandId=brand.id
    storeAdminUser.closeObjectOpened()
    product = storeAdminUser.create_product("ui_auto" + uuid_random_name(),"CAT1" )
    product.enterLocalizedData(name="name", shortDescription="short desc", description="description...  ..")
    product.enterLocalizedData(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[1]],LANGUAGES_CODES_CONFIGURED[1], "name こんにちは", "short description こんにちは", "nadescription ... @ こんにちは")
    product.add_pdt_basedata_details(brandId=brandId)
    # input("good?")
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@jamatest.test(123)
@pytest.mark.reg
@pytest.mark.ui
def test_storemanager_product_created_with_brand_with_multi_language(storeManagerUser:storeAdmin, track_brands):
    brand_name = storeManagerUser.generate_faked_brand_name()
    brand = storeManagerUser.createBrand(brand_name)
    brand.enterName(name=brand_name)
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brandId=brand.id
    storeManagerUser.closeObjectOpened()
    product = storeManagerUser.create_product("ui_auto" + uuid_random_name(),"CAT1" )
    product.enterLocalizedData(name="Product name en", shortDescription="short desciption en", description="description...  ..")
    product.enterLocalizedData(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[1]],LANGUAGES_CODES_CONFIGURED[1], "Product name こんにちは", "short description こんにちは", "nadescription ... @ こんにちは")
    product.add_pdt_basedata_details(brandId=brandId)
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)

@jamatest.test(123)
@pytest.mark.reg
@pytest.mark.ui
def test_store_product_created_with_brand_shows_validation_when_unpublishing(storeAdminUser:storeAdmin, track_brands):
    function_name = "test_store_product_created_with_brand_shows_validation_when_unpublishing"
    print(f"\nRunning script: {function_name}")
    brand_name = storeAdminUser.generate_faked_brand_name()
    brand = storeAdminUser.createBrand(brand_name)
    brand.enterName(name="ui_auto" + generate_short_uuid())
    brand.save_and_publish()
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brandId=brand.id
    storeAdminUser.closeObjectOpened()
    product = storeAdminUser.create_product("ui_auto" + uuid_random_name(),"CAT1" )
    product.enterLocalizedData(name="name", shortDescription="short desc", description="description...  ..")
    product.add_pdt_basedata_details(brandId=brandId)
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.closeObjectOpened()
    storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=str(brandId))
    brand.unpublish(validate_successful=False)
    brand.validateUnableUnpublishUsedBrand()
    brand.reload_object()
    brand.enterName(name="ui_auto" + generate_short_uuid())
    brand.save_and_publish()
    brand.unpublish(validate_successful=False)
    brand.validateUnableUnpublishUsedBrand()
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
@pytest.mark.ui
def test_validate_product_does_not_see_unpublished_brands(storeAdminUser:storeAdmin, track_brands):
    function_name = "test_validate_product_does_not_see_unpublished_brands"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.unpublish()
    brand.validateUnpublishSNS()
    brand.reload_object()
    brandId=brand.id
    storeAdminUser.closeObjectOpened()
    product = storeAdminUser.create_product("ui_auto" + uuid_random_name(),"CAT1" )
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf")
    product.isBrandNotAvailable(brandId)
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.navigate_to_brand_folder()
    brand.search_brand_within_folder(brand_name=str(brandId), second_tab=True)
    brand.save_and_publish()
    storeAdminUser.closeObjectOpened() # close brand
    storeAdminUser.closeObjectOpened() # close folder
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    product.reload_object()
    product.add_pdt_basedata_details( brandId=brandId)
    product.save_and_publish()
    product.validateSNS()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
@pytest.mark.ui
def test_pac_product_created_with_brand_with_multi_language(pacAdminUser:pacAdmin, track_brands):
    function_name = "test_pac_product_created_with_brand_with_multi_language"
    print(f"\nRunning script: {function_name}")
    brand = pacAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.selectStore(STORE_ID)
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand.reload_object()
    unpopulated_lang = brand.fetchName(LANGUAGE_MAP[LANGUAGES_CODES_CONFIGURED[2]])
    assert unpopulated_lang == brand.data["name"]['en']
    brand_id=brand.getObjectID()
    pacAdminUser.closeObjectOpened()
    product = pacAdminUser.create_product_pac("ui_auto" + uuid_random_name(),"CAT1", store=STORE_NAME)
    brandId = str(brand_id).split()[-1]
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf", brandId)
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    pacAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
@pytest.mark.ui
def test_pac_product_created_with_unpublished_brand_having_multi_language(pacAdminUser:pacAdmin, track_brands):
    function_name = "test_pac_product_created_with_unpublished_brand_having_multi_language"
    print(f"\nRunning script: {function_name}")
    brand = pacAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.selectStore(STORE_ID)
    brand.save_and_publish(data_tracker=track_brands)
    brand.unpublish()
    brand.reload_object()
    brandId = brand.id
    pacAdminUser.closeObjectOpened()
    product = pacAdminUser.create_product_pac("ui_auto" + uuid_random_name(),"CAT1", store=STORE_NAME)
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf")
    product.isBrandNotAvailable(brandId)
    product.save_and_publish()
    product.validateSNS(LANGUAGES_CODES_CONFIGURED)
    pacAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

# product csv import with brand
def test_store_product_csv_with_multi_language_brand_headers_verify(storeAdminUser:storeAdmin, env):
    function_name = "test_store_product_csv_with_multi_language_brand_headers_verify"
    print(f"\nRunning script: {function_name}")
    storeAdminUser.product_csv_samplefile_download(multi_lang=True)
    headers = storeAdminUser.fetch_product_csv_headers(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.verify_headers_for_csv(valid_headers=headers)
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

def test_store_csv_import_product_with_and_without_brand_with_multi_language(store_http_session, track_product_input_data_map, storeAdminUser:storeAdmin, env,
                                                         track_brands):
    function_name = "test_store_csv_import_product_with_and_without_brand_with_multi_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    sleep(2)
    brandId = brand.getObjectID()
    storeAdminUser.closeObjectOpened()
    product_csv_update_brand_id(csv_path="product_csv_path_after_name_update_with_multi_language", brand_id=brandId)
    storeAdminUser.store_product_csv_upload(csv_path="product_csv_path_after_name_update_with_multi_language", categoryPath=CATEGORY_PATH)
    csv_reference = Config.get_path("product_with_brand_and_without_brand_with_multi_language")
    test_filename = generate_test_csv(in_file=csv_reference)
    test_json_products_list = product_csv_to_json(test_filename)
    sleep(3)
    storeAdminUser.closeObjectOpened()
    num_products = len(test_json_products_list)
    temp_list = test_json_products_list
    logger.debug("Number of products created to verify: %s", num_products)
    sleep(25)
    temp_list = test_json_products_list
    all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
    for product in all_get_product_list:
        for p_in in test_json_products_list:
            if product['sku'] == p_in['sku']:
                track_product_input_data_map[product['id']] = p_in
                print(f"the id {product['id']}")
                logger.info("Found a matching product %s", product['sku'])
                sns = get_sns_message_by_id_catalog(product["id"],
                                                    retries=25)  # incase the message was read in previous
                validate_product(sns)
                assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in))
                num_products = num_products - 1
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
            elif "fails" in product['sku']:  # to make sure failed products are not getting created
                product_id = product["id"]
                raise Exception(f"There was a product with fails {product_id}")
    if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")

def test_store_csv_import_two_products_with_same_brand_with_multi_language(store_http_session, track_product_input_data_map, storeAdminUser:storeAdmin, env,
                                                         track_brands):
    function_name = "test_store_csv_import_two_products_with_same_brand_with_multi_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    sleep(2)
    brandId = brand.getObjectID()
    storeAdminUser.closeObjectOpened()
    product_csv_update_brand_id(csv_path="product_csv_path_after_brand_update_with_multi_language", brand_id=brandId, same_brand=True)
    storeAdminUser.store_product_csv_upload(csv_path="product_csv_path_after_brand_update_with_multi_language", categoryPath=CATEGORY_PATH)
    csv_reference = Config.get_path("csv_with_two_products_with_same_brand_with_multi_language")
    test_filename = generate_test_csv(in_file=csv_reference)
    test_json_products_list = product_csv_to_json(test_filename)
    sleep(3)
    storeAdminUser.closeObjectOpened()
    num_products = len(test_json_products_list)
    temp_list = test_json_products_list
    logger.debug("Number of products created to verify: %s", num_products)
    sleep(25)
    temp_list = test_json_products_list
    all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
    for product in all_get_product_list:
        for p_in in test_json_products_list:
            if product['sku'] == p_in['sku']:
                track_product_input_data_map[product['id']] = p_in
                print(f"the id {product['id']}")
                logger.info("Found a matching product %s", product['sku'])
                sns = get_sns_message_by_id_catalog(product["id"],
                                                    retries=25)  # incase the message was read in previous
                validate_product(sns)
                assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in))
                num_products = num_products - 1
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
            elif "fails" in product['sku']:  # to make sure failed products are not getting created
                product_id = product["id"]
                raise Exception(f"There was a product with fails {product_id}")
    if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")

def test_store_csv_export_and_update_product_with_brand_with_multi_language(store_http_session, track_product_input_data_map, storeAdminUser:storeAdmin, env,
                                                         track_brands):
    function_name = "test_store_csv_export_and_update_product_with_brand_with_multi_language"
    print(f"\nRunning script: {function_name}")
    brand = storeAdminUser.createBrand(objName="ui_auto " + generate_short_uuid())
    brand.enterName(name="ui_auto_" + generate_short_uuid())
    brand.save_and_publish(data_tracker=track_brands)
    brand.validateSNS(LANGUAGES_CODES_CONFIGURED)
    brand_id = brand.getObjectID()
    storeAdminUser.closeObjectOpened()
    product = storeAdminUser.create_product("ui_auto" + uuid_random_name(), "CAT1" )
    brandId = str(brand_id).split()[-1]
    product.add_pdt_basedata_details("name", "short descr", "desctiptionsadf")
    product.save_and_publish()
    pdt_id = product.getObjectID()
    storeAdminUser.closeObjectOpened()
    storeAdminUser.navigate_to_product_folder(multi_lang=True)
    product.search_pdt(id=pdt_id)
    product.click_export_csv_button(export=True)
    sleep(1)
    storeAdminUser.closeObjectOpened()
    headers = storeAdminUser.fetch_product_csv_headers(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.verify_headers_for_csv(valid_headers=headers)
    latest_csv = product.update_brand_and_product_csv_upload(brand_id=brandId)
    storeAdminUser.closeObjectOpened()
    product.search_pdt(id=pdt_id, after_export=True, multi_lang=True)
    product.open_product(multi_lang=True)
    product.isBrandpresentinui(id=brandId)
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print("latest", latest_csv)
    csv_reference = latest_csv
    test_filename = generate_test_csv(in_file=csv_reference)
    test_json_products_list = product_csv_to_json(test_filename)
    num_products = len(test_json_products_list)
    temp_list = test_json_products_list
    logger.debug("Number of products created to verify: %s", num_products)
    sleep(25)
    temp_list = test_json_products_list
    all_get_product_list = fetch_last_n_products(store_http_session, product_url, n=100, items_per_page=100)
    for product in all_get_product_list:
        for p_in in test_json_products_list:
            if product['sku'] == p_in['sku']:
                track_product_input_data_map[product['id']] = p_in
                print(f"the id {product['id']}")
                logger.info("Found a matching product %s", product['sku'])
                sns = get_sns_message_by_id_catalog(product["id"],
                                                    retries=25)  # incase the message was read in previous
                validate_product(sns)
                assert compare_input_to_expected_data(sns, product_sns_data_transform(json_data=p_in, default_lang=True))
                num_products = num_products - 1
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                temp_list = [obj for obj in temp_list if obj.get("sku") != product['sku']]
                assert compare_input_to_expected_data(product, product_get_api_data_transform(json_data=p_in))
            elif "fails" in product['sku']:  # to make sure failed products are not getting created
                product_id = product["id"]
                raise Exception(f"There was a product with fails {product_id}")
    if num_products != 0:
        logger.critical("Elements SKU remaining in the list:")
        for i in temp_list:
            logger.critical(i['sku'])
        raise Exception("Not all products imported succesfully")