from jamatest import jamatest
from users.pacAdmin import pacAdmin
from modules.airport import airport
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, airport_update_csv, logger

@jamatest.test(19661448)
def test_pac_csv_update_airport(env, driver):
    function_name = "test_pac_csv_update_airport"
    print(f"\nRunning script: {function_name}")
    airport_update_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airport = airport(driver, env)
        pacUser.import_csv()
        test_airport.pac_upload_airport_csv()
        test_airport.pac_open_latest_airport()
        # pacUser.get_latest_from_folder(obj_xpath=constant.latest_airport_xpath)
        airport_id = test_airport.get_id()
        test_airport.read_and_verify_name_in_airport_csv()
        logger.info(f"Testcase - {function_name} passed successfully, Airport ID - {airport_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19661449)
def test_pac_ui_unpublish_airport(env, driver):
    function_name = "test_pac_ui_unpublish_airport"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_airport = airport(driver, env)
        test_airport.pac_open_latest_airport()
        airport_id = test_airport.get_id()
        pacUser.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, Airport ID - {airport_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()