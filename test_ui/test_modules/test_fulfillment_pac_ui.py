from jamatest import jamatest
from users.pacAdmin import pacAdmin
from modules.fulfillment import fulfillment
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import get_screenshot, logger

def test_pac_ui_add_fulfillment(env, driver):
    function_name = "test_pac_ui_add_fulfillment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_fulfillment = fulfillment(driver,env)
        pacUser.home_add_object()
        test_fulfillment.add_fulfillment()
        fulfillment_id = test_fulfillment.get_id()
        test_fulfillment.add_basedata()
        pacUser.save_and_publish()
        logger.info(f"Testcase - {function_name} passed successfully, Fulfillment ID - {fulfillment_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(19662697)
def test_pac_ui_unpublish_fulfillment(env, driver):
    function_name = "test_pac_ui_unpublish_fulfillment"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_fulfillment = fulfillment(driver,env)
        test_fulfillment.open_latest_fulfillment()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_fulfillment_xpath)
        fulfillment_id = test_fulfillment.get_id(unpublish=True)
        pacUser.unpublish()
        logger.info(f"Testcase - {function_name} passed successfully, Fulfillment ID - {fulfillment_id}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()
