from time import sleep

import pytest
from driver_setup.config import *
from jamatest import jamatest
from users.pacAdmin import pacAdmin
from users.storeAdmin import storeAdmin
from project_configs.aws_config import Config
from project_configs.env_ui_constants import constant
from modules.specialityAttribute import specialityAttribute
from project_configs.login_helpers import get_screenshot, prepare_spa_csv_for_upload, spa_update_csv, logger

def get_queue_url(env):
    config = Config(env)
    queue_url = config.env_aws_pdt_spa_ca()
    if not queue_url:
        print("Failed to get the queue URL")
        return None
    return queue_url

@jamatest.test(16284394)
def test_pac_csv_add_specialityattribute(env, driver):
    function_name = "test_pac_csv_add_specialityattribute"
    print(f"\nRunning script: {function_name}")
    prepare_spa_csv_for_upload()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_spa = specialityAttribute(driver, env)
        pacUser.import_csv()
        test_spa.upload_spa_csv()
        test_spa.open_latest_spa()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update)
        test_spa.verify_spa_csv_uploaded()
        spa_to_check = test_spa.spa_id_check()
        ui_data = test_spa.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='SpecialityAttribute')
        result = test_spa.compare_spa_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284395)
def test_pac_csv_update_specialityattribute(env, driver):
    function_name = "test_pac_csv_update_specialityattribute"
    print(f"\nRunning script: {function_name}")
    spa_update_csv()

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_spa = specialityAttribute(driver, env)
        pacUser.import_csv()
        test_spa.upload_spa_csv()
        test_spa.open_latest_spa()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update)
        test_spa.verify_spa_csv_uploaded_after_update()
        spa_to_check = test_spa.spa_id_check()
        ui_data = test_spa.get_data()
        sns = pacUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='SpecialityAttribute')
        result = test_spa.compare_spa_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284398)
def test_pac_ui_add_specialityattribute(env, driver):
    function_name = "test_pac_ui_add_specialityattribute"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_spa = specialityAttribute(driver, env)
        pacUser.home_add_object()
        test_spa.add_spa_ui()
        spa_to_check = test_spa.spa_id_check()
        test_spa.spa_basedata()
        test_spa.add_store_for_spa()
        ui_data = test_spa.get_data()
        test_spa.add_asset()
        pacUser.save_and_publish()
        sns = pacUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='SpecialityAttribute')
        result = test_spa.compare_spa_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284399)
def test_pac_ui_update_specialityattribute(env, driver):
    function_name = "test_pac_ui_update_specialityattribute"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_spa = specialityAttribute(driver, env)
        test_spa.open_latest_spa()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update)
        spa_to_check = test_spa.spa_id_check()
        test_spa.update_spa()
        ui_data = test_spa.get_data()
        pacUser.save_and_publish()
        sns = pacUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        pacUser.validate_schema(sns_data=sns, object='SpecialityAttribute')
        result = test_spa.compare_spa_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284400)
def test_pac_ui_unpublish_specialityattribute(env, driver):
    function_name = "test_pac_ui_unpublish_specialityattribute"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_spa = specialityAttribute(driver, env)
        test_spa.open_latest_spa()
        pacUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update)
        spa_to_check = test_spa.spa_id_check()
        ui_data = test_spa.get_data()
        pacUser.unpublish()
        sns = pacUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        result = test_spa.compare_spa_unpublished_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284396)
def test_store_csv_add_specialityattribute(env, driver):
    function_name = "test_store_csv_add_specialityattribute"
    print(f"\nRunning script: {function_name}")
    prepare_spa_csv_for_upload()

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_spa = specialityAttribute(driver, env)
        storeUser.import_csv()
        test_spa.upload_store_spa_csv()
        test_spa.open_latest_spa_store()
        storeUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update)
        test_spa.verify_spa_csv_uploaded()
        spa_to_check = test_spa.spa_id_check()
        ui_data = test_spa.get_data()
        sns = storeUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        storeUser.validate_schema(sns_data=sns, object='SpecialityAttribute')
        result = test_spa.compare_spa_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284397)
def test_store_csv_update_specialityattribute(env, driver):
    function_name = "test_store_csv_update_specialityattribute"
    print(f"\nRunning script: {function_name}")
    spa_update_csv()

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_spa = specialityAttribute(driver, env)
        storeUser.import_csv()
        test_spa.upload_store_spa_csv()
        test_spa.open_latest_spa_store()
        storeUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update)
        spa_to_check = test_spa.spa_id_check()
        test_spa.verify_spa_csv_uploaded_after_update()
        ui_data = test_spa.get_data()
        sns = storeUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        storeUser.validate_schema(sns_data=sns, object='SpecialityAttribute')
        result = test_spa.compare_spa_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@pytest.mark.reg
def test_store_spa_csv_export_with_default_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_spa_csv_export_with_default_language"
    print(f"\nRunning script: {function_name}")
    test_spa = storeAdminUser.open_latest_spa_store()
    sleep(5)
    test_spa.select_spa(single_export=True)
    test_spa.click_export_csv_button(export=True)
    test_spa.verify_headers_for_csv(valid_headers=['description_en','disclaimer_en', 'image'])
    object_Data = test_spa.read_spa_export_csv()
    test_spa.open_selected_spa(single_export=True)
    test_spa.spa_id_check()
    ui_data = test_spa.get_data()
    for spa in object_Data:
        if ui_data['description'] == spa['description_en']:
            print("SpecilaityAttribute present:", spa)
        else:
            assert False, f"Brand with ID {id} not present"
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed")
    logger.info(f"Testcase - {function_name} passed")

@pytest.mark.reg
def test_store_spa_csv_export_with_multi_language(storeAdminUser:storeAdmin, env):
    function_name = "test_store_spa_csv_export_with_multi_language"
    print(f"\nRunning script: {function_name}")
    test_spa = storeAdminUser.open_latest_spa_store()
    test_spa.select_spa(single_export=True)
    test_spa.click_export_csv_button(export=True)
    headers = storeAdminUser.fetch_spa_csv_headers(LANGUAGES_CODES_CONFIGURED)
    storeAdminUser.verify_headers_for_csv(valid_headers=headers)
    object_Data = test_spa.read_multilang_spa_export_csv()
    test_spa.open_selected_spa(single_export=True)
    spa_to_check = test_spa.spa_id_check()
    ui_data = test_spa.get_data()
    for spa in object_Data:
        if ui_data['description'] == spa['description_en']:
            print("SpecilaityAttribute present:", spa)
        else:
            assert False, f"Brand with ID {id} not present"
    storeAdminUser.closeObjectOpened()
    storeAdminUser.closeObjectOpened()
    print(f"Testcase - {function_name} passed, Speciality Attribute - {spa_to_check}")
    logger.info(f"Testcase - {function_name} passed, Speciality Attribute - {spa_to_check}")

@jamatest.test(16284401)
def test_store_ui_add_specialityattribute(env, driver):
    function_name = "test_store_ui_add_specialityattribute"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_spa = specialityAttribute(driver, env)
        test_spa.add_spa_ui_store()
        spa_to_check = test_spa.spa_id_check()
        test_spa.spa_basedata()
        ui_data = test_spa.get_data()
        test_spa.add_asset()
        storeUser.save_and_publish()
        sns = storeUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        storeUser.validate_schema(sns_data=sns, object='SpecialityAttribute')
        result = test_spa.compare_spa_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284402)
def test_store_ui_update_specialityattribute(env, driver):
    function_name = "test_store_ui_update_specialityattribute"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_spa = specialityAttribute(driver, env)
        test_spa.open_latest_spa_store()
        storeUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update)
        spa_to_check = test_spa.spa_id_check()
        test_spa.update_spa()
        ui_data = test_spa.get_data()
        storeUser.save_and_publish()
        sns = storeUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        storeUser.validate_schema(sns_data=sns, object='SpecialityAttribute')
        result = test_spa.compare_spa_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284403)
def test_store_ui_unpublish_specialityattribute(env, driver):
    function_name = "test_store_ui_unpublish_specialityattribute"
    print(f"\nRunning script: {function_name}")

    try:
        storeUser = storeAdmin(driver, constant.store_user_username, constant.store_user_password, env)
        test_spa = specialityAttribute(driver, env)
        test_spa.open_latest_spa_store()
        storeUser.get_latest_from_folder(obj_xpath=constant.latest_id_xpath_update)
        spa_to_check = test_spa.spa_id_check()
        ui_data = test_spa.get_data()
        storeUser.unpublish()
        sns = storeUser.get_sns_message_by_object_id(id=spa_to_check[2:], queue_url=get_queue_url(env))
        result = test_spa.compare_spa_unpublished_data(dict1=ui_data, dict2=sns)
        assert result, f"Speciality Attribute data comparison failed for testcase - {function_name}"
        print(f"Testcase - {function_name} passed successfully.")
        logger.info(f"Testcase - {function_name} passed successfully, Speciality Attribute - {spa_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()