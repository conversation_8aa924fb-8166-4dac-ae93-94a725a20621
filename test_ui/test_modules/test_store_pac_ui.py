from jamatest import jamatest
from modules.store import store
from users.pacAdmin import pacAdmin
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import wait, get_screenshot, logger

@jamatest.test(16284404)
def test_pac_ui_add_store(env, driver):
    function_name = "test_pac_ui_add_store"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_store = store(driver, env)
        pacUser.home_add_object()
        test_store.add_store()
        store_to_check = test_store.get_store_id()
        test_store.store_basedatatab()
        test_store.address_infotab()
        test_store.store_configtab()
        test_store.store_payconfigtab()
        test_store.store_associatedairlinetab()
        pacUser.save_and_publish()
        wait(15)
        pacUser.navigate_to_sns(add=True)
        sns_found = pacUser.verify_sns(id=store_to_check, save=True,
                                       sns_published=constant.store_sns_latest_published_xpath,
                                       sns_id_chk=constant.sns_id_chk)
        pacUser.verify_sns_and_download(crct_sns=sns_found, id=store_to_check,
                                        sns_published=constant.store_sns_latest_published_xpath, save=True)
        logger.info(f"Testcase - {function_name} passed successfully, Store ID - {store_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()

@jamatest.test(16284405)
def test_pac_ui_update_store(env, driver):
    function_name = "test_pac_ui_update_store"
    print(f"\nRunning script: {function_name}")

    try:
        pacUser = pacAdmin(driver, constant.pac_user_username, constant.pac_user_password, env)
        test_store = store(driver, env)
        test_store.open_latest_store()
        pacUser.get_latest_from_folder(obj_xpath=constant.store_latest_from_folder_xpath)
        store_to_check = test_store.get_store_id(update=True)
        test_store.update_store()
        pacUser.save_and_publish()
        wait(15)
        pacUser.navigate_to_sns(update_unpublish=True)
        sns_found = pacUser.verify_sns(id=store_to_check, save=True,
                                       sns_published=constant.store_sns_latest_published_xpath,
                                       sns_id_chk=constant.sns_id_chk)
        pacUser.verify_sns_and_download(crct_sns=sns_found, id=store_to_check,
                                        sns_published=constant.store_sns_latest_published_xpath, save=True)
        logger.info(f"Testcase - {function_name} passed successfully, Store ID - {store_to_check}")
    finally:
        get_screenshot(function_name, driver)
        print("Executed finally block - script end")
        driver.quit()