import pytest, pandas as pd
from modules.brand import brand
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from selenium.webdriver import Keys, ActionChains
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import wait, get_spa
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from time import sleep
from shared_config.config import *
from collections import defaultdict
import json



class product(baseModule):
    PANEL_XPATH = "//div[contains(@class,'x-panel pimcore_class_Products x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]"
    QUEUE_URL = CATALOG_SQS_QUEUE_URL
    def __init__(self, driver, e=None, data=None, populateId=False):
        super().__init__(driver)
        if data is None:
            data = defaultdict(lambda: defaultdict(dict))
        self.data = data
        self.e = e
        if populateId:
            self.id = self.getObjectID()

    def upload_pdt_pac_csv(self, e):
        global csv_file
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.pdtcsv_value)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.store_name))).send_keys(constant.store_value_name)
        wait(2)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.categorycsv_id))).click()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.categorypdt_value_xpath))).click()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.downloadcsv_btn_id))).click()
        wait(3)
        env = e.get('environment')
        if env == "dev":
            csv_file = Config.get_path("product_sample_dev_csv_path")
        elif env == "qa":
            csv_file = Config.get_path("product_sample_qa_csv_path")
        elif env == "test":
            csv_file = Config.get_path("product_sample_test_csv_path")
        else:
            print("Invalid product sample file")
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(csv_file)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new product")
        wait(15)

    def upload_pdt_store_csv(self, e):
        global csv_file
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.pdtcsv_value)
        wait(2)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.categorycsv_id))).click()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.categorypdt_value_xpath))).click()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.downloadcsv_btn_id))).click()
        wait(3)
        env = e.get('environment')
        if env == "dev":
            csv_file = Config.get_path("product_sample_dev_csv_path")
        elif env == "qa":
            csv_file = Config.get_path("product_sample_qa_csv_path")
        elif env == "test":
            csv_file = Config.get_path("product_sample_dev_csv_path")
        else:
            print("Invalid product sample file")
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(csv_file)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new product")
        wait(15)

    def verify_product_csv_uploaded(self):
        df = pd.read_csv(csv_file)
        name_csv = df['name_en'].tolist()
        print("Reading csv file uploaded - name in csv: " + (name_csv[0]).lower())
        field_value = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_name)))
        name_ui = field_value.get_attribute("value")
        print("Value of name in ui: " + name_ui.lower())
        if (name_ui.lower() == (name_csv[0]).lower()):
            print("Latest product is same as uploaded from CSV!")
        else:
            pytest.fail("Product names dont match between UI and CSV, Import failed")
            print("Product names dont match between UI and CSV, Import failed")

    def verify_product_csv_uploaded_after_update(self):
        df = pd.read_csv(csv_file)
        shortdesc_csv = df['shortDescription_en'].tolist()
        print("Reading csv file uploaded - name in csv: " + (shortdesc_csv[0]).lower())
        wait(5)
        element = self.driver.find_element(By.XPATH, "(//div[contains(@class,'pimcore_editable_wysiwyg')])[1]")
        desc_ui = element.text
        if (desc_ui.lower() == (shortdesc_csv[0]).lower()):
            print("Latest product is same as uploaded from CSV!")
        else:
            pytest.fail("Product names dont match between UI and CSV, Import failed.")
            print("Product names dont match between UI and CSV, Import failed.")

    def pac_open_latest_pdt(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_expand_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdt_folder_xpath))).click()
        click_column = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creationdate_column_xpath_update)))
        action.double_click(click_column).perform()
        wait(5)
        pdt_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdtlatest_id_xpath)))
        action.context_click(pdt_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(2)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened latest product")
        wait(5)

    def open_product(self, default_lang=None, multi_lang=None):
        action = ActionChains(self.driver)
        if default_lang:
            pdt_id = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"(//div[contains(text(),'/Products/{CATEGORY_NAME_VALUE}/')])[1]")))
            action.context_click(pdt_id).perform()
        elif multi_lang:
            pdt_id = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"(//div[contains(text(),'/Products/{CATEGORY_NAME_VALUE}/')])[1]")))
            action.context_click(pdt_id).perform()
        else:
            print("Invalid product path")
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(2)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened latest product")
        wait(5)

    def store_open_latest_pdt(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_management_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdt_folder_xpath))).click()
        wait(15)
        click_column = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creationdate_column_xpath_update)))
        action.double_click(click_column).perform()
        wait(10)
        pdt_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdtlatest_id_xpath)))
        action.context_click(pdt_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(2)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened latest product")
        wait(5)

    def add_store_pdt_asset_details(self, e):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.assets_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_1_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img1_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file1 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file1).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_2_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_2_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img2_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file2 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file2).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_3_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_3_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img3_search_value_pdt)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file3 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file3).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_4_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_4_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img3_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file4 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file4).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        env = e.get('environment')
        if env == "test":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.img_5test_dropdown_xpath))).click()
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.img_5_dropdown_store_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_5_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img5_search_value_pdt)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file5 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file5).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img6_expand_xpath))).click()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_6_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.img6_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        select_file = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_grid_xpath)))
        action.double_click(select_file).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.imgposition_name))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.PAGE_DOWN).perform()
        print("Filled fields under assets tab")
        wait(3)

    def get_pdt_id(self):
        global pdt_to_check
        pdt_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdt_id_xpath))).text
        print("Object id taken to check with sns in super admin logs - product id: " + pdt_to_check)
        return pdt_to_check

    def add_pdt_ui(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.productcreate_btn_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_sku_name))).send_keys(constant.pdtsku_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_choose_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.storeoptions_xpath))).click()
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_options_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.categorypdt_value_xpath))).click()
        product_set_select = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_set_select_name)))
        product_set_select.send_keys(constant.pdt_set_value)
        product_set_select.send_keys(Keys.RETURN)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.ok_btn_value))).click()
        print("Added new object - Product from UI")
        wait(5)

    def add_store_pdt_ui(self):
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.productcreate_btn_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_sku_name))).send_keys(constant.pdtsku_value + "_1")
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_options_store_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.categorypdt_value_xpath))).click()
        product_set_select = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_set_select_name)))
        product_set_select.send_keys(constant.pdt_set_value)
        product_set_select.send_keys(Keys.RETURN)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.ok_btn_value))).click()
        print("Added new object - Product from UI")
        wait(5)

    def select_brand(self, Id=None): #### May need to rework this to select the brand correctly
        current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)     
        wait = WebDriverWait(current_page, 10)
        brand_object = wait.until(EC.element_to_be_clickable((By.XPATH, ".//input[contains(@name,'brandSelect')]")))
        brand_object.send_keys(Id)
        sleep(1) ### propagation delay?? 
        brand_object.send_keys(Keys.RETURN)
        sleep(1)

    def isBrandpresentinui(self, id=None):
        id = str(id)
        brand_in_ui = self.wait.until(
            EC.element_to_be_clickable((By.XPATH,"//input[contains(@name,'brandSelect')]"))).get_attribute("value")
        if (id in brand_in_ui.lower()):
            print("Brand is updated in ui from CSV")
        else:
            pytest.fail("Brand is updation is not seen in ui from CSV")

    def isBrandNotAvailable(self, id=None):
        id = str(id)
        brand_list = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-form-arrow-trigger-default ')])[1]")))
        brand_list.click()
        ul_element = self.driver.find_element(By.CLASS_NAME, 'x-list-plain')
        elements_in_list = ul_element.find_elements(By.TAG_NAME, 'li')
        print(f"Checking for brand ID: {id}") # Print all items for debugging
        print("Items in dropdown:")
        for item in elements_in_list:
            print(f" - '{item.text.strip()}'")
        element_found = False
        for brand in elements_in_list:
            if id in brand.text.strip():
                element_found = True
                print(f"The brand id '{id}' was found in the dropdown item: '{brand.text.strip()}'")
                break
        brand_list.click()
        assert not element_found, f"Brand id '{id}' was found in the product dropdown. Unpublished/Deleted brands shouldn't be displayed"
        return True


    def verifyBrandEntered(self, Id=None):
        brand = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.Brand_field)))
        try:
            selected_value = brand.get_attribute("value")
            if not selected_value or selected_value.strip() == "":
                pytest.fail(f"Automation Engineer to investigate... Brand selection failed after entering ID '{Id}'")
            print("Entered brand Id is selected:", selected_value)
            if "Empty" in selected_value:
                pytest.fail(f"Automation Engineer to investigate. Empty found... Brand selection failed after entering ID '{Id}'")
        except Exception as e:
            pytest.fail(f"Automation Engineer to investigate... Brand selection failed after entering ID '{Id}': {e}")


    def add_pdt_basedata_details(self, name=None, shortDescription=None, description=None, brandId=None, barcode=None, deliveryMethod="Duty Free"):
        if name or shortDescription or description:
            self.enterLocalizedData(name=name, shortDescription=shortDescription, description=description)
        if brandId:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.Brand_field))).clear()
            sleep(0.5)
            print("Adding brand ID", brandId)
            self.select_brand(brandId)
            sleep(1)
            self.verifyBrandEntered(brandId)  
            self.data['brandId'] = int(brandId)
        if barcode:
            self.wait.until(EC.element_to_be_clickable((By.NAME, constant.barcode_name))).send_keys(barcode)
            self.data['barcode'] = barcode
        
        delivery_type = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.delivery_type_select_name)))
        delivery_type.send_keys(constant.deltype_value)
        delivery_type.send_keys(Keys.RETURN)
        producttype = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_type)))
        producttype.send_keys(deliveryMethod)
        producttype.send_keys(Keys.RETURN)
        if deliveryMethod == "Duty Free":
            self.data['deliveryMethod'] = "inflightCurrent"
        else:
            self.data['deliveryMethod'] = deliveryMethod

        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.pdtid_name))).send_keys(constant.pdtsku_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.na_country_name))).send_keys(constant.country_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.newfrm_name))).send_keys(constant.newfrm_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.newto_name))).send_keys(constant.newto_value)
        action = ActionChains(self.driver)
        action.send_keys(Keys.PAGE_DOWN).perform()
        print("Filled fields under basedata tab")
        sleep(2)

    def add_pdt_asset_details(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.assets_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_1_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img1_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file1 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file1).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_2_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_2_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img2_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file2 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file2).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_3_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_3_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img3_search_value_pdt)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file3 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file3).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_4_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_4_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img3_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file4 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file4).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_5_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_5_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img5_search_value_pdt)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file5 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file5).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img6_expand_xpath))).click()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_6_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.img6_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        select_file = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_grid_xpath)))
        action.double_click(select_file).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.imgposition_name))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.PAGE_DOWN).perform()
        print("Filled fields under assets tab")
        wait(3)

    def search_pdt(self, id=None, after_export=None, multi_lang=None):
        if after_export:
            if multi_lang:
                self.wait.until(
                    EC.visibility_of_element_located((By.XPATH, f"//span[contains(text(),'{CATEGORY_NAME_VALUE}')]"))).click()
            else:
                print("category", CATEGORY_NAME_VALUE)
                self.wait.until(
                    EC.visibility_of_element_located((By.XPATH, f"//span[contains(text(),'{CATEGORY_NAME_VALUE}')]"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(id,
                                                                                         Keys.ENTER)

        sleep(10)
        self.wait.until(EC.element_to_be_clickable(
            (By.XPATH, "(//div[contains(text(),'/Products')])[1]"))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-grid-cell-inner x-grid-checkcolumn-cell-inner')])[1]"))).click()

    def add_pdt_pricetaxtab_details(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.pricing_and_taxation_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.price_name))).send_keys(constant.imgposition_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.specialPrice_name))).send_keys(constant.imgposition_value)
        self.driver.find_element(By.NAME, constant.taxtick_name).click()
        # self.wait.until(
        #     EC.element_to_be_clickable((By.NAME, constant.maxqty_name))).send_keys(constant.imgposition_value)
        print("Filled fields under pricing_and_taxation tab")
        wait(3)

    def add_weightandshippingtab_details(self):
        action = ActionChains(self.driver)
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.weight_and_shipping_details_tab_text))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.weight_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.weight_xpath))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipl_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipl_xpath))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipw_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipw_xpath))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shiph_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shiph_xpath))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.wt_unit_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.wt_unit_xpath))).send_keys(constant.wt_unit_value)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.wt_unit_xpath))).send_keys(Keys.RETURN)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipl_unit_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipl_unit_xpath))).send_keys(constant.ship_unit_value)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipl_unit_xpath))).send_keys(Keys.RETURN)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipw_unit_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipw_unit_xpath))).send_keys(constant.ship_unit_value)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shipw_unit_xpath))).send_keys(Keys.RETURN)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shiph_unit_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shiph_unit_xpath))).send_keys(constant.ship_unit_value)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.shiph_unit_xpath))).send_keys(Keys.RETURN)
        print("Filled fields under weight_and_shipping_details tab")
        wait(3)

    def open_inventory_tab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.inventory_tab_text))).click()
        print("Filled fields under inventory tab")

    def add_categoryspecifictab_details(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.categoryspecific_tab_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.display_name))).send_keys(constant.imgposition_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.ram_name))).send_keys(constant.ram_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.os_name))).send_keys(constant.os_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.phonefield_name))).send_keys(constant.phone_value)
        print("Filled fields under category_specific tab")
        wait(3)

    def add_store_pdt_pricetaxtab_details(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.pricing_and_taxation_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.price_name))).send_keys(constant.imgposition_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.specialPrice_name))).send_keys(constant.imgposition_value)
        self.driver.find_element(By.NAME, constant.taxtick_name).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.maxqty_name))).send_keys(constant.imgposition_value)
        print("Filled fields under pricing_and_taxation tab")
        wait(3)

    def add_store_weightandshippingtab_details(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.weight_and_shipping_details_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.weight_store_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.weight_store_xpath))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipl_store_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipl_store_xpath))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipw_store_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipw_store_xpath))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shiph_store_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shiph_store_xpath))).send_keys(constant.imgposition_value)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.wt_unit_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.wt_unit_xpath))).send_keys(constant.wt_unit_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.wt_unit_xpath))).send_keys(Keys.RETURN)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipl_unit_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipl_unit_xpath))).send_keys(constant.ship_unit_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipl_unit_xpath))).send_keys(Keys.RETURN)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipw_unit_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipw_unit_xpath))).send_keys(constant.ship_unit_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shipw_unit_xpath))).send_keys(Keys.RETURN)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shiph_unit_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shiph_unit_xpath))).send_keys(constant.ship_unit_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shiph_unit_xpath))).send_keys(Keys.RETURN)
        print("Filled fields under weight_and_shipping_details tab")
        wait(3)

    def open_storespecifictab(self):
        storespecific_tab = self.wait.until(
            EC.presence_of_element_located((By.XPATH, constant.storespecific_tab_xpath)))
        self.driver.execute_script("arguments[0].scrollIntoView();", storespecific_tab)
        self.driver.find_element(
            By.XPATH,
            constant.storespecific_tab_xpath,
        ).click()
        # self.wait.until(
        #     EC.element_to_be_clickable((By.LINK_TEXT, constant.storespecific_tab_text))).click()
        print("Opened store_specific tab")

    def add_spatab_details(self):
        wait(5)
        spa_tab = self.wait.until(
            EC.presence_of_element_located((By.XPATH, constant.spa_tab_xpath)))
        self.driver.execute_script("arguments[0].scrollIntoView();", spa_tab)
        self.driver.find_element(
            By.XPATH,
            constant.spa_tab_xpath,
        ).click()
        get_spa(self.driver, self.e)
        print("Filled fields under speciality_attribute tab")

    def update_latest_pdt(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shortdesc_pdt_xpath))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.shortdesc_pdt_xpath))).send_keys(
            constant.pdtsku_value + "_updatedValue")
        print("Updated short_description value")

    def get_data(self):
        self.driver.refresh()
        wait(50)
        self.data['id'] = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdt_id_xpath))).text
        self.data['title'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_name))).get_attribute("value")
        element = self.driver.find_element(By.XPATH, "(//div[contains(@class,'pimcore_editable_wysiwyg')])[1]")
        self.data['shortDescription'] = element.text
        element = self.driver.find_element(By.XPATH, "(//div[contains(@class,'pimcore_editable_wysiwyg')])[2]")
        self.data['description'] = element.text
        self.driver.execute_script("window.scrollTo(0,500)")
        self.data['sku'] = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdt_sku_xpath))).get_attribute("value")
        element = self.driver.find_element(By.XPATH, constant.deliverymethod_xpath)
        self.data['deliveryMethod'] = element.text
        self.data['productType'] = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdttype_xpath))).get_attribute("value")
        wait(5)
        spa_tab = self.wait.until(
            EC.presence_of_element_located((By.XPATH, constant.spa_tab_xpath)))
        self.driver.execute_script("arguments[0].scrollIntoView();", spa_tab)
        self.driver.find_element(
            By.XPATH,
            constant.spa_tab_xpath,
        ).click()
        element = self.wait.until(
            EC.presence_of_element_located((By.XPATH, constant.spa_value_xpath)))
        self.data['SpecialityAttribute'] = element.text
        print("Data taken from UI")
        return self.data

    def compare_pdt_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n",dict2)

        id1=dict1['id'].split(' ')[1]
        shortDescription1 = dict1['shortDescription']
        description1 = dict1['description']
        sku1 = dict1['sku']
        # deliveryMethod1 = dict1['deliveryMethod']
        productType1 = dict1['productType']
        specialityAttribute1 = dict1['SpecialityAttribute']

        id2 = dict2['id']
        shortDescription2 = dict2['shortDescription']['en']
        description2 = dict2['description']['en']
        sku2 = dict2['sku']
        deliveryMethod2 = dict2['deliveryMethod'][0]['name']
        productType2 = dict2['productType']
        attribute = dict2['specialityAttributes'][0]
        id_value = attribute['id']
        description_value = attribute['description']['en']
        result = f"{id_value} - {description_value}"
        specialityAttribute2 = result

        # comparing dict1 and dict2 values
        comparisons = {
            "id": id1 == str(id2),
            "shortDescription": shortDescription1 == shortDescription2,
            "description": description1 == description2,
            "sku": sku1 == str(sku2),
            "deliveryMethod": deliveryMethod2 == "inflightCurrent",
            "productType": productType1 == productType2,
            "SpecialityAttribute": specialityAttribute1 == specialityAttribute2
        }
        print("\nComparisons -",comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
                    return False
        else:
            print("Comparison succeeded.")
            return True

    def compare_pdt_unpublished_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n", dict2)

        id1 = dict1['id'].split(' ')[1]
        id2 = dict2['id']

        # comparing dict1 and dict2 values
        comparisons = {
            "id": id1 == str(id2),
        }
        print("\nComparisons -",comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
                    return False
        else:
            print("Comparison succeeded.")
            return True

    def get_basedata(self):
        self.driver.refresh()
        sleep(5)
        id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdt_id_xpath))).text
        self.data['id'] = id.split(' ')[1]
        self.data['title'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_name))).get_attribute("value")
        element = self.driver.find_element(By.XPATH, "(//div[contains(@class,'pimcore_editable_wysiwyg')])[1]")
        self.data['shortDescription'] = element.text
        element = self.driver.find_element(By.XPATH, "(//div[contains(@class,'pimcore_editable_wysiwyg')])[2]")
        self.data['description'] = element.text
        self.driver.execute_script("window.scrollTo(0,500)")
        self.data['sku'] = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdt_sku_xpath))).get_attribute("value")
        element = self.driver.find_element(By.XPATH, constant.deliverymethod_xpath)
        self.data['deliveryMethod'] = "inflightCurrent" ### What is the acutal delivery method entered? we can use an if sta
        self.data['productType'] = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pdttype_xpath))).get_attribute("value")
        sleep(5)
        return self.data

    def ensure_dict(self,value=None):
        if isinstance(value, str):
            return {'en': value}
        elif isinstance(value, dict):
            return value
        return {}

    def validate_multi_lang(self, payload_entry, payload_sns, languages):
        payload_entry = self.ensure_dict(payload_entry)
        payload_sns = self.ensure_dict(payload_sns)
        if not languages:
            assert payload_entry['en'] == payload_sns['en']
        else:
            for language in languages:
                if language not in payload_entry:
                    assert payload_entry['en'] == payload_sns[language]
                else:
                    assert payload_entry[language] == payload_sns[language]

    def enterLocalizedData(self, lang="English", langCode='en', name=None, shortDescription=None, description=None ):
        "Enters data for the Name, short description and long description. These are localized fields for multi-language"
        current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)     
        self.open_base_data()
        self.selectLanguage(lang)
        current_language_field = current_page.find_element(By.XPATH,".//div[@class='x-panel x-tabpanel-child x-panel-default']")
        wait = WebDriverWait(current_language_field, 10)
        if name is None:
            name = constant.pdtsku_value
        name_field = wait.until(EC.element_to_be_clickable((By.NAME, constant.name_name)))
        name_field.clear()
        name_field.send_keys(name) 
        self.data['title'][langCode] = name 
        logger.info(f"Entered name '{name}'  for language, {lang}" )

        if shortDescription:
            xpath = f".//div[contains(text(), 'Short Description')]/ancestor::div[5]//div[contains(@class, 'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr cke_show_borders')]"
            wait.until(EC.element_to_be_clickable((By.XPATH, xpath))).send_keys(shortDescription)
            self.data['shortDescription'][langCode] = shortDescription
            logger.info(f"Entered shortDescription '{shortDescription}'  for language, {lang}" )
        if description:
            xpath = f".//div[text()='Description']/ancestor::div[5]//div[contains(@class, 'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr cke_show_borders')]"
            wait.until(EC.element_to_be_clickable((By.XPATH, xpath))).send_keys(description)
            self.data['description'][langCode] = description
            logger.info(f"Entered description '{description}'  for language, {lang}" )



    def validateSNS(self, languages:list=None, csv=None, brand_id_csv=None):
        """Fetch the latest SNS and compare data against the existing data stored
        Args:
            in_data (_type_): _description_
        """
        print("the data")
        print(json.dumps(self.data, indent=4))
        if csv:
            self.id = brand_id_csv
        sns = self.get_sns(self.id)
        print("Validating SNS/S3 data")
        assert sns['id'] == self.id
        assert "title" in self.data and "title" in sns
        self.validate_multi_lang(self.data['title'], sns['title'], languages)
        if "shortDescription" in self.data:
            self.validate_multi_lang(self.data['shortDescription'], sns['shortDescription'], languages)
        if "description" in self.data:
            self.validate_multi_lang(self.data['description'], sns['description'], languages)
        if "brandId" in self.data:
            assert self.data["brandId"] == sns['brand_id']
        assert self.data['deliveryMethod'] == sns['deliveryMethod'][0]['name']
        return sns
        
        # if "image" in self.data:
        #     validate_input_in_output(self.data['image'], sns['image'], "image")