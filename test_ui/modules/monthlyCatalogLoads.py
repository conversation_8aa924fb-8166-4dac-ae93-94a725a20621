from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.login_helpers import wait
from selenium.webdriver import ActionChains, Keys
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class monthlyCatalogLoads(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        self.e = e
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }

    def get_id(self):
        id_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'ID')]"))).text
        print("Object id taken to check with sns in super admin logs " + id_to_check)
        return id_to_check

    def add_monthlycatalogload(self):
        action = ActionChains(self.driver)
        home = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.home_xpath)))
        action.context_click(home).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.monthlycatalogload_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_category_id))).send_keys(constant.monthlycatalogload_name)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Monthly Catalog Load through UI")

    def open_airline(self):
        wait(5)
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.airline_textbox))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.airline_textbox))).send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN)
        print("Opened airline")
        wait(5)

    def get_data(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.getcadata_btn_text))).click()
        print("Clicked on getdata button")
        wait(10)
