import csv
from time import sleep
from conftest import driver
from modules.airline import airline
from users.pacAdmin import pacAdmin
from users.storeAdmin import storeAdmin
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from users.airlineAdmin import airlineAdmin
from project_configs.csv_config import Config
from selenium.webdriver import Action<PERSON>hains, Keys
from project_configs.env_ui_constants import constant
from selenium.webdriver.support.wait import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common import NoSuchElementException, TimeoutException
from project_configs.login_helpers import wait, generate_random_string, get_uitemp, generate_random_integer


class catalogAssignment(baseModule):
    def __init__(self, driver, e, data: dict = {}):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }



    def open_store_in_pac_admin(self, store_only=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.store_text)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.storesfolder_xpath))).click()
        action.send_keys(Keys.RETURN).perform()
        if store_only:
            element = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.store_close)))
            action.context_click(element).perform()
            self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Close Others"))).click()
            wait(3)
        search_input = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.search_input_name)))
        search_input.send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        select_store = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//table[contains(@id,'patchedgridview-')]")))
        action.context_click(select_store).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened store in pac admin")

    def create_catalogassignment_by_pacadmin(self, automapOFF=None, deltachange=None,
                                             autoupdatecatalog=None, id_second=None):
        # storeUser = storeAdmin(self.driver, constant.store_user_username, constant.store_user_password, self.e)
        # self.check_autoapprove_catalog_store()
        # storeUser.logout()
        # self.driver.refresh()
        pacUser = pacAdmin(self.driver, constant.pac_user_username, constant.pac_user_password, self.e)
        # self.open_store_in_pac_admin()
        # self.check_autoapprove_catalog_store()
        # self.open_airline_in_pac_admin()
        # wait(5)
        if autoupdatecatalog:
            self.open_airline_in_pac_admin()
            self.check_autoupdatecatalog()
        elif autoupdatecatalog==False:
            self.open_airline_in_pac_admin()
            self.uncheck_autoupdatecatalog()
        else:
            print("Invalid autoupdate catalog state")
        pacUser.home_add_object()
        self.pac_add_ca()
        # if autoupdatecatalog==False:
        #     self.open_ca_clear_others()
        if id_second:
            ca_to_check = self.get_ca_id(id=True)
        else:
            ca_to_check = self.get_ca_id()
        wait(5)
        if deltachange:
            self.add_delta_catalog()
        else:
            self.add_catalog_5lvl_value()
        self.add_basedata_details()
        self.add_assignmenttype_routegroup()
        self.perform_action_request_for_association_state()

        # # for dev bug
        # try:
        #     element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
        #     element.click()
        # except NoSuchElementException:
        #     pass
        # wait(5)
        # self.driver.refresh()
        # wait(5)

        if automapOFF:
            self.open_products_tab()
            self.map_5level_airline_category(pac=True)
            self.perform_action_airlinecategory_mapped_state()
        else:
            pass
        print("Created catalog assignment through pac admin")
        return ca_to_check

    def create_catalogassignment_by_airlineadmin(self, automapON=None, automapOFF=None, deltachange=None,
                                                 autoupdatecatalog=None, airline_first=None):
        # storeUser = storeAdmin(self.driver, constant.store_user_username, constant.store_user_password, self.e)
        # self.check_autoapprove_catalog_store()
        # storeUser.logout()
        # self.driver.refresh()
        # self.open_store_in_pac_admin()
        # self.check_autoapprove_catalog_store()
        # self.open_airline_in_pac_admin()
        # if automapON:
        #     self.automap_on()
        # elif automapOFF:
        #     self.automap_off()
        # else:
        #     print("Please provide valid state for automap in airline")
        if autoupdatecatalog:
            pacUser = pacAdmin(self.driver, constant.pac_user_username, constant.pac_user_password, self.e)
            self.open_airline_in_pac_admin()
            self.check_autoupdatecatalog()
            wait(15)
            pacUser.logout()
            self.driver.refresh()
        airlineAdmin(self.driver, constant.airline_user_username, constant.airline_user_password, self.e)
        if airline_first:
            self.airline_add_ca(airline1=True)
        else:
            self.airline_add_ca()
        ca_to_check = self.get_ca_id()
        if deltachange:
            self.add_delta_catalog()
        else:
            self.add_catalog_5lvl_value()
        self.add_basedata_details(airline=True)
        self.add_assignmenttype_routegroup()
        self.perform_action_request_for_association_state()
        if automapOFF:
            self.open_products_tab()
            self.map_5level_airline_category(airline=True)
            self.perform_action_airlinecategory_mapped_state()
        else:
            pass
        print("Created catalog assignment through airline admin")
        return ca_to_check

    def open_category_make_uitemp_empty(self, airline=None):
        action = ActionChains(self.driver)
        if airline:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.category_management_tab_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
            wait(5)
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.airline_value)
            action.send_keys(Keys.RETURN).perform()
            wait(10)
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
            wait(5)
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
            wait(5)
        env = self.e.get('environment')
        if env == "dev":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_dev,
                Keys.ENTER)
        elif env == "qa":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_qa,
                Keys.ENTER)
        elif env == "test":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_test,
                Keys.ENTER)
        else:
            print("Invalid category id")
        wait(5)
        obj_id = WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.XPATH, constant.ac_search_xpath_sixth)))
        action.context_click(obj_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.del_uitemp_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        print("Updated uitemp to empty in category")
        wait(3)

    def chk_delta_update(self, airline=None, airlineOFF=None, airline_delta=None, airline_name_change=None):
        wait(8)
        if airline:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airlineadmin_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airlineadmin_xpath_with_input))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        elif airlineOFF:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_box_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_box_xpath_with_input))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        elif airline_delta:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airline_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airline_box_xpath_with_input))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        elif airline_name_change:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_xpath_with_input))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_pac_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_pac_box_xpath_with_input))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.load_update_option_xpath))).click()
        wait(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.ok_btn_text)
            self.driver.execute_script("arguments[0].click();", element)
            wait(3)
            element = self.driver.find_element(By.LINK_TEXT, constant.ok_btn_text)
            self.driver.execute_script("arguments[0].click();", element)
            print("No update in delta catalog under products tab")
        except NoSuchElementException:
            print("Not clicked on ok button")
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.delta_tab_text))).click()
        wait(5)
        self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.updatecatalog_text))).click()
        assert WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located((By.XPATH,
                                            '//div[contains(text(),"No Products To Update")]'))).is_displayed()
        element = self.driver.find_element(By.LINK_TEXT, constant.ok_btn_text)
        self.driver.execute_script("arguments[0].click();", element)
        print("No update in delta catalog under delta tab")
        wait(5)

    def invalid_category_seq(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        self.driver.find_element(By.NAME, constant.selectfilecat_name).send_keys(
                Config.get_path("ca_invalid_catseq_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Providing invalid sequences under category sequence tab")
        wait(5)

    def find_error_msg_invalid_category_seq(self):
        assert WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//label[contains(text(),"Sequences are not added for the following category")]'))).is_displayed()
        print("Error message pop-up came for invalid category sequence")

    def invalid_product_seq(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        env = self.e.get('environment')
        if env == "dev":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_invalid_pdtseq_dev_sample_csv_path"))
        elif env == "qa":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_invalid_pdtseq_qa_sample_csv_path"))
        elif env == "test":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_invalid_pdtseq_test_sample_csv_path"))
        else:
            print("Invalid product sequence case")
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Providing invalid sequences under product sequence tab")
        wait(5)

    def find_error_msg_invalid_product_seq(self):
        assert WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//label[contains(text(),"Sequences are not added for the following product")]'))).is_displayed()
        print("Error message pop-up came for invalid product sequence")

    def check_airlinecategory_mapping_state_presence(self):
        assert WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//div[contains(text(),"Airline Category Mapping Pending")]'))).is_displayed()
        print("The Catalog Assignment is currently in Airline Category Mapping Pending state")

    def check_editairlinecategory_state_presence(self):
        assert WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located(
                (By.XPATH, '(//div[contains(text(),"Edit Airline Category")])[1]'))).is_displayed()
        print("The Catalog Assignment is currently in Edit Airline Category state")

    def check_requestforassociation_state_presence(self):
        wait(5)
        assert WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located(
                (By.XPATH, '//div[contains(text(),"Requested for Association")]'))).is_displayed()
        print("The Catalog Assignment is currently in Requested For Association state")

    def check_autoapprove_catalog_store(self, store=None):
        # self.wait.until(
        #     EC.element_to_be_clickable((By.XPATH, constant.store_folder_xpath))).click()
        # self.wait_and_click(By.XPATH, constant.store_folder_xpath).click()
        # wait(5)
        # try:
        #     element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
        #     element.click()
        # except NoSuchElementException:
        #     pass
        if store:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.store_folder_xpath))).click()
            wait(5)
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.config_tab_text))).click()
        wait(5)
        checkbox = self.driver.find_element(By.XPATH, constant.autoapprove_chk_xpath)
        if checkbox.is_selected():
            print("Checkbox is checked")
        else:
            print("Checkbox is not checked")
            checkbox.click()
            self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
            wait(20)
        print("Checked autoapprove catalog box in store profile under configuration tab")
        wait(5)

    def uncheck_autoapprove_catalog_store(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_folder_xpath))).click()
        wait(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.config_tab_text))).click()
        wait(5)
        checkbox = self.driver.find_element(By.XPATH, constant.autoapprove_chk_xpath)
        if checkbox.is_selected():
            print("Checkbox is checked")
            checkbox.click()
            self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
            wait(20)
        else:
            print("Checkbox is not checked")
        print("Un-checked autoapprove catalog box in store profile under configuration tab")
        wait(5)

    def check_autoupdatecatalog(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_click_xpath))).click()
        wait(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.config_tab_text))).click()
        wait(5)
        checkbox = self.driver.find_element(By.XPATH, constant.autoupdate_xpath)
        if checkbox.is_selected():
            print("Checkbox is checked")
        else:
            print("Checkbox is not checked")
            checkbox.click()
            self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
            wait(20)
        print("Checked auto update catalog box in airline profile under configuration tab")
        wait(5)

    def uncheck_autoupdatecatalog(self, airline_second=None):
        wait(10)
        if airline_second:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.ca_airline_folder_xpath))).click()
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.airline_click_xpath))).click()
        wait(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.config_tab_text))).click()
        wait(5)
        checkbox = self.driver.find_element(By.XPATH, constant.autoupdate_xpath)
        if checkbox.is_selected():
            print("Checkbox is checked")
            checkbox.click()
            self.wait.until(
                    EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
            wait(20)
        else:
            print("Checkbox is not checked")
        print("Un-checked auto update catalog box in store profile under configuration tab")
        wait(5)

    def open_airline_in_pac_admin(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update)))
        search_home_button = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update)))
        search_home_button.click()
        filter_input = self.wait.until(
            EC.visibility_of_element_located((By.NAME, constant.filterfromhome_name)))
        filter_input.send_keys(constant.airline_folder_text)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        airline_expander = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_main_folder_expander_xpath)))
        airline_expander.click()
        wait(5)
        airline_folder = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_folder_search_xpath)))
        airline_folder.click()
        airline_folder_filter = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_folder_filter_xpath)))
        airline_folder_filter.send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        print("Opened airline profile")

    def open_airline_in_airline_admin(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_profile_xpath))).click()
        print("Opened airline profile in airline admin")
        wait(5)

    def automap_off(self):
        element = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_name_xpath)))
        self.driver.execute_script("arguments[0].click();", element)
        wait(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.stores_tab_text))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL, 'a')
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.BACK_SPACE * 3)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        wait(10)
        # wait(80)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL, 'a')
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys("No", Keys.ENTER)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        wait(15)
        # wait(80)
        self.driver.refresh()
        print("In airline profile - turned off automap and autopopulate")

    def automap_on(self):
        element = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_name_xpath)))
        self.driver.execute_script("arguments[0].click();", element)
        wait(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.stores_tab_text))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL, 'a')
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.BACK_SPACE * 3)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        wait(15)
        # wait(25)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL, 'a')
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys("Yes", Keys.ENTER)
        wait(10)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//td[5]"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[5]//input"))).send_keys(Keys.CONTROL, 'a')
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[5]//input"))).send_keys("Yes", Keys.ENTER)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        wait(15)
        # wait(80)
        self.driver.refresh()
        print("In airline profile - turned on automap and autopopulate")

    def pac_open_latest_ca(self,airline_delta_check=None):
        global creation_date
        action = ActionChains(self.driver)
        self.driver.refresh()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update)))
        search_home_button = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update)))
        search_home_button.click()
        filter_input = self.wait.until(
            EC.visibility_of_element_located((By.NAME, constant.filterfromhome_name)))
        filter_input.send_keys(constant.catalog_assignment_folder)
        action.send_keys(Keys.RETURN).perform()
        wait(15)
        ca_expander = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_folder_expander_xpath)))
        ca_expander.click()
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_filter_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_filter_store_send_xpath))).send_keys(
            constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_name_xpath))).click()
        if airline_delta_check:
            creation_date = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_column_after_update_xpath)))
        else:
            creation_date = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.creation_date_column_xpath)))
        action.double_click(creation_date).perform()
        wait(5)
        ca_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_latest_xpath)))
        action.context_click(ca_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opening latest object - Catalog Assignment from UI")

    def pac_add_ca(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.catalog_folder_text))).click()
        action.send_keys(Keys.ARROW_RIGHT).perform()
        wait(8)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.ca_obj_add_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_catalog_id))).send_keys(constant.catalog_assignment_name + generate_random_string(4))
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Catalog Assignment from pac admin UI")
        wait(10)

    def airline_add_ca(self, airline1=None):
        action = ActionChains(self.driver)
        self.driver.refresh()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_assignment_tab_xpath))).click()
        wait(10)
        if airline1:
            ca_folder = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.airline_first_xpath)))
        else:
            ca_folder = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.ca_airline_folder_xpath)))
        action.context_click(ca_folder).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        action.send_keys(Keys.ARROW_RIGHT, Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_box_xpath))).send_keys(
            constant.catalog_assignment_name+generate_random_string(2))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.ok_btn_text))).click()
        print("Created new catalog assignment from airline admin ui")

    def get_ca_id(self,id=None):
        wait(5)
        global catalog_assignment_to_check
        # catalog_assignment_to_check = self.wait.until(
        #     EC.element_to_be_clickable((By.XPATH, constant.ca_id_xpath))).text
        if id:
            catalog_assignment_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(text(),'ID')])[2]"))).text
        else:
            catalog_assignment_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(text(),'ID')])[1]"))).text
        print("Object id taken to check with sns in super admin logs - Catalog Assignment id: " + catalog_assignment_to_check)
        return catalog_assignment_to_check

    def add_basedata_details(self, airline=None):
        action = ActionChains(self.driver)
        self.wait.until(EC.element_to_be_clickable((By.NAME, constant.catalogfrom_name))).send_keys(constant.current_date)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(EC.element_to_be_clickable((By.NAME, constant.catalogto_name))).send_keys(constant.next_date_str)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        if airline:
            pass
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.NAME, constant.airline_field_name))).send_keys(constant.airline_value)
            action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(EC.element_to_be_clickable((By.NAME, constant.cabincls_name))).send_keys(constant.cabincls_value)
        action.send_keys(Keys.RETURN).perform()
        print("Added other basedata details")
        wait(3)

    def open_base_data_tab(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.basedata_xpath))).click()
        print("Opened basedata tab")

    def catalog_delta_change(self, update=None, delete=None, addpdt=None, airlineOFF=None):
        self.pac_open_catalog_to_update(airline=airlineOFF)
        self.open_products_tab()
        if update:
            if addpdt:
                self.update_products(add=True)
            else:
                self.update_products(airline=airlineOFF)
        elif delete:
            self.delete_pdt_from_catalog()
        else:
            print("Invalid catalog state")
        pacAdmin.save_and_publish(self)
        print("Catalog delta change done for update/delete")
        wait(10)

    def open_latest_created_ca(self):
        wait(10)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.open_ca_tab_xpath))).click()
        print("Opened catalog assignment")


    def open_airline_make_automap_off(self):
        # wait(80)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.ca_airline_folder_xpath))).click()
        print("Opened airline")
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.stores_tab_text))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL, 'a')
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.BACK_SPACE * 3)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        wait(10)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL, 'a')
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys("No", Keys.ENTER)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        wait(10)
        self.driver.refresh()
        print("In airline profile - turned off automap and autopopulate")

    def open_ca_clear_others(self):
        print("Opened catalog assignment")
        action = ActionChains(self.driver)
        element = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.open_ca_tab_xpath)))
        action.context_click(element).perform()
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Close Others"))).click()
        print("Closing other tabs other than catalog assignment")
        wait(3)

    def close_tab(self):
        action = ActionChains(self.driver)
        element = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.open_store_xpath)))
        action.context_click(element).perform()
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Close Tab"))).click()
        print("Closing tab")
        wait(3)

    def refresh_after_catalog_save(self):
        self.driver.refresh()
        print("Refreshing screen")
        wait(10)

    def update_catalog_delta(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.ca_delta_tab_xpath))).click()
        print("Opened catalog")
        self.open_products_tab()
        self.driver.refresh()
        self.open_products_tab()
        self.delete_pdt_from_catalog()
        pacAdmin.save_and_publish(self)
        print("Updating catalog delta")
        wait(3)

    def open_airline(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.ca_airline_folder_xpath))).click()
        print("Opened airline")

    def add_catalog_5lvl_value(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_catalog_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.ca_select_catalog_5level)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        select_catalog = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_value)))
        action.double_click(select_catalog).perform()
        print("Added catalog value")

    def add_delta_catalog(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_catalog_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
            constant.ca_select_catalog_delta)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        select_catalog = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_delta_value)))
        action.double_click(select_catalog).perform()
        print("Added delta catalog value")

    def add_category_changes(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
        wait(5)
        env = self.e.get('environment')
        if env == "dev":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_dev,
                Keys.ENTER)
        elif env == "qa":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.category_namechangecase_id_qa,
                                                                                             Keys.ENTER)
        elif env == "test":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_test,
                Keys.ENTER)
        else:
            print("Invalid category id")
        wait(10)
        obj_id = WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.XPATH, constant.ac_search_xpath)))
        action.context_click(obj_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).click()
        print("Opened category for name updation")
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).send_keys(constant.category_namechangecase_text+generate_random_string(2))
        new_name = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).get_attribute("value")
        self.save_and_publish()
        print("Updated new name in category")
        # self.driver.refresh()
        wait(15)
        print("Added category changes")
        return new_name

    def add_category_changes_for_on_case(self, airline=None):
        action = ActionChains(self.driver)
        if airline:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.category_management_tab_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
            wait(5)
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.airline_value)
            action.send_keys(Keys.RETURN).perform()
            wait(10)
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
            wait(5)
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
            wait(5)
        # env = self.e.get('environment')
        # if env == "qa":
        #     self.wait.until(
        #         EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
        #         constant.category_namechangecase_id_qa,
        #         Keys.ENTER)
        # elif env == "dev":
        #     pass
        # elif env == "test":
        #     pass
        # else:
        #     pass
        # wait(10)
        env = self.e.get('environment')
        if env == "dev":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_dev,
                Keys.ENTER)
        elif env == "qa":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_qa,
                Keys.ENTER)
        elif env == "test":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_test,
                Keys.ENTER)
        else:
            print("Invalid category id")
        wait(5)
        obj_id = WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.XPATH, constant.ac_search_xpath)))
        action.context_click(obj_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        print("Opened category for uitemp updation")
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.uitemp_search_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath_ca))).send_keys(constant.uitemp_created_value)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        get_uitemp(self.driver, self.e)
        self.save_and_publish()
        wait(10)
        self.driver.refresh()
        wait(15)
        print("Added category changes")

    def add_category_changes_through_airlineadmin(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_management_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
        wait(5)
        env = self.e.get('environment')
        if env == "dev":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_dev,
                Keys.ENTER)
        elif env == "qa":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.category_namechangecase_id_qa,
                                                                                             Keys.ENTER)
        elif env == "test":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(
                constant.category_namechangecase_id_test,
                Keys.ENTER)
        else:
            print("Invalid category id")
        wait(5)
        obj_id = WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_airlinecategory_xpath)))
        action.context_click(obj_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).click()
        print("Opened category for name updation")
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).send_keys(
            constant.category_namechangecase_text + generate_random_string(2))
        new_name = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).get_attribute("value")
        self.save_and_publish()
        print("Updated new name in category")
        self.driver.refresh()
        wait(10)
        return new_name

    def open_category_make_old_name(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.open_category_tab_text))).click()
        print("Opened category")
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).send_keys(constant.category_namechangecase_text)
        self.save_and_publish()
        print("Updated old name in category")
        wait(5)

    def add_assignmenttype_routegroup(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.assignmenttype_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.assignmenttype_name))).send_keys(
            constant.assignmenttype_value_R)
        wait(3)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.rg_name))).send_keys(constant.rg_value)
        wait(3)
        action.send_keys(Keys.RETURN).perform()
        print("Added assignmenttype as RouteGroup")
        wait(10)

    def add_assignmenttype_flight(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.assignmenttype_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.assignmenttype_name))).send_keys(
            constant.assignmenttype_value_F)
        wait(3)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_flight_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.flight_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        action.send_keys(Keys.RETURN).perform()
        flight = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_flight_value)))
        action.double_click(flight).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.select_btn_text))).click()
        print("Added assignmenttype as Flight")
        wait(10)

    def add_assignmenttype_sector(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.assignmenttype_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.assignmenttype_name))).send_keys(
            constant.assignmenttype_value_S)
        wait(3)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_sector_search_xpath))).click()
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.sector_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        sector = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_sector_value)))
        action.double_click(sector).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.select_btn_text))).click()
        print("Added assignmenttype as Sector")
        wait(10)

    def perform_action_request_for_association_state(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.actions_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.req_assoc_text))).click()
        print("Performed Request for Association action")
        wait(10)

    def perform_action_request_for_dissociation_state(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.actions_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.req_dissoc_text))).click()
        print("Performed Request for Dissociation to Store action")
        wait(10)

    def open_products_tab(self):
        # wait(20)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.products_tab_text))).click()
        print("Opened Products tab")
        wait(5)

    def ca_read_category_name(self, name=None):
        element = self.driver.find_element(By.XPATH, constant.aircat_value1_5lvl_dev_xpath).text
        pdt2_mapped_airline_category = element.split('-')[1]
        print("airlinecategory detail in ca = ",pdt2_mapped_airline_category)
        print("airlinecategory name updated in category = ",name)
        if name in pdt2_mapped_airline_category:
            print("New name updated")
            return True
        else:
            print("Name is not updated")
            return False

    def update_uitemp_back_to_empty(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.open_category_tab_text))).click()
        print("Opened category")
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH,constant.del_uitemp_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        print("Updated uitemp to empty in category")
        wait(3)

    def open_productseq_tab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.pdtseq_tab_text))).click()
        print("Opened product sequence tab")
        wait(5)

    def open_categoryseq_tab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.catseq_tab_text))).click()
        print("Opened category sequence tab")
        wait(5)
    
    def csv_upload_reload_save(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.close_reload_btn_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_btn_text))).click()
        wait(5)
        print("Uploaded csv")

    def update_product_seq(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        env = self.e.get('environment')
        if env == "dev":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_update_dev_pdtseq_sample_csv_path"))
        elif env == "qa":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_update_qa_pdtseq_sample_csv_path"))
        elif env == "test":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_update_test_pdtseq_sample_csv_path"))
        else:
            print("Invalid product sequence case")
        self.csv_upload_reload_save()
        print("Updated sequences under products sequence tab")

    def update_category_seq(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        # env = self.e.get('environment')
        # if env == "qa":
        #     self.driver.find_element(By.NAME, constant.selectfilecat_name).send_keys(constant.ca_update_catseq_sample_csv_path)
        # elif env == "dev":
        #     pass
        # elif env == "test":
        #     pass
        # else:
        #     pass
        self.driver.find_element(By.NAME, constant.selectfilecat_name).send_keys(
            Config.get_path("ca_update_catseq_sample_csv_path"))
        self.csv_upload_reload_save()
        print("Updated sequences under category sequence tab")

    def remove_category_seq(self):
        wait(8)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        self.driver.find_element(By.NAME, constant.selectfilecat_name).send_keys(Config.get_path("ca_remove_catseq_sample_csv_path"))
        self.csv_upload_reload_save()
        print("Remove sequences under category sequence tab")

    def remove_product_seq(self):
        wait(8)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        env = self.e.get('environment')
        if env == "dev":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_remove_dev_pdtseq_sample_csv_path"))
        elif env == "qa":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_remove_qa_pdtseq_sample_csv_path"))
        elif env == "test":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_remove_test_pdtseq_sample_csv_path"))
        else:
            print("Invalid product sequence remove case")
        self.csv_upload_reload_save()
        print("Removed sequences under product sequence tab")

    def add_category_seq_details(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.catseq_tab_text))).click()
        wait(5)
        print("Opened Category Sequence tab")
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        # env = self.e.get('environment')
        # if env == "qa":
        #     self.driver.find_element(By.NAME, constant.selectfilecat_name).send_keys(
        #         Config.get_path("ca_add_catseq_sample_csv_path"))
        # elif env == "dev":
        #     self.driver.find_element(By.NAME, constant.selectfilecat_name).send_keys(
        #         Config.get_path("ca_add_dev_catseq_sample_csv_path"))
        # elif env == "test":
        #     pass
        # else:
        #     pass
        self.driver.find_element(By.NAME, constant.selectfilecat_name).send_keys(Config.get_path("ca_add_catseq_sample_csv_path"))
        self.csv_upload_reload_save()
        print("Filled fields under category sequence tab")

    def add_product_seq_details(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.pdtseq_tab_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        env = self.e.get('environment')
        if env == "dev":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_add_dev_pdtseq_sample_csv_path"))
        elif env == "qa":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_add_qa_pdtseq_sample_csv_path"))
        elif env == "test":
            self.driver.find_element(By.NAME, constant.selectfilepdt_name).send_keys(
                Config.get_path("ca_add_test_pdtseq_sample_csv_path"))
        else:
            print("Invalid product sequence add case")
        self.csv_upload_reload_save()
        print("Filled fields under product sequence tab")

    def add_pdt_substitution_details(self, OFF=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.subs_tab_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.managesubs_btn_text))).click()
        wait(5)
        cat_map = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.aircat_map_xpath)))
        action.double_click(cat_map).perform()
        if OFF:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.aircat_dropdown_update_xpath))).click()
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.aircat_dropdown_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.aircat_map_subs_value_xpath))).click()
        wait(5)
        subs1_map = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs1_xpath)))
        action.double_click(subs1_map).perform()
        if OFF:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.subs1_dropdown_update_xpath))).click()
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.subs1_dropdown_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs1_value_xpath))).click()
        wait(5)
        subs2_map = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs2_xpath)))
        action.double_click(subs2_map).perform()
        if OFF:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.subs2_dropdown_update_xpath))).click()
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.subs2_dropdown_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs2_value_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.submit_btn_text))).click()
        wait(10)
        print("Filled fields under product substitutions tab")

    def map_5level_airline_category(self, pac=None, airline=None, pac_delta=None, delta_off=None):
        if pac:
            # map_click=self.driver.find_element(By.XPATH, "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[14]")
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[14]"))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[14]//input"))).send_keys(
                "Map Airline Categories", Keys.ENTER)
            # map_click.send_keys(Keys.ENTER)
        elif airline:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airline_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airline_box_xpath_with_input))).send_keys(
                "Map Airline Categories", Keys.ENTER)
        elif pac_delta:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_delta_pac_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_delta_pac_xpath_with_input))).send_keys(
                "Map Airline Categories", Keys.ENTER)
        elif delta_off:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_delta_pac_off_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_delta_pac_off_xpath_with_input))).send_keys(
                "Map Airline Categories", Keys.ENTER)
        else:
            print("Invalid")
        wait(5)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.map_option_xpath))).click()
        # try:
        #     self.wait.until(
        #         EC.element_to_be_clickable((By.XPATH, constant.map_option_xpath))).click()
        # except NoSuchElementException:
        #     pass
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        wait(5)
        env = self.e.get('environment')
        if env == "dev":
            self.driver.find_element(By.NAME, constant.selectfile_name).send_keys(Config.get_path("ca_add_dev_mapaircat_csv_path"))
        elif env == "qa":
            self.driver.find_element(By.NAME, constant.selectfile_name).send_keys(Config.get_path("ca_add_qa_mapaircat_csv_path"))
        elif env == "test":
            self.driver.find_element(By.NAME, constant.selectfile_name).send_keys(Config.get_path("ca_add_test_mapaircat_csv_path"))
        else:
            pass
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.close_reload_btn_text))).click()
        wait(5)
        print("Filled fields under products tab")

    def check_dropdown_fields(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.choose_map_pac_xpath))).click()
        self.wait.until(
            EC.presence_of_element_located((By.XPATH, constant.dropdown_click_xpath)))
        options = self.driver.find_elements(By.XPATH, "//ul[contains(@id,'-picker-listEl')]/li")
        option_texts = [option.text.strip() for option in options]
        required_fields = ["Map Airline Categories", "Load Updated Catalog", "Update Deployment Inventory",
                           "Export Deployment Inventory CSV", "Import Deployment Inventory CSV"]
        missing_fields = [field for field in required_fields if field not in option_texts]
        if not missing_fields:
            print("All required fields are present in the dropdown.")
        else:
            print(f"Missing fields: {', '.join(missing_fields)}")

    def open_update_deployment_inventory(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.choose_map_box_xpath_with_input))).send_keys(
            "Update Deployment Inventory", Keys.ENTER)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.update_inventory_option_xpath))).click()
        print("Clicked on update inventory option")
        wait(8)

    # adding values not working
    def update_inventory_values(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH,constant.deployment_inventory1_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH,constant.deployment_inventory1_xpath))).send_keys(generate_random_integer(2),Keys.ENTER)
        wait(2)
        # same as above tried in different manner
        # try:
        #     element = WebDriverWait(self.driver, 10).until(
        #         EC.element_to_be_clickable((By.XPATH, constant.deployment_inventory1_xpath))
        #     )
        #     print("Element is clickable.")
        #     element.click()
        #     print("Element clicked.")
        #     time.sleep(2)
        #     input_element = self.driver.find_element(By.XPATH, "/html/body/div[1]/div/div/div[3]/div/div[2]/div[3]/div/div/div/div/fieldset/div/div/div/div/div/div/div/div/div/div[2]/div[2]/div/div[1]/table[1]/tbody/tr/td[3]/div[1]")
        #     input_element.clear()
        #     input_element.send_keys("1")
        #     input_element.send_keys(Keys.ENTER)
        #     print("Value entered and ENTER pressed.")
        # except Exception as e:
        #     print(f"Error occurred: {e}")

        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.deployment_inventory2_xpath))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.deployment_inventory2_xpath))).send_keys("2",
                                                                                                                Keys.ENTER)
        wait(2)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.deployment_inventory3_xpath))).send_keys(3,
                                                                                                                Keys.ENTER)
        wait(2)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.deployment_inventory4_xpath))).send_keys(generate_random_integer(2),
                                                                                                                Keys.ENTER)
        wait(2)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.deployment_inventory5_xpath))).send_keys(generate_random_integer(2),
                                                                                                                Keys.ENTER)
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.submit_btn_text))).click()
        print("Updated values under inventory tab")
        wait(5)

    def update_inventory_values_through_csv(self, ca_id=None):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.choose_map_box_xpath_with_input))).send_keys(
            "Import Deployment Inventory CSV", Keys.ENTER)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.csv_update_inventory_xpath))).click()
        wait(8)
        csv_file = Config.get_path("ca_inventory_csv_xpath")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][2] = data[2][2] = data[3][2] = data[4][2] = data[5][2] = ca_id.split(' ')[1]
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        # df = pd.read_csv(constant.ca_inventory_csv_xpath)
        # caforpdt_1 = df['CatalogAssignmentId'].tolist()
        # caforpdt_2 = df['CatalogAssignmentId'].tolist()
        # caforpdt_3 = df['CatalogAssignmentId'].tolist()
        # caforpdt_4 = df['CatalogAssignmentId'].tolist()
        # caforpdt_5 = df['CatalogAssignmentId'].tolist()
        # print("\n")
        # print("ca1 in csv: " + str(caforpdt_1[0]))
        # print("ca2 in csv: " + str(caforpdt_2[0]))
        # print("ca3 in csv: " + str(caforpdt_3[0]))
        # print("ca4 in csv: " + str(caforpdt_4[0]))
        # print("ca5 in csv: " + str(caforpdt_5[0]))
        self.driver.find_element(By.NAME, constant.selectinventoryfile_name).send_keys(Config.get_path("ca_inventory_csv_xpath"))
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.close_reload_btn_text))).click()
        print("Updated inventory values through csv")
        wait(5)

    def delete_pdt_from_catalog(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.delete_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.yes_btn_text))).click()
        print("Deleted a product from products tab")

    def perform_action_airlinecategory_mapped_state(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.actions_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.airlineCategoryMap_action_text))).click()
        print("Performed airlinecategory mapped action")
        wait(10)

    def perform_action_edit_airlinecategory_state(self):
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.actions_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.editairlinecat_text))).click()
        print("Performed edit airlinecategory action")
        wait(10)

    def pac_open_catalog_to_update(self, airline=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_expand_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_folder_xpath_pac))).click()
        wait(8)
        env = self.e.get('environment')
        if env == "dev":
            if airline:
                self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, constant.search_xpath_ca))).send_keys(
                constant.catalog_delta_dev_value)
            else:
                self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, constant.search_catalog_pdt_xpath))).send_keys(
                    constant.catalog_delta_dev_value)
        elif env == "qa":
            if airline:
                self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, constant.search_xpath_ca))).send_keys(
                constant.catalog_delta_qa_value)
            else:
                self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, constant.search_catalog_pdt_xpath))).send_keys(
                    constant.catalog_delta_qa_value)
        elif env == "test":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_catalog_pdt_xpath))).send_keys(
                constant.catalog_delta_test_value)
        else:
            print("Invalid catalog value")
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        catalog_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_delta_value_path)))
        action.context_click(catalog_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened catalog object to update - Catalog from UI")

    def update_products(self,add=None,airline=None):
        global product_id, env_dev, env_qa, env_test, productIDs
        env_dev = False
        env_qa = False
        env_test = False
        env = self.e.get('environment')
        if env == "dev":
            env_dev = True
        elif env == "qa":
            env_qa = True
        elif env == "test":
            env_test = True
        else:
            print("Invalid env")
        if env_dev:
            productIDs = ["69189784", "69189790", "69189791", "69189792", "69189793"]
        elif env_qa:
            productIDs = ["217091", "217097","217098","217099","217100"]
        elif env_test:
            productIDs = ["86909713","86909719","86909720","86909721","86909722"]
        else:
            print("Invalid products set")
        action = ActionChains(self.driver)
        product_page = self.driver.find_element(By.XPATH,
                                           "//div[contains(@class,'x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        # product_page.find_element(By.XPATH, constant.catalog_delta_search).click()
        # below for automapoff, autoapproveoff case
        if add:
            product_page.find_element(By.XPATH,
                                  "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search ')])[2]").click()
            wait(2)
            product_pop_up = self.driver.find_element(By.XPATH,
                                                      "(//div[contains(@class,'x-panel x-border-item x-box-item x-panel-default')])[6]")
        else:
            product_page.find_element(By.XPATH,
                                      "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search ')])[1]").click()
            wait(2)
            product_pop_up = self.driver.find_element(By.XPATH,
                                                 "//div[contains(@class,'x-panel x-border-item x-box-item x-panel-default')]")
        if airline:
            text_box = product_pop_up.find_element(By.XPATH, "(//input[contains(@name,'query')])[3]")
        else:
            text_box = product_pop_up.find_element(By.XPATH, "(//input[contains(@name,'query')])[2]")
        # text_box = product_pop_up.find_element(By.XPATH, constant.search_catalog_pdt_xpath)
        # below for automapoff, autoapproveoff case
        self.driver.execute_script("arguments[0].scrollIntoView();", text_box)
        for i in productIDs:
            id = str(i)
            text_box.send_keys(id)
            text_box.send_keys(Keys.ENTER)
            wait(3)
            product_id = product_pop_up.find_element(By.XPATH, f"//table[contains(@id,'patchedgridview')]//div[contains(text(),'{id}')]")
            action.double_click(product_id).perform()
            text_box.clear()
            sleep(0.2)
        wait(2)
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.select_btn_text))).click()
        print("Filled fields under products tab")

    def delta_update_changes(self, airline_delta_update=None, delta=None, delta_off=None):
        if airline_delta_update:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airlineadmin_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airlineadmin_xpath_with_input))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        elif delta:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airlineoff_xpath))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_airlineoff_xpath_with_input))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        elif delta_off:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-form-trigger x-form-trigger-default x-form-arrow-trigger x-form-arrow-trigger-default ')])[1]"))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-form-trigger x-form-trigger-default x-form-arrow-trigger x-form-arrow-trigger-default ')])[1]//input"))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_xpath_after_update))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.choose_map_xpath_after_update_with_input))).send_keys(
                "Load Updated Catalog", Keys.ENTER)
        # if delta_off_case:
        #     self.wait.until(
        #         EC.element_to_be_clickable((By.XPATH, constant.choose_map_xpath_after_update))).click()
        #     self.wait.until(
        #         EC.element_to_be_clickable((By.XPATH, constant.choose_map_xpath_after_update_with_input))).send_keys(
        #         "Load Updated Catalog", Keys.ENTER)
        # else:
        #     self.wait.until(
        #         EC.element_to_be_clickable((By.XPATH, constant.choose_map_pac_xpath))).click()
        #     self.wait.until(
        #         EC.element_to_be_clickable((By.XPATH, constant.choose_map_pac_box_xpath_with_input))).send_keys(
        #         "Load Updated Catalog", Keys.ENTER)
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.delta_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.updatecatalog_chkbox))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.updatecatalog_text))).click()
        print("Updated the catalog changes")
        wait(10)

    def open_pdt_substitution_tab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.subs_tab_text))).click()
        print("Opened product substitution tab")
        wait(5)

    def change_values_in_substitutions(self, airline=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.managesubs_btn_text))).click()
        wait(8)
        subs1_map = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs1_xpath)))
        action.double_click(subs1_map).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs1_dropdown_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs2_update_value_xpath))).click()
        wait(5)
        subs2_map = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs2_xpath)))
        action.double_click(subs2_map).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs2_dropdown_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs1_update_value_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.submit_btn_text))).click()
        print("Made changes to values in fields under substitutions tab")
        wait(10)

    def update_substitutions(self, airline=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.managesubs_btn_text))).click()
        wait(5)
        cat_map = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.aircat_map_xpath)))
        action.double_click(cat_map).perform()
        if airline:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.aircat_airline_dropdown_update_xpath))).click()
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.aircat_dropdown_update_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.aircat_map_subs_value_xpath))).click()
        wait(5)
        subs1_map = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs1_xpath)))
        action.double_click(subs1_map).perform()
        if airline:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.subs1_airline_dropdown_update_xpath))).click()
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.subs1_dropdown_update_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs1_value_xpath))).click()
        wait(5)
        subs2_map = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs2_xpath)))
        action.double_click(subs2_map).perform()
        if airline:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.subs2_airline_dropdown_update_xpath))).click()
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.subs2_dropdown_update_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.subs2_value_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.submit_btn_text))).click()
        print("Updated fields under substitutions tab")
        wait(10)
        
    def remove_substitutions(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.managesubs_btn_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.del_subs_xpath))).click()
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.submit_btn_text))).click()
        print("Removed fields under substitutions tab")
        wait(10)

    def get_data(self):
        self.data['id'] = catalog_assignment_to_check
        # self.data['associateCatalog'] = "catalog"
        self.data['fromDate'] = constant.current_date
        self.data['toDate'] = constant.next_date_str
        self.data['cabinClass'] = constant.cabincls_value
        print("Data taken from ui for comparison")
        return self.data

    def compare_CatalogAssignment_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n", dict2)

        # ui data - dict1
        id1 = dict1['id'].split(' ')[1]
        # associateCatalog1 = dict1['associateCatalog']
        fromDate1 = dict1['fromDate']
        toDate1 = dict1['toDate']

        # aws-sns data - dict2
        id2 = dict2['id']
        # associateCatalog2 = dict2['associateCatalog']['name']['en']
        fromDate2 = dict2['fromDate']
        toDate2 = dict2['toDate']
        cabinClass2 = dict2['cabinClass'][0]['name']['en']

        # comparing dict1 and dict2 values
        comparisons = {
            "id": id1 == str(id2),
            # "associateCatalog": associateCatalog1 == associateCatalog2, - giving error
            "fromDate": fromDate1 == fromDate2,
            "toDate": toDate1 == toDate2
            # "cabinClass": cabinClass1 == cabinClass2
        }
        print("\nComparisons -",comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
        else:
            print("Comparison succeeded.")
        return all_match

###########################################################################################################################
    def get_products(self, before_deltachange=None, after_deltachange=None):
        self.open_products_tab()
        products_list = []
        if before_deltachange:
            element = self.driver.find_element(By.XPATH, constant.pdt_value1_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            element = self.driver.find_element(By.XPATH, constant.pdt_value2_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            element = self.driver.find_element(By.XPATH, constant.pdt_value3_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            element = self.driver.find_element(By.XPATH, constant.pdt_value4_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            list_beforechange = products_list
            print("List of products taken - before delta change")
            return list_beforechange
        elif after_deltachange:
            element = self.driver.find_element(By.XPATH, constant.pdt_value1_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            element = self.driver.find_element(By.XPATH, constant.pdt_value2_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            element = self.driver.find_element(By.XPATH, constant.pdt_value3_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            element = self.driver.find_element(By.XPATH, constant.pdt_value4_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            element = self.driver.find_element(By.XPATH, constant.pdt_value5_aircat5lvl_dev_xpath)
            products_list.append(element.text)
            list_afterchange = products_list
            print("List of products taken - after delta change")
            return list_afterchange
        else:
            print("Invalid state")

    def close_filter(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.filter_close_xpath))).click()
        wait(3)

    def get_ui_data(self, catalog=None, categoryseq=None, productseq=None, pdt_ctgry_seq=None, del_pdt=None):
        self.data['id'] = catalog_assignment_to_check
        # self.data['associateCatalog'] = catalog
        self.data['fromDate'] = constant.current_date
        self.data['toDate'] = constant.next_date_str
        self.open_products_tab()
        product_airlinecategory_mapping = []
        element = self.driver.find_element(By.XPATH,constant.pdt_value1_aircat5lvl_dev_xpath)
        product1_id = element.text
        element = self.driver.find_element(By.XPATH,constant.aircat_value1_5lvl_dev_xpath).text
        pdt1_mapped_airline_category = element.split('-')[0]
        product_airlinecategory_mapping.append({
            'productId': product1_id,
            'mappedAirlineCategory': pdt1_mapped_airline_category
        })
        element = self.driver.find_element(By.XPATH, constant.pdt_value2_aircat5lvl_dev_xpath)
        product2_id = element.text
        element = self.driver.find_element(By.XPATH, constant.aircat_value2_5lvl_dev_xpath).text
        pdt2_mapped_airline_category = element.split('-')[0]
        product_airlinecategory_mapping.append({
            'productId': product2_id,
            'mappedAirlineCategory': pdt2_mapped_airline_category
        })
        element = self.driver.find_element(By.XPATH, constant.pdt_value3_aircat5lvl_dev_xpath)
        product3_id = element.text
        element = self.driver.find_element(By.XPATH, constant.aircat_value3_5lvl_dev_xpath).text
        pdt3_mapped_airline_category = element.split('-')[0]
        product_airlinecategory_mapping.append({
            'productId': product3_id,
            'mappedAirlineCategory': pdt3_mapped_airline_category
        })
        element = self.driver.find_element(By.XPATH, constant.pdt_value4_aircat5lvl_dev_xpath)
        product4_id = element.text
        element = self.driver.find_element(By.XPATH, constant.aircat_value4_5lvl_dev_xpath).text
        pdt4_mapped_airline_category = element.split('-')[0]
        product_airlinecategory_mapping.append({
            'productId': product4_id,
            'mappedAirlineCategory': pdt4_mapped_airline_category
        })
        if del_pdt:
            pass
        else:
            element = self.driver.find_element(By.XPATH, constant.pdt_value5_aircat5lvl_dev_xpath)
            product5_id = element.text
            element = self.driver.find_element(By.XPATH, constant.aircat_value5_5lvl_dev_xpath).text
            pdt5_mapped_airline_category = element.split('-')[0]
            product_airlinecategory_mapping.append({
                'productId': product5_id,
                'mappedAirlineCategory': pdt5_mapped_airline_category
            })
        self.data['product_airlinecategory_mapping'] = product_airlinecategory_mapping
        print("product_airlinecategory_mapping - ",self.data['product_airlinecategory_mapping'])
        if productseq:
            self.open_productseq_tab()
            try:
                element = self.driver.find_element(By.XPATH,"(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn-2085 x-grid-cell-last x-unselectable')])[1]")
                element_text = element.text.strip()
                if not element_text:
                    self.data['product1_sequence'] = 0
                else:
                    self.data['product1_sequence'] = element_text
            except NoSuchElementException:
                self.data['product1_sequence'] = 0
            try:
                element = self.driver.find_element(By.XPATH,"(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn-2085 x-grid-cell-last x-unselectable')])[2]")
                element_text = element.text.strip()
                if not element_text:
                    self.data['product2_sequence'] = 0
                else:
                    self.data['product2_sequence'] = element_text
            except NoSuchElementException:
                self.data['product2_sequence'] = 0
            print("Product 1 Sequence:", self.data['product1_sequence'])
            print("Product 2 Sequence:", self.data['product2_sequence'])
        elif categoryseq:
            self.open_categoryseq_tab()
        elif pdt_ctgry_seq:
            pass
        else:
            pass

        print("Data taken from ui for comparison")
        return self.data

    def compare_CatalogAssignment_all_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        # print("Data taken from AWS SNS: \n", dict2)

        # ui data - dict1
        id1 = dict1['id'].split(' ')[1]
        # associateCatalog1 = dict1['associateCatalog']
        fromDate1 = dict1['fromDate']
        toDate1 = dict1['toDate']
        pdt1_id1 = dict1['product_airlinecategory_mapping'][0]['productId']
        pdt2_id1 = dict1['product_airlinecategory_mapping'][1]['productId']
        pdt1_mappedcategory1 = dict1['product_airlinecategory_mapping'][0]['mappedAirlineCategory']
        pdt2_mappedcategory1 = dict1['product_airlinecategory_mapping'][1]['mappedAirlineCategory']
        pdt1_pdtseq1 = dict1['product1_sequence']
        pdt2_pdtseq1 = dict1['product2_sequence']
        # add category sequence also when bug is fixed

        # aws-sns data - dict2
        id2 = dict2['id']
        # associateCatalog2 = dict2['associateCatalog']['name']['en']
        fromDate2 = dict2['fromDate']
        toDate2 = dict2['toDate']
        product_airlinecategory_info = []
        for product in dict2['products']:
            product_info = {
                'product_id': product['id'],
                'sku': product['sku'],
                'airlineCategory': product['airlineCategory']
            }
            product_airlinecategory_info.append(product_info)
        print("Stored Product and Airline Category Info:")
        print("product_airlinecategory_info - ",product_airlinecategory_info)
        pdt1_id2 = product_airlinecategory_info[0]['product_id']
        pdt2_id2 = product_airlinecategory_info[1]['product_id']
        pdt1_mappedcategory2 = product_airlinecategory_info[0]['airlineCategory'][0]
        pdt2_mappedcategory2 = product_airlinecategory_info[1]['airlineCategory'][0]
        def extract_products(dict2):
            products = []
            def recursive_extract(categories):
                for category in categories:
                    if 'subCategories' in category:
                        recursive_extract(category['subCategories'])
                    if 'products' in category:
                        for product in category['products']:
                            products.append({
                                'productId': product['productId'],
                                'sequenceNumber': product['sequenceNumber']
                            })
            recursive_extract(dict2['routeSequences']['categories'])
            return products
        product_sequence_data = extract_products(dict2)
        print("product_sequence_data - ",product_sequence_data)
        pdt1_pdtseq2 = product_sequence_data[0]['sequenceNumber']
        pdt2_pdtseq2 = product_sequence_data[1]['sequenceNumber']
        print("Data taken from AWS SNS: \n", {
            "id2": id2,
            # "associateCatalog2": associateCatalog2,
            "fromDate2": fromDate2,
            "toDate2": toDate2,
            "pdt1_id2": pdt1_id2,
            "pdt2_id2": pdt2_id2,
            "pdt1_mappedcategory2": pdt1_mappedcategory2,
            "pdt2_mappedcategory2": pdt2_mappedcategory2,
            "pdt1_pdtseq2": pdt1_pdtseq2,
            "pdt2_pdtseq2": pdt2_pdtseq2
        })

        # comparing dict1 and dict2 values
        comparisons = {
            "id": id1 == str(id2),
            # "associateCatalog": associateCatalog1 == associateCatalog2,
            "fromDate": fromDate1 == fromDate2,
            "toDate": toDate1 == toDate2,
            "productid_1": pdt1_id1 == str(pdt1_id2),
            "productid_2": pdt2_id1 == str(pdt2_id2),
            "product_mappedcategory1": pdt1_mappedcategory1 == str(pdt1_mappedcategory2),
            "product_mappedcategory2": pdt2_mappedcategory1 == str(pdt2_mappedcategory2),
            "product_sequence1": pdt1_pdtseq1 == pdt1_pdtseq2,
            "product_sequence2": pdt2_pdtseq1 == pdt2_pdtseq2,
        }
        print("\nComparisons -",comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
        else:
            print("Comparison succeeded.")
        return all_match