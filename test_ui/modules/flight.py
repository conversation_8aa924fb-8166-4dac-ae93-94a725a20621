from datetime import datetime
import pytz, pandas as pd, pytest
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from project_configs.login_helpers import wait, generate_random_string
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class flight(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {
                'sectors': []
            }

    def open_latest_flight(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.flight_folder_xpath))).click()
        print("Opened flight folder")

    def open_latest_flight_airline(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.flight_folder_xpath))).click()
        print("Opened flight folder")
        wait(10)

    def flight_ui_update(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.flight_routeno_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.flight_routeno_name))).send_keys(
            constant.flight_name + "_Updated")
        print("Flight ui has been updated")
        wait(5)

    def upload_flight_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.flight_text)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("flight_pac_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new flight")
        # wait(25)
        wait(15)

    def upload_airline_flight_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.flight_text)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("flight_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new flight")
        wait(25)

    def verify_flight_csv_uploaded(self):
        df = pd.read_csv(Config.get_path("flight_pac_sample_csv_path"))
        flightRouteNumber_ui = df['flightRouteNumber'].tolist()
        print("Reading csv file uploaded - flightRouteNumber in csv: " + (flightRouteNumber_ui[0]).lower())
        field_value = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.flight_routeno_name)))
        flightRouteNumber = field_value.get_attribute("value")
        if (flightRouteNumber.lower() == (flightRouteNumber_ui[0]).lower()):
            print("Latest Flight is same as uploaded from CSV!")
        else:
            pytest.fail("Flight names dont match between UI and CSV, Import failed.")
            print("Flight names dont match between UI and CSV, Import failed.")

    def verify_flight_csv_uploaded_after_update(self):
        df = pd.read_csv(Config.get_path("flight_pac_sample_csv_path"))
        sectorsName_ui = df['sectorsName'].tolist()
        print("Reading csv file uploaded - sectorsName in csv: " + (sectorsName_ui[0]).lower())
        field_value = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.sector_seq_name)))
        sector_sequence = field_value.get_attribute("value")
        if (sector_sequence.lower() in (sectorsName_ui[0]).lower()):
            print("Latest Flight is same as uploaded from CSV!")
        else:
            pytest.fail("Flight names dont match between UI and CSV, Import failed.")
            print("Flight names dont match between UI and CSV, Import failed.")
        print("Sequences verified")

    def enter_flight_information(self, flightRouteNumber=constant.flight_name, begin_date=constant.current_date,
                                 begin_time=constant.current_time, arrival_date=constant.next_date_str,
                                 arrival_time=constant.next_minute_str):  # adding values under basedata
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.flight_routeno_name))).send_keys(flightRouteNumber + generate_random_string(2))
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.begin_date_xpath))).send_keys(begin_date)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.begin_time_xpath))).send_keys(begin_time)
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.end_date_xpath))).send_keys(arrival_date)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.end_time_xpath))).send_keys(arrival_time)
        print("Entered data for flight")

    def add_sector(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.sectorinflight_add_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.sectorfield_name))).send_keys(constant.sector_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.sector_seq_name))).send_keys(constant.sequence_value)
        print("Added sector")

    def get_flight_id(self):
        global flight_to_check
        flight_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.flight_id_xpath))).text
        print("Object id taken to check with sns in super admin logs - " + flight_to_check)
        return flight_to_check

    def select_airline(self, airline_name=constant.airline_value):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        airline = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_select_xpath)))
        action.double_click(airline).perform()
        print("Selected airline for flight object")

    def create_flight(self, name=constant.flight_name):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.flight_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_category_id))).send_keys(name)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Flight from UI")
        return name

    def __getattr__(self, name):
        if name in self.data:
            return self.data[name]
        raise AttributeError(f"'{self.__class__.__name__}' Object has no attribute '{name}")

    def get_data(self):
        self.data['id'] = flight_to_check
        wait(10)
        self.data['flightRouteNumber'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.flight_routeno_name))).get_attribute("value")
        flightBeginDate_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.begin_date_xpath)))
        field_value1 = flightBeginDate_to_check.get_attribute("value")
        flightBeginTime_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.begin_time_xpath)))
        field_value2 = flightBeginTime_to_check.get_attribute("value")
        flightEndDate_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.end_date_xpath)))
        field_value3 = flightEndDate_to_check.get_attribute("value")
        flightEndTime_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.end_time_xpath)))
        field_value4 = flightEndTime_to_check.get_attribute("value")
        self.data['flightBeginDateTime'] = field_value1 + " " + field_value2
        self.data['flightEndDateTime'] = field_value3 + " " + field_value4
        self.data['sectors'].append({})
        self.data['sectors'][-1]['sectorName'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.sectorfield_name))).get_attribute("value")
        self.data['sectors'][-1]['sequence'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.sector_seq_name))).get_attribute("value")
        print("Flight UI data: \n", self.data)
        return self.data

    def compare_flight_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n", dict2)

        # UI Data
        id1 = dict1.get('id', '').split(' ')[1] if dict1.get('id') else None
        flight_route_number1 = dict1.get('flightRouteNumber')
        sector1 = dict1.get('sectors', [{}])[0]
        sector_name1 = sector1.get('sectorName')
        sequence1 = str(sector1.get('sequence'))

        # SNS Data
        flight_info = dict2.get('flights', [{}])[0]
        id2 = flight_info.get('internalId')
        flight_route_number2 = flight_info.get('flightRouteNumber')
        sector2 = flight_info.get('sectors', [{}])[0]
        sector_name2 = sector2.get('sectorName')
        sequence2 = str(sector2.get('sequenceNumber'))

        # Timezone setup
        ist = pytz.timezone('Asia/Kolkata')
        utc = pytz.utc

        # Extract & Convert times
        try:
            flight_begin_ui_dt = ist.localize(
                datetime.strptime(dict1.get('flightBeginDateTime'), '%Y-%m-%d %H:%M')).astimezone(utc)
            flight_end_ui_dt = ist.localize(
                datetime.strptime(dict1.get('flightEndDateTime'), '%Y-%m-%d %H:%M')).astimezone(utc)
            flight_begin_sns_dt = utc.localize(
                datetime.strptime(flight_info.get('flightBeginDateTime'), '%Y-%m-%d %H:%M'))
            flight_end_sns_dt = utc.localize(datetime.strptime(flight_info.get('flightEndDateTime'), '%Y-%m-%d %H:%M'))
        except Exception as e:
            print(f"Time parsing error: {e}")
            return False

        # Print times
        print(f"Flight begin time UI: {flight_begin_ui_dt}")
        print(f"Flight begin time SNS: {flight_begin_sns_dt}")
        print(f"Flight end time UI: {flight_end_ui_dt}")
        print(f"Flight end time SNS: {flight_end_sns_dt}")

        # Compare values
        comparisons = {
            "id": id1 == id2,
            "flight_route_number": flight_route_number1 == flight_route_number2,
            "sector_name": sector_name1 == sector_name2,
            "sequence_value": sequence1 == sequence2,
            "begin_times_match": flight_begin_ui_dt == flight_begin_sns_dt,
            "end_times_match": flight_end_ui_dt == flight_end_sns_dt
        }

        print("\nComparisons:", comparisons)
        all_match = all(comparisons.values())

        if not all_match:
            print("Comparison failed for the following:")
            for key, match in comparisons.items():
                if not match:
                    print(f"  - {key} did not match.")
            return False
        else:
            print("Comparison succeeded.")
            return True

    def compare_flight_unpublished_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n", dict2)

        # ui data - dict1
        id1 = dict1['id'].split(' ')[1]

        # aws-sns data - dict2
        # flight_info = dict2['flights'][0]
        # id2 = flight_info['internalId']
        id2 = dict2['id']

        # comparing dict1 and dict2 values
        comparisons = {
            "id": id1 == str(id2)
        }
        print("\nComparisons -",comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
        else:
            print("Comparison succeeded.")

        return all_match