from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.login_helpers import wait
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class roottype(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {}

    def get_id(self, unpublish=None):
        if unpublish:
            id_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(text(),'ID')])[2]"))).text
        else:
            id_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'ID')]"))).text
        print("Object id taken to check with sns in super admin logs " + id_to_check)
        return id_to_check

    def add_roottype(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.roottype_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_aircraft_id))).send_keys(constant.roottype_value_add)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - RootType from UI")

    def add_basedata(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.roottype_xpath))).send_keys(constant.roottype_value_add)
        action = ActionChains(self.driver)
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_search_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.airline_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        select_airline = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_select_xpath)))
        action.double_click(select_airline).perform()
        print("Added details under basedata")
            
    def open_latest_roottype(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.master_data_text)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.masterdata_folder_expander_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.roottype_expand_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.roottype_filter_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_filter_store_send_xpath))).send_keys(
            constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
        # self.wait.until(
        #     EC.element_to_be_clickable((By.LINK_TEXT, constant.airline_value))).click()
        print("Opened airline folder")
