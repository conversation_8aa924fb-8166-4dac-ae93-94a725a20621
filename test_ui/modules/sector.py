import pandas as pd, pytest
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from project_configs.login_helpers import wait
from selenium.webdriver import Keys, ActionChains
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class sector(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }

    def get_id(self, add=None):
        if add:
            id_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'ID')]"))).text
        else:
            id_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(text(),'ID')])[2]"))).text
        print("Object id taken to check with sns in super admin logs " + id_to_check)
        return id_to_check

    def upload_sector_csv(self):
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.assignmenttype_value_S)
        wait(3)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("sector_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new sector")
        wait(15)

    def get_latest_sector(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.sector_folder_xpath))).click()
        click_column = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_xpath)))
        action.double_click(click_column).perform()
        wait(5)
        sector_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_id_xpath)))
        action.context_click(sector_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened latest sector")

    def read_and_verify_sector_name_in_csv(self):
        sector_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.sector_name_name)))
        field_value = sector_to_check.get_attribute("value")
        print("Object value taken to check with uploaded csv file value as no sns feature for sector - sector name: " + field_value)
        df = pd.read_csv(Config.get_path("sector_sample_csv_path"))
        name = df['sectorName_en'].tolist()
        print("Reading csv file uploaded - sector name in csv: " + (name[0]).lower())
        if (field_value.lower() == (name[0]).lower()):
            print("Sector has been created successfully through CSV and verified!")
        else:
            pytest.fail("Sector names dont match between UI and CSV, Import failed.")
            print("Sector names dont match between UI and CSV, Import failed.")
        print("Names verified")

    def read_and_verfy_sector_summary_in_csv(self):
        sector_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.summary_name)))
        field_value = sector_to_check.get_attribute("value")
        print("Object value taken to check with uploaded csv file value as no sns feature for sector - sector summary: " + field_value)
        df = pd.read_csv(Config.get_path("sector_sample_csv_path"))
        summary = df['summary_en'].tolist()
        print("Reading csv file uploaded - sector name in csv: " + (summary[0]).lower())
        if (field_value.lower() == (summary[0]).lower()):
            print("Sector has been created successfully through CSV and verified!")
        else:
            pytest.fail("Sector names dont match between UI and CSV, Import failed.")
            print("Sector names dont match between UI and CSV, Import failed.")
        print("Names verified")

    def add_sector_ui(self):
        action = ActionChains(self.driver)
        home = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.home_xpath)))
        action.context_click(home).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.assignmenttype_value_S))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_catalog_id))).send_keys(constant.sector_name)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Sector from UI")

    def fill_sector_details(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.sector_name_name))).send_keys(constant.sector_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.summary_name))).send_keys(constant.sector_name)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.rg_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.routegrp_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        select_rg = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.rg_click_xpath)))
        action.double_click(select_rg).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airport1_search_xpath))).click()
        select_airport1 = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airport1_select_xpath)))
        action.double_click(select_airport1).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airport2_search_xpath))).click()
        select_airport2 = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airport2_select_xpath)))
        action.double_click(select_airport2).perform()
        print("Sector has been created successfully through UI and verified!")
        wait(5)

    def select_airline(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_search_rg_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.airline_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        select_airline = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_select_xpath)))
        action.double_click(select_airline).perform()
        print("Selected airline")

    def update_sector(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.summary_name))).send_keys(constant.sector_name + "_Updated")
        print("Updated sector")
        wait(5)
