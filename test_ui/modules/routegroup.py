import pandas as pd, pytest
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from project_configs.login_helpers import wait
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class routegroup(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {}
            
    def upload_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.routegrp_text)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("routegrp_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - updated routegroup")
        wait(15)
        
    def open_latest_routgrp(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.routegroup_folder_xpath))).click()
        print("Opened routegroup folder")
    
    def get_rg_id(self):
        global field_value
        rg_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.rg_code_ui)))
        field_value = rg_to_check.get_attribute("value")
        print("Object id taken to check - Routegroup code: " + field_value)

    def verify_values(self):
        df = pd.read_csv(Config.get_path("routegrp_sample_csv_path"))
        code = df['code'].tolist()
        print("Reading csv file uploaded - routgroup code in csv: " + (code[0]).lower())
        if (field_value.lower() == (code[0]).lower()):
            print("Routegroup has been updated successfully through CSV and verified!")
        else:
            pytest.fail("Routegroup names dont match between UI and CSV, Import failed.")
            print("Routegroup names dont match between UI and CSV, Import failed.")
        print("Names verified")