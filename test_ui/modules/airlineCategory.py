from time import sleep

import pandas as pd
import pytest
from selenium.common import NoSuchElementException

from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import wait, automap_off, get_uitemp

class airlineCategory(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }

    def airline_automap_off(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_profile_xpath))).click()
        automap_off(self.driver)
        print("In airline profile - turned off autopopulate")

    def open_basedata(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.basedata_xpath))).click()
        print("Opened basedata")
        wait(3)

    def upload_airlinecategory_csv_pac(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.airlinecategory_text)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.airline_csvfield_name))).send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("airlinecategory_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new airline category")
        wait(15)

    def upload_airlinecategory_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.airlinecategory_text)
        action.send_keys(Keys.RETURN).perform()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("airlinecategory_sample_csv_path"))
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new airline category")
        wait(15)

    def open_latest_airlinecategory(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_management_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
        print("Opened category folder")

    def airlinecategory_id_check(self):
        global airlinecategory_to_check
        airlinecategory_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airlinecategory_id_xpath))).text
        print("Object id taken to check with sns in super admin logs " + airlinecategory_to_check)
        return airlinecategory_to_check

    def get_data(self):
        self.data['id'] = airlinecategory_to_check
        self.data['name'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).get_attribute("value")
        self.data['disclaimer'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name))).get_attribute("value")
        self.data['shortDescription'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.shortDescription_name))).get_attribute("value")
        self.data['description'] = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.description_name))).get_attribute("value")
        print("Data taken from UI")
        return self.data

    def navigate_and_create_airlinecategory_pac(self):
        wait(8)
        action = ActionChains(self.driver)
        home = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.home_xpath)))
        action.context_click(home).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.airline_folder_addobj_text))).click()
        action.send_keys(Keys.ARROW_RIGHT).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.airlinecategory_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_object_name_id))).send_keys(constant.airlinecategory_name)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Airline category through UI")

    def add_airlinecategory_ui(self):
        wait(8)
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_management_tab_xpath))).click()
        category_folder = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath)))
        action.context_click(category_folder).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        action.send_keys(Keys.ARROW_RIGHT).perform()
        action.send_keys(Keys.ARROW_RIGHT, Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_box_xpath))).send_keys(
            constant.airlinecategory_name)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.ok_btn_text))).click()
        print("Added new object - Airline category through UI")

    def airlinecategory_basedatatab(self):
        wait(8)
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).send_keys(constant.airlinecategory_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name))).send_keys(constant.airlinecategory_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.shortDescription_name))).send_keys(
            constant.airlinecategory_name)
        action.send_keys(Keys.PAGE_DOWN).perform()
        # self.wait.until(
        #     EC.element_to_be_clickable((By.XPATH, constant.description_box_aircat_xpath))).send_keys(
        #     constant.airlinecategory_name)
        self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.description_box_xpath))).send_keys(constant.airlinecategory_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.url_name))).send_keys(constant.airlinecategory_name)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.uitemp_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.search_input_name))).send_keys(constant.uitemp_created_value)
        action.send_keys(Keys.RETURN).perform()
        wait(8)
        get_uitemp(self.driver, self.e)
        print("Added basedata details")

    def airlinecategory_addroottype(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.roottype_name))).send_keys(constant.roottype_value)
        action.send_keys(Keys.RETURN).perform()
        print("Filled fields under basedata tab")

    def airlinecategory_choose_airline(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.airline_field_name))).send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        print("Airline chosen for airline category")
        wait(3)

    def airlinecategory_assettab(self):
        wait(5)
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.assets_tab_text))).click()
        # changed aircat xpath to normal xpath ones
        # self.wait.until(
        #     EC.element_to_be_clickable((By.XPATH, constant.img_1_search_aircat_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_1_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img1_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        # wait(25)
        select_file1 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file1).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_2_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_2_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img2_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        # wait(25)
        select_file2 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file2).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_3_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_3_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img3_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        # wait(25)
        select_file3 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file3).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_4_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_4_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img4_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        # wait(25)
        select_file4 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file4).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_5_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img5_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        # wait(25)
        select_file5 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file5).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_6_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img6_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        # wait(25)
        select_file6 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file6).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        print("Filled fields under assets tab")
        wait(3)

    def get_latest_airlinecategory(self):
        wait(8)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_management_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
        print("Opened category folder")
        wait(5)

    def update_airlinecategory(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name))).send_keys(
            constant.airlinecategory_name + "_updatedValue")
        print("Updated disclaimer value")

    def navigate_to_airlinecategory_folder_pac(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        wait(8)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_folder_xpath))).click()
        print("Opened category folder")
        wait(15)

    def search_category_by_name(self, name=None, obj_xpath=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(name)
        action.send_keys(Keys.RETURN).perform()
        obj_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, obj_xpath)))
        action.context_click(obj_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        sleep(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened category from folder in UI")

    def read_and_verify_airlinecategory_name_in_csv(self):
        airlinecategory_name_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name)))
        field_value = airlinecategory_name_to_check.get_attribute("value")
        print(
            "Object value taken to check with uploaded csv file value as no sns feature for airline category - airline category name: " + field_value.lower())
        df = pd.read_csv(Config.get_path("airlinecategory_sample_csv_path"))
        name = df['name_en'].tolist()
        print("Reading csv file uploade - category name in csv: " + (name[0]).lower())
        if (field_value.lower() == (name[0]).lower()):
            print("Airline Category has been created successfully through CSV and verified!")
        else:
            pytest.fail("Airline Category names dont match between CSV and UI, Import failed")
            print("Airline Category names dont match between CSV and UI, Import failed")
        print("Names verified")

    def compare_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n", dict2)

        id1 = dict1['id'].split(' ')[1]
        description1 = dict1['description']
        disclaimer1 = dict1['disclaimer']
        shortdesc1 = dict1['shortDescription']
        name1 = dict1['name']

        id2 = dict2['id']
        shortdesc2 = dict2['shortDescription']['en']
        disclaimer2 = dict2['disclaimer']['en']
        description2 = dict2['longDescription']['en']
        name2 = dict2['title']['en']

        comparisons = {
            "id": id1 == str(id2),
            "shortdescription": shortdesc1 == shortdesc2,
            "disclaimer": disclaimer1 == disclaimer2,
            "longdescription": description1 == description2,
            "name": name1==name2
        }
        print("\nComparisons -", comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
                    return False
        else:
            print("Comparison succeeded.")
            return True

    def compare_unpublished_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n", dict2)

        id1 = dict1['id'].split(' ')[1]
        id2 = dict2['id']
        comparisons = {
            "id": id1 == str(id2)
        }
        print("\nComparisons -", comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
                    return False
        else:
            print("Comparison succeeded.")
            return True

