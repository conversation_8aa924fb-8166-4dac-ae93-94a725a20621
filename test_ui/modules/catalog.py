import math

import pandas as pd
from time import sleep
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from selenium.webdriver import Keys, ActionChains
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import wait, get_store, generate_random_string

class catalog(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        self.e = e
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }

    def pac_import_catalog_csv(self):
        wait(10)
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.importdatatype_value_c)
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.store_box_name))).send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("catalog_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new catalog")
        wait(15)

    def store_import_catalog_csv(self, csv_path=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csvimport_btn_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.importdatatype_value_c)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path(csv_path))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new catalog")
        wait(15)

    def pac_open_latest_catalog(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_expand_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_folder_xpath_pac))).click()
        wait(5)
        click_column = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_xpath)))
        action.double_click(click_column).perform()
        wait(5)
        catalog_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_id_xpath)))
        action.context_click(catalog_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.catalog_name_value))).click()
        print("Opened uploaded catalog")

    def store_open_category_folder(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_management_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_folder_xpath))).click()
        sleep(5)

    def search_catalog(self, name_to_search=None, products_to_search=None):
        action = ActionChains(self.driver)
        sleep(8)
        data = name_to_search
        cleaned_data = [item for item in data if not isinstance(item, float) or not math.isnan(item)]
        for name in cleaned_data:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(name)
            action.send_keys(Keys.RETURN).perform()
            sleep(5)
            obj_id = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"(//div[contains(text(),'/store_automation/Catalog/')])[1]")))
            action.context_click(obj_id).perform()
            print(f"Searching for the catalog uploaded - {name}")
            self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
            try:
                element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
                element.click()
            except NoSuchElementException:
                pass
            catalog_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//input[contains(@name,'name')]")))
            field_value = catalog_to_check.get_attribute("value")
            print(
                "Object value taken to check with uploaded csv file value as no sns feature for catalog - catalog name: " + field_value)
            if (field_value.lower() in name):
                print("Catalog has been created successfully through CSV and verified!")
            self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Products"))).click()
            product_1 = self.driver.find_element(By.XPATH,
                                                 "(//table[contains(@id,'tableview')]//td[3][contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')])[1]")
            product_1.get_attribute("value")
            product_2 = self.driver.find_element(By.XPATH,
                                                 "(//table[contains(@id,'tableview')]//td[3][contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')])[2]")
            product_2.get_attribute("value")
            if product_1.text in products_to_search and product_2.text in products_to_search:
                print("Products has been added successfully to the catalog!")
            else:
                print("error")
            lower_name = name.lower()
            name_click = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//span[contains(text(),'{lower_name}')]")))
            action.context_click(name_click).perform()
            self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Close Tab"))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//span[contains(text(),'Catalog')])[2]"))
            ).click()
            input_element = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath)))
            input_element.clear()
        print("Opened latest objects from folder in UI")
        sleep(1)

    def store_open_latest_catalog(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_management_tab_xpath))).click()
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_folder_xpath))).click()
        wait(15)
        click_column = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_xpath)))
        action.double_click(click_column).perform()
        wait(10)
        catalog_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_id_xpath)))
        action.context_click(catalog_id).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.catalog_name_value))).click()
        print("Opened uploaded catalog")

    def get_catalog_id(self):
        catalog_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_id_xpath))).text
        print("Object id taken to check - catalog id: " + catalog_to_check)

    def read_and_verify_name_in_catalog_csv(self):
        catalog_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.catalog_name_value)))
        field_value = catalog_to_check.get_attribute("value")
        print(
            "Object value taken to check with uploaded csv file value as no sns feature for catalog - catalog name: " + field_value)
        df = pd.read_csv(Config.get_path("catalog_sample_csv_path"))
        name = df['name_en'].tolist()
        print("Reading csv file uploaded - catalog name in csv: " + (name[0]).lower())
        if (field_value.lower() == (name[0]).lower()):
            print("Catalog has been created successfully through CSV and verified!")

    def pac_add_catalog_ui(self):
        action = ActionChains(self.driver)
        home = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.home_xpath)))
        action.context_click(home).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.catalog_folder_text))).click()
        action.send_keys(Keys.ARROW_RIGHT, Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_catalog_id))).send_keys(constant.catalog_value + generate_random_string(2))
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Catalog from UI")

    def store_add_catalog_ui(self):
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_management_tab_xpath))).click()
        action = ActionChains(self.driver)
        wait(10)
        store = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_button_xpath)))
        action.context_click(store).perform()
        add_obj = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_obj_btn_text)))
        action.move_to_element(add_obj).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.catalog_btn_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_catalog_id))).send_keys(constant.catalog_value + generate_random_string(2))
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Catalog from UI")

    def add_basedatatab_details(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).send_keys(constant.catalog_value + generate_random_string(2))
        print("Adding basedata details")

    def add_store_details(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.store_value_name)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        get_store(self.driver, self.e)
        print("Filled fields under basedata tab")

    def add_producttab_details(self, productIDs_dev=None, productIDs_qa =None, productIDs_test=None):
        # action = ActionChains(self.driver)
        # wait(10)
        # self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.products_tab_text))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.pac_pdt_search_xpath))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.pdt1)
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        # select_product1 = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.product1_click_xpath_pac)))
        # action.double_click(select_product1).perform()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).clear()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.pdt2)
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        # select_product2 = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.product2_click_xpath_pac)))
        # action.double_click(select_product2).perform()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).clear()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.pdt3)
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        # select_product3 = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.product3_click_xpath_pac)))
        # action.double_click(select_product3).perform()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).clear()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.pdt4)
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        # select_product4 = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.product4_click_xpath_pac)))
        # action.double_click(select_product4).perform()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).clear()
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.pdt5)
        # self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        # select_product5 = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.product5_click_xpath_pac)))
        # action.double_click(select_product5).perform()
        # self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.select_btn_text))).click()
        # print("Filled fields under products tab")
        wait(5)
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.products_tab_text))).click()
        global productIDs
        env = self.e.get('environment')
        if env == "dev":
            productIDs_dev = True
        elif env == "qa":
            productIDs_qa = True
        elif env == "test":
            productIDs_qa = True
        else:
            print("Invalid env")
        if productIDs_dev:
            productIDs = ["69189784", "69189790", "69189791", "69189792", "69189793"]
        elif productIDs_qa:
            productIDs = ["217091", "217097","217098","217099","217100"]
        elif productIDs_test:
            productIDs = ["86909713","86909719","86909720","86909721","86909722"]
        else:
            print("Invalid product listed")
        action = ActionChains(self.driver)
        product_page = self.driver.find_element(By.XPATH,
                                           "//div[contains(@class,'x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]")
        product_page.find_element(By.XPATH,constant.pac_pdt_search_xpath).click()
        wait(2)
        product_pop_up = self.driver.find_element(By.XPATH,
                                             "//div[contains(@class,'x-panel x-border-item x-box-item x-panel-default')]")
        text_box = product_pop_up.find_element(By.XPATH, constant.search_xpath)
        self.driver.execute_script("arguments[0].scrollIntoView();", text_box)
        for i in productIDs:
            id = str(i)
            text_box.send_keys(id)
            text_box.send_keys(Keys.ENTER)
            wait(3)
            product_id = product_pop_up.find_element(By.XPATH, f"//div[contains(text(),'{id}')]")
            action.double_click(product_id).perform()
            text_box.clear()
            sleep(0.2)
        wait(2)
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.select_btn_text))).click()
        print("Filled fields under products tab")

    def catalog_update_product(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.products_tab_text))).click()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.delete_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.yes_btn_text))).click()
        print("Updated fields under products tab")