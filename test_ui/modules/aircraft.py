import pytest
import pandas as pd
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from project_configs.login_helpers import wait
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class aircraft(baseModule):
    def __init__(self, driver, e):
        super().__init__(driver)
        self.e = e

    def upload_aircraft_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.aircraft_text)
        action.send_keys(Keys.RETURN).perform()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("aircraft_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new Aircraft")
        wait(15)

    def open_latest_aircraft(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.master_data_text)
        action.send_keys(Keys.RETURN).perform()
        wait(8)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.masterdata_folder_expander_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.aircraft_folder_xpath))).click()
        print("Opened aircraft folder")

    def aircraft_id_check(self, add=None):
        if add:
            aircraft_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.pdt_id_store_xpath))).text
        else:
            aircraft_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.airport_id_xpath))).text
        print("Object id taken to check " + aircraft_to_check)
        return  aircraft_to_check

    def csv_value_check(self):
        global field_value
        aircraft_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.oem_name)))
        field_value = aircraft_to_check.get_attribute("value")
        print("Object value taken to check with uploaded csv file value as no sns feature for aircraft " + field_value)
        return field_value

    def csv_value_check_after_update(self):
        global field_value_updated
        aircraft_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.short_desc_name)))
        field_value_updated = aircraft_to_check.get_attribute("value")
        print("Object value taken to check with uploaded csv file value as no sns feature for aircraft " + field_value_updated)
        return field_value_updated

    def read_csv(self):
        global name
        df = pd.read_csv(Config.get_path("aircraft_sample_csv_path"))
        name = df['OEMName_en'].tolist()
        print("Reading csv file uploaded " + (name[0]).lower())

    def verify_values(self):
        if (field_value.lower() == (name[0]).lower()):
            print("Aircraft has been created successfully through CSV and verified!")
        else:
            pytest.fail("Aircraft names dont match between UI and CSV, Import failed.")
            print("Aircraft names dont match between UI and CSV, Import failed.")
        print("Names verified")

    def read_updated_csv(self):
        global desc
        df = pd.read_csv(Config.get_path("aircraft_sample_csv_path"))
        desc = df['description_en'].tolist()
        print("Reading csv file uploaded: " + (desc[0]).lower())

    def verify_updated_values(self):
        if (field_value_updated.lower() == (desc[0]).lower()):
            print("Aircraft has been created successfully through CSV and verified!")
        else:
            pytest.fail("Aircraft names dont match between UI and CSV, Import failed.")
            print("Aircraft names dont match between UI and CSV, Import failed.")
        print("Names verified")

    def add_aircraft_ui(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.aircraft_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_aircraft_id))).send_keys(constant.aircraft_value)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Aircraft from UI")

    def aircraft_basedatatab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.oem_name))).send_keys(constant.aircraft_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.short_desc_name))).send_keys(constant.aircraft_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.family_name))).send_keys(constant.aircraft_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.model_name))).send_keys(constant.aircraft_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.logo_search_xpath))).click()
        search_input = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.searchbox_name)))
        search_input.send_keys(constant.search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.searchbutton_text))).click()
        wait(15)
        select_file = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_grid_xpath)))
        action = ActionChains(self.driver)
        action.double_click(select_file).perform()
        print("Filled details under basedata tab")

    def update_aircraft_ui(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.short_desc_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.short_desc_name))).send_keys(
            constant.aircraft_value + "_updatedValue")
        print("Updated description value")