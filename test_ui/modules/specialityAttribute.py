import glob
import os
import pytest
import pandas as pd
from selenium.common import NoSuchElementException
from shared_config.config import *
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import wait, get_store, generate_random_string

class specialityAttribute(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }

    def upload_spa_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.importdatatype_value)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.store_name))).send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("specialityattribute_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new speciality attribute")
        wait(15)

    def verify_spa_csv_uploaded(self):
        df = pd.read_csv(Config.get_path("specialityattribute_sample_csv_path"))
        desc_csv = df['description_en'].tolist()
        print("Reading csv file uploaded - description in csv: " + (desc_csv[0]).lower())
        field_value = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.short_desc_name)))
        desc_ui = field_value.get_attribute("value")
        if (desc_ui.lower() == (desc_csv[0]).lower()):
            print("Latest Speciality Attribute is same as uploaded from CSV!")
        else:
            pytest.fail("Speciality Attribute names dont match between UI and CSV, Import failed.")
            print("Speciality Attribute names dont match between UI and CSV, Import failed.")

    def verify_spa_csv_uploaded_after_update(self):
        wait(2)
        df = pd.read_csv(Config.get_path("specialityattribute_sample_csv_path"))
        disc_csv = df['disclaimer_en'].tolist()
        print("Reading csv file uploaded - disclaimer in csv: " + (disc_csv[0]).lower())
        field_value = self.driver.find_element(By.XPATH,"//div[contains(@class,'pimcore_editable_wysiwyg')]")
        disc_ui=field_value.text
        if (disc_ui.lower() == (disc_csv[0]).lower()):
            print("Latest Speciality Attribute is same as uploaded from CSV!")
        else:
            pytest.fail("Speciality Attribute names dont match between UI and CSV, Import failed.")
            print("Speciality Attribute names dont match between UI and CSV, Import failed.")

    def upload_store_spa_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.importdatatype_value)
        action.send_keys(Keys.RETURN).perform()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("specialityattribute_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new speciality attribute")
        wait(15)

    def open_latest_spa(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_expand_xpath))).click()
        wait(8)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.spa_folder_xpath))).click()
        print("Opened speciality attribute folder")

    def spa_id_check(self):
        global spa_to_check
        spa_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.spa_id_xpath))).text
        print("Object id taken to check with sns in super admin logs " + spa_to_check)
        return spa_to_check

    def add_spa_ui(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.speciality_attribute_btn_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_speciality_attribute_id))).send_keys(constant.spa_value)
        spec_ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        spec_ok_button.click()
        print("Added new object - Speciality attribute from UI")

    def spa_basedata(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.short_desc_name))).send_keys(constant.spa_value + generate_random_string(2))
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.disclaimer_xpath))).send_keys(constant.spa_value)
        print("Filled fields under basedata tab")

    def add_store_for_spa(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.store_value_name)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        get_store(self.driver, self.e)
        print("Added store details")

    def add_asset(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.assets_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.pimcore_icon_search_xpath))).click()
        search_input = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.search_input_name)))
        search_input.send_keys(constant.image_value)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.search_btn_text))).click()
        select_file = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action = ActionChains(self.driver)
        action.double_click(select_file).perform()
        print("Filled fields under assets tab")

    def update_spa(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.disclaimer_xpath))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.disclaimer_xpath))).send_keys(
            constant.spa_value + "_updatedValue")
        print("Updated disclaimer value")

    def open_latest_spa_store(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_management_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.spa_folder_xpath))).click()
        print("Opened speciality attribute folder")

    def select_spa(self, single_export=None, multiple_export=None):
        if single_export:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-grid-checkcolumn-cell-inner')])[1]"))).click()
            print("Selected brand")
        elif multiple_export:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-grid-checkcolumn-cell-inner')])[1]"))).click()
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-grid-checkcolumn-cell-inner')])[3]"))).click()
            print("Selected multiple brands")
        else:
            print("Invalid entry")

    def read_multilang_spa_export_csv(self):
        csv_file_path = os.getenv("SELENIUM_DOWNLOAD_PATH", "")
        if not os.path.exists(csv_file_path):
            print(f"Download path {csv_file_path} does not exist.")
            return None
        csv_files = glob.glob(os.path.join(csv_file_path, "*.csv"))
        if not csv_files:
            print("No CSV files found.")
            return None
        csv_files.sort(key=os.path.getmtime, reverse=True)
        latest_csv = csv_files[0]
        print(f"Latest CSV file path: {latest_csv}")
        df = pd.read_csv(latest_csv, encoding='utf-8-sig')
        language_name_headers = [
            f'{prefix}_{lang}'
            for lang in LANGUAGES_CODES_CONFIGURED
            for prefix in ['description', 'disclaimer']
        ]
        other_columns = ['image']
        selected_columns = df[other_columns + language_name_headers]
        print("selected_columns",selected_columns)
        for index, row in selected_columns.iterrows():
            image = row['image']
            description = {lang: row.get(f'description_{lang}', '') for lang in LANGUAGES_CODES_CONFIGURED}
            disclaimer = {lang: row.get(f'disclaimer_{lang}', '') for lang in LANGUAGES_CODES_CONFIGURED}
            for lang in LANGUAGES_CODES_CONFIGURED:
                print(f"Description ({lang}): {description[lang]}")
                print(f"Disclaimer ({lang}): {disclaimer[lang]}")
            print(f"Image: {image}")
        data_list = selected_columns.to_dict(orient='records')
        print(data_list)
        return data_list

    def read_spa_export_csv(self, img_verify=None):
        csv_file_path = os.getenv("SELENIUM_DOWNLOAD_PATH", "")
        if not os.path.exists(csv_file_path):
            print(f"Download path {csv_file_path} does not exist.")
            return None
        csv_files = glob.glob(os.path.join(csv_file_path, "*.csv"))
        if not csv_files:
            print("No CSV files found.")
            return None
        csv_files.sort(key=os.path.getmtime, reverse=True)
        latest_csv = csv_files[0]
        print(f"Latest CSV file path: {latest_csv}")
        df = pd.read_csv(latest_csv, encoding='utf-8-sig')
        df.columns = df.columns.str.replace('^\ufeff', '', regex=True)
        required_columns = ['description_en','disclaimer_en', 'image']
        selected_columns = df[required_columns]
        print(selected_columns)
        for index, row in selected_columns.iterrows():
            desc = row['description_en']
            disc = row['disclaimer_en']
            image = row['image']
            print(f"Description: {desc}, Disclaimer: {disc}, Image: {image}")
        data_list = selected_columns.to_dict(orient='records')
        return data_list

    def open_selected_spa(self, multiple_export=None, single_export=None, click_xpath=None):
        action = ActionChains(self.driver)
        global obj_id
        if multiple_export:
            obj_id = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, click_xpath)))
            action.context_click(obj_id).perform()
        elif single_export:
            print("store", STORE_NAME)
            obj_id = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"(//div[contains(text(),'/{STORE_NAME}/SpecialityAttributes/')])[1]")))
            action.context_click(obj_id).perform()
        else:
            print("Invalid entry")
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened selected speciality attribute to verify details")

    def get_data(self):
        self.data['id']=spa_to_check
        self.data['description']=self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.short_desc_name))).get_attribute("value")
        element = self.driver.find_element(By.XPATH,"//div[contains(@class,'pimcore_editable_wysiwyg')]")
        self.data['disclaimer']=element.text
        # print("disclaimer",self.data['disclaimer'])
        print("Data taken from ui")
        return self.data

    def compare_spa_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n", dict2)

        id1=dict1['id'].split(' ')[1]
        description1=dict1['description']
        disclaimer1=dict1['disclaimer']

        # spa_info = dict2['SpecialityAttribute'][0]
        id2 = dict2['id']
        description2=dict2['description']['en']
        disclaimer2=dict2['disclaimer']['en']

        # comparing dict1 and dict2 values
        comparisons = {
            "id": id1 == str(id2),
            "description": description1 == description2,
            "disclaimer": disclaimer1 == disclaimer2
        }
        print("\nComparisons -",comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
                    return False
        else:
            print("Comparison succeeded.")
            return True

    def compare_spa_unpublished_data(self, dict1=None, dict2=None):
        print("Data taken from UI: \n", dict1)
        print("Data taken from AWS SNS: \n", dict2)

        id1=dict1['id'].split(' ')[1]
        id2 = dict2['id']

        # comparing dict1 and dict2 values
        comparisons = {
            "id": id1 == str(id2),
        }
        print("\nComparisons -",comparisons)
        all_match = all(comparisons.values())
        if not all_match:
            print("Comparison failed for the following:")
            for key, value in comparisons.items():
                if not value:
                    print(f"{key} did not match.")
                    return False
        else:
            print("Comparison succeeded.")
            return True

    def add_spa_ui_store(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_management_tab_xpath))).click()
        action = ActionChains(self.driver)
        store = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_button_xpath)))
        action.context_click(store).perform()
        add_obj = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_obj_btn_text)))
        action.move_to_element(add_obj).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.speciality_attribute_btn_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_speciality_attribute_id))).send_keys(constant.spa_value)
        spec_ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        spec_ok_button.click()
        print("Added new object - Speciality attribute from UI")

