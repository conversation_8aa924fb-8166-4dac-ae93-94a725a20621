from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.login_helpers import wait
from selenium.webdriver import Keys, ActionChains
from project_configs.login_helpers import get_store
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class airline(baseModule):
    def __init__(self, driver, e):
        super().__init__(driver)
        self.e = e

    def airline_id_check(self):
        airline_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_id_xpath))).text
        print("Object id taken to check with sns in super admin logs " + airline_to_check)
        return airline_to_check

    def airlne_basedatatab(self):
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.airlinename_box_name))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.description_box_xpath))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.icaocode_box_name))).send_keys(constant.icaocode_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.iatacode_box_name))).send_keys(constant.iatacode_value)
        # try:
        #     element = self.driver.find_element(By.LINK_TEXT, constant.ok_btn_text)
        #     element.click()
        # except NoSuchElementException:
        #     pass
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.logo_search_xpath))).click()
        search_input = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.searchbox_name)))
        search_input.send_keys(constant.search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.searchbutton_text))).click()
        wait(15)
        select_file = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_grid_xpath)))
        action = ActionChains(self.driver)
        action.double_click(select_file).perform()
        print("Filled details under basedata tab")

    def airline_contactinfotab(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.contact_info_tab_name))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.firstname_box_name))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.company_name))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.address_name))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.state_name))).send_keys(constant.airline_name)
        country = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.country_name)))
        country.click()
        country.send_keys(constant.country_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.email_box_name))).send_keys(constant.email_id_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.lastname_name))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.addressline1_name))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.city_name))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.zipcode_name))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.phone_name))).send_keys(constant.phone_number)
        print("Filled details under contact_info tab")

    def airline_aircrafttab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.aircrafttab_text))).click()
        print("Opened aircraft tab")

    def airline_storetab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.stores_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_search_pac_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.store_value_name)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        get_store(self.driver, self.e)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.select_button_text))).click()
        print("Filled fields under stores tab")

    def airline_configtab(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.config_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.autoupdatecatalog_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.selectairlinelang_name))).send_keys(constant.lang_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.onboarddelivery_name))).send_keys(
            constant.onboarddelivery_value)
        print("Filled fields under config tab")

    def update_airline(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.description_box_xpath))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.description_box_xpath))).send_keys(
            constant.airline_name + "_updatedValue")
        print("Updated disclaimer value")

    def add_airline(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.airline_folder_addobj_text))).click()
        action.send_keys(Keys.ARROW_RIGHT, Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.airline_name_textbox_id))).send_keys(constant.airline_name)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.ok_button_text))).click()
        print("Added new object - Airline from UI")

    def open_latest_airline(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(
            constant.airline_folder_value)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_folder_open_xpath))).click()
        # action.double_click(airline_folder).perform()
        wait(5)
        print("Opened airline folder")
