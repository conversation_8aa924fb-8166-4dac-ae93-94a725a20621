import math
import os
import pytest
import pandas as pd
from time import sleep
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from selenium.webdriver import Keys, ActionChains
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import wait, generate_random_string

class category(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        self.e = e
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }

    def pac_import_category_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.category_text)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.sales_name_xpath))).send_keys(constant.sales_name)
        wait(2)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.store_box_name))).send_keys(constant.store_value_name)
        wait(2)
        action.send_keys(Keys.RETURN).perform()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("category_sample_csv_path"))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new category")
        wait(30)

    def pac_get_latest_category(self):
        action = ActionChains(self.driver)
        wait(30)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.master_data_text)
        action.send_keys(Keys.RETURN).perform()
        wait(20)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.masterdata_folder_expander_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_expand_xpath))).click()
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_filter_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_filter_store_send_xpath))).send_keys(
            constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_folder_xpath))).click()
        wait(10)
        creation_date = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_column_xpath)))
        sleep(30)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_column_xpath)))
        action.double_click(creation_date).perform()
        wait(5)
        category_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_category_xpath)))
        action.context_click(category_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).click()
        print("Opened uploaded category")

    def category_get_id(self):
        category_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_id_xpath))).text
        print("Object id taken to check - category id: " + category_to_check)
        return category_to_check

    def read_and_verify_category_name_in_csv(self):
        category_name_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name)))
        field_value = category_name_to_check.get_attribute("value")
        print(
            "Object value taken to check with uploaded csv file value as no sns feature for category - category name: " + field_value.lower())
        df = pd.read_csv(Config.get_path("category_sample_csv_path"))
        name = df['name_en'].tolist()
        print("Reading csv file uploade - category name in csv: " + (name[0]).lower())
        if (field_value.lower() == (name[0]).lower()):
            print("Category has been created successfully through CSV and verified!")
        else:
            pytest.fail("Category names dont match between UI and CSV, Import failed")
            print("Category names dont match between UI and CSV, Import failed")
        print("Names verified")


    def search_category(self, name_to_search=None):
        action = ActionChains(self.driver)
        sleep(20)
        for name in name_to_search:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(name)
            action.send_keys(Keys.RETURN).perform()
            sleep(10)
            obj_id = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"(//div[contains(text(),'/Master Data/Category/store_automation/')])[1]")))
            action.context_click(obj_id).perform()
            print(f"Searching for the category uploaded - {name}")
            self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
            try:
                element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
                element.click()
            except NoSuchElementException:
                pass
            catalog_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//input[contains(@name,'name')]")))
            field_value = catalog_to_check.get_attribute("value")
            print(
                "Object value taken to check with uploaded csv file value as no sns feature for category - category name: " + field_value)
            print("field", field_value)
            print("name", name)
            if (field_value.lower()==name):
                print("Catalog has been created successfully through CSV and verified!")
            lower_name = name.lower()
            name_click = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//span[contains(text(),'{lower_name}')]")))
            action.context_click(name_click).perform()
            self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Close Tab"))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//span[contains(text(),'Category')])[2]"))
            ).click()
            input_element = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath)))
            input_element.clear()
        print("Opened latest objects from folder in UI")
        sleep(1)

    def read_and_verify_multilevel_category_name_in_csv(self, names_to_search=None):
        category_name_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name)))
        field_value = category_name_to_check.get_attribute("value")
        print(
            "Object value taken to check with uploaded csv file value as no sns feature for category - category name: " + field_value.lower())
        df = pd.read_csv(Config.get_path("category_multilevel_path"))
        name = df['name_en'].tolist()
        print("Reading csv file uploade - category name in csv: " + (name[0]).lower())
        if (field_value.lower() == (name[0]).lower()):
            print("Category has been created successfully through CSV and verified!")
        else:
            pytest.fail("Category names dont match between UI and CSV, Import failed")
            print("Category names dont match between UI and CSV, Import failed")
        print("Names verified")

    def read_name(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.basedata_xpath))).click()
        print("Opened basedata tab")
        category_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//input[contains(@name,'name')]")))
        field_value = category_to_check.get_attribute("value")
        print("Name taken - ", field_value)
        return field_value

    def read_and_verify_category_updated_disclaimer(self):
        category_disclaimer_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name)))
        field_value = category_disclaimer_to_check.get_attribute("value")
        print("Object value taken to check with uploaded csv file value as no sns feature for category - category disclaimer: " + field_value.lower())
        df = pd.read_csv(Config.get_path("category_sample_csv_path"))
        disclaimer = df['disclaimer_en'].tolist()
        print("Reading csv file uploaded - category disclaimer in csv: " + (disclaimer[0]).lower())

        # verifying the values
        if (field_value.lower() == (disclaimer[0]).lower()):
            print("Category has been updated successfully through CSV and verified!")
        else:
            pytest.fail("Category names dont match between UI and CSV, Import failed.")
            print("Category names dont match between UI and CSV, Import failed.")
        print("Disclaimers verified")

    def pac_ui_add_category(self):
        action = ActionChains(self.driver)
        home = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.home_xpath)))
        action.context_click(home).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.category_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_category_id))).send_keys(constant.category_value)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Category from UI")

    def store_ui_add_category(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_tab_xpath))).click()
        action = ActionChains(self.driver)
        wait(10)
        store = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_category_open_xpath)))
        action.context_click(store).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.category_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_category_id))).send_keys(constant.category_value + generate_random_string(2))
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Category from UI")

    def category_add_basedata_details_store(self):
        action = ActionChains(self.driver)
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).send_keys(constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name))).send_keys(constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.shortDescription_name))).send_keys(constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.description_box_xpath))).send_keys(constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.url_name))).send_keys(
            constant.url + "_" + constant.category_value)
        action.send_keys(Keys.PAGE_DOWN).perform()
        print("Added basedata details")
        wait(10)

    def category_add_basedata_details(self):
        action = ActionChains(self.driver)
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).send_keys(constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name))).send_keys(constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.shortDescription_name))).send_keys(constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.description_box_xpath))).send_keys(constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.url_name))).send_keys(
            constant.url + "_" + constant.category_value)
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.sales_strategy_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.sales_strategy_xpath))).send_keys(constant.sales_name)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.sales_strategy_xpath))).send_keys(Keys.ENTER)
        print("Added basedata details")
        wait(2)

    def add_store_details(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_category_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_category_xpath))).send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        print("Filled store details")

    def category_add_attributes(self):
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.attributes_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.attribute_set_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.attribute_set_xpath))).send_keys(constant.attribute_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.attribute_set_xpath))).send_keys(Keys.ENTER)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.variant_expand_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.theme_name_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.theme_name_xpath))).send_keys(
            "theme_" + constant.category_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.variant_attribute_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.variant_attribute_xpath))).send_keys(
            constant.varaint_attribute_value)
        print("Filled details under attributes tab")
        wait(2)

    def add_asset_data(self):
        wait(15)
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.assets_tab_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_1_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img1_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file1 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file1).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_2_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_2_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img2_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file2 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file2).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_3_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_3_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img3_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file3 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file3).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_4_dropdown_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_4_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img4_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file4 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file4).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_5_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img5_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file5 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file5).perform()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_6_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_xpath))).send_keys(constant.img6_search_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_btn_xpath))).click()
        select_file6 = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.select_img_name)))
        action.double_click(select_file6).perform()
        action.send_keys(Keys.PAGE_DOWN).perform()
        print("Filled details under assets tab")
        wait(3)

    def category_update_disclaimer(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.disclaimer_name))).send_keys(
            constant.category_value + "_updatedValue")
        print("Updated disclaimer value")

    def store_category_csv_import_with_zip(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.category_text)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("category_sample_csv_path_with_images_zip"))
        wait(10)
        self.driver.find_element(By.ID, "zipFile-button-fileInputEl").send_keys(os.getcwd() + "/zip_images/category_imgs.zip")
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - category with zip file images")
        wait(30)

    def store_category_csv_import(self, csv_path=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.category_text)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        print(csv_path)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path(csv_path))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        wait(30)
        print("Through csv import - category")

    def store_open_category_folder(self):
        sleep(3)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_tab_xpath))).click()
        sleep(2)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_category_folder_xpath))).click()
        sleep(1)

    def store_get_latest_category(self):
        action = ActionChains(self.driver)
        wait(30)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.category_tab_xpath))).click()
        wait(20)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_category_folder_xpath))).click()
        wait(5)
        creation_date = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_column_xpath)))
        action.double_click(creation_date).perform()
        sleep(30)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_column_xpath)))
        action.double_click(creation_date).perform()
        wait(2)
        category_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_category_xpath)))
        action.context_click(category_id).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).click()
        print("Opened uploaded category")
