from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.login_helpers import wait
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from selenium.webdriver.support.ui import <PERSON><PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

class store(baseModule):
    def __init__(self, driver, e, data: dict = {}):
        super().__init__(driver)
        self.e = e

    def add_store(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.store_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_store_id))).send_keys(constant.store_value)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Store from UI")

    def get_store_id(self, update=None):
        if update:
            store_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.store_id_update_xpath))).text
        else:
            store_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.store_id_xpath))).text
        print("Object id taken to check with sns in super admin logs " + store_to_check)
        return store_to_check

    def store_basedatatab(self):
        action = ActionChains(self.driver)
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.storename_name))).send_keys(constant.store_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.description_name))).send_keys(constant.store_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.logo_search_xpath))).click()
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.store_img_name)
        wait(15)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        # sleep(10)
        wait(50)
        select_file = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.img_grid_xpath)))
        action.double_click(select_file).perform()
        print("Filled fields under basedata tab")

    def address_infotab(self):
        action = ActionChains(self.driver)
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.addressinfo_tab_text))).click()
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.firstname_box_name))).send_keys(constant.store_value)
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.addressline1_name))).send_keys(constant.store_value)
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.city_name))).send_keys(constant.store_value)
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.zipcode_name))).send_keys(constant.store_value)
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.email_box_name))).send_keys(constant.email_id_value)
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.lastname_name))).send_keys(constant.store_value)
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.address_name))).send_keys(constant.store_value)
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.state_name))).send_keys(constant.store_value)
        country = WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.country_name)))
        country.click()
        country.send_keys(constant.country_value)
        action.send_keys(Keys.RETURN).perform()
        WebDriverWait(self.driver, constant.wait_time_qa).until(
            EC.element_to_be_clickable((By.NAME, constant.phone_name))).send_keys(constant.phone_number)
        print("Filled fields under address_info tab")

    def store_configtab(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.config_tab_text))).click()
        self.driver.find_element(By.XPATH, constant.autoapprove_chk_xpath).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.managestock_name))).send_keys(constant.yes_btn_text)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.sales_strategy_xpath))).send_keys(constant.sales_name)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.orderthreshold_value_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.orderprovider_name))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.orderprovider_name))).send_keys(constant.orderprovider_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.taxpercent_name))).send_keys(constant.tax_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.selectlang_name))).send_keys(constant.lang_value)
        action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.dispcurrency_name))).send_keys(constant.dispcurrency_value)
        action.send_keys(Keys.RETURN).perform()
        print("Filled fields under config tab")
        wait(5)

    def store_payconfigtab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.payconfig_tab_text))).click()
        self.driver.find_element(By.NAME, constant.bypasspacpay_name).click()
        print("Filled fields under pay_config tab")

    def store_associatedairlinetab(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.associatedairline_tab_text))).click()
        print("Filled fields under associated_airline tab")

    def update_store(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.description_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.description_name))).send_keys(
            constant.store_value + "_updatedValue")
        print("Updated description value")

    def open_latest_store(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.store_text)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.storesfolder_xpath))).click()
        action.send_keys(Keys.RETURN).perform()
        print("Opened store folder")
