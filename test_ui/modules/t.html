<div
  class="x-panel x-window-item x-panel-default"
  style="width: 500px; height: 62px"
  role="presentation"
  id="form-1246"
>
  <div
    id="form-1246-bodyWrap"
    data-ref="bodyWrap"
    class="x-panel-bodyWrap"
    role="presentation"
  >
    <div
      id="form-1246-body"
      data-ref="body"
      class="x-panel-body x-panel-body-default x-panel-body-default x-noborder-trbl"
      role="form"
      style="padding: 10px; width: 500px; left: 0px; top: 0px; height: 62px"
    >
      <div
        id="form-1246-outerCt"
        data-ref="outerCt"
        class="x-autocontainer-outerCt"
        role="presentation"
        style="width: 100%; table-layout: fixed"
      >
        <div
          id="form-1246-innerCt"
          data-ref="innerCt"
          style=""
          role="presentation"
          class="x-autocontainer-innerCt"
        >
          <div
            class="x-field x-form-item x-form-item-default x-form-readonly x-form-type-text x-field-default x-anchor-form-item x-field-focus"
            style="width: 470px"
            role="presentation"
            id="filefield-1247"
          >
            <label
              id="filefield-1247-labelEl"
              data-ref="labelEl"
              class="x-form-item-label x-form-item-label-default x-unselectable"
              style="padding-right: 5px; width: 105px"
              for="filefield-1247-inputEl"
              ><span
                class="x-form-item-label-inner x-form-item-label-inner-default"
                style="width: 100px"
                ><span
                  id="filefield-1247-labelTextEl"
                  data-ref="labelTextEl"
                  class="x-form-item-label-text"
                  >File:</span
                ></span
              ></label
            >
            <div
              id="filefield-1247-bodyEl"
              data-ref="bodyEl"
              role="presentation"
              class="x-form-item-body x-form-item-body-default x-form-text-field-body x-form-text-field-body-default x-form-file-wrap"
            >
              <div
                id="filefield-1247-triggerWrap"
                data-ref="triggerWrap"
                role="presentation"
                class="x-form-trigger-wrap x-form-trigger-wrap-default x-form-trigger-wrap-focus"
              >
                <div
                  id="filefield-1247-inputWrap"
                  data-ref="inputWrap"
                  role="presentation"
                  class="x-form-text-wrap x-form-text-wrap-default x-form-text-wrap-focus"
                >
                  <input
                    id="filefield-1247-inputEl"
                    data-ref="inputEl"
                    type="text"
                    size="1"
                    name=""
                    placeholder="Select a file"
                    readonly="readonly"
                    tabindex="-1"
                    aria-hidden="false"
                    aria-disabled="false"
                    role="textbox"
                    aria-invalid="false"
                    aria-readonly="true"
                    aria-describedby="filefield-1247-ariaStatusEl"
                    aria-required="false"
                    class="x-form-field x-form-empty-field x-form-empty-field-default x-form-text x-form-text-default x-form-text-file"
                    autocomplete="off"
                  />
                </div>
                <div
                  id="filefield-1247-trigger-filebutton"
                  class="x-form-trigger x-form-trigger-default x-form-trigger-cmp x-form-trigger-cmp-default x-form-trigger-focus"
                  role="presentation"
                  style="width: 36px"
                >
                  <div
                    class="x-btn x-form-file-btn x-unselectable x-btn-default-small x-focus x-btn-focus x-btn-default-small-focus"
                    style="margin-left: 3px"
                    unselectable="on"
                    id="filefield-1247-button"
                  >
                    <span
                      id="filefield-1247-button-btnWrap"
                      data-ref="btnWrap"
                      role="presentation"
                      unselectable="on"
                      style=""
                      class="x-btn-wrap x-btn-wrap-default-small"
                      ><span
                        id="filefield-1247-button-btnEl"
                        data-ref="btnEl"
                        role="presentation"
                        unselectable="on"
                        style=""
                        class="x-btn-button x-btn-button-default-small x-btn-no-text x-btn-icon x-btn-icon-left x-btn-button-center"
                        ><span
                          id="filefield-1247-button-btnIconEl"
                          data-ref="btnIconEl"
                          role="presentation"
                          unselectable="on"
                          class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_upload"
                          style=""
                        ></span
                        ><span
                          id="filefield-1247-button-btnInnerEl"
                          data-ref="btnInnerEl"
                          unselectable="on"
                          class="x-btn-inner x-btn-inner-default-small"
                          >&nbsp;</span
                        ></span
                      ></span
                    ><input
                      id="filefield-1247-button-fileInputEl"
                      data-ref="fileInputEl"
                      class="x-form-file-input"
                      type="file"
                      size="1"
                      name="Filedata"
                      unselectable="on"
                      data-componentid="filefield-1247-button"
                    />
                  </div>
                </div>
              </div>
              <span
                id="filefield-1247-ariaStatusEl"
                data-ref="ariaStatusEl"
                aria-hidden="true"
                class="x-hidden-offsets"
              ></span
              ><span
                id="filefield-1247-ariaErrorEl"
                data-ref="ariaErrorEl"
                aria-hidden="true"
                aria-live="assertive"
                class="x-hidden-clip"
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
