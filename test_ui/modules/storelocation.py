from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import wait, generate_random_integer

class storelocation(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {}
            
    def add_storelocation(self):
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.storeloc_text))).click()
        self.wait.until(EC.element_to_be_clickable((By.ID, constant.add_store_id))).send_keys(constant.storeloc_value+generate_random_integer(2))
        ok_button = self.wait.until(EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Store Location from UI")
    
    def get_storeloc_id(self, update_unpublish=None):
        if update_unpublish:
            storeloc_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.store_id_update_xpath))).text
        else:
            storeloc_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.store_id_xpath))).text
        print("Object id taken to check - store location id: " + storeloc_to_check)
        
    def add_basedata_details(self, pac=None):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.storeloc_name))).send_keys(constant.storeloc_value)
        if pac:
            action.send_keys(Keys.PAGE_DOWN).perform()
            wait(3)
            action.send_keys(Keys.PAGE_DOWN).perform()
            wait(3)
            self.wait.until(
                EC.element_to_be_clickable((By.NAME, constant.storelocname))).send_keys(constant.store_value_name)
        print("Filled fields under basedata tab")
        
    def pac_open_latest_storeloc(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.store_value_name)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_expand_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.invloc_folder_xpath))).click()
        print("Opened inventory location folder")
        
    def update_storeloc(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.storeloc_name))).clear()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.storeloc_name))).send_keys(
            constant.storeloc_value + '_Updated')
        print("Updated field under basedata tab")
        
    def store_add_storeloc(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.invloc_tab_xpath))).click()
        home = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.invloc_folder_xpath)))
        action.context_click(home).perform()
        add_obj = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_obj_btn_text)))
        action.move_to_element(add_obj).perform()
        self.add_storelocation()
        print("Added storelocation from UI")
        
    def navigate_to_storelocation_folder(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.invloc_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.invloc_folder_xpath))).click()
        print("Opened inventory location folder")