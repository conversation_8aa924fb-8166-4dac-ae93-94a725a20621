from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.login_helpers import wait
from selenium.webdriver import Keys, ActionChains
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class cabinClass(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {}

    def add_cabinclass(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.cabincls_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_aircraft_id))).send_keys(constant.cabinclass_value)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Cabinclass from UI")
        
    def get_id(self, unpublish=None):
        if unpublish:
            id_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "(//div[contains(text(),'ID')])[2]"))).text
        else:
            id_to_check = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'ID')]"))).text
        print("Object id taken to check with sns in super admin logs " + id_to_check)
        return id_to_check

    def add_basedata(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.name_name))).send_keys(constant.cabinclass_value)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.code_name))).send_keys(constant.cabinclass_value)
        print("Added basedata details")

    def open_pac_latest_cabinclass(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.master_data_text)
        action.send_keys(Keys.RETURN).perform()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.masterdata_folder_expander_xpath))).click()
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.cabinclass_folder_xpath))).click()
        wait(10)
        creation_date = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creation_date_column_xpath)))
        action.double_click(creation_date).perform()
        wait(5)
        cabinclass_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_cabinclass_xpath)))
        action.context_click(cabinclass_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened latest object to unpublish - Cabinclass from UI")