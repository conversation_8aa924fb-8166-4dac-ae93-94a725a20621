import pandas as pd
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from project_configs.login_helpers import wait
from selenium.webdriver import Keys, ActionChains
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class airport(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }

    def get_id(self):
        airport_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "(//div[contains(text(),'ID')])[2]"))).text
        print("Object id taken to check " + airport_to_check)
        return airport_to_check

    def pac_upload_airport_csv(self):
        action = ActionChains(self.driver)
        self.wait.until(EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.airport_text)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(Config.get_path("airport_sample_csv_path"))
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        wait(15)
        print("Through csv import - added new airport")

    def pac_open_latest_airport(self):
        action = ActionChains(self.driver)
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.master_data_text)
        action.send_keys(Keys.RETURN).perform()
        # action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.masterdata_folder_expander_xpath))).click()
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airport_folder_xpath))).click()
        wait(5)
        click_column = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creationdate_column_xpath_update)))
        action.double_click(click_column).perform()
        wait(35)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_airport_xpath))).click()
        obj_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.latest_airport_xpath)))
        action.context_click(obj_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        wait(5)
        print("Opened latest object from folder in UI")

    def read_and_verify_name_in_airport_csv(self):
        airport_to_check = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.airport_name)))
        field_value = airport_to_check.get_attribute("value")
        print("Object value taken to check with uploaded csv file value as no sns feature for airport - airport name: " + field_value)
        df = pd.read_csv(Config.get_path("airport_sample_csv_path"))
        name = df['airportName_en'].tolist()
        print("Reading csv file uploaded - airport name in csv: " + (name[0]).lower())
        if (field_value.lower() == (name[0]).lower()):
            print("Airport has been created successfully through CSV and verified!")