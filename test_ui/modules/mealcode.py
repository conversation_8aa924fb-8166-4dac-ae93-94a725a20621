from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from selenium.webdriver import ActionChains
from project_configs.csv_config import Config
from project_configs.login_helpers import wait
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC

class mealcode(baseModule):
    def __init__(self, driver, e, data=None):
        super().__init__(driver)
        self.e = e
        if data is None:
            data = {}
        self.e = e
        if not data:
            self.data = {

            }

    def get_id(self):
        id_to_check = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'ID')]"))).text
        print("Object id taken to check with sns in super admin logs " + id_to_check)
        return id_to_check

    def add_mealcode(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.mealcode_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_category_id))).send_keys(constant.mealcode_name)
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Meal Code through UI")

    def mealcode_basedata_details(self):
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.mealc_name))).send_keys(constant.mealcode_name)
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.rg_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(constant.airline_value)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_icon_xpath))).click()
        wait(10)
        select_airline = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_xpath)))
        action.double_click(select_airline).perform()
        print("Added mealcode basedata details")

    def open_mealcode_module(self):
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.mealcode_btn_text))).click()
        print("Opened mealcode module")

    def open_airline(self):
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.airline_textbox))).send_keys(constant.airline_value)
        print("Opened airline")

    def add_airline_details(self):
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_search_dropdown_xpath))).click()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_from_dropdown_xpath))).click()
        print("Added airline name")
        wait(5)

    def click_exportcsv_btn(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT,constant.export_csv_btn_text))).click()
        print("Clicked on Export CSV button")
        wait(2)

    def import_mealcode_csv(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csv_import_text))).click()
        env = self.e.get('environment')
        if env == "dev":
            self.driver.find_element(By.NAME, "mealcodefile").send_keys(
                Config.get_path("mealcode_dev_csv_xpath"))
        elif env == "qa":
            self.driver.find_element(By.NAME, "mealcodefile").send_keys(
                Config.get_path("mealcode_qa_csv_xpath"))
        elif env == "test":
            self.driver.find_element(By.NAME, "mealcodefile").send_keys(
                Config.get_path("mealcode_test_csv_xpath"))
        else:
            print("Invalid import state")
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.submit_btn_text))).click()
        print("Imported CSV successfully!")
        wait(3)


