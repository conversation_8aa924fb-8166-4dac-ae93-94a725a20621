import html
import re
from urllib.parse import urljoin

import pytest, pandas as pd
from selenium.common import NoSuchElementException, TimeoutException
from users.baseModule import baseModule
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from selenium.webdriver import Keys, ActionChains
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import wait, get_spa
from selenium.webdriver.support import expected_conditions as EC
from driver_setup.config import *
from driver_setup.helper import *
from selenium.webdriver.support.ui import WebDriverWait
from time import sleep
from typing import Literal, Union, Optional, TypedDict, Any, List, Dict
import imagesize
import difflib, json

AspectRatioType = Literal["1:1", "16:9"]

class brand(baseModule):
    PANEL_XPATH = "//div[@class='x-panel pimcore_class_Brands x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable']"
    QUEUE_URL = CATALOG_SQS_QUEUE_URL
    def __init__(self, driver, data=None, csv=None):
        super().__init__(driver)
        if data is None:
            data = {"name": {}}
        if csv:
            self.data = data
        else:
            self.getObjectID()
            self.data = data

    def enterNameAllLanguages(self, MULTI_LANGUAGES, baseName=None):
        for lang in MULTI_LANGUAGES:
            if baseName is None:
                baseName = lang + generate_short_uuid()
            print("entering name...", LANGUAGE_MAP[lang])
            self.enterName(LANGUAGE_MAP[lang], baseName + " " +lang, lang)

    def fetchName(self, lang="English"):
        self.selectLanguage(lang)
        current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)
        current_language_field = current_page.find_element(By.XPATH, ".//div[@class='x-panel x-tabpanel-child x-panel-default']")
        wait = WebDriverWait(current_language_field, 10)
        name_field = wait.until(EC.element_to_be_clickable((By.NAME, constant.name_name)))
        return name_field.get_attribute("value")

    def enterName(self, lang="English", name="ui_auto_brand_name", langCode='en'):
        current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)     
        self.open_base_data()
        self.selectLanguage(lang)
        current_language_field = current_page.find_element(By.XPATH,".//div[@class='x-panel x-tabpanel-child x-panel-default']")
        wait = WebDriverWait(current_language_field, 10)
        name_field = wait.until(EC.element_to_be_clickable((By.NAME, constant.name_name)))
        name_field.clear()
        name_field.send_keys(name) 
        self.data['name'][langCode] = name 
        logger.info(f"Entered name '{name}'  for language, {lang}" )

    def selectStore(self, store=None):
        action = ActionChains(self.driver)
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.uitemp_search_xpath))).click()        
        try:
            pop_up = self.wait.until(EC.presence_of_element_located((By.XPATH, "//div[@class='x-window-body x-window-body-default x-closable x-window-body-closable x-window-body-default-closable x-noborder-trbl x-resizable x-window-body-resizable x-window-body-default-resizable']")))
        except:
            pop_up = self.driver.find_element(By.XPATH, "//div[@class='x-window-body x-window-body-default x-closable x-window-body-closable x-window-body-default-closable x-noborder-trbl x-resizable x-window-body-resizable x-window-body-default-resizable']")
            logger.info("In Except - it was able to get popup on second try...")
        text_box = pop_up.find_element(By.XPATH, ".//input[contains(@class, 'x-form-field x-form-text x-form-text-default  x-form-empty-field x-form-empty-field-default')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", text_box)
        text_box.send_keys(store)
        text_box.send_keys(Keys.ENTER)
        sleep(50) ### need to find a way to ensure the search completed...
        try:
            product_id = pop_up.find_element(By.XPATH, f".//div[contains(text(), '{storeName}')]")
        except:
            raise Exception("Store could not be selected")
        action.double_click(product_id).perform()
        sleep(1)

    def validateSNS(self, languages:list=None, csv=None, brand_id_csv=None, brand_name=None):
        """Fetch the latest SNS and compare data against the existing data stored
        Args:
            in_data (_type_): _description_
        """
        if csv:
            self.id = brand_id_csv
            self.data['name'] = {'en': brand_name[0]}
        sns = self.get_sns(self.id)
        # sns = self.data  ### for testing
        print("Validating SNS/S3 data")

        assert "name" in self.data and "name" in sns
        self.validate_multi_lang(self.data['name'], sns['name'], languages)

        if "image" in self.data:
            validate_input_in_output(self.data['image'], sns['image'], "image")

    def validate_multi_lang(self, payload_entry, payload_sns, languages):
        if not languages:
            assert payload_entry['en'] == payload_sns['en']
        else:
            for language in languages:
                if language not in payload_entry:
                    assert payload_entry['en'] == payload_sns[language] #validate default language propagates to others
                else:
                    assert payload_entry[language] == payload_sns[language]

        
    def uploadImage(self, filename, aspectRatio:AspectRatioType, appendImageData=True):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        current_dir = os.path.dirname(script_dir)
        image_path = os.path.join(current_dir, "images", filename)
        print("the image path: ", image_path)
        width, height = imagesize.get(image_path)
        converted_ratio = aspectRatio.replace(":", "_")

        aspectRatio_to_xpath = {
            "1:1": "Brand Image 1:1 (100x100) ",
            "16:9": "Brand Image 16:9 (178x100) "
        }

        if appendImageData:
            if "image" not in self.data:
                self.data['image'] = []
            self.data['image'].append({
                "aspectRatio": aspectRatio,
                "height": height,
                "width": width,
                "type": "brand_image-"+converted_ratio,
                "mediaType": "Image",
                "S3bucketlocation": {
                    "bucketName": "pac-marketplace-ground-tool-assets-bucket",
                    "key": "assets/"
               }
            })

        current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)     
        current_page.find_element(By.XPATH, ".//span[contains(text(),'Assets')]").click()
        
        try:
            print(f"Checking if the image field is expanded {aspectRatio}..")
            xpath = f"//div[contains(text(), '{aspectRatio_to_xpath[aspectRatio]}')]/ancestor::div[2]//div[contains(@class, 'x-tool-collapse-top')]"
            element = WebDriverWait(self.driver, 1).until(EC.presence_of_element_located((By.XPATH, xpath)))
            print(f"Element found1! aspectRatio {aspectRatio}")
        except:
            print("Element did find x-tool-collapse-top within 1 seconds. trying to expanded it")
            xpath = f"//div[contains(text(), '{aspectRatio_to_xpath[aspectRatio]}')]/ancestor::div[2]//div[contains(@class, 'x-tool-expand-bottom')]"
            # element = WebDriverWait(self.driver, 1).until(EC.visibility_of_element_located((By.XPATH, xpath)))
            element = WebDriverWait(self.driver, 1).until(EC.element_to_be_clickable((By.XPATH, xpath)))
            element.click() 
            print(f"expanded the image dropdown! {aspectRatio}")

        print("found it")
        self.click_pimcore_icon_upload(aspectRatio_to_xpath[aspectRatio])
        script_dir = os.path.dirname(os.path.abspath(__file__))
        print("waiting 2 sec")
        sleep(2)
        self.upload_file(image_path, 10)

        try:
            print(f"collpasing image field.. {aspectRatio}")
            xpath = f"//div[contains(text(), '{aspectRatio_to_xpath[aspectRatio]}')]/ancestor::div[2]//div[contains(@class, 'x-tool-collapse-top')]"
            element = WebDriverWait(self.driver, 1).until(EC.presence_of_element_located((By.XPATH, xpath))).click()
            print(f"succesfully to minimized the image {aspectRatio}")
        except:
            print(f"failed to minimize the image to be uploaded {aspectRatio}")

    def click_pimcore_icon_upload(self, aspectRatio_xpath):

        """
        Finds and clicks only the visible upload button with the specified classes.
        
        Args:
            driver: Selenium WebDriver instance
            timeout: Maximum time to wait for elements (in seconds)
            
        Returns:
            True if successful, False otherwise
        """

        try:
            print(f"Clicking the upload button.. {aspectRatio_xpath}")
            xpath = f"//div[contains(text(), '{aspectRatio_xpath}')]/ancestor::div[5]//a[contains(@class, 'x-btn pimcore_inline_upload x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')]"
            element = WebDriverWait(self.driver, 1).until(EC.presence_of_element_located((By.XPATH, xpath))).click()
            print(f"succesfully to clicked upload button ")
            return True
        except:
            print(f"failed to clicked upload button.... ")
            return False

    

    def upload_file(self, file_path, timeout=10):
        """
        Upload a file to the file input element on the page using class selectors.
        
        Args:
            driver (webdriver): Selenium WebDriver instance
            file_path (str): Absolute path to the file to upload
            timeout (int, optional): Maximum time to wait for elements. Defaults to 10.
            
        Returns:
            bool: True if upload was successful, False otherwise
            
        Raises:
            FileNotFoundError: If the provided file_path doesn't exist
        """
        # Verify file exists
        if not os.path.isfile(file_path):
            raise FileNotFoundError(f"The file {file_path} does not exist")


        current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)     
        dialog_box = WebDriverWait(current_page, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[@role='dialog' and @aria-hidden='false']"))
        )
        try:
            file_input = dialog_box.find_element(By.CSS_SELECTOR, "input.x-form-file-input")

            file_input.send_keys(file_path)
            logger.info("succesfully send the file path")        
        except:
            print(f"Timed out waiting for upload button after {5} seconds")
            # return False
    
    def get_image_dimensions(image_path):
        # Get width and height - very lightweight operation
        width, height = imagesize.get(image_path)
        return width, height

    def validationDuplicateNames(self):
        wait = WebDriverWait(self.driver, 15)
        message_pop_up = wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="A brand already exists with same name."]')))
        assert message_pop_up.is_displayed()
        logger.debug("Duplicate brand name validation message was displayed")
        sleep(2)
        message_pop_up.find_element(By.XPATH, ".//..//..//..//span[text()='OK']").click()

    def validateCharaterNameLimit(self, char_count=50):
        wait = WebDriverWait(self.driver, 15)
        message_pop_up = wait.until(EC.visibility_of_element_located((By.XPATH, f'//div[text()="Brand name should not exceed {char_count} characters."]')))
        assert message_pop_up.is_displayed()
        logger.debug("Character brand name validation message was displayed")
        sleep(1)
        message_pop_up.find_element(By.XPATH, ".//..//..//..//span[text()='OK']").click()

    def validateUnableUnpublishUsedBrand(self):
        wait = WebDriverWait(self.driver, 15)
        message_pop_up = wait.until(EC.visibility_of_element_located((By.XPATH, f'//div[contains(text(),"Brand cannot be  unpublished because is associated with products.")]')))
        assert message_pop_up.is_displayed()
        logger.debug("Validation 'Brand cannot be unpublished because is associated with products.' displayed")
        sleep(1)
        message_pop_up.find_element(By.XPATH, ".//..//..//..//span[text()='OK']").click()

        ## the way file name is send fo csv
        # driver.find_element(By.XPATH, '//input[@name="csvFile"]').send_keys(test_filename)
        # sleep(10)
        # driver.find_element(By.XPATH, '//span[text()="Upload"]').click()

    def reload_screen(self):
        self.driver.refresh()
        wait(2)

    def open_brand_folder(self):
        self.wait.until(EC.visibility_of_element_located((By.XPATH, f"//span[contains(text(),'Brands')]"))).click()

    def get_name(self):
        name_field = self.wait.until(EC.element_to_be_clickable((By.NAME, constant.name_name)))
        name_in_ui = name_field.get_attribute("value")
        return name_in_ui

    def search_brand_within_folder(self, brand_name=None, max_retries=8, second_tab=None):
        action = ActionChains(self.driver)
        attempts = 0
        sleep(2)
        while attempts < max_retries:
            try:
                print(f"Attempt {attempts + 1}/{max_retries}: Searching for brand '{brand_name}'")
                if second_tab:
                    self.wait.until(
                        EC.visibility_of_element_located(
                            (
                                By.XPATH,
                                "(//span[contains(@class, 'pimcore_material_icon_reload')])[2]",
                            )
                        )
                    ).click()
                else:
                    self.wait.until(
                        EC.visibility_of_element_located(
                            (
                                By.XPATH,
                                "//span[contains(@class, 'pimcore_material_icon_reload')]",
                            )
                        )
                    ).click()
                search_box = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))
                )
                search_box.send_keys(brand_name)
                action.send_keys(Keys.RETURN).perform()
                print("Waiting for obj_id to be clickable...")
                obj_id = WebDriverWait(self.driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, f"(//div[contains(text(),'/{STORE_NAME}/Brands/')])[1]"))
                )
                print("Found obj_id, performing context-click...")
                action.context_click(obj_id).perform()
                print("Search successful, obj_id found.")
                self.wait.until(
                    EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
                try:
                    element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
                    element.click()
                except NoSuchElementException:
                    pass
                return True
            except TimeoutException:
                print(f"Attempt {attempts + 1} failed: Timeout while waiting for obj_id.")
            except Exception as e:
                print(f"Attempt {attempts + 1} failed: {e}")
            attempts += 1
            if attempts >= max_retries:
                print("Max retries reached, obj_id not found.")
                break
        print("Opened latest object from folder in UI")

    def close_all_tabs(self):
        action = ActionChains(self.driver)
        csv = self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//span[contains(text(),'CSV Import')])[1]")))
        action.context_click(csv).perform()
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Close All"))).click()
        print("Closing all tabs")
        wait(1)

    def select_brand(self, single_export=None, multiple_export=None):
        if single_export:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-grid-checkcolumn-cell-inner')])[1]"))).click()
            print("Selected brand")
        elif multiple_export:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-grid-checkcolumn-cell-inner')])[1]"))).click()
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-grid-checkcolumn-cell-inner')])[3]"))).click()
            print("Selected multiple brands")
        else:
            print("Invalid entry")

    def find_export_csv_message(self):
        # sleep(28)
        assert self.wait.until(
            EC.presence_of_element_located((By.XPATH, '//div[contains(text(),"Export Started for Brands")]'))).is_displayed()
        # assert self.wait.until(
        #     EC.presence_of_element_located((By.XPATH, '//div[contains(text(),"Export Has been completed for Brands and a Mail has been sent to you")]'))).is_displayed()
        # print("Message pop-up came for successful export")
        print("Message pop-up came for starting export")

    def open_selected_brand(self, multiple_export=None, single_export=None, click_xpath=None):
        action = ActionChains(self.driver)
        global obj_id
        sleep(15)
        if multiple_export:
            obj_id = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, click_xpath)))
            action.context_click(obj_id).perform()
        elif single_export:
            obj_id = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"(//div[contains(text(),'/{STORE_NAME}/Brands/')])[1]")))
            action.context_click(obj_id).perform()
        else:
            print("Invalid entry")
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened selected brand to verify details")

    def click_reload_button(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//span[contains(@class,'pimcore_material_icon_reload')]"))).click()
        print("Clicked for reload")
        sleep(1)

    def verify_brand(self, obj=None, brand_xpath=None, name_xpath=None, index_text=None):
        self.open_selected_brand(multiple_export=True, click_xpath=brand_xpath)
        print(f"Verifying details of {index_text} brand selected")
        brand_id = self.getObjectID()
        id = int(str(brand_id).split()[-1])
        for brand in obj:
            if brand['brand_id'] == id:
                print(f"Brand present: {brand}")
                name_field = self.wait.until(EC.element_to_be_clickable((By.XPATH, name_xpath)))
                name_in_ui = name_field.get_attribute("value")
                if brand['name_en'] == name_in_ui:
                    print("Names matched from UI and CSV.")
                return
        raise AssertionError(f"Brand with ID {id} not present in the list.")

    def open_multiple_selected_brand(self, obj=None):
        self.verify_brand(obj,brand_xpath=f"(//div[contains(text(),'/{STORE_NAME}/Brands/')])[1]",
            name_xpath=constant.name_xpath1,index_text="first")
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "(//span[contains(text(),'Brands')])[1]"))).click()
        self.verify_brand(obj,brand_xpath=f"(//div[contains(text(),'/{STORE_NAME}/Brands/')])[2]",
            name_xpath=constant.name_xpath2,index_text="second")

    def open_unpublished_brand_within_folder(self, brand_name=None):
        action = ActionChains(self.driver)
        sleep(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(brand_name)
        action.send_keys(Keys.RETURN).perform()
        sleep(3)
        obj_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, f"(//div[contains(text(),'/{STORE_NAME}/Brands/')])[1]")))
        obj_id.is_displayed()
        print("Searched for the unpublished brand from folder")

    def find_error_msg_for_unpublished(self):
        assert WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//div[contains(text(),"Please Select only Published Brands")]'))).is_displayed()
        print("Error message pop-up came for unpublished brand")

    def click_ok_button(self):
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, "OK")))
        ok_button.click()
        print("Clicked ok button")

