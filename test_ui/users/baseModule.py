from .basePage import basePage
from selenium.webdriver.common.by import By
from driver_setup.helper import *
from selenium.common import NoSuchElementException
from selenium.webdriver.support import expected_conditions as EC
from project_configs.env_ui_constants import constant


class baseModule(basePage):
    def __init__(self, driver):
        super().__init__(driver)
    
    def getObjectID(self):
        sleep(2)  ## Allow some propagation incase something else is opened
        id_str = self.driver.find_element(By.XPATH, self.PANEL_XPATH).find_element(By.XPATH,".//div[contains(@class,'x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default')]").text
        print("the text from the ID: "+ id_str)
        id = int("".join(char for char in id_str if char.isdigit()))
        self.id = id
        return id

    def selectLanguage(self, lang):
        """
        Selects a Language by clicking on its tab in the UI.
        First attempts to find it by scrolling right, then left if not found.
        """        
        LANGUAGE_XPATH = f".//span[text()='{lang}']"
        EDITOR_XPATH = ".//div[@class='x-tab-bar x-docked x-tab-bar-default x-horizontal x-tab-bar-horizontal x-tab-bar-default-horizontal x-top x-tab-bar-top x-tab-bar-default-top x-docked-top x-tab-bar-docked-top x-tab-bar-default-docked-top x-noborder-trl x-scroller x-tab-bar-scroller x-tab-bar-default-scroller']"
        SCROLL_XPATHS = {
            "right": ".//div[contains(@class,'x-box-scroller x-box-scroller-right x-box-scroller-tab-bar x-box-scroller-tab-bar-default')]",
            "left": ".//div[contains(@class,'x-box-scroller x-box-scroller-left x-box-scroller-tab-bar x-box-scroller-tab-bar-default')]"
        }
        
        def try_click_language():
            """Find currency element and click if visible"""
            element = self.driver.find_element(By.XPATH, self.PANEL_XPATH).find_element(By.XPATH, LANGUAGE_XPATH)
            if element.is_displayed():
                logger.info(f"Language {lang} found and clicked")
                element.click()
                return True
            else:
                logger.info(f"Didn't find the language {lang}")
            return False
        
        def scroll_and_search( direction):
            """Scroll in given direction and search for currency"""
            try:
                logger.info(f"Starting to search for {lang} in direction {direction}")
                panel = self.driver.find_element(By.XPATH, self.PANEL_XPATH)
                for i in range(200):
                    logger.info(f"Try {i}")
                    try:
                        if try_click_language():
                            return True
                    except NoSuchElementException:
                        logger.info(f"NoSuchElementException  in try: {i}")
                        pass
                    try:
                        language_scrollable = panel.find_element(By.XPATH, EDITOR_XPATH)
                        language_scrollable.find_element(By.XPATH, SCROLL_XPATHS[direction]).click()
                        sleep(0.05)
                    except Exception:
                        logger.warning(f"Error clicking {direction} arrow")
                        break
                        
                logger.info(f"Currency not found after scrolling {direction}")
                return False
            except Exception as e:
                logger.warning(f"Error with {direction} scroll: {str(e)}")
                if direction == "left":
                    raise Exception("Failed to find the language in both directions...")
                return False
        return scroll_and_search("right") or scroll_and_search("left")

    def get_sns(self, id):
        print("searching for id:", id)
        return get_sns_message_by_id_catalog(int(id), self.QUEUE_URL)
    
    def validateUnpublishSNS(self):
        print("searching for id:", self.id)
        sns =  get_sns_message_by_id_catalog(int(self.id), self.QUEUE_URL)
        assert self.id == sns["id"]


    def open_base_data(self):
        if (self.PANEL_XPATH):
            current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)     
            # current_page.find_element(By.XPATH, ".//span[@class='x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_delete
            current_page.find_element(By.XPATH, ".//span[contains(text(),'Base Data')]").click()
        else:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.basedata_xpath))).click()
            print("Opened basedata tab")
