from time import sleep
from selenium.common import NoSuchElementException
from modules.product import product
from project_configs.csv_config import Config
from users.baseUser import baseUser
from selenium.webdriver.common.by import By
from selenium.webdriver import ActionChains, Keys
from project_configs.env_ui_constants import constant
from project_configs.login_helpers import wait, automap_off
from modules.brand import brand
from selenium.webdriver.support import expected_conditions as EC

class pacAdmin(baseUser):
    def __init__(self, driver, user, password, e):
        super().__init__(driver, user, password, e)

    def home_add_object(self):
        action = ActionChains(self.driver)
        home = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.home_xpath)))
        action.context_click(home).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        print("Add new object from home")

    def get_list_language(self, languages_codes_configured, languages_map):
        list_of_languages = []
        for code in languages_codes_configured:
            list_of_languages.append(languages_map[code])
        return list_of_languages

    def navigate_to_store_folder_and_select_languages(self, LANGUAGES_CODES_CONFIGURED=None, LANGUAGE_MAP=None,
                                                      dev=None, store=None):
        action = ActionChains(self.driver)
        # pac stack dont have
        if dev:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
            wait(5)
            self.wait.until(
                EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.store_text)
            action.send_keys(Keys.RETURN).perform()
            sleep(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.storesfolder_xpath))).click()
        action.send_keys(Keys.RETURN).perform()
        sleep(3)
        search_input = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.search_input_name)))
        search_input.send_keys(store)
        sleep(5)
        action.send_keys(Keys.RETURN).perform()
        sleep(10)
        select_store = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//table[contains(@id,'patchedgridview-')]")))
        action.context_click(select_store).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        sleep(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        # self.wait.until(
        #     EC.element_to_be_clickable((By.XPATH, constant.store_brand_xpath))).click()
        # try:
        #     element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
        #     element.click()
        # except NoSuchElementException:
        #     pass
        print("Opened store in pac admin")
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.config_tab_text))).click()
        languages_to_select = self.get_list_language(LANGUAGES_CODES_CONFIGURED, LANGUAGE_MAP)
        dropdown_trigger = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "(//div[contains(@id,'-trigger-picker')])[7]"))
        )
        dropdown_trigger.click()
        dropdown_list = self.wait.until(
            EC.presence_of_element_located((By.XPATH, "//ul[contains(@class, 'x-list-plain') and @role='listbox']"))
        )
        language_elements = dropdown_list.find_elements(By.XPATH, "./li")
        print(f"Number of language <li> elements found: {len(language_elements)}")
        for idx, element in enumerate(language_elements):
            text = self.driver.execute_script("return arguments[0].textContent;", element).strip()
            print(f"Language {idx}: '{text}'")
            if text in languages_to_select:
                is_selected = (
                        'x-selected' in element.get_attribute("class") or
                        element.get_attribute("aria-selected") == "true" or
                        element.get_attribute("aria-checked") == "true"
                )
                if is_selected:
                    print(f"--> Already selected: {text}")
                else:
                    print(f"--> Clicking: {text}")
                    element.click()
        self.save_and_publish()
        return brand(self.driver, csv=True)

    def pac_brand_csv_upload(self, csv_path=None, store=None):
        action = ActionChains(self.driver)
        self.import_csv()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.brand_text)
        wait(1)
        action.send_keys(Keys.RETURN).perform()
        wait(3)
        dropdown_btn_xpath = "(//div[contains(@id,'storeList')])[5]"
        self.wait.until(EC.element_to_be_clickable((By.XPATH, dropdown_btn_xpath))).click()
        self.wait.until(EC.visibility_of_element_located((By.ID, "storeList-picker-listEl")))
        option_xpath = f"//ul[contains(@id,'storeList-picker-listEl')]//child::li[contains(text(),'{store}')]"
        option = self.wait.until(EC.element_to_be_clickable((By.XPATH, option_xpath)))
        option.click()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(
            Config.get_path(csv_path))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new Brand")

    def verify_env(self, store=None):
        action = ActionChains(self.driver)
        env = self.e.get('environment')
        if env == "dev":
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
            self.wait.until(
                EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(store)
            action.send_keys(Keys.RETURN).perform()

    def navigate_to_brand_folder_in_pac(self, store=None, update=None, pac_update=None):
        # pacstack dont have search after clearing contents
        if update:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//span[contains(text(),'Brands')]"))).click()
            sleep(3)
        elif pac_update:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//span[contains(text(),'Brands')]"))).click()
            sleep(3)
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//span[contains(text(),'{store}')]//parent::div//div[contains(@class,'expander')]"))).click()
            sleep(5)
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//span[contains(text(),'Brands')]"))).click()
            sleep(3)
        print("Opened brand folder")
        return brand(self.driver, csv=True)

    def navigate_to_sns(self, autoLogin=False, add=False, update_unpublish=False):
        global creation_date
        if autoLogin:
            self.login()
            print("Admin login successful")
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.assets_tab_id))).click()
        logexpander = self.wait.until(
            EC.presence_of_element_located(
                (By.XPATH, constant.log_folder_expander)
            )
        )
        self.driver.execute_script("arguments[0].scrollIntoView();", logexpander)
        self.driver.find_element(
            By.XPATH,
            constant.log_folder_expander,
        ).click()
        sleep(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.triggeredsns_folder_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.list_btn_text))).click()
        print("Navigation to latest sns is done")
        wait(10)
        if add:
            creation_date = self.wait.until(
                EC.presence_of_element_located((By.XPATH, constant.creation_date_column_xpath)))
            print("Clicked for add - sns")
        elif update_unpublish:
            creation_date = self.wait.until(
                EC.presence_of_element_located((By.XPATH, constant.creation_date_column_after_update_xpath)))
            print("Clicked for update/unpublish - sns")
        else:
            creation_date = self.wait.until(
                EC.presence_of_element_located((By.XPATH, constant.creation_date_column_xpath)))
            print("Clicked for sns")
        self.driver.execute_script("arguments[0].scrollIntoView();", creation_date)
        action.double_click(creation_date).perform()
        wait(10)

    def verify_sns(self, id=None, save=None, unpublish=None, sns_published=None, sns_unpublished=None, sns_id_chk=None, unpublish_sns_id_chk=None, flight=None):
        global cleaned_text
        action = ActionChains(self.driver)
        sleep(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.triggered_sns_tab_xpath))).click()
        if save:
            sns = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, sns_published)))
            action.context_click(sns).perform()
            self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
            wait(5)
            sns_id_chk1 = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, sns_id_chk))).text
            # print("value1_to_chk = " + sns_id_chk1)
            if flight:
                cleaned_text = sns_id_chk1.strip('"')
            else:
                cleaned_text = sns_id_chk1
            print("value1_to_chk = " + cleaned_text)
            print("SNS id captured")
        elif unpublish:
            sns = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, sns_unpublished)))
            action.context_click(sns).perform()
            self.wait.until(
                EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
            wait(8)
            sns_id_chk2 = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, unpublish_sns_id_chk))).text
            # print("value2_to_chk = " + sns_id_chk2)
            cleaned_text = sns_id_chk2
            print("value2_to_chk = " + cleaned_text)
            print("SNS id captured")
        else:
            print("Invalid sns")
            # wait(5)
            # sns_id_chk3 = self.wait.until(
            #     EC.element_to_be_clickable((By.XPATH, constant.sns_id_xpath))).text
            # print("value3_to_chk = " + sns_id_chk3)
            # cleaned_text = sns_id_chk3.strip('"')
            # print("SNS id captured")
        if (cleaned_text in id):
            print("SNS has been verified successfully!")
            return True
        else:
            return False

    def verify_sns_and_download(self, id=None, save=None, crct_sns=None, unpublish=None, sns_unpublished=None, sns_published=None):
        if crct_sns == False:
            if save:
                for i in range(2,10):
                    count = i
                    if count >= 2:
                        print("count = " + str(count))
                        sleep(5)
                        self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, constant.triggered_sns1_tab_xpath))).click()
                        new_sns_value = sns_published + "[" + str(count) + "]"
                        new_sns_id = constant.sns_id_check_xpath + "[" + str(count) + "]"
                        sns = self.verify_sns(id, save, sns_published=new_sns_value, sns_id_chk=new_sns_id)
                        print("Opened sns published (Retry - " + str(count) + ")")
                        if sns:
                            break
            elif unpublish:
                for i in range(2,10):
                    count = i
                    if count >= 2:
                        print("count = " + str(count))
                        sleep(5)
                        self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, constant.triggered_sns1_tab_xpath))).click()
                        new_sns_value = sns_unpublished + "[" + str(count) + "]"
                        new_sns_id = constant.unpublish_sns_id + "[" + str(count) + "]"
                        sns = self.verify_sns(id, unpublish, sns_published=new_sns_value, sns_id_chk=new_sns_id)
                        print("Opened sns unpublished (Retry - " + str(count) + ")")
                        if sns:
                            break
            else:
                print("Invalid state")
        else:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.sns_download_xpath))).click()
            print("SNS downloaded")

    def navigate_and_airline_automap_off(self):
        action = ActionChains(self.driver)
        wait(10)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_home_xpath_update))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.filterfromhome_name))).send_keys(constant.airline_folder_text)
        action.send_keys(Keys.RETURN).perform()
        wait(25)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_main_folder_expander_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_folder_search_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_folder_filter_xpath))).send_keys(
            constant.airline_value)
        action.send_keys(Keys.RETURN).perform()
        automap_off(self.driver)
        print("In airline profile - turned off autopopulate")
        
    def createBrand(self, objName, closePreviouslyOpenedTabs=True):
        if closePreviouslyOpenedTabs:
            self.closeAllTabs()
        action = ActionChains(self.driver)
        home = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.home_xpath)))
        sleep(1)
        action.context_click(home).perform()
        sleep(1)
        
        add_object = self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        sleep(1)
        action.move_to_element(add_object).perform()
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Brands"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_object_name_id))).send_keys(objName)
        ok_button = self.wait.until(EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        return brand(self.driver)

    def create_product_pac(self, sku=None, category="store_automation/category_1", product_type="Simple", store=None):
        if not sku:
            sku = constant.pdtsku_value + "_1"
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.productcreate_btn_text))).click()
        sleep(5)
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_sku_name))).send_keys(sku)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_choose_xpath))).click()
        self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//li[contains(text(),'{store}')]"))).click()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "(//div[contains(@class,'x-form-trigger x-form-trigger-default x-form-arrow-trigger x-form-arrow-trigger-default ')])[3]"))).click()
        category_xpath = f"//li[contains(text(),'{category}')]"
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, category_xpath))).click()
        product_set_select = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_set_select_name)))
        product_set_select.send_keys(product_type)
        product_set_select.send_keys(Keys.RETURN)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.ok_btn_value))).click()
        print("Added new product object")
        sleep(2)
        return product(self.driver, populateId=True)

