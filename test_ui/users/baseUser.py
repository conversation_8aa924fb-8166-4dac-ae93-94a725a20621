import time, random
import json, boto3, os
from time import sleep
from users.basePage import basePage
from shared_config.config import logger
from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By
from openapi_schema_validator import validate
from jsonschema.validators import RefResolver
from project_configs.login_helpers import wait
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from project_configs.ui_helper import check_succesfull_login
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from faker import Faker

class baseUser(basePage):
    _GLOBAL_SEED = random.randint(1, 1000000)
    _instance_counter = 0

    def __init__(self, driver, user, password, e):
        super().__init__(driver)
        self.e = e
        self.user = user
        self.password = password
        self.init_login(user=user, password=password, e=e)

        self.faker = Faker()
        unique_seed = int(time.time() * 1000) & 0xFFFFFFFF
        self.faker.seed_instance(unique_seed)
        self.generated_brands = set()
        self.generated_products = set()

        baseUser._instance_counter += 1
        instance_seed = baseUser._GLOBAL_SEED + baseUser._instance_counter
        self.faker.seed_instance(instance_seed)
        
        # Keep track of previously generated products to ensure uniqueness
        self.generated_products = set()


    def init_login(self, user, password, e):
        self.driver.maximize_window()
        self.driver.get(e.get('qa_home_page_url'))
        sleep(1)
        pimcore_username = self.wait.until(EC.element_to_be_clickable((By.NAME, constant.username_txt_name)))
        pimcore_username.send_keys(user)
        pimcore_password = self.wait.until(EC.element_to_be_clickable((By.NAME, constant.password_txt_name)))
        pimcore_password.send_keys(password)
        start_time = time.time()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.login_btn_xpath))).click()
        print("\nUser Login was successful")
        # try:
        #     element = self.driver.find_element(By.LINK_TEXT, "Details")
        #     element.click()
        # except NoSuchElementException:
        #     pass
        # check_succesfull_login(self.driver, start_time=start_time)

    def logout(self):
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.logout_id))).click()
        print("Logged out")
        sleep(15)
        self.driver.refresh()
        sleep(3)

    def import_csv(self):
        sleep(3)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csvimport_btn_text))).click()
        print("To Import CSV")
        sleep(10)

    def get_latest_from_folder(self, obj_xpath=None):
        action = ActionChains(self.driver)
        sleep(10)
        click_column = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.creationdate_column_xpath_update)))
        action.double_click(click_column).perform()
        sleep(15)
        obj_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, obj_xpath)))
        action.context_click(obj_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        sleep(5)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened latest object from folder in UI")
        sleep(5)

    def resetVisibilityTimeout(self, recept_handles:list, queue_url, sqs):
        for receipt_handle in recept_handles:
            sqs.change_message_visibility(
                QueueUrl=queue_url,
                ReceiptHandle=receipt_handle,
                VisibilityTimeout=0
            )
        print(f"Set {len(recept_handles)} SQS ReceiptHandles active to be read")

        
    def get_sns_message_by_object_id(self, id, queue_url=None, retries=5):
        sqs=boto3.client('sqs', region_name="us-west-2")  #
        s3=boto3.client("s3")
        for i in range(retries):
            recept_handles =[]
            try:
                while True:
                    messages = sqs.receive_message(
                        QueueUrl=queue_url,
                        MessageAttributeNames=["All"],
                        MaxNumberOfMessages=10,
                        WaitTimeSeconds=3,
                    )
                    logger.debug("Looking through set of SQS messages")
                    if 'Messages' in messages:
                        for msg in messages['Messages']:# 
                            sqs_body = json.loads(msg['Body'])
                            if 'MessageAttributes' in sqs_body:
                                sqs_MessageAttributes = sqs_body['MessageAttributes']
                                # logger.info("Message attributes: %s", sqs_MessageAttributes)
                                logger.info("Looking for ID %s, Curent Message attribute id: %s", id , sqs_MessageAttributes['Id']['Value'])
                                if int(sqs_MessageAttributes['Id']['Value']) == int(id):
                                    logger.debug(f"Found the ID {id} ")
                                    message_body = json.loads(sqs_body['Message'])
                                    if 'Bucket' in message_body:
                                        # print("the sqs bucket: ", message_body['Bucket'])
                                        response = s3.get_object(Bucket=message_body['Bucket'], Key=message_body['Key'])
                                        content = response['Body'].read().decode('utf-8')
                                        sqs.delete_message(QueueUrl=queue_url, ReceiptHandle=msg["ReceiptHandle"]) #remove message fromthe queue
                                        sns_data = json.loads(content)
                                        logger.debug(f"The SNS payload data for ID {id}: \n" + json.dumps(sns_data, indent=5))
                                        self.resetVisibilityTimeout(recept_handles, queue_url, sqs)
                                        return sns_data
                                else:
                                    recept_handles.append(msg["ReceiptHandle"]) 
                            else:
                                logger.debug(f"Did not find the MessageAttributes in attempt {i+1}")       
                                recept_handles.append(msg["ReceiptHandle"])           
                    else:
                        logger.info("No more messages to process. Exiting while loop.")
                        break
            except Exception as error:
                logger.exception("Couldn't receive messages from queue")
                raise error
            random_delay = random.randint(5, 10)
            logger.debug(f"Couldn't find the ID in attempt # {i+1}... Sleeping for {random_delay} seconds before checking again")
            self.resetVisibilityTimeout(recept_handles, queue_url, sqs)
            sleep(random_delay)
        raise Exception(f"Could not find the sns message sent id: {id}")

    def validate_schema(self, sns_data=None, object=None):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        for _ in range(2):
            current_dir = os.path.dirname(current_dir)
        schema_json_file_path = os.path.join(current_dir, 'sns_payload_definitions.json')
        print("Json schema file path is: ",schema_json_file_path)
        schema_json_file = open(schema_json_file_path, 'r')
        print("Opened the json file for schema validation")
        schema_contents = schema_json_file.read()
        schema = json.loads(schema_contents)
        ref_resolver = RefResolver.from_schema(schema)
        validate(sns_data, schema['components']['schemas'][object], resolver=ref_resolver)
        logger.info("Data validation passed")
        print("Data validation passed")
        schema_json_file.close()

    def get_sns_message_by_object_id_ca(id, queue_url=None, retries=5):
            sqs = boto3.client('sqs', region_name="us-west-2")  #
            s3 = boto3.client("s3")
            for i in range(retries):
                try:
                    while True:
                        messages = sqs.receive_message(
                            QueueUrl=queue_url,
                            MessageAttributeNames=["All"],
                            MaxNumberOfMessages=10,
                            WaitTimeSeconds=10,
                        )
                        # print(sqs)
                        print("Looking through set of SQS messages")
                        if 'Messages' in messages:
                            for msg in messages['Messages']:  #
                                sqs_body = json.loads(msg['Body'])
                                if 'MessageAttributes' in sqs_body:
                                    sqs_MessageAttributes = sqs_body['MessageAttributes']
                                    # logger.info("Message attributes: %s", sqs_MessageAttributes)
                                    print("Message attributes id: %s", sqs_MessageAttributes['Id']['Value'])
                                    if int(sqs_MessageAttributes['Id']['Value']) == int(id):
                                        print(f"Found the ID {id} ")
                                        message_body = json.loads(sqs_body['Message'])
                                        if 'Bucket' in message_body:
                                            # print("the sqs bucket: ", message_body['Bucket'])
                                            response = s3.get_object(Bucket=message_body['Bucket'],
                                                                     Key=message_body['Key'])
                                            content = response['Body'].read().decode('utf-8')
                                            sqs.delete_message(QueueUrl=queue_url, ReceiptHandle=msg[
                                                "ReceiptHandle"])  # remove message fromthe queue
                                            sns_data = json.loads(content)
                                            print(
                                                f"The SNS payload data for ID {id}: \n" + json.dumps(sns_data,
                                                                                                     indent=5))
                                            return sns_data
                                else:
                                    print(f"Did not find the MessageAttributes in attempt {i + 1}")
                        else:
                            print("No more messages to process. Exiting while loop.")
                            break
                except Exception as error:
                    print("Couldn't receive messages from queue")
                    raise error
                print(f"Couldn't find the ID in attempt # {i + 1}... Sleeping for 5 seconds before checking again")
                sleep(5)
            raise Exception(f"Could not find the sns message sent id: {id}")

    def validate_schema_ca(sns_data=None, object=None):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        for _ in range(2):
            current_dir = os.path.dirname(current_dir)
        schema_json_file_path = os.path.join(current_dir, 'sns_payload_definitions.json')
        print("Json schema file path is: ",schema_json_file_path)
        schema_json_file = open(schema_json_file_path, 'r')
        print("Opened the json file for schema validation")
        schema_contents = schema_json_file.read()
        schema = json.loads(schema_contents)
        ref_resolver = RefResolver.from_schema(schema)
        validate(sns_data, schema['components']['schemas'][object], resolver=ref_resolver)
        logger.info("Data validation passed")
        print("Data validation passed")
        schema_json_file.close()




    def generate_faked_brand_name(self, style=None, max_attempts=100):
        """Generate a unique random brand name.
        Args:
            style: Optional style of brand name to generate
                  ('tech', 'food', 'luxury', 'acronym', or None for random)
            max_attempts: Maximum number of attempts to generate a unique brand
        Returns:
            A unique brand name string
        """
        for _ in range(max_attempts):
            if style is None:
                style = self.faker.random_element(['tech', 'food', 'luxury', 'acronym', 'generic'])
                
            if style == 'tech':
                prefixes = ['Smart', 'Tech', 'Cyber', 'Data', 'Logic', 'Code', 'Cloud']
                suffixes = ['Labs', 'Systems', 'Tech', 'Ware', 'Hub', 'Byte', 'Soft']
                brand = f"{self.faker.random_element(prefixes)}{self.faker.random_element(suffixes)}"
                
            elif style == 'food':
                prefixes = ['Fresh', 'Tasty', 'Golden', 'Green', 'Sweet', 'Natural']
                suffixes = ['Eats', 'Foods', 'Bites', 'Harvest', 'Kitchen', 'Farms']
                brand = f"{self.faker.random_element(prefixes)} {self.faker.random_element(suffixes)}"
                
            elif style == 'luxury':
                brand = f"{self.faker.last_name()} & {self.faker.last_name()}"
                
            elif style == 'acronym':
                letters = ''.join(self.faker.random_letters(length=self.faker.random_int(2, 4))).upper()
                brand = letters
            else:  # generic
                brand = self.faker.company()            
            # if brand not in self.generated_brands: ### we cant do this due to the previous brands that may exist
            #     self.generated_brands.add(brand)
            #     return brand                
        return f"{brand}-{random.randint(1000, 9999)}"
    


    def generate_product(self, category=None, max_attempts=100):
        """Generate a unique random product name.
        
        Args:
            category: Optional category of product to generate
                    ('electronics', 'clothing', 'home', 'beauty', 'food', or None for random)
            max_attempts: Maximum number of attempts to generate a unique product
                  
        Returns:
            A unique product name string
        """
        for _ in range(max_attempts):
            if category is None:
                category = self.faker.random_element(['electronics', 'clothing', 'home', 'beauty', 'food', 'sports'])
                
            if category == 'electronics':
                prefixes = ['Ultra', 'Pro', 'Smart', 'Elite', 'Quantum', 'Digital', 'Cyber']
                product_types = ['Phone', 'Tablet', 'Laptop', 'TV', 'Speaker', 'Watch', 'Headphones', 'Camera']
                models = ['X', 'Pro', 'Plus', 'Max', 'Air', 'Elite', f"{self.faker.random_int(1, 20)}"]
                
                # Create combinations like "UltraPhone X" or "Pro Tablet 12"
                if self.faker.boolean(chance_of_getting_true=30):
                    product = f"{self.faker.random_element(prefixes)}{self.faker.random_element(product_types)} {self.faker.random_element(models)}"
                else:
                    product = f"{self.faker.random_element(prefixes)} {self.faker.random_element(product_types)} {self.faker.random_element(models)}"
                    
            elif category == 'clothing':
                adjectives = ['Slim', 'Relaxed', 'Classic', 'Modern', 'Vintage', 'Premium', 'Essential']
                items = ['Jeans', 'T-Shirt', 'Dress', 'Jacket', 'Coat', 'Sweater', 'Shoes', 'Socks', 'Hat']
                materials = ['Cotton', 'Wool', 'Denim', 'Leather', 'Silk', 'Linen', '']
                
                if self.faker.boolean():
                    product = f"{self.faker.random_element(adjectives)} {self.faker.random_element(items)}"
                else:
                    material = self.faker.random_element(materials)
                    if material:
                        product = f"{material} {self.faker.random_element(adjectives)} {self.faker.random_element(items)}"
                    else:
                        product = f"{self.faker.random_element(adjectives)} {self.faker.random_element(items)}"
                
            elif category == 'home':
                rooms = ['Kitchen', 'Bathroom', 'Bedroom', 'Living Room', 'Office', 'Garden', '']
                items = ['Table', 'Chair', 'Lamp', 'Rug', 'Sofa', 'Shelf', 'Desk', 'Bed', 'Cabinet', 'Storage']
                materials = ['Wooden', 'Metal', 'Glass', 'Bamboo', 'Ceramic', 'Marble', 'Velvet']
                
                room = self.faker.random_element(rooms)
                if room:
                    product = f"{self.faker.random_element(materials)} {room} {self.faker.random_element(items)}"
                else:
                    product = f"{self.faker.random_element(materials)} {self.faker.random_element(items)}"
                
            elif category == 'beauty':
                prefixes = ['Revitalizing', 'Hydrating', 'Illuminating', 'Anti-Aging', 'Purifying', 'Natural']
                products = ['Cream', 'Serum', 'Lotion', 'Shampoo', 'Conditioner', 'Mask', 'Cleanser', 'Oil']
                ingredients = ['Aloe', 'Vitamin C', 'Hyaluronic Acid', 'Retinol', 'Coconut', 'Lavender', 'Rose']
                
                if self.faker.boolean():
                    product = f"{self.faker.random_element(prefixes)} {self.faker.random_element(products)}"
                else:
                    product = f"{self.faker.random_element(ingredients)} {self.faker.random_element(prefixes)} {self.faker.random_element(products)}"
                
            elif category == 'food':
                tastes = ['Spicy', 'Sweet', 'Savory', 'Tangy', 'Smoky', 'Zesty', 'Creamy']
                types = ['Sauce', 'Chips', 'Snack', 'Drink', 'Mix', 'Cookies', 'Cereal', 'Yogurt', 'Candy']
                flavors = ['Chocolate', 'Cheese', 'BBQ', 'Ranch', 'Berry', 'Vanilla', 'Cinnamon', 'Honey']
                
                if self.faker.boolean():
                    product = f"{self.faker.random_element(flavors)} {self.faker.random_element(types)}"
                else:
                    product = f"{self.faker.random_element(tastes)} {self.faker.random_element(flavors)} {self.faker.random_element(types)}"
            
            elif category == 'sports':
                prefixes = ['Pro', 'Elite', 'Ultra', 'Endurance', 'Performance', 'Tournament', 'Champion']
                items = ['Racket', 'Ball', 'Shoes', 'Gloves', 'Helmet', 'Shorts', 'Jersey', 'Socks', 'Equipment']
                sports = ['Tennis', 'Basketball', 'Soccer', 'Golf', 'Running', 'Cycling', 'Swimming', 'Fitness']
                
                if self.faker.boolean():
                    product = f"{self.faker.random_element(prefixes)} {self.faker.random_element(sports)} {self.faker.random_element(items)}"
                else:
                    product = f"{self.faker.random_element(sports)} {self.faker.random_element(prefixes)} {self.faker.random_element(items)}"
            
            else:  # generic
                product = f"{self.faker.word().capitalize()} {self.faker.word().capitalize()}"
            
            # Add a model number for some products
            if category in ['electronics', 'home'] and self.faker.boolean(chance_of_getting_true=40):
                model_number = f"{self.faker.random_letter().upper()}{self.faker.random_int(100, 9999)}"
                product = f"{product} {model_number}"
            
            # Check if this product is unique
            if product not in self.generated_products:
                self.generated_products.add(product)
                return product
                
        # If we've tried max_attempts times and still haven't found a unique product,
        # add a random number to ensure uniqueness
        return f"{product}-{random.randint(1000, 9999)}"
    
    def generate_product_with_details(self, category=None):
        """Generate a product with additional details like price and description.
        
        Args:
            category: Optional category of product to generate
            
        Returns:
            Dictionary containing product name, price, description, and other details
        """
        product_name = self.generate_product(category)
        
        # Generate price based on category
        if category == 'electronics':
            price = round(self.faker.random_int(50, 2000) - 0.01, 2)
        elif category == 'clothing':
            price = round(self.faker.random_int(15, 200) - 0.01, 2)
        elif category == 'home':
            price = round(self.faker.random_int(20, 500) - 0.01, 2)
        elif category == 'beauty':
            price = round(self.faker.random_int(5, 100) - 0.01, 2)
        elif category == 'food':
            price = round(self.faker.random_int(2, 50) - 0.01, 2)
        elif category == 'sports':
            price = round(self.faker.random_int(10, 300) - 0.01, 2)
        else:
            price = round(self.faker.random_int(10, 100) - 0.01, 2)
            
        # Generate a product description
        description = self.faker.paragraph(nb_sentences=3)
        
        # Generate SKU
        sku = f"{self.faker.random_letters(length=3).upper()}-{self.faker.random_int(10000, 99999)}"
        
        # Generate ratings
        rating = round(self.faker.random.uniform(3.0, 5.0), 1)
        review_count = self.faker.random_int(0, 1000)
        
        # Generate stock status
        in_stock = self.faker.boolean(chance_of_getting_true=80)
        stock_quantity = self.faker.random_int(0, 100) if in_stock else 0
        
        return {
            "name": product_name,
            "category": category,
            "price": price,
            "description": description,
            "sku": sku,
            "rating": rating,
            "review_count": review_count,
            "in_stock": in_stock,
            "stock_quantity": stock_quantity
        }

    def fetch_spa_csv_headers(self, LANGUAGES_CODES_CONFIGURED=None):
        base_headers = ['image']
        language_desc_headers = [f'description_{lang}' for lang in LANGUAGES_CODES_CONFIGURED]
        language_disc_headers = [f'disclaimer_{lang}' for lang in LANGUAGES_CODES_CONFIGURED]
        headers = language_desc_headers + language_disc_headers + base_headers
        return headers

    def fetch_csv_headers(self, LANGUAGES_CODES_CONFIGURED=None):
        base_headers = ['brand_image1_1','brand_image16_9', 'brand_id']
        language_name_headers = [f'name_{lang}' for lang in LANGUAGES_CODES_CONFIGURED]
        headers = language_name_headers + base_headers
        return headers

    def fetch_product_csv_headers(self, LANGUAGES_CODES_CONFIGURED=None):
        base_headers = ["sku", "brand_id", "barcode", "deliveryMethod", "nextFlightLeadTime(Business Hrs)",
                                                         "gatePickupLeadTime(Business Hrs)", "productType", "productSet", "notApplicableCountries",
                                                         "newFrom", "newTo", "spotLight", "ageVerificationRequire", "isAvailableToSell", "requireShipping",
                                                         "isvariantDefault", "isPerishable", "image1_1", "image2_1", "image4_3", "image2_3", "image5_6",
                                                         "gallery", "price_USD", "specialPrice_USD", "isTaxable", "minQuantityAllowed/Order",
                                                         "maxQuantityAllowed/Order", "weight", "weightUnit", "shippingLength", "shippingLengthUnit",
                                                         "shippingWidth", "shippingWidthUnit", "shippingHeight", "shippingHeightUnit",
                                                         "customAttribute", "specialityAttribute", "productId", "isVariant", "parentSku", "variantAttributes",
                                                        "price_GBP", "specialPrice_GBP", "price_ARS", "specialPrice_ARS", "price_AUD", "specialPrice_AUD",
                                                        "price_PTS", "specialPrice_PTS", "Ingredients", "Allergies", "Gluten Free", "Vegetarian",
                                                        "Vegan", "Calories", "Nutritional Value", "Cuisine", "Contains Nuts",
                                                        "Must be 18 years of age or older", "Must be 21 years of age or older", "Display",
                                                        "RAM", "OS", "Phone"]
        language_name_headers = [
            f'{field}_{lang}'
            for lang in LANGUAGES_CODES_CONFIGURED
            for field in ['name', 'shortDescription', 'description']
        ]
        headers = language_name_headers + base_headers
        return headers
    
    def closeAllTabs(self):
        print("Closing all tabs")
        actions = ActionChains(self.driver)
        actions.key_down(Keys.ALT).send_keys("t").key_up(Keys.ALT).perform()
        sleep(0.5)
        print("Closed all tabs")