from modules.specialityAttribute import specialityAttribute
from project_configs.csv_config import Config
from users.baseUser import baseUser
from modules.brand import brand
from selenium.webdriver.common.by import By
from project_configs.login_helpers import wait
from selenium.webdriver import Action<PERSON>hains, Keys
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC
from shared_config.config import *
from driver_setup.helper import *
from modules.product import product

class storeAdmin(baseUser):
    def __init__(self, driver, user, password, e):
        super().__init__(driver, user, password, e)

    def open_latestcatalogassignment_in_store(self, ca_id=None, airlineOFF=None):
        action = ActionChains(self.driver)
        self.driver.refresh()
        wait(20)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_assignment_tab_xpath))).click()
        wait(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_foler_expander_xpath))).click()
        # self.wait.until(
        #     EC.element_to_be_clickable((By.XPATH, constant.store_folder_xpath))).click()
        if airlineOFF:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, constant.store_ca_chk_folder_xpath))).click()
        else:
            self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, constant.store_category_folder_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_box_xpath))).send_keys(ca_id.split(' ')[1], Keys.ENTER)
        wait(5)
        ca = self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.ca_latest_store_xpath)))
        action.context_click(ca).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opened latest created catalog assignment to approve")

    def perform_action_approve_for_dissociation_state(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.actions_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.approve_dissoc_text))).click()
        wait(10)
        print("Performed Approved for Dissociation action")

    def perform_action_approve_for_association_state(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.actions_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.approve_assoc_text))).click()
        wait(10)
        print("Performed Approved for Association action")
    
    def createBrand(self, objName, closePreviouslyOpenedTabs=True):
        if closePreviouslyOpenedTabs:
            self.closeAllTabs()
        catalogManagement_tab = self.driver.find_element(By.XPATH, '//div[text()="Catalog Management"]/..')
        if catalogManagement_tab.get_attribute("aria-expanded") != "true":
            self.wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Catalog Management"]'))).click() 
            print("Catalog management page was not expanded. Clicked the page to expand the section")
        action = ActionChains(self.driver)
        # sleep(5)
        store = self.wait.until(EC.visibility_of_element_located((By.XPATH, f"//span[contains(text(),'{STORE_NAME}')]//parent::div//div[contains(@class,'folder')]"))) ## store_button_xpath = "//span[contains(text(),'store_automation')]//parent::div//div[contains(@class,'folder')]"
        sleep(2)
        action.context_click(store).perform()
        add_obj = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_obj_btn_text)))
        action.move_to_element(add_obj).perform()
        self.wait.until(EC.element_to_be_clickable((By.LINK_TEXT, 'Brands'))).click()
        self.wait.until(EC.element_to_be_clickable((By.ID, constant.add_catalog_id))).send_keys(objName)
        ok_button = self.wait.until(EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        return brand(self.driver)

        # actions.context_click(catalogAssigment_button).perform()
        # sleep(0.5)
        # try:
        #     Add_object_button = wait.until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Add Object"]')))
        #     actions.move_to_element(Add_object_button).perform()
        #     sleep(0.5)
        #     Add_object_button.click()
        #     logger.debug("It was able to click the Add Object button on first try :) ")

    def store_brand_csv_upload(self, csv_path=None):
        action = ActionChains(self.driver)
        self.import_csv()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.brand_text)
        action.send_keys(Keys.RETURN).perform()
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(
            Config.get_path(csv_path))
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new Brand")

    def store_product_csv_upload(self, csv_path=None, categoryPath=None):
        action = ActionChains(self.driver)
        self.import_csv()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.products_tab_text)
        sleep(3)
        action.send_keys(Keys.RETURN).perform()
        sleep(2)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.categorycsv_id))).click()
        sleep(2)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, categoryPath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.downloadcsv_btn_id))).click()
        sleep(2)
        csv_file = Config.get_path(csv_path)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(csv_file)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new Product")

    def get_list_language(self, languages_codes_configured, languages_map):
        list_of_languages = []
        for code in languages_codes_configured:
            list_of_languages.append(languages_map[code])
        return list_of_languages

    def verify_images_in_ui(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.assets_tab_text))).click()
        elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Brand Image')]")
        labels = [el.text.strip() for el in elements]
        expected = ["Brand Image 1:1 (100x100)", "Brand Image 16:9 (178x100)"]
        if sorted(labels) == sorted(expected):
            print("PASS: Only expected labels are present.")
        else:
            print("FAIL: Unexpected labels found.")
            print("Found:", labels)
            
    def store_select_languages(self, languages_codes_configured, languages_map):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, f"//span[contains(text(), '{STORE_NAME}')]"))).click()
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.config_tab_text))).click()
        languages_to_select = self.get_list_language(languages_codes_configured, languages_map)
        dropdown_trigger = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "(//div[contains(@id,'-trigger-picker')])[6]"))
        )
        dropdown_trigger.click()
        wait(3)
        dropdown_list = self.wait.until(
            EC.presence_of_element_located((By.XPATH, "//ul[contains(@class, 'x-list-plain') and @role='listbox']"))
        )
        language_elements = dropdown_list.find_elements(By.XPATH, "./li")
        print(f"Number of language <li> elements found: {len(language_elements)}")
        for idx, element in enumerate(language_elements):
            text = self.driver.execute_script("return arguments[0].textContent;", element).strip()
            print(f"Language {idx}: '{text}'")
            if text in languages_to_select:
                is_selected = (
                        'x-selected' in element.get_attribute("class") or
                        element.get_attribute("aria-selected") == "true" or
                        element.get_attribute("aria-checked") == "true"
                )
                if is_selected:
                    print(f"--> Already selected: {text}")
                else:
                    print(f"--> Clicking: {text}")
                    element.click()
        self.save_and_publish()
        return brand(self.driver, csv=True)

    def open_latest_spa_store(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_management_tab_xpath))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.spa_folder_xpath))).click()
        print("Opened speciality attribute folder")
        return specialityAttribute(self.driver, self.e)

    def navigate_to_brand_folder(self):
        catalogManagement_tab = self.driver.find_element(By.XPATH, '//div[text()="Catalog Management"]/..')
        if catalogManagement_tab.get_attribute("aria-expanded") != "true":
            self.wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Catalog Management"]'))).click()
            print("Catalog management page was not expanded. Clicked the page to expand the section")
        brand_folder = self.wait.until(EC.visibility_of_element_located((By.XPATH, f"(//span[contains(text(),'Brands')])[1]")))
        sleep(5)
        brand_folder.click()
        sleep(5)
        print("Opened brand folder")
        return brand(self.driver, csv=True)

    def navigate_to_product_folder(self, multi_lang=None):
        catalogManagement_tab = self.driver.find_element(By.XPATH, '//div[text()="Catalog Management"]/..')
        if catalogManagement_tab.get_attribute("aria-expanded") != "true":
            self.wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Catalog Management"]'))).click()
            print("Catalog management page was not expanded. Clicked the page to expand the section")
        pdt_folder = self.wait.until(
            EC.visibility_of_element_located((By.XPATH, constant.pdt_expand_xpath)))
        sleep(2)
        pdt_folder.click()
        if multi_lang:
            self.wait.until(EC.visibility_of_element_located((By.XPATH, f"//span[contains(text(),'CAT1')]"))).click()
        else:
            self.wait.until(
                EC.visibility_of_element_located((By.XPATH, f"//span[contains(text(),'Food & Drinks')]"))).click()
        print("Opened product folder")

    def create_product(self, sku=None, category="store_automation/category_1", product_type="Simple", closePreviouslyOpenedTabs=True):
        if closePreviouslyOpenedTabs:
            self.closeAllTabs()
        if not sku:
            sku = constant.pdtsku_value + "_1"
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.productcreate_btn_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_sku_name))).send_keys(sku)
        self.driver.find_element(By.XPATH,'//input[@name="category"]').click()
        sleep(0.5)
        # # for local run
        # self.wait.until(
        #     EC.element_to_be_clickable((By.XPATH, "//li[contains(text(),'CAT1 (Flipkart/CAT1)')]"))).click()
        for i in range(3):
            try:
                self.driver.find_element(By.XPATH,f"//li[contains(text(),'{category}')]").click()    ### INCOSISTANT ISSUE HERE or coulbe in line before...
                logger.debug("Clicked the category succesfully!")
                break
            except:
                logger.critical(f"there was an issue bringing up the category lists in try #{i+1}... trying to bring up the category list one more time in 3 seconds")
                sleep(3)
                self.driver.find_element(By.XPATH,'//input[@name="category"]').click()
        product_set_select = self.wait.until(
            EC.element_to_be_clickable((By.NAME, constant.product_set_select_name)))
        product_set_select.send_keys(product_type)
        product_set_select.send_keys(Keys.RETURN)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.ok_btn_value))).click()
        print("Added new product object")
        sleep(2)
        return product(self.driver, populateId=True)
