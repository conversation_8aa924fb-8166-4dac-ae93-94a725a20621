from time import sleep

from users.baseUser import baseUser
from selenium.webdriver.common.by import By
from selenium.webdriver import <PERSON><PERSON>hai<PERSON>, Keys
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import wait, automap_off, automap_on, generate_random_string


class airlineAdmin(baseUser):
    def __init__(self, driver, user, password, e):
        super().__init__(driver, user, password, e)

    def add_flight(self):
        wait(8)
        action = ActionChains(self.driver)
        home = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_name_xpath)))
        action.context_click(home).perform()
        add_object = self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.add_object_text)))
        action.move_to_element(add_object).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.flight_text))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.add_category_id))).send_keys(constant.flight_name + generate_random_string(2))
        ok_button = self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.ok_btn_id)))
        ok_button.click()
        print("Added new object - Flight from UI")

    def airline_automap_off(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_profile_xpath))).click()
        wait(5)
        automap_off(self.driver)
        print("In airline profile - turned off automap and autopopulate")

    def airline_automap_on(self):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.airline_profile_xpath))).click()
        sleep(5)
        automap_on(self.driver)
        print("In airline profile - turned on automap and autopopulate")

    def open_latest_ca(self, id=None):
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.catalog_assignment_tab_xpath))).click()
        sleep(5)
        action = ActionChains(self.driver)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.store_folder_xpath))).click()
        sleep(5)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.search_catalog_pdt_xpath))).send_keys(id)
        action.send_keys(Keys.RETURN).perform()
        sleep(5)
        ca_id = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, constant.ca_latest_xpath)))
        action.context_click(ca_id).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.open_btn_text))).click()
        wait(3)
        try:
            element = self.driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
            element.click()
        except NoSuchElementException:
            pass
        print("Opening latest object - Catalog Assignment from UI")
