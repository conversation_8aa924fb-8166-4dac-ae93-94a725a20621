import csv
import glob
import os
import re
import time, pytest
from time import sleep
import pandas as pd
from selenium.webdriver import <PERSON><PERSON>hai<PERSON>, Keys
from shared_config.config import *
from shared_config.config import logger
from selenium.webdriver.common.by import By
from driver_setup.helper import get_latest_set

from project_configs.env_ui_constants import constant
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from project_configs.login_helpers import brand_add_csv, generate_random_string, wait

class basePage:
    def __init__(self, driver, wait_time=int(constant.wait_time_qa)):
        self.driver = driver
        self.wait = WebDriverWait(driver, wait_time)

    def find_element(self, *locator):
        return self.wait.until(EC.visibility_of_element_located(locator))

    def click(self, *locator):
        self.wait.until(EC.element_to_be_clickable(locator)).click()

    def enter_text(self, text, *locator):
        element = self.find_element(*locator)
        element.clear()
        element.send_keys(text)

    def wait_and_click(self, by, value):
        element = self.wait.until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def closeObjectOpened(self):
            sleep(1)
            parent_box = self.driver.find_element(By.XPATH,f"//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
            parent_box.find_element(By.CLASS_NAME, "x-tab-close-btn").click()

    def validateDeleteOjbect(self):
        try:
            element = self.driver.find_element(By.XPATH, "//a[contains(@class,'x-tab-default-top x-tab-closable x-tab-active')]")
            is_displayed = element.is_displayed()
            assert not is_displayed, "Element should not be displayed but it is"
            print("Test passed: Element exists in DOM but is not displayed")
        except:
            print("Test passed: Element does not exist in the DOM")


    def find_save_text(self,timer_arr=None):
        start_time = time.time()
        try:
            assert WebDriverWait(self.driver, 50).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
            end_time = time.time()
            if timer_arr is not None:
                save_time = end_time - start_time
                logger.debug("The object save timer is " + str(save_time))
                print("The object save timer is " + str(save_time))
                pytest.assume(save_time < 20,
                              f"Object took more than 10 seconds to save... Total save time: {str(save_time)}")
                print(save_time < 20,
                      f"Object took more than 10 seconds to save... Total save time: {str(save_time)}")
                timer_arr.append(save_time)
        except Exception as e:
            assert WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
            end_time = time.time()
            logger.info(
                "It failed to find saved succesfully after 90 seconds. The value of time from timer fuction: " + str(
                    end_time - start_time))
            print(
                "It failed to find saved succesfully after 90 seconds. The value of time from timer fuction: " + str(
                    end_time - start_time))
            timer_arr.append(end_time - start_time)
            logger.info("Found saved succesfully with precence_of_element located ")
            print("Found saved succesfully with precence_of_element located ")

    def save_and_publish(self, validate_successful=True, data_tracker=None):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
        if validate_successful:
            self.find_save_text()
            print("Saved and published successfully!")
        if data_tracker is not None:
            data_tracker[self.id] = self.data

    def save(self):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.save_btn_text))).click()
        self.find_save_text()
        print("Saved")

    def unpublish(self, validate_successful=True):
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.unpublish_btn_text))).click()
        if validate_successful:
            self.find_save_text()
            print("Unpublished successfully!")

    def logout(self):
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.logout_id))).click()
        sleep(20)
        print("Logged out")

    def reload_object(self):
        current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)     
        current_page.find_element(By.XPATH, ".//span[@class='x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_reload pimcore_material_icon ']").click()
        print("reloaded the object. Waiting for 3 seconds to ensure the object is loaded")
        sleep(3)

    def delete_object(self):
        sleep(1) ## propagation delay for the pop-up
        current_page = self.driver.find_element(By.XPATH, self.PANEL_XPATH)     
        current_page.find_element(By.XPATH, ".//span[@class='x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_delete pimcore_material_icon ']").click()
        elements = self.driver.find_elements(By.XPATH, '//span[text()="OK"]')  ### This element is hard to find
        visible_elements = [element for element in elements if element.is_displayed()]
        if visible_elements:
            visible_elements[-1].click()
            logger.info("deleted the object by locating the visible elememt")
        else:
            sleep(1)
            self.driver.find_elements(By.XPATH, '//span[text()="OK"]')[-1].click()
            logger.critical("it was not able to find a visible OK Button, Tried again and clicked the last button")
            logger.info("deleted the object")
        self.driver.implicitly_wait(10)

    def get_list_language(self, languages_codes_configured, languages_map):
        list_of_languages = []
        for code in languages_codes_configured:
            list_of_languages.append(languages_map[code])
        return list_of_languages

    def get_latest_csv(self):
        download_path = os.getenv("SELENIUM_DOWNLOAD_PATH", "")
        if not os.path.exists(download_path):
            print(f"Download path {download_path} does not exist.")
            return None
        csv_files = glob.glob(os.path.join(download_path, "*.csv"))
        if not csv_files:
            print("No CSV files found.")
            return None
        csv_files.sort(key=os.path.getmtime, reverse=True)
        latest_csv = csv_files[0]
        return latest_csv

    def read_csv_headers(self):
        latest_csv = self.get_latest_csv()
        print(f"Latest CSV file path: {latest_csv}")
        with open(latest_csv, mode='r', newline='', encoding='utf-8-sig') as file:
            reader = csv.reader(file)
            headers = next(reader)
            headers = [h.lstrip('\ufeff') for h in headers]
            print("headers", headers)
        return headers

    def read_csv_img_urls(self):
        latest_csv = self.get_latest_csv()
        with open(latest_csv, mode='r', newline='', encoding='utf-8-sig') as file:
            reader = csv.DictReader(file)
            image_urls = []
            valid_ratios = ['100x100', '178x100']
            for row in reader:
                for col in ['brand_image1_1', 'brand_image16_9']:
                    url = row.get(col, '').strip()
                    if any(ratio in url for ratio in valid_ratios):
                        image_urls.append(url)
            print("URLs -", image_urls)
        return image_urls

    def verify_headers_for_csv(self, valid_headers=None):
        headers = self.read_csv_headers()
        if headers == valid_headers:
            print("CSV headers match the given names.")
        else:
            print("CSV headers do not match the given names.")
            print(f"CSV headers: {headers}")
            print(f"Expected headers: {valid_headers}")
            missing_headers = [header for header in valid_headers if header not in headers]
            if missing_headers:
                print("Missing headers found:")
                for missing in missing_headers:
                    print(f"- {missing}")
            extra_headers = [header for header in headers if header not in valid_headers]
            if extra_headers:
                print("Extra headers found:")
                for extra in extra_headers:
                    print(f"- {extra}")
            if missing_headers or extra_headers:
                raise ValueError("CSV headers validation failed. Please check the missing or extra headers.")

    def update_brand_and_product_csv_upload(self, brand_id=None):
        action = ActionChains(self.driver)
        csv_file_path = os.getenv("SELENIUM_DOWNLOAD_PATH", "")
        if not os.path.exists(csv_file_path):
            print(f"Download path {csv_file_path} does not exist.")
            return None
        csv_files = glob.glob(os.path.join(csv_file_path, "*.csv"))
        if not csv_files:
            print("No CSV files found.")
            return None
        csv_files.sort(key=os.path.getmtime, reverse=True)
        latest_csv = csv_files[0]
        print(f"Latest CSV file path: {latest_csv}")
        df = pd.read_csv(latest_csv)
        if 'brand_id' not in df.columns:
            print("'brand_id' column not found. Creating a new 'brand_id' column.")
            df['brand_id'] = brand_id
        else:
            df['brand_id'] = brand_id
        df.to_csv(latest_csv, index=False)
        print(f"Brand ID updated in {latest_csv}")
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csvimport_btn_text))).click()
        print("To Import CSV")
        sleep(10)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.products_tab_text)
        wait(3)
        action.send_keys(Keys.RETURN).perform()
        wait(2)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.categorycsv_id))).click()
        wait(2)
        print("category", CATEGORY_PATH)
        self.wait.until(
            EC.element_to_be_clickable((By.XPATH, f"{CATEGORY_PATH}"))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.downloadcsv_btn_id))).click()
        wait(2)
        print("csv path ", latest_csv)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(
            latest_csv)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Product csv uploaded with Brand")
        return latest_csv

    def click_export_csv_button(self, export=None, export_all=None):
        if export:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//span[contains(text(),'Export CSV')]"))).click()
            print("Clicked on Export CSV button")
            sleep(3)
        elif export_all:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//span[contains(text(),'Export All CSV')]"))).click()
            print("Clicked on Export All CSV button")
            sleep(8)
        else:
            print("Invalid button")

    def read_brand_export_csv(self, img_verify=None):
        csv_file_path = os.getenv("SELENIUM_DOWNLOAD_PATH", "")
        if not os.path.exists(csv_file_path):
            print(f"Download path {csv_file_path} does not exist.")
            return None
        csv_files = glob.glob(os.path.join(csv_file_path, "*.csv"))
        if not csv_files:
            print("No CSV files found.")
            return None
        csv_files.sort(key=os.path.getmtime, reverse=True)
        latest_csv = csv_files[0]
        print(f"Latest CSV file path: {latest_csv}")
        df = pd.read_csv(latest_csv, encoding='utf-8-sig')
        df.columns = df.columns.str.replace('^\ufeff', '', regex=True)
        required_columns = ['brand_id', 'name_en', 'brand_image1_1', 'brand_image16_9']
        selected_columns = df[required_columns]
        print(selected_columns)
        for index, row in selected_columns.iterrows():
            id = row['brand_id']
            name = row['name_en']
            image1 = row['brand_image1_1']
            image2 = row['brand_image16_9']
            print(f"Id:{id}, Name: {name}, Image1: {image1}, Image2: {image2}")
            if img_verify:
                if pd.isna(image1) or str(image1).strip() == "":
                    pytest.fail(f"Image1 is missing for Brand ID: {id}")
                else:
                    print("Image1 is present in csv")
                if pd.isna(image2) or str(image2).strip() == "":
                    pytest.fail(f"Image2 is missing for Brand ID: {id}")
                else:
                    print("Image2 is present in csv")
        data_list = selected_columns.to_dict(orient='records')
        return data_list

    def read_multilang_brand_export_csv(self):
        csv_file_path = os.getenv("SELENIUM_DOWNLOAD_PATH", "")
        if not os.path.exists(csv_file_path):
            print(f"Download path {csv_file_path} does not exist.")
            return None
        csv_files = glob.glob(os.path.join(csv_file_path, "*.csv"))
        if not csv_files:
            print("No CSV files found.")
            return None
        csv_files.sort(key=os.path.getmtime, reverse=True)
        latest_csv = csv_files[0]
        print(f"Latest CSV file path: {latest_csv}")
        df = pd.read_csv(latest_csv, encoding='utf-8-sig')
        language_name_headers = [f'name_{lang}' for lang in LANGUAGES_CODES_CONFIGURED]
        other_columns = ['brand_id', 'brand_image1_1', 'brand_image16_9']
        selected_columns = df[other_columns + language_name_headers]
        print(selected_columns)
        for index, row in selected_columns.iterrows():
            brand_id = row['brand_id']
            brand_images = {
                'image1_1': row['brand_image1_1'],
                'image16_9': row['brand_image16_9']
            }
            names = {lang: row[f'name_{lang}'] for lang in LANGUAGES_CODES_CONFIGURED}
            print(f"\nBrand ID: {brand_id}")
            for lang, name in names.items():
                print(f"  Name ({lang}): {name}")
            print(f"  Images: {brand_images}")
        data_list = selected_columns.to_dict(orient='records')
        print(data_list)
        return data_list

    def upload_exported_csv_with_change(self):
        global name_1
        csv_file_path = os.getenv("SELENIUM_DOWNLOAD_PATH", "")
        if not os.path.exists(csv_file_path):
            print(f"Download path {csv_file_path} does not exist.")
            return None
        csv_files = glob.glob(os.path.join(csv_file_path, "*.csv"))
        if not csv_files:
            print("No CSV files found.")
            return None
        csv_files.sort(key=os.path.getmtime, reverse=True)
        latest_csv = csv_files[0]
        print(f"Latest CSV file path: {latest_csv}")
        df = pd.read_csv(latest_csv, encoding='utf-8-sig')
        selected_columns = df[['brand_id', 'name_en', 'brand_image1_1', 'brand_image16_9']]
        print(selected_columns)
        for index, row in selected_columns.iterrows():
            id = row['brand_id']
            name = row['name_en']
            image1 = row['brand_image1_1']
            image2 = row['brand_image16_9']
            print(f"Id:{id}, Name: {name}, Image1: {image1}, Image2: {image2}")
        data_list = selected_columns.to_dict(orient='records')
        print("data_list ",data_list)
        try:
            new_name = 'Panasonic_'
            csv_file = latest_csv
            with open(csv_file, 'r') as file:
                reader = csv.reader(file)
                data = list(reader)
                data[1][1] = new_name + generate_random_string(3)
            with open(csv_file, 'w', newline='') as file:
                writer = csv.writer(file)
                writer.writerows(data)
            df = pd.read_csv(latest_csv)
            name_1 = df['name_en'].tolist()
            print("\n")
            print("Name in csv: " + (name_1[0]).lower())
        except Exception as E:
            print("Issue found with the csv file provided")
            print("\n" + str(E))
        action = ActionChains(self.driver)
        sleep(3)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csvimport_btn_text))).click()
        print("To Import CSV")
        sleep(10)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.brand_text)
        action.send_keys(Keys.RETURN).perform()
        print("csv path ", latest_csv)
        self.driver.find_element(By.ID, constant.selectfile_id).send_keys(
            latest_csv)
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.upload_btn_text))).click()
        print("Through csv import - added new Brand")
        return name_1

    def verify_spa_csv_ui_data(self, spa_details=None, name=None):
        if name:
            name_in_ui = name
        else:
            name_field = self.wait.until(EC.element_to_be_clickable((By.NAME, "description")))
            name_in_ui = name_field.get_attribute("value")
        if spa_details['description_en'] == name_in_ui:
            print("Names matched from UI and CSV.")

    def verify_csv_ui_data(self, brand_details=None, name=None):
        if name:
            name_in_ui = name
        else:
            name_field = self.wait.until(EC.element_to_be_clickable((By.NAME, constant.name_name)))
            name_in_ui = name_field.get_attribute("value")
        if brand_details['name_en'] == name_in_ui:
            print("Names matched from UI and CSV.")

    def brand_csv_samplefile_download(self, pac=None, store=None):
        action = ActionChains(self.driver)
        sleep(2)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csvimport_btn_text))).click()
        print("To Import CSV")
        sleep(8)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.brand_text)
        action.send_keys(Keys.RETURN).perform()
        sleep(1)
        if pac:
            self.wait.until(
                EC.element_to_be_clickable((By.NAME, constant.store_box_name))).send_keys(store)
            sleep(3)
            action.send_keys(Keys.RETURN).perform()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.download_csv_brand))).click()
        print("Downloaded brand sample CSV file")
        sleep(2)

    def product_csv_samplefile_download(self, default_lang=None, multi_lang=None):
        action = ActionChains(self.driver)
        sleep(2)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.csvimport_icon_id))).click()
        self.wait.until(
            EC.element_to_be_clickable((By.LINK_TEXT, constant.csvimport_btn_text))).click()
        print("To Import CSV")
        sleep(8)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.importdatatype_id))).send_keys(constant.products_tab_text)
        wait(2)
        action.send_keys(Keys.RETURN).perform()
        sleep(2)
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.categorycsv_id))).click()
        sleep(2)
        if default_lang:
            self.wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, f"//li[contains(text(),'{CATEGORY_NAME_VALUE} ({STORE_NAME}/{CATEGORY_NAME_VALUE})')]"))).click()
        elif multi_lang:
            self.wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, f"//li[contains(text(),'{CATEGORY_NAME_VALUE} ({STORE_NAME}/{CATEGORY_NAME_VALUE})')]"))).click()
        else:
            print("Invalid store language config info")
        self.wait.until(
            EC.element_to_be_clickable((By.ID, constant.downloadcsv_btn_id))).click()
        print("Downloaded product sample CSV file")
        sleep(2)

    def close_filter(self):
        self.wait.until(EC.element_to_be_clickable((By.XPATH, constant.filter_close_xpath))).click()
        sleep(1)
