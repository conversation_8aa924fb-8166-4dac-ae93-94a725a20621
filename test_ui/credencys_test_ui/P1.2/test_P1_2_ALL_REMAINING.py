import logging, os, time
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
import credencys_test_ui.credencys_project_configs.utils as utils
from selenium.webdriver.support.wait import WebDriverWait
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append

if os.environ["env"] == "QA":
    pass
elif os.environ["env"] == "DEV":
    pass
elif os.environ["env"] == "TRIAL":
    pass
elif os.environ["env"] == "TEST":
    pass

def test_MKTPL_5179_Export_button_001_003_005_006_008_010_012_014_016_018_020_021_022_023():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5179_Export_button_001_003_005_006_008_010_012_014_016_018_020_021_022_023.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 60)
            # MKTPL-5179-Export button-001
            utils.search_by_path(driver, "/Cred Automation Store/Catalog")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-003
            utils.search_by_path(driver, "/Cred Automation Store/Products")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-005
            utils.search_by_path(driver, "/Master Data/Aircraft")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-006
            utils.search_by_path(driver, "/Master Data/Airports")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-008
            utils.search_by_path(driver, "/Cred Automation Airline/RouteGroup")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-010
            utils.search_by_path(driver, "/Cred Automation Airline/Sectors")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-012
            utils.search_by_path(driver, "/Cred Automation Airline/Flight")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-014
            utils.search_by_path(driver, "/Cred Automation Airline/Category")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-016
            utils.search_by_path(driver, "/Master Data/Category/Cred Automation Store")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-018
            utils.search_by_path(driver, "/Cred Automation Store/Inventory Location")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-020
            utils.search_by_path(driver, "/Cred Automation Airline/Meal Code")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-021
            utils.search_by_path(driver, "/Master Data/Root Type/Cred Automation Airline")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-022
            utils.search_by_path(driver, "/Master Data/UI Templates/Cred Automation Airline")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-023
            utils.search_by_path(driver, "/Master Data/Fulfillment")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)

            Success_List_Append(
                "test_MKTPL_5179_Export_button_001_003_005_006_008_010_012_014_016_018_020_021_022_023",
                "Export CSV button should be visible",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5179_Export_button_001_003_005_006_008_010_012_014_016_018_020_021_022_023",
            "Export CSV button should be visible",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5179_Export_button_001_003_005_006_008_010_012_014_016_018_020_021_022_023",
            "Export CSV button should be visible",
            e,
        )
        raise e

def test_MKTPL_5179_Export_button_002_004_015():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5179_Export_button_002_004_015.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 60)
            # MKTPL-5179-Export button-002
            utils.search_by_path(driver, "/Cred Automation Store/Catalog")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-004
            utils.search_by_path(driver, "/Cred Automation Store/Products")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-015
            driver.find_element(By.XPATH, '//div[contains(text(),"Categories")]').click()
            logging.info("Opened Categories")
            driver.find_element(By.XPATH, '(//span[contains(text(),"Cred Automation Store")])[last()]').click()
            logging.info("Opened Cred Automation Store")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)

            Success_List_Append(
                "test_MKTPL_5179_Export_button_002_004_015",
                "Export CSV button should be visible",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5179_Export_button_002_004_015",
            "Export CSV button should be visible",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5179_Export_button_002_004_015",
            "Export CSV button should be visible",
            e,
        )
        raise e

def test_MKTPL_5179_Export_button_007_009_011_013_019():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5179_Export_button_007_009_011_013.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 60)
            
            # MKTPL-5179-Export button-007
            utils.search_by_path(driver, "/Cred Automation Airline/RouteGroup")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-009
            utils.search_by_path(driver, "/Cred Automation Airline/Sectors")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-011
            utils.search_by_path(driver, "/Cred Automation Airline/Flight")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-019
            utils.search_by_path(driver, "/Cred Automation Airline/Meal Code")
            logging.info("Opened")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)
            # MKTPL-5179-Export button-013
            driver.find_element(By.XPATH, '//div[contains(text(),"Category Management")]').click()
            logging.info("Opened Category Management")
            driver.find_element(By.XPATH, '(//span[contains(text(),"Category")])[1]').click()
            logging.info("Opened Category")
            time.sleep(10)
            assert driver.find_element(By.XPATH, '//span[text()="Export CSV"]').is_displayed()
            logging.info("Export CSV is displayed")
            utils.close_All_Tabs(driver)


            Success_List_Append(
                "test_MKTPL_5179_Export_button_007_009_011_013",
                "Export CSV button should be visible",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5179_Export_button_007_009_011_013",
            "Export CSV button should be visible",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5179_Export_button_007_009_011_013",
            "Export CSV button should be visible",
            e,
        )
        raise e

