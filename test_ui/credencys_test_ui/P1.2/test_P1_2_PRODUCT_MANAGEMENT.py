import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import credencys_test_ui.credencys_project_configs.utils as utils
from selenium.webdriver.support.wait import WebDriverWait
import logging, os, string, random, time, csv, requests, json
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append
# from utils import services_context_wrapper, Till_Login, Delete, Pac_QA, close_All_Tabs, Assets, search_by_id, \
#     update_csv, upload_csv

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

def test_MKTPL_1103_Leadtimeproduct_002_003():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1103_Leadtimeproduct_002_003.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_PRODUCT_2_ID)
            scroll = wait.until(
                EC.presence_of_element_located(("xpath", xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            assert driver.find_element(
                By.XPATH, '//span[text()="Delivery Method "]//span'
            ).is_displayed()
            logging.info(
                "In product tab,User able to see Delivery Type mandatory field in UI"
            )
            arrow = driver.find_element(
                By.XPATH,
                '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
            )
            driver.execute_script("arguments[0].click();", arrow)
            nf = driver.find_element(
                By.XPATH, '(//li[text()="Next Flight"])[1]'
            ).is_displayed()
            logging.info(nf)
            gf = driver.find_element(
                By.XPATH, '(//li[text()="Gate Pick Up"])[1]'
            ).is_displayed()
            logging.info(gf)

            logging.info(
                "Add 1) Next Flight 2)Gate Pick Up in Delivery type dropdown listing"
            )

            Success_List_Append(
                "test_MKTPL_1103_Leadtimeproduct_002",
                "Verify input Lead Time for the product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1103_Leadtimeproduct_002_003",
            "Verify input Lead Time for the product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1103_Leadtimeproduct_002_003",
            "Verify input Lead Time for the product",
            e,
        )
        raise e

def test_MKTPL_1103_Leadtimeproduct_009():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1103_Leadtimeproduct_009.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_PRODUCT_2_ID)
            scroll = wait.until(
                EC.presence_of_element_located(("xpath", xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("Scrolled")
            time.sleep(1)
            try:
                (
                    WebDriverWait(driver, 60).until(
                        EC.presence_of_element_located(
                            (
                                By.XPATH,
                                '//div[text()="Next Flight"]/..//div[@class="x-tagfield-item-close"]',
                            )
                        )
                    )
                )
            except:
                driver.find_element(By.NAME, "deliveryType").send_keys("Next Flight")
                WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located(
                            (
                                By.XPATH,
                                "//li[text()='Next Flight']"
                            )
                        )
                    )
                driver.find_element(By.XPATH, "//li[text()='Next Flight']").click()
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located(
                            (
                                By.XPATH,
                                '//div[text()="Next Flight"]/..//div[@class="x-tagfield-item-close"]',
                            )
                        )
                    )
            driver.find_element(
                By.XPATH,
                '//div[text()="Next Flight"]/..//div[@class="x-tagfield-item-close"]',
            ).click()
            time.sleep(5)
            next_field = driver.find_element(
                By.XPATH, '//span[text()="Next Flight Lead Time(Business Hrs):"]'
            ).is_displayed()
            logging.info(next_field)
            try:
                (
                    WebDriverWait(driver, 60).until(
                        EC.presence_of_element_located(
                            (
                                By.XPATH,
                                '//div[text()="Gate Pick Up"]/..//div[@class="x-tagfield-item-close"]',
                            )
                        )
                    )
                )
            except:
                driver.find_element(By.NAME, "deliveryType").send_keys("Gate Pick Up")
                WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located(
                            (
                                By.XPATH,
                                "//li[text()='Gate Pick Up']"
                            )
                        )
                    )
                driver.find_element(By.XPATH, "//li[text()='Gate Pick Up']").click()
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                        EC.presence_of_element_located(
                            (
                                By.XPATH,
                                '//div[text()="Gate Pick Up"]/..//div[@class="x-tagfield-item-close"]',
                            )
                        )
                    )
            driver.find_element(
                By.XPATH,
                '//div[text()="Gate Pick Up"]/..//div[@class="x-tagfield-item-close"]',
            ).click()
            time.sleep(5)
            gatePickup_field = driver.find_element(
                By.XPATH, '//span[text()="Gate Pickup Lead Time(Business Hrs):"]'
            ).is_displayed()
            logging.info(gatePickup_field)
            assert next_field == False and gatePickup_field == False

            logging.info(
                "Remove Next Flight,Gate Pick Up option from in delivery type field in case Next Flight Lead Time,Gate Pick Up Time fields is hide"
            )
            Success_List_Append(
                "test_MKTPL_1103_Leadtimeproduct_009",
                "Check that remove Next Flight,Gate Pick Up option from in delivery type field in case Next Flight Lead Time,Gate Pick Up Time fields should be hide",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1103_Leadtimeproduct_009",
            "Check that remove Next Flight,Gate Pick Up option from in delivery type field in case Next Flight Lead Time,Gate Pick Up Time fields should be hide",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1103_Leadtimeproduct_009",
            "Check that remove Next Flight,Gate Pick Up option from in delivery type field in case Next Flight Lead Time,Gate Pick Up Time fields should be hide",
            e,
        )
        raise e

def test_MKTPL_1103_Leadtimeproduct_010():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1103_Leadtimeproduct_010.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))
            ).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            import_data_type = WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.NAME, "importType"))
            )
            import_data_type.send_keys("Products")
            import_data_type.send_keys(Keys.ENTER)
            time.sleep(2)
            category = driver.find_element(By.NAME, "category[]")
            category.send_keys(
                ""
                + DEDICATED_CATEGORY_1_NAME
                + " ("
                + DEDICATED_STORE
                + "/"
                + DEDICATED_CATEGORY_1_NAME
                + ")"
            )
            category.send_keys(Keys.ENTER)
            time.sleep(3)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.downloadSampleFileProducts))
            ).click()
            # driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            # WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.upload))).click()
            # upload = WebDriverWait(driver, 60).until(
            #     EC.visibility_of_element_located((By.XPATH, '//div[text()="CSV Uploaded Successfully. Import in Progress. A log file will be sent via Mail."]'))).is_displayed()
            # assert upload
            utils.upload_csv_with_specific_validation(
                "field_blank_product.csv",
                xpath.csvFileSelectButton,
                "CSV Uploaded Successfully",
            )
            Success_List_Append(
                "test_MKTPL_1103_Leadtimeproduct_010",
                "Verify input Lead Time for the product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1103_Leadtimeproduct_010",
            "Verify input Lead Time for the product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1103_Leadtimeproduct_010",
            "Verify input Lead Time for the product",
            e,
        )
        raise e

# def test_MKTPL_1103_Leadtimeproduct_013_014():
#     try:
#         with utils.services_context_wrapper(
#                 "test_MKTPL_1103_Leadtimeproduct_013_014.png"
#         ) as driver:
#             driver.maximize_window()
#             utils.Pac_Credentials.Login_store(driver)
#             act_title = driver.find_element(
#                 By.XPATH, xpath.logout
#             ).get_attribute("id")
#             assert act_title == "pimcore_logout"
#             logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
#             WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))).click()
#             driver.find_element(By.XPATH, xpath.csvImport).click()
#             import_data_type = WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.NAME, 'importType')))
#             import_data_type.send_keys("Products")
#             import_data_type.send_keys(Keys.ENTER)
#             time.sleep(2)
#             category = driver.find_element(By.NAME, 'category[]')
#             category.send_keys(""+DEDICATED_CATEGORY_1_NAME+" ("+DEDICATED_STORE+"/"+DEDICATED_CATEGORY_1_NAME+")")
#             category.send_keys(Keys.ENTER)
#             time.sleep(3)
#             WebDriverWait(driver, 60).until(
#                 EC.element_to_be_clickable((By.XPATH, xpath.downloadSampleFileProducts))).click()
#             RANDOM_NAME = "".join(random.choices(string.ascii_letters, k=7))
#             logging.info(RANDOM_NAME)
#             utils.update_csv("invalidGate_LeadTime.csv", "nextFlightLeadTime(Business Hrs)", RANDOM_NAME)
#             utils.update_csv("invalidGate_LeadTime.csv", "gatePickupLeadTime(Business Hrs)", RANDOM_NAME)
#             path = os.path.join(os.getcwd()+"/credencys_test_ui/"+"invalidGate_LeadTime.csv")
#             logging.info(path)
#             driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
#             WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.upload))).click()
#             upload = WebDriverWait(driver, 60).until(
#                 EC.visibility_of_element_located((By.XPATH, '//div[text()="CSV Uploaded Successfully. Import in Progress. A log file will be sent via Mail."]'))).is_displayed()
#             assert upload
#             driver.find_element(By.XPATH, xpath.logout).click()
#             logging.info("Logged out from Airline Admin")
#             WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
#             # --------------------- verify log's  ---------------------
#             utils.Pac_Credentials.Login_Pac_Admin(driver)
#             Assets_import_Store(driver)
#             list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'/Import/"+PAC_STORE_USER_MAIL+"/products')]")
#             num = len(list1)
#             time.sleep(5)
#             row = driver.find_element(By.XPATH, f"(//div[contains(text(),'/Import/"+PAC_STORE_USER_MAIL+f"/products')])[{num}]")
#             actions = ActionChains(driver)
#             actions.context_click(row).perform()
#             open = driver.find_element(By.XPATH, xpath.open)
#             open.click()
#             logging.info("open clicked")
#             # Open Code if modal appears
#
#
#             try:
#                 if driver.find_element(By.XPATH, xpath.yes).is_displayed():
#                     driver.find_element(By.XPATH, xpath.yes).click()
#             except:
#                 logging.info("Modal is not shown, store not opened on any other device")
#             logging.info("Opened the log file - json")
#             WebDriverWait(driver, 60).until(
#                 EC.presence_of_element_located(
#                     (By.XPATH, '//div[contains(text()," Please enter a valid value for gatePickupLeadTime")]')))
#             driver.find_element(By.XPATH, xpath.logout).click()
#             WebDriverWait(driver, 60).until(
#                 (EC.presence_of_element_located((By.XPATH, xpath.userName))))
#             logging.info('Invalid data added in csv and showing error message like "Invalid value for Next Flight Lead Time,Gate Pick Up Time"')
#             # --------------------- login to store and upload empty field csv  ---------------------
#             utils.Pac_Credentials.Login_store(driver)
#             act_title = driver.find_element(
#                 By.XPATH, xpath.logout
#             ).get_attribute("id")
#             assert act_title == "pimcore_logout"
#             logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
#             WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))).click()
#             driver.find_element(By.XPATH, xpath.csvImport).click()
#             import_data_type = WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.NAME, 'importType')))
#             import_data_type.send_keys("Products")
#             import_data_type.send_keys(Keys.ENTER)
#             time.sleep(2)
#             category = driver.find_element(By.NAME, 'category[]')
#             category.send_keys(""+DEDICATED_CATEGORY_1_NAME+" ("+DEDICATED_STORE+"/"+DEDICATED_CATEGORY_1_NAME+")")
#             category.send_keys(Keys.ENTER)
#             time.sleep(3)
#             utils.upload_csv_with_specific_validation("field_blank_product.csv",xpath.csvFileSelectButton,"CSV Uploaded Successfully. Import in Progress. A log file will be sent via Mail.")
#             driver.find_element(By.XPATH, xpath.logout).click()
#             logging.info("Logged out from Airline Admin")
#
#             Success_List_Append(
#                 "test_MKTPL_1103_Leadtimeproduct_013_014",
#                 'Verify the invalid data added in csv showing error message like "Invalid value for Next Flight Lead Time,Gate Pick Up Time"',
#                 "Pass",
#             )
#
#     except Exception as e:
#         logging.info(f"Error- {e}")
#         Success_List_Append(
#             "test_MKTPL_1103_Leadtimeproduct_013_014",
#             'Verify the invalid data added in csv showing error message like "Invalid value for Next Flight Lead Time,Gate Pick Up Time"',
#             "Fail",
#         )
#         Failure_Cause_Append(
#             "test_MKTPL_1103_Leadtimeproduct_013_014",
#             'Verify the invalid data added in csv showing error message like "Invalid value for Next Flight Lead Time,Gate Pick Up Time"',
#             e,
#         )
#         raise e

def test_MKTPL_1103_Leadtimeproduct_018_019():
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    logging.info(RANDOM_NAME)
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1103_Leadtimeproduct_018_019.png"
        ) as driver:
            # ADD
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Next Flight", "Gate Pick Up", "Onboard"],
                    "nextFlightLeadTime": "rf",
                    "gatePickupLeadTime": "fggf",
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "storeProductId": "",
                    "productType": "Duty Free",
                }
            )
            headers = {
                "language": "en",
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            # assert (
            #     "Invalid type for nextFlightLeadTime" in data["message"]
            #     and "Invalid type for gatePickupLeadTime" in data["message"]
            # )
            assert (
                "Next Flight Lead Time threshold is not having valid value" in data["message"]
            )
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Next Flight", "Gate Pick Up", "Onboard"],
                    "nextFlightLeadTime": 1,
                    "gatePickupLeadTime": "fggf",
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "storeProductId": "",
                    "productType": "Duty Free",
                }
            )
            headers = {
                "language": "en",
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            # assert (
            #     "Invalid type for nextFlightLeadTime" in data["message"]
            #     and "Invalid type for gatePickupLeadTime" in data["message"]
            # )
            assert (
                "Gate Pickup Lead Time threshold is not having valid value" in data["message"]
            )
            logging.info(
                'Invalid data added in API showing error message like "Invalid value for Next Flight Lead Time,Gate Pick Up Time"'
            )
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Next Flight", "Gate Pick Up", "Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "storeProductId": "",
                    "productType": "Duty Free",
                }
            )
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Product added successfully. " in data["message"]
            UID = data["id"]
            try:
                product_id_1 = "ID " + str(UID)
                RT_VALUE = utils.Delete(
                    product_id_1, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
            logging.info(
                "Next Flight Lead Time,Gate Pick Up Time which is non mandatory fields by API impact"
            )

            Success_List_Append(
                "test_MKTPL_1103_Leadtimeproduct_018_019",
                'Verify the invalid data added in API showing error message like "Invalid value for Next Flight Lead Time,Gate Pick Up Time"',
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1103_Leadtimeproduct_018_019",
            'Verify the invalid data added in API showing error message like "Invalid value for Next Flight Lead Time,Gate Pick Up Time"',
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1103_Leadtimeproduct_018_019",
            'Verify the invalid data added in API showing error message like "Invalid value for Next Flight Lead Time,Gate Pick Up Time"',
            e,
        )
        raise e

def test_MKTPL_2100_SNS_001_002_003():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        with utils.services_context_wrapper(
            "test_MKTPL_2100_SNS_001_002_003.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            logging.info("Clicked on Pimcore menu")
            driver.find_element(By.XPATH, xpath.newProductXpath).click()
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Product SKU')]")
                )
            )
            driver.find_element(By.NAME, xpath.productSkuInputFieldName).send_keys(
                RANDOM_NAME
            )
            driver.find_element(By.NAME, xpath.categoryInputFieldName).click()
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        f"//li[contains(text(),'({DEDICATED_STORE}/{DEDICATED_CATEGORY_1_NAME})')]",
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                f"//li[contains(text(),'({DEDICATED_STORE}/{DEDICATED_CATEGORY_1_NAME})')]",
            ).click()
            driver.find_element(By.NAME, xpath.productSetInputFieldName).send_keys(
                "Simple"
            )
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//li[contains(text(),'Simple')]")
                )
            )
            driver.find_element(By.XPATH, "//li[contains(text(),'Simple')]").click()
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            logging.info("Clicked on ok")
            wait.until(EC.visibility_of_element_located((By.NAME, "name")))
            driver.find_element(By.NAME, "name").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, xpath.deliveryMethodName).send_keys("Onboard")
            wait.until(
                EC.visibility_of_element_located((By.XPATH, "//li[text()='Onboard']"))
            )
            driver.find_element(By.XPATH, "//li[text()='Onboard']").click()
            logging.info("Selected Onboard")
            driver.find_element(By.XPATH, "//div[text()='Products']").click()
            time.sleep(1)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//input[@name='productType']//parent::div//following-sibling::div[contains(@id,'trigger-picker')]",
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                "//input[@name='productType']//parent::div//following-sibling::div[contains(@id,'trigger-picker')]",
            ).click()
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//li[text()='Food and Beverage']")
                )
            )
            driver.find_element(By.XPATH, "//li[text()='Food and Beverage']").click()
            logging.info("Selected Food and Beverage")
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            # Check for the validation
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Save and Publish")
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Open Code if modal appears
            try:
                if driver.find_element(
                    By.XPATH, xpath.yes
                ).is_displayed():
                    driver.find_element(
                        By.XPATH, xpath.yes
                    ).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, f"//span[text()='{UID}']"))
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[text()='{UID}']"
            ).is_displayed()
            # test_MKTPL_2100_SNS_002
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, UID)
            wait.until(EC.visibility_of_element_located((By.NAME, "name")))
            driver.find_element(By.NAME, "name").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, xpath.deliveryMethodName).send_keys(
                "Gate Pick Up"
            )
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//li[text()='Gate Pick Up']")
                )
            )
            driver.find_element(By.XPATH, "//li[text()='Gate Pick Up']").click()
            logging.info("Selected Gate Pick Up")
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            # Check for the validation
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Save and Publish")
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Open Code if modal appears
            try:
                if driver.find_element(
                    By.XPATH, xpath.yes
                ).is_displayed():
                    driver.find_element(
                        By.XPATH, xpath.yes
                    ).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, f"//span[text()='{UID}']"))
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[text()='{UID}']"
            ).is_displayed()
            # test_MKTPL_2100_SNS_003
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, UID)
            wait.until(EC.visibility_of_element_located((By.NAME, "name")))
            driver.find_element(By.XPATH, xpath.unpublishButtonXpath).click()
            # Check for the validation
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Unpublish")
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_unpublish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_unpublish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Open Code if modal appears
            try:
                if driver.find_element(
                    By.XPATH, xpath.yes
                ).is_displayed():
                    driver.find_element(
                        By.XPATH, xpath.yes
                    ).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, f"//span[text()='{UID}']"))
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[text()='{UID}']"
            ).is_displayed()

            Success_List_Append(
                "test_MKTPL_2100_SNS_001_002_003",
                "Verify When a new product (base product or variant) is created via UI and it is published",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2100_SNS_001_002_003",
            "Verify When a new product (base product or variant) is created via UI and it is published",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2100_SNS_001_002_003",
            "Verify When a new product (base product or variant) is created via UI and it is published",
            e,
        )
        raise e

def test_MKTPL_2100_SNS_006_007_008():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        with utils.services_context_wrapper(
            "test_MKTPL_2100_SNS_006_007_008.png"
        ) as driver:
            UID = utils.create_Simple_Product("Onboard", "Duty Free", RANDOM_NAME)
            assert UID != None
            driver.maximize_window()
            wait = WebDriverWait(driver, 60)
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Open Code if modal appears
            try:
                if driver.find_element(
                    By.XPATH, xpath.yes
                ).is_displayed():
                    driver.find_element(
                        By.XPATH, xpath.yes
                    ).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, f"//span[text()='{UID}']"))
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[text()='{UID}']"
            ).is_displayed()
            # test_MKTPL_2100_SNS_007
            PRODUCTS_URL_PUT = PRODUCTS_URL + f"{UID}"
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Gate Pick Up"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "productType": "Duty Free",
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "PUT", PRODUCTS_URL_PUT, headers=headers, data=payload
            )
            resp_data = response.json()
            print(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"PUT API for Product responded with 200 status code")
            resp_data = response.json()
            logging.info(resp_data)
            assert "Product updated successfully. " in resp_data["message"]

            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Open Code if modal appears
            try:
                if driver.find_element(
                    By.XPATH, xpath.yes
                ).is_displayed():
                    driver.find_element(
                        By.XPATH, xpath.yes
                    ).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, f"//span[text()='{UID}']"))
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[text()='{UID}']"
            ).is_displayed()
            # test_MKTPL_2100_SNS_008

            payload = json.dumps({})
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "DELETE", PRODUCTS_URL_PUT, headers=headers, data=payload
            )
            resp_data = response.json()
            print(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"DELETE API for Product responded with 200 status code")
            resp_data = response.json()
            logging.info(resp_data)
            assert "Product deleted successfully" in resp_data["message"]
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_unpublish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_unpublish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Open Code if modal appears
            try:
                if driver.find_element(
                    By.XPATH, xpath.yes
                ).is_displayed():
                    driver.find_element(
                        By.XPATH, xpath.yes
                    ).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, f"//span[text()='{UID}']"))
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[text()='{UID}']"
            ).is_displayed()

            Success_List_Append(
                "test_MKTPL_2100_SNS_006_007_008",
                "Verify When a new product (base product or variant) is created via UI and it is published",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2100_SNS_006_007_008",
            "Verify When a new product (base product or variant) is created via UI and it is published",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2100_SNS_006_007_008",
            "Verify When a new product (base product or variant) is created via UI and it is published",
            e,
        )
        raise e

def test_MKTPL_2504_Unpublishedproduct_003():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        with utils.services_context_wrapper(
            "test_MKTPL_2504_Unpublishedproduct_003.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_FOLDER_5_ID)
            id = driver.find_element(
                By.XPATH, "(//div[contains(@class,'pimcore_editable')]//tr//td)[2]//div"
            ).text
            driver.find_element(
                By.XPATH,
                "(//div[contains(@class,'pimcore_editable')]//tr//td)[4]//span",
            ).click()
            # wait_for_style_attribute(driver, 15)
            time.sleep(10)
            assert driver.find_element(
                By.XPATH,
                "(//div[contains(@class,'pimcore_editable')]//tr//td)[4]//span[contains(@class,'x-grid-checkcolumn-checked')]",
            ).is_displayed()
            logging.info("Checked")
            wait.until(EC.element_to_be_clickable((By.XPATH, xpath.saveXpath)))
            driver.find_element(By.XPATH, xpath.saveXpath).click()
            logging.info("Clicked on Save")
            # Check for the validation
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Saved successfully")
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, id)
            assert driver.find_element(
                By.XPATH, xpath.unpublishButtonXpath
            ).is_displayed()
            logging.info("The product has been published")
            driver.find_element(By.XPATH, xpath.unpublishButtonXpath).click()
            logging.info("Unpublished again")
            Success_List_Append(
                "test_MKTPL_2504_Unpublishedproduct_003",
                "Verify by checking the Published column checkbox in Unpublished Products folder grid",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_003",
            "Verify by checking the Published column checkbox in Unpublished Products folder grid",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_003",
            "Verify by checking the Published column checkbox in Unpublished Products folder grid",
            e,
        )
        raise e

def test_MKTPL_2504_Unpublishedproduct_004():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        with utils.services_context_wrapper(
            "test_MKTPL_2504_Unpublishedproduct_004.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_FOLDER_5_ID)
            time.sleep(3)
            # for i in range(1,4):
            #     if (i%2) != 0:
            #         ele = driver.find_element(By.XPATH,f"(//span[@class='x-grid-checkcolumn'])[{i}]")
            #         mouse_hover = ActionChains(driver)
            #         mouse_hover.move_to_element(ele).perform()
            #         logging.info("Move to element-entries")
            #         driver.execute_script("arguments[0].click();", ele)
            #         time.sleep(5)
            #         logging.info(i)
            row1 = driver.find_element(
                By.XPATH, f"(//span[@class='x-grid-checkcolumn'])[1]"
            )
            row2 = driver.find_element(
                By.XPATH, f"(//span[@class='x-grid-checkcolumn'])[3]"
            )
            action_chains = ActionChains(driver)
            action_chains.key_down(Keys.CONTROL).click(row1).click(row2).key_up(
                Keys.CONTROL
            ).perform()
            id1 = driver.find_element(
                By.XPATH,
                "((//div[contains(@class,'pimcore_editable')]//tr)[1]//td)[2]//div",
            ).text
            id2 = driver.find_element(
                By.XPATH,
                "((//div[contains(@class,'pimcore_editable')]//tr)[2]//td)[2]//div",
            ).text
            publishedColumnXpath = driver.find_element(
                By.XPATH, xpath.publishedColumnXpath
            )
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(publishedColumnXpath).perform()
            logging.info("Move to element")
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//span[text()='Published']//ancestor::div[contains(@id,'title')]//div[contains(@id,'trigger')]",
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                "//span[text()='Published']//ancestor::div[contains(@id,'title')]//div[contains(@id,'trigger')]",
            ).click()
            logging.info("Click on arrow")
            driver.find_element(
                By.XPATH, "//span[text()='Batch edit selected']"
            ).click()
            logging.info("Batch edit selected")
            driver.find_element(By.NAME, "published").click()
            logging.info("Published")
            driver.find_element(By.XPATH, "(//span[text()='Save'])[2]").click()
            logging.info("Saved")
            time.sleep(10)
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, id1)
            assert driver.find_element(
                By.XPATH, xpath.unpublishButtonXpath
            ).is_displayed()
            logging.info(f"The product with {id1} has been published")
            driver.find_element(By.XPATH, xpath.unpublishButtonXpath).click()
            logging.info("Unpublished again")
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, id2)
            assert driver.find_element(
                By.XPATH, xpath.unpublishButtonXpath
            ).is_displayed()
            logging.info(f"The product with {id2} has been published")
            driver.find_element(By.XPATH, xpath.unpublishButtonXpath).click()
            logging.info("Unpublished again")
            Success_List_Append(
                "test_MKTPL_2504_Unpublishedproduct_004",
                "Verify by selecting multiple products and click on batch edit selected and checking the Published checkbox in Unpublished Products folder grid",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_004",
            "Verify by selecting multiple products and click on batch edit selected and checking the Published checkbox in Unpublished Products folder grid",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_004",
            "Verify by selecting multiple products and click on batch edit selected and checking the Published checkbox in Unpublished Products folder grid",
            e,
        )
        raise e

def test_MKTPL_2504_Unpublishedproduct_006():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        with utils.services_context_wrapper(
            "test_MKTPL_2504_Unpublishedproduct_006.png"
        ) as driver:
            payload = json.dumps(
                {
                    "sku": DEDICATED_PRODUCT_1_NAME,
                    "name": DEDICATED_PRODUCT_1_NAME,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "productType": "Duty Free",
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(
                f"POST API for Product responded with 400 status code for same sku"
            )
            assert (
                "A product already exists with same sku and store."
                in resp_data["message"]
            )

            Success_List_Append(
                "test_MKTPL_2504_Unpublishedproduct_006",
                "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_006",
            "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_006",
            "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
            e,
        )
        raise e

def test_MKTPL_2504_Unpublishedproduct_007_010_MKTPL_1105_Perishableproduct_016():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper(
            "test_MKTPL_2504_Unpublishedproduct_007_010_MKTPL_1105_Perishableproduct_016.png"
        ) as driver:
            driver.maximize_window()
            wait = WebDriverWait(driver, 60)
            UID = utils.create_Simple_Product("Onboard", "Duty Free", RANDOM_NAME)
            assert UID != None
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, UID)
            driver.find_element(By.XPATH, xpath.unpublishButtonXpath).click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            UID = utils.create_Simple_Product("Onboard", "Duty Free", RANDOM_NAME)
            assert UID != None
            logging.info(UID)
            # Get API
            PRODUCTS_URL_GET = PRODUCTS_URL + f"{UID}"
            payload = json.dumps({})
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "GET", PRODUCTS_URL_GET, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"GET API for Product responded with 200 status code")
            sku = resp_data["data"]["product"]["pacSKU"]
            logging.info(sku)
            assert sku == f"PAC_Cred_Automation_Store_{RANDOM_NAME}_{UID}"
            Success_List_Append(
                "test_MKTPL_2504_Unpublishedproduct_007_010_MKTPL_1105_Perishableproduct_016",
                "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_007_010_MKTPL_1105_Perishableproduct_016",
            "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_007_010_MKTPL_1105_Perishableproduct_016",
            "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
            e,
        )
        raise e

def test_MKTPL_2504_Unpublishedproduct_011_012_013_014():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper("test_MKTPL_2504_Unpublishedproduct_011_012_013_014.png") as driver:
            driver.maximize_window()
            wait = WebDriverWait(driver, 60)
            # payload = json.dumps({
            #     # "products":
            #     #   [816312631876]
 
            # })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", PUBLISH_PRODUCT, headers=headers, data="")
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(f"POST API for Product responded with 400 status code without mandatory params")
            # assert "Request JSON body is invalid." in resp_data["message"]
            assert "Request Body cannot Be Empty" in resp_data["message"]
            # MKTPL-2504-Unpublishedproduct-012
            # Get API
            payload = json.dumps({
                "products":
                    [234234234]
 
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", PUBLISH_PRODUCT, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"POST API for Product responded with 200 status code")
            assert [["Invalid Unpublished Product Id 234234234"]] == resp_data["data"]
            # MKTPL-2504-Unpublishedproduct-013
            UID = utils.create_Simple_Product("Onboard", "Duty Free", RANDOM_NAME)
            assert UID != None
            # Get API
            payload = json.dumps({
                "products":
                    [int(UID)]
 
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", PUBLISH_PRODUCT, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"POST API for Product responded with 200 status code")
            assert [[f'Invalid Unpublished Product Id {UID}']] == resp_data["data"]
            # MKTPL - 2504 - Unpublishedproduct - 014
            # Get API
            payload = json.dumps({
                "products":
                    [int(PRODUCT_DIFFERENT_STORE)]
 
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", PUBLISH_PRODUCT, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"POST API for Product responded with 200 status code")
            assert [[f'Invalid Unpublished Product Id {PRODUCT_DIFFERENT_STORE}']] == resp_data["data"]
            Success_List_Append(
                "test_MKTPL_2504_Unpublishedproduct_011_012_013_014",
                "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_011_012_013_014",
            "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_011_012_013_014",
            "Verify when users try to create the new product with same SKU in the store via UI, CSV and API",
            e,
        )
        raise e

def test_MKTPL_1065_CategorychangesinProduct_001_004():
    RANDOM_NAME = "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1065_CategorychangesinProduct_001_004.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.createNewProduct).click()
            sku_field = driver.find_element(By.NAME, xpath.productSkuInputFieldName)
            sku_field.send_keys(RANDOM_NAME)
            driver.find_element(
                By.XPATH,
                '//div[text()="Add new Object of type Product"]/../../../../..'
                + xpath.okButtonXpath
                + "",
            ).click()
            assert (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.mandatoryFieldErrorMessage)
                    )
                )
                .is_displayed()
            )
            logging.info(f"Validation message display")
            logging.info("started 2---------")
            driver.find_element(
                By.XPATH,
                '//div[text()="Error"]/../../../../..' + xpath.okButtonXpath + "",
            ).click()
            store = driver.find_element(By.NAME, xpath.store)
            store.click()
            store.send_keys(DEDICATED_STORE, Keys.ENTER)
            product_set = driver.find_element(
                By.XPATH, '//input[@name="type"]//../following-sibling::div'
            )
            driver.execute_script("arguments[0].click();", product_set)
            time.sleep(1)
            simple_option = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//li[text()="Simple"]'))
            )
            driver.execute_script("arguments[0].click();", simple_option)
            driver.find_element(
                By.XPATH,
                '//div[text()="Add new Object of type Product"]/../../../../..'
                + xpath.okButtonXpath
                + "",
            ).click()
            assert (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.mandatoryFieldErrorMessage)
                    )
                )
                .is_displayed()
            )
            logging.info("Validation message display")

            Success_List_Append(
                "test_MKTPL_1065_CategorychangesinProduct_001_004",
                "Verify if store/category fields are empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1065_CategorychangesinProduct_001_004",
            "Verify if store/category fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1065_CategorychangesinProduct_001_004",
            "Verify if store/category fields are empty",
            e,
        )
        raise e

def test_MKTPL_1065_CategorychangesinProduct_006_008():
    RANDOM_NAME = "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1065_CategorychangesinProduct_006_008.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.createNewProduct).click()
            wait = WebDriverWait(driver, 60)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.NAME, xpath.productSkuInputFieldName)
                )
            ).send_keys(RANDOM_NAME, Keys.TAB)
            store_dropdown = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//input[@name="store"]//../following-sibling::div')
                )
            )
            driver.execute_script("arguments[0].click();", store_dropdown)
            store = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//li[text()="' + DEDICATED_STORE + '"]')
                )
            )
            driver.execute_script("arguments[0].click();", store)
            driver.execute_script("arguments[0].click();", store_dropdown)
            time.sleep(1)
            cat_dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="category"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", cat_dropdown)
            time.sleep(1)
            category = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//li[text()="'
                        + DEDICATED_CATEGORY_2_NAME
                        + " ("
                        + DEDICATED_STORE
                        + "/"
                        + DEDICATED_CATEGORY_3_NAME
                        + "/"
                        + DEDICATED_CATEGORY_2_NAME
                        + ')"])[1]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", category)
            time.sleep(1)
            driver.execute_script("arguments[0].click();", cat_dropdown)
            time.sleep(1)
            product_set = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//input[@name="type"]//../following-sibling::div')
                )
            )
            driver.execute_script("arguments[0].click();", product_set)
            time.sleep(1)
            p_set = wait.until(
                EC.presence_of_element_located((By.XPATH, '//li[text()="Simple"]'))
            )
            driver.execute_script("arguments[0].click();", p_set)
            logging.info("simple")
            driver.find_element(
                By.XPATH,
                '//div[text()="Add new Object of type Product"]/../../../../..'
                + xpath.okButtonXpath
                + "",
            ).click()
            wait.until(EC.visibility_of_element_located((By.NAME, "name"))).send_keys(
                RANDOM_NAME
            )
            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Store "]'))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            assert driver.find_element(
                By.XPATH,
                '//span[text()="Store "]//span[@class="pimcore_object_label_icon pimcore_icon_gray_lock"]',
            ).is_displayed()
            logging.info(
                "Store field should be in read only mode in the page +\n"
                "------ import --------- "
            )
            driver.find_element(By.XPATH, '//span[@class="x-tab-close-btn"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.csvImport))
            )
            time.sleep(5)
            csv_import = driver.find_element(By.XPATH, xpath.csvImport)
            driver.execute_script("arguments[0].click();", csv_import)
            import_data_type = WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.NAME, xpath.importTypeName))
            )
            import_data_type.send_keys("Products")
            import_data_type.send_keys(Keys.ENTER)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.upload))
            ).click()
            assert (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.mandatoryFieldErrorMessage)
                    )
                )
                .is_displayed()
            )
            logging.info("Validation message display")
            Success_List_Append(
                "test_MKTPL_1065_CategorychangesinProduct_006_008",
                "Verify if store/category fields are empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1065_CategorychangesinProduct_006_008",
            "Verify if store/category fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1065_CategorychangesinProduct_006_008",
            "Verify if store/category fields are empty",
            e,
        )
        raise e

def test_MKTPL_1660_VariantAPI_002_003_004_005_006_007_008_010():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        # 002. Verify if invalid category ID is passed in the request
        payload = utils.get_Simple_Product_Payload(
            RANDOM_NAME, "Next Flight", 41, "Simple", "Duty Free"
        )
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Following categories doesn't exist: [41]" in response.json()["message"]
            or "Invalid Category" in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 003. Verify if category ID passed in the request and token used is of different store
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert "Unauthorized access" in response.json()["message"]
        logging.info("API should display validation message")
        # 004. Verify if invalid product set is passed in the request
        payload = utils.get_Simple_Product_Payload(
            RANDOM_NAME,
            "Next Flight",
            int(DEDICATED_CATEGORY_1_ID),
            RANDOM_NAME,
            "Duty Free",
        )
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert "Invalid Product Set." in response.json()["message"]
        logging.info("API should display validation message")
        # 005. Verify if user gives parent product when product set is Simple
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            int(DEDICATED_PRODUCT_11_ID),
            "Next Flight",
            DEDICATED_CATEGORY_1_ID,
            "Simple",
            "Duty Free",
            0,
            190,
            910,
            "RAM",
            "",
            False,
        )

        # payload = json.dumps({
        #     "sku": RANDOM_NAME,
        #     "name": RANDOM_NAME,
        #     "parentProduct": 23,
        #     "shortDescription": "<ol>\n\t<li>this is</li>\n</ol>\n\n<p>shortDescription</p>\n\n<p>ofproduct</p>\n",
        #     "description": "<ul>\n\t<li>this is</li>\n</ul>\n\n<p>Description<br />\nofproduct</p>\n",
        #     "barcode": "TCGDA0011",
        #     "deliveryMethod": [
        #         "Next Flight",
        #         "Gate Pick Up",
        #         "Onboard"
        #     ],
        #     "nextFlightLeadTime": "1",
        #     "gatePickupLeadTime": "889",
        #     "category": [
        #         DEDICATED_CATEGORY_1_ID
        #     ],
        #     "shippingAttributes": {
        #         "weight": "",
        #         "weightUnit": "",
        #         "height": "10",
        #         "heightUnit": "IN",
        #         "width": "10",
        #         "widthUnit": "IN",
        #         "length": "10",
        #         "lengthUnit": "IN"
        #     },
        #     "PriceTaxation": {
        #         "price": [
        #             {
        #                 "value": "",
        #                 "currency": ""
        #             }
        #         ],
        #         "specialPrice": [
        #             {
        #                 "value": 190,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "cost": [
        #             {
        #                 "value": 910,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "isTaxable": True,
        #         "orderMaxQuantityAllowed": 300,
        #         "orderMinQuantityAllowed": 10
        #     },
        #     "brand": "Spark1",
        #     "ageVerificationRequire": True,
        #     "isAvailableToSell": False,
        #     "Attributes": {
        #         "storeSpecificAttributes": [
        #             {
        #                 "key": "",
        #                 "value": ""
        #             }
        #         ],
        #         "productCategoryAttributes": [
        #             {
        #                 "key": "Display",
        #                 "value": "20"
        #             }
        #         ]
        #     },
        #     "notApplicableCountries": [
        #         "IN",
        #         "US"
        #     ],
        #     "productSet": "Simple",
        #     "storeProductId": "134A34",
        #     "productType": "Duty Free",
        #     "spotLight": True,
        #     "isPerishable": False,
        #     "requireShipping": True
        # })
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Parent Product Cannot be set when Product Set is Simple"
            in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 006. Verify if user gives "isVariantAttribute": true in productCategoryAttributes parameter when product set is Simple
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            0,
            "Next Flight",
            int(DEDICATED_CATEGORY_1_ID),
            "Simple",
            "Duty Free",
            0,
            190,
            910,
            "RAM",
            "",
            True,
        )

        # payload = json.dumps({
        #     "sku": RANDOM_NAME,
        #     "name": RANDOM_NAME,
        #     "shortDescription": "<ol>\n\t<li>this is</li>\n</ol>\n\n<p>shortDescription</p>\n\n<p>ofproduct</p>\n",
        #     "description": "<ul>\n\t<li>this is</li>\n</ul>\n\n<p>Description<br />\nofproduct</p>\n",
        #     "barcode": "TCGDA0011",
        #     "deliveryMethod": [
        #         "Next Flight",
        #         "Gate Pick Up",
        #         "Onboard"
        #     ],
        #     "nextFlightLeadTime": "1",
        #     "gatePickupLeadTime": "889",
        #     "category": [
        #         DEDICATED_CATEGORY_1_ID
        #     ],
        #     "shippingAttributes": {
        #         "weight": "",
        #         "weightUnit": "",
        #         "height": "10",
        #         "heightUnit": "IN",
        #         "width": "10",
        #         "widthUnit": "IN",
        #         "length": "10",
        #         "lengthUnit": "IN"
        #     },
        #     "PriceTaxation": {
        #         "price": [
        #             {
        #                 "value": "",
        #                 "currency": ""
        #             }
        #         ],
        #         "specialPrice": [
        #             {
        #                 "value": 190,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "cost": [
        #             {
        #                 "value": 910,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "isTaxable": True,
        #         "orderMaxQuantityAllowed": 300,
        #         "orderMinQuantityAllowed": 10
        #     },
        #     "brand": "Spark1",
        #     "ageVerificationRequire": True,
        #     "isAvailableToSell": False,
        #     "Attributes": {
        #         "storeSpecificAttributes": [
        #             {
        #                 "key": "",
        #                 "value": ""
        #             }
        #         ],
        #         "productCategoryAttributes": [
        #             {
        #                 "key": "Display",
        #                 "value": "20",
        #                 "isVariantAttribute": True
        #             }
        #         ]
        #     },
        #     "notApplicableCountries": [
        #         "IN",
        #         "US"
        #     ],
        #     "productSet": "Simple",
        #     "storeProductId": "134A34",
        #     "productType": "Duty Free",
        #     "spotLight": True,
        #     "isPerishable": False,
        #     "requireShipping": True
        # })
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Variant Attributes cannot be set when Product Set is Simple"
            in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 007. Verify if invalid parent product ID is passed in the request
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            2131231,
            "Next Flight",
            int(DEDICATED_CATEGORY_1_ID),
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "Display",
            "20",
            True,
        )

        # payload = json.dumps({
        #     "sku": RANDOM_NAME,
        #     "name": RANDOM_NAME,
        #     "parentProduct": 232323,
        #     "shortDescription": "<ol>\n\t<li>this is</li>\n</ol>\n\n<p>shortDescription</p>\n\n<p>ofproduct</p>\n",
        #     "description": "<ul>\n\t<li>this is</li>\n</ul>\n\n<p>Description<br />\nofproduct</p>\n",
        #     "barcode": "TCGDA0011",
        #     "deliveryMethod": [
        #         "Next Flight",
        #         "Gate Pick Up",
        #         "Onboard"
        #     ],
        #     "nextFlightLeadTime": "1",
        #     "gatePickupLeadTime": "889",
        #     "category": [
        #         DEDICATED_CATEGORY_1_ID
        #     ],
        #     "shippingAttributes": {
        #         "weight": "",
        #         "weightUnit": "",
        #         "height": "10",
        #         "heightUnit": "IN",
        #         "width": "10",
        #         "widthUnit": "IN",
        #         "length": "10",
        #         "lengthUnit": "IN"
        #     },
        #     "PriceTaxation": {
        #         "price": [
        #             {
        #                 "value": "",
        #                 "currency": ""
        #             }
        #         ],
        #         "specialPrice": [
        #             {
        #                 "value": 190,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "cost": [
        #             {
        #                 "value": 910,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "isTaxable": True,
        #         "orderMaxQuantityAllowed": 300,
        #         "orderMinQuantityAllowed": 10
        #     },
        #     "brand": "Spark1",
        #     "ageVerificationRequire": True,
        #     "isAvailableToSell": False,
        #     "Attributes": {
        #         "storeSpecificAttributes": [
        #             {
        #                 "key": "",
        #                 "value": ""
        #             }
        #         ],
        #         "productCategoryAttributes": [
        #             {
        #                 "key": "Display",
        #                 "value": "20"
        #             }
        #         ]
        #     },
        #     "notApplicableCountries": [
        #         "IN",
        #         "US"
        #     ],
        #     "productSet": "Configurable",
        #     "storeProductId": "134A34",
        #     "productType": "Duty Free",
        #     "spotLight": True,
        #     "isPerishable": False,
        #     "requireShipping": True
        # })
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Invalid parentProduct/Variants Cannot be created for Simple Products."
            in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 008. Verify if parent product ID passed in the request and token used is of different store
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert "Unauthorized access" in response.json()["message"]
        logging.info("API should display validation message")
        # 010. Verify if user gives "isVariantAttribute": false in productCategoryAttributes parameter when product set is Configurable
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            int(DEDICATED_VARIENT_PARENT_PRODUCT_ID),
            "Next Flight",
            int(DEDICATED_CATEGORY_1_ID),
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "Display",
            "20",
            False,
        )
        # payload = json.dumps({
        #     "sku": RANDOM_NAME,
        #     "name": RANDOM_NAME,
        #     "shortDescription": "<ol>\n\t<li>this is</li>\n</ol>\n\n<p>shortDescription</p>\n\n<p>ofproduct</p>\n",
        #     "description": "<ul>\n\t<li>this is</li>\n</ul>\n\n<p>Description<br />\nofproduct</p>\n",
        #     "barcode": "TCGDA0011",
        #     "deliveryMethod": [
        #         "Next Flight",
        #         "Gate Pick Up",
        #         "Onboard"
        #     ],
        #     "nextFlightLeadTime": "1",
        #     "gatePickupLeadTime": "889",
        #     "category": [
        #         DEDICATED_CATEGORY_1_ID
        #     ],
        #     "shippingAttributes": {
        #         "weight": "",
        #         "weightUnit": "",
        #         "height": "10",
        #         "heightUnit": "IN",
        #         "width": "10",
        #         "widthUnit": "IN",
        #         "length": "10",
        #         "lengthUnit": "IN"
        #     },
        #     "PriceTaxation": {
        #         "price": [
        #             {
        #                 "value": "",
        #                 "currency": ""
        #             }
        #         ],
        #         "specialPrice": [
        #             {
        #                 "value": 190,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "cost": [
        #             {
        #                 "value": 910,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "isTaxable": True,
        #         "orderMaxQuantityAllowed": 300,
        #         "orderMinQuantityAllowed": 10
        #     },
        #     "brand": "Spark1",
        #     "ageVerificationRequire": True,
        #     "isAvailableToSell": False,
        #     "Attributes": {
        #         "storeSpecificAttributes": [
        #             {
        #                 "key": "",
        #                 "value": ""
        #             }
        #         ],
        #         "productCategoryAttributes": [
        #             {
        #                 "key": "Display",
        #                 "value": "20",
        #                 "isVariantAttribute": False
        #             }
        #         ]
        #     },
        #     "notApplicableCountries": [
        #         "IN",
        #         "US"
        #     ],
        #     "productSet": "Configurable",
        #     "storeProductId": "134A34",
        #     "productType": "Duty Free",
        #     "spotLight": True,
        #     "isPerishable": False,
        #     "requireShipping": True
        # })
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Variant Attributes needed when product Set is Configurable"
            in response.json()["message"]
        )
        logging.info("API should display validation message")
        Success_List_Append(
            "test_MKTPL_1660_VariantAPI_002_003_004_005_006_007_008_010",
            "Verify if invalid category ID is passed in the request",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1660_VariantAPI_002_003_004_005_006_007_008_010",
            "Verify if invalid category ID is passed in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1660_VariantAPI_002_003_004_005_006_007_008_010",
            "Verify if invalid category ID is passed in the request",
            e,
        )
        raise e

def test_MKTPL_1660_VariantAPI_013_014_015_016_0017_018_019_020_021_022_023_024_025_027():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        # CREATE PRODUCT 'POST'
        idProduct = utils.create_Simple_Product("Next Flight", "Duty Free", RANDOM_NAME)
        logging.info(idProduct)
        URL = PRODUCTS_URL + str(idProduct)
        logging.info(URL)
        # 013. Verify if more than one category ID is passed in the request
        payload = json.dumps(
            {
                "sku": RANDOM_NAME,
                "name": RANDOM_NAME,
                "deliveryMethod": ["Next Flight"],
                "category": [
                    int(DEDICATED_CATEGORY_1_ID),
                    int(DEDICATED_CATEGORY_2_ID),
                ],
                "productSet": "Simple",
                "productType": "Duty Free",
            }
        )

        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 200
        assert "Product updated successfully. " in response.json()["message"]
        logging.info("API should display validation message")
        # 014. Verify if invalid category ID is passed in the request
        payload = utils.get_Simple_Product_Payload(
            RANDOM_NAME, "Next Flight", 41, "Simple", "Duty Free"
        )
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Following categories doesn't exist: [41]" in response.json()["message"]
            or "Invalid Category" in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 015. Verify if category ID passed in the request and token used is of different store
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert "Unauthorized access" in response.json()["message"]
        logging.info("API should display validation message")
        # 016. Verify if user change the category ID in the request
        payload = utils.get_Simple_Product_Payload(
            RANDOM_NAME, "Next Flight", DEDICATED_CATEGORY_2_ID, "Simple", "Duty Free"
        )
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info("API should display validation message")
        # 017. Verify if invalid product set is passed in the request
        payload = utils.get_Simple_Product_Payload(
            RANDOM_NAME,
            "Next Flight",
            DEDICATED_CATEGORY_2_ID,
            RANDOM_NAME,
            "Duty Free",
        )
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert "Invalid Product Set." in response.json()["message"]
        logging.info("API should display validation message")
        # 018. Verify if user change the product set in the request
        payload = utils.get_Simple_Product_Payload(
            RANDOM_NAME,
            "Next Flight",
            DEDICATED_CATEGORY_2_ID,
            "Configurable",
            "Duty Free",
        )

        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Invalid Product Set. Product Set cannot be changed"
            in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 019. Verify if user gives parent product when product set is Simple
        payload = json.dumps(
            {
                "sku": RANDOM_NAME,
                "name": RANDOM_NAME,
                "parentProduct": 23423,
                "deliveryMethod": ["Next Flight"],
                "category": [int(DEDICATED_CATEGORY_1_ID)],
                "productSet": "Simple",
                "productType": "Duty Free",
            }
        )
        response = requests.request("PUT", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Parent Product Cannot be set when Product Set is Simple"
            in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 020. Verify if user gives "isVariantAttribute": true in productCategoryAttributes parameter when product set is Simple
        payload = json.dumps(
            {
                "sku": RANDOM_NAME,
                "name": RANDOM_NAME,
                "deliveryMethod": ["Onboard"],
                "category": [int(DEDICATED_CATEGORY_1_ID)],
                "Attributes": {
                    "storeSpecificAttributes": [{}],
                    "productCategoryAttributes": [
                        {"key": "Display", "value": "20", "isVariantAttribute": True}
                    ],
                },
                "productSet": "Simple",
                "storeProductId": "",
                "productType": "Duty Free",
            }
        )
        response = requests.request("PUT", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Variant Attributes cannot be set when Product Set is Simple"
            in response.json()["message"]
        )
        logging.info("API should display validation message")
        logging.info("---------CREATE NEW PRODUCT----------")
        # CREATE THE PRODUCT WITH PRODUCT SET 'CONFIGURABLE - POST'
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            int(DEDICATED_PRODUCT_11_ID),
            "Next Flight",
            DEDICATED_CATEGORY_1_ID,
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "RAM",
            "",
            True,
        )

        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        updatedIdProduct = response.json()["id"]
        logging.info(updatedIdProduct)
        updatedUrl = PRODUCTS_URL + str(updatedIdProduct)
        logging.info(updatedUrl)
        # 021. Verify if invalid parent product ID is passed in the request
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            312,
            "Next Flight",
            DEDICATED_CATEGORY_1_ID,
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "RAM",
            "",
            True,
        )

        response = requests.request("PUT", updatedUrl, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Parent Product Cannot be changed for Variant" in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 022. Verify if parent product ID passed in the request and token used is of different store
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", updatedUrl, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert "Unauthorized access" in response.json()["message"]
        logging.info("API should display validation message")
        # 023.Verify if user change the parent product for the variant in the request
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            312,
            "Next Flight",
            DEDICATED_CATEGORY_1_ID,
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "RAM",
            "",
            True,
        )
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", updatedUrl, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Parent Product Cannot be changed for Variant" in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 024. Verify if user gives "isVariantAttribute": false in productCategoryAttributes parameter when product set is Configurable
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            int(DEDICATED_PRODUCT_11_ID),
            "Next Flight",
            DEDICATED_CATEGORY_1_ID,
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "RAM",
            "",
            False,
        )

        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", updatedUrl, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Variant Attributes do not match with Product" in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 025. Verify if user gives "isVariantAttribute": true in productCategoryAttributes parameter and it does not matches with category variant theme
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            int(DEDICATED_PRODUCT_11_ID),
            "Next Flight",
            DEDICATED_CATEGORY_1_ID,
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "eeded",
            "20",
            True,
        )
        headers = {
            "language": "en",
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", updatedUrl, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Variant Attributes do not match with Product" in response.json()["message"]
        )
        logging.info("API should display validation message")
        # 027. Verify if user gives "isVariantAttribute": true in productCategoryAttributes parameter for variant and it does not matches with parent product properties
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            int(DEDICATED_PRODUCT_6_ID),
            "Next Flight",
            DEDICATED_CATEGORY_1_ID,
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "RAM",
            "20",
            True,
        )
        response = requests.request("PUT", updatedUrl, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 400 or response.status_code == 401
        assert (
            "Parent Product Cannot be changed for Variant" in response.json()["message"]
        )
        logging.info("API should display validation message")

        Success_List_Append(
            "test_MKTPL_1660_VariantAPI_002_003_004_005_006_007_008_010",
            "Verify if invalid category ID is passed in the request",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1660_VariantAPI_002_003_004_005_006_007_008_010",
            "Verify if invalid category ID is passed in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1660_VariantAPI_002_003_004_005_006_007_008_010",
            "Verify if invalid category ID is passed in the request",
            e,
        )
        raise e

def test_MKTPL_1019_ProductSKU_001():
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_lowercase, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1019_ProductSKU_001.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.createNewProduct).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.NAME, "sku"))
            )
            driver.find_element(By.NAME, "sku").send_keys(RANDOM_NAME + "-_-_")
            time.sleep(3)
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "category")))
                .click()
            )
            time.sleep(1)
            driver.find_element(
                By.XPATH,
                '//li[text()="'
                + DEDICATED_CATEGORY_1_NAME
                + " ("
                + DEDICATED_STORE
                + "/"
                + DEDICATED_CATEGORY_1_NAME
                + ')"]',
            ).click()
            driver.find_element(By.XPATH, "//span[contains(text(),'Category')]").click()
            time.sleep(1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.NAME, "type"))
            )
            driver.find_element(By.NAME, "type").send_keys("Simple")
            driver.find_element(By.XPATH, "//li[contains(text(),'Simple')]").click()
            driver.find_element(By.XPATH, '(//span[text()="OK"])').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveXpath))
            )
            driver.find_element(By.NAME, "name")
            logging.info(f"Successfully shown New Product details screen.")
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            logging.info(UID)
            try:
                url = PRODUCTS_URL + str(UID)
                logging.info(url)
                payload = {}
                headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}
                response = requests.request(
                    "DELETE", url, headers=headers, data=payload
                )
                data = response.json()
                logging.info(data)
                assert response.status_code == 200 or response.status_code == 201

                # assert
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            logging.info(
                f"No validation message should display and record should be inserted in pimcore"
            )
            Success_List_Append(
                "test_MKTPL_1019_ProductSKU_001",
                "Verify when '-' and '_' in given in Product SKU field",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1019_ProductSKU_001",
            "Verify when '-' and '_' in given in Product SKU field",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1019_ProductSKU_001",
            "Verify when '-' and '_' in given in Product SKU field",
            e,
        )
        raise e

def test_MKTPL_1105_Perishableproduct_007_008_009_010():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1105_Perishableproduct_007_008_009_010.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.csvImport))
            )
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, xpath.importTypeName))
            )
            importType = driver.find_element(By.NAME, xpath.importTypeName)
            importType.send_keys("Products")
            importType.send_keys(Keys.ENTER)
            logging.info("select the product")
            time.sleep(2)
            category = driver.find_element(By.NAME, "category[]")
            category.send_keys(
                ""
                + DEDICATED_CATEGORY_1_NAME
                + " ("
                + DEDICATED_STORE
                + "/"
                + DEDICATED_CATEGORY_1_NAME
                + ")"
            )
            category.send_keys(Keys.ENTER)
            time.sleep(3)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.downloadSampleFileProducts)
                )
            )
            driver.find_element(By.XPATH, xpath.downloadSampleFileProducts).click()
            logging.info("sample file download successful")
            time.sleep(5)
            # os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE)
            # time.sleep(2)
            filelist = []
            last_file_time = 0
            for current_file in os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE):
                filelist.append(current_file)
                current_file_fullpath = os.path.join(
                    LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, current_file
                )
                current_file_time = os.path.getctime(current_file_fullpath)
                if os.path.isfile(current_file_fullpath):
                    logging.info("22222")
                    if last_file_time == 0:
                        last_file = current_file
                        logging.info("11111")
                    last_file_time = os.path.getctime(
                        os.path.join(
                            LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file
                        )
                    )
                    logging.info("12313")
                    if current_file_time > last_file_time and os.path.isfile(
                        current_file_fullpath
                    ):
                        last_file = current_file
                        logging.info("444444")
                last_file_fullpath = os.path.join(
                    LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file
                )
            logging.info(last_file_fullpath)
            with open(last_file_fullpath, "r", encoding="utf-8-sig") as csv_file:
                csv_reader = csv.reader(csv_file)
                found_is_perishable = False
                for row in csv_reader:
                    if "isPerishable" in row:
                        found_is_perishable = True
                        break
            assert found_is_perishable
            logging.info(
                "User able to see perishable field column in download CSV file and Column add in sample file as a 'isPerishable'=1 for sample data"
            )

            filepath = '//input[@name = "csvFile"]'
            utils.upload_csv("Product_Sample.csv", filepath)

            logging.info(
                "User check the checkbox then enter value is 'isPerishable'=1 in upload CSV"
            )
            path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"
            # path = os.path.join(os.getcwd()+"\\credencys_test_ui\\", "Product_Sample.csv")
            RANDOM_Name = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=3)
            )
            utils.update_csv("Product_Sample.csv", "name_en", RANDOM_Name)
            utils.update_csv("Product_Sample.csv", "isPerishable", "0")
            logging.info(path)

            driver.find_element(By.NAME, "csvFile").send_keys(path)

            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.upload))
            ).click()

            element = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            xpath.csvUploadMessage,
                        )
                    )
                )
                .is_displayed()
            )
            assert element
            logging.info(
                "User uncheck the checkbox then enter value is 'isPerishable'=0 or blank in upload CSV"
            )
        Success_List_Append(
            "test_MKTPL_1105_Perishableproduct_007_008_009_010",
            "Create the Category in Airline admin and Enter/update the data",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1105_Perishableproduct_007_008_009_010",
            "Create the Category in Airline admin and Enter/update the data",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1105_Perishableproduct_007_008_009_010",
            "Create the Category in Airline admin and Enter/update the data",
            e,
        )
        raise e

def test_MKTPL_1105_Perishableproduct_002_006_004():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1105_Perishableproduct_002_006_004.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            utils.search_by_id(driver, DEDICATED_PRODUCT_10_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productNameFieldXpath)
                )
            )
            scroll = driver.find_element(By.NAME, xpath.isPerishableName)
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("successful scroll")
            try:
                logging.info(scroll.get_attribute("checked"))
                assert not scroll.get_attribute("checked")
                logging.info("Perishable - checked")

            except:
                scroll.click()
                logging.info(scroll.get_attribute("checked"))
                assert not scroll.get_attribute("checked")
                logging.info("Perishable - Not checked")

            scroll.click()
            logging.info("click successful")
            assert scroll.get_attribute("checked")
            logging.info(scroll.get_attribute("checked"))
            logging.info("Perishable checked")
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            logging.info("Save")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Save successful")
        Success_List_Append(
            "test_MKTPL_1105_Perishableproduct_002_006_004",
            "Create the Category in Airline admin and Enter/update the data",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1105_Perishableproduct_002_006_004",
            "Create the Category in Airline admin and Enter/update the data",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1105_Perishableproduct_002_006_004",
            "Create the Category in Airline admin and Enter/update the data",
            e,
        )
        raise e

def test_MKTPL_5502_floatingpoint_001_002():
    global RANDOM_NAME
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5502_floatingpoint_001_002.png"
        ) as driver:
            return_response_1 = utils.create_delivery_rule(
                "shipping",
                "fulfillment_type",
                "domestic",
                "homeShippingDomesticExpress",
                4,
            )
            assert return_response_1 is not None
            try:
                assert "Delivery Rule added successfully" in return_response_1["message"]
                assert return_response_1["code"] == 200
                UID = return_response_1['id']
            except:
                assert "already exist can not create another one" in return_response_1["message"]
                UID = return_response_1["id"]
                headers = {
                    "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                    "Content-Type": "application/json",
                }
                response = requests.request("DELETE", DELIVERY_URL + "/" + str(UID), headers=headers, data={})
                resp_data = response.json()
                logging.info(resp_data)
                return_response_1 = utils.create_delivery_rule(
                    "shipping",
                    "fulfillment_type",
                    "domestic",
                    "homeShippingDomesticExpress",
                    4,
                )
                assert return_response_1 is not None
                assert "Delivery Rule added successfully" in return_response_1["message"]
                assert return_response_1["code"] == 200
                UID = return_response_1["id"]
            wait = WebDriverWait(driver, 60)
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, UID)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.priceFieldXpath))
            )
            driver.find_element(By.XPATH, xpath.priceFieldXpath).send_keys("4.446")
            utils.save_and_publish(driver)
            logging.info("Changed the price to 4.446 => allowed upto three decimals")
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.priceFieldXpath))
            )
            driver.find_element(By.XPATH, xpath.priceFieldXpath).send_keys(
                "4.4462131241"
            )
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(), 'Allowed upto 3 decimal places for Price')]",
                    )
                )
            )
            logging.info(
                "Validation shown as: 'Allowed upto 3 decimal places for Price'"
            )

            # DELETE DELIVERY RULE
            try:
                UID = "ID " + str(UID)
                RT_VALUE = utils.Delete(
                    UID, DELIVERY_URL + "/", CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_001_002",
            "Verify if user enters Price with upto 3 decimal precision in delivery rule",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_001_002",
            "Verify if user enters Price with upto 3 decimal precision in delivery rule",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5502_floatingpoint_001_002",
            "Verify if user enters Price with upto 3 decimal precision in delivery rule",
            e,
        )
        raise e

def test_MKTPL_5502_floatingpoint_003_004_005():
    global RANDOM_NAME
    try:
        # 004 POST
        return_response = utils.create_delivery_rule(
            "shipping",
            "fulfillment_type",
            "domestic",
            "homeShippingDomesticExpress",
            4.44634,
        )
        assert return_response is not None
        try:
            assert "Allowed upto 3 decimal places for Price" in return_response["message"]
            assert return_response["code"] == 400
        except:
            assert "already exist can not create another one" in return_response["message"]
            UID = return_response["id"]
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("DELETE", DELIVERY_URL + "/" + str(UID), headers=headers, data={})
            resp_data = response.json()
            logging.info(resp_data)
            return_response = utils.create_delivery_rule(
                "shipping",
                "fulfillment_type",
                "domestic",
                "homeShippingDomesticExpress",
                4,
            )
            assert return_response is not None
            assert "Allowed upto 3 decimal places for Price" in return_response["message"]
            assert return_response["code"] == 400
        logging.info("Validation shown for POST request for more than 3 decimals")

        # 003. Verify if user enters Price with upto 3 decimal precision in delivery rule
        return_response_1 = utils.create_delivery_rule(
            "shipping",
            "fulfillment_type",
            "domestic",
            "homeShippingDomesticExpress",
            4.446,
        )
        assert return_response_1 is not None
        assert "Delivery Rule added successfully" in return_response_1["message"]
        assert return_response_1["code"] == 200
        logging.info("Delivery rule created")

        UID_1 = return_response_1["id"]
        URL = DELIVERY_URL + "/" + str(UID_1)
        logging.info(URL)
        # 003 PUT
        payload = json.dumps(
            {
                "deliveryRuleType": "shipping",
                "shipmentGroupingType": "fulfillment_type",
                "shippingDestination": "domestic",
                "deliveryType": ["homeShippingDomesticExpress"],
                "title": "express",
                "description": "express",
                "price": 1.223,
                "priceUnit": "USD",
                "duration": 44,
                "durationType": "days",
            }
        )
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("PUT", URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        assert "Delivery Rule updated successfully" in resp_data["message"]
        assert response.status_code == 200
        logging.info("Validation not shown for PUT request for upto 3 decimals")

        # 003 PATCH
        payload = json.dumps({"price": 44.366, "priceUnit": "USD"})
        response = requests.request("PATCH", URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        assert return_response_1 is not None
        assert "Delivery Rule updated successfully" in resp_data["message"]
        assert response.status_code == 200
        logging.info("Validation not shown for PATCH request for upto 3 decimals")

        # 004. Verify if user enters Price with more than 3 decimal precision in delivery rule

        # PUT
        payload = json.dumps(
            {
                "deliveryRuleType": "shipping",
                "shipmentGroupingType": "fulfillment_type",
                "shippingDestination": "domestic",
                "deliveryType": ["homeShippingDomesticExpress"],
                "title": "express",
                "description": "express",
                "price": 1.22353453,
                "priceUnit": "USD",
                "duration": 44,
                "durationType": "days",
            }
        )

        response = requests.request("PUT", URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        assert "Allowed upto 3 decimal places for Price" in resp_data["message"]
        assert response.status_code == 400
        logging.info("Validation shown for PUT request for more than 3 decimals")

        # 004 PATCH
        payload = json.dumps({"price": 44.366234, "priceUnit": "USD"})
        response = requests.request("PATCH", URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        assert "Allowed upto 3 decimal places for Price" in resp_data["message"]
        assert response.status_code == 400
        logging.info("Validation shown for PATCH request for more than 3 decimals")

        # 005. Verify the GET delivery rule API when Price with upto 3 decimal precision
        payload = json.dumps({})
        response = requests.request("GET", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        assert float(44.366) == data["data"]["deliveryRule"]["price"]
        logging.info(
            "Price with upto 3 decimal precision display in GET delivery rule API"
        )
        # DELETE API
        response = requests.request("DELETE", URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        assert "Delivery Rule deleted successfully." in data["message"]
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_003_004_005",
            "Verify if user enters Price with upto 3 decimal precision in delivery rule",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_003_004_005",
            "Verify if user enters Price with upto 3 decimal precision in delivery rule",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5502_floatingpoint_003_004_005",
            "Verify if user enters Price with upto 3 decimal precision in delivery rule",
            e,
        )
        raise e

def test_MKTPL_5502_floatingpoint_006_007():
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=4)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5502_floatingpoint_006_007.png"
        ) as driver:
            UID = utils.create_Simple_Product(
                "Onboard", "Food and Beverage", RANDOM_NAME
            )
            assert UID is not None
            wait = WebDriverWait(driver, 60)
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, UID)
            driver.find_element(By.XPATH, xpath.priceAndTaxationPageXpath).click()
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Price Details"]')
                )
            )
            driver.find_element(By.NAME, xpath.priceUSDName).send_keys("4.456")
            utils.save_and_publish(driver)
            logging.info("Allowed upto 3 decimals for price_USD field")
            driver.find_element(By.NAME, xpath.specialpriceUSDName).send_keys("4.456")
            utils.save_and_publish(driver)
            logging.info("Allowed upto 3 decimals for specialprice_USD field")
            driver.find_element(By.NAME, xpath.priceUSDName).clear()
            driver.find_element(By.NAME, xpath.priceUSDName).send_keys("4.45613232")
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(), 'Allowed up to 3 decimal places for price_USD')]",
                    )
                )
            )
            logging.info(
                "Validation shown as: 'Allowed upto 3 decimal places for price_USD'"
            )
            driver.find_element(By.XPATH, "(//span[text()='OK'])[last()]").click()
            driver.find_element(By.NAME, xpath.specialpriceUSDName).clear()
            driver.find_element(By.NAME, xpath.specialpriceUSDName).send_keys(
                "4.45613232"
            )
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(), 'Allowed up to 3 decimal places for specialPrice_USD')]",
                    )
                )
            )
            logging.info(
                "Validation shown as: 'Allowed upto 3 decimal places for Price'"
            )
            # DELETE PRODUCT
            try:
                UID = "ID " + str(UID)
                RT_VALUE = utils.Delete(UID, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_006_007",
            "Verify if user enters Price, special price and cost with upto 3 decimal precision in product",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_006_007",
            "Verify if user enters Price, special price and cost with upto 3 decimal precision in product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5502_floatingpoint_006_007",
            "Verify if user enters Price, special price and cost with upto 3 decimal precision in product",
            e,
        )
        raise e

def test_MKTPL_5502_floatingpoint_008_009():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper(
            "test_MKTPL_5502_floatingpoint_008_009.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(3)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[@id="categoryField-trigger-picker"]')
                    )
                )
                .click()
            )
            time.sleep(2)
            driver.find_element(
                By.XPATH, f"//li[contains(text(),'{DEDICATED_CATEGORY_1_NAME}')]"
            ).click()
            file = "Product_Sample_price_validation.csv"
            utils.update_csv(file, "name_en", RANDOM_NAME)
            utils.update_csv(file, "price_USD", 18.234234)
            utils.update_csv(file, "specialPrice_USD", 18.4574567)
            # path = os.getcwd() + "/credencys_test_ui/" + file
            # driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            # driver.find_element(By.XPATH, xpath.upload).click()
            utils.upload_csv_with_specific_validation(
                file, xpath.csvFileSelectButton, "CSV Uploaded Successfully"
            )
            # WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[text()='Failed']")))
            # driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets_import_Store(driver)
            list1 = driver.find_elements(
                By.XPATH,
                "//div[contains(text(),'/Import/"
                + PAC_STORE_USER_MAIL
                + "/products')]",
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'/Import/"
                + PAC_STORE_USER_MAIL
                + f"/products')])[{num}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info("open clicked")
            # Open Code if modal appears
            try:
                if driver.find_element(By.XPATH, xpath.yes).is_displayed():
                    driver.find_element(By.XPATH, xpath.yes).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),\"Allowed only 3 decimal places in price'price_USD': 18.234234 | Allowed only 3 decimal places in price'specialPrice_USD': 18.4574567\")]",
                    )
                )
            )

            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(3)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[@id="categoryField-trigger-picker"]')
                    )
                )
                .click()
            )
            time.sleep(2)
            driver.find_element(
                By.XPATH, f"//li[contains(text(),'{DEDICATED_CATEGORY_1_NAME}')]"
            ).click()
            utils.update_csv(file, "name_en", RANDOM_NAME)
            utils.update_csv(file, "price_USD", 18.234)
            utils.update_csv(file, "specialPrice_USD", 18.567)
            utils.upload_csv_with_specific_validation(
                file, xpath.csvFileSelectButton, "CSV Uploaded Successfully"
            )
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            time.sleep(3)
            list1 = driver.find_elements(
                By.XPATH, "(//div[contains(text(),'product_publish')])"
            )
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            time.sleep(2)
            try:
                if driver.find_element(
                    By.XPATH, xpath.yes
                ).is_displayed():
                    driver.find_element(
                        By.XPATH, xpath.yes
                    ).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{RANDOM_NAME}')]")
                )
            )
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{RANDOM_NAME}')]"
            ).is_displayed()
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys("specialPrice")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'18.234')]"
            ).is_displayed()
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'18.567')]"
            ).is_displayed()
            logging.info(
                "product_publish SNS Payload include the Price, special price parameter when Price, "
                "special price with upto 3 decimal precision in product"
            )

            Success_List_Append(
                "test_MKTPL_5502_floatingpoint_008_009",
                "Verify if user enters Price, special price and cost with upto 3 decimal precision in product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_008_009",
            "Verify if user enters Price, special price and cost with upto 3 decimal precision in product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5502_floatingpoint_008_009",
            "Verify if user enters Price, special price and cost with upto 3 decimal precision in product",
            e,
        )
        raise e

def test_MKTPL_5502_floatingpoint_010_012():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5502_floatingpoint_010_012.png"
        ) as driver:
            # 010. Verify if user enters Price, special price and cost with upto 3 decimal precision in product
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                0,
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Simple",
                "Duty Free",
                7.988,
                1.98,
                910,
                "Display",
                "20",
                False,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Product added successfully. " in data["message"]
            logging.info(
                "Price, special price and cost with upto 3 decimal precision should be allowed from Post API in product"
            )
            idProduct = response.json()["id"]
            logging.info(idProduct)
            URL = PRODUCTS_URL + str(idProduct)
            logging.info(URL)
            # 012. Verify the GET product API when Price, special price and cost with upto 3 decimal precision
            payload = json.dumps({})
            response = requests.request("GET", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            # assert float(7.988) == data['data']['product']['PriceTaxation']['price'][0]['value']
            # assert float(1.98) == data['data']['product']['PriceTaxation']['specialPrice'][0]['value']
            logging.info(
                "Price, special price and cost with upto 3 decimal precision  display in GET product API"
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            time.sleep(3)
            list1 = driver.find_elements(
                By.XPATH, "(//div[contains(text(),'product_publish')])"
            )
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            utils.click_on_yes_no(driver)
            # open1 = driver.find_element(By.XPATH, xpath.open)
            # open1.click()
            # try:
            #     if driver.find_element(
            #         By.XPATH, xpath.yes
            #     ).is_displayed():
            #         driver.find_element(
            #             By.XPATH, xpath.yes
            #         ).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{idProduct}')]")
                )
            )
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{idProduct}')]"
            ).is_displayed()
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys("1.98")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'7.988')]"
            ).is_displayed()
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'1.98')]"
            ).is_displayed()
            logging.info(
                "product_publish SNS Payload include the Price, special price parameter when Price, "
                "special price with upto 3 decimal precision in product"
            )

            # PUT API check
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                0,
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Simple",
                "Duty Free",
                7.123,
                1.123,
                910,
                "Display",
                "20",
                False,
            )
            response = requests.request("PUT", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Product updated successfully. " in data["message"]
            logging.info(
                "Price, special price and cost with upto 3 decimal precision should be allowed from Put API in product"
            )
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            time.sleep(3)
            list1 = driver.find_elements(
                By.XPATH, "(//div[contains(text(),'product_publish')])"
            )
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            utils.click_on_yes_no(driver)
            # open1 = driver.find_element(By.XPATH, xpath.open)
            # open1.click()
            # try:
            #     if driver.find_element(
            #         By.XPATH, xpath.yes
            #     ).is_displayed():
            #         driver.find_element(
            #             By.XPATH, xpath.yes
            #         ).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{idProduct}')]")
                )
            )
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{idProduct}')]"
            ).is_displayed()
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys("1.98")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'7.123')]"
            ).is_displayed()
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'1.123')]"
            ).is_displayed()
            logging.info(
                "product_publish SNS Payload include the Price, special price parameter when Price, "
                "special price with upto 3 decimal precision in product"
            )
            # PATCH API check
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "PriceTaxation": {
                        "price": [{"value": "8.234", "currency": "USD"}],
                        "specialPrice": [{"value": "2.234", "currency": "USD"}],
                        # "cost": [{"value": 910, "currency": "USD"}],
                        "isTaxable": True,
                        "orderMaxQuantityAllowed": 300,
                        "orderMinQuantityAllowed": 10,
                    },
                }
            )
            response = requests.request("PATCH", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Product updated successfully. " in data["message"]
            logging.info(
                "Price, special price and cost with upto 3 decimal precision should be allowed from Patch API in product"
            )
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            time.sleep(3)
            list1 = driver.find_elements(
                By.XPATH, "(//div[contains(text(),'product_publish')])"
            )
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            utils.click_on_yes_no(driver)
            # open1 = driver.find_element(By.XPATH, xpath.open)
            # open1.click()
            # try:
            #     if driver.find_element(
            #         By.XPATH, xpath.yes
            #     ).is_displayed():
            #         driver.find_element(
            #             By.XPATH, xpath.yes
            #         ).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{idProduct}')]")
                )
            )
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{idProduct}')]"
            ).is_displayed()
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys("1.98")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'8.234')]"
            ).is_displayed()
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'2.234')]"
            ).is_displayed()
            logging.info(
                "product_publish SNS Payload include the Price, special price parameter when Price, "
                "special price with upto 3 decimal precision in product"
            )
            # DELETE API
            response = requests.request("DELETE", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            Success_List_Append(
                "test_MKTPL_5502_floatingpoint_010_012",
                "Verify if user enters Price with upto 3 decimal precision in delivery rule",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_010_012",
            "Verify if user enters Price with upto 3 decimal precision in delivery rule",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5502_floatingpoint_010_012",
            "Verify if user enters Price with upto 3 decimal precision in delivery rule",
            e,
        )
        raise e

def test_MKTPL_5502_floatingpoint_011():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_NAME_1 = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5502_floatingpoint_011.png"
        ) as driver:
            # 010. Verify if user enters Price, special price and cost with upto 3 decimal precision in product
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                int(DEDICATED_PRODUCT_11_ID),
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                1.23,
                1.90,
                910,
                "RAM",
                "",
                True,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Product added successfully. " in data["message"]
            logging.info(
                "Price, special price and cost with upto 3 decimal precision should be allowed from Post API in product"
            )
            idProduct = response.json()["id"]
            logging.info(idProduct)
            URL = PRODUCTS_URL + str(idProduct)
            logging.info(URL)
            # 011. Verify if user enters Price, special price and cost with more than 3 decimal precision in product
            payload = utils.get_Product_Payload(
                RANDOM_NAME_1,
                int(DEDICATED_PRODUCT_11_ID),
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                7.98123,
                1.98234,
                910,
                "RAM",
                "",
                True,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 400
            assert "Allowed up to 3 decimal places for price" in data["message"]
            # 011. POST special price
            payload = utils.get_Product_Payload(
                RANDOM_NAME_1,
                int(DEDICATED_PRODUCT_11_ID),
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                7.98,
                1.98123,
                910,
                "RAM",
                "",
                True,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 400
            assert "Allowed up to 3 decimal places for specialPrice" in data["message"]
            # # 011. POST cost
            # payload = utils.get_Product_Payload(
            #     RANDOM_NAME_1,
            #     int(DEDICATED_PRODUCT_11_ID),
            #     "Next Flight",
            #     DEDICATED_CATEGORY_1_ID,
            #     "Configurable",
            #     "Duty Free",
            #     7.98,
            #     1.94,
            #     5.24234,
            #     "RAM",
            #     "",
            #     True,
            # )
            # headers = {
            #     "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            #     "Content-Type": "application/json",
            # }
            # response = requests.request(
            #     "POST", PRODUCTS_URL, headers=headers, data=payload
            # )
            # data = response.json()
            # logging.info(data)
            # assert response.status_code == 400
            # assert "Allowed up to 3 decimal places for cost" in data["message"]
            # PUT  price
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                int(DEDICATED_PRODUCT_11_ID),
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                7.91328,
                1.98234,
                910,
                "RAM",
                "",
                True,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("PUT", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400
            assert "Allowed up to 3 decimal places for price" in data["message"]
            # PUT special price
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                int(DEDICATED_PRODUCT_11_ID),
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                7.913,
                1.98234,
                910,
                "RAM",
                "",
                True,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("PUT", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400
            assert "Allowed up to 3 decimal places for specialPrice" in data["message"]
            logging.info(
                "Price, special price and cost more than 3 decimal precision should not be allowed from Post API in product"
            )

            # # PUT API cost
            # payload = utils.get_Product_Payload(
            #     RANDOM_NAME,
            #     int(DEDICATED_PRODUCT_11_ID),
            #     "Next Flight",
            #     DEDICATED_CATEGORY_1_ID,
            #     "Configurable",
            #     "Duty Free",
            #     7.122,
            #     1.123,
            #     910.456234,
            #     "RAM",
            #     "",
            #     True,
            # )
            # response = requests.request("PUT", URL, headers=headers, data=payload)
            # data = response.json()
            # logging.info(data)
            # assert response.status_code == 400
            # assert "Allowed up to 3 decimal places for cost" in data["message"]
            # logging.info(
            #     "Price, special price and cost with more than 3 decimal precision should not be allowed from Put API in product"
            # )
            # PATCH API check
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "PriceTaxation": {
                        "price": [{"value": "8.212434", "currency": "USD"}],
                        "specialPrice": [{"value": "2.223134", "currency": "USD"}],
                        # "cost": [{"value": "910.23846", "currency": "USD"}],
                        "isTaxable": True,
                        "orderMaxQuantityAllowed": 300,
                        "orderMinQuantityAllowed": 10,
                    },
                }
            )
            response = requests.request("PATCH", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400
            assert "Allowed up to 3 decimal places for price" in data["message"]
            # PATCH API check

            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "PriceTaxation": {
                        "price": [{"value": "8.234", "currency": "USD"}],
                        "specialPrice": [{"value": "2.223134", "currency": "USD"}],
                        # "cost": [{"value": "910.23846", "currency": "USD"}],
                        "isTaxable": True,
                        "orderMaxQuantityAllowed": 300,
                        "orderMinQuantityAllowed": 10,
                    },
                }
            )

            response = requests.request("PATCH", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400
            assert "Allowed up to 3 decimal places for specialPrice" in data["message"]
            # PATCH API check

            # payload = json.dumps(
            #     {
            #         "sku": RANDOM_NAME,
            #         "PriceTaxation": {
            #             "price": [{"value": "8.234", "currency": "USD"}],
            #             "specialPrice": [{"value": "2.22", "currency": "USD"}],
            #             "cost": [{"value": "910.23846", "currency": "USD"}],
            #             "isTaxable": True,
            #             "orderMaxQuantityAllowed": 300,
            #             "orderMinQuantityAllowed": 10,
            #         },
            #     }
            # )
            # response = requests.request("PATCH", URL, headers=headers, data=payload)
            # data = response.json()
            # logging.info(data)
            # assert response.status_code == 400
            # assert "Allowed up to 3 decimal places for cost" in data["message"]
            logging.info(
                "Price, special price and cost with more than 3 decimal precision should not be allowed from Patcht API in product"
            )
            # DELETE API
            response = requests.request("DELETE", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Product deleted successfully" in data["message"]
            Success_List_Append(
                "test_MKTPL_5502_floatingpoint_011",
                "Verify if user enters Price, special price and cost with more than 3 decimal precision in product",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5502_floatingpoint_011",
            "Verify if user enters Price, special price and cost with more than 3 decimal precision in product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5502_floatingpoint_011",
            "Verify if user enters Price, special price and cost with more than 3 decimal precision in product",
            e,
        )
        raise e

def test_MKTPL_1105_Perishableproduct_013_014_015_017():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper(
            "test_MKTPL_1105_Perishableproduct_013_014_015_017.png"
        ) as driver:
            # MKTPL - 1105 - Perishableproduct - 013
            # MKTPL - 1105 - Perishableproduct - 014
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "productType": "Duty Free",
                    "isPerishable": True,
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API for Product responded with 200 status code")
            resp_data = response.json()
            logging.info(resp_data)
            assert "Product added successfully. " in resp_data["message"]
            product_id = resp_data["id"]
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, product_id)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productNameFieldXpath)
                )
            )
            scroll = driver.find_element(By.NAME, xpath.isPerishableName)
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("Scrolled")
            logging.info(scroll.get_attribute("checked"))
            assert scroll.get_attribute("checked")
            logging.info("Perishable - checked")
            # MKTPL-1105-Perishableproduct-015
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "productType": "Duty Free",
                    "isPerishable": False,
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            PRODUCTS_URL_PUT = PRODUCTS_URL + str(product_id)
            response = requests.request(
                "PUT", PRODUCTS_URL_PUT, headers=headers, data=payload
            )
            resp_data = response.json()
            print(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API for Product responded with 200 status code")
            resp_data = response.json()
            logging.info(resp_data)
            assert "Product updated successfully. " in resp_data["message"]
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, product_id)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productNameFieldXpath)
                )
            )
            scroll = driver.find_element(By.NAME, xpath.isPerishableName)
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("Scrolled")
            logging.info(scroll.get_attribute("checked"))
            assert not scroll.get_attribute("checked")
            logging.info("Perishable - not checked")
            try:
                product_id = "ID " + str(product_id)
                RT_VALUE = utils.Delete(
                    product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )

                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
            # MKTPL - 1105 - Perishableproduct - 017
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "isPerishable": "28364",
                    "productType": "Duty Free"
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(f"POST API for Product responded with 400 status code")
            assert (
                "isPerishable"
                in resp_data["message"]
            )
            Success_List_Append(
                "test_MKTPL_1105_Perishableproduct_013_014_015_017",
                "Verify the add perishable field value by API",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1105_Perishableproduct_013_014_015_017",
            "Verify the add perishable field value by API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1105_Perishableproduct_013_014_015_017",
            "Verify the add perishable field value by API",
            e,
        )
        raise e

def test_MKTPL_967_ProductAPI_001_002_008_009():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper(
            "test_MKTPL_967_ProductAPI_001_002_008_009.png"
        ) as driver:
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                0,
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                0,
                190,
                910,
                "",
                "",
                True,
            )

            # MKTPL-967-ProductAPI-008
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(
                f"POST API for Product responded with 400 status code for empty key for product category attributes"
            )

            assert (
                "Variant Attributes do not match with Category Variant Themes "
                in resp_data["message"]
            )
            # MKTPL-967-ProductAPI-009
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                0,
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                0,
                190,
                910,
                "",
                "",
                True,
            )
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(
                f"POST API for Product responded with 400 status code for empty key for product category attributes"
            )

            # assert "Variant Attributes do not match with Category Variant Themes " in resp_data["message"]

            # MKTPL-967-ProductAPI-001
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                0,
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                0,
                190,
                910,
                "RAM",
                "8GB",
                True,
            )
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            product_id = resp_data["id"]

            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                0,
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                0,
                190,
                910,
                "",
                "8GB",
                True,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "PUT", PRODUCTS_URL + str(product_id), headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(
                f"POST API for Product responded with 400 status code for empty key for product category attributes"
            )
            assert (
                "Variant Attributes do not match with Product"
                in resp_data["message"]
            )
            payload = utils.get_Product_Payload(
                RANDOM_NAME,
                0,
                "Next Flight",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                0,
                190,
                910,
                "RAM",
                "",
                True,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "PUT", PRODUCTS_URL + str(product_id), headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(
                f"POST API for Product responded with 400 status code for empty key for product category attributes"
            )
            assert (
                "Variant Attributes do not match with Category Variant Themes "
                in resp_data["message"]
            )
            # assert "Variant Attributes do not match with Category Variant Themes " in resp_data["message"]
            Success_List_Append(
                "test_MKTPL_967_ProductAPI_001_002_008_009",
                "Verify if the mandatory product category attributes is empty in the key in the request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_967_ProductAPI_001_002_008_009",
            "Verify if the mandatory product category attributes is empty in the key in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_967_ProductAPI_001_002_008_009",
            "Verify if the mandatory product category attributes is empty in the key in the request",
            e,
        )
        raise e

def test_MKTPL_1660_VariantAPI_009_010_011():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        RANDOM_NAME_2 = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper(
            "test_MKTPL_1660_VariantAPI_009_010_011.png"
        ) as driver:
            product_id = utils.create_Simple_Product(
                "Onboard", "Duty Free", RANDOM_NAME
            )
            assert product_id is not None

            # MKTPL-1660-VariantAPI-009
            payload = utils.get_Product_Payload(
                RANDOM_NAME_2,
                int(product_id),
                "Onboard",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                0,
                0,
                0,
                "RAM",
                "",
                True,
            )

            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(f"POST API for Product responded with 400 status code ")
            resp_data = response.json()
            logging.info(resp_data)
            assert (
                "Invalid parentProduct/Variants Cannot be created for Simple Products."
                in resp_data["message"]
            )
            try:
                product_id = "ID " + str(product_id)
                RT_VALUE = utils.Delete(
                    product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )

                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
            # MKTPL-1660-VariantAPI-010
            payload = utils.get_Product_Payload(
                RANDOM_NAME_2,
                int(DEDICATED_VARIENT_PARENT_PRODUCT_ID),
                "Onboard",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                0,
                0,
                0,
                "RAM",
                "",
                False,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(f"POST API for Product responded with 400 status code ")
            resp_data = response.json()
            logging.info(resp_data)
            assert (
                "Variant Attributes needed when product Set is Configurable"
                in resp_data["message"]
            )
            # MKTPL-1660-VariantAPI-011
            payload = utils.get_Product_Payload(
                RANDOM_NAME_2,
                int(DEDICATED_VARIENT_PARENT_PRODUCT_ID),
                "Onboard",
                DEDICATED_CATEGORY_1_ID,
                "Configurable",
                "Duty Free",
                0,
                0,
                0,
                RANDOM_NAME,
                "",
                True,
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(f"POST API for Product responded with 400 status code ")
            resp_data = response.json()
            logging.info(resp_data)
            assert (
                "Variant Attributes do not match with Category Variant Themes "
                in resp_data["message"]
            )

            Success_List_Append(
                "test_MKTPL_1660_VariantAPI_009_010_011",
                "Verify if user try to create variants for a parent product whose product set is Simple",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1660_VariantAPI_009_010_011",
            "Verify if user try to create variants for a parent product whose product set is Simple",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1660_VariantAPI_009_010_011",
            "Verify if user try to create variants for a parent product whose product set is Simple",
            e,
        )
        raise e

def test_MKTPL_4865_Productdate_001():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper(
            "test_MKTPL_4865_Productdate_001.png"
        ) as driver:
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "newFrom": "2026-02-04",
                    "newTo": "2026-02-04",
                    "productSet": "Simple",
                    "productType": "Food and Beverage",
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            print(resp_data)
            assert response.status_code == 400
            logging.info(f"POST API for Product responded with 400 status code")
            resp_data = response.json()
            logging.info(resp_data)
            assert "From and To date should be different" in resp_data["message"]
            Success_List_Append(
                "test_MKTPL_4865_Productdate_001",
                "Verify if the newFrom & newTo fields in product is same",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_4865_Productdate_001",
            "Verify if the newFrom & newTo fields in product is same",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_4865_Productdate_001",
            "Verify if the newFrom & newTo fields in product is same",
            e,
        )
        raise e

def test_MKTPL_2016_SNS_001():
    try:
        RANDOM_NAME = "".join(random.choices(string.ascii_letters, k=7))
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper("test_MKTPL_2016_SNS_001.png") as driver:
            # create Parent product
            parentProductID = utils.create_Parent_Product(
                "Configurable", "Next Flight", "Duty Free"
            )
            logging.info(f"Parent Product ID- {parentProductID}")
            assert parentProductID is not None
            VARIENT_ID = utils.create_Variant_Product(
                1, int(parentProductID), "Configurable", "Next Flight", "Duty Free"
            )
            logging.info(f"varient Product ID- {VARIENT_ID}")
            assert VARIENT_ID is not None
            time.sleep(10)
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, parentProductID)
            driver.find_element(By.XPATH, xpath.variantsPageXpath).click()
            assert driver.find_element(
                By.XPATH, "(//table[contains(@id,'patchedgrid')])[1]"
            ).is_displayed()
            count_ele = driver.find_elements(
                By.XPATH, "//span[contains(@class,'checked')]"
            )
            count = len(count_ele)
            logging.info(count)
            logging.info(f"Number of variants: {count}")
            driver.find_element(By.XPATH, "//span[text()='Edit']").click()
            scroll = driver.find_element(By.NAME, xpath.isPerishableName)
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("Scrolled")
            driver.find_element(By.NAME, "barcode").clear()
            driver.find_element(By.NAME, "barcode").send_keys(RANDOM_NAME)
            logging.info(f"Entered barcode as {RANDOM_NAME}")
            utils.save_and_publish(driver)
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(text(),'product_publish')]")
                )
            )
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            parent_sns_num = num - ((count + 1) * 2 - 1)
            # variant_sns_num = parent_sns_num + 2
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'product_publish')])[{parent_sns_num}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'product_publish')])[{parent_sns_num}]",
            )
            parentProductSnsText = row.text
            logging.info(f"Parent Product text- {parentProductSnsText}")
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass
            # UID = UID.split()
            # UID = UID[1]
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{parentProductID}')])[1]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"(//span[contains(text(),'{parentProductID}')])[1]"
            ).is_displayed()
            logging.info(f"Asserted the product name as {parentProductID}")
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(RANDOM_NAME)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{RANDOM_NAME}')]"
            ).is_displayed()
            logging.info(f"Asserted the description as {RANDOM_NAME}")
            for x in range(count):
                parentProductSnsText = parentProductSnsText.split(".")
                varaientName = (
                    parentProductSnsText[0]
                    + "_"
                    + str(x + 1)
                    + "."
                    + parentProductSnsText[1]
                )
                close_button = driver.find_element(
                    By.XPATH, "(//span[@class='x-tab-close-btn'])[last()]"
                ).click()
                wait.until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[contains(text(),"' + varaientName + '")]')
                    )
                )
                # Generate list and get the last row number with store publish
                list1 = driver.find_elements(
                    By.XPATH, '//div[contains(text(),"' + varaientName + '")]'
                )
                # num = len(list1)
                wait.until(
                    EC.presence_of_element_located(
                        (By.XPATH, f'(//div[contains(text(),"' + varaientName + '")])')
                    )
                )
                # Click on the last created entry
                row = driver.find_element(
                    By.XPATH, f'(//div[contains(text(),"' + varaientName + '")])'
                )
                actions = ActionChains(driver)
                actions.double_click(row).perform()
                logging.info(("Opened the entry"))
                try:
                    driver.find_element("xpath", '//span[text()="Yes"]').click()
                    logging.info("Click on message")
                except:
                    pass
                var = VARIENT_ID[x]
                wait.until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//span[contains(text(),"' + str(var) + '")])')
                    )
                )
                time.sleep(5)
                assert driver.find_element(
                    By.XPATH, '(//span[contains(text(),"' + str(var) + '")])'
                ).is_displayed()
                logging.info(f"Asserted the product name as v")
            try:
                url_parent_product = PRODUCTS_URL + str(parentProductID)
                payload = ""
                headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}
                response = requests.delete(
                    url_parent_product, headers=headers, data=payload
                )
                assert response.status_code == 200 or response.status_code == 201
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

            Success_List_Append(
                "test_MKTPL_2016_SNS_001",
                "Verify when variant has inherited data from parent product and data  is changed in parent product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2016_SNS_001",
            "Verify when variant has inherited data from parent product and data  is changed in parent product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2016_SNS_001",
            "Verify when variant has inherited data from parent product and data  is changed in parent product",
            e,
        )
        raise e

def test_MKTPL_2082_SNS_001_MKTPL_2713_SNS_001_MKTPL_2925_SNS_002_MKTPL_1105_Perishableproduct_003_MKTPL_2723_SNS_001():
    try:
        RANDOM_NAME = "".join(random.choices(string.ascii_letters, k=7))
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper(
            "test_MKTPL_2082_SNS_001_MKTPL_2713_SNS_001_MKTPL_2925_SNS_002_MKTPL_1105_Perishableproduct_003_MKTPL_2723_SNS_001.png"
        ) as driver:
            wait = WebDriverWait(driver, 60)
            product_id = utils.create_Simple_Product(
                "Onboard", "Duty Free", RANDOM_NAME
            )
            assert product_id != None
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(text(),'product_publish')]")
                )
            )
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'product_publish')])[{num - 1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{product_id}')])[1]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"(//span[contains(text(),'{product_id}')])[1]"
            ).is_displayed()
            logging.info(f"Asserted the product name as {product_id}")
            assert not len(
                driver.find_elements(
                    By.XPATH, f"//span[contains(text(),'categorySpecificAttributes')]"
                )
            )
            logging.info("categorySpecificAttributes is not displayed")
            assert not len(
                driver.find_elements(
                    By.XPATH, f"//span[contains(text(),'specialPrice')]"
                )
            )
            logging.info("specialPrice is not displayed")
            inflightCurrent = driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'deliveryMethod')]//parent::div//following-sibling::div)[3]//span[contains(text(),'inflightCurrent')]",
            ).is_displayed()
            assert inflightCurrent
            logging.info("inflightCurrent is shown in deliveryMethod")
            listOfItemsToCheck = [
                "isTaxable",
                "requireShipping",
                "spotlight",
                "ageVerificationRequired",
                "isAvailableToSell",
            ]
            assert not all(
                driver.find_element(
                    By.XPATH,
                    "(//span[contains(text()," f"{i}" ")]//parent::div//span)[3]",
                ).text
                for i in listOfItemsToCheck
            )
            try:
                product_id = "ID " + str(product_id)
                RT_VALUE = utils.Delete(
                    product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )

                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
            # Creating the variant through API, updating through UI and then checking the SNS for all FALSE
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "PriceTaxation": {
                        "price": [{"value": 0, "currency": "USD"}],
                        # "specialPrice": [{"value": 0, "currency": "USD"}],
                        # "cost": [{"value": 0, "currency": "USD"}],
                        "isTaxable": True,
                        "orderMaxQuantityAllowed": 2,
                        "orderMinQuantityAllowed": 1,
                    },
                    "productSet": "Simple",
                    "productType": "Duty Free",
                    "requireShipping": True,
                    "spotLight": True,
                    "ageVerificationRequire": True,
                    "isAvailableToSell": True,
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API response code - {response.status_code}")
            product_id = data["id"]
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, product_id)
            scroll = driver.find_element(By.NAME, xpath.spotLightName)
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("Scrolled")
            assert driver.find_element(By.NAME, xpath.spotLightName).get_attribute(
                "checked"
            )
            driver.find_element(By.NAME, xpath.spotLightName).click()
            time.sleep(1)
            logging.info(f"Unchecked for {xpath.spotLightName}")
            assert not driver.find_element(
                By.NAME, xpath.isPerishableName
            ).get_attribute("checked")
            logging.info("isPerishable is unchecked")
            assert driver.find_element(
                By.NAME, xpath.ageVerificationRequiredName
            ).get_attribute("checked")
            driver.find_element(By.NAME, xpath.ageVerificationRequiredName).click()
            time.sleep(1)
            logging.info(f"Unchecked for {xpath.ageVerificationRequiredName}")
            assert driver.find_element(
                By.NAME, xpath.isAvailableToSellName
            ).get_attribute("checked")
            driver.find_element(By.NAME, xpath.isAvailableToSellName).click()
            time.sleep(1)
            logging.info(f"Unchecked for {xpath.isAvailableToSellName}")
            assert driver.find_element(
                By.NAME, xpath.requireShippingName
            ).get_attribute("checked")
            driver.find_element(By.NAME, xpath.requireShippingName).click()
            time.sleep(1)
            logging.info(f"Unchecked for {xpath.requireShippingName}")
            driver.find_element(By.XPATH, xpath.priceAndTaxationPageXpath).click()
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[text()='Price Details']")
                )
            )
            assert driver.find_element(By.NAME, xpath.isTaxableName).get_attribute(
                "checked"
            )
            driver.find_element(By.NAME, xpath.isTaxableName).click()
            time.sleep(1)
            logging.info(f"Unchecked for {xpath.isTaxableName}")
            utils.save_and_publish(driver)
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(text(),'product_publish')]")
                )
            )
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'product_publish')])[{num - 1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{product_id}')])[1]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"(//span[contains(text(),'{product_id}')])[1]"
            ).is_displayed()
            logging.info(f"Asserted the product name as {product_id}")
            assert not len(
                driver.find_elements(
                    By.XPATH, f"//span[contains(text(),'categorySpecificAttributes')]"
                )
            )
            logging.info("categorySpecificAttributes is not displayed")
            assert not len(
                driver.find_elements(
                    By.XPATH, f"//span[contains(text(),'specialPrice')]"
                )
            )
            logging.info("specialPrice is not displayed")
            inflightCurrent = driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'deliveryMethod')]//parent::div//following-sibling::div)[3]//span[contains(text(),'inflightCurrent')]",
            ).is_displayed()
            assert inflightCurrent
            logging.info("inflightCurrent is shown in deliveryMethod")
            listOfItemsToCheck = [
                "isTaxable",
                "requireShipping",
                "spotlight",
                "ageVerificationRequired",
                "isAvailableToSell",
            ]
            assert not all(
                driver.find_element(
                    By.XPATH,
                    "(//span[contains(text()," f"{i}" ")]//parent::div//span)[3]",
                ).text
                for i in listOfItemsToCheck
            )
            try:
                product_id = "ID " + str(product_id)
                RT_VALUE = utils.Delete(
                    product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
            # Through CSV
            RANDOM_NAME_CSV = "".join(random.choices(string.ascii_letters, k=7))
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME_CSV,
                    "name": RANDOM_NAME_CSV,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "PriceTaxation": {
                        "price": [{"value": 0, "currency": "USD"}],
                        "specialPrice": [{"value": 0, "currency": "USD"}],
                        # "cost": [{"value": 0, "currency": "USD"}],
                        "isTaxable": True,
                        "orderMaxQuantityAllowed": 2,
                        "orderMinQuantityAllowed": 1,
                    },
                    "productSet": "Simple",
                    "productType": "Duty Free",
                    "requireShipping": True,
                    "spotLight": True,
                    "ageVerificationRequire": True,
                    "isAvailableToSell": True,
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API response code - {response.status_code}")
            product_id = data["id"]
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait.until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            ).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            time.sleep(2)
            store = driver.find_element(By.NAME, xpath.importTypeName)
            store.send_keys("Products")
            store.send_keys(Keys.ENTER)
            time.sleep(3)
            # wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[contains(@id,"categoryField-trigger-picker")]')
                )
            )
            driver.find_element(
                By.XPATH, '//div[contains(@id,"categoryField-input")]//input'
            ).send_keys(DEDICATED_CATEGORY_1_NAME)
            time.sleep(2)
            driver.find_element(
                By.XPATH, '//div[contains(@id,"categoryField-input")]//input'
            ).send_keys(Keys.DOWN)
            li = driver.find_element(
                By.XPATH, f"//li[contains(text(),'{DEDICATED_CATEGORY_1_NAME}')]"
            )
            li.click()
            time.sleep(2)
            file = "Product_Sample.csv"
            utils.update_csv(file, "name_en", RANDOM_NAME_CSV)
            utils.update_csv(file, "sku", RANDOM_NAME_CSV)
            utils.update_csv(file, "spotLight", 0)
            utils.update_csv(file, "ageVerificationRequire", 0)
            utils.update_csv(file, "isAvailableToSell", 0)
            utils.update_csv(file, "requireShipping", 0)
            utils.update_csv(file, "isTaxable", 0)
            utils.update_csv(file, "specialPrice", "")
            # ele = driver.find_element(By.XPATH, xpath.csvFileSelectButton)
            utils.upload_csv_with_specific_validation(
                file, xpath.csvFileSelectButton, "CSV Uploaded Successfully"
            )
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(text(),'product_publish')]")
                )
            )
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'product_publish')])[{num - 1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{product_id}')])[1]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"(//span[contains(text(),'{product_id}')])[1]"
            ).is_displayed()
            logging.info(f"Asserted the product name as {product_id}")
            assert not len(
                driver.find_elements(
                    By.XPATH, f"//span[contains(text(),'categorySpecificAttributes')]"
                )
            )
            logging.info("categorySpecificAttributes is not displayed")
            assert driver.find_element(By.XPATH, '((//span[contains(text(),"specialPrice")]//parent::div//following-sibling::div)[2]//span)[5]').text == '0'
            logging.info("specialPrice has value zero")
            inflightCurrent = driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'deliveryMethod')]//parent::div//following-sibling::div)[3]//span[contains(text(),'inflightCurrent')]",
            ).is_displayed()
            assert inflightCurrent
            logging.info("inflightCurrent is shown in deliveryMethod")
            listOfItemsToCheck = [
                "isTaxable",
                "requireShipping",
                "spotlight",
                "ageVerificationRequired",
                "isAvailableToSell",
            ]
            assert not all(
                driver.find_element(
                    By.XPATH,
                    "(//span[contains(text()," f"{i}" ")]//parent::div//span)[3]",
                ).text
                for i in listOfItemsToCheck
            )
            try:
                product_id = "ID " + str(product_id)
                RT_VALUE = utils.Delete(
                    product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_2082_SNS_001_MKTPL_2713_SNS_001_MKTPL_2925_SNS_002_MKTPL_1105_Perishableproduct_003_MKTPL_2723_SNS_001",
                "Verify if categorySpecificAtributes does not have data ",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2082_SNS_001_MKTPL_2713_SNS_001_MKTPL_2925_SNS_002_MKTPL_1105_Perishableproduct_003_MKTPL_2723_SNS_001",
            "Verify if categorySpecificAtributes does not have data ",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2082_SNS_001_MKTPL_2713_SNS_001_MKTPL_2925_SNS_002_MKTPL_1105_Perishableproduct_003_MKTPL_2723_SNS_001",
            "Verify if categorySpecificAtributes does not have data ",
            e,
        )
        raise e

def test_MKTPL_2016_SNS_002():
    try:
        RANDOM_NAME = "".join(random.choices(string.ascii_letters, k=7))
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper("test_MKTPL_2016_SNS_001.png") as driver:
            # create Parent product
            parentProductID = utils.create_Parent_Product(
                "Configurable", "Next Flight", "Duty Free"
            )
            logging.info(f"Parent Product ID- {parentProductID}")
            assert parentProductID is not None
            VARIENT_ID = utils.create_Variant_Product(
                2, int(parentProductID), "Configurable", "Next Flight", "Duty Free"
            )
            logging.info(f"varient Product ID- {VARIENT_ID}")
            assert VARIENT_ID is not None
            # DELETE the second variant product
            secondVariantID = VARIENT_ID[1]
            url_parent_product = PRODUCTS_URL + str(secondVariantID)
            payload = ""
            headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}
            response = requests.delete(
                url_parent_product, headers=headers, data=payload
            )
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"A variant deleted successfully.")
            time.sleep(10)
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, parentProductID)
            driver.find_element(By.XPATH, xpath.variantsPageXpath).click()
            assert driver.find_element(
                By.XPATH, "(//table[contains(@id,'patchedgrid')])[1]"
            ).is_displayed()
            count_ele = driver.find_elements(
                By.XPATH, "//span[contains(@class,'checked')]"
            )
            count = len(count_ele)
            logging.info(count)
            logging.info(f"Number of variants: {count}")
            driver.find_element(By.XPATH, "//span[text()='Edit']").click()
            scroll = driver.find_element(By.NAME, xpath.isPerishableName)
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("Scrolled")
            driver.find_element(By.NAME, "barcode").clear()
            driver.find_element(By.NAME, "barcode").send_keys(RANDOM_NAME)
            logging.info(f"Entered barcode as {RANDOM_NAME}")
            utils.save_and_publish(driver)
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(text(),'product_publish')]")
                )
            )
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            parent_sns_num = num - ((count + 1) * 2 - 1)
            # variant_sns_num = parent_sns_num + 2
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'product_publish')])[{parent_sns_num}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'product_publish')])[{parent_sns_num}]",
            )
            parentProductSnsText = row.text
            logging.info(f"Parent Product text- {parentProductSnsText}")
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass
            # UID = UID.split()
            # UID = UID[1]
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{parentProductID}')])[1]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"(//span[contains(text(),'{parentProductID}')])[1]"
            ).is_displayed()
            logging.info(f"Asserted the product id as {parentProductID}")
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(RANDOM_NAME)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{RANDOM_NAME}')]"
            ).is_displayed()
            logging.info(f"Asserted the description as {RANDOM_NAME}")

            for x in range(count):
                parentProductSnsText = parentProductSnsText.split(".")
                varaientName = (
                    parentProductSnsText[0]
                    + "_"
                    + str(x + 1)
                    + "."
                    + parentProductSnsText[1]
                )
                close_button = driver.find_element(
                    By.XPATH, "(//span[@class='x-tab-close-btn'])[last()]"
                ).click()
                wait.until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[contains(text(),"' + varaientName + '")]')
                    )
                )
                # Generate list and get the last row number with store publish
                list1 = driver.find_elements(
                    By.XPATH, '//div[contains(text(),"' + varaientName + '")]'
                )
                # num = len(list1)
                wait.until(
                    EC.presence_of_element_located(
                        (By.XPATH, f'(//div[contains(text(),"' + varaientName + '")])')
                    )
                )
                # Click on the last created entry
                row = driver.find_element(
                    By.XPATH, f'(//div[contains(text(),"' + varaientName + '")])'
                )
                actions = ActionChains(driver)
                actions.double_click(row).perform()
                logging.info(("Opened the entry"))
                try:
                    driver.find_element("xpath", '//span[text()="Yes"]').click()
                    logging.info("Click on message")
                except:
                    pass
                var = VARIENT_ID[x]
                wait.until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//span[contains(text(),"' + str(var) + '")])')
                    )
                )
                time.sleep(5)
                assert driver.find_element(
                    By.XPATH, '(//span[contains(text(),"' + str(var) + '")])'
                ).is_displayed()
                logging.info(f"Asserted the variant is as {var}")
            unpublishVaraientName = (
                parentProductSnsText[0]
                + "_"
                + str(count + 1)
                + "."
                + parentProductSnsText[1]
            )
            logging.info(unpublishVaraientName)
            try:
                driver.find_element(
                    By.XPATH, "(//span[@class='x-tab-close-btn'])[last()]"
                ).click()
                driver.find_element(
                    By.XPATH,
                    f'(//div[contains(text(),"' + unpublishVaraientName + '")])',
                )
                logging.info("Unpublished variant shown")
                assert False
            except:
                logging.info("Unpublished SNS not shown")
            try:
                url_parent_product = PRODUCTS_URL + str(parentProductID)
                payload = ""
                headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}
                response = requests.delete(
                    url_parent_product, headers=headers, data=payload
                )
                assert response.status_code == 200 or response.status_code == 201
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

            Success_List_Append(
                "test_MKTPL_2016_SNS_002",
                "Verify when variant has inherited data from parent product and data  is changed in parent product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2016_SNS_002",
            "Verify when variant has inherited data from parent product and data  is changed in parent product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2016_SNS_002",
            "Verify when variant has inherited data from parent product and data  is changed in parent product",
            e,
        )
        raise e

def test_MKTPL_5835_ProductAPI_001():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper(
            "test_MKTPL_5835_ProductAPI_001.png"
        ) as driver:
            # MKTPL - 5835 - ProductAPI - 001
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "productType": "Duty Free",
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"POST API for Product responded with 200 status code")
            resp_data = response.json()
            logging.info(resp_data)
            assert "Product added successfully. " in resp_data["message"]
            product_id = resp_data["id"]
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400
            logging.info(f"POST API for Product responded with 200 status code")
            resp_data = response.json()
            logging.info(resp_data)
            assert (
                "A product already exists with same sku and store."
                in resp_data["message"]
            )
            try:
                product_id = "ID " + str(product_id)
                RT_VALUE = utils.Delete(
                    product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_5835_ProductAPI_001",
                "Verify when users try to create the new product with same SKU in the store via Post API",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5835_ProductAPI_001",
            "Verify when users try to create the new product with same SKU in the store via Post API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5835_ProductAPI_001",
            "Verify when users try to create the new product with same SKU in the store via Post API",
            e,
        )
        raise e

def test_MKTPL_5836_ProductAPI_001_002_003_004_005():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        RANDOM_NAME_1 = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        logging.info(RANDOM_NAME_1)
        with utils.services_context_wrapper(
            "test_MKTPL_5836_ProductAPI_001_002_003_004_005.png"
        ) as driver:
            product_id = utils.create_Simple_Product(
                "Onboard", "Duty Free", RANDOM_NAME
            )
            product_id_1 = utils.create_Simple_Product(
                "Onboard", "Duty Free", RANDOM_NAME_1
            )
            assert product_id or product_id_1 != None

            # MKTPL - 5836 - ProductAPI - 002

            PRODUCTS_URL_1 = (
                PRODUCTS_URL
                + f"?offset=1&unpublished=false&skus={RANDOM_NAME}, {RANDOM_NAME_1}"
            )
            response = requests.request(
                "GET",
                PRODUCTS_URL_1,
                headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                data={},
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"GET API for Product responded with 200 status code")
            assert resp_data["data"]["products"][0]["id"] == product_id
            logging.info(f"Asserted {product_id}")
            assert resp_data["data"]["products"][1]["id"] == product_id_1
            logging.info(f"Asserted {product_id_1}")
            assert 2 == resp_data["data"]["totalRecords"]
            # MKTPL - 5836 - ProductAPI - 001
            PRODUCTS_URL_1 = (
                PRODUCTS_URL + f"?offset=1&unpublished=false&skus={RANDOM_NAME}"
            )
            response = requests.request(
                "GET",
                PRODUCTS_URL_1,
                headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                data={},
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"GET API for Product responded with 200 status code")
            assert resp_data["data"]["products"][0]["id"] == product_id
            logging.info(f"Asserted {product_id}")
            assert 1 == resp_data["data"]["totalRecords"]
            # MKTPL-5836-ProductAPI-003
            PRODUCTS_URL_1 = (
                PRODUCTS_URL + f"?offset=1&unpublished=false&skus=aasdasdadrkshfksjdf"
            )
            response = requests.request(
                "GET",
                PRODUCTS_URL_1,
                headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                data={},
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"GET API for Product responded with 200 status code")
            assert "No Result Found" or "No Record Found" in resp_data["message"]
            logging.info(f"Asserted {product_id}")
            # MKTPL-5836-ProductAPI-004
            PRODUCTS_URL_1 = (
                PRODUCTS_URL
                + f"?offset=1&unpublished=false&skus=aasdasdadrkshfksjdf,aurwekjhrwekjhrw"
            )
            response = requests.request(
                "GET",
                PRODUCTS_URL_1,
                headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                data={},
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"GET API for Product responded with 200 status code")
            assert "No Result Found" or "No Record Found" in resp_data["message"]
            logging.info(f"Asserted {product_id}")
            # MKTPL - 5836 - ProductAPI - 005
            PRODUCTS_URL_1 = (
                PRODUCTS_URL
                + f"?offset=1&unpublished=false&skus={RANDOM_NAME},ehskdfhskdjhfsfks"
            )
            response = requests.request(
                "GET",
                PRODUCTS_URL_1,
                headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                data={},
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(f"GET API for Product responded with 200 status code")
            assert resp_data["data"]["products"][0]["id"] == product_id
            logging.info(f"Asserted {product_id}")
            assert 1 == resp_data["data"]["totalRecords"]
            try:
                product_id = "ID " + str(product_id)
                RT_VALUE = utils.Delete(
                    product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")

            try:
                product_id_1 = "ID " + str(product_id_1)
                RT_VALUE = utils.Delete(
                    product_id_1, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(e)
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_5836_ProductAPI_001_002_003_004_005",
                'Verify "skus" in Params of GET ALL Products API',
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5836_ProductAPI_001_002_003_004_005",
            'Verify "skus" in Params of GET ALL Products API',
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5836_ProductAPI_001_002_003_004_005",
            'Verify "skus" in Params of GET ALL Products API',
            e,
        )
        raise e

def test_MKTPL_967_ProductAPI_003_004_005_006_007():
    # Choose based on test case
    test_case_list = []
    # _003
    test_case_list.append(
        {
            "search_store": DEDICATED_STORE_ID,
            "input_tag_value": "D2C",
            "api_payload_category": int(DEDICATED_CATEGORY_1_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_TOKEN,
            "validation_message": "Product added successfully",
            "test_case_name": "test_MKTPL_967_ProductAPI_003",
        }
    )
    # _004
    test_case_list.append(
        {
            "search_store": DEDICATED_STORE_ID,
            "input_tag_value": "D2C",
            "api_payload_category": int(DEDICATED_CATEGORY_5_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_TOKEN,
            "validation_message": "Invalid Category",
            "test_case_name": "test_MKTPL_967_ProductAPI_004",
        }
    )
    # _005
    test_case_list.append(
        {
            "search_store": DEDICATED_STORE_ID,
            "input_tag_value": "D2C",
            "api_payload_category": int(DEDICATED_CATEGORY_6_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_TOKEN,
            "validation_message": "Invalid Category",
            "test_case_name": "test_MKTPL_967_ProductAPI_005",
        }
    )
    # _006
    test_case_list.append(
        {
            "search_store": DEDICATED_STORE_MARKETPLACE_ID,
            "input_tag_value": "Marketplace",
            "api_payload_category": int(DEDICATED_CATEGORY_5_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN,
            "validation_message": "Product added successfully",
            "test_case_name": "test_MKTPL_967_ProductAPI_006",
        }
    )
    # _007
    test_case_list.append(
        {
            "search_store": DEDICATED_STORE_MARKETPLACE_ID,
            "input_tag_value": "Marketplace",
            "api_payload_category": int(DEDICATED_CATEGORY_1_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN,
            "validation_message": "Invalid Category",
            "test_case_name": "test_MKTPL_967_ProductAPI_007",
        }
    )
    running_test_case = {}
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    logging.info(RANDOM_NAME)
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_967_ProductAPI_003_004_005_006_007.png"
        ) as driver:
            for test_case in test_case_list:
                running_test_case = test_case
                logging.info(f"-------{test_case['test_case_name']} -------")
                driver.maximize_window()
                #
                utils.Pac_Credentials.Login_Pac_Admin(driver)
                driver.implicitly_wait(10)
                act_title = driver.find_element(
                    By.XPATH, xpath.logout
                ).get_attribute("id")
                assert act_title == "pimcore_logout"
                logging.info("Login is successful.")
                utils.wait_for_style_attribute(driver, 40)
                wait = WebDriverWait(driver, 60)

                # search for store
                utils.search_by_id(driver, test_case["search_store"])
                time.sleep(2)
                # Click on Configuration
                configuration = driver.find_element(
                    By.XPATH, "(//span[contains(text(),'Configuration')])[1]"
                )
                configuration.click()
                # Verify sales strategy
                # driver.find_element(By.XPATH, "//input[@name='manageStock']").send_keys("Yes", Keys.ENTER)
                # driver.find_element(By.XPATH, "//input[@name='salesStrategy']")
                if driver.find_element(
                    By.XPATH,
                    f"//input[@name='salesStrategy' and @value='{test_case['input_tag_value']}']",
                ):
                    logging.info("Sales strategy is D2C verified")
                    time.sleep(1)
                    payload = json.dumps(
                        {
                            "sku": RANDOM_NAME,
                            "name": RANDOM_NAME,
                            "shortDescription": "<ol>\n\t<li>this is</li>\n</ol>\n\n<p>shortDescription</p>\n\n<p>ofproduct</p>\n",
                            "description": "<ul>\n\t<li>this is</li>\n</ul>\n\n<p>Description<br />\nofproduct</p>\n",
                            "barcode": "TCGDA0011",
                            "deliveryMethod": [
                                "Next Flight",
                                "Gate Pick Up",
                                "Onboard",
                            ],
                            "nextFlightLeadTime": 1,
                            "gatePickupLeadTime": 889,
                            "category": [test_case["api_payload_category"]],
                            "shippingAttributes": {
                                "weight": 0,
                                "weightUnit": "",
                                "height": 10,
                                "heightUnit": "IN",
                                "width": 10,
                                "widthUnit": "IN",
                                "length": 10,
                                "lengthUnit": "IN",
                            },
                            "PriceTaxation": {
                                "price": [{"value": 11, "currency": "USD"}],
                                "specialPrice": [{"value": 190, "currency": "USD"}],
                                # "cost": [{"value": 910, "currency": "USD"}],
                                "isTaxable": True,
                                "orderMaxQuantityAllowed": 300,
                                "orderMinQuantityAllowed": 10,
                            },
                            "brand": "Spark1",
                            "ageVerificationRequire": True,
                            "isAvailableToSell": False,
                            "Attributes": {
                                "storeSpecificAttributes": [{"key": "", "value": ""}],
                                "productCategoryAttributes": [
                                    {"key": "Display", "value": "20"}
                                ],
                            },
                            "notApplicableCountries": ["IN", "US"],
                            "productSet": "Simple",
                            "storeProductId": "134A34",
                            "productType": "Duty Free",
                            "spotLight": True,
                            "isPerishable": False,
                            "requireShipping": True,
                        }
                    )
                    headers = {
                        "language": "en",
                        "Authorization": test_case["api_authorization_token"],
                        "Content-Type": "application/json",
                    }
                    response = requests.request(
                        "POST", PRODUCTS_URL, headers=headers, data=payload
                    )
                    data = response.json()
                    logging.info(data)
                    if test_case["test_case_name"] in [
                        "test_MKTPL_967_ProductAPI_003",
                        "test_MKTPL_967_ProductAPI_006",
                    ]:
                        assert (
                            response.status_code == 200 or response.status_code == 201
                        )
                        logging.info(
                            "The product is created in the pimcore with all the details provided in the API and it should be "
                            "publish in the pimcore"
                        )
                    elif test_case["test_case_name"] in [
                        "test_MKTPL_967_ProductAPI_004",
                        "test_MKTPL_967_ProductAPI_005",
                        "test_MKTPL_967_ProductAPI_007",
                    ]:
                        assert response.status_code == 400
                        logging.info("Error code detected")

                    if data["message"] == test_case["validation_message"]:
                        logging.info("test case passed")
                # Logout
                driver.find_element(By.XPATH, xpath.logout).click()
                logging.info("Logged out")
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.NAME, "username"))
                )
            Success_List_Append(
                "test_MKTPL_967_ProductAPI_003_004_005_006_007",
                "Verified sales strategy and excuted API",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_967_ProductAPI_003_004_005_006_007",
            "Verified sales strategy and excuted API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_967_ProductAPI_003_004_005_006_007",
            "Verified sales strategy and excuted API",
            e,
        )
        raise e

def test_MKTPL_967_ProductAPI_010_011_012_013_014():
    # Choose based on test case
    test_case_list = []
    # _003
    test_case_list.append(
        {
            "search_store": int(DEDICATED_STORE_ID),
            "input_tag_value": "D2C",
            "api_payload_category": int(DEDICATED_CATEGORY_1_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_TOKEN,
            "validation_message": "Product updated successfully. ",
            "test_case_name": "test_MKTPL_967_ProductAPI_010",
        }
    )
    # _004
    test_case_list.append(
        {
            "search_store": int(DEDICATED_STORE_ID),
            "input_tag_value": "D2C",
            "api_payload_category": int(DEDICATED_CATEGORY_5_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_TOKEN,
            "validation_message": "Invalid Category",
            "test_case_name": "test_MKTPL_967_ProductAPI_011",
        }
    )
    # _005
    test_case_list.append(
        {
            "search_store": int(DEDICATED_STORE_ID),
            "input_tag_value": "D2C",
            "api_payload_category": int(DEDICATED_CATEGORY_6_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_TOKEN,
            "validation_message": "Invalid Category",
            "test_case_name": "test_MKTPL_967_ProductAPI_012",
        }
    )
    # _006
    test_case_list.append(
        {
            "search_store": int(DEDICATED_STORE_MARKETPLACE_ID),
            "input_tag_value": "Marketplace",
            "api_payload_category": int(DEDICATED_CATEGORY_5_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN,
            "validation_message": "Product updated successfully. ",
            "test_case_name": "test_MKTPL_967_ProductAPI_013",
        }
    )
    # _007
    test_case_list.append(
        {
            "search_store": int(DEDICATED_STORE_MARKETPLACE_ID),
            "input_tag_value": "Marketplace",
            "api_payload_category": int(DEDICATED_CATEGORY_1_ID),
            "api_authorization_token": CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN,
            "validation_message": "Invalid Category",
            "test_case_name": "test_MKTPL_967_ProductAPI_014",
        }
    )
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    logging.info(RANDOM_NAME)
    body = {
        "sku": RANDOM_NAME,
        "name": RANDOM_NAME,
        "shortDescription": "<ol>\n\t<li>this is</li>\n</ol>\n\n<p>shortDescription</p>\n\n<p>ofproduct</p>\n",
        "description": "<ul>\n\t<li>this is</li>\n</ul>\n\n<p>Description<br />\nofproduct</p>\n",
        "barcode": "TCGDA0011",
        "deliveryMethod": ["Next Flight", "Gate Pick Up", "Onboard"],
        "nextFlightLeadTime": 1,
        "gatePickupLeadTime": 889,
        "category": [],
        "shippingAttributes": {
            "weight": 0,
            "weightUnit": "",
            "height": 10,
            "heightUnit": "IN",
            "width": 10,
            "widthUnit": "IN",
            "length": 10,
            "lengthUnit": "IN",
        },
        "PriceTaxation": {
            "price": [{"value": 1, "currency": "USD"}],
            "specialPrice": [{"value": 2, "currency": "USD"}],
            # "cost": [{"value": 3, "currency": "USD"}],
            "isTaxable": True,
            "orderMaxQuantityAllowed": 300,
            "orderMinQuantityAllowed": 10,
        },
        "brand": "Spark1",
        "ageVerificationRequire": True,
        "isAvailableToSell": False,
        "Attributes": {
            "storeSpecificAttributes": [{"key": "", "value": ""}],
            "productCategoryAttributes": [{"key": "Display", "value": "20"}],
        },
        "notApplicableCountries": ["IN", "US"],
        "productSet": "Simple",
        "storeProductId": "134A34",
        "productType": "Duty Free",
        "spotLight": True,
        "isPerishable": False,
        "requireShipping": True,
    }
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_967_ProductAPI_010_011_012_013_014.png"
        ) as driver:
            for test_case in test_case_list:
                logging.info(f"-------{test_case['test_case_name']} -------")
                driver.maximize_window()
                #
                utils.Pac_Credentials.Login_Pac_Admin(driver)
                driver.implicitly_wait(10)
                act_title = driver.find_element(
                    By.XPATH, xpath.logout
                ).get_attribute("id")
                assert act_title == "pimcore_logout"
                logging.info("Login is successful.")
                utils.wait_for_style_attribute(driver, 40)
                wait = WebDriverWait(driver, 60)

                # search for store
                utils.search_by_id(driver, test_case["search_store"])
                time.sleep(2)
                # Click on Configuration
                configuration = driver.find_element(
                    By.XPATH, "(//span[contains(text(),'Configuration')])[1]"
                )
                configuration.click()
                # Verify sales strategy
                # driver.find_element(By.XPATH, "//input[@name='manageStock']").send_keys("Yes", Keys.ENTER)
                # driver.find_element(By.XPATH, "//input[@name='salesStrategy']")
                if driver.find_element(
                    By.XPATH,
                    f"//input[@name='salesStrategy' and @value='{test_case['input_tag_value']}']",
                ):
                    logging.info("Sales strategy is D2C verified")
                    time.sleep(1)
                    driver.find_element(By.XPATH, xpath.logout).click()
                    logging.info("Logged out from Store Admin")
                    wait.until(EC.visibility_of_element_located((By.NAME, "username")))
                    product_id = ""
                    if test_case["input_tag_value"] == "D2C" and product_id == "":
                        New_random_name = "From_Automation_" + "".join(
                            random.choices(string.ascii_letters, k=7)
                        )
                        body["sku"] = New_random_name
                        body["name"] = New_random_name
                        body["category"] = [int(DEDICATED_CATEGORY_1_ID)]
                        payload = json.dumps(body)
                        headers = {
                            "language": "en",
                            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                            "Content-Type": "application/json",
                        }
                        post_response = requests.request(
                            "POST", PRODUCTS_URL, headers=headers, data=payload
                        )
                        post_data = post_response.json()
                        logging.info(post_data)
                        product_id = post_data["id"]
                    elif (
                        test_case["input_tag_value"] == "Marketplace"
                        and product_id == ""
                    ):
                        New_random_name = "From_Automation_" + "".join(
                            random.choices(string.ascii_letters, k=7)
                        )
                        body["sku"] = New_random_name
                        body["name"] = New_random_name
                        body["category"] = [int(DEDICATED_CATEGORY_5_ID)]
                        payload = json.dumps(body)
                        headers = {
                            "language": "en",
                            "Authorization": CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN,
                            "Content-Type": "application/json",
                        }
                        post_response = requests.request(
                            "POST", PRODUCTS_URL, headers=headers, data=payload
                        )
                        post_data = post_response.json()
                        logging.info(post_data)
                        product_id = post_data["id"]
                    Updated_random_name = "From_Automation_" + "".join(
                        random.choices(string.ascii_letters, k=7)
                    )
                    logging.info(Updated_random_name)
                    body["name"] = Updated_random_name
                    body["category"] = [test_case["api_payload_category"]]
                    payload = json.dumps(body)
                    headers = {
                        "language": "en",
                        "Authorization": test_case["api_authorization_token"],
                        "Content-Type": "application/json",
                    }
                    response = requests.request(
                        "PUT",
                        PRODUCTS_URL + str(product_id),
                        headers=headers,
                        data=payload,
                    )
                    data = response.json()
                    logging.info(data)

                    if test_case["test_case_name"] in [
                        "test_MKTPL_967_ProductAPI_010",
                        "test_MKTPL_967_ProductAPI_013",
                    ]:
                        assert (
                            response.status_code == 200 or response.status_code == 201
                        )
                        logging.info(
                            "The product is created in the pimcore with all the details provided in the API and it should be "
                            "publish in the pimcore"
                        )
                    elif test_case["test_case_name"] in [
                        "test_MKTPL_967_ProductAPI_011",
                        "test_MKTPL_967_ProductAPI_012",
                        "test_MKTPL_967_ProductAPI_014",
                    ]:
                        assert response.status_code == 400
                        logging.info("Error code detected")

                    if data["message"] == test_case["validation_message"]:
                        logging.info("test case passed")
                    if test_case["input_tag_value"] == "D2C":
                        headers = {
                            "language": "en",
                            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                            "Content-Type": "application/json",
                        }
                        delete_response = requests.request(
                            "DELETE", PRODUCTS_URL + str(product_id), headers=headers
                        )
                        delete_data = delete_response.json()
                        logging.info(delete_data)
                    elif test_case["input_tag_value"] == "Marketplace":
                        headers = {
                            "language": "en",
                            "Authorization": CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN,
                            "Content-Type": "application/json",
                        }
                        delete_response = requests.request(
                            "DELETE", PRODUCTS_URL + str(product_id), headers=headers
                        )
                        delete_data = delete_response.json()
                        logging.info(delete_data)
            Success_List_Append(
                "test_MKTPL_967_ProductAPI_010_011_012_013_014",
                "Verified sales strategy and excuted PUT API",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_967_ProductAPI_010_011_012_013_014",
            "Verified sales strategy and excuted PUT API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_967_ProductAPI_010_011_012_013_014",
            "Verified sales strategy and excuted PUT API",
            e,
        )
        raise e

def test_MKTPL_1105_Perishableproduct_005():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1105_Perishableproduct_005.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            utils.search_by_id(driver, DEDICATED_PRODUCT_10_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productNameFieldXpath)
                )
            )
            item_list = driver.find_elements(By.XPATH, '//li[@class="x-tagfield-item"]')
            for item in item_list:
                if item.find_element(By.XPATH, '//div[contains(text(),"Next Flight")]'):
                    logging.info("Verified Next Flight")
                    break
            scroll = driver.find_element(By.NAME, xpath.isPerishableName)
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("successful scroll")
            try:
                logging.info(scroll.get_attribute("checked"))
                assert scroll.get_attribute("checked")
                logging.info("Perishable - checked")
            except:
                logging.info(scroll.get_attribute("checked"))
                assert not scroll.get_attribute("checked")
                logging.info("Perishable - Not checked")
                logging.info("3")
                scroll.click()
                logging.info("click successful")
                assert scroll.get_attribute("checked")
                logging.info(scroll.get_attribute("checked"))
            logging.info("Perishable checked")
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            logging.info("Save")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Save successful")
        Success_List_Append(
            "test_MKTPL_1105_Perishableproduct_005",
            "Verify the box will true flag the product as perishable and can be carried over to the next flight",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1105_Perishableproduct_005",
            "Verify the box will true flag the product as perishable and can be carried over to the next flight",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1105_Perishableproduct_005",
            "Verify the box will true flag the product as perishable and can be carried over to the next flight",
            e,
        )
        raise e

def test_MKTPL_1660_VariantAPI_012():
    try:
        RANDOM_NAME_2 = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        logging.info(RANDOM_NAME)
        payload = utils.get_Product_Payload(
            RANDOM_NAME,
            int(DEDICATED_VARIENT_PARENT_PRODUCT_ID),
            "Next Flight",
            int(DEDICATED_CATEGORY_1_ID),
            "Configurable",
            "Duty Free",
            0,
            190,
            910,
            "OS",
            "",
            True,
        )

        # payload = json.dumps({
        #     "sku": RANDOM_NAME_2,
        #     "name": RANDOM_NAME_2,
        #     "parentProduct": DEDICATED_VARIENT_PARENT_PRODUCT_ID,
        #     "shortDescription": "Lorem Ipsum is simply dummy text of the printing",
        #     "description": "Lorem Ipsum is simply dummy text of the printing",
        #     "barcode": "",
        #     "deliveryMethod": [
        #         "Onboard"
        #     ],
        #     "nextFlightLeadTime": "",
        #     "gatePickupLeadTime": "",
        #     "category": [
        #         DEDICATED_CATEGORY_1_ID
        #     ],
        #     "notApplicableCountries": [
        #         "IN"
        #     ],
        #     "shippingAttributes": {},
        #     "assets": {
        #         "images": [
        #             {}
        #         ]
        #     },
        #     "PriceTaxation": {
        #         "price": [
        #             {
        #                 "value": 0,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "specialPrice": [
        #             {
        #                 "value": 0,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "cost": [
        #             {
        #                 "value": 0,
        #                 "currency": "USD"
        #             }
        #         ],
        #         "isTaxable": True,
        #         "orderMaxQuantityAllowed": 2,
        #         "orderMinQuantityAllowed": 1
        #     },
        #     "brand": "spark",
        #     "ageVerificationRequire": False,
        #     "isAvailableToSell": True,
        #     "Attributes": {
        #         "productCategoryAttributes": [
        #             {
        #                 "key": "OS",
        #                 "value": "",
        #                 "isVariantAttribute": True
        #             }
        #         ],
        #         "storeSpecificAttributes": [],
        #         "specialityAttributes": []
        #     },
        #     "productSet": "Configurable",
        #     "storeProductId": "",
        #     "productType": "Duty Free",
        #     "spotLight": True,
        #     "requireShipping": False,
        #     "isPerishable": True
        # })
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        logging.info(f"POST API for Product responded with 400 status code ")
        resp_data = response.json()
        logging.info(resp_data)
        assert (
            "Variant Attributes do not match for Product and Parent Product"
            in resp_data["message"]
        )

        Success_List_Append(
            "test_MKTPL_1660_VariantAPI_012",
            "Verify if user gives 'isVariantAttribute': true in productCategoryAttributes parameter for variant and it does not matches with parent product properties",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1660_VariantAPI_012",
            "Verify if user gives 'isVariantAttribute': true in productCategoryAttributes parameter for variant and it does not matches with parent product properties",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1660_VariantAPI_012",
            "Verify if user gives 'isVariantAttribute': true in productCategoryAttributes parameter for variant and it does not matches with parent product properties",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_001():
    try:
        # empty_payload = json.dumps({})
        # FLIGHT API
        RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
 
        response = requests.request("PATCH", CRED_AUTOMATION_AIRLINE_URL + str(DEDICATED_FLIGHT_1_ID),
                                    headers={'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN}, data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        resp_data = response.json()
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for FLIGHT responded with 400 status code for empty body ")
 
        # SECTOR API
        response = requests.request("PATCH", ROUTE_SECTOR_URL + str(DEDICATED_SECTOR_1_ID),
                                    headers={'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN}, data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        resp_data = response.json()
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for SECTOR responded with 400 status code for empty body ")
 
        # ROUTE_GROUP API
        response = requests.request("PATCH", CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL + str(DEDICATED_ROUTE_GROUP_1_ID),
                                    headers={'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN}, data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        resp_data = response.json()
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for ROUTE_GROUP responded with 400 status code for empty body ")
 
        # ROUTE_CATALOG API
        response = requests.request("PATCH", ROUTE_CATALOG + str(DEDICATED_CATALOG_ASSIGNMENT_3_ID),
                                    headers={'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN}, data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for ROUTE_CATALOG responded with 400 status code for empty body ")
 
        # AIRLINE_CATEGORY_URL API
        response = requests.request("PATCH", AIRLINE_CATEGORY_URL+str(DEDICATED_AIRLINE_CATEGORY_1_ID), headers={'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN}, data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        resp_data = response.json()
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for AIRLINE_CATEGORY_URL responded with 400 status code for empty body ")
 
        # PRODUCT API
        response = requests.request("PATCH", PRODUCTS_URL + str(DEDICATED_PRODUCT_11_ID),
                                    headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN}, data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        resp_data = response.json()
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for PRODUCT responded with 400 status code for empty body ")
        # CATALOG
        response = requests.request("PATCH", CRED_AUTOMATION_STORE_CATALOG_URL + str(DEDICATED_CATALOG_3_ID),
                                    headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN}, data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        resp_data = response.json()
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for CATALOG responded with 400 status code for empty body ")
 
        # STORE_LOCATION
        response = requests.request("PATCH", STORE_LOCATION_URL+"/"+str(DEDICATED_STORE_LOCATION_1_ID), headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN},data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        resp_data = response.json()
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for STORE LOCATION responded with 400 status code for empty body ")
 
        # DELIVERY URL
        payload = json.dumps({
        "deliveryRuleType": "shipping",
        "shipmentGroupingType": "fulfillment_type",
        "shippingDestination": "domestic",
        "deliveryType": [
            "homeShippingDomesticPriority"
        ],
        "title": "express",
        "description": "express",
        "price": 44,
        "priceUnit": "USD",
        "duration": 44,
        "durationType": "days"
        })
        response = requests.request("POST", DELIVERY_URL, headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN},
                                    data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        id = resp_data["id"]
        response = requests.request("PATCH", DELIVERY_URL+"/"+str(id), headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN},
                                    data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        resp_data = response.json()
        assert "Request JSON body is invalid." in resp_data["message"]
        logging.info(f"PATCH API for PRODUCT responded with 400 status code for empty body ")
        response = requests.request("DELETE", DELIVERY_URL + "/" + str(id),
                                    headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN},
                                    data="")
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        assert "Delivery Rule deleted successfully" in resp_data["message"]
        Success_List_Append("test_MKTPL_1655_PatchAPI_001",
                            "Verify if request body is empty",
                            "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1655_PatchAPI_001",
                            "Verify if request body is empty",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_1655_PatchAPI_001",
                             "Verify if request body is empty",
                             e)
        raise e