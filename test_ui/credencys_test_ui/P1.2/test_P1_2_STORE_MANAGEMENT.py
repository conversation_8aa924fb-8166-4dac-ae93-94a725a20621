from random import randint
from urllib.parse import urlparse
from openpyxl import load_workbook
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import credencys_test_ui.credencys_project_configs.utils as utils
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import datetime, logging, os, re, csv, requests, json, string, random, time, os
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

def test_MKTPL_1424_StoreManagers_001_002_003_004_005_006():
    try:
        with utils.services_context_wrapper("test_MKTPL_1424_StoreManagers_001_002_003_004_005_006.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Store_Manager(driver)

            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("PAC Store Manager Login successfully.")
            panels = [
                '//div[text()="Store Profile"]',
                '//div[text()="Inventory Location"]',
                xpath.catalogManagement,
                xpath.catalogAssignment,
                '//div[text()="Promotions"]',
                '//div[text()="Campaigns"]',
                '//div[text()="Delivery Rule"]',
                '//div[text()="Categories"]',
                '//div[text()="Order Management"]'
            ]
            assert all(
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, i))).is_displayed() for
                i in panels)
            logging.info("store managers should be able to see following panels: Store profile, Inventory location, "
                         "Catalog management, Catalog assignment, Promotions, Campaigns, Delivery rule, Categories, "
                         "Order management")
            assert len(driver.find_elements(By.XPATH, xpath.UserManagementTab)) == 0
            logging.info("Store managers not able to see User Management tab")
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,
                                                                               '(//div[text()="Store Profile"])/../../../../..//span[text()="' + DEDICATED_STORE + '"]'))).click())
            read_only = (WebDriverWait(driver, 60).until(EC.visibility_of_element_located(
                (By.XPATH, xpath.readModeLock))).is_displayed())
            logging.info(f"Store profile should be in ready only mode for store managers = {read_only}")

            utils.close_All_Tabs(driver)

            (WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//div[text()="Promotions"])'))).click())
            promotions_class = (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,
                                                                                                  '(//div[text()="Promotions"])/../../../../..//span[text()="Promotions"]'))).is_displayed())

            (WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//div[text()="Campaigns"])'))).click())
            campaigns_class = (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,
                                                                                                 '(//div[text()="Campaigns"])/../../../../..//span[text()="Campaigns"]'))).is_displayed())

            (WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//div[text()="Delivery Rule"])'))).click())
            delivery_rule_class = (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,
                                                                                                     '(//div[text()="Delivery Rule"])/../../../../..//span[text()="Delivery Rule"]'))).is_displayed())

            (WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Categories"]'))).click())
            category_class = (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,
                                                                                                '//div[text()="Categories"]/../../../../..//span[text()="' + DEDICATED_STORE + '"]'))).is_displayed())

            (WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Inventory Location"]'))).click())
            inventory_location = (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,
                                                                                                    '//div[text()="Inventory Location"]/../../../../..//span[text()="Inventory Location"]'))).is_displayed())

            assert [promotions_class, campaigns_class, delivery_rule_class, category_class, inventory_location]
            logging.info("store managers should be able to see following entity's classes: "
                         "Promotion, Campaign, Delivery rule, Category, Catalog, Inventory location")

            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.ID, xpath.pimcoreMenu_id))).click())

            create_new_product = driver.find_element(By.XPATH, xpath.createNewProduct).is_displayed()
            logging.info(
                f"Create new product icon at left panel should be visible to store managers = {create_new_product}")

            driver.find_element(By.XPATH, xpath.csvImport).click()
            (WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[@id="importType-trigger-picker"]'))).click())
            category_options = [
                '//li[text()="Products"]',
                '//li[text()="SpecialityAttributes"]',
                '//li[text()="Category"]',
                '//li[text()="Inventory"]',
                '//li[text()="TrackingInfo"]'
            ]
            assert all(
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath))).is_displayed() for
                xpath in category_options)
            logging.info("store managers should be able to import following entity's CSV: "
                         "Products, TrackingInfo, Category, Catalog, Inventory")

            Success_List_Append(
                "test_MKTPL_1424_StoreManagers_001_002_003_004_005_006",
                "Verify the panels available for store managers",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1424_StoreManagers_001_002_003_004_005_006",
            "Verify the panels available for store managers",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1424_StoreManagers_001_002_003_004_005_006",
            "Verify the panels available for store managers",
            e,
        )
        raise e

def test_MKTPL_1029_Storefieldincategory_007():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1029_Storefieldincategory_007.png"
        ) as driver:
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            search_home = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Home"]//..//a[2]',
                    )
                )
            )
            driver.find_element(By.XPATH, '//span[text()="Home"]//..//a[2]').click()
            driver.find_element(By.XPATH,"(//input[@name='filter'])[last()]").send_keys("Master", Keys.ENTER)
            master_data_expander = WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//span[(text()='Master Data')]//parent::div//div[contains(@class,'expander')]",
                        )
                    )
                )
            driver.execute_script("arguments[0].scrollIntoView();", master_data_expander)

            driver.find_element(
                    By.XPATH,
                    "//span[(text()='Master Data')]//parent::div//div[contains(@class,'expander')]",
                ).click()
            category_expander = WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//span[(text()='Category')]//parent::div//div[contains(@class,'expander')]",
                        )
                    )
                )
            driver.execute_script("arguments[0].scrollIntoView();", category_expander)

            driver.find_element(
                    By.XPATH,
                    "//span[(text()='Category')]//parent::div//div[contains(@class,'expander')]",
                ).click()
            driver.find_element(By.XPATH,'(//span[text()="Category"]//..//a[2])[last()]').click()
            driver.find_element(By.XPATH,"(//input[@name='filter'])[last()]").send_keys(DEDICATED_STORE, Keys.ENTER)
            store = driver.find_element(By.XPATH,"(//span[text()='"+DEDICATED_STORE+"'])[last()]")
            actions = ActionChains(driver)
            actions.context_click(store).perform()
            ADD_OBJECT = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="Category"])[1]')
                )
            )
            # Click on Category
            elm = driver.find_element(
                By.XPATH, '(//span[text()="Category"])[last()]'
            )
            driver.execute_script("arguments[0].click();", elm)
            logging.info(f"Clicked on Category")
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            logging.info(
                f"Clicked on ok after entering Category name as {RANDOM_NAME}"
            )
            wait.until(
                EC.presence_of_element_located((By.NAME, 'name'))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME,"salesStrategy").send_keys("D2C", Keys.ENTER)
            driver.find_element(By.XPATH,"//li[text()='D2C']").click()
            driver.find_element(By.NAME,"store").send_keys(Keys.DOWN, Keys.DOWN)
            time.sleep(2)
            driver.find_element(By.XPATH,"//li[text()='"+DEDICATED_STORE+"']").click()
            parent_category_id = utils.get_uid(driver)
            utils.save_and_publish(driver)
            utils.close_All_Tabs(driver)
            # create sub category
            utils.wait_for_style_attribute(driver, 40)
            search_home = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Home"]//..//a[2]',
                    )
                )
            )
            driver.find_element(By.XPATH, '//span[text()="Home"]//..//a[2]').click()
            driver.find_element(By.XPATH,"(//input[@name='filter'])[last()]").send_keys("Master", Keys.ENTER)
            master_data_expander = WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//span[(text()='Master Data')]//parent::div//div[contains(@class,'expander')]",
                        )
                    )
                )
            driver.execute_script("arguments[0].scrollIntoView();", master_data_expander)

            driver.find_element(
                    By.XPATH,
                    "//span[(text()='Master Data')]//parent::div//div[contains(@class,'expander')]",
                ).click()
            category_expander = WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//span[(text()='Category')]//parent::div//div[contains(@class,'expander')]",
                        )
                    )
                )
            driver.execute_script("arguments[0].scrollIntoView();", category_expander)

            driver.find_element(
                    By.XPATH,
                    "//span[(text()='Category')]//parent::div//div[contains(@class,'expander')]",
                ).click()
            time.sleep(2)
            wait.until(EC.visibility_of_element_located((By.XPATH,'(//span[text()="Category"]//..//a[2])[last()]')))
            time.sleep(2)
            driver.find_element(By.XPATH,'(//span[text()="Category"]//..//a[2])[last()]').click()
            time.sleep(2)
            driver.find_element(By.XPATH,"(//input[@name='filter'])[last()]").send_keys(DEDICATED_STORE, Keys.ENTER)
            time.sleep(4)
            store = driver.find_element(By.XPATH,"(//span[text()='"+DEDICATED_STORE+"'])[last()]")
            time.sleep(2)
            store_expander = WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//span[(text()='"+DEDICATED_STORE+"')]//parent::div//div[contains(@class,'expander')]",
                        )
                    )
                )
            driver.execute_script("arguments[0].scrollIntoView();", store_expander)
            time.sleep(2)

            driver.find_element(
                    By.XPATH,
                    "//span[(text()='"+DEDICATED_STORE+"')]//parent::div//div[contains(@class,'expander')]",
                ).click()
            time.sleep(4)
            driver.find_element(By.XPATH,'(//span[text()="'+DEDICATED_STORE+'"]//..//a[2])[last()]').click()
            time.sleep(4)
            driver.find_element(By.XPATH,"(//input[@name='filter'])[last()]").send_keys(DEDICATED_STORE, Keys.ENTER)
            time.sleep(4)            
            store = driver.find_element(By.XPATH,"(//span[text()='"+RANDOM_NAME+"'])[last()]")
            actions = ActionChains(driver)
            actions.context_click(store).perform()
            ADD_OBJECT = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="Category"])[1]')
                )
            )
            # Click on Category
            elm = driver.find_element(
                By.XPATH, '(//span[text()="Category"])[last()]'
            )
            driver.execute_script("arguments[0].click();", elm)
            logging.info(f"Clicked on Category")
            RANDOM_NAME_SUB = RANDOM_NAME + "_Sub"
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME_SUB)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            logging.info(
                f"Clicked on ok after entering Category name as {RANDOM_NAME_SUB}"
            )
            wait.until(
                EC.presence_of_element_located((By.NAME, 'name'))
            ).send_keys(RANDOM_NAME_SUB)
            driver.find_element(By.NAME,"salesStrategy").send_keys("D2C", Keys.ENTER)
            driver.find_element(By.XPATH,"//li[text()='D2C']").click()
            driver.find_element(By.NAME,"store").send_keys(Keys.DOWN, Keys.DOWN)
            time.sleep(2)
            driver.find_element(By.XPATH,"//li[text()='"+DEDICATED_STORE+"']").click()
            sub_category_id = utils.get_uid(driver)
            utils.save_and_publish(driver)
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, parent_category_id)
            driver.find_element(By.NAME,"store").click()
            driver.find_element(By.NAME,"store").send_keys(Keys.DOWN, Keys.DOWN, Keys.ENTER)
            utils.save_and_publish(driver)
            store = driver.find_element(By.NAME,"store").get_attribute("value")
            logging.info(store)
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, sub_category_id)
            sub_category = driver.find_element(By.NAME,"store").get_attribute("value")
            assert store == sub_category
            logging.info("Asserted the changed store")
            Success_List_Append("test_MKTPL_1029_Storefieldincategory_007","Verify if store is change in parent category","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1029_Storefieldincategory_007","Verify if store is change in parent category","Fail",)
        Failure_Cause_Append("test_MKTPL_1029_Storefieldincategory_007", "Verify if store is change in parent category",e,)
        raise e

def test_MKTPL_1029_Storefieldincategory_008():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1029_Storefieldincategory_008.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # 008. Verify if store/category fields are empty
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.newProductXpath).click()
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            driver.find_element(By.XPATH, xpath.mandatoryFieldErrorMessage).is_displayed()
            logging.info("Validation message should display")
            Success_List_Append("test_MKTPL_1029_Storefieldincategory_008","Verify the the manage shipping setting","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1029_Storefieldincategory_008","Verify the the manage shipping setting","Fail",)
        Failure_Cause_Append("test_MKTPL_1029_Storefieldincategory_008", "Verify the the manage shipping setting",e,)
        raise e

def test_MKTPL_1029_Storefieldincategory_009_010():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1029_Storefieldincategory_009_010.png"
        ) as driver:
            driver.maximize_window()
            role = ["Login_marketplace_store", "Login_Store"]
            for i in role:
                if i is "Login_marketplace_store":
                    utils.Pac_Credentials.Login_marketplace_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    utils.wait_for_style_attribute(driver, 40)
                    logging.info("MARKETPLACE")
                elif i is "Login_Store":
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    utils.wait_for_style_attribute(driver, 40)
                WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
                driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
                driver.find_element(By.XPATH, xpath.newProductXpath).click()
                if i is "Login_Store":
                    (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.NAME, "category"))).click())
                    option1 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.exclusivelyCookedAtHome3Xpath))).is_displayed())
                    option2 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.exclusivelyCookedAtHome7Xpath))).is_displayed())
                    option3 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.nonAlcoholiXpath))).is_displayed())
                    assert [option1, option2, option3]
                    logging.info("Asserted the categories in D2C")
                elif i is "Login_marketplace_store":
                    (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.NAME, "category"))).click())
                    option1 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, "//li[contains(text(),'Marketplace')]"))).is_displayed())
                    # option2 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, "//li[contains(text(),'Skincare (Marketplace/Skincare)')]"))).is_displayed())
                    # option3 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, "//li[contains(text(),'Hair Care (Marketplace/Hair Care)')]"))).is_displayed())
                    assert [option1]
                    logging.info("Asserted the categories in Marketplace")

                driver.find_element(By.XPATH,"//span[contains(text(),'Category')]").click()
                cancel = driver.find_element(By.XPATH, xpath.cancelBtn)
                driver.execute_script("arguments[0].click();", cancel)
                logging.info("Category dropdown show category based on the store sales strategy")
                (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.logout))))
                logout = driver.find_element(By.XPATH, xpath.logout)
                driver.execute_script("arguments[0].click();", logout)
                logging.info("Logged out")
            Success_List_Append(
                    "test_MKTPL_1029_Storefieldincategory_009_010",
                    "Verify the category field",
                    "Pass",
                )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1029_Storefieldincategory_009_010",
            "Verify the category field",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1029_Storefieldincategory_009_010",
            "Verify the category field",
            e,
        )
        raise e

def test_MKTPL_1029_Storefieldincategory_011_012_013():
    CAT_LIST = []
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1029_Storefieldincategory_011_012_013.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # 011. Verify if store/category fields are empty
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            driver.find_element(By.XPATH, xpath.upload).click()
            driver.find_element(By.XPATH, xpath.mandatoryFieldErrorMessage).is_displayed()
            logging.info("Validation message should display")
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            # 012. Verify the category field
            store = [ DEDICATED_STORE_MARKETPLACE, DEDICATED_STORE]
            for i in store:
                time.sleep(3)
                driver.find_element(By.NAME,"storeList").clear()
                driver.find_element(By.NAME,"storeList").send_keys(i)
                (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, "//li[text()='"+i+"']"))).click())
 
                if i is DEDICATED_STORE_MARKETPLACE:
                    time.sleep(3)
                    WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, "categoryField-trigger-picker"))).click()
                    WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "//li[contains(text(),'Marketplace')]")))
                    option1 = driver.find_element(By.XPATH, "//li[contains(text(),'Marketplace')]").is_displayed()
                    # option2 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, "//li[contains(text(),'Skincare (Marketplace/Skincare)')]"))).is_displayed())
                    # option3 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, "//li[contains(text(),'Hair Care (Marketplace/Hair Care)')]"))).is_displayed())
                    # assert [option1, option2, option3]
                    assert [option1]
                    logging.info("Asserted the categories in Marketplace")
                    driver.find_element(By.XPATH,"//span[contains(text(),'Category')]").click()
                    driver.find_element(By.NAME,"storeList").clear()
                elif i is DEDICATED_STORE:
                    time.sleep(3)
                    WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, "categoryField-trigger-picker"))).click()
                    option1 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.exclusivelyCookedAtHome3Xpath))).is_displayed())
                    option2 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.exclusivelyCookedAtHome7Xpath))).is_displayed())
                    option3 = (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.nonAlcoholiXpath))).is_displayed())
                    # assert option1, option2, option3
                    logging.info("Asserted the categories in D2C")
                    (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.exclusivelyCookedAtHome3Xpath))).click())
                    # 013. Verify the "Download Sample File" button
                    buttonEnabled = (WebDriverWait(driver, 60).until(EC.visibility_of_element_located(
                                (By.XPATH, xpath.downloadSampleFileProducts))).is_displayed)
                    assert buttonEnabled
                    logging.info("It is only be enable after selecting store and category")
            Success_List_Append("test_MKTPL_1029_Storefieldincategory_011_012_013","Verify the the manage shipping setting","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1029_Storefieldincategory_011_012_013","Verify the the manage shipping setting","Fail",)
        Failure_Cause_Append("test_MKTPL_1029_Storefieldincategory_011_012_013", "Verify the the manage shipping setting",e,)
        raise e

def test_MKTPL_941_Categoryimport_011_012_014_016_017_018_019_020_021_026_027_028():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_941_Categoryimport_011_012_014_016_017_018_019_020_021_026_027_028.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # 011. Make sure without any fields fill click on Upload button showing message like "Field Required"
            csvImportFun = utils.csv_import(driver, "Category")
            assert csvImportFun
            driver.find_element(By.XPATH, xpath.upload).click()
            driver.find_element(By.XPATH, xpath.mandatoryFieldErrorMessage).is_displayed()
            logging.info("011. Validation message should display")
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            # 012. Make sure that validation message should display if file type is other than .csv format
            path = os.path.join(os.getcwd(), "credencys_test_ui", "invalid_file.ppt")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.implicitly_wait(0)
            driver.find_element(By.XPATH, xpath.upload).click()
            verifyValidation = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="File Import: Uploaded file should be CSV file with .csv extension."]'))).is_displayed())
            assert verifyValidation
            logging.info("012. Validation message should display")
            # 014. When any mandatory column is missing or change name in csv that time showing message like"CSV file
            # not matched with sample file"
            path = os.path.join(os.getcwd(), "credencys_test_ui", "Invalid_Category_Sample.csv")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.implicitly_wait(0)
            upload = driver.find_element(By.XPATH, xpath.upload)
            driver.execute_script("arguments[0].click();", upload)
            verifyValidationMess = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH,'//div[text()="CSV file not matched with downloaded sample file"]'))).is_displayed())
            assert verifyValidationMess
            logging.info("014. Validation message should display")
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            # 017. Make sure user not able to add category while mandatory fields is blank or invalid value
            # 018. Make sure that validation message should display if images having file format other than image file
            # format
            # 016. Make sure that validation message should display when data is not added in mandatory column
            RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
            utils.update_csv("category_fields_invalid.csv", "name_en", "")
            utils.update_csv("category_fields_invalid.csv", "image1_1", "https://placehold.jp/1200x1200")
            
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("category_fields_invalid.csv", file_path)
            assert saved_message
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.search_by_id(driver, CATEGORY_FOLDER)
            driver.find_element(By.NAME,xpath.queryName).send_keys(RANDOM_NAME, Keys.ENTER)
            time.sleep(3)
            assert len(driver.find_elements(By.XPATH,"//div[contains(text(),'"+RANDOM_NAME+"')]")) == 0
            logging.info("Category not added")
            utils.close_All_Tabs(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL,'category', 'Enter valid format for image1_1 Ex PNG, JPG')
            logging.info('Validation message should display')
            assert (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[contains(text(),"Category name is mandatory.")]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[contains(text(),"Parent not found.")]'))).is_displayed())
            logging.info("Validation message should display")
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.userName))))
            # 020. Verify if image is not found in given URL
            # 019. Make sure that validation message should display if given image URL is invalid
            # 027. Only CSV file without zip should get uploaded
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.csv_import_airline(driver, "Category")
            utils.update_csv("category_fields_invalid.csv", "image1_1", "https:///1200x1200.")
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("category_fields_invalid.csv", file_path)
            assert saved_message
            logging.info("CSV file without zip should get uploaded")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver,PAC_STORE_USER_MAIL, 'category', 'Image download failed with HTTP code 0')
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.userName))))
            # 028.If User tries to upload another file except zip it should validate
            # 021. Make sure that category on import csv there should be option to upload zip file
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.csv_import_airline(driver, "Category")
            # 026. Only zip file without csv should not get uploaded
            path = os.getcwd() + "/credencys_test_ui/" + "category_fields_invalid.csv"
            driver.find_element(By.XPATH,
                                xpath.zipFileUploadXpath).send_keys(path)
            driver.find_element(By.XPATH,
                                xpath.upload).click()
            driver.find_element(By.XPATH,
                                xpath.mandatoryFieldErrorMessage).is_displayed()
            logging.info("Zip file without csv not get uploaded")
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            #
            path = os.getcwd() + "/credencys_test_ui/" + "category_fields_invalid.csv"
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            path = os.getcwd() + "/credencys_test_ui/" + "invalid_file.ppt"
            driver.find_element(By.XPATH, xpath.zipFileUploadXpath).send_keys(path)
            logging.info("Options available for the upload zip file in import CSV")
            driver.implicitly_wait(0)
            driver.find_element(By.XPATH, xpath.upload).click()
            validation_mess = (
                WebDriverWait(driver, 60)
                .until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Zip files are allowed only."]')))
                .is_displayed()
            )
            assert validation_mess
            logging.info("Validation message display")
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.okButtonXpath))).click())
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            Success_List_Append("test_MKTPL_941_Categoryimport_011_012_014_016_017_018_019_020_021_026_027_028",
                                "Make sure without any fields fill click on Upload button showing message like "
                                "'Field Required'","Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_941_Categoryimport_011_012_014_016_017_018_019_020_021_026_027_028",
                            "Make sure without any fields fill click on Upload button showing message like "
                            "'Field Required'","Fail",)
        Failure_Cause_Append("test_MKTPL_941_Categoryimport_011_012_014_016_017_018_019_020_021_026_027_028",
                             "Make sure without any fields fill click on Upload button showing message like "
                             "'Field Required'",e,)
        raise e

def test_MKTPL_938_CRUDcategory_001_002_004_005_006():
    try:
        with (utils.services_context_wrapper(
                "test_MKTPL_938_CRUDcategory_001_002_004_005_006.png"
        ) as driver):
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="Categories"]'))).click())
            
            utils.click_on_add_object_Store(driver, "Category")

            RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.popupNameFieldXpath))).send_keys(RANDOM_NAME))
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.okButtonXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath))).click())
            assert driver.find_element(By.XPATH, '//div[text()="Validation failed: Empty mandatory field [ Name ] in English."]').is_displayed()
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="OK"])[2]'))).click())
            
            assert driver.find_element(By.XPATH, xpath.productNameFieldXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.airlineProductCatDisclaimerXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.airlineProductCatShortDesXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.airlineProductCatDescriptionXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.airlineProductCatUrlXpath).is_displayed()
            assert driver.find_element(By.XPATH, '//span[contains(text(),"Published At")]').is_displayed()
            
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.airlineProductCatAssetsTabXpath))).click())
            assert driver.find_element(By.XPATH, '//div[text()=" Image 1:1 (1200x1200)"]').is_displayed()
            assert driver.find_element(By.XPATH, '//b[text()="Banner Image 5X1 (3160 x 632)"]').is_displayed()
            time.sleep(2)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Attributes"]'))).click())
            assert driver.find_element(By.XPATH, '//input[@name="attributeSet"]').is_displayed()
            assert driver.find_element(By.XPATH, '//textarea[@name="attributeKeys"]').is_displayed()            
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_plus "]'))).click())
            assert driver.find_element(By.XPATH, '//input[@name="themeName"]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[contains(text(),"Variant Attribute ")]').is_displayed()
            
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_minus "]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.yes))).click())
            logging.info("Passed test case: 938_001_002")
            
            #create and delete category
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.airlineProductCatBaseDataTabXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.productNameFieldXpath))).send_keys("Testnameis"))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath))).click())
            display_save_msg = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]')))
            assert display_save_msg
            
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_delete pimcore_material_icon "]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.okButtonXpath))).click())
            
            utils.search_by_id(driver, DEDICATED_CATEGORY_1_ID)
            get_name_text = driver.find_element(By.XPATH, xpath.productNameFieldXpath).get_attribute('value')
            logging.info(get_name_text)
            assert len(get_name_text) != 0

            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_delete pimcore_material_icon "]'))).click())
            assert driver.find_element(By.XPATH, '//div[text()="Category Associated With a Product"]').is_displayed()
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="OK"])[2]'))).click())
            logging.info("Passed test case: 938_004_005_006")  
            
            Success_List_Append(
                "test_MKTPL_938_CRUDcategory_001_002_004_005_006",
                "Verify the fields displaying in Store Backend Category/sub category screen",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_938_CRUDcategory_001_002_004_005_006",
            "Verify the fields displaying in Store Backend Category/sub category screen",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_938_CRUDcategory_001_002_004_005_006",
            "Verify the fields displaying in Store Backend Category/sub category screen",
            e,
        )
        raise e

def test_MKTPL_480_CatalogAPI_002_003_005_006_007_009_010_012_014_015_017_018_019_020_021_025_026_027():
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_480_CatalogAPI_002_003_005_006_007_009_010_012_014_015_017_018_019_020_021_025_026_027.png") as driver:
            # 002 Verify if invalid Catalog ID is passed in the URL
            response = requests.request("GET", CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL + "565775",
                                        headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN}, data={})
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "No Result Found." in data["message"]
            logging.info("GET - Verified validation when invalid Catalog ID is passed in the URL")
            # 003 Verify if Catalog ID passed in the URL and token used is of different stores
            response = requests.request("GET", CRED_AUTOMATION_STORE_CATALOG_URL + "41196",
                                        headers={'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN}, data={})
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Unauthorized access" in data["message"]
            logging.info("GET - Verified validation Catalog ID passed in the URL and token used is of different store")
            # 005 006 007
            response = requests.request("GET", CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL,
                                        headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN}, data={})
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            limit = resp_data['data']['limit']
            offset = resp_data['data']['offset']
            records = resp_data['data']['totalRecords']
            assert limit != 0 and offset == 1
            logging.info("The pagination should be implemented in the API")
            logging.info("By default, the limit should be 10 and offset should be 1")
            assert records
            logging.info(
                "GET - API should display total numbers of Catalog records present in the pimcore and it should remain same throughout the pages")
            # 25 26 27
            response = requests.request("GET", CATALOG_BY_STORE_URL,
                                        headers={"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}, data={})
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            limit = resp_data['data']['limit']
            offset = resp_data['data']['offset']
            records = resp_data['data']['totalRecords']
            assert limit == 10 and offset == 1
            logging.info("The pagination should be implemented in the API")
            logging.info("By default, the limit should be 10 and offset should be 1")
            assert records
            logging.info(
                "GET - API should display total numbers of Catalog records present in the pimcore and it should remain same throughout the pages")
            # 009
            response = requests.request("DELETE", CRED_AUTOMATION_STORE_CATALOG_URL + "1221",
                                        headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN},
                                        data={})
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Please provide valid catalog id" in data["message"]
            logging.info("Verified validation for invalid catalog id - DELETE")
            # 010
            response = requests.request("DELETE", CRED_AUTOMATION_STORE_CATALOG_URL + "41196",
                                        headers={'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN},
                                        data={})
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Unauthorized access" in data["message"]
            logging.info("Verified validation for different token - DELETE")
            # POST URL
            # 012
            payload = json.dumps({
                "name": "",
                "products": []
            })
            response = requests.request("POST", CRED_AUTOMATION_STORE_CATALOG_URL,
                                        headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN}, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            # assert "Invalid request: Mandatory field: name | Mandatory field: products" in data["message"]
            # assert "Name is a Required Field." in data["message"]
            assert "Name" in data["message"]
            logging.info("Validation for Mandatory field - POST")
            # 014
            RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=5))
            payload = json.dumps({
                "name": RANDOM_NAME,
                "products": [
                    RANDOM_NAME
                ]
            })
            response = requests.request("POST", CRED_AUTOMATION_STORE_CATALOG_URL,
                                        headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN}, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert f"{RANDOM_NAME} is/are not a valid/published product sku for store." in data["message"]
            logging.info("Verified validation for not a valid/published product - POST")
            # 015
            payload = json.dumps({
                "name": RANDOM_NAME,
                "products": [
                    RANDOM_NAME
                ]
            })
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PATCH", CRED_AUTOMATION_STORE_CATALOG_URL + "45916",
                                        headers=headers,
                                        data=payload)

            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Unauthorized access" in data["message"]
            logging.info("PATCH - Verified validation for unauthorized access")
            # 017
            payload = json.dumps({
                "name": "",
                "products": []
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", CRED_AUTOMATION_STORE_CATALOG_URL,
                                        headers=headers,
                                        data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Invalid request: Mandatory field: name | Mandatory field: products | Invalid type for products. Expected array" or "Name is a Required Field." in \
                   data["message"]
            logging.info("Mandatory field validation - POST")
            # 018
            payload = json.dumps({
                "name": RANDOM_NAME,
                "products": [
                    RANDOM_NAME
                ]
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PATCH", CRED_AUTOMATION_STORE_CATALOG_URL + "2222",
                                        headers=headers,
                                        data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Please provide valid catalog id" in data["message"]
            logging.info("Verified validation for invalid catalog id - PATCH")

            # 019
            payload = json.dumps({
                "name": RANDOM_NAME,
                "products": [
                    RANDOM_NAME
                ]
            })
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", CRED_AUTOMATION_STORE_CATALOG_URL,
                                        headers=headers,
                                        data=payload)

            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Unauthorized access" in data["message"]
            logging.info("Verified validation for unauthorized access- POST")

            # 020
            RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=5))
            payload = json.dumps({
                "name": RANDOM_NAME,
                "products": [
                    RANDOM_NAME
                ]
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", CRED_AUTOMATION_STORE_CATALOG_URL,
                                        headers=headers,
                                        data=payload)

            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert f"{RANDOM_NAME} is/are not a valid/published product sku for store." in data["message"]
            logging.info("Verified validation for invalid published product - POST")

            # 021
            payload = json.dumps({
                "name": RANDOM_NAME,
                "products": [
                    RANDOM_NAME
                ]
            })
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PATCH", CRED_AUTOMATION_STORE_CATALOG_URL + "2222",
                                        headers=headers,
                                        data=payload)

            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Unauthorized access" in data["message"]
            logging.info("Verified validation for unauthorized access - PATCH")

            Success_List_Append(
                "test_MKTPL_480_CatalogAPI_002_003_005_006_007_009_010_012_014_015_017_018_019_020_021_025_026_027",
                "API should display validation message",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_480_CatalogAPI_002_003_005_006_007_009_010_012_014_015_017_018_019_020_021_025_026_027",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_480_CatalogAPI_002_003_005_006_007_009_010_012_014_015_017_018_019_020_021_025_026_027",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_390_388_Productimport_006_007_030_031():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_390_388_Productimport_006_007_030_031.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # 006. Verify by clicking on Upload button and category field and/or CSV file field is empty
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            driver.find_element(By.XPATH, xpath.upload).click()
            driver.find_element(By.XPATH, xpath.mandatoryFieldErrorMessage).is_displayed()
            logging.info("006. Validation message should display")
            time.sleep(5)
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            # 007. Make sure that validation message should display if file type is other than .csv format
            utils.upload_csv_with_specific_validation("invalid_file.ppt", xpath.csvFileSelectButton, "File Import: Uploaded file should be CSV file with .csv extension.")
            # path = os.path.join(os.getcwd(), "credencys_test_ui", "invalid_file.ppt")
            # driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            # driver.implicitly_wait(0)
            # driver.find_element(By.XPATH, xpath.upload).click()
            # verifyValidation = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
            #         (By.XPATH, '//div[text()="File Import: Uploaded file should be CSV file with .csv extension."]'))).is_displayed())
            # assert verifyValidation
            time.sleep(5)
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            logging.info("007. Validation message should display")
            # 030. Only zip file without csv should not get uploaded
            time.sleep(3)
            utils.upload_csv_with_specific_validation("invalid_file.ppt", xpath.zipFileUploadXpath, "Please fill all mandatory fields")

            # path = os.getcwd() + "/credencys_test_ui/" + "invalid_file.ppt"
            # driver.find_element(By.XPATH,
            #                     xpath.zipFileUploadXpath).send_keys(path)
            # driver.find_element(By.XPATH,
            #                     xpath.upload).click()
            # driver.find_element(By.XPATH,
            #                     xpath.mandatoryFieldErrorMessage).is_displayed()
            logging.info("030. Zip file without csv not get uploaded")
            time.sleep(10)
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # 031.If User tries to upload another file except zip it should validate
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(os.getcwd()+"/credencys_test_ui/" + "Product_Sample.csv")
            utils.upload_csv_with_specific_validation("Product_Sample.csv", xpath.zipFileUploadXpath, "Zip files are allowed only.")

            # path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"
            # driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            # driver.implicitly_wait(0)
            # driver.find_element(By.XPATH, xpath.upload).click()
            # validation_mess = (
            #     WebDriverWait(driver, 60)
            #     .until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Zip files are allowed only."]')))
            #     .is_displayed()
            # )
            # assert validation_mess
            logging.info("031. Validation message display")
            time.sleep(5)
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.okButtonXpath))).click())
            Success_List_Append("test_MKTPL_390_388_Productimport_006_007_030_031",
                                "Make sure without any fields fill click on Upload button showing message like "
                                "'Field Required'","Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_390_388_Productimport_006_007_030_031",
                            "Make sure without any fields fill click on Upload button showing message like "
                            "'Field Required'","Fail",)
        Failure_Cause_Append("test_MKTPL_390_388_Productimport_006_007_030_031",
                             "Make sure without any fields fill click on Upload button showing message like "
                             "'Field Required'",e,)
        raise e

def test_MKTPL_390_388_Productimport_008_009_017_018_019_026():
    RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=7))

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_390_388_Productimport_008_009_017_018_019_026.png"
        ) as driver:
            driver.maximize_window()
            # 008. Verify if data is not enter any mandatory column
            # 026. Verify the zip file field
            # 017. Verify if given format is not matched with mm/dd/yyyyhh: mm in new from & new to field
            # 018. Verify if invalid delivery type is entered
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("Product_csv_validation.csv", "name_en", "")
            utils.update_csv("Product_csv_validation.csv", "newFrom", "02-02-2000")
            utils.update_csv("Product_csv_validation.csv", "deliveryMethod", RANDOM_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Product_csv_validation.csv", file_path)
            assert saved_message
            logging.info("Zip file field is non-mandatory to select while uploading the CSV file")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver,PAC_STORE_USER_MAIL, 'products', ''+RANDOM_NAME+' is not a valid deliveryMethod Name')
            assert (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//div[contains(text(),"name_en cannot be blank. It is a mandatory field.")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                    (By.XPATH,'(//div[contains(text(),"Invalid Date format in newFrom. Accepted Format is yyyy-mm-dd")])[1]'))).is_displayed())
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.userName))))
            # 009. Verify if special characters or space are added in sku/barcode
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("Product_csv_validation.csv", "name_en", RANDOM_NAME)
            utils.update_csv("Product_csv_validation.csv", "sku", "%%$$")
            utils.update_csv("Product_csv_validation.csv", "newFrom", "2024-12-01")
            utils.update_csv("Product_csv_validation.csv", "deliveryMethod", "Onboard")
            utils.update_csv("Product_csv_validation.csv", "productType", "Duty Free")            
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Product_csv_validation.csv", file_path)
            assert saved_message
            utils.update_csv("Product_csv_validation.csv", "sku", RANDOM_NAME)
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, 'products','invalid filename')
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.userName))))
            Success_List_Append("test_MKTPL_390_388_Productimpor_008_009_026",
                                "Verify if uploaded file type is other than .csv format","Pass")
            # 018. "Verify if invalid delivery type is entered
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("Product_csv_validation.csv", "productType", RANDOM_NAME)

            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Product_csv_validation.csv", file_path)
            assert saved_message
            utils.update_csv("Product_csv_validation.csv", "productType", RANDOM_NAME)
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, 'products', 'Please enter valid product type.')
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.userName))))
            Success_List_Append("test_MKTPL_390_388_Productimport_008_009_017_018_019_026",
                                "Verify if uploaded file type is other than .csv format", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_390_388_Productimport_008_009_017_018_019_026",
                            "Verify if uploaded file type is other than .csv format"
                            "'Field Required'","Fail",)
        Failure_Cause_Append("test_MKTPL_390_388_Productimport_008_009_017_018_019_026",
                             "Verify if uploaded file type is other than .csv format",e,)
        raise e

def test_MKTPL_336_CRUDProduct_004_007():
    try:
        with (utils.services_context_wrapper(
                "test_MKTPL_336_CRUDProduct_004_007.png"
        ) as driver):
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.newProductXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//div[text()='Add new Object of type Product']/../../../../..//span[text()='OK']"))))
            driver.find_element(By.XPATH, "//div[text()='Add new Object of type Product']/../../../../..//span[text()='OK']").click()
            assert driver.find_element(By.XPATH, xpath.mandatoryFieldErrorMessage).is_displayed()
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//span[text()='OK'])[2]"))).click())
            (WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, xpath.cancelBtn))).click())
            logging.info("passed 336_004")
            
            utils.search_by_id(driver, DEDICATED_PRODUCT_1_ID)
            get_name_text = driver.find_element(By.XPATH, xpath.productNameFieldXpath).get_attribute('value')
            assert len(get_name_text) != 0
            get_categoryname_text = driver.find_element(By.XPATH, '(//div[@class="x-tagfield-item-text"])[1]').text
            assert len(get_categoryname_text) != 0
            get_pacsku_text = driver.find_element(By.XPATH, '//input[@name="pacSKU"]').get_attribute('value')
            assert len(get_pacsku_text) != 0
            get_sku_text = driver.find_element(By.NAME, xpath.productSkuInputFieldName).get_attribute('value')
            assert len(get_sku_text) != 0
            get_deliveryMethod_text = driver.find_element(By.XPATH, '(//div[@class="x-tagfield-item-text"])[2]').text
            assert len(get_deliveryMethod_text) != 0
            get_productType_text = driver.find_element(By.NAME, xpath.productType).get_attribute('value')
            assert len(get_productType_text) != 0
            get_uri_text = driver.find_element(By.XPATH, '//input[@name="uri"]').get_attribute('value')
            assert len(get_uri_text) != 0
            get_productSet_text = driver.find_element(By.XPATH, '//input[@name="productSet"]').get_attribute('value')
            assert len(get_productSet_text) != 0
            logging.info("passed 336_007")
            
            Success_List_Append(
                "test_MKTPL_336_CRUDProduct_004_007",
                "Verify if mandatory fields are empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_336_CRUDProduct_004_007",
            "Verify if mandatory fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_336_CRUDProduct_004_007",
            "Verify if mandatory fields are empty",
            e,
        )
        raise e

def test_MKTPL_338_CRUDUser_002_004():
    try:
        with (utils.services_context_wrapper(
                "test_MKTPL_338_CRUDUser_002_004.png"
        ) as driver):
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.UserManagementTab))).click())
            # utils.click_on_add_object_Store(driver, "Users", "Users")            
            home = driver.find_element(By.XPATH, f"(//span[contains(text(),'Users')])[last()]")
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            ADD_OBJECT = driver.find_element(By.XPATH, xpath.addObjectXpath)
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")
            time.sleep(2)
            WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, f'(//span[text()="Users"])[last()]')))
            elm = driver.find_element(By.XPATH, f'(//span[text()="Users"])[last()]')
            elm.click()
            driver.execute_script("arguments[0].click();", elm)
            logging.info(f"Clicked on Users")            
            RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.popupNameFieldXpath))).send_keys(RANDOM_NAME))
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.okButtonXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath))).click())
            assert driver.find_element(By.XPATH, '//div[text()="Validation failed: Empty mandatory field [ firstName ] / Empty mandatory field [ email ] / Empty mandatory field [ userRole ]"]').is_displayed()
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="OK"])[2]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[@class="x-tab-close-btn"]'))).click())
            logging.info("passed 338_002")
            time.sleep(2)
            utils.search_by_id(driver, DEDICATED_STORE_USERID)
            get_firstname_text = driver.find_element(By.XPATH, '//input[@name="firstName"]').get_attribute('value')
            assert len(get_firstname_text) != 0
            get_email_text = driver.find_element(By.XPATH, '//input[@name="email"]').get_attribute('value')
            assert len(get_email_text) != 0
            get_usertype_text = driver.find_element(By.XPATH, '//input[@name="userType"]').get_attribute('value')
            assert len(get_usertype_text) != 0
            get_userRole_text = driver.find_element(By.XPATH, '//input[@name="userRole"]').get_attribute('value')
            assert len(get_userRole_text) != 0
            logging.info("passed 338_004")
            
            Success_List_Append(
                "test_MKTPL_338_CRUDUser_002_004",
                "Verify if mandatory fields are empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_338_CRUDUser_002_004",
            "Verify if mandatory fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_338_CRUDUser_002_004",
            "Verify if mandatory fields are empty",
            e,
        )
        raise e

def test_MKTPL_478_CategoryAPI_002_003_004():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_478_CategoryAPI_002_003_004.png"
        ) as driver:
            response = requests.request(
                "GET", CATEGORIES_URL, headers={
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }, data={}
            )
            logging.info(CRED_AUTOMATION_STORE_TOKEN)
            resp_data = response.json()
            assert response.status_code == 200 or response.status_code == 201
            limit = resp_data['data']['limit']
            offset = resp_data['data']['offset']
            records = resp_data['data']['totalRecords']
            assert limit == 10 and offset == 1
            logging.info("The pagination should be implemented in the API")
            logging.info("By default, the limit should be 10 and offset should be 1")
            assert records
            logging.info("API should display total numbers of categories records present in the pimcore and it should remain same throughout the pages")
            Success_List_Append("test_MKTPL_478_CategoryAPI_002_003_004","Verify if there are large numbers of records for the categories","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_478_CategoryAPI_002_003_004","Verify if there are large numbers of records for the categories","Fail",)
        Failure_Cause_Append("test_MKTPL_478_CategoryAPI_002_003_004", "Verify if there are large numbers of records for the categories",e,)
        raise e
    
def test_MKTPL_488_Taxpercentage_002():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_488_Taxpercentage_002.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.configuration_tab))).click())
        
            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//input[@name="taxPercentage"]')))
            tax_input = driver.find_element(By.XPATH, '//input[@name="taxPercentage"]')
            tax_input.send_keys("abc")
            driver.find_element(By.XPATH,'//span[text()="Tax Percentage:"]').click()
            time.sleep(3)
            get_val_tax_percentage = tax_input.get_attribute('aria-valuenow')
            logging.info(get_val_tax_percentage)
            assert get_val_tax_percentage is not "abc"
            
            tax_input = driver.find_element(By.XPATH, '//input[@name="taxPercentage"]')
            tax_input.send_keys("zero")
            driver.find_element(By.XPATH,'//span[text()="Tax Percentage:"]').click()
            time.sleep(3)
            get_val_tax_percentage = tax_input.get_attribute('aria-valuenow')
            logging.info(get_val_tax_percentage)
            assert get_val_tax_percentage is not "zero"
            
            tax_input.clear()
            tax_input.send_keys("@@")
            driver.find_element(By.XPATH,'//span[text()="Tax Percentage:"]').click()
            time.sleep(3)
            get_val_tax_percentage = tax_input.get_attribute('aria-valuenow')
            assert get_val_tax_percentage is not "@@"
            logging.info('testtest')
            
            tax_input.clear()
            tax_input.send_keys(25)
            driver.find_element(By.XPATH,'//span[text()="Tax Percentage:"]').click()
            time.sleep(3)
            get_val_tax_percentage = tax_input.get_attribute('aria-valuenow')
            assert get_val_tax_percentage == '25'
            
            
            Success_List_Append("test_MKTPL_488_Taxpercentage_002","verify validation of text percentage field.","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_488_Taxpercentage_002","verify validation of text percentage field.","Fail",)
        Failure_Cause_Append("test_MKTPL_488_Taxpercentage_002", "verify validation of text percentage field.",e,)
        raise e    
   
def test_MKTPL_488_Taxpercentage_003():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_488_Taxpercentage_003.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            utils.search_by_id(driver, DEDICATED_STORE_MARKETPLACE_ID)
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.configuration_tab))).click())
            
            tax_input = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//input[@name="taxPercentage"]')))
            assert tax_input.get_attribute('value') == ''
            assert tax_input.get_attribute('disabled') == 'true'
            assert tax_input.get_attribute('aria-readonly') == 'true'
            
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath))).click())
            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(("xpath", xpath.saveSuccessfullyMessageXpath))).is_displayed()
            
            Success_List_Append("test_MKTPL_488_Taxpercentage_003","verify validation of text percentage field.","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_488_Taxpercentage_003","verify validation of text percentage field.","Fail",)
        Failure_Cause_Append("test_MKTPL_488_Taxpercentage_003", "verify validation of text percentage field.",e,)
        raise e    

def test_MKTPL_3704_Newfromtodate_001():
    try:
        with utils.services_context_wrapper("test_MKTPL_3704_Newfromtodate_001.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            utils.search_by_id(driver, DEDICATED_PRODUCT_4_ID)

            Newfrom = WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '(//input[@title="Expected date format Y-m-d."])[1]'))).is_displayed()
            assert Newfrom

            Newto = WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '(//input[@title="Expected date format Y-m-d."])[2]'))).is_displayed()
            assert Newto

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'newFrom')))
            driver.find_element(By.NAME, 'newFrom').send_keys("2024-03-01")

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'newTo')))
            driver.find_element(By.NAME, 'newTo').send_keys("2024-03-15")

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'product_publish')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//span[@class="ace_constant ace_numeric"])[4]'))).is_displayed()


            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//span[@class="ace_constant ace_numeric"])[5]'))).is_displayed()


            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            utils.search_by_id(driver, DEDICATED_PRODUCT_4_ID)

            Newfrom1 = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'newFrom')))
            Newfrom1.clear()

            Newto1 = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'newTo')))
            Newto1.clear()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()

            Success_List_Append(
                " test_MKTPL_3704_Newfromtodate_001",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_3704_Newfromtodate_001",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_3704_Newfromtodate_001",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_3704_Newfromtodate_004():
    try:
        with utils.services_context_wrapper("test_MKTPL_3704_Newfromtodate_004.png") as driver:
            RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
            payload = json.dumps({
                "sku": RANDOM_NAME,
                "name": RANDOM_NAME,

                "deliveryMethod": [
                    "onboard"
                ],
                "newFrom": "2032-01-03",
                "newTo": "2033-03-03",
                "category": [
                    DEDICATED_CATEGORY_4_ID
                ],
                "productSet": "Simple",
                "productType": "Duty Free"

            })
            response = requests.request("POST", PRODUCTS_URL, headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN},
                                        data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            logging.info(
                f"POST API for Product responded with 200 status code")
            resp_data = response.json()
            assert "Product added successfully. " in resp_data["message"]
            product_id = str(resp_data["id"])
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }
            response = requests.request("GET",PRODUCTS_URL + product_id, headers=headers, data=payload)

            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            Newfrom33 = data['data']['product']['newFrom']
            Tofrom33 = data['data']['product']['newTo']
            # assert Newfrom33 == "2024-07-04" and Tofrom33 == "2024-09-06"
            try:
                datetime.date.fromisoformat(Newfrom33)
            except ValueError:
                raise ValueError("Incorrect data format - FROM, should be YYYY-MM-DD")
            try:
                datetime.date.fromisoformat(Tofrom33)
            except ValueError:
                raise ValueError("Incorrect data format - FROM, should be YYYY-MM-DD")
            try:
                UID = "ID " + product_id
                RT_VALUE = utils.Delete(UID, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_3704_Newfromtodate_004",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3704_Newfromtodate_004",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_3704_Newfromtodate_004",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_392_332_Catalogimport_005_006_007():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_392_332_Catalogimport_005_006_007.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            # 005 Verify by clicking on Upload button and/or CSV file field is empty
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.upload)))
            driver.find_element(By.XPATH, xpath.upload).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.mandatoryFieldErrorMessage))).is_displayed()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.okButtonXpath)))
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info("Validation message should display")

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Catalog")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Download Sample File Of Catalog"]')))
            driver.find_element(By.XPATH, '//span[text()="Download Sample File Of Catalog"]').click()
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_CATLOG_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_CATLOG_SAMPLE, "r", encoding='utf-8-sig'
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert any(items[0] in CSV_SAMPLE_CATLOG[0][0] for items in ACTUAL_CSV_DATA)
            assert any(items[0] in CSV_SAMPLE_CATLOG[0][3] for items in ACTUAL_CSV_DATA)
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_CATLOG_SAMPLE)

            # 006 Verify if uploaded file type is other than .csv format

            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "invalid_file.ppt")
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).\
                send_keys(path)
            time.sleep(5)
            driver.find_element(By.XPATH, xpath.upload).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="File Import: Uploaded file should be CSV file with .csv extension."]'))).is_displayed()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.okButtonXpath)))
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info("Validation message should display")

            # 007 Verify if data is not enter in any mandatory column
            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "Catalog_Sample_validation.csv")
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton). \
                send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, "catalog", "Name cannot be blank. It is a mandatory field. | PAC1123 is not a valid\/published product sku for store Cred Automation Store")


            Success_List_Append(
                " test_MKTPL_392_332_Catalogimport_005_006_007",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_392_332_Catalogimport_005_006_007",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_392_332_Catalogimport_005_006_007",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_392_332_Catalogimport_008():
    try:
        with utils.services_context_wrapper("test_MKTPL_392_332_Catalogimport_008.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Catalog")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            RANDOM_NAME = "222" + "".join(random.choices(string.ascii_letters, k=7))
            utils.update_csv("Catalog_Sample_validation.csv", 'products', RANDOM_NAME)

            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "Catalog_Sample_validation.csv")
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton). \
                send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, "catalog",
                                     f"{RANDOM_NAME} is not a valid\/published product sku for store Cred Automation Store")

            Success_List_Append(
                " test_MKTPL_392_332_Catalogimport_008",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_392_332_Catalogimport_008",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_392_332_Catalogimport_008",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_392_332_Catalogimport_009():
    try:
        with utils.services_context_wrapper("test_MKTPL_392_332_Catalogimport_009.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Catalog")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            utils.update_csv("Catalog_Sample_validation.csv", 'products', PRODUCT_DIFFERENT_STORE)

            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "Catalog_Sample_validation.csv")
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton). \
                send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, "catalog",
                                     "Name cannot be blank. It is a mandatory field. | '"+str(PRODUCT_DIFFERENT_STORE)+"' is not a valid\/published product sku for store Cred Automation Store")

            Success_List_Append(
                " test_MKTPL_392_332_Catalogimport_009",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_392_332_Catalogimport_009",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_392_332_Catalogimport_009",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_390_388_Productimport_020_021_022_023_024():
    RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=7))

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_390_388_Productimport_020_021_022_023_024.png"
        ) as driver:
            driver.maximize_window()
            # 020. Verify if negative values are entered in weight, height, length, width, price, special price, cost,
            # max Quantity Allowed, min Quantity Allowed
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "specialPrice_USD", "-10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "price_USD", "-10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "minQuantityAllowed/Order", "-11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "maxQuantityAllowed/Order", "-10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weight", "-11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLength", "-11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeight", "-11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidth", "-11")
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("ProductCsv_invalid_NumericFields.csv", file_path)
            assert saved_message
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "specialPrice_USD", "10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "price_USD", "10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "minQuantityAllowed/Order", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "maxQuantityAllowed/Order", "10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weight", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLength", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeight", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidth", "11")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver,PAC_STORE_USER_MAIL, 'products', 'Invalid value for weight')
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Invalid value\'price_USD\': -10")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Invalid value\'specialPrice_USD\': -10")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Invalid value for weight")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Invalid value for shippingLength")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Invalid value for shippingWidth")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Invalid value for shippingHeight")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Numeric value for maxQuantityAllowed\/Order must be greater than or equal to 1")])[1]'))).is_displayed())
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until((EC.presence_of_element_located((By.XPATH, xpath.userName))))
            # ---------------------------------------
            # 021. Verify if values are entered in weight, height, length, width, price, special price, cost but values
            # are not entered in unit
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "specialPrice_USD", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "price_USD", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "minQuantityAllowed/Order", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "maxQuantityAllowed/Order", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weight", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLength", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeight", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidth", "")
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("ProductCsv_invalid_NumericFields.csv", file_path)
            assert saved_message
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "specialPrice_USD", "10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "price_USD", "10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "minQuantityAllowed/Order", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "maxQuantityAllowed/Order", "10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weight", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLength", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeight", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidth", "11")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, 'products', 'weight is required with weightUnit')
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '(//div[contains(text(),"shippingLength is required with shippingLength Unit")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,
                                                                                    '(//div[contains(text(),"shippingWidth is required with shippingWidth Unit")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '(//div[contains(text(),"shippingHeight is required with shippingHeightUnit")])[1]'))).is_displayed())
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until((EC.presence_of_element_located((By.XPATH, xpath.userName))))
            # ------------------------------------
            # 023. Verify if values are entered in weight, height, length, width, price, special price, cost but values
            # are not entered in unit
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys("" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weightUnit", "10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLengthUnit", "10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidthUnit", "11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeightUnit", "10")
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("ProductCsv_invalid_NumericFields.csv", file_path)
            assert saved_message
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weightUnit", "LB")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLengthUnit", "IN")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidthUnit", "IN")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeightUnit", "IN")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, 'products', 'weightUnit parameters not matched with the store configuration.')
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '(//div[contains(text(),"shippingLengthUnit parameters not matched with the store configuration.")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(), "shippingWidthUnit parameters not matched with the store configuration.")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '(//div[contains(text(),"shippingHeightUnit parameters not matched with the store configuration.")])[1]'))).is_displayed())
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until((EC.presence_of_element_located((By.XPATH, xpath.userName))))
            # # ------------------------------------
            # 021. Verify if values are entered in weight, height, length, width, price, special price, cost but values
            # are not entered in unit
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weightUnit", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLengthUnit", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidthUnit", "")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeightUnit", "")
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("ProductCsv_invalid_NumericFields.csv", file_path)
            assert saved_message
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weightUnit", "LB")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLengthUnit", "IN")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidthUnit", "IN")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeightUnit", "IN")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, 'products', 'weightUnit not found in weightUnit column.')
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH,'(//div[contains(text(),"shippingLengthUnit not found in shippingLengthUnit column.")])[1]'))).is_displayed())
            # assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,
            #                                                                         '(//div[contains(text(), "shippingWidthUnit parameters not matched with the store configuration.")])[1]'))).is_displayed())
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until((EC.presence_of_element_located((By.XPATH, xpath.userName))))
            # ------------------------------------------------------------------------------------------------------------
            # 024. Verify if weight, height, length, width unit is not matched with store configuration
            # are not entered in unit
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weightUnit", " ")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLengthUnit", " ")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidthUnit", " ")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeightUnit", " ")
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("ProductCsv_invalid_NumericFields.csv", file_path)
            assert saved_message
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "weightUnit", "LB")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingLengthUnit", "IN")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidthUnit", "IN")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingHeightUnit", "IN")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver, PAC_STORE_USER_MAIL, 'products',
                                     'weightUnit parameters not matched with the store configuration.')
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH,
                 '(//div[contains(text(),"shippingLengthUnit parameters not matched with the store configuration.")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,
                       '(//div[contains(text(), "shippingWidthUnit parameters not matched with the store configuration.")])[1]'))).is_displayed())
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until((EC.presence_of_element_located((By.XPATH, xpath.userName))))
            Success_List_Append("test_MKTPL_390_388_Productimport_020_021_022_023_024",
                                "Verify if negative values are entered in weight, height, length, width, price, special price, cost, max Quantity Allowed, min Quantity Allowed ", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_390_388_Productimport_020_021_022_023_024",
                            "Verify if negative values are entered in weight, height, length, width, price, special price, cost, max Quantity Allowed, min Quantity Allowed ","Fail",)
        Failure_Cause_Append("test_MKTPL_390_388_Productimport_020_021_022_023_024",
                             "Verify if negative values are entered in weight, height, length, width, price, special price, cost, max Quantity Allowed, min Quantity Allowed ",e,)
        raise e

def test_MKTPL_390_388_Productimport_025():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_390_388_Productimport_025.png"
        ) as driver:
            driver.maximize_window()
            # 025. Verify if data entered is not 0 or 1 for spotlight, age verification require, available, requireShipping, isvariantDefault, isPerishable, isTaxable
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            csvImportFun = utils.csv_import(driver, "Products")
            assert csvImportFun
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "spotLight", "-10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "ageVerificationRequire", "-10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "isAvailableToSell", "-11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "requireShipping", "-10")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "isvariantDefault", "-11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "isPerishable", "-11")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "isTaxable", "-11")
            # utils.update_csv("ProductCsv_invalid_NumericFields.csv", "shippingWidth", "-11")
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("ProductCsv_invalid_NumericFields.csv", file_path)
            assert saved_message
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "spotLight", "1")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "ageVerificationRequire", "1")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "isAvailableToSell", "1")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "requireShipping", "1")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "isvariantDefault", "1")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "isPerishable", "1")
            utils.update_csv("ProductCsv_invalid_NumericFields.csv", "isTaxable", "1")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            #  --------------------- verify log's  ---------------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Store(driver)
            utils.verify_import_logs(driver,PAC_STORE_USER_MAIL, 'products', 'Please enter a valid spotlight value - 1 for YES \/ 0 for NO')
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Please enter a valid age verification value - 1 for YES \/ 0 for NO")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Please enter a valid availability value - 1 for YES \/ 0 for NO")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Please enter a valid require shipping value - 1 for YES \/ 0 for NO")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Please enter a valid product variant value - 1 for YES \/ 0 for NO")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Please enter a valid perishable value - 1 for YES \/ 0 for NO")])[1]'))).is_displayed())
            assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Please enter a valid isTaxable value - 1 for YES \/ 0 for NO")])[1]'))).is_displayed())
            logging.info('Validation message should display')
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until((EC.presence_of_element_located((By.XPATH, xpath.userName))))
            Success_List_Append("test_MKTPL_390_388_Productimport_025",
                                "Verify if data entered is not 0 or 1 for spotlight, age verification require, available, requireShipping, isvariantDefault, isPerishable, isTaxable", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_390_388_Productimport_025",
                            " Verify if data entered is not 0 or 1 for spotlight, age verification require, available, requireShipping, isvariantDefault, isPerishable, isTaxable","Fail",)
        Failure_Cause_Append("test_MKTPL_390_388_Productimport_025",
                             "Verify if data entered is not 0 or 1 for spotlight, age verification require, available, requireShipping, isvariantDefault, isPerishable, isTaxable ",e,)
        raise e

def test_MKTPL_315_Orderthresholdlimits_002_003_004():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_315_Orderthresholdlimits_002_003_004.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.configuration_tab))).click())
            order_threshold_field = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//input[@class="x-form-field x-form-text x-form-text-default   "])[7]')))
            assert order_threshold_field.get_attribute("value")
            order_threshold_field.clear()
            order_threshold_field.send_keys("-5")
            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//div[text()="The Order Threshold Value Should not be Negative"]'))).is_displayed()
            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//span[text()="OK"])[2]'))).click()
            order_threshold_field.clear()
            order_threshold_field.send_keys("4.5")
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located(("xpath", xpath.saveSuccessfullyMessageXpath))).is_displayed()
            logging.info("passed 002_003_004")
  
            Success_List_Append("test_MKTPL_315_Orderthresholdlimits_002_003_004","Verify by clicking on the store","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_315_Orderthresholdlimits_002_003_004","Verify by clicking on the store","Fail",)
        Failure_Cause_Append("test_MKTPL_315_Orderthresholdlimits_002_003_004", "Verify by clicking on the store",e,)
        raise e  

def test_MKTPL_1264_TaxpercentageAPI_002_003_004_005_006_007():
    random_float = random.uniform(10.32, 11.34)
    # random_float_rounded = round(random_float, 2)
    # logging.info(random_float_rounded)
    try:
        with utils.services_context_wrapper("test_MKTPL_1264_TaxpercentageAPI_002_003_004_005_006_007.png") as driver:
            # 002 Verify if the mandatory parameters are empty in the request
            payload = utils.get_payload_taxPercentage_API(None)
            response = requests.request("POST", TAX_URL, headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400 or response.status_code == 401
            logging.info(resp_data["message"])
            # assert resp_data["message"] == "Invalid request: Mandatory field: taxPercentage"
            assert "Tax Percentage cannot be blank. It is a mandatory field" in resp_data["message"]
            logging.info("The tax URL shows validation for mandatory field")
            # 003 Verify when store token used have sales strategy as Marketplace in store profile
            payload = utils.get_payload_taxPercentage_API(random_float)
            response = requests.request("POST", TAX_URL, headers={"Authorization": CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN},
                                        data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400 or response.status_code == 401
            logging.info(resp_data["message"])
            assert resp_data["message"] == "Cannot update Tax Percentage as you have selected Sales Strategy as 'Marketplace'."
            logging.info(f"The tax URL shows validation for Marketplace store token")
            # 004 Verify if user enters more than 100 value in tax percentage
            payload = utils.get_payload_taxPercentage_API(100.000000000000006)
            response = requests.request("POST", TAX_URL,
                                        headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                                        data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            # assert response.status_code == 400 or response.status_code == 401
            # logging.info(resp_data["message"])
            # assert resp_data[
            #            "message"] == "Tax Percentage value is not valid."
            logging.info(f"The tax URL shows validation for more than 100 value")
            # 005 Verify if negative values are entered in the tax percentage
            payload = utils.get_payload_taxPercentage_API(-5)
            response = requests.request("POST", TAX_URL,
                                        headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                                        data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400 or response.status_code == 401
            logging.info(resp_data["message"])
            assert resp_data[
                       "message"] == "Tax Percentage value is not valid."
            logging.info(f"The tax URL shows validation for negative value")
            # 006 Verify if Fraction values are entered in the tax percentage
            payload = utils.get_payload_taxPercentage_API(random_float)
            response = requests.request("POST", TAX_URL,
                                        headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                                        data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(resp_data["message"])
            assert resp_data[
                       "message"] == "Tax Percentage updated successfully."
            # 007 Verify the newly added parameter in the response
            response = requests.request("GET", STORE_GET_URL,
                                        headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                                        data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            taxPercentage = resp_data["data"]["store"]["taxPercentage"]
            logging.info(taxPercentage)
            assert str(taxPercentage) in str(round(random_float,12))
            logging.info(f"The tax URL allows adding decimal values")
        Success_List_Append(
            "test_MKTPL_1264_TaxpercentageAPI_002_003_004_005_006_007",
            "Verify the newly added parameter in the response",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1264_TaxpercentageAPI_002_003_004_005_006_007",
            "Verify the newly added parameter in the response",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1264_TaxpercentageAPI_002_003_004_005_006_007",
            "Verify the newly added parameter in the response",
            e,
        )
        raise e

def test_MKTPL_479_ProductAPI_002_003():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_479_ProductAPI_002_003.png"
        ) as driver:
            response = requests.request(
                "GET", PRODUCTS_URL, headers={
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }, data={}
            )
            resp_data = response.json()
            assert response.status_code == 200 or response.status_code == 201
            categoryImageUrl = resp_data["data"]["products"][0]["category"][0]["categoryImageUrl"]
            assert categoryImageUrl
            parsed_url = urlparse(categoryImageUrl)
            extension = parsed_url.path.rsplit(".")[1:]
            logging.info(extension)
            image_url_match = re.search(r'(https://.*\.(jpg|jpeg|png|svg|GIF))', categoryImageUrl, re.IGNORECASE)
            assert image_url_match
            assert 'gallery' in resp_data['data']['products'][0]['assets']
            logging.info("Gallery parameter exists.")
            logging.info("passed: 002_003")
            Success_List_Append("test_MKTPL_479_ProductAPI_002_003","Verify the image parameter","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_479_ProductAPI_002_003","Verify the image parameter","Fail",)
        Failure_Cause_Append("test_MKTPL_479_ProductAPI_002_003", "Verify the image parameter",e,)
        raise e
    
def test_MKTPL_479_ProductAPI_004_005_006():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_479_ProductAPI_004_005_006.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            utils.search_by_id(driver, DEDICATED_PRODUCT_1_ID)
            
            ageverification_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.ageVerificationRequiredName)))
            if ageverification_checkbox.is_selected():
                print("age verification Checkbox is checked")
            else:
                print("age verification Checkbox is not checked")
                ageverification_checkbox.click()
        
            spotlight_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.spotLightName)))
            if spotlight_checkbox.is_selected():
                print("spotlight Checkbox is checked")
            else:
                print("spotlight Checkbox is not checked")
                spotlight_checkbox.click()
            
            require_shipping_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.requireShippingName)))
            if require_shipping_checkbox.is_selected():
                print("require shipping Checkbox is checked")
            else:
                print("require shipping Checkbox is not checked")
                require_shipping_checkbox.click()
                
            is_in_stock_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.isAvailableToSellName)))
            if is_in_stock_checkbox.is_selected():
                print("is available to sell Checkbox is checked")
            else:
                print("is available to sell Checkbox is not checked")
                is_in_stock_checkbox.click()
            
            time.sleep(3)
            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.priceAndTaxationPageXpath))).click()
            taxable_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.isTaxableName)))
            if taxable_checkbox.is_selected():
                print("taxable Checkbox is checked")
            else:
                print("texable Checkbox is not checked")
                taxable_checkbox.click()
            
            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath))).click()
            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located(("xpath", xpath.saveSuccessfullyMessageXpath))).is_displayed()
            time.sleep(3)            
            # API call
            apiUrl = PRODUCTS_URL+DEDICATED_PRODUCT_1_ID
            response = requests.request("GET", apiUrl, headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN}, data={})
            resp_data = response.json()
            time.sleep(3)
            ageverification = resp_data["data"]["product"]["ageVerificationRequire"]
            assert ageverification == True
            spotlight = resp_data["data"]["product"]["spotlight"]
            assert spotlight == True
            requireShipping = resp_data["data"]["product"]["requireShipping"]
            assert requireShipping == True
            stock = resp_data["data"]["product"]["isAvailableToSell"]
            assert stock == True
            taxableIs = resp_data["data"]["product"]["PriceTaxation"]["isTaxable"]
            assert taxableIs == True
            logging.info("passed testcase: 004")
            
            #005
            apiUrl = PRODUCTS_URL+ "*********"
            response = requests.request("GET", apiUrl, headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN}, data={})
            resp_data = response.json()
            assert resp_data["message"] == "No Record Found" or resp_data["message"] == "No Result Found."
            logging.info("validation check: passed 005")
            
            #006
            response = requests.request("GET", PRODUCTS_URL, headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN}, data={})
            resp_data = response.json()
            for productexist in resp_data["data"]["products"]:
                assert productexist["id"] != PRODUCT_DIFFERENT_STORE

            Success_List_Append("test_MKTPL_479_ProductAPI_004_005_006","Verify by clicking on the store","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_479_ProductAPI_004_005_006","Verify by clicking on the store","Fail",)
        Failure_Cause_Append("test_MKTPL_479_ProductAPI_004_005_006", "Verify by clicking on the store",e,)
        raise e  
    
def test_MKTPL_479_ProductAPI_030_031_035_036_039_040_041_047_048_033_050_052_053_056():
    RANDOM_NAME = "(From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    RANDOM_NAME_SKU = "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_479_ProductAPI_030_031_035_036_039_040_041_047_048_033_050_052_053_056.png"
        ) as driver:
            # 031. Verify if past date is entered in newfrom/newto parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST", PRODUCTS_URL, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard", int(DEDICATED_CATEGORY_1_ID),
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "2022-01-03", "2022-03-03", 1, 11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "From date should not be in past" == api_response["message"]
            logging.info("031. POST - API should display validation message")
            # 048. Verify if past date is entered in newfrom/newto parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT", PRODUCTS_URL+DEDICATED_PRODUCT_1_ID, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "2022-04-03", "2022-03-09", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "From date should not be in past" == api_response["message"]
            logging.info("048. PUT - API should display validation message")
            # 033. Verify if newfrom date is more than newto date in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST",
                                                     PRODUCTS_URL, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "2025-04-09", "2025-03-09", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "From date should not be more than To date" == api_response["message"]
            logging.info("033. POST - API should display validation message")
            # 050. Verify if newfrom date is more than newto date in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT",
                                                     PRODUCTS_URL+DEDICATED_PRODUCT_1_ID, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "2025-04-09", "2025-03-09", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "From date should not be more than To date" == api_response["message"]
            logging.info("050. PUT - API should display validation message")
            # 035. Verify if 13 or more digit is enter in month in newfrom/newto parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST",
                                                     PRODUCTS_URL, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "2025-14-09", "", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "Invalid From date" == api_response["message"]
            logging.info("035. POST - API should display validation message")
            # 052. Verify if 13 or more digit is enter in month in newfrom/newto parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT",
                                                     PRODUCTS_URL+DEDICATED_PRODUCT_1_ID, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "2025-14-09", "", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "Invalid From date" == api_response["message"]
            logging.info("052. PUT - API should display validation message")
            # 036. Verify if 32 or more digit is enter in date in newfrom/newto parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST",
                                                     PRODUCTS_URL, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "2025-03-38", "", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "Invalid From date" == api_response["message"]
            logging.info("036. POST - API should display validation message")
            # 053. Verify if 32 or more digit is enter in date in newfrom/newto parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT",
                                                     PRODUCTS_URL + DEDICATED_PRODUCT_1_ID, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "2025-03-38", "", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "Invalid From date" == api_response["message"]
            logging.info("053. PUT - API should display validation message")
            # 039. Verify if negative values are entered in weight, height, length, width, price, special price, cost,
            # qty, qty Threshold, max Quantity Allowed, min Quantity Allowed parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST",
                                                     PRODUCTS_URL, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     -1, "LB", -1, "IN", -1, "IN", -1, "IN", "2025-03-38", "", -1,
                                                     -11, -11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "price value can not be negative" == api_response["message"]
            logging.info("039. POST - API should display validation message")
            # 056. Verify if negative values are entered in weight, height, length, width, price, special price, cost,
            # qty, qty Threshold, max Quantity Allowed, min Quantity Allowed parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT",
                                                     PRODUCTS_URL+DEDICATED_PRODUCT_1_ID, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     -1, "LB", -1, "IN", -1, "IN", -1, "IN", "2025-03-38", "", -1,
                                                     -11, -11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "price value can not be negative" == api_response["message"]
            logging.info("056. POST - API should display validation message")
            # 030. Verify if unit is not entered in mandatory shipping attributes parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST",
                                                     PRODUCTS_URL, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "", 1, "", 1, "", 1, "", "", "", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "weight Unit can not be empty if value is given" == api_response["message"]
            logging.info("030. POST - API should display validation message")
            # 047. Verify if unit is not entered in mandatory shipping attributes parameters in the request
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT",
                                                     PRODUCTS_URL+DEDICATED_PRODUCT_1_ID, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     int(DEDICATED_CATEGORY_1_ID),
                                                     1, "", 1, "", 1, "", 1, "", "", "", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "weight Unit can not be empty if value is given" == api_response["message"]
            logging.info("047. POST - API should display validation message")
            # 040. Verify if the mandatory product category attributes is empty in the key in the request
            payload = json.dumps({
              "sku": "ede",
              "name": "ede fcfcfc",
              "deliveryMethod": [
                "Onboard"
              ],
              "category": [
                int(DEDICATED_CATEGORY_2_ID)
              ],
              "Attributes": {
                "productCategoryAttributes": [

                ]
              },
              "productSet": "Configurable",
              "storeProductId": "134A34",
              "productType": "Food and Beverage"
            })
            headers = { "Authorization": CRED_AUTOMATION_STORE_TOKEN, "Content-Type": "application/json",
            }
            response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Product Category Attributes cannot be empty when Product Set is Configurable" == resp_data["message"]
            logging.info("040. POST - API should display validation message")
            # 041. Verify if the value of mandatory product category attributes is empty in the request
            payload = json.dumps({
                "sku": "ede",
                "name": "ede fcfcfc",
                "deliveryMethod": [
                    "Onboard"
                ],
                "category": [
                    int(DEDICATED_CATEGORY_2_ID)
                ],
                "Attributes": {
                    "productCategoryAttributes": [
                        {
                            "key": "",
                            "value": ""
                        }

                    ]
                },
                "productSet": "Configurable",
                "storeProductId": "134A34",
                "productType": "Food and Beverage"
            })
            headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN, "Content-Type": "application/json",
                       }
            response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Variant Attributes needed when product Set is Configurable" == resp_data["message"]
            logging.info("041. POST - API should display validation message")
            # 057. Verify if the mandatory product category attributes is empty in the key in the request
            # payload = json.dumps({
            #     "sku": "ede",
            #     "name": "ede fcfcfc",
            #     "deliveryMethod": [
            #         "Onboard"
            #     ],
            #     "category": [
            #         9268
            #     ],
            #     "Attributes": {
            #         "productCategoryAttributes": [
            #
            #         ]
            #     },
            #     "productSet": "Configurable",
            #     "storeProductId": "134A34",
            #     "productType": "Food and Beverage"
            # })
            # headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN, "Content-Type": "application/json",
            #            }
            # response = requests.request("PUT", PRODUCTS_URL+DEDICATED_PRODUCT_11_ID, headers=headers, data=payload)
            # resp_data = response.json()
            # logging.info(resp_data)
            # assert response.status_code == 400 or response.status_code == 401
            # assert "Product Category Attributes cannot be empty when Product Set is Configurable" == resp_data[
            #     "message"]
            # logging.info("057. POST - API should display validation message")
            Success_List_Append(
                "test_MKTPL_479_ProductAPI_030_031_035_036_039_040_041_047_048_033_050_052_053_056",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_479_ProductAPI_030_031_035_036_039_040_041_047_048_033_050_052_053_056",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_479_ProductAPI_030_031_035_036_039_040_041_047_048_033_050_052_053_056",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_479_ProductAPI_040():
    try:
        RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
        with utils.services_context_wrapper(
            "test_MKTPL_479_ProductAPI_040.png"
        ) as driver:

            #040 passing empty key and check validation msg
            payload = utils.get_Product_Payload(RANDOM_NAME, int(DEDICATED_VARIENT_PARENT_PRODUCT_ID), "Onboard", int(DEDICATED_CATEGORY_1_ID), "Configurable", "Duty Free",0,0,0, "", "8GB", True)
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
            resp_data = response.json()
            # logging.info("--//--")
            # logging.info(resp_data)
            # logging.info("--//--")
            
            assert resp_data['code'] == 400 or resp_data['code'] == 401
            assert "Variant Attributes do not match with Category Variant Themes " in resp_data["message"]
            
            Success_List_Append("test_MKTPL_479_ProductAPI_040","Verify if special characters or space are added in sku/barcode parameters in the request","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_479_ProductAPI_040","Verify if special characters or space are added in sku/barcode parameters in the request","Fail",)
        Failure_Cause_Append("test_MKTPL_479_ProductAPI_040", "Verify if special characters or space are added in sku/barcode parameters in the request",e,)
        raise e  

def test_MKTPL_479_ProductAPI_012_013_014_20_21_22():
    try:
        with utils.services_context_wrapper("test_MKTPL_479_ProductAPI_012_013_014") as driver:
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            }

            response = requests.request("GET", PRODUCTS_URL, headers=headers,
                                        data={})

            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            limit = resp_data['data']['limit']
            offset = resp_data['data']['offset']
            records = resp_data['data']['totalRecords']
            gallry = resp_data['data']['products'][0]['assets']
            assert limit == 10 and offset == 1
            logging.info("The pagination should be implemented in the API")
            logging.info("By default, the limit should be 10 and offset should be 1")
            logging.info(limit)
            logging.info(offset)
            logging.info(records)
            logging.info(gallry)
            assert records
            assert gallry
            logging.info(
                "API should display total numbers of Catalog records present in the pimcore and it should remain same throughout the pages")
            # 20 21 22
            response = requests.request("GET", PRODUCTS_URL+str(DEDICATED_VARIENT_PARENT_PRODUCT_ID)+"/variant", headers=headers,
                                        data={})

            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            limit = resp_data['data']['limit']
            offset = resp_data['data']['offset']
            records = resp_data['data']['totalRecords']
            assert limit == 10 and offset == 1
            logging.info("The pagination should be implemented in the API")
            logging.info("By default, the limit should be 10 and offset should be 1")
            logging.info(limit)
            logging.info(offset)
            logging.info(records)
            assert records
            logging.info(
                "API should display total numbers of Catalog records present in the pimcore and it should remain same throughout the pages")
            Success_List_Append(
                "test_MKTPL_479_ProductAPI_012_013_014",
                "API should display validation message",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_479_ProductAPI_012_013_014",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_479_ProductAPI_012_013_014",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_479_ProductAPI_009_010_017():
    try:
        with utils.services_context_wrapper("test_MKTPL_479_ProductAPI_009_010_017.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            utils.search_by_id(driver, DEDICATED_PRODUCT_2_ID)
            # age verficication
            ageverification_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.ageVerificationRequiredName)))
            if ageverification_checkbox.is_selected():
                logging.info("age verification Checkbox is checked")
            else:
                logging.info("age verification Checkbox is not checked")
                ageverification_checkbox.click()
            # spotlight
            spotlight_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.spotLightName)))
            if spotlight_checkbox.is_selected():
                logging.info("spotlight Checkbox is checked")
            else:
                logging.info("spotlight Checkbox is not checked")
                spotlight_checkbox.click()
            # require shipping
            require_shipping_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.requireShippingName)))
            if require_shipping_checkbox.is_selected():
                logging.info("require shipping Checkbox is checked")
            else:
                logging.info("require shipping Checkbox is not checked")
                require_shipping_checkbox.click()
            # price taxation
            time.sleep(3)
            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.priceAndTaxationPageXpath))).click()
            taxable_checkbox = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, xpath.isTaxableName)))
            if taxable_checkbox.is_selected():
                logging.info("taxable Checkbox is checked")
            else:
                logging.info("texable Checkbox is not checked")
                taxable_checkbox.click()
            utils.save_and_publish(driver)
            time.sleep(5)
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }
            response = requests.request("GET", PRODUCTS_URL+DEDICATED_PRODUCT_2_ID, headers=headers,
                                        data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            gallry = resp_data['data']['product']['assets']
            priceTaxation = resp_data['data']['product']['PriceTaxation']['isTaxable']
            Spotlight = resp_data['data']['product']['spotlight']
            RequireShipping = resp_data['data']['product']['requireShipping']
            ageverificationrequire = resp_data['data']['product']['ageVerificationRequire']
            logging.info(gallry)
            logging.info(priceTaxation)
            logging.info(Spotlight)
            logging.info(RequireShipping)
            logging.info(ageverificationrequire)
            assert 'gallery' in gallry
            assert priceTaxation
            assert Spotlight
            assert RequireShipping
            assert ageverificationrequire

            logging.info("API should display full and correct URL including file extension and position of the image")
            Success_List_Append(
                "test_MKTPL_479_ProductAPI_009_010_017",
                "API should display validation message",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_479_ProductAPI_009_010_017",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_479_ProductAPI_009_010_017",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_479_ProductAPI_019_025_027_028_029_042_044_045_046_059():
    RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
    try:
        with utils.services_context_wrapper("test_MKTPL_479_ProductAPI_019_025_027_028_029_042_044_045_046_059.png") as driver:
            # 025 042 POST
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST", PRODUCTS_URL, RANDOM_NAME,
                                      RANDOM_NAME, RANDOM_NAME, RANDOM_NAME,
                                      RANDOM_NAME, "Onboard", DEDICATED_CATEGORY_2_ID,
                                      1, "LB", 1, "IN", 1, "IN",
                                      1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                      11, True)
            
            assert api_response["code"] == 200 or api_response["code"] == 201
            ID = api_response['id']
            logging.info(ID)
            
            # UI call
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            utils.search_by_id(driver, ID)
            
            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Non-alcoholic (Cred Automation Store/Beverages/Non-alcoholic)"]'))).is_displayed()
            
            logging.info("025 042 The product should be created in the pimcore with all the details provided in the API and it should be publish in the pimcore")
            
            # 027 POST
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST", PRODUCTS_URL,"", "",
                                                      RANDOM_NAME, RANDOM_NAME,RANDOM_NAME, "Onboard", DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "name" in  api_response["message"] or "sku" in  api_response["message"]
            
            logging.info("027 POST API should display validation message for the mandatory parameters")
            
            # 044 PUT
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT", PRODUCTS_URL + DEDICATED_PRODUCT_2_ID, "", "",
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME, "Onboard",
                                                     DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "name" in  api_response["message"] or "sku" in  api_response["message"]
            logging.info("044 PUT API should display validation message for the mandatory parameters")
            
            # 028 POST
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST", PRODUCTS_URL, RANDOM_NAME,
                                                     RANDOM_NAME, "", "",
                                                     "",
                                                     "Onboard", DEDICATED_CATEGORY_2_ID,
                                                     0, "", 0, "", 0, "",
                                                     0, "", "", "", 11, 11,
                                                     "", True
                                                     )
            
            assert api_response["code"] == 200 or api_response["code"] == 201
            assert "Product added successfully. " in api_response["message"]
            logging.info("028 POST API should display validation message for the mandatory parameters")
            
            # 045 PUT
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT", PRODUCTS_URL + DEDICATED_PRODUCT_2_ID, RANDOM_NAME,
                                                     RANDOM_NAME, "", "",
                                                     "",
                                                     "Onboard", DEDICATED_CATEGORY_2_ID,
                                                     0, "", 0, "", 0, "",
                                                     0, "", "", "", 10, 11,
                                                     "", True)
            
            assert api_response["code"] == 200 or api_response["code"] == 201
            assert "Product updated successfully. " in api_response["message"]
            logging.info("045 PUT API should display validation message for the mandatory parameters")
            
            # 029 POST SKU
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST", PRODUCTS_URL, "@#$", RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME, "Onboard",
                                                     DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "SKU value doesn't match input validation [a-zA-Z0-9-_]" in api_response["message"]
            logging.info("029 POST SKU API should display validation message for the mandatory parameters")
            
            # 029 POST BARCODE
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST", PRODUCTS_URL, RANDOM_NAME,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, "@#$", "Onboard",
                                                     DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "Barcode value doesn't match input validation [a-zA-Z0-9]" in api_response["message"]
            logging.info("029 POST BARCODE API should display validation message for the mandatory parameters")
            
            # 046 PUT SKU
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT", PRODUCTS_URL + DEDICATED_PRODUCT_2_ID, "@#$",
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME, "Onboard",
                                                     DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "SKU value doesn't match input validation [a-zA-Z0-9-_]" in api_response["message"]
            logging.info("029 PUT SKU API should display validation message for the mandatory parameters")
            
            # 046 PUT BARCODE
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT",
                                                     PRODUCTS_URL + DEDICATED_PRODUCT_2_ID, RANDOM_NAME,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, "@#$", "Onboard",
                                                     DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "Barcode value doesn't match input validation [a-zA-Z0-9]" in api_response["message"]
            logging.info("029 PUT BARCODE API should display validation message for the mandatory parameters")
            
            # 059 PATCH
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(5)]))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PATCH",
                                                     PRODUCTS_URL + RANDOM_NUMBER, RANDOM_NAME,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME, "Onboard",
                                                     DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "Please provide valid product id" in api_response["message"]
            logging.info("059 PATCH API should display validation message for the mandatory parameters")
            
            # 019 PATCH
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(5)]))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PATCH",
                                                     PRODUCTS_URL + RANDOM_NUMBER, RANDOM_NAME,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME, "Onboard",
                                                     DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            
            assert api_response["code"] == 400 or api_response["code"] == 401
            assert "Please provide valid product id" in api_response["message"]
            logging.info("019 PUT API should display validation message for the mandatory parameters")

            # 017 PATCH
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(5)]))
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PATCH",
                                                     PRODUCTS_URL +  DEDICATED_PRODUCT_2_ID, RANDOM_NAME,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME, "Onboard",
                                                     DEDICATED_CATEGORY_2_ID,
                                                     1, "LB", 1, "IN", 1, "IN",
                                                     1, "IN", "2024-08-04", "2024-12-04", 10, 11,
                                                     11, True)
            logging.info(api_response)
            # gallry = resp_data['data']['product']['assets']
            Success_List_Append(
                "test_MKTPL_479_ProductAPI_019_025_027_028_029_042_044_045_046_059",
                "API should display validation message",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_479_ProductAPI_019_025_027_028_029_042_044_045_046_059",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_479_ProductAPI_019_025_027_028_029_042_044_045_046_059",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_778_Duplicatecatalogsassignments_001_002_003():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_778_Duplicatecatalogsassignments_001_002_003.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # utils.revert_auto_populate_auto_map_airline_categories(driver)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.catalogManagement))
            )
            driver.find_element(By.XPATH, xpath.catalogManagement).click()
            logging.info("Clicked on Catalog Management")
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, '(//span[text()="Cred Automation Store"])[2]'))
            )
            # Move to Cred Automation Store
            home = driver.find_element(By.XPATH, '(//span[text()="Cred Automation Store"])[2]')
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            logging.info("Right clicked on Cred Automation Store")
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.refresh))
            )
            driver.find_element(By.XPATH, xpath.refresh).click()
            logging.info("Clicked on refresh")
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, '(//span[text()="Cred Automation Store"])[2]'))
            )
            # Move to home icon
            home = driver.find_element(By.XPATH, '(//span[text()="Cred Automation Store"])[2]')
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            logging.info("Right clicked on Cred Automation Store")
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            action = ActionChains(driver)
            action.move_to_element(add_object).perform()
            logging.info("Clicked on Add Object")
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, '(//span[(text()="Catalog")])[2]'))
            )
            # Click on Catalog
            elm = driver.find_element(By.XPATH, '(//span[(text()="Catalog")])[2]')
            driver.execute_script("arguments[0].click();", elm)
            logging.info("Clicked on Catalog")
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//div[text()="Enter the name of the new item"])[1]'
                                                          '//parent::div//input'))).send_keys(RANDOM_NAME))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))).click())
 
            # 001. Verify that user able to create a duplicate Catalog before create a new catalog
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name = "name"]'))).send_keys(RANDOM_NAME))
            driver.find_element(By.XPATH, xpath.saveXpath).click()
 
            assert driver.find_element(By.XPATH, '//div[contains(text(),"Saved successfully")]')
            logging.info("Verified new create catalog successfully before duplicate creation")
 
            # 002. Verify the catalog is already created
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//a[@data-qtip="Reload"]'))).click()
 
            assert WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, f'//span[contains(text(), "{RANDOM_NAME}")]')))
            logging.info("Verified user able to see already created catalog")
 
            # 003. Verify created catalog showing in catalog folder
            utils.search_by_id(driver, STORE_CATALOG_FOLDER)
            driver.find_element(By.XPATH,
                                "//a[@data-qtip='Last Page' and contains(@class, 'x-toolbar-item x-btn-plain-toolbar-small')]").click()
            logging.info("Navigated to last page")
 
            assert driver.find_element(By.XPATH, f'//div[contains(text(), "/{RANDOM_NAME}")]')
            logging.info("Verified created catalog showing in catalog folder successfully")
 
            # time.sleep(3)
            # utils.click_on_yes_no(driver)
 
            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_778_Duplicatecatalogsassignments_001_002_003",
                "Verify that user able to create a duplicate Catalog before create a new catalog and view it",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_778_Duplicatecatalogsassignments_001_002_003",
            "Verify that user able to create a duplicate Catalog before create a new catalog and view it",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_778_Duplicatecatalogsassignments_001_002_003",
            "Verify that user able to create a duplicate Catalog before create a new catalog and view it",
            e,
        )
        raise e

def test_MKTPL_778_Duplicatecatalogsassignments_004_005_006_007_008():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_778_Duplicatecatalogsassignments_004_005_006_007_008.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # utils.revert_auto_populate_auto_map_airline_categories(driver)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.catalogManagement))
            )
            driver.find_element(By.XPATH, xpath.catalogManagement).click()
            logging.info("Clicked on Catalog Management")
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "(" + xpath.credAutomationStore +")[last()]"))
            )
            # Move to Cred Automation Store
            home = driver.find_element(By.XPATH, "(" + xpath.credAutomationStore +")[last()]")
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            logging.info("Right clicked on Cred Automation Store")
            # WebDriverWait(driver, 60).until(
            #     EC.element_to_be_clickable((By.XPATH, xpath.refresh))
            # )
            # driver.find_element(By.XPATH, xpath.refresh).click()
            # logging.info("Clicked on refresh")
            # WebDriverWait(driver, 60).until(
            #     EC.element_to_be_clickable((By.XPATH, "(" + xpath.credAutomationStore +")[last()]"))
            # )
            # # Move to home icon
            # home = driver.find_element(By.XPATH, xpath.credAutomationStore)
            # actions = ActionChains(driver)
            # actions.context_click(home).perform()
            # logging.info("Right clicked on Cred Automation Store")
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            action = ActionChains(driver)
            action.move_to_element(add_object).perform()
            logging.info("Clicked on Add Object")
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, '(//span[(text()="Catalog")])[2]'))
            )
            # Click on Catalog
            elm = driver.find_element(By.XPATH, '(//span[(text()="Catalog")])[2]')
            driver.execute_script("arguments[0].click();", elm)
            logging.info("Clicked on Catalog")
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//div[text()="Enter the name of the new item"])[1]'
                                                          '//parent::div//input'))).send_keys(RANDOM_NAME))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))).click())
            # Save catalog
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name = "name"]'))).send_keys(RANDOM_NAME))
            driver.find_element(By.XPATH, xpath.saveXpath).click()

            assert driver.find_element(By.XPATH, '//div[contains(text(),"Saved successfully")]')
            logging.info("Verified new create catalog successfully before duplicate creation")
            # Close all open tabs
            close_tabs = driver.find_elements(By.XPATH, '//span[contains(@class, "x-tab-close-btn")]')
            logging.info(len(close_tabs))
            for close in close_tabs:
                close.click()

            # Window refresh 
            driver.refresh()
            time.sleep(5)

            # 004. Verify performing action and select the existing catalog assignment
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.catalogManagement))
            )
            driver.find_element(By.XPATH, xpath.catalogManagement).click()
            logging.info("Clicked on Catalog Management")
            time.sleep(2)
            # Opening Catalog elbow
            driver.find_element(By.XPATH, '//span[text()="Catalog"]//parent::div//div[contains(@class, "x-tree-elbow-plus")]').click()
            time.sleep(1)
            # Clicking on search icon and entering catalog name for search
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, '(//span[contains(text(), "Catalog")])//following-sibling::span//a[contains(@data-qtip, "Filter")]'))
            )
            driver.find_element(By.XPATH, '(//span[contains(text(), "Catalog")])//following-sibling::span//a[contains(@data-qtip, "Filter")]').click()
            time.sleep(1)
            filter_input = driver.find_element(By.XPATH, '((//span[contains(text(), "Catalog")])//following-sibling::span//a[contains(@data-qtip, \
                "Filter")])//parent::div//input[@name="filter"]')
            filter_input.send_keys(RANDOM_NAME)
            filter_input.send_keys(Keys.ENTER)
            # Copying the catalog element
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f'//span[(contains(text(), "{RANDOM_NAME}"))and (contains(@class, "x-tree-node-text"))]'))
            )
            original_catalog = driver.find_element(By.XPATH, f'//span[(contains(text(), "{RANDOM_NAME}"))and (contains(@class, "x-tree-node-text"))]')
            actions = ActionChains(driver)
            actions.context_click(original_catalog).perform()
            logging.info(f"Right clicked on {RANDOM_NAME} element")
            time.sleep(2)
            driver.find_element(By.XPATH, '//span[text()="Copy"][1]').click()
            logging.info("Clicked on Copy")
            # Pasting the copied catalog to catalog folder
            time.sleep(2)
            catalog_folder = driver.find_element(By.XPATH, '//span[text()="Catalog"]')
            actions = ActionChains(driver)
            actions.context_click(catalog_folder).perform()
            logging.info(f"Right clicked on Catalog folder")
            driver.find_element(By.XPATH, '(//span[text()="Paste"])[1]').click()
            logging.info("Clicked on Paste")
            time.sleep(2)
            # Searching Catalog for duplicate catalog
            utils.search_by_id(driver, STORE_CATALOG_FOLDER)
            driver.find_element(By.XPATH,
                                "//a[@data-qtip='Last Page' and contains(@class, 'x-toolbar-item x-btn-plain-toolbar-small')]").click()
            logging.info("Navigated to last page")
            assert driver.find_element(By.XPATH, f'//div[contains(text(), "/{RANDOM_NAME}_copy")]')
            logging.info("Verified created duplicate catalog showing in catalog folder successfully")

            # 005. Verify that user changes to the duplicate catalog
            duplicate_element_id = driver.find_element(By.XPATH, f'((//div[contains(text(), "/{RANDOM_NAME}_copy")])//parent::td//parent::tr)//td[2]//div').text
            # Window refresh 
            driver.refresh()
            time.sleep(3)
            #Close all open tabs
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[contains(@class, "x-tab-close-btn")]'))).click())

            logging.info(duplicate_element_id)
            # opening duplicate element by it's ID for updated
            utils.search_by_id(driver, duplicate_element_id)
            time.sleep(5)
            logging.info("working..")
            # updating Name field
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text() = "Base Data"]')))
            duplicate_input = driver.find_element(By.XPATH, f'//input[contains(@value, {RANDOM_NAME})]')
            duplicate_input.click()
            duplicate_input.send_keys(Keys.CONTROL + "a")
            duplicate_input.send_keys(Keys.DELETE)
            duplicate_input.send_keys(RANDOM_NAME+"_copy")
            driver.find_element(By.XPATH, xpath.saveXpath).click()
            assert driver.find_element(By.XPATH, '//div[contains(text(),"Saved successfully")]')
            logging.info("Verified user able to changes and edit value in duplicate catalog successfully")

            # 006. Verify that user edit fields to the duplicate catalog
            # passing empty name field
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text() = "Base Data"]')))
            duplicate_input = driver.find_element(By.XPATH, f'//input[contains(@value, {RANDOM_NAME})]')
            duplicate_input.click()
            duplicate_input.send_keys(Keys.CONTROL + "a")
            duplicate_input.send_keys(Keys.DELETE)
            driver.find_element(By.XPATH, xpath.saveXpath).click()
            assert driver.find_element(By.XPATH, '//div[contains(text(), "Name is Mandatory")]')
            driver.find_element(By.XPATH, '((//div[contains(text(), "Name is Mandatory")])//parent::div//parent::div//parent::div)//span[text()= "OK"]').click()
            logging.info("Verified when required fields not fill then showing error fields validation")
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//a[contains(@data-qtip, "Reload")]'))).click())

            # 007. Verify that user make a add product in duplicate catalog
            NEW_RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
            product_uid = str(utils.create_Simple_Product(deliveryMethod="onboard", productType="Duty Free", random_name=NEW_RANDOM_NAME))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text() = "Products"]'))).click())
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, '//span[contains(@class,"search")]'))))
            driver.find_element(By.XPATH,'//span[contains(@class,"search")]').click()
            time.sleep(5)
            WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH,'//input[@name="query"]')))
            query_input = driver.find_element(By.XPATH,'//input[@name="query"]')
            query_input.send_keys(product_uid)
            query_input.send_keys(Keys.ENTER)
            logging.info(f"Searching for product: {product_uid}")
            time.sleep(5)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,'//div[contains(text(),"/Cred Automation Store/Products")]')))
            x = driver.find_element(By.XPATH,'//div[contains(text(),"/Cred Automation Store/Products")]')
            action = ActionChains(driver)
            action.double_click(x).perform()
            driver.find_element(By.XPATH,'//span[text()="Select"]').click()
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveXpath)))
            driver.find_element(By.XPATH, xpath.saveXpath).click()
            # Check for the validation
            assert driver.find_element(By.XPATH, xpath.saveSuccessfullyMessageXpath)
            logging.info("Verified user able to add product in duplicate catalog successfully")
            
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text() = "Products"]'))).click())
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, '//span[contains(@class,"search")]'))))
            driver.find_element(By.XPATH,'//span[contains(@class,"search")]').click()
            time.sleep(5)
            WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH,'//input[@name="query"]')))
            query_input = driver.find_element(By.XPATH,'//input[@name="query"]')
            query_input.send_keys(DEDICATED_PRODUCT_9_ID)
            query_input.send_keys(Keys.ENTER)
            logging.info(f"Searching for product: {DEDICATED_PRODUCT_9_ID}")
            time.sleep(5)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,'//div[contains(text(),"/Cred Automation Store/Products")]')))
            x = driver.find_element(By.XPATH,'//div[contains(text(),"/Cred Automation Store/Products")]')
            action = ActionChains(driver)
            action.double_click(x).perform()
            driver.find_element(By.XPATH,'//span[text()="Select"]').click()
            
            # 008. Verify that user make a Remove product in duplicate catalog
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text() = "Products"]'))).click())
            # //img[@data-qtip="Remove"]
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, f'//div[text()="{product_uid}"]'))))
            logging.info(f"Product found: {product_uid}")
            driver.find_element(By.XPATH, f'//div[text()="{product_uid}"]//ancestor::tr//img[@data-qtip="Remove"]').click()
            time.sleep(5)
            WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH,'//span[text()="Yes"]')))
            driver.find_element(By.XPATH,'//span[text()="Yes"]').click()
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveXpath)))
            driver.find_element(By.XPATH, xpath.saveXpath).click()
            # Check for the validation
            assert driver.find_element(By.XPATH, xpath.saveSuccessfullyMessageXpath)
            logging.info("Verified User able to Remove product in duplicate catalog successfully")

            try:
                product_id = "ID " + product_uid
                RT_VALUE = utils.Delete(product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            
            # time.sleep(3)
            # utils.click_on_yes_no(driver)

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_778_Duplicatecatalogsassignments_004_005_006_007_008",
                "Verify performing action and select the existing catalog assignment to duplicate catalog",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_778_Duplicatecatalogsassignments_004_005_006_007_008",
            "Verify performing action and select the existing catalog assignment to duplicate catalog",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_778_Duplicatecatalogsassignments_004_005_006_007_008",
            "Verify performing action and select the existing catalog assignment to duplicate catalog",
            e,
        )
        raise e

def test_MKTPL_2707_Storeprofile_001():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2707_Storeprofile_001.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            # Open store object
            utils.search_by_id(driver, DEDICATED_STORE_ID)

            # # 001. "Verify the Domain, Permanent Domain, Types, Vendors, Url, Password Message, Currency, Enabled Currencies, \
            # #           Enabled Payment Types, Categories Count, Money Format, Money with currency format, Metafields, Policies, \
            # #           Privacy Policy, Refund Policy, Shipping Policy, Subscription Policy, Terms of Service fields in the store object, \
            # #           GET Store API and store_publish SNS",

            # Update store
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.airlineProductCatDescriptionXpath))))
            driver.find_element(By.XPATH,xpath.airlineProductCatDescriptionXpath).send_keys("_")

            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
            save_and_publish.click()

            # check_list = ["Domain", "Permanent Domain", "Types", "Vendors", "Url", "Password Message", "Currency", "Enabled Currencies", 
            #     "Enabled Payment Types", "Categories Count", "Money Format", "Money with currency format", "Metafields", "Policies", 
            #         "Privacy Policy", "Refund Policy", "Shipping Policy", "Subscription Policy", "Terms of Service"
            # ]

            check_list = ["domain", "permanentDomain", "types", "vendors", "url", "passwordMessage", "currency", "enabledCurrencies", 
                "enabledPaymentTypes", "categoriesCount", "moneyFormat", "moneyWithCurrencyFormat", "metafields", "policies", 
                    "privacyPolicy", "refundPolicy", "shippingPolicy", "subscriptionPolicy", "termsOfService"
            ]

            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("GET", STORE_GET_URL,
                                        headers=headers)
            data = response.json()
            assert len([x for x in check_list if x in data["data"]["store"].keys()]) == 0
            logging.info("Verified Domain, Permanent Domain, Types, Vendors, Url, Password Message, Currency, Enabled Currencies, Enabled Payment Types, \
                Categories Count, Money Format, Money with currency format, Metafields, Policies, Privacy Policy, Refund Policy, Shipping Policy, \
                    Subscription Policy, Terms of Service fields are invisible in API")

            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'store_publish')]")
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]")))
            # Click on the last created entry
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f"(//span[contains(text(),'{DEDICATED_STORE}')])[1]")))
            time.sleep(5)
            sns_keys = [keys.text.replace('"','') for keys in driver.find_elements(By.XPATH, '(//div[@class="ace_line"]//span[@class="ace_variable"])')] 
            logging.info(sns_keys)
            assert len([x for x in check_list if x in sns_keys]) == 0
            logging.info("Verified Domain, Permanent Domain, Types, Vendors, Url, Password Message, Currency, Enabled Currencies, Enabled Payment Types, \
                Categories Count, Money Format, Money with currency format, Metafields, Policies, Privacy Policy, Refund Policy, Shipping Policy, \
                    Subscription Policy, Terms of Service fields are invisible in Triggered SNS")

            # time.sleep(3)
            # utils.click_on_yes_no(driver)

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_2707_Storeprofile_001",
                "Verify the Domain, Permanent Domain, Types, Vendors, Url, Password Message, Currency, Enabled Currencies, \
                    Enabled Payment Types, Categories Count, Money Format, Money with currency format, Metafields, Policies, \
                    Privacy Policy, Refund Policy, Shipping Policy, Subscription Policy, Terms of Service fields in the store object, \
                    GET Store API and store_publish SNS",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2707_Storeprofile_001",
            "Verify the Domain, Permanent Domain, Types, Vendors, Url, Password Message, Currency, Enabled Currencies, \
                Enabled Payment Types, Categories Count, Money Format, Money with currency format, Metafields, Policies, \
                Privacy Policy, Refund Policy, Shipping Policy, Subscription Policy, Terms of Service fields in the store object, \
                GET Store API and store_publish SNS",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2707_Storeprofile_001",
            "Verify the Domain, Permanent Domain, Types, Vendors, Url, Password Message, Currency, Enabled Currencies, \
                Enabled Payment Types, Categories Count, Money Format, Money with currency format, Metafields, Policies, \
                Privacy Policy, Refund Policy, Shipping Policy, Subscription Policy, Terms of Service fields in the store object, \
                GET Store API and store_publish SNS",
            e,
        )
        raise e

def test_MKTPL_3237_Disassociatemultiplecategories_001_002_003():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
        product_id = str(utils.create_Simple_Product(deliveryMethod="Onboard", productType="Duty Free", random_name=RANDOM_NAME))
        logging.info(product_id)
       
        # 001: Verify if user remove the category and it's variant theme is associated in parent configurable product
        put_payload = json.dumps({
            "sku": RANDOM_NAME,
            "name": RANDOM_NAME,
 
            "deliveryMethod": [
                "Onboard"
            ],
 
            "category": [
            ],
            "productSet": "Simple",
            "productType": "Duty Free"
 
        })
        headers = {
            'Authorization': CRED_AUTOMATION_STORE_TOKEN,
            'Content-Type': 'application/json'
        }
        put_response = requests.request("PUT", PRODUCTS_URL + product_id, headers=headers, data=put_payload)
        put_resp_data = put_response.json()
        logging.info(put_resp_data)
        assert put_response.status_code == 400 or put_response.status_code == 401
        # assert "Invalid request: Mandatory field: category" in put_resp_data["message"]
        assert "category" in put_resp_data["message"]
        logging.info("Verified validation message should display in Put/Patch API")
       
        # 002: Verify if user add the category while creating variant product and it does not matches with category of parent configurable product
        VARIANT_RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
        variant_post_payload = json.dumps({
            "sku": VARIANT_RANDOM_NAME,
            "name": VARIANT_RANDOM_NAME,
 
            "deliveryMethod": [
                "Onboard"
            ],
            "parentProduct": int(DEDICATED_VARIENT_PARENT_PRODUCT_ID),
            "category": [
                int(DEDICATED_CATEGORY_4_ID)
            ],
            "productSet": "Configurable",
            "productType": "Duty Free"
 
        })
        variant_post_response = requests.request("POST", PRODUCTS_URL, headers=headers, data=variant_post_payload)
        variant_post_resp_data = variant_post_response.json()
        # print(variant_post_resp_data)
        logging.info(variant_post_resp_data)
        assert variant_post_response.status_code == 400 or variant_post_response.status_code == 401
        assert "Categories Cannot be Modified in Variants" in variant_post_resp_data["message"]
        logging.info("Verified validation message should display in Post API")
       
        # 003: Verify if user update the category while updating variant product and it does not matches with category of parent configurable product
        VARIANT_RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
        variant_post_payload = utils.get_Product_Payload(
            RANDOM_NAME= VARIANT_RANDOM_NAME,
            parentProduct= int(DEDICATED_VARIENT_PARENT_PRODUCT_ID),
            deliveryMethod= "Onboard",
            category= int(DEDICATED_CATEGORY_1_ID),
            productSet= "Configurable",
            productType= "Duty Free",
            PriceTaxation_price_value= 0,
            PriceTaxation_special_price_value= 0,
            PriceTaxation_cost_value= 0,
            productCategoryAttributesKey= "RAM",
            productCategoryAttributesValue= "8GB",
            isVariantAttribute= True,
        )
        variant_post_response = requests.request("POST", PRODUCTS_URL, headers=headers, data=variant_post_payload)
        variant_post_resp_data = variant_post_response.json()
        logging.info(variant_post_resp_data)
        variant_product_id = str(variant_post_resp_data["id"])
        logging.info(variant_product_id)
        variant_put_payload = json.loads(variant_post_payload)
        variant_put_payload["category"] = [int(DEDICATED_CATEGORY_4_ID)]
        variant_put_payload = json.dumps(variant_put_payload)
        headers = {
            'Authorization': CRED_AUTOMATION_STORE_TOKEN,
            'Content-Type': 'application/json'
        }
        variant_put_response = requests.request("PUT", PRODUCTS_URL + variant_product_id, headers=headers, data=variant_put_payload)
        variant_put_resp_data = variant_put_response.json()
        assert variant_put_response.status_code == 400 or variant_put_response.status_code == 401
        assert "Categories Cannot be Modified in Variants" in variant_put_resp_data["message"]
        logging.info("Verified validation message should display in Put/Patch API")
       
        try:
            variant_product_id = "ID " + variant_product_id
            RT_VALUE = utils.Delete(variant_product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
            assert RT_VALUE == 200, RT_VALUE
            logging.info(f"Data cleaned up successfully.")
        except Exception as e:
            logging.info(f"Error in cleaning up the data.")
       
        try:
            product_id = "ID " + product_id
            RT_VALUE = utils.Delete(product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
            assert RT_VALUE == 200, RT_VALUE
            logging.info(f"Data cleaned up successfully.")
        except Exception as e:
            logging.info(f"Error in cleaning up the data.")
 
        Success_List_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories_001_002_003",
            "Verify if user remove the category and it's variant theme is associated in parent configurable product",
            "Pass",
        )
 
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3237_Disassociatemultiplecategories_001_002_003",
                            "Verify if user remove the category and it's variant theme is associated in parent configurable product", "Fail")
        Failure_Cause_Append("test_MKTPL_3237_Disassociatemultiplecategories_001_002_003",
                             "Verify if user remove the category and it's variant theme is associated in parent configurable product", e)
        raise e

def test_MKTPL_3237_Disassociatemultiplecategories_004():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_3237_Disassociatemultiplecategories_004.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_VARIENT_PARENT_PRODUCT_ID)
            
            # 004. Verify if user remove the category and it's variant theme is associated in parent configurable product
            # remove category
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, f'//div[contains(text(), "{DEDICATED_CATEGORY_1_NAME}")]'))))
            driver.find_element(By.XPATH, f'(//div[contains(text(), "{DEDICATED_CATEGORY_1_NAME}")])//following-sibling::div').click()
            logging.info("Clicked on 'X' icon on Category")
            
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath))).click())
            logging.info("Clicked on Saved and Published")
            error_message = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[contains(text(), "Validation failed: Empty mandatory field [ category ] fieldname=category")]')))
            #  driver.find_element(By.XPATH, '//div[contains(text(), "Validation failed: Empty mandatory field [ category ] fieldname=category")]')
            assert error_message
            logging.info("Verified validation message displayed in Product object")
            
            # Window refresh 
            driver.refresh()
            time.sleep(5)
            # time.sleep(3)
            # utils.click_on_yes_no(driver)

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_3237_Disassociatemultiplecategories_004",
                "Verify if user remove the category and it's variant theme is associated in parent configurable product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories_004",
            "Verify if user remove the category and it's variant theme is associated in parent configurable product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories_004",
            "Verify if user remove the category and it's variant theme is associated in parent configurable product",
            e,
        )
        raise e

def test_MKTPL_3237_Disassociatemultiplecategories__005():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
        get_response = requests.request("GET", PRODUCTS_URL + DEDICATED_VARIENT_PARENT_PRODUCT_ID + "/variant", headers=headers)
        get_resp_data = get_response.json()
        # pick random id from response list
        variant_object_id = str(random.choice(get_resp_data["data"]["product_variants"])["id"])
        logging.info(f"Variant object id: {variant_object_id}")
        with utils.services_context_wrapper(
            "test_MKTPL_3237_Disassociatemultiplecategories__005.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, variant_object_id)
            
            # 005. Verify the category field in Variant object
            # search category
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, '(//label//..//span[contains(text(),"Category ")])//parent::span\
                                                 //parent::label//parent::div//following-sibling::div//div//div//div'))))
            category = driver.find_element(By.XPATH, '(//label//..//span[contains(text(),"Category ")])//parent::span\
                //parent::label//parent::div//following-sibling::div//div//div//div')
            logging.info(category.get_attribute("aria-disabled"))
            category_read_only = category.get_attribute("aria-disabled")
            assert category_read_only == "true"
            logging.info("Verify category dropdown is read only in Variant Objects")

            # time.sleep(3)
            # utils.click_on_yes_no(driver)

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_3237_Disassociatemultiplecategories__005",
                "Verify the category field in Variant object",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories__005",
            "Verify the category field in Variant object",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories__005",
            "Verify the category field in Variant object",
            e,
        )
        raise e

def test_MKTPL_2164_SNS_001():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2164_SNS_001.png"
        ) as driver:
            # Login through PAC admin user
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            ## Create new Store object
            utils.click_on_add_object(driver,"Stores")
            # Enter store name
            store_name = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(10))
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(store_name)
            # Click on ok
            ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
            ok.click()
            logging.info(f"Clicked on ok after entering Store name as {store_name}")
            # input store name
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//input[@name='storeName']")))
            driver.find_element(By.XPATH, "//input[@name='storeName']").send_keys(store_name)
            # Click on address info
            address_information = driver.find_element(By.XPATH, "//span[contains(text(),'Address Information')]")
            address_information.click()
            # Enter first name
            driver.find_element(By.XPATH, "//input[@name='firstName']").send_keys("First Name - Dummy")
            logging.info("Entered first name")
            # Enter email
            driver.find_element(By.XPATH, "//input[@name='email']").send_keys("<EMAIL>")
            # Click on Configuration
            configuration = driver.find_element(By.XPATH, "(//span[contains(text(),'Configuration')])[1]")
            configuration.click()
            # Enter into manage stock and sales strategy
            driver.find_element(By.XPATH, "//input[@name='manageStock']").send_keys("Yes", Keys.ENTER)
            driver.find_element(By.XPATH, "//input[@name='salesStrategy']").send_keys("D2C", Keys.ENTER)
            # Get UID
            UID = str(utils.get_uid(driver))
            logging.info(f"UID: {UID}")
            UID = UID.split()[1]
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
            save_and_publish.click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
            
            ## Associate Store to Airline: Cred Automation Airline
            # Close all tabs
            utils.close_All_Tabs(driver)
            # search for Airline by ID
            utils.search_by_id(driver, DEDICATED_AIRLINE_ID)
            # go to stores tab
            driver.find_element(By.XPATH,'//span[contains(text(),"Stores")]').click()
            # click on search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//b[contains(text(),"Associated Stores")]//parent::div//following-sibling::a[3])//span//span')))
            driver.find_element(By.XPATH,'(//b[contains(text(),"Associated Stores")]//parent::div//following-sibling::a[3])//span//span').click()
            # query input tag in Store search
            query_input = driver.find_element(By.XPATH, xpath.queryXpath)
            query_input.send_keys(UID)
            query_input.send_keys(Keys.ENTER)
            store_row = driver.find_element(By.XPATH, f'//div[text()={UID}]')
            actions = ActionChains(driver)
            actions.context_click(store_row).perform()
            # add store to "Your Selection"
            driver.find_element(By.XPATH, '//span[text()="Add"]').click()
            # click on select
            driver.find_element(By.XPATH, '//span[text()="Select"]').click()
            logging.info("Store selected")
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
            save_and_publish.click()
            logging.info("Airline saved")
            
            ## UPDATING THE STORE
            # updates the log file with Associated Airlines
            # close all tabs
            utils.close_All_Tabs(driver)
            # search for new created store by ID
            utils.search_by_id(driver, UID)
            # Enter description
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[@name='description']")))
            description = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(20))
            driver.find_element(By.XPATH, "//textarea[@name='description']").clear()
            driver.find_element(By.XPATH, "//textarea[@name='description']").send_keys(
                f"Description for {store_name}: {description}")
            logging.info(f"Description added as {description}")
            time.sleep(5)
            # click on Save & Publish
            save_and_publish_after_edit = driver.find_element(By.XPATH,
                                                              "(//span[contains(text(),'Save & Publish')]//parent::span)[1]")
            driver.execute_script("arguments[0].click();", save_and_publish_after_edit)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
            
            ## Verify SNS
            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'store_publish')]")
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]")))
            # Click on the last created entry
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]")))
            time.sleep(5)
            assert driver.find_element(By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]").is_displayed()
            logging.info(f"Asserted the store name as {store_name}")

            assert driver.find_element(By.XPATH, f"//span[contains(text(),'{UID}')]").is_displayed()
            logging.info(f"Asserted the UID as {UID}")
            
            assert driver.find_element(By.XPATH, "//span[contains(text(),'\"associatedAirlines\"')]").is_displayed()
            logging.info("Asserted the Associated Airline Parameter")
            
            assert driver.find_element(By.XPATH, "//span[contains(text(),'\"icaoCode\"')]").is_displayed()
            logging.info("Asserted the ICAO Code label")
            
            icao_code = "CRE"
            assert driver.find_element(By.XPATH, f"//span[contains(text(),'\"{icao_code}\"')]").is_displayed()
            logging.info(f"Asserted the ICAO Code as {icao_code}")
            
            # time.sleep(3)
            # utils.click_on_yes_no(driver)

            time.sleep(10)
            Success_List_Append(
                "test_MKTPL_2164_SNS_001",
                "Verify the Store SNS Payload",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2164_SNS_001",
            "Verify the Store SNS Payload",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2164_SNS_001",
            "Verify the Store SNS Payload",
            e,
        )
        raise e

def test_MKTPL_2131_Product_001():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2131_Product_001.png"
        ) as driver:
            # Login through PAC admin user
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            ## Search for product
            utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Category "]')))
            # Determining category
            assert driver.find_element(By.XPATH, '(//span[text()="Category "]//ancestor::label)//following-sibling::div//div//span')
            logging.info("Verified category is associated with product")
            category_text = driver.find_element(By.XPATH, '(//span[text()="Category "]//ancestor::label)//following-sibling::div//div//span').text
            logging.info(f"Category assigned: {category_text}")
            category = category_text.split("(")[1].split(")")[0]
            category_path = "/Master Data/Category/" + category
            logging.info(f"Category path: {category_path}")
            
            # close all tabs
            utils.close_All_Tabs(driver)
            
            ## Open the category object for editing
            utils.search_by_path(driver, category_path)
            
            # 001. Verify if category is associated with product and user try to remove the attribute set
            # Navigate to Attributes tab in the category object
            logging.info("1")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Attributes"]')))
            driver.find_element(By.XPATH, '//span[text()="Attributes"]').click()
            logging.info("Attributes tab opened")
            # Attribute set available
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[contains(text(), "Attribute Set")]')))
            # Removing Attribute set
            driver.find_element(By.XPATH, '(//span[contains(text(), "Attribute Set")]//ancestor::label)//following-sibling::div//div//div//div//ul//li//div[2]').click()
            logging.info("Clicked in attempt to remove Attribute Set")
            assert driver.find_element(By.XPATH, '//div[contains(text(), "Existing Attribute Sets Cannot Be Removed")]')
            logging.info("Verified it does not allow users to remove the attribute set and validation message should displays")


            time.sleep(10)
            Success_List_Append(
                "test_MKTPL_2131_Product_001",
                "Verify if category is associated with product and user try to remove the attribute set",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2131_Product_001",
            "Verify if category is associated with product and user try to remove the attribute set",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2131_Product_001",
            "Verify if category is associated with product and user try to remove the attribute set",
            e,
        )
        raise e

def test_MKTPL_2131_Product_005():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    payload = json.dumps({
        "sku": RANDOM_NAME,
        "name": RANDOM_NAME,

        "deliveryMethod": [
            "onboard"
        ],

        "category": [
            DEDICATED_CATEGORY_4_ID
        ],
        "productSet": "Simple",
        "productType": "Duty Free"

    })
    response = requests.request("POST", PRODUCTS_URL, headers={'Authorization': CRED_AUTOMATION_STORE_TOKEN},
                                data=payload)
    resp_data = response.json()
    logging.info(resp_data)
    assert response.status_code == 200
    logging.info(
        f"POST API for Product responded with 200 status code")
    resp_data = response.json()
    assert "Product added successfully. " in resp_data["message"]
    product_id = str(resp_data["id"])
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2131_Product_005.png"
        ) as driver:
            # Login through PAC admin user
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            ## Search for product
            utils.search_by_id(driver, product_id)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Category "]')))
            # Checking Category Specific Attributes
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Category Specific Attributes"]')))
            driver.find_element(By.XPATH, '//span[text()="Category Specific Attributes"]').click()
            time.sleep(3)
            
            # close all tabs
            utils.close_All_Tabs(driver)
            
            ## Open the category object for editing
            utils.search_by_id(driver, DEDICATED_CATEGORY_4_ID)
            
            # 005. Verify When a new attribute Set is added to existing Category
            # Navigate to Attributes tab in the category object
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Attributes"]')))
            driver.find_element(By.XPATH, '//span[text()="Attributes"]').click()
            logging.info("Attributes tab opened")
            # Attribute set available
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[contains(text(), "Attribute Set")]')))
            # determine the id of Attribute Set
            element_id = driver.find_element(By.XPATH, '//span[contains(text(), "Attribute Set")]').get_attribute("id")
            # determine tag field id
            tagfield_id = element_id.split("-")[0]+ "-" + element_id.split("-")[1]
            # determine trigger picker id
            tagfield_id_trigger_picker = tagfield_id + "-trigger-picker"
            # determine picker list
            tagfield_id_picker_list = tagfield_id + "-picker-listEl"
            # clicking attibute set drop down icon
            driver.find_element(By.XPATH, f'//div[@id="{tagfield_id_trigger_picker}"]').click()
            time.sleep(2)
            # fetching data of picker list that are not selected
            list_items = driver.find_elements(By.XPATH, f'//ul[@id="{tagfield_id_picker_list}"]//li[@class="x-boundlist-item"]')
            # randomly selecting any one attribute set from list
            random_choice = random.choice(list_items)
            random_choice.click()
            # determine attribute set random element name
            random_choice_text = random_choice.text
            logging.info(f"Attribute selected: {random_choice_text}")
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
            save_and_publish.click()
            logging.info("Category saved")

            # close all tabs
            utils.close_All_Tabs(driver)
            
            ## Open the product for validation
            utils.search_by_id(driver, product_id)
            # move to Category Specific Attributes
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Category Specific Attributes"]')))
            driver.find_element(By.XPATH, '//span[text()="Category Specific Attributes"]').click()
            logging.info("Category Specific Attributes tab opened")
            assert driver.find_element(By.XPATH, f'//div[text()="{random_choice_text}"]')
            logging.info("Verified the new attributes of the attribute set are populated in products that are associated with that category")

            time.sleep(10)
            Success_List_Append(
                "test_MKTPL_2131_Product_005",
                "Verify When a new attribute Set is added to existing Category",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2131_Product_005",
            "Verify When a new attribute Set is added to existing Category",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2131_Product_005",
            "Verify When a new attribute Set is added to existing Category",
            e,
        )
        raise e

def test_MKTPL_2501_Currency_001():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2501_Currency_001.png"
        ) as driver:
            updated_currency = "JPY"
            # Login through PAC admin user
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            ## Search for product
            utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Pricing & Taxation"]')))
            driver.find_element(By.XPATH, '//span[text()="Pricing & Taxation"]').click()
            logging.info("Pricing & Taxation tab opened")
            # Determining currency
            default_currency = driver.find_element(By.XPATH, '(//span[text()="Price:"]//ancestor::fieldset)[2]//span//span//span[2]').text
            logging.info(f"Current currency is {default_currency}")

            ## Open the store object for editing
            utils.search_by_id(driver, DEDICATED_STORE_ID)

            # 001. Verify the price, special price and cost fields in Product
            # Navigate to Configuration tab in the store object
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.configuration_tab)))
            driver.find_element(By.XPATH, xpath.configuration_tab).click()
            logging.info("Configuration tab opened")
            # Default Currency available
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[contains(text(), "Default Currency ")]')))
            # determine the id of Attribute Set
            element_id = driver.find_element(By.XPATH, '//span[contains(text(), "Default Currency ")]').get_attribute("id")
            # determine tag field id
            tagfield_id = element_id.split("-")[0]+ "-" + element_id.split("-")[1]
            # determine trigger picker id
            tagfield_id_trigger_picker = tagfield_id + "-trigger-picker"
            # determine picker list
            tagfield_id_picker_list = tagfield_id + "-picker-listEl"
            # clicking default currency drop down icon
            driver.find_element(By.XPATH, f'//div[@id="{tagfield_id_trigger_picker}"]').click()
            time.sleep(2)
            # fetching data of picker list that are not selected
            element = driver.find_element(By.XPATH, f'//ul[@id="{tagfield_id_picker_list}"]//li[contains(text(), "{updated_currency}")]')
            actions = ActionChains(driver)
            actions.move_to_element(element).perform()
            element.click()
            logging.info(f"Currency updated as {updated_currency}")
            
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="Save & Publish"])[2]')))
            save_and_publish = driver.find_element(By.XPATH, '(//span[text()="Save & Publish"])[2]')
            save_and_publish.click()
                        
            ## Search for product
            utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '(//span[contains(@class, "reload")])[1]')))
            driver.find_element(By.XPATH, '(//span[contains(@class, "reload")])[1]').click()
            
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Pricing & Taxation"]')))
            driver.find_element(By.XPATH, '//span[text()="Pricing & Taxation"]').click()
            logging.info("Pricing & Taxation tab opened")
            # Determining currency
            assert driver.find_element(By.XPATH, f'//span[contains(text(), "{updated_currency}")]')
            logging.info(f"Current currency is {updated_currency}")
            logging.info("Verified PAC/Store users able to select JPY as currency in price, special price and cost fields in product")
            
            ## Open the store object for editing
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '(//span[contains(@class, "reload")])[2]')))
            driver.find_element(By.XPATH, '(//span[contains(@class, "reload")])[2]').click()
            
            # Navigate to Configuration tab in the store object
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.configuration_tab)))
            driver.find_element(By.XPATH, xpath.configuration_tab).click()
            logging.info("Configuration tab opened")
            # Default Currency available
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[contains(text(), "Default Currency ")]')))
            # determine the id of Attribute Set
            element_id = driver.find_element(By.XPATH, '//span[contains(text(), "Default Currency ")]').get_attribute("id")
            # determine tag field id
            tagfield_id = element_id.split("-")[0]+ "-" + element_id.split("-")[1]
            # determine trigger picker id
            tagfield_id_trigger_picker = tagfield_id + "-trigger-picker"
            # determine picker list
            tagfield_id_picker_list = tagfield_id + "-picker-listEl"
            # clicking default currency drop down icon
            driver.find_element(By.XPATH, f'//div[@id="{tagfield_id_trigger_picker}"]').click()
            time.sleep(2)
            # fetching data of picker list that are not selected
            element = driver.find_element(By.XPATH, f'//ul[@id="{tagfield_id_picker_list}"]//li[contains(text(), "{default_currency}")]')
            actions = ActionChains(driver)
            actions.move_to_element(element).perform()
            element.click()
            logging.info(f"Currency updated as {default_currency}")
            
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="Save & Publish"])[2]')))
            save_and_publish = driver.find_element(By.XPATH, '(//span[text()="Save & Publish"])[2]')
            save_and_publish.click()

            time.sleep(10)
            Success_List_Append(
                "test_MKTPL_2501_Currency_001",
                "Verify the price, special price and cost fields in Product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2501_Currency_001",
            "Verify the price, special price and cost fields in Product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2501_Currency_001",
            "Verify the price, special price and cost fields in Product",
            e,
        )
        raise e

def test_MKTPL_2013_DeploymentInventory_001_002():

    try:
        with utils.services_context_wrapper("test_MKTPL_2013_DeploymentInventory_001_002.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH,
                                                xpath.configurationTabXpath)))).click()
            value = driver.find_element(By.XPATH, xpath.manageStockXpath).get_attribute("value")
            logging.info(value)
            manageStock = False

            if  value == "Yes" :
                logging.info("The MAnage Stock is - YES so change to NO")
                inputField = driver.find_element(By.XPATH, xpath.manageStockXpath)
                inputField.clear()
                inputField.send_keys("No", Keys.TAB)
                time.sleep(3)
                try:
                    if driver.find_element(By.XPATH, '//div[text()="WARNING"]/../../../../..//span[text()="Yes"]').is_displayed():
                        driver.find_element(By.XPATH, '//div[text()="WARNING"]/../../../../..//span[text()="Yes"]').click()
                except:
                    logging.info("Modal is not shown, store not opened on any other device")
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
                save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
                save_and_publish.click()
                # Check for the validation
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
                manageStock = True

            else:
                logging.info("The store is in Manage Stock - NO")
                manageStock = True
            # -------- MKTPL-2013-DeploymentInventory-002 - API - ------
            # Verify if manage stock is No in Store Config and user try to update deployment inventory via API
            payload = json.dumps({
                "catalog": {
                    "id": DEDICATED_CATALOG_3_ID,
                    "products": [
                        {
                            "productId": DEDICATED_SKU_1_ID,
                            "productQuantity": 123
                        }
                    ]
                }
            })
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("PUT", ROUTE_CATALOG + DEDICATED_CATALOG_ASSIGNMENT_6_ID + "/deploymentInventory", headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Deployment Inventory cannot be updated as Store has Manage Stock set as No." in resp_data['message']
            logging.info("002. API - API display validation message ")

            # --------- opened the catalog assignment ------
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_6_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productsTabXpath))).click()
            dropdown = WebDriverWait(driver, 60).until( EC.visibility_of_element_located(
                    (By.ID, "options"+str(DEDICATED_CATALOG_ASSIGNMENT_6_ID)+"-trigger-picker")))
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            try:
                driver.find_element(By.XPATH, "//li[contains(text(),'Update Deployment Inventory')]").is_displayed()
                driver.find_element(By.XPATH, "//li[contains(text(),'Export Deployment Inventory CSV')]").is_displayed()
                driver.find_element(By.XPATH, "//li[contains(text(),'Import Deployment Inventory CSV')]").is_displayed()
                assert False

            except Exception as e:
                pass
                logging.info("Following options should be hidden from Choose Action to Perform dropdown :"
                             "- Update Deployment Inventory"
                             "- Export Deployment Inventory CSV"
                             "- Import Deployment Inventory CSV")
                utils.close_All_Tabs(driver)

            # --------- opened the store profile ---------
            if manageStock:
                utils.search_by_id(driver, DEDICATED_STORE_ID)
                logging.info("STORE OPENED")
                (WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, xpath.configurationTabXpath)))).click()
                logging.info("CLICKED AT CONFIGURATION")
                inputField = driver.find_element(By.XPATH, xpath.manageStockXpath)
                inputField.clear()
                inputField.send_keys("Yes", Keys.TAB)
                time.sleep(3)
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
                save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
                save_and_publish.click()
                logging.info("Manage Stock is changed to Yes")

            else:
                logging.info("Manage Stock is Yes")

            Success_List_Append(
                "test_MKTPL_2013_DeploymentInventory_001_002",
                "Verify if manage stock is No in Store Config",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_2013_DeploymentInventory_001_002",
            "Verify if manage stock is No in Store Config",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2013_DeploymentInventory_001_002",
            "Verify if manage stock is No in Store Config",
            e,
        )
        raise e

def test_MKTPL_339_Storeprofile_001_002_004():
    try:
        with utils.services_context_wrapper("test_MKTPL_339_Storeprofile_001_002_004.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            # 002
            utils.search_by_id(driver, DEDICATED_STORE_ID)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'storeName')))
            store_name = driver.find_element(By.NAME, 'storeName')
            store_name.clear()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Validation failed: Empty mandatory field [ storeName ][ localizedfields-en ]"]'))).is_displayed()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//span[text()="OK"])[2]')))
            driver.find_element(By.XPATH, '(//span[text()="OK"])[2]').click()
            logging.info("002 - Validation message should display")

            # 001 # 004
            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Store Name "]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Description:"]'))).is_displayed()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Address Information"]')))
            driver.find_element(By.XPATH, '//span[text()="Address Information"]').click()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="First Name "]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Last Name:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Address Line 1:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Address Line 2:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="City:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="State:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Zip Code:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Country:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Email "]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Phone:"]'))).is_displayed()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.configuration_tab)))
            driver.find_element(By.XPATH, xpath.configuration_tab).click()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Auto Approve Catalog:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Manage Stock "]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Manage Shipping "]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Sales Strategy "]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Unit Of Measure "]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Tax Percentage:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Order Threshold Value:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Order Provider:"]'))).is_displayed()
            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Client ID "]'))).is_displayed()
            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Client Secret "]'))).is_displayed()
            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Select Languages:"]'))).is_displayed()
            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Display Currencies:"]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Default Currency "]'))).is_displayed()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Payment Configuration"]')))
            driver.find_element(By.XPATH, '//span[text()="Payment Configuration"]').click()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[contains(text(),"Bypass PAC Payment")]'))).is_displayed()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Associated Airline"]')))
            driver.find_element(By.XPATH, '//span[text()="Associated Airline"]').click()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//div[text()="Associated Airline "]'))).is_displayed()

            logging.info("001 - Verify all fields displaying in store profile screen in successfully")
            logging.info("004 - User should be able to view the information of store profile")

            Success_List_Append(
                " test_MKTPL_339_Storeprofile_001_002_004",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_339_Storeprofile_001_002_004",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_339_Storeprofile_001_002_004",
                             "Verify the active catalog assignments associated", e)
        raise e
    
def test_MKTPL_941_Categoryimport_015():
    try:
        with utils.services_context_wrapper("test_MKTPL_941_Categoryimport_015.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Category")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            path = os.getcwd() + "/credencys_test_ui/" + "Category_field_blank_Sample.csv"
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()

            ele = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'CSV Uploaded Successfully.')]"))).is_displayed()
            assert ele
            logging.info("File uploaded successfully or showing invalid columns with remove column")

            Success_List_Append(
                " test_MKTPL_941_Categoryimport_015",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_941_Categoryimport_015",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_941_Categoryimport_015",
                             "Verify the active catalog assignments associated", e)
        raise e
    
def test_MKTPL_3704_Newfromtodate_002_003():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_3704_Newfromtodate_002_003.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
        
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()
        
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys(
                "" + DEDICATED_CATEGORY_1_NAME + " (" + DEDICATED_STORE + "/" + DEDICATED_CATEGORY_1_NAME + ")")
            category.send_keys(Keys.ENTER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.downloadSampleFileProducts)))
            driver.find_element(By.XPATH, xpath.downloadSampleFileProducts).click()

            os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE)
            time.sleep(2)
            filelist = []
            last_file_time = 0
            for current_file in os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE):
                filelist.append(current_file)
                current_file_fullpath = os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, current_file)
                current_file_time = os.path.getctime(current_file_fullpath)
                if os.path.isfile(current_file_fullpath):
                    if last_file_time == 0:
                        last_file = current_file
                    last_file_time = os.path.getctime(
                        os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file))
                    if current_file_time > last_file_time and os.path.isfile(current_file_fullpath):
                        last_file = current_file
                last_file_fullpath = os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file)
            logging.info(last_file_fullpath)

            with open(
                    last_file_fullpath, "r", encoding='utf-8-sig'
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            date_text_from = ACTUAL_CSV_DATA[1][20]
            date_text_to = ACTUAL_CSV_DATA[1][21]
            try:
                datetime.date.fromisoformat(date_text_from)
            except ValueError:
                raise ValueError("Incorrect data format - FROM, should be YYYY-MM-DD")
            try:
                datetime.date.fromisoformat(date_text_to)
            except ValueError:
                raise ValueError("Incorrect data format - TO, should be YYYY-MM-DD")
            logging.info("Time should be removed from newFrom & newTo fields from CSV Sample file of product Format should be YYYY-MM-DD in newFrom & newTo fields in CSV Sample file of product")

            utils.update_csv("Product_Sample.csv", "newFrom", "2027-06-16")
            utils.update_csv("Product_Sample.csv", "newTo", "2027-06-20")
            path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"

            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            utils.Assets(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'product_publish')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[@class="ace_constant ace_numeric"])[3]'))).is_displayed()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[@class="ace_constant ace_numeric"])[4]'))).is_displayed()
            logging.info("newFrom & newTo parameters should be in UTC epoch timestamp in product_publish SNS")

            Success_List_Append(
                " test_MKTPL_3704_Newfromtodate_002_003",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_3704_Newfromtodate_002_003",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_3704_Newfromtodate_002_003",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_692_StoreAPI_002_003_005():
    try:
        with utils.services_context_wrapper("test_MKTPL_692_StoreAPI_002_003_005.png") as driver:
            # 002 Verify the logo parameter
            payload = {}
            response = requests.request("GET", STORE_GET_URL, headers={
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logo_url = resp_data["data"]["store"]["logo"]
            logging.info(logo_url)
            #  verify image url
            response = requests.request("GET", logo_url, headers={}, data={})
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"002. API should display full and correct URL including file extension")
            # 003. Verify the country parameter
            payload = {}
            response = requests.request("GET", STORE_GET_URL, headers={
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            country = resp_data["data"]["store"]["country"]
            logging.info(country)
            assert "India" in country
            # 005. Verify if specific store token is used in Authentication
            verifyData = resp_data["data"]["store"]["storeName"]
            logging.info(verifyData)
            assert "Cred Automation Store" in verifyData
            logging.info(" 005. API should display data of that specific store and should not display data of others stores")
            logging.info(" 003. API should display full name of the country")
            # 005. Verify if specific store token is used in Authentication
            response = requests.request("GET", STORE_GET_URL, headers={
                'Authorization': JWT_TOKEN
            }, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Signature verification failed" in resp_data["message"]
            logging.info("API display data of that specific store and should not display data of others stores")

        Success_List_Append(
            "test_MKTPL_692_StoreAPI_002_003_005",
            "Verify the logo parameter",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_692_StoreAPI_002_003_005",
            "Verify the logo parameter",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_692_StoreAPI_002_003_005",
            "Verify the logo parameter",
            e,
        )
        raise e

def test_MKTPL_692_StoreAPI_004():
    try:
        with utils.services_context_wrapper("test_MKTPL_692_StoreAPI_004.png") as driver:
            # 004. Verify the auto approve catalog parameter
            utils.auto_approve_catalog_on(driver, DEDICATED_STORE)
            payload = {}
            response = requests.request("GET", STORE_GET_URL, headers={
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            autoapprovecatalog = resp_data["data"]["store"]["autoapprovecatalog"]
            logging.info(autoapprovecatalog)
            assert  autoapprovecatalog == True

            logging.info("API display true if checkbox is checked or false if checkbox is unchecked")

        Success_List_Append(
            "test_MKTPL_692_StoreAPI_004",
            "Verify the logo parameter",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_692_StoreAPI_004",
            "Verify the logo parameter",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_692_StoreAPI_004",
            "Verify the logo parameter",
            e,
        )
        raise e

def test_MKTPL_479_ProductAPI_011_017_018_026_043():
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_479_ProductAPI_011_017_018_026_043.png"
        ) as driver:
            RANDOM_NAME = "(From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
            RANDOM_NAME_SKU = "".join(random.choices(string.ascii_letters, k=7))
            # 026. Verify if specific store token is used in Authentication
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "POST", PRODUCTS_URL, RANDOM_NAME_SKU,
                                                     RANDOM_NAME,
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     DEDICATED_CATEGORY_1_ID,
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "", "", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 200 or api_response["code"] == 201
            assert "Product added successfully. " == api_response["message"]
            productId = api_response["id"]
            logging.info(productId)
            logging.info("026. POST - API create the product with all the details provided in the API under specific "
                         "store folder")
            # 043. Verify if specific store token is used in Authentication
            api_response = utils.product_api_payload(CRED_AUTOMATION_STORE_TOKEN, "PUT", PRODUCTS_URL+str(productId), RANDOM_NAME_SKU,
                                                     RANDOM_NAME+"EDE",
                                                     RANDOM_NAME, RANDOM_NAME, RANDOM_NAME_SKU, "Onboard",
                                                     DEDICATED_CATEGORY_1_ID,
                                                     1, "LB", 1, "IN", 1, "IN", 1, "IN", "", "", 1,
                                                     11, 11, True)
            assert api_response != False
            assert api_response["code"] == 200 or api_response["code"] == 201
            assert "Product updated successfully. " == api_response["message"]
            logging.info(" 043. API update the product with all the details provided in the API under specific store folder")

            # 017. Verify the selected, isTaxable parameter
            response = requests.request("GET", PRODUCTS_URL+str(productId), headers={
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }, data={})
            resp_data = response.json()
            assert resp_data['code'] == 200 or resp_data['code'] == 201
            verifyValue = resp_data["data"]["product"]["PriceTaxation"]["isTaxable"]
            assert verifyValue == True
            logging.info(" 017. API should display true if checkbox is checked or false if checkbox is unchecked")
            # 011. Verify if specific store token is used in Authentication
            assert productId == resp_data["data"]["product"]["id"]
            logging.info(" 011. API should display products of that specific store and should not display products of others stores")
            # 018. Verify if specific store token is used in Authentication
            response = requests.request("GET", PRODUCTS_URL, headers={
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }, data={})
            resp_data = response.json()
            assert resp_data['code'] == 200 or resp_data['code'] == 201
            logging.info("018. API display products of that specific store and should not display products of"
                         " others stores")
            Success_List_Append("test_MKTPL_479_ProductAPI_011_017_018_026_043",
                                "Verify if specific store token is used in Authentication",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_479_ProductAPI_011_017_018_026_043",
                            "Verify if specific store token is used in Authentication",
                            "Fail", )
        Failure_Cause_Append("test_MKTPL_479_ProductAPI_011_017_018_026_043",
                             "Verify if specific store token is used in Authentication",
                             e, )
        raise e

def test_MKTPL_479_ProductAPI_057():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
        with utils.services_context_wrapper(
                "test_MKTPL_479_ProductAPI_057.png"
        ) as driver:

            # 057. Verify if the mandatory product category attributes is empty in the key in the request
            payload = utils.get_Product_Payload(RANDOM_NAME, int(DEDICATED_PRODUCT_11_ID), "Next Flight",
                                                DEDICATED_CATEGORY_1_ID, "Configurable", "Duty Free", 1.23, 1.90, 910,
                                                "RAM", "", True)
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)

            assert resp_data['code'] == 200 or resp_data['code'] == 201
            # assert "Variant Attributes do not match with Category Variant Themes " in resp_data["message"]

            Success_List_Append("test_MKTPL_479_ProductAPI_040",
                                "Verify if special characters or space are added in sku/barcode parameters in the request",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_479_ProductAPI_040",
                            "Verify if special characters or space are added in sku/barcode parameters in the request",
                            "Fail", )
        Failure_Cause_Append("test_MKTPL_479_ProductAPI_040",
                             "Verify if special characters or space are added in sku/barcode parameters in the request",
                             e, )
        raise e

def test_MKTPL_2708_Product_001_004():
    RANDOM_NAME = "Brand"+"".join(random.choices(string.ascii_letters, k=3))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2708_Product_001_004.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            # Open product object
            utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)

            # Update product
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.productNameFieldXpath))))
            # driver.find_element(By.XPATH,xpath.productNameFieldXpath).send_keys("_")
            driver.find_element(By.NAME, "brand").send_keys(RANDOM_NAME)
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
            save_and_publish.click()

            # 001. "Verify the Visibility Scope and Channel fields in the product object, GET Product API and product_publish SNS"
            check_list = ["visibilityScope", "channel"]

            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("GET", PRODUCTS_URL+DEDICATED_PRODUCT_9_ID,
                                        headers=headers)
            data = response.json()
            logging.info(data)
            assert len([x for x in check_list if x in data["data"]["product"].keys()]) == 0
            logging.info("Visibility Scope and Channel fields should be invisible in API")

            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'product_publish')]")
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f"(//div[contains(text(),'product_publish')])[{num-1}]")))
            # Click on the last created entry
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'product_publish')])[{num-1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f"(//span[contains(text(),'{DEDICATED_STORE}')])[1]")))
            time.sleep(5)
            sns_keys = [keys.text.replace('"','') for keys in driver.find_elements(By.XPATH, '(//div[@class="ace_line"]//span[@class="ace_variable"])')] 
            logging.info(sns_keys)
            assert len([x for x in check_list if x in sns_keys]) == 0
            logging.info("Visibility Scope and Channel fields should be invisible in Triggered SNS")
            
            # 004. SNS Payload should not include the Price and Weight parameters
            new_check_list = ["price", "weight"]
            assert len([x for x in new_check_list if x in sns_keys]) == 0
            logging.info("Verified SNS Payload does not include the Price and Weight parameters")
            
            # time.sleep(3)
            # utils.click_on_yes_no(driver)

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_2708_Product_001_004",
                "Verify the Visibility Scope and Channel fields in the product object, GET Product API and product_publish SNS",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2708_Product_001_004",
            "Verify the Visibility Scope and Channel fields in the product object, GET Product API and product_publish SNS",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2708_Product_001_004",
            "Verify the Visibility Scope and Channel fields in the product object, GET Product API and product_publish SNS",
            e,
        )
        raise e

def test_MKTPL_2708_Product_002_003():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    product_uid = str(utils.create_Simple_Product(deliveryMethod="onboard", productType="Duty Free", random_name=RANDOM_NAME))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2708_Product_002_003.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
 
            # Open product object
            utils.search_by_id(driver, product_uid)
 
            # Update product
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.productNameFieldXpath))))
            driver.find_element(By.XPATH,xpath.productNameFieldXpath).send_keys("_")
 
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
            save_and_publish.click()
 
            # 002. "Verify the Visibility Scope and Channel fields in the product CSV sample file, Catalog Export XLSX and Post/Put/Patch product request"
            check_list = ["visibilityScope", "channel"]
           
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(1)
            store_input = driver.find_element(By.XPATH, '//input[@name="storeList"]')
            store_input.send_keys(DEDICATED_STORE)
            time.sleep(1)
            store_input.send_keys(Keys.ENTER)
            time.sleep(1)
            category_input = driver.find_element(By.XPATH, '//input[@name="category[]"]')
            category_input.send_keys(DEDICATED_CATEGORY_1_NAME)
            time.sleep(2)
            category_input.send_keys(Keys.ENTER)
            time.sleep(10)
            driver.find_element(By.XPATH, xpath.downloadSampleFileProducts).click()
            time.sleep(10)
            filelist = []
            last_file_time = 0
            for current_file in os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE):
                filelist.append(current_file)
                current_file_fullpath = os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, current_file)
                current_file_time = os.path.getctime(current_file_fullpath)
                if os.path.isfile(current_file_fullpath):
                    if last_file_time == 0:
                        last_file = current_file
                    last_file_time = os.path.getctime(
                        os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file))
                    if current_file_time > last_file_time and os.path.isfile(current_file_fullpath):
                        last_file = current_file
                last_file_fullpath = os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file)
            logging.info(last_file_fullpath)
            data = utils.pd.read_csv(last_file_fullpath)
            columns = data.columns
            columns_list = list(columns)
            logging.info(columns_list)
            assert len([x for x in check_list if x in columns_list]) == 0
            logging.info("Verified Visibility Scope and Channel fields are not available in CSV")
           
            payload = json.dumps({
                "sku": RANDOM_NAME+"_",
                "name": RANDOM_NAME,
 
                "deliveryMethod": [
                    "Onboard"
                ],
 
                "category": [
                    "82950"
                ],
                "productSet": "Simple",
                "productType": "Duty Free"
 
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PUT", PRODUCTS_URL+product_uid, headers=headers,
                            data=payload)
            data = response.json()
            logging.info(data)
            assert len([x for x in check_list if x in json.loads(payload).keys()]) == 0
            logging.info("Visibility Scope and Channel fields are not available in API")
           
            # 003. Verify the Price and Weight fields in the product object, product CSV file and Post/Put/Patch product request
            new_check_list = ["price", "weight"]
            utils.update_csv("Product_csv_validation.csv", "name_en", RANDOM_NAME+"_")
            utils.update_csv("Product_csv_validation.csv", "sku", RANDOM_NAME)
            utils.update_csv("Product_csv_validation.csv", "productType", "Duty Free")
            utils.update_csv("Product_csv_validation.csv", "weight", "")
            utils.update_csv("Product_csv_validation.csv", "price_USD", "")
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Product_csv_validation.csv", file_path)
            assert saved_message
            logging.info("Verified Price and Weightfields are non-mandatory in CSV")
           
            assert len([x for x in new_check_list if x in json.loads(payload).keys()]) == 0
            logging.info("Verified Price and Weightfields are non-mandatory in API")
           
            try:
                product_id = "ID " + product_uid
                RT_VALUE = utils.Delete(product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
           
            # time.sleep(3)
            # utils.click_on_yes_no(driver)
 
            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_2708_Product_002_003",
                "Verify the Visibility Scope and Channel fields in the product CSV sample file, Catalog Export XLSX and Post/Put/Patch product request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2708_Product_002_003",
            "Verify the Visibility Scope and Channel fields in the product CSV sample file, Catalog Export XLSX and Post/Put/Patch product request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2708_Product_002_003",
            "Verify the Visibility Scope and Channel fields in the product CSV sample file, Catalog Export XLSX and Post/Put/Patch product request",
            e,
        )
        raise e

def test_MKTPL_2708_Product_005():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2708_Product_005.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
 
            # Open product object
            utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
 
            # 005. "Verify the Tax and Calculated Price fields in the product object"
            check_list = ["tax", "calcPrice"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//li[@data-menu-tooltip="Settings"]'))
            )
            driver.find_element(By.XPATH, '//li[@data-menu-tooltip="Settings"]').click()
            logging.info("Clicked on Settings")
            driver.find_element(By.XPATH, '//span[text()="Data Objects"]').click()
            logging.info("Clicked on Data Objects")
            driver.find_element(By.XPATH, '//span[text()="Classes"]').click()
            logging.info("Clicked on Classes")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Products (PRD)"]'))
            )
            driver.find_element(By.XPATH, '//span[text()="Products (PRD)"]').click()
            time.sleep(10)
            logging.info("Clicked on PRD")
           
            scroll_element12 = WebDriverWait(driver, 120).until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="Assets"])[last()]'))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll_element12)
            logging.info("Scroll to Layout element")
       
            weight_ele = WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '(//span[text()="Weight & Shipping Details"])[last()]')))
            time.sleep(5)
            weight_ele.click()
            logging.info("clicked on weight successfully")
           
            logging.info("Cliked on Weight and shipping details")
            driver.find_element(By.XPATH, f'//span[text()="{check_list[0]}"]').click()
            time.sleep(1)
            assert driver.find_element(By.XPATH, '//span[text()="Invisible:"]//ancestor::label//following-sibling::div//input[@checked="checked"]')
            logging.info("tax asserted")
            driver.find_element(By.XPATH, f'//span[text()="{check_list[1]}"]').click()
            time.sleep(1)
            assert driver.find_element(By.XPATH, '//span[text()="Invisible:"]//ancestor::label//following-sibling::div//input[@checked="checked"]')
            logging.info("calculated price asserted")
            logging.info("Verified Tax and Calculated Price fields are invisible")
           
            # time.sleep(3)
            # utils.click_on_yes_no(driver)
 
            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_2708_Product_005",
                "Verify the Tax and Calculated Price fields in the product object",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2708_Product_005",
            "Verify the Tax and Calculated Price fields in the product object",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2708_Product_005",
            "Verify the Tax and Calculated Price fields in the product object",
            e,
        )
        raise e

def test_MKTPL_2708_Product_006():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    product_uid = str(utils.create_Simple_Product(deliveryMethod="onboard", productType="Duty Free", random_name=RANDOM_NAME))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2708_Product_006.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            # Open product object
            utils.search_by_id(driver, product_uid)

            # Update product
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, xpath.productNameFieldXpath))))
            driver.find_element(By.XPATH,xpath.productNameFieldXpath).send_keys("_")

            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            save_and_publish = driver.find_element(By.XPATH, xpath.saveAndPublishXpath)
            save_and_publish.click()
            
            # 006. "Verify the Delivery Method and Is Available To Sell fields in the product object, \
            #        product CSV sample file, Catalog Export XLSX, GET Product API, product_publish SNS and Post/Put/Patch product request",
            check_list = ["deliveryMethod", "isAvailableToSell"]
            
            payload = json.dumps({
                "sku": RANDOM_NAME+"_",
                "name": RANDOM_NAME,

                "deliveryMethod": [
                    "Onboard"
                ],

                "category": [
                    "82950"
                ],
                "productSet": "Simple",
                "productType": "Duty Free",
                "isAvailableToSell": True

            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PUT", PRODUCTS_URL+product_uid, headers=headers,
                            data=payload)
            data = response.json()
            logging.info(data)
            assert len([x for x in check_list if x in json.loads(payload).keys()]) > 0
            logging.info("Verified Delivery Method and Is Available To Sell fields are visible in API")
            
            WebDriverWait(driver, 60).until(
                (EC.presence_of_element_located((By.XPATH, '//span[text()="Delivery Method "]'))))
            assert driver.find_element(By.XPATH, '//span[text()="Delivery Method "]')
            logging.info("product object: Delivery Method asserted")
            assert driver.find_element(By.XPATH, '//span[text()="Is Available To Sell:"]//ancestor::label//following-sibling::div//input[@checked="checked"]')
            logging.info("product object: Is Available to sell asserted")
            logging.info("Verified Delivery Method and Is Available To Sell fields are visible in product object")
            
            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'product_publish')]")
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f"(//div[contains(text(),'product_publish')])[{num-1}]")))
            # Click on the last created entry
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'product_publish')])[{num-1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f"(//span[contains(text(),'{DEDICATED_STORE}')])[1]")))
            time.sleep(5)
            sns_keys = [keys.text.replace('"','') for keys in driver.find_elements(By.XPATH, '(//div[@class="ace_line"]//span[@class="ace_variable"])')] 
            logging.info(sns_keys)
            assert len([x for x in check_list if x in sns_keys]) > 0
            logging.info("Visibility Scope and Channel fields should be invisible in Triggered SNS")
            
            try:
                product_id = "ID " + product_uid
                RT_VALUE = utils.Delete(product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            
            # time.sleep(3)
            # utils.click_on_yes_no(driver)

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_2708_Product_006",
                "Verify the Delivery Method and Is Available To Sell fields in the product object, \
                    product CSV sample file, Catalog Export XLSX, GET Product API, product_publish SNS and Post/Put/Patch product request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2708_Product_006",
            "Verify the Delivery Method and Is Available To Sell fields in the product object, \
                product CSV sample file, Catalog Export XLSX, GET Product API, product_publish SNS and Post/Put/Patch product request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2708_Product_006",
            "Verify the Delivery Method and Is Available To Sell fields in the product object, \
                product CSV sample file, Catalog Export XLSX, GET Product API, product_publish SNS and Post/Put/Patch product request",
            e,
        )
        raise e

def test_MKTPL_1372_OrderProvider_002():
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_1372_OrderProvider_002.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.configuration_tab))).click())
            verify = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="orderProvider"]'))).get_attribute('value'))
            logging.info(verify)
            assert verify is not None
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath))).click())
            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(("xpath", xpath.saveSuccessfullyMessageXpath))).is_displayed()
            Success_List_Append("test_MKTPL_1372_OrderProvider_002", "Verify by clicking on the store",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1372_OrderProvider_002", "Verify by clicking on the store", "Fail", )
        Failure_Cause_Append("test_MKTPL_1372_OrderProvider_002", "Verify by clicking on the store", e, )
        raise e

def test_MKTPL_3704_Newfromtodate_006():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_3704_Newfromtodate_006.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_CATALOG_1_ID)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Export XLSX"]')))
            export = driver.find_element(By.XPATH, '//span[text()="Export XLSX"]')
            driver.execute_script("arguments[0].click();",export)
            time.sleep(5)
            os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE)
            time.sleep(5)
            filelist = []
            last_file_time = 0
            for current_file in os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE):
                filelist.append(current_file)
                current_file_fullpath = os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, current_file)
                current_file_time = os.path.getctime(current_file_fullpath)
                if os.path.isfile(current_file_fullpath):
                    if last_file_time == 0:
                        last_file = current_file
                    last_file_time = os.path.getctime(
                        os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file))
                    if current_file_time > last_file_time and os.path.isfile(current_file_fullpath):
                        last_file = current_file
                last_file_fullpath = os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file)
            logging.info(last_file_fullpath)

            try:
                workbook = load_workbook(last_file_fullpath)
                sheet_names = workbook.sheetnames

                if "Products" in sheet_names:
                    sheet = workbook["Products"]
                    for row in sheet.iter_rows(values_only=True):
                        logging.info(row)
                        ACTUAL_CSV_DATA.append(row)
                else:
                    logging.error("Sheet 'products' not found in the Excel file.")
            except FileNotFoundError:
                logging.error(f"File {last_file_fullpath} not found.")
            except Exception as e:
                logging.error(f"An error occurred while reading the file: {e}")
            date_text_from = ACTUAL_CSV_DATA[1][16] 
            date_text_to = ACTUAL_CSV_DATA[1][17]
            logging.info(date_text_from)
            logging.info(date_text_to)  
            try:
                datetime.date.fromisoformat(date_text_from)
            except ValueError:
                raise ValueError("Incorrect data format - FROM, should be YYYY-MM-DD")
            try:
                datetime.date.fromisoformat(date_text_to)
            except ValueError:
                raise ValueError("Incorrect data format - TO, should be YYYY-MM-DD")
            Success_List_Append(" test_MKTPL_3704_Newfromtodate_006",
                            "Verify the active catalog assignments associated", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_3704_Newfromtodate_006",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_3704_Newfromtodate_006",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_480_CatalogAPI_023_024():
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_480_CatalogAPI_023_024.png"
        ) as driver:
            URL = PAC_API_GATEWAY_URL+ "marketplace/v1/pim/store/386429486239847239487/catalog"
            # 23
            payload = {}
            headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
            response = requests.request(
                "GET", URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------INVALID STORE ID-------")
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Invalid Store Id or Store Not Associated" in data['message']
            URL = PAC_API_GATEWAY_URL+ "marketplace/v1/pim/store/'"+str(DEDICATED_STORE_1_ID)+"'/catalog"
            # 24
            payload = {}
            headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
            response = requests.request(
                "GET", URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------GET BY AIRLINE TOKEN-------")
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Invalid Store Id or Store Not Associated" in data['message']       
            Success_List_Append(
                "test_MKTPL_480_CatalogAPI_023_024",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_480_CatalogAPI_023_024",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_480_CatalogAPI_023_024",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_1679_BypassPayment_003():
    try:
        with utils.services_context_wrapper("test_MKTPL_1679_BypassPayment_003.png") as driver:
            driver.maximize_window()
            role = ["Login_Store_Manager", "Login_Store"]
            for i in role:
                if i is "Login_Store_Manager":
                    utils.Pac_Credentials.Login_Store_Manager(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    utils.wait_for_style_attribute(driver, 40)
                    logging.info("Manager")
                elif i is "Login_Store":
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    utils.wait_for_style_attribute(driver, 40)
                driver.find_element(By.XPATH, "//div[text()='Store Profile']//ancestor::div//span[text()='"+DEDICATED_STORE+"']").click()
                utils.click_on_yes_no(driver)
                (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//span[text()="Payment Configuration"]'))).click())
                check_box = driver.find_element(By.NAME, 'bypassPACPayment').get_attribute("aria-disabled")
                assert check_box == "true"
                logging.info("The checkbox is disabled")
                (WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, xpath.logout))))
                logout = driver.find_element(By.XPATH, xpath.logout)
                driver.execute_script("arguments[0].click();", logout)
            Success_List_Append("test_MKTPL_1679_BypassPayment_003", "Verify by clicking on the store", "Pass")
    except Exception as e:
        Success_List_Append("test_MKTPL_1679_BypassPayment_003", "Verify by clicking on the store", "Fail")
        Failure_Cause_Append("test_MKTPL_1679_BypassPayment_003", "Verify by clicking on the store", e)
        raise e

def test_MKTPL_390_388_Productimport_004_005_032():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_390_388_Productimport_004_005_032.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            utils.wait_for_style_attribute(driver, 200)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(2)
            category = driver.find_element(By.NAME, 'category[]')
            category.send_keys("Non-alcoholic", Keys.DOWN, Keys.DOWN)
            time.sleep(3)
            driver.find_element(By.XPATH,"//li[contains(text(),'Non-alcoholic')]").click()
            # category.send_keys(Keys.ENTER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.downloadSampleFileProducts)))
            driver.find_element(By.XPATH, xpath.downloadSampleFileProducts).click()

            os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE)
            time.sleep(2)
            filelist = []
            last_file_time = 0
            for current_file in os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE):
                filelist.append(current_file)
                current_file_fullpath = os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, current_file)
                current_file_time = os.path.getctime(current_file_fullpath)
                if os.path.isfile(current_file_fullpath):
                    if last_file_time == 0:
                        last_file = current_file
                    last_file_time = os.path.getctime(
                        os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file))
                    if current_file_time > last_file_time and os.path.isfile(current_file_fullpath):
                        last_file = current_file
                last_file_fullpath = os.path.join(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file)
            logging.info(last_file_fullpath)

            with open(
                    last_file_fullpath, "r", encoding='utf-8-sig'
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert ACTUAL_CSV_DATA == CSV_SAMPLE_PRODUCT

            logging.info("CSV file should be download on the system and sample records should display in CSV")
            logging.info("005 all column is showing")
            # 032
            RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_lowercase, k=5))
            utils.update_csv("Product_Sample.csv", "name_en", RANDOM_NAME)
            utils.upload_csv_with_specific_validation("Product_Sample.csv", xpath.csvFileSelectButton, 'CSV Uploaded Successfully')
            # path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"
            # driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            # driver.find_element(By.XPATH, xpath.upload).click()

            # ele = WebDriverWait(driver, 60).until(
            #     EC.visibility_of_element_located(
            #         (By.XPATH, "//div[contains(text(),'CSV Uploaded Successfully')]"))).is_displayed()
            # assert ele
            # logging.info("sdkfkdjskfjh")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)

            utils.Assets_import_Store(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'products')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'products')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//div[@class="ace_line"])[4]')))
            validation = driver.find_element(By.XPATH, '(//div[@class="ace_line"])[4]').text
            logging.info(validation)

            assert "Row Inserted\/updated Successfully" in validation

            Success_List_Append(
                " test_MKTPL_390_388_Productimport_004_005_032",
                "Verify the fields displaying in UI template screen",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(" test_MKTPL_390_388_Productimport_004_005_032",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append(" test_MKTPL_390_388_Productimport_004_005_032",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_2131_Product_002_003_004():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2131_Product_002_003_004.png"
        ) as driver:
            # Login through PAC admin user
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            ## Search for product
            utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Category "]')))
            # Determining category
            assert driver.find_element(By.XPATH, '(//span[text()="Category "]//ancestor::label)//following-sibling::div//div//span')
            logging.info("Verified category is associated with product")
            category_text = driver.find_element(By.XPATH, '(//span[text()="Category "]//ancestor::label)//following-sibling::div//div//span').text
            logging.info(f"Category assigned: {category_text}")
            category = category_text.split("(")[1].split(")")[0]
            category_path = "/Master Data/Category/" + category
            logging.info(f"Category path: {category_path}")
            
            # close all tabs
            utils.close_All_Tabs(driver)
            
            ## Open the category object for editing
            utils.search_by_path(driver, category_path)
            
            # 002. "Verify if variant theme is associated with product and user try to remove the variant theme"
            # Navigate to Attributes tab in the category object
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Attributes"]')))
            driver.find_element(By.XPATH, '//span[text()="Attributes"]').click()
            logging.info("Attributes tab opened")
            # Variant Theme available
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//div[contains(text(), "Variant Theme")]')))
            # Removing Attribute set
            driver.find_element(By.XPATH, '(//div[text()="VariantTheme"])[1]//parent::div//a[3]').click()
            logging.info("Clicked in attempt to remove Variant Theme")
            assert driver.find_element(By.XPATH, '//div[contains(text(), "Existing Variant Themes Cannot Be Removed")]')
            logging.info("Verified it does not allow users to remove the variant theme and validation message is displayed")

            driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            
            # 003. "Verify if variant theme is associated with product and user try to update the theme name"
            # Variant Theme available
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//div[contains(text(), "Variant Theme")]')))
            # Checking theme name is enabled or disabled
            driver.find_element(By.XPATH, '(//input[@name="themeName"])[1]')
            assert not driver.find_element(By.XPATH, '(//input[@name="themeName"])[1]').is_enabled()
            logging.info("Verified Theme name field is in read only mode")
            
            # 004. "Verify if variant theme is associated with product and user try to update the variant attribute"
            # Variant Theme available
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//div[contains(text(), "Variant Theme")]')))
            # Checking variant attribute is enabled or disabled
            driver.find_element(By.XPATH, '(//input[@name="variantAttribute"])[1]')
            assert not driver.find_element(By.XPATH, '(//input[@name="variantAttribute"])[1]').is_enabled()
            logging.info("Verified Variant Attribute field is in read only mode")

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_2131_Product_002_003_004",
                "Verify if variant theme is associated with product and user try to remove the variant theme",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2131_Product_002_003_004",
            "Verify if variant theme is associated with product and user try to remove the variant theme",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2131_Product_002_003_004",
            "Verify if variant theme is associated with product and user try to remove the variant theme",
            e,
        )
        raise e

def test_MKTPL_3237_Disassociatemultiplecategories_006():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_3237_Disassociatemultiplecategories_006.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.new_csv_import(driver, "Products")
             
            # 006. "Verify if user remove the category and it's variant theme is associated in parent configurable product"
            time.sleep(2)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//input[@name="category[]"]')))
            category_input = driver.find_element(By.XPATH, '//input[@name="category[]"]')
            category_input.send_keys(DEDICATED_CATEGORY_1_NAME)
            category_input.send_keys(Keys.ENTER)
            utils.update_csv("Product_csv_validation.csv", "name_en", RANDOM_NAME+"_")
            utils.update_csv("Product_csv_validation.csv", "sku", RANDOM_NAME)
            utils.update_csv("Product_csv_validation.csv", "newFrom", "2024-12-01")
            utils.update_csv("Product_csv_validation.csv", "deliveryMethod", "Onboard")
            utils.update_csv("Product_csv_validation.csv", "productType", "Duty Free")       
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Product_csv_validation.csv", file_path)
            logging.info(saved_message)
            
            utils.close_All_Tabs(driver)
            
            utils.new_csv_import(driver, "Products")
            
            path = os.getcwd()+"/credencys_test_ui/" + "Product_csv_validation.csv"
            # Clicking on upload button
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.upload)))
            driver.find_element(By.XPATH, file_path).send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()
            assert driver.find_element(By.XPATH, xpath.mandatoryFieldErrorMessage).is_displayed()
            logging.info("Verified validation message displayed in CSV Import")

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_3237_Disassociatemultiplecategories_006",
                "Verify if user remove the category and it's variant theme is associated in parent configurable product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories_006",
            "Verify if user remove the category and it's variant theme is associated in parent configurable product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories_006",
            "Verify if user remove the category and it's variant theme is associated in parent configurable product",
            e,
        )
        raise e
