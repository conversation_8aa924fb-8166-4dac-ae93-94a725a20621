import string, random, time
import logging, os, requests, json
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import credencys_test_ui.credencys_project_configs.utils as utils
from selenium.webdriver.support.wait import Web<PERSON>river<PERSON>ait
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

# test_MKTPL_1134_Locationobject_001
def test_MKTPL_1134_Locationobject_001_002_003_004_005_006_007_014():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_NAME_STORE = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1134_Locationobject_001_002_003_004_005_006_007_014.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Stores")
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME_STORE)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "storeName"))
            ).send_keys(RANDOM_NAME_STORE)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Address Information"]')
                )
            ).click()
            driver.find_element(By.NAME, "firstName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.configuration_tab)
                )
            ).click()
            driver.find_element(By.NAME, "manageStock").send_keys("Yes")
            driver.find_element(By.NAME, "salesStrategy").send_keys("D2C")
            driver.find_element(By.NAME, "defaultCurrency").send_keys("USD", Keys.ENTER)
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Store is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            # MKTPL-1134-Locationobject-002
            utils.close_All_Tabs(driver)
            time.sleep(10)
            utils.search_by_path(driver, "/"+RANDOM_NAME_STORE+"/Inventory Location/Default Inventory")
            driver.find_element(By.XPATH, xpath.showInTreeXpath).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//span[contains(text(),'Default Inventory')])[2]")))
            assert len(driver.find_elements(By.XPATH, "//span[contains(text(),'Default Inventory')]")) == 2
            logging.info("default_inventory")
            logging.info("Key name should be Default Inventory")
            fulfillment = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[@class="x-form-trigger-wrap x-form-trigger-wrap-default"]//span[@class="x-hidden-clip"])[1]',
                    )
                )
            ).text
            logging.info(fulfillment)
            assert fulfillment != None
            assert driver.find_element(By.XPATH, "//span[contains(text(), 'Name')]").is_displayed()
            assert driver.find_element(By.XPATH, "//span[contains(text(), 'Store')]//parent::span//span[contains(@class,'icon_gray_lock')]").is_displayed()
            # MKTPL-1134-Locationobject-007
            logging.info("Following field should display:- Name- Fulfilment- Store")
            # MKTPL-1134-Locationobject-003
            defaultInventory = driver.find_element(By.XPATH, "(//span[contains(text(),'Default Inventory')])[1]")
            action = ActionChains(driver)
            action.move_to_element(defaultInventory).perform()
            action.context_click(defaultInventory).perform()
            assert driver.find_element(By.XPATH, "//span[text() = 'Copy']").is_displayed()
            logging.info("Clicked on Copy")
            time.sleep(2)
            try:
              assert not driver.find_element(By.XPATH, "//span[text() = 'Rename']").is_enabled()
              logging.info("Rename not shown")
              assert False
            except Exception as e:
              logging.info(e)
              logging.info("Rename not shown")
              pass
            logging.info("Admin should not be able to rename key of Default Location object")
            # MKTPL-1134-Locationobject-004
            x = driver.find_element(By.NAME, "storeId").get_attribute("readonly")
            assert x 
            # assert driver.find_element(By.NAME, "storeId").get_attribute("disabled") == ""
            logging.info("Store field should be auto fetch and will be non editable in the object")
            # MKTPL-1134-Locationobject-005
            utils.close_All_Tabs(driver)
            time.sleep(10)
            utils.search_by_path(driver, "/"+RANDOM_NAME_STORE+"/Inventory Location/")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Default Inventory')]")))
            defaultInventory = driver.find_element(By.XPATH, "//div[contains(text(),'Default Inventory')]")
            action.context_click(defaultInventory).perform()
            driver.find_element(By.XPATH, "//span[contains(text(), 'Delete')]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Do you really want to delete this item?')]")))
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Could not delete item')]")))
            assert driver.find_element(By.XPATH, "//div[contains(text(),'Could not delete item')]").is_displayed()
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Access Denied.')]")))
            assert driver.find_element(By.XPATH, "//div[contains(text(),'Access Denied.')]").is_displayed()
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            logging.info("Admin should not be able to delete the Default Location object")
            utils.close_All_Tabs(driver)
            # MKTPL-1134-Locationobject-006
            UID = UID.split()
            UID = UID[1]
            logging.info("Opening object")
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'description')))
            logging.info("Changed description")
            driver.find_element(By.NAME, 'description').send_keys("Description for " + RANDOM_NAME)
            utils.save_and_publish(driver)
            utils.close_All_Tabs(driver)
            utils.search_by_path(driver, "/"+RANDOM_NAME_STORE+"/Inventory Location/Default Inventory")
            driver.find_element(By.XPATH, xpath.showInTreeXpath).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//span[contains(text(),'Default Inventory')])[2]")))
            assert len(driver.find_elements(By.XPATH, "//span[contains(text(),'Default Inventory')]")) == 2
            
            logging.info(
                "Store is Created and Default Location object should be created under "
                "Store Name>Inventory Location folder>Default Inventory"
            )
            Success_List_Append(
                "test_MKTPL_1134_Locationobject_001_002_003_004_005_006_007_014",
                "Verify Store Created and Default Location object "
                "should be created under "
                "Store Name>Inventory Location folder>Default Inventory",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_1134_Locationobject_001_002_003_004_005_006_007_014",
            "Verify Store Created and Default Location object "
            "should be created under "
            "Store Name>Inventory Location folder>Default Inventory",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1134_Locationobject_001_002_003_004_005_006_007_014",
            "Verify Store Created and Default Location object "
            "should be created under "
            "Store Name>Inventory Location folder>Default Inventory",
            e,
        )
        raise e

# MKTPL-1134-Locationobject-008
def test_MKTPL_1134_Locationobject_008_009():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=4)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1134_Locationobject_008_009.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            # WebDriverWait(driver, 50).until(
            #     EC.presence_of_element_located((By.XPATH, '//span[text()="Home"]'))
            # )
            # driver.implicitly_wait(0)
            wait = WebDriverWait(driver, 60)
            # search_home = wait.until(
            #     EC.visibility_of_element_located(
            #         (By.XPATH, '(//span[text()="Home"]/..//a)[2]')
            #     )
            # )
            utils.search_by_id(driver, DEDICATED_STORE_LOCATION_1_ID)
            location_name = wait.until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="locationName"]')
                    )
                )
            )
            logging.info("location_name")
            lname_text = driver.find_element(By.XPATH, '//input[@name="locationName"]').get_attribute("value")
            logging.info(lname_text)
            driver.find_element(By.XPATH, '//input[@name="locationName"]').clear()
            # MKTPL-1134-Locationobject-009
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            logging.info("Before assert")
            assert (
            WebDriverWait(driver, 60)
            .until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Validation failed: Empty mandatory field [ locationName ]')]")
                    )
                )
                .is_displayed()
            )
            logging.info("Validation shown for empty field")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            lname = "Default Inventory " + RANDOM_NAME
            driver.find_element(By.XPATH, '//input[@name="locationName"]').send_keys(lname)
            logging.info(lname_text)

            fulfillment = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '((//li[@class="x-tagfield-item"])[1]//div)[1]')
                )
            ).text
            logging.info(fulfillment)

            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="fullfillmentOption"]')
                    )
                )
            )
            # Delete_fulfillment = driver.find_element(
            #     By.XPATH, '//input[@name="fullfillmentOption"]'
            # )
            # Delete_fulfillment.clear()
            x = len(driver.find_elements(By.XPATH, "//ul[@data-ref = 'itemList']//div[contains(@class, 'close')]"))
            for i in range(1, x):
              driver.find_element(By.XPATH, "(//ul[@data-ref = 'itemList']//div[contains(@class, 'close')])["+str(i)+"]").click()
            saved = utils.save_and_publish(driver)
            assert saved

            location_name = driver.find_element(By.NAME, "locationName").get_attribute(
                "value"
            )
            logging.info(location_name)
            fulfillment_edit = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[@class="x-form-trigger-wrap x-form-trigger-wrap-default"]//span[@class="x-hidden-clip"])[1]',
                    )
                )
            ).text
            logging.info(fulfillment_edit)

            assert location_name != lname_text and saved
            assert fulfillment != fulfillment_edit

            UID = utils.get_uid(driver)
            
            logging.info(
                "PAC admin is able to update default Inventory Name and Fulfillment"
            )
            Success_List_Append(
                "test_MKTPL_1134_Locationobject_008_009",
                "Verify default Inventory Name and Fulfillment" " is updated.",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_1134_Locationobject_008_009",
            "Verify default Inventory Name and Fulfillment" " is updated.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1134_Locationobject_008_009",
            "Verify default Inventory Name and Fulfillment" " is updated.",
            e,
        )
        raise e

def test_MKTPL_1134_Locationobject_012_013_015_016_017_019():
    try:
        RANDOM_DESCRIPTION = "".join(random.choices(string.ascii_letters, k=15))
        logging.info(RANDOM_DESCRIPTION)
        with utils.services_context_wrapper(
            "test_MKTPL_1134_Locationobject_012_013_015_016_017_019.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            driver.find_element(By.NAME, "description").clear()
            driver.find_element(By.NAME, "description").send_keys(RANDOM_DESCRIPTION)
            # utils.save_and_publish(driver)
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, DEDICATED_STORE_LOCATION_1_ID)
            # MKTPL-1134-Locationobject-013
            # MKTPL-1134-Locationobject-015
            assert not (len(driver.find_elements(By.XPATH, "//a[@data-qtip='Delete']")))
            logging.info("Delete icon not shown")
            driver.find_element(By.XPATH, "//div[contains(text(),'Inventory Location')]").click()
            logging.info("Clicked on Inventory Location")
            
            driver.find_element(By.XPATH, xpath.showInTreeXpath).click()
            logging.info("Clicked on show in tree button")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//span[text()='Default Inventory'])[2]")))
            assert len(driver.find_elements(By.XPATH, "//span[text()='Default Inventory']")) == 2
            logging.info("default_inventory")
            logging.info("Key name should be Default Inventory")
            x1 = driver.find_element(By.XPATH, "(//span[text()='Default Inventory'])[1]")
            actions = ActionChains(driver)
            actions.context_click(x1).perform()
            time.sleep(1)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//span[text()='Copy']")))
            assert len(driver.find_elements(By.XPATH, "//span[text()='Copy']")) == 1
            try:
                driver.find_element(By.XPATH, "//span[text()='Rename']").click()
                time.sleep(2)
                assert not (driver.find_elements(By.NAME, "messagebox-1001-textfield-inputEl"))
            except:
                logging.info("Cannot rename")
            # assert not len(driver.find_elements(By.XPATH, "//span[text()='Rename']"))
            logging.info("Rename option not visible")
            # MKTPL-1134-Locationobject-012
            fulfillment = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[@class="x-form-trigger-wrap x-form-trigger-wrap-default"]//span[@class="x-hidden-clip"])[1]',
                    )
                )
            ).text
            logging.info(fulfillment)
            assert fulfillment != None
            assert driver.find_element(By.XPATH, "//span[contains(text(), 'Name')]").is_displayed()
            logging.info("Following field should display:- Name- Fulfilment- Store")
            # MKTPL-1134-Locationobject-019
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, DEDICATED_STORE_LOCATION_1_ID)
            location_name = wait.until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="locationName"]')
                    )
                )
            )
            lname_text = location_name.get_attribute("value")
            location_name.clear()
            driver.find_element(By.XPATH, xpath.saveXpath).click()
            assert not (
            WebDriverWait(driver, 60)
            .until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
                .is_displayed()
            )
            logging.info(
                "Store is Created and Default Location object should be created under "
                "Store Name>Inventory Location folder>Default Inventory"
            )
            Success_List_Append(
                "test_MKTPL_1134_Locationobject_012_013_015_016_017_019",
                "Verify Store Created and Default Location object "
                "should be created under "
                "Store Name>Inventory Location folder>Default Inventory",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_1134_Locationobject_012_013_015_016_017_019",
            "Verify Store Created and Default Location object "
            "should be created under "
            "Store Name>Inventory Location folder>Default Inventory",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1134_Locationobject_012_013_015_016_017_019",
            "Verify Store Created and Default Location object "
            "should be created under "
            "Store Name>Inventory Location folder>Default Inventory",
            e,
        )
        raise e

def test_MKTPL_2466_CRUDRoottype_001_002_005_006_008_011_014():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_DESC = "".join(random.choices(string.ascii_letters, k=20))

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2466_CRUDRoottype_001_002_005_006_008_011_014.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            utils.click_on_add_object(driver, "RootType")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )


            assert driver.find_element(By.XPATH, xpath.rootTypeNameXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.rootDescriptionXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.rootAirlineXpath).is_displayed()

            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Save & Publish"]')
                )
            )
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            assert WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Validation failed: Empty mandatory field [ rootTypeName ] / Empty mandatory field [ airline ]"]')
                )
            ).is_displayed()
            
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="OK"])[last()]')
                )
            ).click()

            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "rootTypeName")))
                .send_keys(RANDOM_NAME)
            )
            driver.find_element(By.NAME, "description").send_keys(RANDOM_DESC)
            driver.find_element(
                By.XPATH,
                '(//span[text()="Airline "]//parent::span//parent::span//parent::label//'
                "parent::div//a)[3]",
            ).click()
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.NAME,
                            'query',
                        )
                    )
                )
            time.sleep(1)
            driver.find_element(By.NAME, "query").send_keys(DEDICATED_AIRLINE, Keys.ENTER)
            time.sleep(1)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]',
                        )
                    )
                )
            )
            select_and_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]'
            )
            time.sleep(2)
            action = ActionChains(driver)
            action.double_click(select_and_airline).perform()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Root Type should be successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.unpublishButtonXpath)
                )
            ).click()
            assert (
                WebDriverWait(driver, 50)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
                .is_displayed()
            )
            logging.info("Saved Successfully")
            utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 60)
            utils.click_on_add_object(driver, "RootType")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))).click()

            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, "rootTypeName"))).send_keys(" "+RANDOM_NAME + "  ")
            driver.find_element(By.NAME, "description").send_keys(RANDOM_DESC)
            driver.find_element(
                By.XPATH,
                '(//span[text()="Airline "]//parent::span//parent::span//parent::label//'
                "parent::div//a)[3]",
            ).click()
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.NAME,
                            'query',
                        )
                    )
                )
            time.sleep(1)
            driver.find_element(By.NAME, "query").send_keys(DEDICATED_AIRLINE, Keys.ENTER)
            time.sleep(1)
            (
                WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,
                            '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]',
                        )
                    )
                )
            )
            select_and_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]'
            )
            time.sleep(2)
            action = ActionChains(driver)
            action.double_click(select_and_airline).perform()
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Save & Publish"]')
                )
            )
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()

            assert WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Root Type already exists with the same Name."]')
                )
            ).is_displayed()
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="OK"])[last()]')
                )
            ).click()

            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, UID.split(" ")[1])
            driver.find_element(
                By.XPATH,
                '(//span[text()="Airline "]//parent::span//parent::span//parent::label//'
                "parent::div//a)[3]",
            ).click()
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.NAME,
                            'query',
                        )
                    )
                )
            time.sleep(1)
            driver.find_element(By.NAME, "query").send_keys(DEDICATED_AIRLINE_NO_STORE, Keys.ENTER)
            time.sleep(1)
            (
                WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,
                            '//div[text()="/Airlines/' + DEDICATED_AIRLINE_NO_STORE + '"]',
                        )
                    )
                )
            )
            select_and_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE_NO_STORE + '"]'
            )
            time.sleep(2)
            action = ActionChains(driver)
            action.double_click(select_and_airline).perform()
            saved = utils.save_and_publish(driver)
            assert saved
            utils.search_by_id(driver, ROOT_TYPE_FOLDER)
            try:
                WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "(//span[contains(@class,'reload')])[2]")
                    )
                )
                driver.find_element(
                    By.XPATH, "(//span[contains(@class,'reload')])[2]"
                ).click()
                (
                    WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located(
                            (By.XPATH, '(//input[@name="query"])')
                        )
                    )
                )
            except:
                logging.info("No Reload button shows")
                pass
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                RANDOM_NAME, Keys.ENTER
            )
            created_root_type = (
                WebDriverWait(driver, 20)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Master Data/Root Type/'
                            + DEDICATED_AIRLINE_NO_STORE
                            + "/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"Root Type object should move automatically under Master Data/Root Type/Airline Name"
                f" folder = {created_root_type}"
            )
            utils.deleteObjectViaUI(driver, UID.split(" ")[1])
            Success_List_Append(
                "test_MKTPL_2466_CRUDRoottype_001_002_005_006_008_011_014",
                "Verify by entering/updating data in below fields",
                "Pass",
            )
            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_AIRLINE_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2466_CRUDRoottype_001_002_005_006_008_011_014",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2466_CRUDRoottype_001_002_005_006_008_011_014",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

def test_MKTPL_2467_RoottypeView_001_003_004_005_006():
    try:
        with utils.services_context_wrapper(
            "def test_MKTPL_2467_RoottypeView_001_003_004_005_006.png"
        ) as driver:
            driver.maximize_window()
            role = ["Login_Airline", "Login_Airline_Manager"]
            for i in role:
                if i =="Login_Airline":
                    utils.Pac_Credentials.Login_Airline(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("Login_Airline")
                elif i =="Login_Airline_Manager":
                    utils.Pac_Credentials.Login_Airline_Manager(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful with Login_Airline_Manager.")
                try:
                    for x in range(7):
                        driver.find_element(
                            By.XPATH,
                            '(//div[@class="x-tool-tool-el x-tool-img x-tool-left "])[last()]',
                        ).click()
                except:
                    logging.info(" Screen loaded on one side properly.")
                utils.search_by_id(driver, DEDICATED_ROOT_TYPE_1_ID)
                assert driver.find_element(By.XPATH, xpath.rootTypeNameXpath).is_displayed()
                assert driver.find_element(By.XPATH, xpath.rootDescriptionXpath).is_displayed()
                assert not len(driver.find_elements(By.XPATH, xpath.saveAndPublishXpath))
                assert not len(driver.find_elements(By.XPATH, xpath.unpublishButtonXpath))
                assert driver.find_element(By.XPATH, xpath.readModeLock).is_displayed()
                logging.info(
                    f"Admin/manager able to view the data of Root Type in read only mode"
                )
                WebDriverWait(driver, 50).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//div[contains(text(),'Root Type')]")
                    )
                ).click()
                credAirline = driver.find_element(By.XPATH,"//div[text()='Root Type']//ancestor::div[5]//span[text()='Cred Automation Airline']")
                actions = ActionChains(driver)
                actions.context_click(credAirline).perform()
                assert not len(driver.find_elements(By.XPATH, "(//span[text()='Add Object'])"))
                WebDriverWait(driver, 50).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//div[contains(text(),'Root Type')]")
                    )
                ).click()
                WebDriverWait(driver, 50).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//div[contains(text(),'Root Type')]")
                    )
                ).click()
                credAirline = driver.find_element(By.XPATH,"//div[text()='Root Type']//ancestor::div[5]//span[text()='"+DEDICATED_ROOT_TYPE_1_NAME+"']")
                actions = ActionChains(driver)
                actions.context_click(credAirline).perform()
                assert not len(driver.find_elements(By.XPATH, "(//span[text()='Add Object'])"))
                WebDriverWait(driver, 50).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//div[contains(text(),'Root Type')]")
                    )
                ).click()
                WebDriverWait(driver, 50).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//div[contains(text(),'Root Type')]")
                    )
                ).click()
                assert not len(driver.find_elements(By.XPATH,"//div[text()='Root Type']//ancestor::div[5]//span[text()='"+DEDICATED_UNPUBLISHED_ROOTTYPE_NAME+"']"))


                
                logging.info(
                    f"Admin/manager not able to add/update data and the unpublished obejct not shown."
                )
                WebDriverWait(driver, 50).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.logout)))
                driver.find_element(By.XPATH, xpath.logout).click()
            
            Success_List_Append(
                "test_MKTPL_2467_RoottypeView_001_003_004_005_006",
                "VVerify by clicking on the Root Type",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2467_RoottypeView_001_003_004_005_006",
            "Verify by clicking on the Root Type",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2467_RoottypeView_001_003_004_005_006", "Verify by clicking on the Root Type", e
        )
        raise e

def test_MKTPL_378_CRUDAircraft_001_002_004_005():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper("test_MKTPL_378_CRUDAircraft_001_002_004_005.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            utils.create_aircraft_pac_admin(driver, RANDOM_NAME)
            logging.info('Aircraft successfully created/updated in the system')

            name = driver.find_element(By.NAME, 'OEMName').get_attribute("value")
            assert name is not None
            logging.info("Name is displayed")
            familyname = driver.find_element(By.NAME, 'familyName').get_attribute("value")
            assert familyname is not None
            logging.info("familyname is displayed")
            modelName = driver.find_element(By.NAME, 'modelName').get_attribute("value")
            assert modelName is not None
            logging.info("modelName is displayed")
            description = driver.find_element(By.NAME, 'description').get_attribute("value")
            assert description is not None
            logging.info("description is displayed")

            utils.search_by_path(driver, "/Master Data/Aircraft")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "query")))
            driver.find_element(By.NAME, "query").send_keys(RANDOM_NAME, Keys.ENTER)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(text(),'"+RANDOM_NAME+"')])[1]")))
            assert driver.find_element(By.XPATH, "(//div[contains(text(),'"+RANDOM_NAME+"')])[1]").is_displayed()
            Success_List_Append("test_MKTPL_378_CRUDAircraft_001_002_004_005",
                                "Verify by entering/updating data in below fields:- OEM Name- Description- Family Name- Model Name- Image", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_378_CRUDAircraft_001_002_004_005",
                            "Verify by entering/updating data in below fields:- OEM Name- Description- Family Name- Model Name- Image", "Fail")
        Failure_Cause_Append("test_MKTPL_378_CRUDAircraft_001_002_004_005",
                             "Verify by entering/updating data in below fields:- OEM Name- Description- Family Name- Model Name- Image",
                             e)
        raise e

def test_MKTPL_528_Taxpercentage_002_003_MKTPL_296_Orderthresholdlimits_002_003_004_MKTPL_882_SalesStrategy_001_002_MKTPL_368_Onboardstore_001_002():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_528_Taxpercentage_002_003_MKTPL_296_Orderthresholdlimits_002_003_004_MKTPL_882_SalesStrategy_001_002_MKTPL_368_Onboardstore_001_002.png"
        ) as driver:
            driver.maximize_window()
            # Login through PAC admin user
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            utils.click_on_add_object(driver, "Stores")
            # Enter store name
            store_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            logging.info("Clicked on ok after entering Store name as {store_name}")
            # input store name
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='storeName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='storeName']").send_keys(
                store_name
            )
            # Click on address info
            address_information = driver.find_element(
                By.XPATH, "//span[contains(text(),'Address Information')]"
            )
            address_information.click()
            # Enter first name
            driver.find_element(By.XPATH, "//input[@name='firstName']").send_keys(
                "First Name - Dummy"
            )
            logging.info("Entered first name")
            # Enter email
            driver.find_element(By.XPATH, "//input[@name='email']").send_keys(
                "<EMAIL>"
            )
            # Click on Configuration
            configuration = driver.find_element(
                By.XPATH, "(//span[contains(text(),'Configuration')])[1]"
            )
            configuration.click()
            # Enter into manage stock and sales strategy
            driver.find_element(By.XPATH, "//input[@name='manageStock']").send_keys(
                "Yes", Keys.ENTER
            )
            driver.find_element(By.XPATH,"//span[contains(text(),'Sales Strategy')]//ancestor::label//parent::div//div[contains(@id,'trigger-picker')]").click()
            assert driver.find_element(By.XPATH, '//li[contains(text(),"D2C")]').is_displayed()
            assert driver.find_element(By.XPATH, '//li[contains(text(),"Marketplace")]').is_displayed()
            driver.find_element(By.XPATH, "//input[@name='salesStrategy']").send_keys(
                "D2C"
            )
            time.sleep(1)
            driver.find_element(By.XPATH, "//input[@name='salesStrategy']").send_keys(
                Keys.ENTER
            )
            time.sleep(1)
            driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'Order Threshold Value')]//ancestor::div[1]//input)[1]",
            ).click()
            driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'Order Threshold Value')]//ancestor::div[1]//input)[1]",
            ).send_keys("1.56", Keys.ENTER)
            
            Threshold = driver.find_element(By.XPATH,
                "(//span[contains(text(),'Order Threshold Value')]//ancestor::div[1]//input)[2]").get_attribute("value")
            assert Threshold is not None
            logging.info("Threshold is displayed")

            assert driver.find_element(By.XPATH, '//span[text()="Auto Approve Catalog:"]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[text()="Associated Airline"]').is_displayed()

            WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//input[@name="taxPercentage"]')))
            tax_input = driver.find_element(By.XPATH, '//input[@name="taxPercentage"]')
            tax_input.send_keys("abc")
            driver.find_element(By.XPATH,'//span[text()="Tax Percentage:"]').click()
            time.sleep(3)
            get_val_tax_percentage = tax_input.get_attribute('aria-valuenow')
            logging.info(get_val_tax_percentage)
            assert get_val_tax_percentage is not "abc"
            
            tax_input = driver.find_element(By.XPATH, '//input[@name="taxPercentage"]')
            tax_input.send_keys("zero")
            driver.find_element(By.XPATH,'//span[text()="Tax Percentage:"]').click()
            time.sleep(3)
            get_val_tax_percentage = tax_input.get_attribute('aria-valuenow')
            logging.info(get_val_tax_percentage)
            assert get_val_tax_percentage is not "zero"
            
            tax_input.clear()
            tax_input.send_keys("@@")
            driver.find_element(By.XPATH,'//span[text()="Tax Percentage:"]').click()
            time.sleep(3)
            get_val_tax_percentage = tax_input.get_attribute('aria-valuenow')
            assert get_val_tax_percentage is not "@@"
            logging.info('testtest')
            
            tax_input.clear()
            tax_input.send_keys(25)
            driver.find_element(By.XPATH,'//span[text()="Tax Percentage:"]').click()
            time.sleep(3)
            get_val_tax_percentage = tax_input.get_attribute('aria-valuenow')
            assert get_val_tax_percentage == '25'
        
            # Get UID
            UID = utils.get_uid(driver)
            # Click on Save and Publish
            saved = utils.save_and_publish(driver)
            assert saved
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            # WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.NAME, "username")))
            # utils.Pac_Credentials.Login_store(driver)

            # WebDriverWait(driver, 20).until(
            #     EC.presence_of_element_located((By.XPATH, "//div[text()='Store Profile']")))

            Success_List_Append(
                "test_MKTPL_528_Taxpercentage_002_003_MKTPL_296_Orderthresholdlimits_002_003_004_MKTPL_882_SalesStrategy_001_002_MKTPL_368_Onboardstore_001_002",
                "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_528_Taxpercentage_002_003_MKTPL_296_Orderthresholdlimits_002_003_004_MKTPL_882_SalesStrategy_001_002_MKTPL_368_Onboardstore_001_002",
            "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_528_Taxpercentage_002_003_MKTPL_296_Orderthresholdlimits_002_003_004_MKTPL_882_SalesStrategy_001_002_MKTPL_368_Onboardstore_001_002",
            "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
            e,
        )
        raise e

def test_MKTPL_528_Taxpercentage_004_005():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_528_Taxpercentage_004_005.png"
        ) as driver:
            driver.maximize_window()
            # Login through PAC admin user
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            utils.click_on_add_object(driver, "Stores")
            # Enter store name
            store_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            logging.info("Clicked on ok after entering Store name as {store_name}")
            # input store name
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='storeName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='storeName']").send_keys(
                store_name
            )
            # Click on address info
            address_information = driver.find_element(
                By.XPATH, "//span[contains(text(),'Address Information')]"
            )
            address_information.click()
            # Enter first name
            driver.find_element(By.XPATH, "//input[@name='firstName']").send_keys(
                "First Name - Dummy"
            )
            logging.info("Entered first name")
            # Enter email
            driver.find_element(By.XPATH, "//input[@name='email']").send_keys(
                "<EMAIL>"
            )
            # Click on Configuration
            configuration = driver.find_element(
                By.XPATH, "(//span[contains(text(),'Configuration')])[1]"
            )
            configuration.click()
            WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.NAME, "manageStock")))
            # Enter into manage stock and sales strategy
            driver.find_element(By.XPATH, "//input[@name='manageStock']").send_keys(
                "Yes", Keys.ENTER
            )
            driver.find_element(By.XPATH, "//input[@name='salesStrategy']").send_keys(
                "Marketplace", Keys.ENTER
            )
            time.sleep(1)
            driver.find_element(By.XPATH, "//input[@name='salesStrategy']").send_keys(
                Keys.ENTER
            )
            time.sleep(1)
            configuration.click()
            driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'Order Threshold Value')]//ancestor::div[1]//input)[1]",
            ).click()
            driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'Order Threshold Value')]//ancestor::div[1]//input)[1]",
            ).send_keys("1", Keys.ENTER)

            taxPercentage = driver.find_element(By.NAME, 'taxPercentage').get_attribute("aria-disabled")
            assert taxPercentage 
            logging.info("taxPercentage is disabled and empty")
            

            # Get UID
            UID = utils.get_uid(driver)
            # Click on Save and Publish
            saved = utils.save_and_publish(driver)
            assert saved
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            # WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.NAME, "username")))
            # utils.Pac_Credentials.Login_store(driver)

            # WebDriverWait(driver, 20).until(
            #     EC.presence_of_element_located((By.XPATH, "//div[text()='Store Profile']")))

            Success_List_Append(
                "test_MKTPL_528_Taxpercentage_004_005",
                "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_528_Taxpercentage_004_005",
            "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_528_Taxpercentage_004_005",
            "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
            e,
        )
        raise e

def test_MKTPL_380_CRUDAirport_001_002_005():
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_CODE = "".join(random.choices(string.digits, k=5))
    icao_code_dummy = "".join(random.choices(string.ascii_letters, k=6))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_380_CRUDAirport_001_002_005.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)

            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Airports")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "airportName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "icao_code").send_keys(icao_code_dummy)
            driver.find_element(By.NAME, "IATACode").send_keys(RANDOM_CODE)
            driver.find_element(By.NAME, "address").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "city").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "state").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "zipCode").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "country").send_keys("India")
            driver.find_element(By.NAME, "timeZone").send_keys("Asia/Kolkata")
            driver.find_element(By.NAME, "timeZone").send_keys(Keys.ENTER)

            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Airport is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)

            icao_code = driver.find_element(By.NAME, 'icao_code').get_attribute("value")
            assert icao_code is not None
            logging.info("icao_code is displayed")

            IATACode = driver.find_element(By.NAME, 'IATACode').get_attribute("value")
            assert IATACode is not None
            logging.info("IATACode is displayed")

            address = driver.find_element(By.NAME, 'address').get_attribute("value")
            assert address is not None
            logging.info("address is displayed")

            city = driver.find_element(By.NAME, 'city').get_attribute("value")
            assert city is not None
            logging.info("city is displayed")

            state = driver.find_element(By.NAME, 'state').get_attribute("value")
            assert state is not None
            logging.info("state is displayed")

            zipCode = driver.find_element(By.NAME, 'zipCode').get_attribute("value")
            assert zipCode is not None
            logging.info("zipCode is displayed")

            country = driver.find_element(By.NAME, 'country').get_attribute("value")
            assert country is not None
            logging.info("country is displayed")
        
            Success_List_Append(
                "test_MKTPL_380_CRUDAirport_001_002_005",
                "Verify Airport is created/updated in the system, "
                "moved to Master Data/Airport folder.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_380_CRUDAirport_001_002_005",
            "Verify Airport is created/updated in the system, "
            "moved to Master Data/RouteGroup folder.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_380_CRUDAirport_001_002_005",
            "Verify Airport is created/updated in the system, "
            "moved to Master Data/RouteGroup folder.",
            e,
        )
        raise e

def test_MKTPL_2217_UITemplate_005_008_011_013():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_DESC = "".join(random.choices(string.ascii_letters, k=20))

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2217_UITemplate_005_008_011_013.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            utils.click_on_add_object(driver, "UITemplate")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            logging.info("Entered Name")

            assert driver.find_element(By.XPATH, xpath.UiTemplateNameXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.UiTemplateAirlineXpath).is_displayed()
            logging.info("UI Template name and Airline displayed")

            driver.find_element(
                By.XPATH,
                '(//span[text()="Airline "]//parent::span//parent::span//parent::label//'
                "parent::div//a)[3]",
            ).click()
            logging.info("Clicked")
            
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "templateName")))
                .send_keys(RANDOM_NAME)
            )
            logging.info("Entered name")
            
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.NAME,
                            'query',
                        )
                    )
                )
            time.sleep(1)
            driver.find_element(By.NAME, "query").send_keys(DEDICATED_AIRLINE)
            driver.find_element(By.NAME, "query").send_keys(Keys.ENTER)
            
            time.sleep(1)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]',
                        )
                    )
                )
            )
            select_and_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]'
            )
            time.sleep(2)
            action = ActionChains(driver)
            action.double_click(select_and_airline).perform()
            logging.info("Double click done")
            
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"UITemplate should be successfully created/updated in the system = {saved}"
            )

            UID = utils.get_uid(driver)
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.unpublishButtonXpath)
                )
            ).click()
            logging.info("Clicked on Unpublish button")
            
            assert (
                WebDriverWait(driver, 50)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
                .is_displayed()
            )
            logging.info("Unpublished Successfully")


            utils.close_All_Tabs(driver)
            utils.wait_for_style_attribute(driver, 60)
            utils.click_on_add_object(driver, "UITemplate")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, "(" + xpath.okButtonXpath + ")[last()]"))
                )
                .click()
            )
            logging.info("Second time object is created")
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "templateName")))
                .send_keys(RANDOM_NAME)
            )
            logging.info("Entered name")
            
            driver.find_element(
                By.XPATH,
                '(//span[text()="Airline "]//parent::span//parent::span//parent::label//'
                "parent::div//a)[3]",
            ).click()
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.NAME,
                            'query',
                        )
                    )
                )
            time.sleep(1)
            driver.find_element(By.NAME, "query").send_keys(DEDICATED_AIRLINE, Keys.ENTER)
            time.sleep(1)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]',
                        )
                    )
                )
            )
            select_and_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]'
            )
            time.sleep(2)
            action = ActionChains(driver)
            action.double_click(select_and_airline).perform()
            logging.info("Double click")
            
            saved = utils.save_and_publish(driver)
            assert WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="UI Template already exists with the same Name."]')
                )
            ).is_displayed()
            logging.info("Validation displayed as UI Template already exists with the same Name.")
            
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '(//span[text()="OK"])[last()]'))
                )
                .click()
            )
            logging.info("validation msg is displayed when same name template is created with same airline");

            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, UID.split(" ")[1])
            driver.find_element(
                By.XPATH,
                '(//span[text()="Airline "]//parent::span//parent::span//parent::label//'
                "parent::div//a)[3]",
            ).click()
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.NAME,
                            'query',
                        )
                    )
                )
            time.sleep(1)
            driver.find_element(By.NAME, "query").send_keys(DEDICATED_AIRLINE_NO_STORE, Keys.ENTER)
            time.sleep(1)
            (
                WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,
                            '//div[text()="/Airlines/' + DEDICATED_AIRLINE_NO_STORE + '"]',
                        )
                    )
                )
            )
            select_and_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE_NO_STORE + '"]'
            )
            time.sleep(2)
            action = ActionChains(driver)
            action.double_click(select_and_airline).perform()
            logging.info("Double click")
            saved = utils.save_and_publish(driver)
            assert saved
            utils.search_by_id(driver, UI_TEMPLATE_FOLDER)
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                RANDOM_NAME, Keys.ENTER
            )
            created_root_type = (
                WebDriverWait(driver, 20)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Master Data/UI Templates/'
                            + DEDICATED_AIRLINE_NO_STORE
                            + "/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"UI Template object should move automatically under Master Data/UI Templates/Airline Name"
                f" folder = {created_root_type}"
            )

            Success_List_Append(
                "test_MKTPL_2217_UITemplate_005_008_011_013",
                "Verify by entering/updating data in below fields",
                "Pass",
            )
            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_AIRLINE_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2217_UITemplate_005_008_011_013",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2217_UITemplate_005_008_011_013",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

def test_MKTPL_2217_UITemplate_006():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2217_UITemplate_006.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            utils.click_on_add_object(driver, "UITemplate")

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            name_field = (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "templateName")))
                .is_displayed()
            )
            driver.find_element(By.NAME, "templateName").send_keys(RANDOM_NAME)
            airline = driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).is_displayed()
            assert [name_field, airline]
            logging.info(
                f"Following field should display:- Name = {name_field} , Airline = {airline}"
            )
            UID = utils.get_uid(driver)
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            validation = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Validation failed: Empty mandatory field [ airline ]"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert validation
            logging.info(f"Validation message should display  =  {validation}")
            driver.find_element(By.XPATH, '(//span[text()="OK"])[2]').click()
            driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(EC.element_to_be_clickable((By.XPATH, '//input[@name="query"]')))
                .send_keys("Cred Automation Airline", Keys.ENTER)
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="/Airlines/Cred Automation Airline"]')
                )
            )
            select_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/Cred Automation Airline"]'
            )
            action = ActionChains(driver)
            action.double_click(select_airline).perform()
            time.sleep(1)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="/Airlines/Cred Automation Airline" and @class = "x-form-display-field x-form-display-field-default"]',
                    )
                )
            )
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"UI template should be successfully created/updated in the system = {saved}"
            )
            
            # 006
            utils.deleteObjectViaUI(driver, UID.split(" ")[1])
            Success_List_Append(
                "test_MKTPL_2217_UITemplate_006",
                "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2217_UITemplate_006",
            "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2217_UITemplate_006",
            "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
            e,
        )
        raise e

def test_MKTPL_383_CRUDUser_001_002_004_005_006():
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    logging.info(RANDOM_NAME)
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_383_CRUDUser_001_002_004_005_006.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Users")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            time.sleep(5)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="First Name "]'))
            ).click()
            assert driver.find_element(By.XPATH, '//span[text()="First Name "]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[text()="Last Name:"]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[text()="Is Active:"]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[text()="Email "]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[text()="User Type "]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[text()="User Role "]').is_displayed()
            logging.info("Fields are verified successfully")
            utils.save_and_publish(driver)

            assert WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Validation failed: Empty mandatory field [ firstName ] / Empty mandatory field [ email ] / Empty mandatory field [ userType ] / Empty mandatory field [ userRole ]"]')
                )
            ).is_displayed()
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="OK"])[last()]')
                )
            ).click()
            logging.info("verified validation message")
            wait.until(
                EC.presence_of_element_located((By.NAME, "firstName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys("testlastname")
            driver.find_element(By.NAME, "isActive").click()
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            driver.find_element(By.NAME, "userType").send_keys("Airline")
            driver.find_element(By.NAME, "userRole").send_keys("airlineadmin")
            time.sleep(2)
            airline = wait.until(EC.visibility_of_element_located((By.NAME, "airline")))
            airline.clear()
            airline.send_keys(DEDICATED_AIRLINE)
            # driver.implicitly_wait(0)
            # wait.until(
            #     EC.presence_of_element_located(
            #         (By.XPATH, '//span[text()="Mail Subscriptions"]')
            #     )
            # ).click()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"User is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            assert driver.find_element(By.XPATH, "(//input[@name='firstName'])[last()]").is_displayed()
            assert driver.find_element(By.XPATH, "(//input[@name='lastName'])[last()]").is_displayed()
            # assert driver.find_element(By.XPATH, "(//input[@name='isActive'])[last()]").is_displayed()
            assert driver.find_element(By.XPATH, "(//input[@name='email'])[last()]").is_displayed()
            assert driver.find_element(By.XPATH, "(//input[@name='userType'])[last()]").is_displayed()
            assert driver.find_element(By.XPATH, "(//input[@name='userRole'])[last()]").is_displayed()
            logging.info("User data displayed")
            utils.close_All_Tabs(driver)
            RANDOM_PASSWORD = utils.setPassword(driver, RANDOM_NAME)
            #Logout
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out")
            WebDriverWait(driver,20).until(EC.visibility_of_element_located((By.NAME,"username")))
            driver.find_element(By.XPATH, xpath.userName).send_keys(
                RANDOM_NAME + "@gmail.com"
            )
            driver.find_element(By.XPATH, xpath.password).send_keys(
                RANDOM_PASSWORD
            )
            driver.find_element(By.XPATH, xpath.submit).click()
            WebDriverWait(driver,20).until(EC.visibility_of_element_located((By.XPATH,"//div[contains(text(), 'Login failed!')]")))
            logging.info("Successfully shown as - Login failed! for inavtive user")
            
            Success_List_Append(
                "test_MKTPL_383_CRUDUser_001_002_004_005_006",
                "Verify User is created/updated in the system, "
                "moved to Airline name folder/Users folder and receive an email "
                "for successfull registration.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_383_CRUDUser_001_002_004_005_006",
            "Verify User is created/updated in the system, "
            "moved to Airline name folder/Users folder and receive an email "
            "for successfull registration.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_383_CRUDUser_001_002_004_005_006",
            "Verify User is created/updated in the system, "
            "moved to Airline name folder/Users folder and receive an email "
            "for successfull registration.",
            e,
        )
        raise e

def test_MKTPL_516_379_Airportimport_005_006_007_008_009():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_516_379_Airportimport_005_006_007_008_009.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)

            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.csvImport)))
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.upload)))
                .click()
            )
            assert driver.find_element(
                By.XPATH, xpath.mandatoryFieldErrorMessage
            ).is_displayed()
            logging.info("mandatory field msg verified")

            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.okButtonXpath)))
                .click()
            )

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[@id="importType-trigger-picker"]')
                    )
                )
                .click()
            )

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//li[text()="Airports"]')
                    )
                )
                .click()
            )

            assert utils.upload_csv_with_specific_validation(
                "1200x1200.png",
                xpath.csvFileSelectButton,
                "File Import: Uploaded file should be CSV file with .csv extension.",
            )

            logging.info("only .csv upload msg vaidation done")

            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.okButtonXpath)))
                .click()
            )
            utils.update_csv("Airports_Sample_Validation.csv", "airportName_en", "")
            utils.update_csv("Airports_Sample_Validation.csv", "country", "testing")
            utils.update_csv("Airports_Sample_Validation.csv", "timeZone", "Testing")
            assert utils.upload_csv_with_specific_validation(
                "Airports_Sample_Validation.csv",
                xpath.csvFileSelectButton,
                "CSV Uploaded Successfully",
            )
            utils.close_All_Tabs(driver)
            utils.Assets_import_Pac_admin(driver)
            utils.verify_import_logs_pac_admin(
                driver, "airport", "Name cannot be blank. It is a mandatory field | Country not matched with master data | Timezone does not matched with master data"
            )
            utils.update_csv("Airports_Sample_Validation.csv", "airportName_en", "yuyuyuy")
            utils.update_csv("Airports_Sample_Validation.csv", "country", "IN")
            utils.update_csv("Airports_Sample_Validation.csv", "timeZone", "Africa/Accra")
            logging.info("import log verify msg done")

            Success_List_Append(
                "test_MKTPL_516_379_Airportimport_005_006_007_008_009",
                "Verify by clicking on Upload button and/or CSV file field is empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_516_379_Airportimport_005_006_007_008_009",
            "Verify by clicking on Upload button and/or CSV file field is empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_516_379_Airportimport_005_006_007_008_009",
            "Verify by clicking on Upload button and/or CSV file field is empty",
            e,
        )
        raise e

def test_MKTPL_517_377_Aircraftimport_005_006_007():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_517_377_Aircraftimport_005_006_007.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)

            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.csvImport)))
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.upload)))
                .click()
            )
            assert driver.find_element(
                By.XPATH, xpath.mandatoryFieldErrorMessage
            ).is_displayed()
            logging.info("mandatory field msg verified")

            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.okButtonXpath)))
                .click()
            )

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[@id="importType-trigger-picker"]')
                    )
                )
                .click()
            )

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//li[text()="AirCraft"]')
                    )
                )
                .click()
            )

            assert utils.upload_csv_with_specific_validation(
                "1200x1200.png",
                xpath.csvFileSelectButton,
                "File Import: Uploaded file should be CSV file with .csv extension.",
            )

            logging.info("only .csv upload msg vaidation done")

            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.okButtonXpath)))
                .click()
            )
            utils.update_csv("AirCraft_Sample_Validation.csv", "OEMName_en", "")
            
            assert utils.upload_csv_with_specific_validation(
                "AirCraft_Sample_Validation.csv",
                xpath.csvFileSelectButton,
                "CSV Uploaded Successfully",
            )
            utils.close_All_Tabs(driver)
            utils.Assets_import_Pac_admin(driver)
            utils.verify_import_logs_pac_admin(
                driver, "aircraft", "Name cannot be blank. It is a mandatory field")
            utils.update_csv("AirCraft_Sample_Validation.csv", "OEMName_en", "yuyuyuy")
            
            logging.info("import log verify msg done")

            Success_List_Append(
                "test_MKTPL_517_377_Aircraftimport_005_006_007",
                "Verify by clicking on Upload button and/or CSV file field is empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_517_377_Aircraftimport_005_006_007",
            "Verify by clicking on Upload button and/or CSV file field is empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_517_377_Aircraftimport_005_006_007",
            "Verify by clicking on Upload button and/or CSV file field is empty",
            e,
        )
        raise e

def test_MKTPL_385_Manageprofile_001_002_003():
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_385_Manageprofile_001_002_003.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)

            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Airline")
            time.sleep(1)
            mouse_hover = ActionChains(driver)
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            mouse_hover.move_to_element(airline).perform()
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[2]')
            airline.click()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "airlineName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "description").send_keys(
                "creating airline description for automation test"
            )
            driver.find_element(By.NAME, "ICAOCode").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "iataCode").send_keys(RANDOM_NAME)
            driver.find_element(
                By.XPATH, '//span[text()="Contact & Address Information"]'
            ).click()
            driver.find_element(By.NAME, "firstName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "company").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "addressLine1").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "addressLine2").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "city").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "state").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "zipCode").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@Gmail.com")
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Airline is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            utils.close_All_Tabs(driver)
            
            Success_List_Append(
                "test_MKTPL_385_Manageprofile_001_002_003",
                "Verify Airline is created/updated in the system, "
                "moved to Airlines folder.",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_385_Manageprofile_001_002_003",
            "Verify Airline is created/updated in the system, moved to Airlines folder.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_385_Manageprofile_001_002_003",
            "Verify Airline is created/updated in the system, moved to Airlines folder.",
            e,
        )
        raise e

def test_MKTPL_374_AirlineProfile_001_002_005():
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_374_AirlineProfile_001_002_005.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)

            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Airline")
            time.sleep(1)
            mouse_hover = ActionChains(driver)
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            mouse_hover.move_to_element(airline).perform()
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[2]')
            airline.click()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "airlineName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "description").send_keys(
                "creating airline description for automation test"
            )
            driver.find_element(By.NAME, "ICAOCode").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "iataCode").send_keys(RANDOM_NAME)
            driver.find_element(
                By.XPATH, '//span[text()="Contact & Address Information"]'
            ).click()
            driver.find_element(By.NAME, "firstName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "company").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "addressLine1").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "addressLine2").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "city").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "state").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "zipCode").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@Gmail.com")
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Airline is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)

            airlineName = driver.find_element(By.NAME, "airlineName").get_attribute("value")
            assert airlineName is not None
            logging.info("airlineName is displayed")

            description = driver.find_element(By.NAME, "description").get_attribute("value")
            assert description is not None
            logging.info("description is displayed")

            ICAOCode = driver.find_element(By.NAME, "ICAOCode").get_attribute("value")
            assert ICAOCode is not None
            logging.info("ICAOCode is displayed")

            iataCode = driver.find_element(By.NAME, "iataCode").get_attribute("value")
            assert iataCode is not None
            logging.info("iataCode is displayed")

            firstName = driver.find_element(By.NAME, "firstName").get_attribute("value")
            assert firstName is not None
            logging.info("firstName is displayed")

            utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 60)
            # wait.until(
            #     EC.presence_of_element_located((By.XPATH, '(//span[text()="Home"]/..//a)[2]')))
            utils.search_by_id(driver, AIRLINES_FOLDER)
            time.sleep(3)
            try:
                (
                    WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located(
                            (By.XPATH, xpath.selectClassXpath)
                        )
                    )
                )
                select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
                select_class.click()
                select_class.send_keys("Airline", Keys.ENTER)
                select_class.send_keys(Keys.ENTER)
            except:
                logging.info("SelectCalss doesnot show")
                pass
            # WebDriverWait(driver, 60).until(
            #     EC.element_to_be_clickable((By.XPATH, "(//span[contains(@class,'reload')])[1]")))
            # driver.find_element(By.XPATH, "(//span[contains(@class,'reload')])[1]").click()
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="query"]'))
            )
            driver.find_element(By.XPATH, '//input[@name="query"]').send_keys(
                RANDOM_NAME, Keys.ENTER
            )
            new_airline = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[text()="' + RANDOM_NAME + '"])[1]')
                )
            ).is_displayed()
            assert new_airline
            logging.info(
                f"New created Airline is moved under Airlines folder = {new_airline}"
            )
            logging.info(
                "Verify Airline is created/updated in the system, moved to Airlines folder."
            )
            Success_List_Append(
                "test_MKTPL_374_AirlineProfile_001_002_005",
                "Verify Airline is created/updated in the system, "
                "moved to Airlines folder.",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_374_AirlineProfile_001_002_005",
            "Verify Airline is created/updated in the system, moved to Airlines folder.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_374_AirlineProfile_001_002_005",
            "Verify Airline is created/updated in the system, moved to Airlines folder.",
            e,
        )
        raise e

def test_MKTPL_382_CRUDUser_001_002_004_005_006():
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    logging.info(RANDOM_NAME)
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_382_CRUDUser_001_002_004_005_006.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Users")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            time.sleep(5)

            assert driver.find_element(By.XPATH, '//span[contains(text(),"First Name")]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[contains(text(),"Last Name")]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[contains(text(),"Is Active")]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[contains(text(),"Email")]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[contains(text(),"User Type")]').is_displayed()
            assert driver.find_element(By.XPATH, '//span[contains(text(),"User Role")]').is_displayed()
            logging.info("Fields are verified successfully")
            utils.save_and_publish(driver)

            assert WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Validation failed: Empty mandatory field [ firstName ] / Empty mandatory field [ email ] / Empty mandatory field [ userType ] / Empty mandatory field [ userRole ]"]')
                )
            ).is_displayed()
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="OK"])[last()]')
                )
            ).click()
            logging.info("verified validation message")
            wait.until(
                EC.presence_of_element_located((By.NAME, "firstName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys("testlastname")
            driver.find_element(By.NAME, "isActive").click()
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            driver.find_element(By.NAME, "userType").send_keys("Store")
            driver.find_element(By.NAME, "userRole").send_keys("storeadmin")
            time.sleep(2)
            airline = wait.until(EC.visibility_of_element_located((By.NAME, "store")))
            airline.clear()
            airline.send_keys(DEDICATED_STORE)
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"User is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            assert driver.find_element(By.XPATH, "(//input[@name='firstName'])[last()]").is_displayed()
            assert driver.find_element(By.XPATH, "(//input[@name='lastName'])[last()]").is_displayed()
            # assert driver.find_element(By.XPATH, "(//input[@name='isActive'])[last()]").is_displayed()
            assert driver.find_element(By.XPATH, "(//input[@name='email'])[last()]").is_displayed()
            assert driver.find_element(By.XPATH, "(//input[@name='userType'])[last()]").is_displayed()
            assert driver.find_element(By.XPATH, "(//input[@name='userRole'])[last()]").is_displayed()
            logging.info("User data displayed")
            utils.close_All_Tabs(driver)
            RANDOM_PASSWORD = utils.setPassword(driver, RANDOM_NAME)
            #Logout
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out")
            WebDriverWait(driver,20).until(EC.visibility_of_element_located((By.NAME,"username")))
            driver.find_element(By.XPATH, xpath.userName).send_keys(
                RANDOM_NAME + "@gmail.com"
            )
            driver.find_element(By.XPATH, xpath.password).send_keys(
                RANDOM_PASSWORD
            )
            driver.find_element(By.XPATH, xpath.submit).click()
            WebDriverWait(driver,20).until(EC.visibility_of_element_located((By.XPATH,"//div[contains(text(), 'Login failed!')]")))
            logging.info("Successfully shown as - Login failed! for inavtive user")
            
            Success_List_Append(
                "test_MKTPL_382_CRUDUser_001_002_004_005_006",
                "Verify User is created/updated in the system, "
                "moved to Airline name folder/Users folder and receive an email "
                "for successfull registration.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_382_CRUDUser_001_002_004_005_006",
            "Verify User is created/updated in the system, "
            "moved to Airline name folder/Users folder and receive an email "
            "for successfull registration.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_382_CRUDUser_001_002_004_005_006",
            "Verify User is created/updated in the system, "
            "moved to Airline name folder/Users folder and receive an email "
            "for successfull registration.",
            e,
        )
        raise e

def test_MKTPL_5458_ExportImportUiTemplate_002_003_MKTPL_5529_ExportImportMealcode_002_003():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5458_ExportImportUiTemplate_002_003_MKTPL_5529_ExportImportMealcode_002_003.png"
        ) as driver:
            driver.maximize_window()
            
            data = ["UITemplate", "MealCode"]
            for i in data:
                logging.info(i)
                wait = WebDriverWait(driver, 60)
                if i =="UITemplate":
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    utils.wait_for_style_attribute(driver, 60)
                    utils.search_by_path(driver, "/Master Data/UI Templates/Cred Automation Airline")
                elif i =="MealCode":
                    utils.Pac_Credentials.Login_Airline(driver)
                    act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    wait = WebDriverWait(driver, 60)
                    utils.wait_for_style_attribute(driver, 60)
                    driver.find_element(By.XPATH, "//span[text()='Meal Code']").click()
                wait.until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(@id, 'trigger-picker')])[1]")))
                driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
                logging.info("Export CSV")
                if i == "UITemplate":
                    assert driver.find_element(By.XPATH, "//div[text()='Please Select the UI Template']").is_displayed()
                    driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
                    logging.info("Validation displayed as: Please Select the UI Template")
                elif i == "MealCode":
                    assert driver.find_element(By.XPATH, "//div[text()='Please Select the Meal Code']").is_displayed()
                    driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
                    logging.info("Validation displayed as: Please Select the Meal Code")
                # 003
                time.sleep(3)
                wait.until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(@id, 'trigger-picker')])[1]"))).click()
                logging.info("Click on dropdown")
                # driver.find_element(By.XPATH, "(//div[contains(@id, 'trigger-picker')])[1]").click()
                wait.until(EC.visibility_of_element_located((By.XPATH, "//li[text()='100']"))).click()
                # driver.find_element(By.XPATH, "//li[text()='100']").click()
                logging.info("Select 100")
                utils.wait_for_style_attribute(driver, 60)
                x = driver.find_element(By.XPATH, "(//span[@class = 'x-grid-checkcolumn' and @role = 'button']//ancestor::td//parent::tr//td//div)[1]")
                x.click()
                driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
                logging.info("Export CSV")
                # Check validation for unpublished object - export csv
                if i == "UITemplate":
                    assert driver.find_element(By.XPATH, "//div[text()='Please Select only Published UI Template']").is_displayed()
                    driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
                    logging.info("Validation dispplayed as: Please Select only Published UI Template")
                elif i == "MealCode":
                    assert driver.find_element(By.XPATH, "//div[text()='Please Select only Published Meal Code']").is_displayed()
                    driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
                    logging.info("Validation dispplayed as: Please Select only Published Meal Code")
                driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                # logout
                logging.info("Logged out")
                WebDriverWait(driver,20).until(EC.visibility_of_element_located((By.NAME,"username")))
                logging.info(f"CSV file should be download on the system and sample records should display in CSV")
            Success_List_Append(
                "test_MKTPL_5458_ExportImportUiTemplate_002_003_MKTPL_5529_ExportImportMealcode_002_003",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5458_ExportImportUiTemplate_002_003_MKTPL_5529_ExportImportMealcode_002_003",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5458_ExportImportUiTemplate_002_003_MKTPL_5529_ExportImportMealcode_002_003",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

def test_MKTPL_5458_ExportImportUiTemplate_010_011_012():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5458_ExportImportUiTemplate_010_011_012.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            import_data_type = WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.NAME, xpath.importTypeName)))
            import_data_type.send_keys("UiTemplate")
            import_data_type.send_keys(Keys.ENTER)
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.mandatoryFieldErrorMessage)))
            logging.info("Validation displayed for mandatory field")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            time.sleep(3)
            utils.close_All_Tabs(driver)
            utils.csv_import_pac_admin(driver, "UiTemplate")
            logging.info("Selected uitemplate and airline")
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.mandatoryFieldErrorMessage)))
            logging.info("Validation displayed for mandatory field")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            time.sleep(3)
            utils.upload_csv_with_specific_validation("invalid_file.ppt", xpath.csvFileSelectButton, "File Import: Uploaded file should be CSV file with .csv extension.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            
            Success_List_Append(
                "test_MKTPL_5458_ExportImportUiTemplate_010_011_012",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5458_ExportImportUiTemplate_010_011_012",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5458_ExportImportUiTemplate_010_011_012",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
            e,
        )
        raise e

def test_MKTPL_5528_ExportImportRoottype_011_012_013():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5528_ExportImportRoottype_011_012_013.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            import_data_type = WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.NAME, xpath.importTypeName)))
            import_data_type.send_keys("RootType")
            import_data_type.send_keys(Keys.ENTER)
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.mandatoryFieldErrorMessage)))
            logging.info("Validation displayed for mandatory field")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            time.sleep(3)
            utils.close_All_Tabs(driver)
            utils.csv_import_pac_admin(driver, "RootType")
            logging.info("Selected uitemplate and airline")
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.mandatoryFieldErrorMessage)))
            logging.info("Validation displayed for mandatory field")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            time.sleep(3)
            utils.upload_csv_with_specific_validation("invalid_file.ppt", xpath.csvFileSelectButton, "File Import: Uploaded file should be CSV file with .csv extension.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            
            Success_List_Append(
                "test_MKTPL_5528_ExportImportRoottype_011_012_013",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5528_ExportImportRoottype_011_012_013",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5528_ExportImportRoottype_011_012_013",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
            e,
        )
        raise e

def test_MKTPL_5529_ExportImportMealcode_016_017():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5529_ExportImportMealcode_016_017.png"
        ) as driver:
            driver.maximize_window()
            wait = WebDriverWait(driver, 60)
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            utils.search_by_path(driver, "/Cred Automation Airline/Meal Code")
            wait.until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(@id, 'trigger-picker')])[1]")))
            driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
            logging.info("Export CSV")
            assert driver.find_element(By.XPATH, "//div[text()='Please Select the Meal Code']").is_displayed()
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            logging.info("Validation dispplayed as: Please Select the Meal Code")
            # 003
            time.sleep(3)
            x = driver.find_element(By.XPATH, "(//span[@class = 'x-grid-checkcolumn' and @role = 'button']//ancestor::td//parent::tr//td//div)[1]")
            x.click()
            time.sleep(3)
            driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
            logging.info("Export CSV")
            # Check validation for unpublished object - export csv
            assert driver.find_element(By.XPATH, "//div[text()='Please Select only Published Meal Code']").is_displayed()
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            logging.info("Validation dispplayed as: Please Select only Published Meal Code")
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            Success_List_Append(
                "test_MKTPL_5529_ExportImportMealcode_016_017",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5529_ExportImportMealcode_016_017",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5529_ExportImportMealcode_016_017",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

def test_MKTPL_5529_ExportImportMealcode_024_025_026():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5529_ExportImportMealcode_024_025_026.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            import_data_type = WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.NAME, xpath.importTypeName)))
            import_data_type.send_keys("Mealcode")
            import_data_type.send_keys(Keys.ENTER)
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.mandatoryFieldErrorMessage)))
            logging.info("Validation displayed for mandatory field")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            time.sleep(3)
            utils.close_All_Tabs(driver)
            utils.csv_import_pac_admin(driver, "Mealcode")
            logging.info("Selected uitemplate and airline")
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.mandatoryFieldErrorMessage)))
            logging.info("Validation displayed for mandatory field")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            time.sleep(3)
            utils.upload_csv_with_specific_validation("invalid_file.ppt", xpath.csvFileSelectButton, "File Import: Uploaded file should be CSV file with .csv extension.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            
            Success_List_Append(
                "test_MKTPL_5529_ExportImportMealcode_024_025_026",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5529_ExportImportMealcode_024_025_026",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5529_ExportImportMealcode_024_025_026",
                "Verify by clicking on Upload button and airline field and/or CSV file field is empty",
            e,
        )
        raise e

def test_MKTPL_372_CRUDRoutegroup_002_005():
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_372_CRUDRoutegroup_002_005.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "RouteGroup")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath))
            )
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            wait.until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Validation failed: Empty mandatory field [ name ]')]"))
            )
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            logging.info(
                "Validation shown for empty mandatory field"
            )
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, DEDICATED_ROUTE_GROUP_1_ID)
            assert driver.find_element(By.NAME, "name").get_attribute("value") is not None
            assert driver.find_element(By.NAME, "airline").get_attribute("value") is not None
            logging.info("Asserted the not null fileds - user can see the details")
            Success_List_Append(
                "test_MKTPL_372_CRUDRoutegroup_002_005",
                "Verify if mandatory fields are empty",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_372_CRUDRoutegroup_002_005",
            "Verify if mandatory fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_372_CRUDRoutegroup_002_005",
            "Verify if mandatory fields are empty",
            e,
        )
        raise e

def test_MKTPL_2959_Defaultcurrencystore_005_007():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2959_Defaultcurrencystore_005_007.png"
        ) as driver:
           
            driver.maximize_window()
           
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 60)
           
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.configurationTabXpath))).click()
            store_default_element_currency_ele = driver.find_element(By.NAME, "defaultCurrency")
            store_ele_value = store_default_element_currency_ele.get_attribute("value")
            assert store_ele_value == "US Dollar (USD)", f"Expected value was 'US Dollar (USD)', but got '{store_ele_value}'"
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, DEDICATED_PRODUCT_1_ID)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.priceAndTaxationPageXpath))).click()            
            assert driver.find_element(By.XPATH, '//span[text()="US Dollar (USD)"]').is_displayed()
            logging.info("Verified default currency US Dollar for store and product")
            time.sleep(1)
            utils.close_All_Tabs(driver)
            utils.search_by_path(driver, "/Cred Automation Store/Delivery Rule")
            xpath_ele = '(//*[contains(text(), "Cred Automation Store/Delivery Rule")])[1]'
            deliveryrule_element = WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath_ele)))
            actions = ActionChains(driver)
            actions.move_to_element(deliveryrule_element).double_click().perform()
            utils.click_on_yes_no(driver)
            time.sleep(3)
            wait.until(EC.visibility_of_element_located((By.XPATH, "((//span[contains(text(), 'Price')]//ancestor::label//following-sibling::div//div[@data-ref = 'triggerWrap'])[2]//div)[2]")))
           
            x = driver.find_element(By.XPATH, "((//span[contains(text(), 'Price')]//ancestor::label//following-sibling::div//div[@data-ref = 'triggerWrap'])[2]//div)[1]//input")
            time.sleep(5)
            x.click()
            time.sleep(1)
            x.send_keys(Keys. DOWN)
            wait.until(EC.visibility_of_element_located((By.XPATH, "//li[contains(text(),'USD')]")))
            assert driver.find_element(By.XPATH, "//li[contains(text(),'USD')]").is_displayed()
            logging.info("USD displayed")
            assert len(driver.find_elements(By.XPATH, "//li[contains(text(),'USD')]//parent::ul//li")) == 1
            logging.info("Asserted only USD is shown")
           
            Success_List_Append(
                "test_MKTPL_2959_Defaultcurrencystore_005_007",
                "Verify the currency in store, product, delivery path",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2959_Defaultcurrencystore_005_007",
            "Verify the currency in store, product, delivery path",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2959_Defaultcurrencystore_005_006_007",
            "Verify the currency in store, product, delivery path",
            e,
        )
        raise e

def test_MKTPL_2217_UITemplate_007_009_012():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2217_UITemplate_007_009_012.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            utils.click_on_add_object(driver, "UITemplate")

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            name_field = (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "templateName")))
                .is_displayed()
            )
            driver.find_element(By.NAME, "templateName").send_keys(RANDOM_NAME)
            airline = driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).is_displayed()
            assert [name_field, airline]
            logging.info(
                f"Following field should display:- Name = {name_field} , Airline = {airline}"
            )
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(EC.element_to_be_clickable((By.XPATH, '//input[@name="query"]')))
            )
            x = driver.find_element(By.XPATH, "(//span[@class='x-grid-checkcolumn']//ancestor::td//preceding-sibling::td)[2]")
            action = ActionChains(driver)
            action.double_click(x).perform()
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, "(" + xpath.okButtonXpath + ")[last()]")))
            assert driver.find_element(By.XPATH, "//div[contains(text(), 'Unpublished Airline cannot be Associated.')]").is_displayed()
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(EC.element_to_be_clickable((By.XPATH, '//input[@name="query"]')))
                .send_keys("Cred Automation Airline", Keys.ENTER)
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="/Airlines/Cred Automation Airline"]')
                )
            )
            select_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/Cred Automation Airline"]'
            )
            action = ActionChains(driver)
            action.double_click(select_airline).perform()
            time.sleep(1)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="/Airlines/Cred Automation Airline" and @class = "x-form-display-field x-form-display-field-default"]',
                    )
                )
            )
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"UI template should be successfully created/updated in the system = {saved}"
            )
            utils.close_All_Tabs(driver)
            # Add
            RANDOM_NAME_AIRLINE_CATEGORY = "".join(
                random.choices(string.ascii_letters, k=7)
            )
            payload = json.dumps(
                {
                    "name": RANDOM_NAME_AIRLINE_CATEGORY,
                    "disclaimer": "disclaimer add for automation testing",
                    "shortDescription": "this is for api automation testing",
                    "description": "automation testing",
                    "url": "",
                    "uiTemplate": RANDOM_NAME,
                    "sequenceNumber": 75,
                    "rootType": "",
                    "bannerImage": "",
                    "parentCategory": 0,
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", AIRLINE_CATEGORY_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info("------ADD------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API for Airlinecategory responded with 200 status code")
            logging.info(
                f"The Front end category created in the pimcore with all the details provide"
            )
            airline_category_id = resp_data["id"]
            logging.info(f"Airline Id- {airline_category_id}")
            # DELETE - 007
            utils.search_by_id(driver, UID)
            driver.find_element(By.XPATH, '//span[contains(@class,"pimcore_material_icon_delete pimcore_material_icon")]').click()
            logging.info("Clicked on delete icon")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(" + xpath.okButtonXpath+ ")[last()]")))
            assert driver.find_element(By.XPATH, "//div[contains(text(), 'UI Template is Associated with Airline Category')]").is_displayed()
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # UNPUBLISH - 009
            driver.find_element(By.XPATH, xpath.unpublishButtonXpath).click()
            logging.info("Clicked on Unpublish icon")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(" + xpath.okButtonXpath+ ")[last()]")))
            assert driver.find_element(By.XPATH, "//div[contains(text(), 'UI Template is Associated with Airline Category')]").is_displayed()
            
            Success_List_Append(
                "test_MKTPL_2217_UITemplate_007_009_012",
                "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2217_UITemplate_007_009_012",
            "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2217_UITemplate_007_009_012",
            "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
            e,
        )
        raise e

def test_MKTPL_5179_Export_button_017_MKTPL_882_SalesStrategy_003():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5179_Export_button_017_MKTPL_882_SalesStrategy_003.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            driver.find_element(By.XPATH, "//div[text()='Inventory Location']").click()
            driver.find_element(By.XPATH, "//span[text()='Inventory Location']").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.exportCsvXpath)))
            logging.info("Export csv button displayed")
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            driver.find_element(By.XPATH, xpath.configuration_tab).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//span[text()='Sales Strategy ']")))
            assert driver.find_element(By.XPATH, "//span[text()='Sales Strategy ']//span[contains(@class, 'gray_lock')]").is_displayed()
            logging.info("Sales strategy is in Read only mode")
            Success_List_Append("test_MKTPL_5179_Export_button_017_MKTPL_882_SalesStrategy_003",
                                "Make sure without any fields fill click on Upload button showing message like "
                                "'Field Required'","Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5179_Export_button_017_MKTPL_882_SalesStrategy_003",
                            "Make sure without any fields fill click on Upload button showing message like "
                            "'Field Required'","Fail",)
        Failure_Cause_Append("test_MKTPL_5179_Export_button_017_MKTPL_882_SalesStrategy_003",
                             "Make sure without any fields fill click on Upload button showing message like "
                             "'Field Required'",e,)
        raise e

def test_MKTPL_1134_Locationobject_010():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1134_Locationobject_010.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 60)
            wait = WebDriverWait(driver, 60)
            utils.search_by_path(driver, '/Master Data/UI Templates/Cred Automation Airline')
            wait.until(EC.visibility_of_element_located((By.XPATH, xpath.showInTreeXpath)))
            driver.find_element(By.XPATH, xpath.showInTreeXpath).click()
            logging.info("Clicked on show in tree")
            expander = WebDriverWait(driver, 100).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//span[text()='"+DEDICATED_AIRLINE+"']//parent::div//div[contains(@class, 'expander')]",
                    )
                )
            )
            driver.execute_script("arguments[0].scrollIntoView();", expander)
            driver.find_element(
                By.XPATH,
                "//span[text()='"+DEDICATED_AIRLINE+"']//parent::div//div[contains(@class, 'expander')]",
            ).click()
            logging.info("Clicked on expander")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//span[text()='Cred Automation Airline']//ancestor::table//following::table)[1]//div[contains(@class, 'expanded')]//following-sibling::span")))
            x = driver.find_element(By.XPATH, "(//span[text()='Cred Automation Airline']//ancestor::table//following::table)[1]//div[contains(@class, 'expanded')]//following-sibling::span")
            action = ActionChains(driver)
            action.context_click(x).perform()
            try:
                time.sleep(2)
                assert not driver.find_element(By.XPATH, xpath.addObjectXpath).is_displayed()
            except:
                logging.info("Add object not shown")
                pass
            logging.info("Passed - 010 - Add object is not shown for - child object")
            Success_List_Append(
                "test_MKTPL_1134_Locationobject_010",
                "Verify Store Created and Default Location object "
                "should be created under "
                "Store Name>Inventory Location folder>Default Inventory",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_1134_Locationobject_010",
            "Verify Store Created and Default Location object "
            "should be created under "
            "Store Name>Inventory Location folder>Default Inventory",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1134_Locationobject_010",
            "Verify Store Created and Default Location object "
            "should be created under "
            "Store Name>Inventory Location folder>Default Inventory",
            e,
        )
        raise e

def test_MKTPL_2959_Defaultcurrencystore_009():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2959_Defaultcurrencystore_009.png"
        ) as driver:
            driver.maximize_window()
            role = ["Login_Pac_Admin", "Login_Store", "Login_Airline"]
            for i in role:
                if i =="Login_Pac_Admin":
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("PAC ADMIN")
                elif i =="Login_Store":
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                elif i =="Login_Airline":
                    utils.Pac_Credentials.Login_Airline(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
           
                utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
               
                WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, xpath.CHILDREN_GRID_TAB))).click()
                dropmenu = WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Please select a type')]//ancestor::div[contains(@class, 'docked')]//div[contains(@id,'trigger-picker')]")))
                logging.info("dropmenu xpath got")
                time.sleep(5)            
                dropmenu.click()
                logging.info("dropmenu clicked2")
                time.sleep(5)
                WebDriverWait(driver, 40).until(EC.visibility_of_element_located((By.XPATH, xpath.SHOP_ORDER_ITEM))).click()
                el_double_click = WebDriverWait(driver, 50).until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(text(),'Cred Automation Airline')])[last()]")))
                action_chains = ActionChains(driver)
                action_chains.double_click(el_double_click).perform()
               
                utils.click_on_yes_no(driver)
                discount_total = WebDriverWait(driver, 40).until(EC.visibility_of_element_located((By.XPATH, "(//span[contains(text(), 'Discount Total ')]//ancestor::label//parent::div//div//input)[2]"))).get_attribute("value")
                tax_Total = driver.find_element(By.XPATH, "(//span[contains(text(), 'Tax Total ')]//ancestor::label//parent::div//div//input)[2]").get_attribute("value")          
                sale_price = driver.find_element(By.XPATH, "(//span[contains(text(), 'Sale Price ')]//ancestor::label//parent::div//div//input)[2]").get_attribute("value")          
                Unit_Tax  = driver.find_element(By.XPATH, "(//span[contains(text(), 'Unit Tax ')]//ancestor::label//parent::div//div//input)[2]").get_attribute("value")
                Unit_gross_total  = driver.find_element(By.XPATH, "(//span[contains(text(), 'Unit Gross Total ')]//ancestor::label//parent::div//div//input)[2]").get_attribute("value")
                Unit_Net_Total  = driver.find_element(By.XPATH, "(//span[contains(text(), 'Unit Net Total ')]//ancestor::label//parent::div//div//input)[2]").get_attribute("value")
           
                logging.info([discount_total, tax_Total, sale_price, Unit_Tax, Unit_gross_total, Unit_Net_Total])
                x = [discount_total, tax_Total, sale_price, Unit_Tax, Unit_gross_total, Unit_Net_Total]
                assert all("USD" == i for i in x)
                logging.info("USD verified for online shop order item.")
           
                utils.close_All_Tabs(driver)
               
                utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
                WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, xpath.CHILDREN_GRID_TAB))).click()
                dropmenu = WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Please select a type')]//ancestor::div[contains(@class, 'docked')]//div[contains(@id,'trigger-picker')]")))
                logging.info("dropmenu xpath got")
                time.sleep(7)            
                dropmenu.click()
                logging.info("dropmenu clicked1")
                time.sleep(7)
                WebDriverWait(driver, 30).until(EC.visibility_of_element_located((By.XPATH, xpath.ORDER_SHIPMENT))).click()
                logging.info("order shipment")
               
                el_double_click = WebDriverWait(driver, 30).until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(text(),'Cred Automation Store')])[2]")))
                action_chains = ActionChains(driver)
                action_chains.double_click(el_double_click).perform()
               
                logging.info("double clicked")
               
                utils.click_on_yes_no(driver)
               
                logging.info("yes - no PASS")
                # tracking_rate = WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "(//span[contains(text(), 'Rate:')]//ancestor::label//parent::div//div//input)[2]"))).get_attribute("value")
                # tracking_rate = WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "(//span[contains(text(), 'Rate ')]//ancestor::label//parent::div//div//input)[2]"))).get_attribute("value")
               
                # Combined XPath for 'Rate'
                xpath_combined_rate = "(//span[contains(text(), 'Rate')]//ancestor::label//parent::div//div//input)[last()]"
                # xpath_combined_rate = "(//span[contains(text(), 'Rate:')]//ancestor::label//parent::div//div//input)[2]"
               
                logging.info("--xpath_combined_rate--" )
                logging.info(xpath_combined_rate)
                logging.info("--///////////////////--" )
               
                # Locate the element using the combined XPath
                tracking_rate_element = WebDriverWait(driver, 30).until(
                    EC.visibility_of_element_located((By.XPATH, xpath_combined_rate))
                )
               
                logging.info("--get xpath_combined_rate--" )
               
                tracking_rate = tracking_rate_element.get_attribute("value")
                logging.info("----" + tracking_rate)
               
                # Combined XPath for 'Tax'
                xpath_combined_tax = "(//span[contains(text(), 'Tax')]//ancestor::label//parent::div//div//input)[last()]"
               
                # xpath_combined_tax = "(//span[contains(text(), 'Tax:')]//ancestor::label//parent::div//div//input)[2]"
               
                # Locate the element using the combined XPath
                tracking_tax_element = WebDriverWait(driver, 30).until(
                    EC.visibility_of_element_located((By.XPATH, xpath_combined_tax))
                )
                tracking_tax = tracking_tax_element.get_attribute("value")
                logging.info("----" + tracking_tax)
               
                # Log the extracted values
                logging.info([tracking_rate, tracking_tax])
                x = [tracking_rate, tracking_tax]
               
                # Assertion to check if both values are 'USD'
                assert all("USD" in i for i in x), f"Expected 'USD' in both values but got {x}"
 
                logging.info("USD verified for order shipment.")
               
                #Logout
                time.sleep(5)
                driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                logging.info("Logged out")
                WebDriverWait(driver,30).until(EC.visibility_of_element_located((By.NAME,"username")))
                time.sleep(5)
                logging.info("time success")
               
            Success_List_Append("test_MKTPL_2959_Defaultcurrencystore_009",
                                "Make sure without any fields fill click on Upload button showing message like "
                                "'Field Required'","Pass")
           
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_2959_Defaultcurrencystore_009",
                            "Make sure without any fields fill click on Upload button showing message like "
                            "'Field Required'","Fail",)
        Failure_Cause_Append("test_MKTPL_2959_Defaultcurrencystore_009",
                             "Make sure without any fields fill click on Upload button showing message like "
                             "'Field Required'",e,)
        raise e

