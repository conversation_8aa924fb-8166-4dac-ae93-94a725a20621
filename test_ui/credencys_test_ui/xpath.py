userName = "//input[@name='username']"
password = "//input[@name='password']"
submit = "//button[@type='submit']"
logout = "//a[@id='pimcore_logout']"
catalogAssignment = "//div[text()='Catalog Assignment']"
list = "//span[contains(text(),'List')]"
assets = "//div[contains(text(),'Assets')]"
pimcoreMenu_id = "pimcore_menu_massupdate"
pimcoreMenuXpath = '//li[@id="pimcore_menu_massupdate"]'
csvImport = "//span[text()='CSV Import']"
# csvImport = '//span[text()="Bulk Import"]'
csvFileSelectButton = '//input[@name="csvFile"]'
upload = '//span[text()="Upload"]'
downloadSampleFileProducts = '//span[text()="Download Sample File Of Products"]'
yes = "//span[contains(text(),'Yes')]"
open = "//span[contains(text(),'Open')]"
store = "store"

# when adding new product

newProductXpath = "//span[text()='Create New Product']"
sku = '//span[text()="SKU "]'
skuField = "sku"
store = "store"

productSkuInputFieldName = "sku"
categoryInputFieldName = "category"
productSetInputFieldName = "type"
okButtonXpath = "//span[text()='OK']"
# name_name="name"
deliveryMethodName = "deliveryType"
productType = "productType"
saveAndPublishXpath = '//span[text()="Save & Publish"]'
unpublishButtonXpath = "//span[text()='Unpublish']"
publishedColumnXpath = "//span[text()='Published']"
mandatoryFieldErrorMessage = '//div[text()="Please fill all mandatory fields"]'
importDataType = "importType"
csvUploadMessage = '//div[contains(text(),"CSV Uploaded Successfully")]'
createNewProduct = '//span[text()="Create New Product"]'
readModeLock = '//span[@class="pimcore_object_label_icon pimcore_icon_gray_lock"]'
productNameFieldXpath = '//input[@name="name"]'
isPerishableName = "isPerishable"
saveSuccessfullyMessageXpath = "//div[contains(text(),'Saved successfully!')]"
variantsPageXpath = "//span[text()='Variants']"
sectorDublicateDataErrorMessageXpath = (
    '//div[text()=" Sector can not have duplicate data."]'
)
fulfillmentCutOffTimeLimitFlightFieldXpath = (
    '//input[@name="fulfillmentCutOffTimeLimit"]'
)
flightRouteNumberFlightFieldXpath = '//input[@name="flightRouteNumber"]'
sectorFlightFieldXpath = '(//input[@name="sector"])'
noXpath = "//span[contains(text(),'No')]"
reloadXpath = '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_reload pimcore_material_icon "]'
airlineOperationXpath = '//div[text()="Airline Operation"]'
airlineProfileXpath = '//div[text()="Airline Profile"]'
monthlyCatalogsLoadsXpath = '(//span[text()="MonthlyCatalogsLoads"])'
addVariantXpath = "//span[text()='Add variant']"
newObjectInputFieldXpath = "//input[contains(@id,'textfield-input')]"
spotLightName = "spotLight"
ageVerificationRequiredName = "ageVerificationRequire"
isAvailableToSellName = "available"
requireShippingName = "requireShipping"
isTaxableName = "isTaxable"
priceAndTaxationPageXpath = "//span[text()='Pricing & Taxation']"
statusCodeFlightFieldXpath = "//span[contains(text(),'Status Code')]"
popupNameFieldXpath = (
    '(//div[text()="Enter the name of the new item"])[1]//parent::div//input'
)
importTypeName = "importType"
importTypeNameXpath = '//input[@name="importType"]'
priceFieldXpath = "(//span[contains(text(),'Price')]//ancestor::div[1]//input)[1]"
airlineProductCatDisclaimerXpath = '//textarea[@name="disclaimer"]'
airlineProductCatShortDesXpath = '//textarea[@name="shortDescription"]'
airlineProductCatDescriptionXpath = '//textarea[@name="description"]'
airlineProductCatUrlXpath = '//input[@name="url"]'
airlineProductCatImageXpath = '//div[contains(text(),"Image 1:1 (1200x1200)")]'
airlineProductCatBannerImageXpath = (
    '//b[contains(text(),"Banner Image 5X1 (3160 x 632)")]'
)
airlineProductCatAssetsTabXpath = '//span[text()="Assets"]'
airlineProductCatBaseDataTabXpath = '//span[text()="Base Data"]'
airlineProductCatUITemplateXpath = '//span[text()="UI Template:"]'
airlineProductCatSequenceNumXpath = '//input[@name="sequenceNumber"]'
airlineProductCatRootTypeXpath = '//input[@name="rootType"]'
priceUSDName = "price_USD"
specialpriceUSDName = "specialPrice_USD"
saveXpath = '//span[text()="Save"]'
exportCsvXpath = '//span[text()="Export CSV"]'
importCsvXpath = '//span[text()="Import CSV"]'
queryName = "query"
selectClassName = "selectClass"
variantsXpath = '//span[text()="Variants"]'
propertiesXpath = '//span[text()="Properties"]'
productsTabXpath = '//span[text()="Products"]'
airlineListName = "airlineList"
zipFileUploadXpath = '//input[@name="zipFile"]'
openXpath = "//span[text()='Open']"
downloadCsvButtonId = "download_CSV_button"
selectClassXpath = '(//input[@name="selectClass"])'
queryXpath = '(//input[@name="query"])'
addObjectXpath = '(//span[text()="Add Object"])[1]'
shippingDestinationName = "shippingDestination"
searchIconName = "//span[contains(@class,'pimcore_icon_search')]"
templateNameName = "templateName"
sequencingModuleXpath = '//span[text()="Open Sequencing Module"]'
routegroupOptionXpath = "//li[text()='RouteGroup']"
catalogFromDateXpath = '(//input[@name="catalogFromDate"])[1]'
catalogToDateXpath = '(//input[@name="catalogToDate"])[1]'
manageShippingName = 'manageShipping'
manageShippingXpath = '//input[@name="manageShipping"]'
listYesOptionXpath = '(//li[text()="Yes"])'
listNoOptionXpath = '(//li[text()="No"])'
configurationTabXpath = "(//span[contains(text(),'Configuration')])"
salesStrategyXpath = '//input[@name="salesStrategy"]'
listD2cOptionXpath = '(//li[text()="D2C"])'
listMarketplaceOptionXpath = '(//li[text()="Marketplace"])'
exclusivelyCookedAtHome3Xpath = '//li[text()="Exclusively cooked at home_3 (Cred Automation Store/Exclusively cooked at home_3)"]'
exclusivelyCookedAtHome7Xpath = '//li[text()="Exclusively cooked at home_7 (Cred Automation Store/Exclusively cooked at home_7)"]'
nonAlcoholiXpath = (
    '//li[text()="Non-alcoholic (Cred Automation Store/Beverages/Non-alcoholic)"]'
)
cancelBtn = '//span[text()="Cancel"]'
UserManagementTab = '//div[text()="User Management"]'
# cred_auomation_store = '//span[text()="Cred Automation Store"]'
configuration_tab = '//span[text()="Configuration"]'
catalogManagement = '//div[text()="Catalog Management"]'
credAutomationStore = '//span[text()="Cred Automation Store"]'
refresh = '//span[text()="Refresh"]'
actionsXpath = "//span[contains(text(),'Actions')]"
mapAirlineCategories = "//li[contains(text(),'Map Airline Categories')]"
childrenGridXpath = '//span[text()="Children Grid"]'
orderInformationXpath = '//span[text()="Order Information"]'
customerInformationXpath = '//div[text()="Customer Information"]'
orderShipmentDivXpath = '//div[text()="Order Shipment"]'
paymentInformationXpath = '//div[text()="Payment Information"]'
orderSummaryXpath = '//div[text()="Order Summary"]'
itineraryXpath = '//div[text()="Itinerary"]'
flightInformationXpath = '//div[text()="Flight Information"]'
orderKeyXpath = '//span[text()="Order Key "]'
externalIdXpath = '//span[text()="External ID "]'
orderProviderXpath = '//span[text()="Order Provider "]'
orderStateXpath = '//span[text()="Order State "]'
orderDateXpath = '//span[text()="Order Date "]'
idXpath = '(//span[text()="ID"])'
dateXpath = '(//span[text()="Creation Date (System)"])'
statusXpath = '//span[text()="Status"]'
productXpath = '//span[text()="Product"]'
pacVariantIdXpath = '//span[text()="PAC Variant ID"]'
csvExportButtonXpath = '//span[text()="CSV Export"]'
selectClassXpath = '//input[@name="selectClass"]'
homeDeliveryXpath = '(//div[contains(text(), "Cred Automation Store/home_delivery")])'
# homeDeliveryXpath = '(//div[contains(text(), "Cred Automation Store/home_delivery")])[2]'
openXpathContains = "//span[contains(text(),'Open')]"
generalInformationXpath = '//div[text()="General Information"]'
unitPriceXpath = '//span[text()="Unit Price "]'
quantityXpath = '(//span[contains(text(), "Quantity")])'
discountTotalXpath = '//span[text()="Discount Total "]'
taxTotalXpath = '//span[text()="Tax Total "]'
salePriceXpath = '//span[contains(text(), "Sale Price")]'
unitTaxXpath = '//span[text()="Unit Tax "]'
unitDiscountsXpath = '//span[text()="Unit Discounts "]'
unitGrossTotalXpath = '//span[text()="Unit Gross Total "]'
unitNetTotalXpath = '//span[text()="Unit Net Total "]'
discountAdjustmentsXpath = '//div[contains(text(),"Discount Adjustments")]'
amountXpath = '//span[contains(text(),"Amount")]'
ajustmentTypeXpath = '//span[contains(text(),"Adjustment Type")]'
promotionCodeXpath = '//span[contains(text(),"Promotion Code")]'
promotionNameXpath = '//span[contains(text(),"Promotion Name")]'
taxAdjustmentsXpath = '//div[contains(text(),"Tax Adjustments")]'
typeXpath = '//span[contains(text(),"Type")]'
taxAmountXpath = '//span[contains(text(),"Tax Amount")]'
rateXpath = '//span[contains(text(),"Rate")]'
orderShipmentXpath ='//span[contains(text(),"Order Shipment")]'
baseDataDivXpath = '//div[text()="Base Data"]'
openButtonXpath = '//img[@role="button"]'
orderLineItemXpath = '//div[contains(text(),"Order Line Item")]'
shipmentIdXpath = '//span[contains(text(),"Shipment ID")]'
shipmentMethodXpath = '//span[contains(text(),"Shipment Method")]'
shipmentModeXpath ='//span[contains(text(),"Shipment Mode")]'
trackingIdXpath = '//span[contains(text(),"Tracking ID")]'
trackingCarrierXpath = '//span[contains(text(),"Tracking Carrier")]'
trackingLinkXpath = '//span[contains(text(),"Tracking Link")]'
inventoryLocationXpath = '//span[contains(text(),"Inventory Location")]'
deliveryInformatioXpath = '//span[contains(text(),"Delivery Information")]'
salutationXpath = '//input[@name="salutation"]'
firstNameXpath = '//input[@name="firstName"]'
lastNameXpath = '//input[@name="lastName"]'
address1Xpath = '//textarea[@name="address1"]'
postalCodeXpath = '//input[@name="postalCode"]'
shippingStateXpath = '//input[@name="shippingState"]'
emailXpath = '//input[@name="email"]'
titleXpath = '//input[@name="title"]'
middleNameXpath = '//input[@name="middleName"]'
address2Xpath = '//textarea[@name="address2"]'
shippingCityXpath = '//input[@name="shippingCity"]'
shippingCountryXpath = '//input[@name="shippingCountry"]'
shippingPhoneXpath = '//input[@name="shippingPhone"]'
storeName = 'storeList'
validationMessManageShippingXpath = '//div[text()="Validation failed: Empty mandatory field [ manageShipping ]"]'
validationMessUnitPriceXpath = "//div[text()='Allowed upto 3 decimal places for Unit Price']"
validationMessUnitTaxXpath = "//div[text()='Allowed upto 3 decimal places for Unit Tax']"
validationMessUnitDiscountsXpath = "//div[text()='Allowed upto 3 decimal places for Unit Discounts']"
validationMessUnitNetXpath = "//div[text()='Allowed upto 3 decimal places for Unit Net']"
validationMessUnitGrossXpath = "//div[text()='Allowed upto 3 decimal places for Unit Gross']"
validationMessDiscountTotalXpath = "//div[text()='Allowed upto 3 decimal places for Unit Discounts']"
validationMessTaxTotalXpath = "//div[text()='Allowed upto 3 decimal places for Tax Total']"
validationMessTaxAmountXpath = "//div[text()='Allowed upto 3 decimal places for tax Amount']"
settingsIcon_li = '//li[@id="pimcore_menu_settings"]'
inventoryLocationFieldXpath = '//input[@name="inventoryLocation"]'
pendingOptionListXpath = '//li[text()="Pending"]'
deliveredOptionListXpath = '//li[text()="Delivered"]'
statusFieldXpath = '//input[@name="status"]'
printEReceipt =  '//span[text()="Print E-Receipt"]'
orderShippingInformation ='//span[text()="Order Shipping Information"]'
credAutomationStoreListing = '//li[text()="Cred Automation Store"]'
manageStockXpath = '//input[@name="manageStock"]'
logsTimeVerifyXpath = '//div[contains(text(),"time")]'
exportXLSXButtonXpath = "//span[text()='Export XLSX']"
mealCodeSampleFileDownloadButtonXpath = '//span[text()="Download Sample File Of Mealcode"]'
UiTemplateSampleFileDownloadButtonXpath = '//span[text()="Download Sample File Of UiTemplate"]'
storeName_input = '//input[@name= "storeName"]'
storeName_span = '//span[text()= "Store Name "]'
catalogAssignment_span = '//span[text()="CatalogAssignment"]'
generateProfileXpath = "//span[text()='Generate Profile']"
compareProfileXpath = "//span[text()='Compare Profile']"
importAirlineData = "//span[text()='Import Airline Data']"
dataObjects_span = '//span[text()="Data Objects"]'
classes_span = '//span[text()="Classes"]'
home_span = '//span[text()="Home"]'
filter_a = '//a[@data-qtip="Filter"]'
filter_input = '//input[@name="filter"]'
settings = "//li[@data-menu-tooltip='Settings']"
usersOrRoles = "//span[text()='Users / Roles']"
noButtonXpath = "//span[text()='No']"
yesButtonXpath = "//span[text()='Yes']"
downloadSampleFileroottpe = '//span[text()="Download Sample File Of RootType"]'
airlineName = 'airlineList'
Exportcsvbutton = '//span[text()="Export CSV"]'
showInTreeXpath = "//a[@data-qtip = 'Show in Tree']"
rootTypeNameXpath = '//span[text()="Root Type Name "]'
rootDescriptionXpath = '//span[contains(text(),"Description")]'
rootAirlineXpath = '//span[text()="Airline "]'
deleteXpath = "//span[text()='Delete']"
UiTemplateNameXpath = '//span[text()="Name "]'
UiTemplateAirlineXpath = '//span[text()="Airline "]'
catalog_span = '//span[text()="Catalog"]'
select_span = '//span[text()="Select"]'
requestForAssociation_span = '//span[text()="Request for Association"]'
requestForAssociation_div = '//div[text()="Request for Association"]'
requestedForAssociation_div = '//div[contains(text(), "Requested for Association")]'
closeTabButton_span = '//span[@class="x-tab-close-btn"]'
approveForAssociation_span = '//span[text()="Approve For Association"]'
approveForAssociation_div = '//div[text()="Approve For Association"]'
approvedForAssociation_div = '//div[contains(text(), "Approved for Association")]'
airlineCategoryMapped_div = '//div[contains(text(), "Airline Category Mapped")]'
