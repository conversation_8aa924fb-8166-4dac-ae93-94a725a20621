import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC

deletionScriptName = "deleteAutomationData"

def Deletion_Automation_Data():
    import credencys_test_ui.credencys_project_configs.utils as utils
    try:
        with utils.services_context_wrapper(
            "Delete_Automation_Data.png"
        ) as driver:
            print("Entering into Delete_Automation_Data")
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            print("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            driver.find_element(By.XPATH, "//li[@data-menu-tooltip = 'Tools']").click()
            Process_Manager = driver.find_element(By.XPATH, "//span[text()='Process Manager']")
            action = ActionChains(driver)
            action.move_to_element(Process_Manager).perform()
            Process_Manager.click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[text()='"+deletionScriptName+"']")
                )
            )
            driver.find_element(By.XPATH, "(//div[text()='"+deletionScriptName+"']//parent::td//parent::tr)//td//img[@data-qtip='Process Manager - Execute']").click()   
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(text(),'"+deletionScriptName+" (ID: ')]")
                )
            )
            id = driver.find_element(By.XPATH, "//div[contains(text(),'"+deletionScriptName+" (ID: ')]//a").text
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//a[contains(text(),'"+id+"')]//ancestor::div[@style='']//a[@data-qtip = 'Done, hide process']")
                )
            )
            print("All processes finished")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//a[contains(text(),'"+id+"')]//ancestor::div[@style='']//a[@data-qtip = 'Done, hide process']")
                )
            )
            driver.find_element(By.XPATH, "//div[@data-qtip='Done, hide all processes']").click()
    except Exception as e:
        print(f"Error- {e}")
        pass
