from faker import Faker
from conftest import create_driver
from credencys_test_ui import xpath
from contextlib import contextmanager
from selenium.webdriver.common.by import By
from selenium.webdriver import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.wait import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import json, string, random, os, requests, time, logging, pandas as pd

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

@contextmanager
def services_context_wrapper(screenshot=None):
    global driver
    try:
        testcase_id = screenshot.split(".")
        logging.info(f"Start for {testcase_id[0]}")
        c = Options()
        # c.add_argument("--headless=new")
        c.add_argument("--window-size=1920,1080")
        c.add_argument("--no-sandbox")
        # c.add_argument("enable-automation")
        c.add_argument("--disable-blink-features=AutomationControlled")
        c.add_argument("--disable-dev-shm-usage")
        prefs = {"download.default_directory": os.getcwd() + "/downloads"}
        c.add_experimental_option("prefs", prefs)
        # driver = webdriver.Chrome(options=c)
        # driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()),options=c)
        # driver = webdriver.Remote(
        #     command_executor="http://selenium__standalone-chrome:4444/wd/hub", options=c
        # )
        driver = create_driver()
        # driver = webdriver.Chrome()
        yield driver
    except Exception as e:
        logging.info(f"End for {testcase_id[0]}")
        if screenshot:
            driver.save_screenshot(screenshot)
        raise e
    finally:
        logging.info(f"End for {testcase_id[0]}")
        driver.quit()
        time.sleep(1)

def is_element_present(x):
    logging.info("In is_element_present")
    try:
        driver.find_element(By.XPATH, x)
        return True
    except:
        return False

def retryForProperLoading(driver, maxRetries):
    logging.info("In retryForProperLoading")
    driver.implicitly_wait(0)
    retries = 0
    while retries < int(maxRetries):
        try:
            panels = [
                        "//div[contains(text(),'Airline Operation')]",
                        "//div[contains(text(),'Documents')]",
                        "//div[contains(text(),'Store Profile')]"]

            # Check if at least one of the elements is present
            is_found = any(is_element_present(x) for x in panels)
            assert is_found, "None of the elements were found on the page."
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("UI is loaded")
            return True
        except Exception:
            driver.refresh()
            logging.info(f"Treetype error/UI not loaded. Retrying... ({retries + 1}/{int(maxRetries)})")
            retries += 1

@contextmanager
def services_context_wrapper_optimized(login= False, screenshot=None, quit= False):
    global driver
    try:
        if screenshot:
                testcase_id = screenshot.split(".")
                logging.info(f"Start for {testcase_id[0]}")
        if login:            
            c = Options()
            c.add_argument("--headless=new")
            c.add_argument("--window-size=1920,1080")
            c.add_argument("--no-sandbox")
            # c.add_argument("enable-automation")
            c.add_argument("--disable-blink-features=AutomationControlled")
            c.add_argument("--disable-dev-shm-usage")
            prefs = {"download.default_directory": os.getcwd() + "/downloads"}
            c.add_experimental_option("prefs", prefs)
            # driver = webdriver.Chrome(options=c)
            # driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()),options=c)
            # driver = webdriver.Remote(
            #     command_executor="http://selenium__standalone-chrome:4444/wd/hub", options=c
            # )
            driver = create_driver()
        yield driver
    except Exception as e:
        if screenshot:
            logging.info(f"End for {testcase_id[0]}")
            driver.save_screenshot(screenshot)
        raise e
    finally:
        # retry mechanism to avoid treetype
        driver.refresh()
        retryForProperLoading(driver, os.environ.get("MAX_RETRIES", 7))
        if screenshot:
            logging.info(f"End for {testcase_id[0]}")
        if quit:
            logging.info(os.getcwd())
            driver.quit()

class Pac_Credentials:
    def Login_Pac_Admin(driver):
        # driver = webdriver.Chrome()
        # time.sleep(4)
        retries = 0
        while retries < 7:
            try:
                driver.maximize_window()
                driver.implicitly_wait(10)
                driver.get(PAC_URL)
                driver.find_element(By.XPATH, xpath.userName).send_keys(
                    PAC_USER_MAIL
                )
                driver.find_element(By.XPATH, xpath.password).send_keys(
                    PAC_USER_PASSWORD
                )
                driver.find_element(By.XPATH, xpath.submit).click()
                return True
            except Exception:
                driver.refresh()
                logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
                retries += 1

        raise Exception(f"Login Elements not found after {7} retries")

    def Login_store(driver):
        # driver = webdriver.Chrome()
        # time.sleep(4)
        retries = 0
        while retries < 7:
            try:
                driver.maximize_window()
                driver.implicitly_wait(10)
                driver.get(PAC_URL)
                driver.find_element(By.XPATH, xpath.userName).send_keys(
                    PAC_STORE_USER_MAIL
                )
                driver.find_element(By.XPATH, xpath.password).send_keys(
                    PAC_STORE_USER_PASSWORD
                )
                driver.find_element(By.XPATH, xpath.submit).click()
                return True
            except Exception:
                driver.refresh()
                logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
                retries += 1

        raise Exception(f"Login Elements not found after {7} retries")

    def Login_Airline(driver):
        # driver = webdriver.Chrome()
        # time.sleep(4)
        retries = 0
        while retries < 7:
            try:
                driver.maximize_window()
                driver.implicitly_wait(10)
                driver.get(PAC_URL)
                driver.find_element(By.XPATH, xpath.userName).send_keys(
                    PAC_AIRLINE_MAIL
                )
                driver.find_element(By.XPATH, xpath.password).send_keys(
                    PAC_PASSWORD_MAIL
                )
                driver.find_element(By.XPATH, xpath.submit).click()
                return True
            except Exception:
                driver.refresh()
                logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
                retries += 1

        raise Exception(f"Login Elements not found after {7} retries")

    def Login_Airline_Manager(driver):
        # driver = webdriver.Chrome()
        # time.sleep(4)
        retries = 0
        while retries < 7:
            try:
                driver.maximize_window()
                driver.implicitly_wait(10)
                driver.get(PAC_URL)
                driver.find_element(By.XPATH, xpath.userName).send_keys(
                    PAC_AIRLINE_MANAGER_MAIL
                )
                driver.find_element(By.XPATH, xpath.password).send_keys(
                    PAC_AIRLINE_MANAGER_PASSWORD
                )
                driver.find_element(By.XPATH, xpath.submit).click()
                return True
            except Exception:
                driver.refresh()
                logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
                time.sleep(2)
                retries += 1

        raise Exception(f"Login Elements not found after {7} retries")

    def Login_Store_Manager(driver):
        # driver = webdriver.Chrome()
        # time.sleep(4)
        retries = 0
        while retries < 7:
            try:
                driver.maximize_window()
                driver.implicitly_wait(10)
                driver.get(PAC_URL)
                driver.find_element(By.XPATH, xpath.userName).send_keys(
                    PAC_STORE_MANAGER_MAIL
                )
                driver.find_element(By.XPATH, xpath.password).send_keys(
                    PAC_STORE_MANAGER_PASSWORD
                )
                driver.find_element(By.XPATH, xpath.submit).click()
                return True
            except Exception:
                driver.refresh()
                logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
                time.sleep(2)
                retries += 1

        raise Exception(f"Login Elements not found after {7} retries")

    def Login_marketplace_store(driver):
        # driver = webdriver.Chrome()
        retries = 0
        while retries < 7:
            try:
                driver.maximize_window()
                driver.implicitly_wait(10)
                driver.get(PAC_URL)
                driver.find_element(By.XPATH, xpath.userName).send_keys(
                    PAC_STORE_MARKETPLACE_USER_MAIL
                )
                driver.find_element(By.XPATH, xpath.password).send_keys(
                    PAC_STORE_MARKETPLACE_PASSWORD
                )
                driver.find_element(By.XPATH, xpath.submit).click()
                return True
            except Exception:
                driver.refresh()
                logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
                retries += 1

        raise Exception(f"Login Elements not found after {7} retries")

    def Login_Unpublished_Store(driver):
        # driver = webdriver.Chrome()
        # time.sleep(4)
        retries = 0
        while retries < 7:
            try:
                driver.maximize_window()
                driver.implicitly_wait(10)
                driver.get(PAC_URL)
                driver.find_element(By.XPATH, xpath.userName).send_keys(
                    PAC_UNPUBLISHED_STORE_USER_MAIL
                )
                driver.find_element(By.XPATH, xpath.password).send_keys(
                    PAC_UNPUBLISHED_STORE_PASSWORD
                )
                driver.find_element(By.XPATH, xpath.submit).click()
                return True
            except Exception:
                driver.refresh()
                logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
                retries += 1

        raise Exception(f"Login Elements not found after {7} retries")

    def Login_Unpublished_Airline(driver):
        # driver = webdriver.Chrome()
        # time.sleep(4)
        retries = 0
        while retries < 7:
            try:
                driver.maximize_window()
                driver.implicitly_wait(10)
                driver.get(PAC_URL)
                driver.find_element(By.XPATH, xpath.userName).send_keys(
                    PAC_UNPUBLISHED_AIRLINE_USER_MAIL
                )
                driver.find_element(By.XPATH, xpath.password).send_keys(
                    PAC_UNPUBLISHED_AIRLINE_PASSWORD
                )
                driver.find_element(By.XPATH, xpath.submit).click()
                return True
            except Exception:
                driver.refresh()
                logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
                retries += 1

        raise Exception(f"Login Elements not found after {7} retries")

def Delete(uid, url, token):
    if uid is not None:
        uid = uid.split()
        url = url + uid[1]
        logging.info(url)
        payload = ""
        headers = {"Authorization": token}
        response = requests.delete(url, headers=headers, data=payload)
        logging.info(response.text)
        return response.status_code
    else:
        return -1

def generate_random_email():
    dummy = Faker()
    return dummy.email()

def Assets(driver):
    wait_for_style_attribute(driver, 40)
    WebDriverWait(driver, 60).until(
        (EC.element_to_be_clickable((By.XPATH, xpath.assets)))
    )
    time.sleep(5)
    driver.find_element(By.XPATH, xpath.assets).click()
    logexpander = WebDriverWait(driver, 60).until(
        EC.presence_of_element_located(
            (
                By.XPATH,
                "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]",
            )
        )
    )
    driver.execute_script("arguments[0].scrollIntoView();", logexpander)

    driver.find_element(
        By.XPATH,
        "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]",
    ).click()
    logging.info("Clicked on Log")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[contains(text(),'Triggered SNS')]")
        )
    )
    driver.find_element(By.XPATH, "//span[contains(text(),'Triggered SNS')]").click()
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.XPATH, "//span[contains(text(),'List')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
    logging.info("Navigated to triggered SNS - List View")
    time.sleep(10)
    try:
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (
                    By.XPATH,
                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
                )
            )
        )
        driver.find_element(
            By.XPATH,
            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
        ).click()
        logging.info("Navigated to last page")
    except Exception as e:
        logging.info("last page error")
        logging.info(e)
        pass
    time.sleep(10)

def close_All_Tabs(driver):
    logging.info(f"In close_All_Tabs")
    actions = ActionChains(driver)
    actions.key_down(Keys.ALT).send_keys("t").key_up(
        Keys.ALT).perform()
    driver.refresh()
    wait_for_style_attribute(driver, 40)
    logging.info("End close_All_Tabs")
    
def click_on_add_object(driver, elm_name):
    retries = 0
    while retries < 7:
        try:
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Home')]")
                )
            )
            # Move to home icon
            home = driver.find_element(By.XPATH, "//span[contains(text(),'Home')]")
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "(//span[text()='Refresh'])"))
            )
            driver.find_element(By.XPATH, "(//span[text()='Refresh'])").click()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Home')]")
                )
            )
            # Move to home icon
            home = driver.find_element(By.XPATH, "//span[contains(text(),'Home')]")
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            # time.sleep(2)
            # Move to Add object
            # WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'Add Object'])[1]"))
            # )
            ADD_OBJECT = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="' + elm_name + '"])[1]')
                )
            )
            # Click on Stores
            elm = driver.find_element(
                By.XPATH, '(//span[text()="' + elm_name + '"])[1]'
            )
            driver.execute_script("arguments[0].click();", elm)
            logging.info(f"Clicked on {elm_name}")
            return True
        except Exception:
            driver.refresh()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Home')]")
                )
            )
            time.sleep(5)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1

    raise Exception(f"Element {elm_name} not found after {7} retries")

def search_by_id(driver, id):
    # body_element = driver.find_element(
    #     By.XPATH, "(//div[contains(@id,'pimcore_panel_tabs-body')])[2]"
    # )
    # body_element.click()
    logging.info(f"Clicked on the open area for search window")
    actions = ActionChains(driver)
    actions.key_down(Keys.CONTROL).key_down(Keys.SHIFT).send_keys("o").key_up(
        Keys.CONTROL
    ).key_up(Keys.SHIFT).perform()
    driver.find_element(
        By.XPATH, "//input[contains(@name,'textfield-inputEl')]"
    ).send_keys(id, Keys.ENTER)
    # driver.find_element(By.XPATH, "//span[contains(text(),'OK')]").click()
    logging.info(f"Click on OK post search the id.")
    click_on_yes_no(driver)
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.XPATH, f"//div[contains(text(),'{id}')]"))
    )

def update_csv(file, field, code):
    logging.info("In update_csv")
    df = pd.read_csv(os.getcwd() + "/credencys_test_ui/" + file)  # read CSV
    old_price = df.loc[0, field]
    logging.info(old_price)
    df.loc[0, field] = code  # update price in CSV
    logging.info(df.loc[0, field])
    df.to_csv(os.getcwd() + "/credencys_test_ui/" + file, index=False)
    logging.info(df)
    logging.info("Product data updated in CSV")

def upload_csv(file, ele_xpath):
    path = os.getcwd() + "/credencys_test_ui/" + file
    driver.find_element(By.XPATH, ele_xpath).send_keys(path)
    driver.implicitly_wait(0)
    driver.find_element(By.XPATH, xpath.upload).click()
    try:
        saved_message = (
            WebDriverWait(driver, 30)
            .until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Success"]'))
            )
            .is_displayed()
        )
        logging.info(f"Upload is {saved_message}")
    except Exception as e:
        saved_message = (
            WebDriverWait(driver, 30)
            .until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//label[text()="All records imported successfully."]')
                )
            )
            .is_displayed()
        )
        logging.info(f"Upload is {saved_message}")
    return saved_message

def upload_csv_with_specific_validation(file, ele_xpath, validation_message):
    path = os.getcwd() + "/credencys_test_ui/" + file
    driver.find_element(By.XPATH, ele_xpath).send_keys(path)
    driver.implicitly_wait(0)
    driver.find_element(By.XPATH, xpath.upload).click()

    saved_message = (
        WebDriverWait(driver, 60)
        .until(
            EC.visibility_of_element_located(
                (By.XPATH, f"//div[contains(text(),'{validation_message}')]")
            )
        )
        .is_displayed()
    )
    logging.info(f"Validation is shown as {validation_message}")
    return saved_message

def Assets_import(driver):
    wait_for_style_attribute(driver, 40)
    time.sleep(5)
    WebDriverWait(driver, 60).until(
        (EC.element_to_be_clickable((By.XPATH, xpath.assets)))
    )
    driver.find_element(By.XPATH, xpath.assets).click()
    driver.find_element(
        By.XPATH,
        "//span[text()='Import']//parent::div//div[contains(@class,'expander')]",
    ).click()
    logging.info("Clicked on Import")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[contains(text(),'" + PAC_AIRLINE_MAIL + "')]")
        )
    )
    driver.find_element(
        By.XPATH, "//span[contains(text(),'" + PAC_AIRLINE_MAIL + "')]"
    ).click()
    driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
    logging.info(f"Navigated to {PAC_AIRLINE_MAIL} - List View")
    time.sleep(5)
    try:
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (
                    By.XPATH,
                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
                )
            )
        )
        driver.find_element(
            By.XPATH,
            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
        ).click()
        logging.info("Navigated to last page")
    except Exception as e:
        logging.info("last page error")
        logging.info(e)
        pass

def get_uid(driver):
    try:
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (
                    By.XPATH,
                    '(//div[@class = "x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default"])[1]',
                )
            )
        )
        UID = driver.find_element(
            By.XPATH,
            '(//div[@class = "x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default"])[1]',
        ).text
        logging.info(f"Unique ID: {UID}")
        return UID
    except Exception as e:
        UID = None
        return UID

def auto_populate_airline_category(driver):
    cred_automation_airline = "8122"
    try:
        driver.maximize_window()
        Pac_Credentials.Login_Airline(driver)
        driver.implicitly_wait(10)
        act_title = driver.find_element(
            By.XPATH, xpath.logout
        ).get_attribute("id")
        assert act_title == "pimcore_logout"
        logging.info("Login is successful.")
        wait_for_style_attribute(driver, 40)
        search_by_id(driver, cred_automation_airline)
        driver.find_element(By.XPATH, "//span[contains(text(),'Stores')]").click()
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (
                    By.XPATH,
                    "(//b[contains(text(),'Associated Stores')]//ancestor::fieldset//tbody//td)[4]//div",
                )
            )
        )
        option = driver.find_element(
            By.XPATH,
            "(//b[contains(text(),'Associated Stores')]//ancestor::fieldset//tbody//td)[4]//div",
        ).text
        if "No" in option:
            logging.info("Auto populate Airline Category - NO")
            pass
        else:
            logging.info("Auto populate Airline Category - YES")
            driver.find_element(
                By.XPATH,
                "(//b[contains(text(),'Associated Stores')]//ancestor::fieldset//tbody//td)[4]//div",
            ).click()
            driver.find_element(
                By.XPATH,
                "(//b[contains(text(),'Associated Stores')]//ancestor::fieldset//tbody//td)[4]//input",
            ).send_keys(
                Keys.BACKSPACE, Keys.BACKSPACE, Keys.BACKSPACE, "No", Keys.ENTER
            )
            driver.find_element(
                By.XPATH, "//b[contains(text(),'Associated Stores')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                )
            )
            save_and_publish = driver.find_element(
                By.XPATH, "//span[contains(text(),'Save & Publish')]"
            )
            save_and_publish.click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Auto populate Airline Category changed to - NO")
        driver.find_element(By.XPATH, xpath.logout).click()
        logging.info("Logged out from Store Admin")
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located((By.NAME, "username"))
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        raise e

def auto_populate_auto_map_airline_categories(driver):
    Pac_Credentials.Login_Airline(driver)
    act_title = driver.find_element(
        By.XPATH, xpath.logout
    ).get_attribute("id")
    assert act_title == "pimcore_logout"
    logging.info("Login is successful.")
    wait_for_style_attribute(driver, 40)
    search_by_id(driver, DEDICATED_AIRLINE_ID)

    (
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "(//span[text()='Stores'])[last()]")
            )
        )
    ).click()
    time.sleep(2)
    x = driver.find_element(By.XPATH, "//td[4]").text
    y = driver.find_element(By.XPATH, "//td[5]").text
    if "Yes" not in x:
        driver.find_element(By.XPATH, "//td[4]").click()
        time.sleep(2)
        driver.find_element(By.XPATH, "//td[4]//input").send_keys(
            Keys.CONTROL, "a", Keys.BACKSPACE
        )
        time.sleep(2)
        driver.find_element(By.XPATH, "//td[4]//input").send_keys("Yes", Keys.ENTER)
        time.sleep(2)
        # Click on Save and Publish
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "//span[contains(text(),'Save & Publish')]")
            )
        )
        save_and_publish = driver.find_element(
            By.XPATH, "//span[contains(text(),'Save & Publish')]"
        )
        save_and_publish.click()
        # Check for the validation
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, xpath.saveSuccessfullyMessageXpath)
            )
        )
        logging.info("The value changed to Yes for Auto Populate Airline Categories")
    else:
        logging.info("The value is already Yes in Auto-Populate Airline Categories")
    if "Yes" not in y:
        WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable((By.XPATH, "//td[5]"))
        )
        driver.find_element(By.XPATH, "//td[5]").click()
        time.sleep(2)
        driver.find_element(By.XPATH, "//td[5]//input").send_keys(
            Keys.CONTROL, "a", Keys.BACKSPACE
        )
        time.sleep(2)
        driver.find_element(By.XPATH, "//td[5]//input").send_keys("Yes", Keys.ENTER)
        # Click on Save and Publish
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "//span[contains(text(),'Save & Publish')]")
            )
        )
        save_and_publish = driver.find_element(
            By.XPATH, "//span[contains(text(),'Save & Publish')]"
        )
        save_and_publish.click()
        # Check for the validation
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, xpath.saveSuccessfullyMessageXpath)
            )
        )
        logging.info("The value changed to Yes for Auto Map Airline Categories")
        time.sleep(10)
    else:
        logging.info("The value is already Yes in Auto-Map Airline Categories")

    driver.find_element(By.XPATH, xpath.logout).click()
    logging.info("Logged out from Airline Admin")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.NAME, "username"))
    )

def revert_auto_populate_auto_map_airline_categories(driver):
    Pac_Credentials.Login_Airline(driver)
    act_title = driver.find_element(
        By.XPATH, xpath.logout
    ).get_attribute("id")
    assert act_title == "pimcore_logout"
    logging.info("Login is successful.")
    wait_for_style_attribute(driver, 40)
    search_by_id(driver, DEDICATED_AIRLINE_ID)
    (
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "(//span[text()='Stores'])[last()]")
            )
        )
    ).click()
    time.sleep(2)
    x = driver.find_element(By.XPATH, "//td[4]").text
    y = driver.find_element(By.XPATH, "//td[5]").text
    if "Yes" in x:
        driver.find_element(By.XPATH, "//td[4]").click()
        time.sleep(2)
        driver.find_element(By.XPATH, "//td[4]//input").send_keys(
            Keys.CONTROL, "a", Keys.BACKSPACE
        )
        time.sleep(2)
        driver.find_element(By.XPATH, "//td[4]//input").send_keys("No", Keys.ENTER)
        time.sleep(2)
        # Click on Save and Publish
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "//span[contains(text(),'Save & Publish')]")
            )
        )
        save_and_publish = driver.find_element(
            By.XPATH, "//span[contains(text(),'Save & Publish')]"
        )
        save_and_publish.click()
        # Check for the validation
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, xpath.saveSuccessfullyMessageXpath)
            )
        )
        logging.info("The value changed to No for Auto Populate Airline Categories")
    else:
        logging.info("The value is already No in Auto-Populate Airline Categories")
    driver.find_element(By.XPATH, xpath.logout).click()
    logging.info("Logged out from Airline Admin")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.NAME, "username"))
    )

def wait_for_style_attribute(driver, timeout):
    logging.info("In wait_for_style_attribute")
    try:
        element = driver.find_element(By.ID, "pimcore_loading")
        style_value = "visibility: hidden;"
        return WebDriverWait(driver, timeout).until(
            lambda driver: element.get_attribute("style") == style_value
        )
    except:
        pass

def create_New_Object_With_UserAnd_Verify_Fields_With_Mandatory_Fields(
    driver, user_role, user_folder_id
):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    wait = WebDriverWait(driver, 60)
    users = wait.until(
        EC.presence_of_element_located((By.XPATH, '//span[text()="Users"]'))
    )
    action = ActionChains(driver)
    action.context_click(users).perform()
    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.XPATH, xpath.refresh))
    )
    driver.find_element(By.XPATH, xpath.refresh).click()
    users = wait.until(
        EC.presence_of_element_located((By.XPATH, '//span[text()="Users"]'))
    )
    action = ActionChains(driver)
    action.context_click(users).perform()

    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.XPATH, '//span[text()="Add Object"]'))
    )
    time.sleep(1)
    add_object = driver.find_element(By.XPATH, '//span[text()="Add Object"]')
    mouse_hover = ActionChains(driver)
    mouse_hover.move_to_element(add_object).perform()
    time.sleep(1)
    user = driver.find_element(
        By.XPATH, '(//div[@class="x-box-target"]//span[text()="Users"])[2]'
    )
    mouse_hover.move_to_element(user).click().perform()
    driver.implicitly_wait(0)
    wait.until(
        EC.presence_of_element_located(
            (
                By.XPATH,
                '(//div[text()="Enter the name of the new item"])[1]'
                "//parent::div//input",
            )
        )
    ).send_keys(RANDOM_NAME)
    wait.until(
        EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
    ).click()

    ### 001
    first_Name_field = (
        WebDriverWait(driver, 60)
        .until(
            EC.presence_of_element_located((By.XPATH, '//span[text()="First Name "]'))
        )
        .is_displayed()
    )
    last_Name_field = (
        WebDriverWait(driver, 60)
        .until(
            EC.presence_of_element_located((By.XPATH, '//span[text()="Last Name:"]'))
        )
        .is_displayed()
    )
    isActive_field = (
        WebDriverWait(driver, 60)
        .until(
            EC.presence_of_element_located((By.XPATH, '//span[text()="Is Active:"]'))
        )
        .is_displayed()
    )
    email_field = (
        WebDriverWait(driver, 60)
        .until(EC.presence_of_element_located((By.XPATH, '//span[text()="Email "]')))
        .is_displayed()
    )
    usertype_field = (
        WebDriverWait(driver, 60)
        .until(
            EC.presence_of_element_located((By.XPATH, '//span[text()="User Type "]'))
        )
        .is_displayed()
    )
    userrole_selection = (
        WebDriverWait(driver, 60)
        .until(
            EC.presence_of_element_located((By.XPATH, '//span[text()="User Role "]'))
        )
        .is_displayed()
    )

    assert [
        first_Name_field,
        last_Name_field,
        isActive_field,
        email_field,
        usertype_field,
        userrole_selection,
    ]
    logging.info(
        f"Following field should display:- First Name = {first_Name_field} , Last Name = {last_Name_field}, Is Active = {isActive_field}, Email = {email_field}, user type = {usertype_field}, user role selection = {userrole_selection}"
    )

    ### 002
    logging.info("Click on Save & Publish button")
    driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
    time.sleep(2)
    validation = (
        WebDriverWait(driver, 60)
        .until(
            EC.presence_of_element_located(
                (
                    By.XPATH,
                    '//div[text()="Validation failed: Empty mandatory field [ firstName ] / Empty mandatory field [ email ] / Empty mandatory field [ userRole ]"]',
                )
            )
        )
        .is_displayed()
    )
    time.sleep(2)
    assert validation
    logging.info(f"Validation message should display  =  {validation}")
    driver.find_element(By.XPATH, '(//span[text()="OK"])[2]').click()
    ###

    wait.until(EC.presence_of_element_located((By.NAME, "firstName"))).send_keys(
        RANDOM_NAME
    )
    logging.info("Click on Save & Publish button")
    driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
    time.sleep(2)
    validation = (
        WebDriverWait(driver, 60)
        .until(
            EC.presence_of_element_located(
                (
                    By.XPATH,
                    '//div[text()="Validation failed: Empty mandatory field [ email ] / Empty mandatory field [ userRole ]"]',
                )
            )
        )
        .is_displayed()
    )

    time.sleep(2)
    assert validation
    logging.info(f"Validation message should display  =  {validation}")
    driver.find_element(By.XPATH, '(//span[text()="OK"])[2]').click()
    ###
    driver.find_element(By.NAME, "isActive").click()
    wait.until(EC.presence_of_element_located((By.NAME, "email"))).send_keys(
        RANDOM_NAME + "@gmail.com"
    )
    logging.info("Click on Save & Publish button")
    time.sleep(2)
    driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
    validation = (
        WebDriverWait(driver, 60)
        .until(
            EC.presence_of_element_located(
                (
                    By.XPATH,
                    '//div[text()="Validation failed: Empty mandatory field [ userRole ]"]',
                )
            )
        )
        .is_displayed()
    )
    assert validation
    logging.info(f"Validation message should display  =  {validation}")
    driver.find_element(By.XPATH, '(//span[text()="OK"])[2]').click()
    ###
    wait.until(EC.presence_of_element_located((By.NAME, "userRole"))).send_keys(
        user_role
    )

    logging.info("Click on Save & Publish button")
    driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
    time.sleep(2)
    ###
    driver.implicitly_wait(0)
    saved = (
        WebDriverWait(driver, 60)
        .until(
            EC.visibility_of_element_located(
                (By.XPATH, '//div[text()="Saved successfully!"]')
            )
        )
        .is_displayed()
    )
    assert saved
    logging.info(f"User is successfully created/updated in the system = {saved}")

    search_by_id(driver, user_folder_id)
    logging.info("search the selected user")
    driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
        RANDOM_NAME, Keys.ENTER
    )
    time.sleep(3)
    WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (By.XPATH, '(//div[text()="'+RANDOM_NAME+'"])')
            )
        )
    driver.find_element(By.XPATH,'(//div[text()="'+RANDOM_NAME+'"])')
    return RANDOM_NAME

def click_on_action(driver, user, UID):
    driver.find_element(By.XPATH, xpath.logout).click()
    logging.info("Logged out")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.NAME, "username"))
    )
    if user == "store":
        Pac_Credentials.Login_store(driver)
        driver.implicitly_wait(10)
        act_title = driver.find_element(
            By.XPATH, xpath.logout
        ).get_attribute("id")
        assert act_title == "pimcore_logout"
        logging.info("StoreLogin is successful.")
        wait_for_style_attribute(driver, 40)
    elif user == "airline":
        Pac_Credentials.Login_Airline(driver)
        driver.implicitly_wait(10)
        act_title = driver.find_element(
            By.XPATH, xpath.logout
        ).get_attribute("id")
        assert act_title == "pimcore_logout"
        logging.info("Airline Login is successful.")
        wait_for_style_attribute(driver, 40)
        try:
            for x in range(6):
                driver.find_element(
                    By.XPATH,
                    '(//div[@class="x-tool-tool-el x-tool-img x-tool-left "])[last()]',
                ).click()
        except:
            logging.info(" Screen loaded on one side properly.")
    elif user == "pac_admin":
        Pac_Credentials.Login_Pac_Admin(driver)
        driver.implicitly_wait(10)
        act_title = driver.find_element(
            By.XPATH, xpath.logout
        ).get_attribute("id")
        assert act_title == "pimcore_logout"
        logging.info("Admin Login is successful.")
        wait_for_style_attribute(driver, 40)
    search_by_id(driver, UID)
    WebDriverWait(driver, 60).until(
        (
            EC.presence_of_element_located(
                (By.XPATH, xpath.actionsXpath)
            )
        )
    )
    driver.find_element(By.XPATH, xpath.actionsXpath).click()
    logging.info("Clicked on Actions")

def move_modules_right_to_left(driver):
    try:
        for x in range(6):
            driver.find_element(
                By.XPATH,
                '(//div[@class="x-tool-tool-el x-tool-img x-tool-left "])[last()]',
            ).click()
    except:
        logging.info(" Screen loaded on one side properly.")

def auto_approve_catalog_off(driver, DEDICATED_STORE):
    Pac_Credentials.Login_store(driver)
    act_title = driver.find_element(
        By.XPATH, xpath.logout
    ).get_attribute("id")
    assert act_title == "pimcore_logout"
    logging.info("Login is successful.")
    wait_for_style_attribute(driver, 40)
    search_by_id(driver, DEDICATED_STORE_ID)

    (
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.XPATH, "//span[text()='Configuration']"))
        )
    ).click()
    x = driver.find_element(By.NAME, "autoapprovecatalog").is_selected()
    if x:
        logging.info("The store has auto approve - ON")
        driver.find_element(By.NAME, "autoapprovecatalog").click()
        time.sleep(3)
        assert not driver.find_element(By.NAME, "autoapprovecatalog").is_selected()
        logging.info("Changed to OFF")
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "//span[contains(text(),'Save & Publish')]")
            )
        )
        save_and_publish = driver.find_element(
            By.XPATH, "//span[contains(text(),'Save & Publish')]"
        )
        save_and_publish.click()
        # Check for the validation
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, xpath.saveSuccessfullyMessageXpath)
            )
        )
    else:
        logging.info("The store has auto approve - OFF")
    driver.find_element(By.XPATH, xpath.logout).click()
    logging.info("Logged out from Store Admin")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.NAME, "username"))
    )

def auto_approve_catalog_on(driver, DEDICATED_STORE):
    Pac_Credentials.Login_store(driver)
    driver.implicitly_wait(10)
    act_title = driver.find_element(
        By.XPATH, xpath.logout
    ).get_attribute("id")
    assert act_title == "pimcore_logout"
    logging.info("Login is successful.")
    wait_for_style_attribute(driver, 40)
    search_by_id(driver, DEDICATED_STORE_ID)
    (
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.XPATH, "//span[text()='Configuration']"))
        )
    ).click()
    x = driver.find_element(By.NAME, "autoapprovecatalog").is_selected()
    if x:
        logging.info("The store has auto approve - ON")
    else:
        driver.find_element(By.NAME, "autoapprovecatalog").click()
        assert driver.find_element(By.NAME, "autoapprovecatalog").is_selected()
        logging.info("Changed to ON")
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "//span[contains(text(),'Save & Publish')]")
            )
        )
        save_and_publish = driver.find_element(
            By.XPATH, "//span[contains(text(),'Save & Publish')]"
        )
        save_and_publish.click()
        # Check for the validation
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, xpath.saveSuccessfullyMessageXpath)
            )
        )
        logging.info("The store has auto approve - ON")
    driver.refresh()
    retryForProperLoading(driver, os.environ.get("MAX_RETRIES", 7))
    driver.find_element(By.XPATH, xpath.logout).click()
    logging.info("Logged out from Store Admin")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.NAME, "username"))
    )

def airline_auto_update_catalog_check_on(driver):
    logging.info("auto_checkmark_auto_update_catalog")
    Pac_Credentials.Login_Airline(driver)
    act_title = driver.find_element(
        By.XPATH, xpath.logout
    ).get_attribute("id")
    assert act_title == "pimcore_logout"
    logging.info("Login is successful.")
    wait_for_style_attribute(driver, 40)
    search_by_id(driver, DEDICATED_AIRLINE_ID)

    (
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.XPATH, "//span[text()='Configuration']"))
        )
    ).click()
    time.sleep(2)
    x = driver.find_element(By.NAME, "autoUpdateCatalog").is_selected()
    if not x:
        logging.info("The store has auto approve - OFF")
        driver.find_element(By.NAME, "autoUpdateCatalog").click()
        time.sleep(3)
        assert driver.find_element(By.NAME, "autoUpdateCatalog").is_selected()
        # Click on Save and Publish
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "//span[contains(text(),'Save & Publish')]")
            )
        )
        save_and_publish = driver.find_element(
            By.XPATH, "//span[contains(text(),'Save & Publish')]"
        )
        save_and_publish.click()
        # Check for the validation
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, xpath.saveSuccessfullyMessageXpath)
            )
        )
        logging.info("The value changed to Yes for Auto Populate Airline Categories")

    driver.find_element(By.XPATH, xpath.logout).click()
    logging.info("Logged out from Airline Admin")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.NAME, "username"))
    )

def airline_auto_update_catalog_check_off(driver):
    logging.info("auto_checkmark_auto_update_catalog")
    Pac_Credentials.Login_Airline(driver)
    act_title = driver.find_element(
        By.XPATH, xpath.logout
    ).get_attribute("id")
    assert act_title == "pimcore_logout"
    logging.info("Login is successful.")
    wait_for_style_attribute(driver, 40)
    search_by_id(driver, DEDICATED_AIRLINE_ID)

    (
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.XPATH, "//span[text()='Configuration']"))
        )
    ).click()
    time.sleep(2)
    x = driver.find_element(By.NAME, "autoUpdateCatalog").is_selected()
    if x:
        logging.info("The store has auto approve - ON")
        driver.find_element(By.NAME, "autoUpdateCatalog").click()

        assert not driver.find_element(By.NAME, "autoUpdateCatalog").is_selected()
        # Click on Save and Publish
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, "//span[contains(text(),'Save & Publish')]")
            )
        )
        save_and_publish = driver.find_element(
            By.XPATH, "//span[contains(text(),'Save & Publish')]"
        )
        save_and_publish.click()
        # Check for the validation
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, xpath.saveSuccessfullyMessageXpath)
            )
        )
        logging.info("The value changed to Yes for Auto Populate Airline Categories")

    driver.find_element(By.XPATH, xpath.logout).click()
    logging.info("Logged out from Airline Admin")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.NAME, "username"))
    )

def add_remove_product_in_catalog(driver):
    logging.info("auto_checkmark_auto_update_catalog")
    Pac_Credentials.Login_store(driver)
    act_title = driver.find_element(
        By.XPATH, xpath.logout
    ).get_attribute("id")
    assert act_title == "pimcore_logout"
    logging.info("Login is successful.")
    wait_for_style_attribute(driver, 40)
    search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_3_ID)
    
    # Navigate to Base Data tab
    WebDriverWait(driver, 60).until(
        (
            EC.presence_of_element_located(
                (By.XPATH, "//span[text()='Base Data' and contains(@class,'inner')]")
            )
        )
    )
    base_data = driver.find_element(
        By.XPATH, "//span[text()='Base Data' and contains(@class,'inner')]"
    )
    driver.execute_script("arguments[0].click();", base_data)
    logging.info("Clicked on Base Data")
    # WebDriverWait(driver, 60).until((EC.presence_of_element_located((By.XPATH,"//span[text()='button-1139-btnIconEl' and contains(@class,'pimcore_icon_open')]"))))
    driver.find_element(
        By.XPATH,
        '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_open "]',
    ).click()
    time.sleep(2)
    click_on_yes_no(driver)
    # Navigate to Products tab
    WebDriverWait(driver, 60).until(
        (EC.presence_of_element_located((By.XPATH, '(//span[text()="Products"])[2]')))
    )
    products = driver.find_element(By.XPATH, '(//span[text()="Products"])[2]')
    # products = driver.find_element(By.XPATH, "//span[text()='Products' and contains(@class,'inner')]")
    driver.execute_script("arguments[0].click();", products)
    logging.info("Clicked on Products")
    time.sleep(2)
    # Remove entry
    # driver.find_element(By.XPATH,"(//img[@data-qtip='Remove'])[1]").click()
    # WebDriverWait(driver, 60).until(
    #     (EC.presence_of_element_located((By.XPATH, "//span[text()='Yes']"))))
    # driver.find_element(By.XPATH,"//span[text()='Yes']").click()
    WebDriverWait(driver, 60).until(
        (
            EC.presence_of_element_located(
                (By.XPATH, "//span[contains(@class,'search')]")
            )
        )
    )
    driver.find_element(By.XPATH, "//span[contains(@class,'search')]").click()
    time.sleep(5)
    WebDriverWait(driver, 60).until(
        EC.element_to_be_clickable((By.XPATH, "//span[contains(text(),'Published')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(),'Published')]").click()
    time.sleep(5)
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "(//div[contains(text(),'/Cred Automation Store/Products')])[1]")
        )
    )
    x = driver.find_element(
        By.XPATH, "(//div[contains(text(),'/Cred Automation Store/Products')])[2]"
    )
    action = ActionChains(driver)
    action.double_click(x).perform()
    driver.find_element(By.XPATH, "//span[text()='Select']").click()
    # Click on Save and Publish
    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located(
            (By.XPATH, "//span[contains(text(),'Save & Publish')]")
        )
    )
    save_and_publish = driver.find_element(
        By.XPATH, "//span[contains(text(),'Save & Publish')]"
    )
    save_and_publish.click()
    # Check for the validation
    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located(
            (By.XPATH, xpath.saveSuccessfullyMessageXpath)
        )
    )
    logging.info("$$")
    # Search icon click
    time.sleep(10)
    driver.find_element(By.XPATH, xpath.logout).click()
    logging.info("Logged out from Airline Admin")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.NAME, "username"))
    )

def create_catalog_assignment_PAC_admin(driver):

    Pac_Credentials.Login_Pac_Admin(driver)
    act_title = driver.find_element(
        By.XPATH, xpath.logout
    ).get_attribute("id")
    assert act_title == "pimcore_logout"
    logging.info("Login is successful.")
    wait_for_style_attribute(driver, 40)
    click_on_add_object(driver, "Catalog")
    # Move to Catalog
    catalog = driver.find_element(By.XPATH, "(//a//span[text()='Catalog'])[1]")
    action = ActionChains(driver)
    action.move_to_element(catalog).perform()
    action.click(catalog).perform()
    # Click on Catalog Assignment
    ca_assignment = driver.find_element(
        By.XPATH, "(//a//span[text()='CatalogAssignment'])[1]"
    )
    driver.execute_script("arguments[0].click();", ca_assignment)
    logging.info("Clicked on Catalog Assignment")
    # Enter catalog assignment name
    ca_name = "".join(
        random.choice(string.ascii_letters + string.digits) for _ in range(10)
    )
    driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(ca_name)
    # Click on ok
    ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
    ok.click()
    logging.info(f"Clicked on ok after entering Catalog Assignment name as {ca_name}")
    # search icon
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[contains(text(),'Base Data')]")
        )
    )
    driver.find_element(
        By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
    ).click()
    WebDriverWait(driver, 60).until(
        (EC.visibility_of_element_located((By.XPATH, "//input[@name='query']")))
    )
    driver.find_element(By.XPATH, "//input[@name='query']").send_keys(
        DEDICATED_CATALOG_1_ID, Keys.ENTER
    )
    time.sleep(2)
    cred = driver.find_element(
        By.XPATH, "//div[contains(text(),'" + DEDICATED_CATALOG_1_NAME + "')]"
    )
    action = ActionChains(driver)
    action.double_click(cred).perform()
    logging.info("Selected a catalog")
    driver.find_element(By.NAME, "airline").send_keys(DEDICATED_AIRLINE, Keys.ENTER)
    driver.find_element(By.XPATH, "//span[text()='Catalog To Date:']").click()
    logging.info("Selected Airline")
    # Get UID
    UID = get_uid(driver)
    UID = UID.split()
    UID = UID[1]
    driver.find_element(
        By.XPATH,
        "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
    ).click()
    time.sleep(2)
    assignment_type = driver.find_element(
        By.XPATH, "//input[contains(@name,'assignmentType')]"
    )
    assignment_type.send_keys("RouteGroup")
    time.sleep(2)
    (
        WebDriverWait(driver, 60)
        .until(
            EC.element_to_be_clickable(
                (By.XPATH, "//li[contains(text(),'RouteGroup')]")
            )
        )
        .click()
    )
    time.sleep(5)
    logging.info("Selected Route Group as Assignment Type")
    driver.find_element(
        By.XPATH,
        "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
    ).click()
    driver.find_element(
        By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
    ).click()
    driver.find_element(
        By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
    ).send_keys(f"{DEDICATED_ROUTE_GROUP_1_NAME}")
    time.sleep(1)
    (
        WebDriverWait(driver, 60)
        .until(
            EC.visibility_of_element_located(
                (By.XPATH, f"//li[contains(text(),'{DEDICATED_ROUTE_GROUP_1_NAME}')]")
            )
        )
        .click()
    )
    logging.info("Selected Route Group")
    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located(
            (By.XPATH, xpath.saveSuccessfullyMessageXpath)
        )
    )
    logging.info("Saved successfully")
    return UID

def Assets_import_Store(driver):
    time.sleep(5)
    WebDriverWait(driver, 60).until(
        (EC.element_to_be_clickable((By.XPATH, xpath.assets)))
    )
    driver.find_element(By.XPATH, xpath.assets).click()
    import_plus = driver.find_element(
        By.XPATH,
        "//span[text()='Import']//parent::div//div[contains(@class,'expander')]",
    )
    driver.execute_script("arguments[0].scrollIntoView();", import_plus)
    driver.execute_script("arguments[0].click();", import_plus)
    logging.info("Clicked on Import")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[contains(text(),'" + PAC_STORE_USER_MAIL + "')]")
        )
    )
    driver.find_element(
        By.XPATH, "//span[contains(text(),'" + PAC_STORE_USER_MAIL + "')]"
    ).click()
    driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
    logging.info(f"Navigated to {PAC_STORE_USER_MAIL} - List View")
    time.sleep(5)
    try:
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (
                    By.XPATH,
                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
                )
            )
        )
        driver.find_element(
            By.XPATH,
            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
        ).click()
        logging.info("Navigated to last page")
        time.sleep(10)
    except Exception as e:
        logging.info("last page error")
        logging.info(e)
        pass

def click_on_yes_no(driver):
    try:
        WebDriverWait(driver, 4).until(
            EC.visibility_of_element_located(
                (
                    By.XPATH,
                    xpath.yes
                )
            )
        )
        if driver.find_element(
            By.XPATH, xpath.yes
        ).is_displayed():
            driver.find_element(By.XPATH, xpath.yes).click()
    except:
        logging.info("Modal is not shown, store not opened on any other device")

def save_and_publish(driver):
    try:
        driver.implicitly_wait(0)
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located(
                (By.XPATH, '//span[text()="Save & Publish"]')
            )
        )
        driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
        assert (
            WebDriverWait(driver, 60)
            .until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Saved successfully!"]')
                )
            )
            .is_displayed()
        )
        logging.info("Saved Successfully")
        time.sleep(2)
        return True
    except:
        return False

def Assets_import_Airline(driver):
    time.sleep(5)
    WebDriverWait(driver, 60).until(
        (EC.element_to_be_clickable((By.XPATH, xpath.assets)))
    )
    driver.find_element(By.XPATH, xpath.assets).click()
    import_plus = driver.find_element(
        By.XPATH,
        "//span[text()='Import']//parent::div//div[contains(@class,'expander')]",
    )
    driver.execute_script("arguments[0].scrollIntoView();", import_plus)
    driver.execute_script("arguments[0].click();", import_plus)
    logging.info("Clicked on Import")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[contains(text(),'" + PAC_AIRLINE_MAIL + "')]")
        )
    )
    driver.find_element(
        By.XPATH, "//span[contains(text(),'" + PAC_AIRLINE_MAIL + "')]"
    ).click()
    driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
    logging.info(f"Navigated to {PAC_AIRLINE_MAIL} - List View")
    time.sleep(5)
    try:
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (
                    By.XPATH,
                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
                )
            )
        )
        driver.find_element(
            By.XPATH,
            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
        ).click()
        logging.info("Navigated to last page")
        time.sleep(10)
    except Exception as e:
        logging.info("last page error")
        logging.info(e)
        pass

def Assets_import_Pac_admin(driver):
    time.sleep(5)
    WebDriverWait(driver, 60).until(
        (EC.element_to_be_clickable((By.XPATH, xpath.assets)))
    )
    driver.find_element(By.XPATH, xpath.assets).click()
    import_plus = driver.find_element(
        By.XPATH,
        "//span[text()='Import']//parent::div//div[contains(@class,'expander')]",
    )
    driver.execute_script("arguments[0].scrollIntoView();", import_plus)
    driver.execute_script("arguments[0].click();", import_plus)
    logging.info("Clicked on Import")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[text()='" + PAC_USER_MAIL + "']")
        )
    )
    driver.find_element(By.XPATH, "//span[text()='" + PAC_USER_MAIL + "']").click()
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.XPATH, "//span[contains(text(),'List')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
    logging.info(f"Navigated to {PAC_USER_MAIL} - List View")
    time.sleep(5)
    try:
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (
                    By.XPATH,
                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
                )
            )
        )
        driver.find_element(
            By.XPATH,
            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
        ).click()
        logging.info("Navigated to last page")
        time.sleep(10)
    except Exception as e:
        logging.info("last page error")
        logging.info(e)
        pass

def add_new_montly_catalog_load(driver):
    retries = 0
    while retries < 7:
        try:
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="Monthly Catalog Load"]/../../../../..//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )

            monthly_catalog = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="Monthly Catalog Load"]/../../../../..//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )
            action = ActionChains(driver)
            action.context_click(monthly_catalog).perform()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.addObjectXpath)
                )
            )
            time.sleep(1)
            add_object = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.monthlyCatalogsLoadsXpath + "[1]")
                    )
                )
                .click()
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).is_displayed()
            return True
        except Exception:
            driver.refresh()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Home')]")
                )
            )
            time.sleep(5)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1

    raise Exception(f"Element  not found after {7} retries")

def create_Parent_Product(productSet, deliveryMethod, productType):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=4)
    )
    try:
        payload = json.dumps(
            {
                "sku": RANDOM_NAME,
                "name": RANDOM_NAME,
                "shortDescription": "test Automation",
                "description": "this is from test Automation",
                "barcode": "",
                "deliveryMethod": [deliveryMethod],
                "nextFlightLeadTime": 1,
                "gatePickupLeadTime": 889,
                "category": [DEDICATED_CATEGORY_1_ID],
                "notApplicableCountries": ["IN"],
                "shippingAttributes": {},
                "assets": {"images": [{}]},
                "PriceTaxation": {
                    "price": [{"value": 0, "currency": "USD"}],
                    "specialPrice": [{"value": 0, "currency": "USD"}],
                    # "cost": [{"value": 0, "currency": "USD"}],
                    "isTaxable": True,
                    "orderMaxQuantityAllowed": 2,
                    "orderMinQuantityAllowed": 1,
                },
                "brand": "spark",
                "ageVerificationRequire": False,
                "isAvailableToSell": True,
                "Attributes": {
                    "productCategoryAttributes": [
                        {"key": "RAM", "value": "8GB", "isVariantAttribute": True}
                    ],
                    "storeSpecificAttributes": [],
                    "specialityAttributes": [],
                },
                "productSet": productSet,
                "storeProductId": "",
                "productType": productType,
                "spotLight": True,
                "requireShipping": False,
                "isPerishable": True,
            }
        )
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API for Product responded with 200 status code")
        return resp_data["id"]
    except:
        return None

def create_Variant_Product(count, parentID, productSet, deliveryMethod, productType):
    varientId = []
    for x in range(count):
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=4)
        )
        try:
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": RANDOM_NAME,
                    "parentProduct": parentID,
                    "shortDescription": "test Automation",
                    "description": "this is from test Automation",
                    "barcode": "",
                    "deliveryMethod": [deliveryMethod],
                    "nextFlightLeadTime": 1,
                    "gatePickupLeadTime": 889,
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "notApplicableCountries": ["IN"],
                    "shippingAttributes": {},
                    "assets": {"images": [{}]},
                    "PriceTaxation": {
                        "price": [{"value": 0, "currency": "USD"}],
                        "specialPrice": [{"value": 0, "currency": "USD"}],
                        # "cost": [{"value": 0, "currency": "USD"}],
                        "isTaxable": True,
                        "orderMaxQuantityAllowed": 2,
                        "orderMinQuantityAllowed": 1,
                    },
                    "brand": "spark",
                    "ageVerificationRequire": False,
                    "isAvailableToSell": True,
                    "Attributes": {
                        "productCategoryAttributes": [
                            {"key": "RAM", "value": "8GB", "isVariantAttribute": True}
                        ],
                        "storeSpecificAttributes": [],
                        "specialityAttributes": [],
                    },
                    "productSet": productSet,
                    "storeProductId": "",
                    "productType": productType,
                    "spotLight": True,
                    "requireShipping": False,
                    "isPerishable": True,
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", PRODUCTS_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            print(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API for Product responded with 200 status code")
            varientId.append(resp_data["id"])
        except:
            varientId = None
    return varientId

def click_on_add_object_airline(driver, elm_name, second_elem):
    retries = 0
    while retries < 7:
        try:
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'"+elm_name+"')]")
                )
            )
            # Move to home icon
            home = driver.find_element(
                By.XPATH, "//span[contains(text(),'"+elm_name+"')]"
            )
            actions = ActionChains(driver)
            actions.context_click(home).perform()

            ADD_OBJECT = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="'+second_elem+'"])[last()]')
                )
            )
            elm = driver.find_element(By.XPATH, '(//span[text()="'+second_elem+'"])[last()]')
            elm.click()
            driver.execute_script("arguments[0].click();", elm)
            logging.info(f"Clicked on {second_elem}")

            return True

        except Exception:
            driver.refresh()
            time.sleep(5)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1

    raise Exception(f"Element {elm_name} not found after {7} retries")

def create_delivery_rule(
    deliveryRuleType, shipmentGroupingType, shippingDestination, deliveryType, price
):
    try:
        payload = json.dumps(
            {
                "deliveryRuleType": deliveryRuleType,
                "shipmentGroupingType": shipmentGroupingType,
                "shippingDestination": shippingDestination,
                "deliveryType": [deliveryType],
                "title": "express",
                "description": "express",
                "price": price,
                "priceUnit": "USD",
                "duration": 44,
                "durationType": "days",
            }
        )
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", DELIVERY_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        return resp_data
    except:
        return None

def create_Simple_Product(deliveryMethod, productType, random_name):
    # RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=4))
    try:
        payload = json.dumps(
            {
                "sku": random_name,
                "name": random_name,
                "deliveryMethod": [deliveryMethod],
                "category": [int(DEDICATED_CATEGORY_1_ID)],
                "productSet": "Simple",
                "productType": productType,
            }
        )
        response = requests.request(
            "POST",
            PRODUCTS_URL,
            headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
            data=payload,
        )
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API for Product responded with 200 status code")
        resp_data = response.json()
        assert "Product added successfully" in resp_data["message"]
        UID = resp_data["id"]
        return UID
    except:
        return None

def airline_category_create_verify(driver, elm_name, second_elem, third_elem):
    retries = 0
    while retries < 7:
        try:
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '(//div[@class="x-title-text x-title-text-default x-title-item"])[3]',
                    )
                )
            )
            click_on_category_management = driver.find_element(
                By.XPATH,
                '(//div[@class="x-title-text x-title-text-default x-title-item"])[3]',
            )
            click_on_category_management.click()

            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Category')]")
                )
            )
            # Move to home icon
            home = driver.find_element(By.XPATH, "//span[contains(text(),'Category')]")
            actions = ActionChains(driver)
            actions.context_click(home).perform()

            ADD_OBJECT = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")

            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="Airline"])[last()]')
                )
            )
            elm = driver.find_element(By.XPATH, '(//span[text()="Airline"])[last()]')
            elm.click()
            logging.info(f"Clicked on {second_elem}")

            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="AirlineCategory"])[last()]')
                )
            )
            elm = driver.find_element(
                By.XPATH, '(//span[text()="AirlineCategory"])[last()]'
            )
            elm.click()
            logging.info(f"Clicked on {third_elem}")

            return True

        except Exception:
            driver.refresh()
            time.sleep(5)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1

    raise Exception(f"Element {elm_name} not found after {7} retries")

def airline_catalog_assignment(driver):
    retries = 0
    while retries < 7:
        try:

            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '//*[text()="Catalog Assignment"]')
                )
            )
            click_on_catlog_assignment = driver.find_element(
                By.XPATH, '//*[text()="Catalog Assignment"]'
            )
            click_on_catlog_assignment.click()

            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        f'//div[text()="Catalog Assignment"]/../../../../..//span[text()="{DEDICATED_AIRLINE}"]',
                    )
                )
            )
            # Move to home icon
            home = driver.find_element(
                By.XPATH,
                f'//div[text()="Catalog Assignment"]/../../../../..//span[text()="{DEDICATED_AIRLINE}"]',
            )
            actions = ActionChains(driver)
            actions.context_click(home).perform()

            ADD_OBJECT = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")

            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="CatalogAssignment"])[last()]')
                )
            )
            elm = driver.find_element(
                By.XPATH, '(//span[text()="CatalogAssignment"])[last()]'
            )
            elm.click()

            return True

        except Exception:
            driver.refresh()
            time.sleep(5)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1

    raise Exception(f"Element not found after {7} retries")

def set_cred_airline_auto_populate_map_update_airline_categories(
    driver, auto_map, auto_update
):
    try:
        search_by_id(driver, DEDICATED_AIRLINE_ID)
        if auto_map is None:
            logging.info("No changes in auto map auto populate airline category")
        elif auto_map.lower() == "yes":
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "(//span[text()='Stores'])[last()]")
                    )
                )
            ).click()
            time.sleep(2)
            x = driver.find_element(By.XPATH, "//td[4]").text
            y = driver.find_element(By.XPATH, "//td[5]").text
            if "Yes" not in x:
                driver.find_element(By.XPATH, "//td[4]").click()
                time.sleep(2)
                driver.find_element(By.XPATH, "//td[4]//input").send_keys(
                    Keys.CONTROL, "a", Keys.BACKSPACE
                )
                time.sleep(2)
                driver.find_element(By.XPATH, "//td[4]//input").send_keys(
                    "Yes", Keys.ENTER
                )
                time.sleep(2)
                # Click on Save and Publish
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                    )
                )
                save_and_publish = driver.find_element(
                    By.XPATH, "//span[contains(text(),'Save & Publish')]"
                )
                save_and_publish.click()
                # Check for the validation
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                    )
                )
                logging.info(
                    "The value changed to Yes for Auto Populate Airline Categories"
                )
            else:
                logging.info(
                    "The value is already Yes in Auto-Populate Airline Categories"
                )
            if "Yes" not in y:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, "//td[5]"))
                )
                driver.find_element(By.XPATH, "//td[5]").click()
                time.sleep(2)
                driver.find_element(By.XPATH, "//td[5]//input").send_keys(
                    Keys.CONTROL, "a", Keys.BACKSPACE
                )
                time.sleep(2)
                driver.find_element(By.XPATH, "//td[5]//input").send_keys(
                    "Yes", Keys.ENTER
                )
                # Click on Save and Publish
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                    )
                )
                save_and_publish = driver.find_element(
                    By.XPATH, "//span[contains(text(),'Save & Publish')]"
                )
                save_and_publish.click()
                # Check for the validation
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                    )
                )
                logging.info("The value changed to Yes for Auto Map Airline Categories")
                time.sleep(10)
            else:
                logging.info("The value is already Yes in Auto-Map Airline Categories")
        elif auto_map.lower() == "no":
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "(//span[text()='Stores'])[last()]")
                    )
                )
            ).click()
            time.sleep(2)
            x = driver.find_element(By.XPATH, "//td[4]").text
            y = driver.find_element(By.XPATH, "//td[5]").text
            if "Yes" in x:
                driver.find_element(By.XPATH, "//td[4]").click()
                time.sleep(2)
                driver.find_element(By.XPATH, "//td[4]//input").send_keys(
                    Keys.CONTROL, "a", Keys.BACKSPACE
                )
                time.sleep(2)
                driver.find_element(By.XPATH, "//td[4]//input").send_keys(
                    "No", Keys.ENTER
                )
                time.sleep(2)
                # Click on Save and Publish
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                    )
                )
                save_and_publish = driver.find_element(
                    By.XPATH, "//span[contains(text(),'Save & Publish')]"
                )
                save_and_publish.click()
                # Check for the validation
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                    )
                )
                logging.info(
                    "The value changed to No for Auto Populate Airline Categories"
                )
            else:
                logging.info(
                    "The value is already No in Auto-Populate Airline Categories"
                )
        if auto_update is None:
            logging.info("No changes in auto-update configuration")
        elif auto_update.lower() == "yes":
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[text()='Configuration']")
                    )
                )
            ).click()
            time.sleep(2)
            x = driver.find_element(By.NAME, "autoUpdateCatalog").is_selected()
            if not x:
                logging.info("The store has auto approve - OFF")
                driver.find_element(By.NAME, "autoUpdateCatalog").click()
                time.sleep(3)
                assert driver.find_element(By.NAME, "autoUpdateCatalog").is_selected()
                # Click on Save and Publish
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                    )
                )
                save_and_publish = driver.find_element(
                    By.XPATH, "//span[contains(text(),'Save & Publish')]"
                )
                save_and_publish.click()
                # Check for the validation
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                    )
                )
                logging.info(
                    "The value changed to Yes for Auto Populate Airline Categories"
                )
            else:
                logging.info("The value is already Yes in Auto-update Categories")
        elif auto_update.lower() == "no":
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[text()='Configuration']")
                    )
                )
            ).click()
            time.sleep(2)
            x = driver.find_element(By.NAME, "autoUpdateCatalog").is_selected()
            if x:
                logging.info("The store has auto approve - ON")
                driver.find_element(By.NAME, "autoUpdateCatalog").click()

                assert not driver.find_element(
                    By.NAME, "autoUpdateCatalog"
                ).is_selected()
                # Click on Save and Publish
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                    )
                )
                save_and_publish = driver.find_element(
                    By.XPATH, "//span[contains(text(),'Save & Publish')]"
                )
                save_and_publish.click()
                # Check for the validation
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                    )
                )
                logging.info(
                    "The value changed to Yes for Auto Populate Airline Categories"
                )
            else:
                logging.info("The value is already No in Auto-update Categories")
        driver.refresh()
        retryForProperLoading(driver, os.environ.get("MAX_RETRIES", 7))
    except Exception as e:
        logging.info(e)
        logging.info(
            "Failure in: cred_airline_auto_populate_auto_map_airline_categories"
        )
        raise e

def update_csv_multiple_row_number(file, row, field, code):
    df = pd.read_csv(os.getcwd() + "\\credencys_test_ui\\" + file)  # read CSV
    old_price = df.loc[row, field]
    logging.info(old_price)
    df.loc[row, field] = code  # update price in CSV
    logging.info(df.loc[row, field])
    df.to_csv(os.getcwd() + "\\credencys_test_ui\\" + file, index=False)
    logging.info(df)
    logging.info("Product data updated in CSV")

def airline_category_create_verify(driver):
    retries = 0
    while retries < 7:
        try:
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Category Management"]',
                        )
                    )
                )
                .click()
            )
            category = driver.find_element(By.XPATH, '//span[text()="Category"]')
            action = ActionChains(driver)
            action.context_click(category).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.addObjectXpath)
                )
            )
            time.sleep(1)
            add_object = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(airline).perform()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//span[text()="AirlineCategory"])[1]')
                    )
                )
                .click()
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            )

            return True

        except Exception:
            driver.refresh()
            time.sleep(5)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1

    raise Exception(f"Element not found after {7} retries")

def csv_import_store(driver, elm_name):
    WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))).click()
    driver.find_element(By.XPATH, xpath.csvImport).click()
    import_data_type = WebDriverWait(driver, 60).until(
        EC.element_to_be_clickable((By.NAME, xpath.importTypeName)))
    import_data_type.send_keys(elm_name)
    import_data_type.send_keys(Keys.ENTER)
    time.sleep(2)
    return True

def csv_import_airline(driver, elm_name):
        WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))).click()
        driver.find_element(By.XPATH, xpath.csvImport).click()
        import_data_type = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable((By.NAME, xpath.importTypeName)))
        import_data_type.send_keys(elm_name)
        time.sleep(1)
        import_data_type.send_keys(Keys.ENTER)
        time.sleep(2)
        return True

def csv_import_pac_admin(driver, elm_name):
    WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, xpath.pimcoreMenu_id))).click()
    driver.find_element(By.XPATH, xpath.csvImport).click()
    import_data_type = WebDriverWait(driver, 60).until(
        EC.element_to_be_clickable((By.NAME, xpath.importTypeName)))
    import_data_type.send_keys(elm_name)
    import_data_type.send_keys(Keys.ENTER)
    airlineList = driver.find_element(By.NAME, xpath.airlineListName)
    airlineList.click()
    time.sleep(2)
    airlineList.send_keys(DEDICATED_AIRLINE, Keys.DOWN, Keys.DOWN)
    # time.sleep(2)
    # airlineList.send_keys(Keys.ENTER)
    WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, f"//li[text()='{DEDICATED_AIRLINE}']")))
    driver.find_element(By.XPATH, f"//li[text()='{DEDICATED_AIRLINE}']").click()
    time.sleep(5)
    return True

def verify_import_logs_airline(driver,path, validationMess):
    list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'/Import/"+PAC_AIRLINE_MAIL+"/"+path+"')]")
    num = len(list1)
    time.sleep(5)
    row = driver.find_element(By.XPATH, "(//div[contains(text(),'/Import/"+PAC_AIRLINE_MAIL+"/"+path+"')])["+str(num)+"]")
    actions = ActionChains(driver)
    actions.double_click(row).perform()
    # open = driver.find_element(By.XPATH, xpath.open)
    # open.click()
    # logging.info("open clicked")
    # Open Code if modal appears
    click_on_yes_no(driver)
    # try:
    #     if driver.find_element(By.XPATH, xpath.yes).is_displayed():
    #         driver.find_element(By.XPATH, xpath.yes).click()
    # except:
    #     logging.info("Modal is not shown, store not opened on any other device")
    logging.info("Opened the log file - json")
    assert (WebDriverWait(driver, 60).until(
        EC.presence_of_element_located(
            (By.XPATH, '//div[contains(text(),"'+validationMess+'")]'))).is_displayed())
    return True

def verify_import_logs_pac_admin(driver,path, validationMess):
    list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'/Import/"+PAC_USER_MAIL+"/"+path+"')]")
    num = len(list1)
    time.sleep(5)
    row = driver.find_element(By.XPATH, "(//div[contains(text(),'/Import/"+PAC_USER_MAIL+"/"+path+"')])["+str(num)+"]")
    actions = ActionChains(driver)
    actions.context_click(row).perform()
    open = driver.find_element(By.XPATH, xpath.openXpath)
    open.click()
    logging.info("open clicked")
    # Open Code if modal appears
    click_on_yes_no(driver)
    logging.info("Opened the log file - json")
    assert (WebDriverWait(driver, 60).until(
        EC.presence_of_element_located(
            (By.XPATH, '//div[contains(text(),"'+validationMess+'")]'))).is_displayed())
    return True

def verify_import_logs_store(driver,path, validationMess):
    list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'/Import/"+PAC_STORE_USER_MAIL+"/"+path+"')]")
    num = len(list1)
    time.sleep(5)
    row = driver.find_element(By.XPATH, "(//div[contains(text(),'/Import/"+PAC_STORE_USER_MAIL+"/"+path+"')])["+str(num)+"]")
    actions = ActionChains(driver)
    actions.context_click(row).perform()
    open = driver.find_element(By.XPATH, xpath.openXpath)
    open.click()
    logging.info("open clicked")
    # Open Code if modal appears
    click_on_yes_no(driver)
    logging.info("Opened the log file - json")
    assert (WebDriverWait(driver, 10).until(
        EC.presence_of_element_located(
            (By.XPATH, '//div[contains(text(),"'+validationMess+'")]'))).is_displayed())
    return True

def modify_WorkFlow_Status(id, token, status):
    payload = json.dumps({"catalogAssignmentId": int(id), "status": status})
    headers = {"Authorization": token, "Content-Type": "application/json"}
    # 9 Verify if the mandatory parameters are empty in the request
    response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
    data = response.json()
    logging.info("------Inside modify_WorkFlow_Status -------")
    logging.info(data)

def verify_import_logs(driver, mail, path, validationMess):
    try:
        list1 = driver.find_elements(
            By.XPATH, "//div[contains(text(),'/Import/" + mail + "/" + path + "')]"
        )
        num = len(list1)
        time.sleep(5)
        row = driver.find_element(
            By.XPATH,
            "(//div[contains(text(),'/Import/"
            + mail
            + "/"
            + path
            + "')])["
            + str(num)
            + "]",
        )
        actions = ActionChains(driver)
        actions.context_click(row).perform()
        open = driver.find_element(By.XPATH, xpath.openXpath)
        open.click()
        logging.info("open clicked")
        # Open Code if modal appears
        click_on_yes_no(driver)
        logging.info("Opened the log file - json")
        assert (
            WebDriverWait(driver, 60)
            .until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//div[contains(text(),"' + validationMess + '")])[1]')
                )
            )
            .is_displayed()
        )
        return True
    except Exception:
        logging.info("Validation not found")

def route_API_Payload(
    sectorName, routeGroup, destination, origin, distance, token, request, url
):
    try:
        payload = json.dumps(
            {
                "sectorName": sectorName,
                "summary": "test summary",
                "routeGroup": routeGroup,
                "destination": destination,
                "origin": origin,
                "distance": distance,
            }
        )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data
    except:
        return False

def route_Group_Payload(name, token, request, url):
    try:
        payload = json.dumps({"name": name})
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data
    except:
        return False

def click_on_add_object_Store(driver, elm_name):
    retries = 0
    while retries < 7:
        try:
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        f"(//span[contains(text(),'{DEDICATED_STORE}')])[last()]",
                    )
                )
            )
            # Move to home icon
            home = driver.find_element(
                By.XPATH, f"(//span[contains(text(),'{DEDICATED_STORE}')])[last()]"
            )
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            ADD_OBJECT = driver.find_element(
                By.XPATH, xpath.addObjectXpath
            )
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, f'(//span[text()="{elm_name}"])[last()]')
                )
            )
            elm = driver.find_element(
                By.XPATH, f'(//span[text()="{elm_name}"])[last()]'
            )
            elm.click()
            driver.execute_script("arguments[0].click();", elm)
            logging.info(f"Clicked on {elm_name}")
            return True
        except Exception:
            driver.refresh()
            time.sleep(5)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1
    raise Exception(f"Element {elm_name} not found after {7} retries")

def airline_category_API(RANDOM_NAME, uiTemplate, rootType):
    try:
        payload = json.dumps(
            {
                "name": RANDOM_NAME,
                "disclaimer": "disclaimer",
                "shortDescription": "shortDescription",
                "description": "description",
                "url": "",
                "images": [{"image1_1": "https://placehold.jp/1200x1200.png"}],
                "bannerImage": "https://placehold.jp/3160x632.png",
                "backgroundImage": "https://placehold.jp/3840x2160.png",
                "uiTemplate": uiTemplate,
                "rootType": rootType,
            }
        )
        response = requests.request(
            type,
            AIRLINE_CATEGORY_URL,
            headers={"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN},
            data=payload,
        )
        return response
    except:
        return None

def update_csv_for_multiple_fields(file, field_values):
    # logging.info("In update_csv")
    df = pd.read_csv(os.getcwd() + "/credencys_test_ui/" + file)  # read CSV

    for field, code in field_values.items():
        old_value = df.loc[0, field]
        logging.info(f"Old value for {field}: {old_value}")
        df.loc[0, field] = code  # update value in CSV
        logging.info(f"Updated value for {field}: {df.loc[0, field]}")

    df.to_csv(os.getcwd() + "/credencys_test_ui/" + file, index=False)
    logging.info("Product data updated in CSV")

def store_configuration(driver, ID):
    retries = 0
    while retries < 7:
        try:
            search_by_id(driver, ID)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.configurationTabXpath + "[1]")
                    )
                )
                .click()
            )
            logging.info("clicked on configuration")
            assert (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.manageShippingXpath)
                    )
                )
                .is_displayed()
            )
            return True

        except Exception:
            driver.refresh()
            time.sleep(2)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1

    raise Exception(f"Element not found after {7} retries")

def csv_import(driver, object):
    WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
    driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
    driver.find_element(By.XPATH, xpath.csvImport).click()
    data_type = driver.find_element(By.NAME, xpath.importTypeName)
    data_type.send_keys(object)
    time.sleep(1)
    data_type.send_keys(Keys.ENTER)
    assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.upload))).is_displayed())
    return True

def store_configuration(driver, ID):
    retries = 0
    while retries < 7:
        try:
            search_by_id(driver, ID)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.configurationTabXpath + "[1]")
                    )
                )
                .click()
            )
            logging.info("clicked on configuration")
            assert (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.manageShippingXpath)
                    )
                )
                .is_displayed()
            )
            return True

        except Exception:
            driver.refresh()
            time.sleep(2)
            logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
            retries += 1

    raise Exception(f"Element not found after {7} retries")

# def category_creation(driver):
#     retries = 0
#     RANDOM_NAME = "From_Automation_" + "".join(
#         random.choices(string.ascii_letters, k=7)
#     )
#     while retries < 7:
#         try:
#             click_on_add_object(driver, "Category")
#             (
#                 WebDriverWait(driver, 60)
#                 .until(
#                     EC.presence_of_element_located(
#                         (By.XPATH, xpath.popupNameFieldXpath)
#                     )
#                 )
#                 .send_keys(RANDOM_NAME)
#             )
#             (
#                 WebDriverWait(driver, 60)
#                 .until(EC.presence_of_element_located((By.XPATH, xpath.okButtonXpath)))
#                 .click()
#             )
#             assert (
#                 WebDriverWait(driver, 60)
#                 .until(EC.presence_of_element_located((By.NAME, "name")))
#                 .is_displayed()
#             )
#             return True

#         except Exception:
#             driver.refresh()
#             time.sleep(2)
#             logging.info(f"Element not found. Retrying... ({retries + 1}/{7})")
#             retries += 1

#     raise Exception(f"Element not found after {7} retries")

def get_Simple_Product_Payload(
    RANDOM_NAME, deliveryMethod, category, productSet, productType
):
    payload = json.dumps(
        {
            "sku": RANDOM_NAME,
            "name": RANDOM_NAME,
            "deliveryMethod": [deliveryMethod],
            "category": [int(category)],
            "productSet": productSet,
            "productType": productType,
        }
    )
    return payload

def get_Product_Payload(
    RANDOM_NAME,
    parentProduct,
    deliveryMethod,
    category,
    productSet,
    productType,
    PriceTaxation_price_value,
    PriceTaxation_special_price_value,
    PriceTaxation_cost_value,
    productCategoryAttributesKey,
    productCategoryAttributesValue,
    isVariantAttribute,
):
    payload = json.dumps(
        {
            "sku": RANDOM_NAME,
            "name": RANDOM_NAME,
            "parentProduct": int(parentProduct),
            "shortDescription": "",
            "description": "",
            "barcode": "",
            "deliveryMethod": [deliveryMethod],
            # "nextFlightLeadTime": 1,
            # "gatePickupLeadTime": 889,
            "category": [int(category)],
            "shippingAttributes": {
                "weight": 0,
                "weightUnit": "",
                "height": 10,
                "heightUnit": "IN",
                "width": 10,
                "widthUnit": "IN",
                "length": 10,
                "lengthUnit": "IN",
            },
            "PriceTaxation": {
                "price": [{"value": PriceTaxation_price_value, "currency": "USD"}],
                "specialPrice": [
                    {"value": PriceTaxation_special_price_value, "currency": "USD"}
                ],
                # "cost": [{"value": PriceTaxation_cost_value, "currency": "USD"}],
                "isTaxable": True,
                "orderMaxQuantityAllowed": 300,
                "orderMinQuantityAllowed": 10,
            },
            "brand": "Spark1",
            "ageVerificationRequire": True,
            "isAvailableToSell": False,
            "Attributes": {
                "storeSpecificAttributes": [{"key": "", "value": ""}],
                "productCategoryAttributes": [
                    {
                        "key": productCategoryAttributesKey,
                        "value": productCategoryAttributesValue,
                        "isVariantAttribute": isVariantAttribute,
                    }
                ],
            },
            "notApplicableCountries": ["IN", "US"],
            "productSet": productSet,
            "storeProductId": "",
            "productType": productType,
            # "spotLight": True,
            # "isPerishable": False,
            # "requireShipping": True
        }
    )
    return payload

def get_RouteCatalog_Payload(
    RANDOM_NAME,
    catologID,
    productID,
    airlinecategoryID,
    routegroupID,
    sectorID,
    excluded,
    catalogFromDate,
    catalogToDate,
    cabinclass,
):
    payload = json.dumps(
        {
            "name": RANDOM_NAME,
            "catalog": {
                "id": int(catologID),
                "products": [
                    {"productId": int(productID), "airlineCategory": [int(airlinecategoryID)]}
                ],
            },
            "routeGroup": {
                "id": int(routegroupID),
                "sectors": [{"id": int(sectorID), "flights": {}, "excluded": excluded}],
            },
            "catalogFromDate": catalogFromDate,
            "catalogToDate": catalogToDate,
            "cabinClass": cabinclass,
        }
    )
    return payload

def get_payload_taxPercentage_API(random_float):
    payload = json.dumps({"taxPercentage": random_float})
    return payload

def search_by_path(driver, path):
    # body_element = driver.find_element(
    #     By.XPATH, "(//div[contains(@id,'pimcore_panel_tabs-body')])[2]"
    # )
    # body_element.click()
    logging.info(f"Clicked on the open area for search window")
    actions = ActionChains(driver)
    actions.key_down(Keys.CONTROL).key_down(Keys.SHIFT).send_keys("o").key_up(
        Keys.CONTROL
    ).key_up(Keys.SHIFT).perform()
    driver.find_element(
        By.XPATH, "//input[contains(@name,'textfield-inputEl')]"
    ).send_keys(path)
    driver.find_element(By.XPATH, "//span[contains(text(),'OK')]").click()
    logging.info(f"Click on OK post search the id.")
    click_on_yes_no(driver)

def new_csv_import(driver, object):
    WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.pimcoreMenuXpath))
            )
    driver.find_element(By.XPATH, xpath.pimcoreMenuXpath).click()
    time.sleep(1)
    driver.find_element(By.XPATH, xpath.csvImport).click()
    WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.importTypeNameXpath))
            )
    data_type = driver.find_element(By.XPATH, xpath.importTypeNameXpath)
    data_type.send_keys(object)
    time.sleep(1)
    data_type.send_keys(Keys.ENTER)
    assert (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.upload))).is_displayed())
    return True

# Use for the Simple product[productSet]
def create_Product(
    productSet,
    deliveryMethod,
    productType,
    productsku,
    newfromdate,
    newtodate,
    category_id,
):
    try:
        payload = json.dumps(
            {
                "sku": productsku,
                "name": productsku,
                "deliveryMethod": [deliveryMethod],
                "category": int(category_id),
                "newFrom": newfromdate,
                "newTo": newtodate,
                "productSet": productSet,
                "productType": productType,
            }
        )
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        return resp_data
    except Exception as e:
        logging.info(e)
        return None

def product_api_payload(
    token,
    request,
    url,
    sku_name,
    name,
    shortDescription,
    description,
    barcode,
    deliveryMethod,
    category,
    weight,
    weightUnit,
    height,
    heightUnit,
    width,
    widthUnit,
    length,
    lengthUnit,
    newFrom,
    newTo,
    price,
    specialPrice,
    cost,
    isTaxable,
):
    try:
        payload = json.dumps(
            {
                "sku": sku_name,
                "name": name,
                "shortDescription": shortDescription,
                "description": description,
                "barcode": barcode,
                "deliveryMethod": [deliveryMethod],
                "category": [int(category)],
                "shippingAttributes": {
                    "weight": weight,
                    "weightUnit": weightUnit,
                    "height": height,
                    "heightUnit": heightUnit,
                    "width": width,
                    "widthUnit": widthUnit,
                    "length": length,
                    "lengthUnit": lengthUnit,
                },
                "newFrom": newFrom,
                "newTo": newTo,
                "PriceTaxation": {
                    "price": [{"value": price, "currency": "USD"}],
                    "specialPrice": [{"value": specialPrice, "currency": "USD"}],
                    # "cost": [{"value": cost, "currency": "USD"}],
                    "isTaxable": isTaxable,
                    "orderMaxQuantityAllowed": 2,
                    "orderMinQuantityAllowed": 1,
                },
                "brand": "spark",
                "ageVerificationRequire": False,
                "isAvailableToSell": True,
                "productSet": "Simple",
                "storeProductId": "134A34",
                "productType": "Food and Beverage",
                "spotLight": True,
                "requireShipping": False,
                "isPerishable": True,
            }
        )

        headers = {"Authorization": token, "Content-Type": "application/json"}
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data
    except Exception as e:
        logging.info(e)
        return False

def search_by_path(driver, path):
    # body_element = driver.find_element(
    #     By.XPATH, "(//div[contains(@id,'pimcore_panel_tabs-body')])[2]"
    # )
    # body_element.click()
    logging.info(f"Clicked on the open area for search window")
    actions = ActionChains(driver)
    actions.key_down(Keys.CONTROL).key_down(Keys.SHIFT).send_keys("o").key_up(
        Keys.CONTROL
    ).key_up(Keys.SHIFT).perform()
    driver.find_element(
        By.XPATH, "//input[contains(@name,'textfield-inputEl')]"
    ).send_keys(path)
    driver.find_element(By.XPATH, "//span[contains(text(),'OK')]").click()
    logging.info(f"Click on OK post search the id.")
    click_on_yes_no(driver)

def order_api_payload(firstName1,lastName1,email1,firstName,lastName,address1,city,state,postalCode,countryCode,email,arrivalAirportCodeIATA,departureAirportCodeIATA,flightNumber,airlineCodeICAO, externalId, createdTime,retailerCode,associateStore, status1, status, paymentService, mode,seatClass, seatNumber, groundSystem, currency, token, request, url):
    try:
        payload = json.dumps({
            "meta": {
                "airlineCodeICAO": airlineCodeICAO,
                "flightNumber": flightNumber,
                "arrivalAirportCodeIATA": arrivalAirportCodeIATA,
                "departureAirportCodeIATA": departureAirportCodeIATA,
                "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
            },
            "orders": [
                {
                    "orderKey": "From_Automation_3328 improvement current flight shipment",
                    "externalId": externalId,
                    "basketKey": "From_Automation_3328 improvement current flight shipment",
                    "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                    "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                    "createdTime": createdTime,
                    "lineItems": [
                        {
                            "title": "Demo Line Item",
                            "PACVariantID": "1hospitality:mktplvb48943-var",
                            "retailerCode": retailerCode,
                            "associateStore": str(associateStore),
                            "fulfillmentType": "inHouse",
                            "alwaysInStock": False,
                            "imageUrl": "https://images.test/file.jpg",
                            "vendorProductVariantID": str(DEDICATED_SKU_1_ID),
                            "unitPrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "unitTax": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "unitDiscount": {
                                "value": 4.9,
                                "currency": "USD"
                            },
                            "unitNet": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "unitGross": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "quantity": 2,
                            "status": status1,
                            "discountTotal": {
                                "currency": "USD",
                                "value": 9.8
                            },
                            "taxTotal": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "discountAdjustments": [
                                {
                                    "discountAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "adjustType": "DISCOUNT",
                                    "rate": 10,
                                    "promotionCode": "asd",
                                    "promotionName": "Percentage discount 10%"
                                }
                            ],
                            "taxAdjustments": [
                                {
                                    "type": "shipping_rule_tax",
                                    "taxAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "rate": 7
                                }
                            ],
                            "salePrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                        }
                    ],
                    "status": status,
                    "payment": [
                        {
                            "paymentService": paymentService,
                            "paymentMethod": "CARD",
                            "authAmount": {
                                "currency": "USD",
                                "value": 230
                            },
                            "paymentId": "BBAA-0324111-88",
                            "technicalServiceProviderTransactionId": "20220324173949153132",
                            "gatewayTransactionId": "A60087687",
                            "status": "AUTHORIZED",
                            "billingAddress": {
                                "firstName": "karan",
                                "lastName": "bhatt",
                                "address1": "123",
                                "city": "arizona",
                                "state": "Los angeles",
                                "postalCode": "78945",
                                "countryCode": "US",
                                "email": "<EMAIL>"
                            }
                        }
                    ],
                    "shipments": [
                        {
                            "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                            "rate": {
                                "currency": "USD",
                                "value": 50
                            },
                            "shippingMethod": "STANDARD",
                            "carrier": "DHL",
                            "taxTotal": [
                                {
                                    "currency": "USD",
                                    "value": 12
                                }
                            ],
                            "address": {
                                "firstName": firstName,
                                "lastName": lastName,
                                "address1": address1,
                                "city": city,
                                "state": state,
                                "postalCode": postalCode,
                                "countryCode": countryCode,
                                "email": email
                            },
                            "itemKeys": [
                                "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                            ],
                            "item": [
                                "21074012"
                            ],
                            "mode": mode
                        }
                    ],
                    "orderSummary": {
                        "grossTotal": {
                            "currency": "USD",
                            "value": 200
                        },
                        "discounts": [
                            {
                                "discountAmount": {
                                    "value": 20,
                                    "currency": "USD"
                                },
                                "promotionCode": "New",
                                "promotionName": "Percentage discount 10%",
                                "rate": 0
                            }
                        ],
                        "adjustmentTotal": {
                            "currency": "USD",
                            "value": 20
                        },
                        "taxes": [
                            {
                                "type": "shipping_rule_tax",
                                "taxAmount": {
                                    "currency": "USD",
                                    "value": 12
                                },
                                "rate": 7
                            }
                        ],
                        "totalTaxAmount": {
                            "currency": "USD",
                            "value": 12
                        },
                        "shippingTotal": {
                            "currency": "USD",
                            "value": 50
                        },
                        "currency": "USD",
                        "netTotal": {
                            "currency": currency,
                            "value": 230
                        }
                    },
                    "user": {
                        "firstName": firstName1,
                        "lastName": lastName1,
                        "email": email1
                    },
                    "itinerary": {
                        "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                        "confirmationCode": "6kcb2s",
                        "airline": "SQ322",
                        "firstName": "RAJU",
                        "lastName": "sasi",
                        "middleName": "s"
                    },
                    "seatInfo": {
                        "seatClass": seatClass,
                        "seatNumber": seatNumber
                    },
                    "groundSystem": groundSystem
                }
            ]
        })
        headers = {
            'Authorization': token,
            'Content-Type': 'application/json'
        }
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data
    except Exception as e:
        logging.info(e)
        return False

def Trackinginfoimport_log_store(field, code, Message):
    try:
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
        driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

        WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
        driver.find_element(By.XPATH, xpath.csvImport).click()

        WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
        data_type = driver.find_element(By.NAME, xpath.importTypeName)
        data_type.send_keys("TrackingInfo")
        time.sleep(1)
        data_type.send_keys(Keys.ENTER)

        update_csv("order_trackingInfo.csv", field, code)

        path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
        driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

        driver.find_element(By.XPATH, xpath.upload).click()

        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located((By.XPATH, xpath.logout)))
        driver.find_element(By.XPATH, xpath.logout).click()

        Pac_Credentials.Login_Pac_Admin(driver)
        act_title = driver.find_element(
            By.XPATH, xpath.logout
        ).get_attribute("id")
        assert act_title == "pimcore_logout"
        logging.info("Login is successful.")

        Assets_import_Store(driver)

        list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
        num = len(list1)
        time.sleep(5)
        row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
        actions = ActionChains(driver)
        actions.double_click(row).perform()
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (By.XPATH, '(//div[@class="ace_line"])[4]')))
        mass = driver.find_element(By.XPATH,
                                   '(//div[@class="ace_line"])[4]').text
        logging.info(mass)
        assert Message in mass

        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located((By.XPATH, xpath.logout)))
        driver.find_element(By.XPATH, xpath.logout).click()

        Pac_Credentials.Login_store(driver)
        act_title = driver.find_element(
            By.XPATH, xpath.logout
        ).get_attribute("id")
        assert act_title == "pimcore_logout"
        logging.info("Login is successful.")

    except Exception as e:
        logging.info(e)

def manageshipping(driver, manageshipping):
    search_by_id(driver, DEDICATED_STORE_ID)

    (WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.XPATH,
                                        "//span[text()='Configuration']")))).click()
    x = driver.find_element(By.NAME, "manageShipping").get_attribute("value")
    if manageshipping.lower() == "yes":
        if x == "No":
            logging.info("The manage shipping is - NO")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'manageShipping')))
            manageShipping_field = driver.find_element(By.NAME, 'manageShipping')
            manageShipping_field.clear()
            manageShipping_field.send_keys("Yes")
            manageShipping_field.send_keys(Keys.ENTER)

            logging.info("Changed to YES")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'Save & Publish')]")))
            save_and_publish = driver.find_element(By.XPATH, "//span[contains(text(),'Save & Publish')]")
            save_and_publish.click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Saved successfully!')]")))
        else:
            logging.info("The manage shipping is - YES")
    elif manageshipping.lower() == "no":
        if x == "No":
            logging.info("The manage shipping is - No")
        else:
            logging.info("The manage shipping is - Yes")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'manageShipping')))
            manageShipping_field = driver.find_element(By.NAME, 'manageShipping')
            manageShipping_field.clear()
            manageShipping_field.send_keys("No")
            manageShipping_field.send_keys(Keys.ENTER)

            logging.info("Changed to No")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'Save & Publish')]")))
            save_and_publish = driver.find_element(By.XPATH, "//span[contains(text(),'Save & Publish')]")
            save_and_publish.click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Saved successfully!')]")))
    close_button = driver.find_element(
        By.XPATH, "(//span[@class='x-tab-close-btn'])[1]"
    )
    close_button.click()

def managestock(driver, managestock):
    search_by_id(driver, DEDICATED_STORE_ID)

    (WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.XPATH,
                                        "//span[text()='Configuration']")))).click()
    x = driver.find_element(By.NAME, "manageStock").get_attribute("value")
    if managestock.lower() == "yes":
        if x == "No":
            logging.info("The manageStock is - NO")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'manageStock')))
            manageStock_field = driver.find_element(By.NAME, 'manageStock')
            manageStock_field.clear()
            manageStock_field.send_keys("Yes")
            manageStock_field.send_keys(Keys.ENTER)

            logging.info("Changed to YES")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'Save & Publish')]")))
            save_and_publish = driver.find_element(By.XPATH, "//span[contains(text(),'Save & Publish')]")
            save_and_publish.click()
            
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Saved successfully!')]")))
        else:
            logging.info("The manageStock is - YES")
    elif managestock.lower() == "no":
        if x == "No":
            logging.info("The manageStock is - No")
        else:
            logging.info("The manageStock is - Yes")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'manageStock')))
            manageStock_field = driver.find_element(By.NAME, 'manageStock')
            manageStock_field.clear()
            manageStock_field.send_keys("No")
            manageStock_field.send_keys(Keys.ENTER)
            
            logging.info("Changed to No")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'Save & Publish')]")))
            save_and_publish = driver.find_element(By.XPATH, "//span[contains(text(),'Save & Publish')]")
            save_and_publish.click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Saved successfully!')]")))
    close_button = driver.find_element(
        By.XPATH, "(//span[@class='x-tab-close-btn'])[1]"
    )
    close_button.click()

def Trackinginfoimport_log_admin(fild, code, Message):
    try:
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
        driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

        WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
        driver.find_element(By.XPATH, xpath.csvImport).click()

        WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
        data_type = driver.find_element(By.NAME, xpath.importTypeName)
        data_type.send_keys("TrackingInfo")
        time.sleep(1)
        data_type.send_keys(Keys.ENTER)

        WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.storeName)))
        store = driver.find_element(By.NAME, xpath.storeName)
        store.send_keys("Cred Automation ", Keys.TAB)
        time.sleep(5)

        update_csv("order_trackingInfo.csv", fild, code)

        path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
        driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

        driver.find_element(By.XPATH, xpath.upload).click()

        Assets_import_Pac_admin(driver)

        list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
        num = len(list1)
        time.sleep(5)
        row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
        actions = ActionChains(driver)
        actions.double_click(row).perform()
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (By.XPATH, '(//div[@class="ace_line"])[4]')))
        mass = driver.find_element(By.XPATH,
                                   '(//div[@class="ace_line"])[4]').text
        logging.info(mass)
        assert Message in mass
    except Exception as e:
        logging.info(e)

def order_post_payload(request, url, token, airlineCodeICAO, flightNumber, arrivalAirportCodeIATA, departureAirportCodeIATA, orderKey,
                       externalId, authAmountCurrency, authCurrencyValue, paymentId, billingAddressFirstName,
                       billingAddressLastName, billingAddressAddress1, billingAddressCity, billingAddressState,
                       billingAddressPostalCode, billingAddressCountryCode, billingAddressEmail, grossTotalCurrency,
                       grossTotalValue, taxesType, taxAmountCurrency, taxValue, taxesRate, userFirstName, userLastName,
                       userEmail, netTotalCurrency,netTotalValue):
    try:
        payload = json.dumps({"meta": {
            "airlineCodeICAO": airlineCodeICAO,
            "flightNumber": flightNumber,
            # "flightNumber": "13KNTV34",
            "arrivalAirportCodeIATA": arrivalAirportCodeIATA,
            "departureAirportCodeIATA": departureAirportCodeIATA,
            "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
            "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
        },
            "orders": [
                {
                    "orderKey": str(orderKey),
                    "externalId": str(externalId),
                    "basketKey": "From_Automation_3328 improvement current flight shipment",
                    "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                    "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                    "createdTime": "2024-05-24T17:39:51.287+00:00",
                    "lineItems": [
                        {
                            "title": "KRIS ⋮ JUNI12OR POLAR1 BEAR BY JELLYCAT®",
                            "PACVariantID": "1hospitality:mktplvb48943-var",
                            "retailerCode": "random",
                            "associateStore": str(DEDICATED_STORE_ID),
                            "fulfillmentType": "inHouse",
                            "alwaysInStock": False,
                            "imageUrl": "https://images.test/file.jpg",
                            "vendorProductVariantID": str(DEDICATED_SKU_1_ID),
                            "unitPrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "unitTax": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "unitDiscount": {
                                "value": 4.9,
                                "currency": "USD"
                            },
                            "unitNet": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "unitGross": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "quantity": 2,
                            "status": "DELIVERED",
                            "discountTotal": {
                                "currency": "USD",
                                "value": 9.8
                            },
                            "taxTotal": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "discountAdjustments": [
                                {
                                    "discountAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "adjustType": "DISCOUNT",
                                    "rate": 10,
                                    "promotionCode": "asd",
                                    "promotionName": "Percentage discount 10%"
                                }
                            ],
                            "taxAdjustments": [
                                {
                                    "type": "shipping_rule_tax",
                                    "taxAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "rate": 7
                                }
                            ],
                            "salePrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                        }
                    ],
                    "status": "CALL_CREW",
                    "payment": [
                        {
                            "paymentService": "INTERNAL",
                            "paymentMethod": "CARD",
                            "authAmount": {
                                "currency": authAmountCurrency,
                                "value": authCurrencyValue
                            },
                            "paymentId": paymentId,
                            # "paymentId": "BBAA-0324111-88",
                            "technicalServiceProviderTransactionId": "20220324173949153132",
                            "gatewayTransactionId": "A60087687",
                            "status": "AUTHORIZED",
                            "billingAddress": {
                                "firstName": billingAddressFirstName,
                                "lastName": billingAddressLastName,
                                "address1": billingAddressAddress1,
                                "city": billingAddressCity,
                                "state": billingAddressState,
                                "postalCode": billingAddressPostalCode,
                                "countryCode": billingAddressCountryCode,
                                "email": billingAddressEmail
                            }
                        }
                    ],
                    "shipments": [
                        {
                            "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                            "rate": {
                                "currency": "USD",
                                "value": 50
                            },
                            "shippingMethod": "STANDARD",
                            "carrier": "DHL",
                            "taxTotal": [
                                {
                                    "currency": "USD",
                                    "value": 12
                                }
                            ],
                            "address": {
                                "firstName": "ismail",
                                "lastName": "rangwala",
                                "address1": "456",
                                "city": "Manchester",
                                "state": "England",
                                "postalCode": "12345",
                                "countryCode": "GB",
                                "email": "<EMAIL>"
                            },
                            "itemKeys": [
                                "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                            ],
                            "item": [
                                "21074012"
                            ],
                            "mode": "home_delivery"
                        }
                    ],
                    "orderSummary": {
                        "grossTotal": {
                            "currency": grossTotalCurrency,
                            "value": grossTotalValue
                        },
                        "discounts": [
                            {
                                "discountAmount": {
                                    "value": 20,
                                    "currency": "USD"
                                },
                                "promotionCode": "New",
                                "promotionName": "Percentage discount 10%",
                                "rate": 0
                            }
                        ],
                        "adjustmentTotal": {
                            "currency": "USD",
                            "value": 20
                        },
                        "taxes": [
                            {
                                "type": taxesType,
                                # "type": "shipping_rule_tax",
                                "taxAmount": {
                                    "currency": taxAmountCurrency,
                                    "value": taxValue
                                },
                                "rate": taxesRate
                            }
                        ],
                        "totalTaxAmount": {
                            "currency": "USD",
                            "value": 12
                        },
                        "shippingTotal": {
                            "currency": "USD",
                            "value": 50
                        },
                        "currency": "USD",
                        "netTotal": {
                            "currency": netTotalCurrency,
                            "value": netTotalValue
                        }
                    },
                    "user": {
                        "firstName": userFirstName,
                        "lastName": userLastName,
                        "email": userEmail
                    },
                    "itinerary": {
                        "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                        "confirmationCode": "6kcb2s",
                        "airline": "SQ322",
                        "firstName": "RAJU",
                        "lastName": "sasi",
                        "middleName": "s"
                    },
                    "seatInfo": {
                        "seatClass": "Outside demo",
                        "seatNumber": "Outside demo"
                    },
                    "groundSystem": "GA"
                }
            ]

        })

        headers = {
            'Authorization': token,
            'Content-Type': 'application/json'
        }
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data

    except Exception as e:
        logging.info(e)
        return False

def order_post_payload_for_5503(request, url, token, unitPrice, unitTax, unitDiscount, unitNet, unitGross,
                                     discountTotal, taxTotal, taxAmount, taxAdjustmentsRate, authAmount, grossTotal,
                                     netTotal, shippingTotal, adjustmentTotal, totalTaxAmount, orderSummaryTaxAmount,
                                     orderSummaryRate):
    RANDOM_NUMBER = ("".join([str(random.randint(0, 9)) for i in range(7)]))

    try:
        payload = json.dumps({
        "meta": {
            "airlineCodeICAO": "CRE",
            "flightNumber": "13KNTV34",
            "arrivalAirportCodeIATA": "",
            "departureAirportCodeIATA": "sx",
            "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
            "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
        },
        "orders": [
            {
                "orderKey": "From_Automation_2dsed improvement current flight shipment",
                "externalId": "From_Automation_" + RANDOM_NUMBER,
                "basketKey": "From_Automation_3328 improvement current flight shipment",
                "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                "createdTime": "2024-05-24T17:39:51.287+00:00",
                "lineItems": [
                    {
                        "title": "KRIS ⋮ JUNI12OR POLAR1 BEAR BY JELLYCAT®",
                        "PACVariantID": "1hospitality:mktplvb48943-var",
                        "retailerCode": "random",
                        "associateStore": DEDICATED_STORE_ID,
                        "fulfillmentType": "inHouse",
                        "alwaysInStock": False,
                        "imageUrl": "https://images.test/file.jpg",
                        "vendorProductVariantID": str(DEDICATED_PRODUCT_9_ID),
                        "unitPrice": {
                            "value": unitPrice,
                            "currency": "USD"
                        },
                        "unitTax": {
                            "value": unitTax,
                            "currency": "USD"
                        },
                        "unitDiscount": {
                            "value": unitDiscount,
                            "currency": "USD"
                        },
                        "unitNet": {
                            "value": unitNet,
                            "currency": "USD"
                        },
                        "unitGross": {
                            "value": unitGross,
                            "currency": "USD"
                        },
                        "quantity": 2,
                        "status": "DELIVERED",
                        "discountTotal": {
                            "currency": "USD",
                            "value": discountTotal
                        },
                        "taxTotal": {
                            "value": taxTotal,
                            "currency": "USD"
                        },
                        "discountAdjustments": [
                            {
                                "discountAmount": {
                                    "currency": "USD",
                                    "value": 12
                                },
                                "adjustType": "DISCOUNT",
                                "rate": 10,
                                "promotionCode": "asd",
                                "promotionName": "Percentage discount 10%"
                            }
                        ],
                        "taxAdjustments": [
                            {
                                "type": "shipping_rule_tax",
                                "taxAmount": {
                                    "currency": "USD",
                                    "value": taxAmount
                                },
                                "rate": taxAdjustmentsRate
                            }
                        ],
                        "salePrice": {
                            "value": 49,
                            "currency": "USD"
                        },
                        "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                    }
                ],
                "status": "CALL_CREW",
                "payment": [
                    {
                        "paymentService": "INTERNAL",
                        "paymentMethod": "CARD",
                        "authAmount": {
                            "currency": "USD",
                            "value": authAmount
                        },
                        "paymentId": "ededed-edejdnx",
                        "technicalServiceProviderTransactionId": "20220324173949153132",
                        "gatewayTransactionId": "A60087687",
                        "status": "AUTHORIZED",
                        "billingAddress": {
                            "firstName": "karan",
                            "lastName": "bhatt",
                            "address1": "123",
                            "city": "arizona",
                            "state": "Los angeles",
                            "postalCode": "78945",
                            "countryCode": "US",
                            "email": "<EMAIL>"
                        }
                    }
                ],
                "shipments": [
                    {
                        "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                        "rate": {
                            "currency": "USD",
                            "value": 50
                        },
                        "shippingMethod": "STANDARD",
                        "carrier": "DHL",
                        "taxTotal": [
                            {
                                "currency": "USD",
                                "value": 12
                            }
                        ],
                        "address": {
                            "firstName": "ismail",
                            "lastName": "rangwala",
                            "address1": "456",
                            "city": "Manchester",
                            "state": "England",
                            "postalCode": "12345",
                            "countryCode": "GB",
                            "email": "<EMAIL>"
                        },
                        "itemKeys": [
                            "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                        ],
                        "item": [
                            "21074012"
                        ],
                        "mode": "home_delivery"
                    }
                ],
                "orderSummary": {
                    "grossTotal": {
                        "currency": "USD",
                        "value": grossTotal
                    },
                    "discounts": [
                        {
                            "discountAmount": {
                                "value": 20,
                                "currency": "USD"
                            },
                            "promotionCode": "New",
                            "promotionName": "Percentage discount 10%",
                            "rate": 0
                        }
                    ],
                    "adjustmentTotal": {
                        "currency": "USD",
                        "value": adjustmentTotal
                    },
                    "taxes": [
                        {
                            "type": "shipping_rule_tax",
                            "taxAmount": {
                                "currency": "USD",
                                "value": orderSummaryTaxAmount
                            },
                            "rate": orderSummaryRate
                        }
                    ],
                    "totalTaxAmount": {
                        "currency": "USD",
                        "value": totalTaxAmount
                    },
                    "shippingTotal": {
                        "currency": "USD",
                        "value": shippingTotal
                    },
                    "currency": "USD",
                    "netTotal": {
                        "currency": "USD",
                        "value": netTotal
                    }
                },
                "user": {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>"
                },
                "itinerary": {
                    "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                    "confirmationCode": "6kcb2s",
                    "airline": "SQ322",
                    "firstName": "RAJU",
                    "lastName": "sasi",
                    "middleName": "s"
                },
                "seatInfo": {
                    "seatClass": "Outside demo",
                    "seatNumber": "Outside demo"
                },
                "groundSystem": "GA"
            }
        ]
        })

        headers = {
            'Authorization': token,
            'Content-Type': 'application/json'
        }
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data, RANDOM_NUMBER


    except Exception as e:
        logging.info(e)
        return False

def order_api_payload_1265(orderKey, basketKey, orderProvider,
                           externalId,productID, airlineCodeICAO, status,  token, request, url):
    try:
        payload = json.dumps({
    "meta": {
        "airlineCodeICAO": airlineCodeICAO,
        "flightNumber": "13KNTV34",
        "arrivalAirportCodeIATA": "",
        "departureAirportCodeIATA": "sx",
        "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
        "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
    },
    "orders": [
        {
            "orderKey": orderKey,
            "externalId": externalId,
            "basketKey": basketKey,
            "orderProvider": orderProvider,
            "modifiedTime": "2024-05-24T17:39:51.590+00:00",
            "createdTime": "2024-05-24T17:39:51.287+00:00",
            "lineItems": [
                {
                    "title": "KRIS ⋮ JUNI12OR POLAR1 BEAR BY JELLYCAT®",
                    "PACVariantID": "1hospitality:mktplvb48943-var",
                    "retailerCode": "random",
                    "associateStore": "40643",
                    "fulfillmentType": "inHouse",
                    "alwaysInStock": False,
                    "imageUrl": "https://images.test/file.jpg",
                    "vendorProductVariantID": productID,
                    "unitPrice": {
                        "value": 10,
                        "currency": "USD"
                    },
                    "unitTax": {
                        "value": 10,
                        "currency": "USD"
                    },
                    "unitDiscount": {
                        "value": 4.9,
                        "currency": "USD"
                    },
                    "unitNet": {
                        "value": 44.1,
                        "currency": "USD"
                    },
                    "unitGross": {
                        "value": 44.1,
                        "currency": "USD"
                    },
                    "quantity": 2,
                    "status": status,
                    "discountTotal": {
                        "currency": "USD",
                        "value": 9.8
                    },
                    "taxTotal": {
                        "value": 10,
                        "currency": "USD"
                    },
                    "discountAdjustments": [
                        {
                            "discountAmount": {
                                "currency": "USD",
                                "value": 12
                            },
                            "adjustType": "DISCOUNT",
                            "rate": 10,
                            "promotionCode": "asd",
                            "promotionName": "Percentage discount 10%"
                        }
                    ],
                    "taxAdjustments": [
                        {
                            "type": "shipping_rule_tax",
                            "taxAmount": {
                                "currency": "USD",
                                "value": 12
                            },
                            "rate": 7
                        }
                    ],
                    "salePrice": {
                        "value": 49,
                        "currency": "USD"
                    },
                    "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                }
            ],
            "status": "CALL_CREW",
            "payment": [
                {
                    "paymentService": "INTERNAL",
                    "paymentMethod": "CARD",
                    "authAmount": {
                        "currency": "USD",
                        "value": 230
                    },
                    "paymentId": "ededed-edejdnx",
                    "technicalServiceProviderTransactionId": "20220324173949153132",
                    "gatewayTransactionId": "A60087687",
                    "status": "AUTHORIZED",
                    "billingAddress": {
                        "firstName": "karan",
                        "lastName": "bhatt",
                        "address1": "123",
                        "city": "arizona",
                        "state": "Los angeles",
                        "postalCode": "78945",
                        "countryCode": "US",
                        "email": "<EMAIL>"
                    }
                }
            ],
            "shipments": [
                {
                    "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                    "rate": {
                        "currency": "USD",
                        "value": 50
                    },
                    "shippingMethod": "STANDARD",
                    "carrier": "DHL",
                    "taxTotal": [
                        {
                            "currency": "USD",
                            "value": 12
                        }
                    ],
                    "address": {
                        "firstName": "ismail",
                        "lastName": "rangwala",
                        "address1": "456",
                        "city": "Manchester",
                        "state": "England",
                        "postalCode": "12345",
                        "countryCode": "GB",
                        "email": "<EMAIL>"
                    },
                    "itemKeys": [
                        "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                    ],
                    "item": [
                        "21074012"
                    ],
                    "mode": "home_delivery"
                }
            ],
            "orderSummary": {
                "grossTotal": {
                    "currency": "USD",
                    "value": 200
                },
                "discounts": [
                    {
                        "discountAmount": {
                            "value": 20,
                            "currency": "USD"
                        },
                        "promotionCode": "New",
                        "promotionName": "Percentage discount 10%",
                        "rate": 0
                    }
                ],
                "adjustmentTotal": {
                    "currency": "USD",
                    "value": 20
                },
                "taxes": [
                    {
                        "type": "shipping_rule_tax",
                        "taxAmount": {
                            "currency": "USD",
                            "value": 12
                        },
                        "rate": 7
                    }
                ],
                "totalTaxAmount": {
                    "currency": "USD",
                    "value": 12
                },
                "shippingTotal": {
                    "currency": "USD",
                    "value": 50
                },
                "currency": "USD",
                "netTotal": {
                    "currency": "USD",
                    "value": 230
                }
            },
            "user": {
                "firstName": "John",
                "lastName": "Doe",
                "email": "<EMAIL>"
            },
            "itinerary": {
                "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                "confirmationCode": "6kcb2s",
                "airline": "SQ322",
                "firstName": "RAJU",
                "lastName": "sasi",
                "middleName": "s"
            },
            "seatInfo": {
                 "seatClass": "Outside demo",
                "seatNumber": "Outside demo"
            },
            "groundSystem": "GA"
        }
    ]
})
        headers = {
            'Authorization': token,
            'Content-Type': 'application/json'
        }
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data

    except Exception as e:
        logging.info(e)
        return False

def Assets_Failed_Order_Logs(driver):
    wait_for_style_attribute(driver,40)
    WebDriverWait(driver, 60).until(
        (EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'Assets')]")))
    )
    time.sleep(5)
    driver.find_element(By.XPATH, "//div[contains(text(),'Assets')]").click()
    logexpander = WebDriverWait(driver, 60).until(
        EC.presence_of_element_located(
            (By.XPATH, "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]")
        )
    )
    driver.execute_script("arguments[0].scrollIntoView();", logexpander)

    driver.find_element(
        By.XPATH,
        "//span[text()='Log']//parent::div//div[contains(@class,'expander')]",
    ).click()
    logging.info("Clicked on Log")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[contains(text(),'Failed Order Logs')]")
        )
    )
    driver.find_element(By.XPATH, "//span[contains(text(),'Failed Order Logs')]").click()
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[contains(text(),'List')]")
        )
    )
    driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
    logging.info("Navigated to Failed Order Logs - List View")
    time.sleep(10)
    try:
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located((By.XPATH,
                                              "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]")))
        driver.find_element(By.XPATH,
                            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]").click()
        logging.info("Navigated to last page")
    except Exception as e:
        logging.info("last page error")
        logging.info(e)
        pass

    time.sleep(5)

def order_api_payload_4840_5043(title, externalId,itemKeys,mode,token, request, url):
    try:
        payload = json.dumps({
            "meta": {
                "airlineCodeICAO": "CRE",
                "flightNumber": "13KNTV34",
                "arrivalAirportCodeIATA": "",
                "departureAirportCodeIATA": "sx",
                "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
            },
            "orders": [
                {
                    "orderKey": "From_Automation_2dsed improvement current flight shipment",
                    "externalId": externalId,
                    "basketKey": "From_Automation_3328 improvement current flight shipment",
                    "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                    "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                    "createdTime": "2024-05-24T17:39:51.287+00:00",
                    "lineItems": [
                        {
                            "title": title,
                            "PACVariantID": "1hospitality:mktplvb48943-var",
                            "retailerCode": "poiuuyt",
                            "associateStore": "40643",
                            "fulfillmentType": "inHouse",
                            "alwaysInStock": False,
                            "imageUrl": "https://images.test/file.jpg",
                            "vendorProductVariantID": str(DEDICATED_SKU_1_ID),
                            "unitPrice": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "unitTax": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "unitDiscount": {
                                "value": 4.9,
                                "currency": "USD"
                            },
                            "unitNet": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "unitGross": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "quantity": 2,
                            "status": "DELIVERED",
                            "discountTotal": {
                                "currency": "USD",
                                "value": 9.8
                            },
                            "taxTotal": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "discountAdjustments": [
                                {
                                    "discountAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "adjustType": "DISCOUNT",
                                    "rate": 10,
                                    "promotionCode": "asd",
                                    "promotionName": "Percentage discount 10%"
                                }
                            ],
                            "taxAdjustments": [
                                {
                                    "type": "shipping_rule_tax",
                                    "taxAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "rate": 7
                                }
                            ],
                            "salePrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                        }
                    ],
                    "status": "CALL_CREW",
                    "payment": [
                        {
                            "paymentService": "INTERNAL",
                            "paymentMethod": "CARD",
                            "authAmount": {
                                "currency": "USD",
                                "value": 230
                            },
                            "paymentId": "ededed-edejdnx",
                            "technicalServiceProviderTransactionId": "20220324173949153132",
                            "gatewayTransactionId": "A60087687",
                            "status": "AUTHORIZED",
                            "billingAddress": {
                                "firstName": "karan",
                                "lastName": "bhatt",
                                "address1": "123",
                                "city": "arizona",
                                "state": "Los angeles",
                                "postalCode": "78945",
                                "countryCode": "US",
                                "email": "<EMAIL>"
                            }
                        }
                    ],
                    "shipments": [
                        {
                            "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                            "rate": {
                                "currency": "USD",
                                "value": 0
                            },
                            "shippingMethod": "STANDARD",
                            "carrier": "DHL",
                            "taxTotal": [
                                {
                                    "currency": "USD",
                                    "value": 12
                                }
                            ],
                            "address": {
                                "firstName": "ismail",
                                "lastName": "rangwala",
                                "address1": "456",
                                "city": "Manchester",
                                "state": "England",
                                "postalCode": "12345",
                                "countryCode": "GB",
                                "email": "<EMAIL>"
                            },
                            "itemKeys": [
                                itemKeys
                            ],
                            "item": [
                                "21074012"
                            ],
                            "mode": mode
                        }
                    ],
                    "orderSummary": {
                        "grossTotal": {
                            "currency": "USD",
                            "value": 200
                        },
                        "discounts": [
                            {
                                "discountAmount": {
                                    "value": 20,
                                    "currency": "USD"
                                },
                                "promotionCode": "New",
                                "promotionName": "Percentage discount 10%",
                                "rate": 0
                            }
                        ],
                        "adjustmentTotal": {
                            "currency": "USD",
                            "value": 20
                        },
                        "taxes": [
                            {
                                "type": "shipping_rule_tax",
                                "taxAmount": {
                                    "currency": "USD",
                                    "value": 12
                                },
                                "rate": 7
                            }
                        ],
                        "totalTaxAmount": {
                            "currency": "USD",
                            "value": 12
                        },
                        "shippingTotal": {
                            "currency": "USD",
                            "value": 50
                        },
                        "currency": "USD",
                        "netTotal": {
                            "currency": "USD",
                            "value": 230
                        }
                    },
                    "user": {
                        "firstName": "John",
                        "lastName": "Doe",
                        "email": "<EMAIL>"
                    },
                    "itinerary": {
                        "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                        "confirmationCode": "6kcb2s",
                        "airline": "SQ322",
                        "firstName": "RAJU",
                        "lastName": "sasi",
                        "middleName": "s"
                    },
                    "seatInfo": {
                        "seatClass": "Outside demo",
                        "seatNumber": "Outside demo"
                    },
                    "groundSystem": "GA"
                }
            ]
        })

        headers = {
            'Authorization': token,
            'Content-Type': 'application/json'
        }
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data
    except Exception as e:
        logging.info(e)
        return False

def order_api_payload_current_flight(firstName1,
                                     lastName1,
                                     email1,
                                     firstName,
                                     lastName,
                                     address1,
                                     city,
                                     state,
                                     postalCode,
                                     countryCode,
                                     email,
                                     arrivalAirportCodeIATA,
                                     departureAirportCodeIATA,
                                     flightNumber,
                                     airlineCodeICAO,
                                     externalId,
                                     createdTime,
                                     retailerCode,
                                     associateStore,
                                     status1,
                                     status,
                                     paymentService,
                                     mode,
                                     seatClass,
                                     seatNumber,
                                     PACVariantID,
                                     scheduledDepartureTime,
                                     groundSystem,
                                     currency,
                                     token,
                                     request,
                                     url,
                                     shippingMethod= "STANDARD"):
    try:
        payload = json.dumps({
            "meta": {
                "airlineCodeICAO": airlineCodeICAO,
                "flightNumber": flightNumber,
                "arrivalAirportCodeIATA": arrivalAirportCodeIATA,
                "departureAirportCodeIATA": departureAirportCodeIATA,
                "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
            },
            "orders": [
                {
                    "orderKey": "From_Automation_3328 improvement current flight shipment",
                    "externalId": externalId,
                    "basketKey": "From_Automation_3328 improvement current flight shipment",
                    "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                    "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                    "createdTime": createdTime,
                    "lineItems": [
                        {
                            "title": "Demo Line Item",
                            "PACVariantID": PACVariantID,
                            "retailerCode": retailerCode,
                            "associateStore": str(associateStore),
                            "fulfillmentType": "inHouse",
                            "alwaysInStock": False,
                            "imageUrl": "https://images.test/file.jpg",
                            "vendorProductVariantID": str(DEDICATED_SKU_1_ID),
                            "unitPrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "unitTax": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "unitDiscount": {
                                "value": 4.9,
                                "currency": "USD"
                            },
                            "unitNet": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "unitGross": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "quantity": 2,
                            "status": status1,
                            "discountTotal": {
                                "currency": "USD",
                                "value": 9.8
                            },
                            "taxTotal": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "discountAdjustments": [
                                {
                                    "discountAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "adjustType": "DISCOUNT",
                                    "rate": 10,
                                    "promotionCode": "asd",
                                    "promotionName": "Percentage discount 10%"
                                }
                            ],
                            "taxAdjustments": [
                                {
                                    "type": "shipping_rule_tax",
                                    "taxAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "rate": 7
                                }
                            ],
                            "salePrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                        }
                    ],
                    "status": status,
                    "payment": [
                        {
                            "paymentService": paymentService,
                            "paymentMethod": "CARD",
                            "authAmount": {
                                "currency": "USD",
                                "value": 230
                            },
                            "paymentId": "BBAA-0324111-88",
                            "technicalServiceProviderTransactionId": "20220324173949153132",
                            "gatewayTransactionId": "A60087687",
                            "status": "AUTHORIZED",
                            "billingAddress": {
                                "firstName": "karan",
                                "lastName": "bhatt",
                                "address1": "123",
                                "city": "arizona",
                                "state": "Los angeles",
                                "postalCode": "78945",
                                "countryCode": "US",
                                "email": "<EMAIL>"
                            }
                        }
                    ],
                    "shipments": [
                        {
                            "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                            "rate": {
                                "currency": "USD",
                                "value": 50
                            },
                            "shippingMethod": shippingMethod,
                            "carrier": "DHL",
                            "taxTotal": [
                                {
                                    "currency": "USD",
                                    "value": 12
                                }
                            ],
                            "address": {
                                "firstName": firstName,
                                "lastName": lastName,
                                "address1": address1,
                                "city": city,
                                "state": state,
                                "postalCode": postalCode,
                                "countryCode": countryCode,
                                "email": email,
                                "shippingMethod": "STANDARD",
                                "flightNumber": flightNumber,
                                "scheduledDepartureTime": scheduledDepartureTime,
                                "departureAirportCodeIATA": departureAirportCodeIATA,
                                "arrivalAirportCodeIATA": arrivalAirportCodeIATA,
                                "seatClass": seatClass,
                                "seatNumber": seatNumber,
                            },
                            "itemKeys": [
                                "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                            ],
                            "item": [
                                "21074012"
                            ],
                            "mode": mode
                        }
                    ],
                    "orderSummary": {
                        "grossTotal": {
                            "currency": "USD",
                            "value": 200
                        },
                        "discounts": [
                            {
                                "discountAmount": {
                                    "value": 20,
                                    "currency": "USD"
                                },
                                "promotionCode": "New",
                                "promotionName": "Percentage discount 10%",
                                "rate": 0
                            }
                        ],
                        "adjustmentTotal": {
                            "currency": "USD",
                            "value": 20
                        },
                        "taxes": [
                            {
                                "type": "shipping_rule_tax",
                                "taxAmount": {
                                    "currency": "USD",
                                    "value": 12
                                },
                                "rate": 7
                            }
                        ],
                        "totalTaxAmount": {
                            "currency": "USD",
                            "value": 12
                        },
                        "shippingTotal": {
                            "currency": "USD",
                            "value": 50
                        },
                        "currency": "USD",
                        "netTotal": {
                            "currency": currency,
                            "value": 230
                        }
                    },
                    "user": {
                        "firstName": firstName1,
                        "lastName": lastName1,
                        "email": email1
                    },
                    "itinerary": {
                        "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                        "confirmationCode": "6kcb2s",
                        "airline": "SQ322",
                        "firstName": "RAJU",
                        "lastName": "sasi",
                        "middleName": "s"
                    },
                    "seatInfo": {
                        "seatClass": seatClass,
                        "seatNumber": seatNumber
                    },
                    "groundSystem": groundSystem
                }
            ]
        })
        headers = {
            'Authorization': token,
            'Content-Type': 'application/json'
        }
        response = requests.request(request, url, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        return data
    except Exception as e:
        logging.info(e)
        return False

def create_aircraft_pac_admin(driver, RANDOM_NAME):
    
    click_on_add_object(driver, "AirCraft")
    (WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.XPATH, '(//div[text()="Enter the name of the new item"])[1]'
                                                  '//parent::div//input'))).send_keys(RANDOM_NAME))
    (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))).click())
    (WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.NAME, 'OEMName'))).send_keys(RANDOM_NAME))
    driver.find_element(By.NAME, 'familyName').send_keys('family')
    driver.find_element(By.NAME, 'modelName').send_keys('modelName')
    driver.find_element(By.NAME, 'description').send_keys('description')
    save_and_publish(driver)
    return True

def explicitWaitUntilPresenceOfElementLocatedByXpath(driver, ele):
    (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, ele))))

def random_numer_file_download_path(path):
    filelist = []
    last_file_time = 0
    for current_file in os.listdir(path):
        logging.info(current_file)
        filelist.append(current_file)
        current_file_fullpath = os.path.join(path, current_file)
        current_file_time = os.path.getctime(current_file_fullpath)
        if os.path.isfile(current_file_fullpath):
            if last_file_time == 0:
                last_file = current_file
            last_file_time = os.path.getctime(
                os.path.join(path, last_file))
            if current_file_time > last_file_time and os.path.isfile(current_file_fullpath):
                last_file = current_file
        last_file_fullpath = os.path.join(path, last_file)
    logging.info(last_file_fullpath)
    return last_file_fullpath

def Assets_import_Airline_Manager(driver):
    time.sleep(5)
    WebDriverWait(driver, 60).until(
        (EC.element_to_be_clickable((By.XPATH, xpath.assets)))
    )
    driver.find_element(By.XPATH, xpath.assets).click()
    import_plus = driver.find_element(
        By.XPATH,
        "//span[text()='Import']//parent::div//div[contains(@class,'expander')]",
    )
    driver.execute_script("arguments[0].scrollIntoView();", import_plus)
    driver.execute_script("arguments[0].click();", import_plus)
    logging.info("Clicked on Import")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located(
            (By.XPATH, "//span[contains(text(),'" + PAC_AIRLINE_MANAGER_MAIL + "')]")
        )
    )
    driver.find_element(
        By.XPATH, "//span[contains(text(),'" + PAC_AIRLINE_MANAGER_MAIL + "')]"
    ).click()
    driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
    logging.info(f"Navigated to {PAC_AIRLINE_MANAGER_MAIL} - List View")
    time.sleep(5)
    try:
        WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located(
                (
                    By.XPATH,
                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
                )
            )
        )
        driver.find_element(
            By.XPATH,
            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
        ).click()
        logging.info("Navigated to last page")
        time.sleep(10)
    except Exception as e:
        logging.info("last page error")
        logging.info(e)
        pass

def validateSnsMessageAttributes(driver, x):
    logging.info("Checking messageAttributes")
    # x = WebDriverWait(driver, 60).until(
    #             EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
    #         )
    x.send_keys(Keys.CONTROL, "f")
    driver.find_element(
        By.XPATH, "//input[contains(@placeholder,'Search for')]"
    ).send_keys("messageAttributes")
    time.sleep(1)
    assert driver.find_element(
        By.XPATH, "//span[contains(text(),'messageAttributes')]"
    ).is_displayed()
    logging.info(f"Asserted the messageAttributes")

    x.send_keys(Keys.CONTROL, "f")
    driver.find_element(
        By.XPATH, "//input[contains(@placeholder,'Search for')]"
    ).send_keys("Bucket")
    bucket_text = driver.find_element(
        By.XPATH, "//span[contains(text(),'Bucket')]//following-sibling::span"
    ).text
    logging.info("Bucket name : " + bucket_text)
    time.sleep(1)
    x.send_keys(Keys.CONTROL, "f")
    driver.find_element(
        By.XPATH, "//input[contains(@placeholder,'Search for')]"
    ).send_keys("Key")
    key_text = driver.find_elements(
        By.XPATH, "//span[contains(text(),'Key')]//following-sibling::span"
    )
    key_value = ""
    for key in key_text:
        key_value += str(key.text)

    logging.info("Object Key : " + key_value)
    logging.info(f"SNS Payload have S3 bucket name and object key")
    logging.info("Succesfully checked messageAttributes")

def validateSnsEventType(driver, value):
    logging.info("Checking EventType")
    # EventType
    assert driver.find_element(
        By.XPATH, "(//span[contains(text(),'eventType')])[1]"
    ).is_displayed()
    assert driver.find_element(
        By.XPATH, "(//span[contains(text(),'String')])[1]"
    ).is_displayed()
    string_value = driver.find_element(
        By.XPATH,
        "(//span[contains(text(),'String')])[1]//following-sibling::span",
    ).text
    
    assert driver.find_element(
        By.XPATH, "(//span[contains(text(),'DataType')])[1]"
    ).is_displayed()
    data_value = driver.find_element(
        By.XPATH,
        "(//span[contains(text(),'DataType')])[1]//following-sibling::span",
    ).text
    logging.info(
        f"Asserted the eventType - "
        f"StringValue is {string_value}, "
        f"DataType is {data_value}"
    )
    assert value in string_value
    logging.info("Succesfully checked EventType")

def validateSnsId(driver):
    logging.info("Checking Id")
    # Id
    assert driver.find_element(
        By.XPATH, "//span[contains(text(),'\"Id\"')]"
    ).is_displayed()
    assert driver.find_element(
        By.XPATH, "(//span[contains(text(),'String')])[3]"
    ).is_displayed()
    id = driver.find_element(
        By.XPATH, "(//span[@class='ace_constant ace_numeric'])[2]"
    )
    assert id.is_displayed()
    assert driver.find_element(
        By.XPATH, "(//span[@class='ace_constant ace_numeric'])[2]"
    ).is_displayed()
    assert driver.find_element(
        By.XPATH, "(//span[contains(text(),'DataType')])[2]"
    ).is_displayed()
    data_value = driver.find_element(
        By.XPATH,
        "(//span[contains(text(),'DataType')])[2]//following-sibling::span",
    ).text
    logging.info(
        f"Asserted the ID - "
        f"StringValue is - {id.text}, "
        f"DataType is {data_value}"
    )
    logging.info("Succesfully checked Id")

def validateSnsAirlineCode(driver):
    logging.info("Checking airlineCode")
    # airlineCode
    assert driver.find_element(
        By.XPATH, "(//span[contains(text(),'airlineCode')])[1]"
    ).is_displayed()
    assert driver.find_element(
        By.XPATH, "(//span[contains(text(),'String')])[4]"
    ).is_displayed()
    data_value = driver.find_element(
        By.XPATH,
        "(//span[contains(text(),'DataType')])[3]//following-sibling::span",
    ).text
    string_value = driver.find_element(
        By.XPATH,
        "(//span[contains(text(),'String')])[4]//following-sibling::span",
    ).text
    logging.info(
        f"Asserted the eventType - "
        f"StringValue is {string_value}, "
        f"DataType is {data_value}"
    )
    logging.info("Succesfully checked airlineCode")

def goCheckSNS(driver, snsName, changedValue, airlineIcao=False):
    Assets(driver)
    time.sleep(8)
    list1 = driver.find_elements(
        By.XPATH, "//div[contains(text(),'"+snsName+"')]"
    )
    num = len(list1)
    time.sleep(5)
    row = driver.find_element(
        By.XPATH, f"(//div[contains(text(),'"+snsName+f"')])[{num-1}]"
    )
    actions = ActionChains(driver)
    actions.double_click(row).perform()
    # Open Code if modal appears
    click_on_yes_no(driver)
 
    logging.info("Opened the log file - json")
    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located(
            (By.XPATH, f"//span[contains(text(),'{changedValue}')]")
        )
    )
    driver.implicitly_wait(10)
    x = driver.find_element(By.XPATH, "(//textarea)[1]")
    x.send_keys(Keys.CONTROL, "f")
    x.send_keys(Keys.CONTROL, "f")
    validateSnsMessageAttributes(driver, x)
    validateSnsEventType(driver, snsName)
    validateSnsId(driver)
    if airlineIcao: 
        validateSnsAirlineCode(driver)

def clickOnDropDownForChooseAction(driver, catalogAssignmentId, action):
    logging.info("Into clickOnDropDownForChooseAction")
    WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productsTabXpath)
                )
            ).click()
    dropdown = driver.find_element(
                    By.ID,
                    f"options{str(catalogAssignmentId)}-trigger-picker"
                )
    dropdown.click()
    (
        WebDriverWait(driver, 60)
        .until(
            EC.visibility_of_element_located(
                (
                    "xpath",
                    '//span[text()="Choose Action To perform:"]/../../..//input',
                )
            )
        )
        .clear()
    )
    (
        WebDriverWait(driver, 60)
        .until(
            EC.visibility_of_element_located(
                (
                    "xpath",
                    '//span[text()="Choose Action To perform:"]/../../..//input',
                )
            )
        )
        .send_keys(action)
    )
    time.sleep(4)
    (
        WebDriverWait(driver, 60)
        .until(
            EC.visibility_of_element_located(
                (
                    "xpath",
                    '//span[text()="Choose Action To perform:"]/../../..//input',
                )
            )
        )
        .send_keys(Keys.TAB)
    )
    logging.info("Code executed for - clickOnDropDownForChooseAction")

def deleteObjectViaUI(driver, UID):
    driver.refresh()
    search_by_id(driver, UID)
    logging.info("Searched")
    driver.find_element(By.XPATH, '//span[contains(@class,"pimcore_material_icon_delete pimcore_material_icon")]').click()
    logging.info("Clicked on delete icon")
    WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(" + xpath.okButtonXpath+ ")[last()]")))
    driver.find_element(By.XPATH, "(" + xpath.okButtonXpath+ ")[last()]").click()
    logging.info("Clicked on ok button")
    wait_for_style_attribute(driver, 30)
    logging.info(f"Clicked on the open area for search window")
    actions = ActionChains(driver)
    actions.key_down(Keys.CONTROL).key_down(Keys.SHIFT).send_keys("o").key_up(
        Keys.CONTROL
    ).key_up(Keys.SHIFT).perform()
    driver.find_element(
        By.XPATH, "//input[contains(@name,'textfield-inputEl')]"
    ).send_keys(UID, Keys.ENTER)
    # driver.find_element(By.XPATH, "//span[contains(text(),'OK')]").click()
    logging.info(f"Click on OK post search the id.")
    WebDriverWait(driver, 60).until(
        EC.visibility_of_element_located((By.XPATH, "//div[text()='Error']"))
    )
    x = WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[text()='Error']"))).is_displayed()
    assert x
    logging.info("Shown validation as element not found")

def setPassword(driver, RANDOM_NAME):
    RANDOM_PASSWORD = "".join(
        random.choices(string.ascii_letters, k=7)
    ) + "*123"
    logging.info(RANDOM_PASSWORD)
    driver.find_element(By.XPATH, xpath.settings).click()
    action = ActionChains(driver)
    action.move_to_element(driver.find_element(By.XPATH, xpath.usersOrRoles)).perform()
    driver.find_element(By.XPATH, "(//span[text()='Users'])[last()]").click()
    WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "//span[text()='Search']")))
    driver.find_element(By.XPATH, "//span[text()='Search']").click()
    WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "(//input)[last()]")))
    driver.find_element(By.XPATH, "(//input)[last()]").send_keys(RANDOM_NAME)
    WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, "//h3[contains(text(),'"+RANDOM_NAME+"')]")))
    driver.find_element(By.XPATH, "(//input)[last()]").send_keys(Keys.ENTER)
    WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, "//label[text()='Active']"))).click()
    WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.NAME, "password")))
    driver.find_element(By.NAME, "password").clear()
    time.sleep(1)
    driver.find_element(By.NAME, "password").send_keys(RANDOM_PASSWORD)
    driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
    WebDriverWait(driver,20).until(EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
    logging.info("Successfully set the password as: " + RANDOM_PASSWORD)
    return RANDOM_PASSWORD

def clickOnReload(driver):
    try:
        WebDriverWait(driver,20).until(EC.visibility_of_element_located((By.XPATH, "(//span[contains(@class,'reload')])[last()]")))
        driver.find_element(By.XPATH,"(//span[contains(@class,'reload')])[last()]").click()
        logging.info("Clicked on Reload button")
    except:
        logging.info("Reload button not clicked")
        pass