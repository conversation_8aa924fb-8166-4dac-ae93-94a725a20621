import os

LOCAL_DOWNLOADED_PATH = os.getcwd() + "/downloads/"
# LOCAL_DOWNLOADED_PATH = "C:\\Users\\<USER>\\Downloads\\"
# LOCAL_DOWNLOADED_PATH = "C:\\Users\\<USER>\\Downloads\\"
# LOCAL_DOWNLOADED_PATH = "C:\\Users\\<USER>\\Downloads\\"

# region URL and Credentails
PAC_URL = "https://marketplace-test.nextcloud.aero/admin/login?perspective="
# PAC_URL = "https://marketplace-dev.nextcloud.aero/admin/login?perspective="

PAC_API_GATEWAY_URL = "https://api.marketplace-test.nextcloud.aero/"

PAC_USER_MAIL = "<EMAIL>"
PAC_USER_PASSWORD = "PACadmin@12345"

PAC_STORE_USER_MAIL = "<EMAIL>"
PAC_STORE_USER_PASSWORD = "Autostoread@123"

PAC_AIRLINE_MAIL = "<EMAIL>"
PAC_PASSWORD_MAIL = "AirlineAdmin@1234"


PAC_AIRLINE_MANAGER_MAIL = "<EMAIL>"
PAC_AIRLINE_MANAGER_PASSWORD = "Airlinead@123"

PAC_STORE_MANAGER_MAIL = "<EMAIL>"
PAC_STORE_MANAGER_PASSWORD = "Autostoread@123"

PAC_STORE_MARKETPLACE_USER_MAIL = "<EMAIL>"
PAC_STORE_MARKETPLACE_PASSWORD = "MPStoreAdmin@1234"

PAC_UNPUBLISHED_STORE_USER_MAIL = "<EMAIL>"
PAC_UNPUBLISHED_STORE_PASSWORD = "Unpublished@123"

PAC_UNPUBLISHED_AIRLINE_USER_MAIL = "<EMAIL>"
PAC_UNPUBLISHED_AIRLINE_PASSWORD = "Unpublished@123"
#endregion URL and Credentails

CRED_AUTOMATION_AIRLINE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/flight/"
CRED_AUTOMATION_AIRLINE_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjg2NjA0MzQ4LCJzdWIiOiJhaXJsaW5lIiwieC1hbXpuLXRyYWNlLWlkIjoiUm9vdD0xLTY2NDg2YTJjLTk0NTY0NGM0NmI1YmU5YzkyNzQ1M2E0MTtQYXJlbnQ9YjllMTExNjQ1YTIwZDVmYTtTYW1wbGVkPTEiLCJpYXQiOjE3MTYwMjE4MDQsImV4cCI6MTc0NzU1NzgwNH0.PgvKLeZx0lG4qlBdPx76k_Qq6Xs9DEIyNJXLUVQttZk"
CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjg2NjA1MjcxLCJzdWIiOiJzdG9yZSIsImlhdCI6MTcwNzczNDM1MywiZXhwIjoxNzM5MzU2NzUzfQ.W2IrJzN5nosPec61DN2csYKrlDSVIiVMHHDCRbR1lVQ"
CRED_AUTOMATION_STORE_CATALOG_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/catalog/"
CRED_AUTOMATION_STORE_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjg2NjA0MzE2LCJzdWIiOiJzdG9yZSIsIngtYW16bi10cmFjZS1pZCI6IlJvb3Q9MS02NjQ4NjllZC03ZDI2YmFhNTQzODg2YTI3OThkZjc1NTA7UGFyZW50PTA1ZDA3MWFlMzg1MjE4MWE7U2FtcGxlZD0xIiwiaWF0IjoxNzE2MDIxNzQxLCJleHAiOjE3NDc1NTc3NDF9.esshtRX30z_8dAzJ3V9OME4Ur6XFGYJjb8ahoVFb9xk"
AIRLINE_CATEGORY_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/airline-category/"
CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL = (
    PAC_API_GATEWAY_URL + "marketplace/v1/ams/routegroups/"
)
JWT_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJTSUEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNjg5ODM0MTczLCJleHAiOjI1MzY0MzE0MDB9.LqZ4Mzqjv_PRey7aHG8OQ-0ewCXmAEjUiRV9e-_PZig"

ORDER_JWT_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJDUkUiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNjgxMjAwODkzLCJleHAiOjI1MzY0MzE0MDB9.GaKRjhdV3WSkFsrFZP1_6_w2aBGZxhaz5vvcjOVH0YI"
FULFILLMENT_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/fulfillment"
CABIN_CLASS_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/cabinClass"
STORE_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/store/information"
AIRLINE_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/airline"
AIRCRAFT_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/aircrafts?limit=121"
ROUTE_SECTOR_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/sector/"
MEAL_CODE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/meal-code?offset=1"
PRODUCT_MEALCODE_URL = (
    PAC_API_GATEWAY_URL + "marketplace/v1/ams/meal-code/products?offset=1&limit=1000"
)
UPDATE_MEALCODE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/product-configuration"
KIT_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/prepare-kit/"
ROOT_TYPE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/root-type?limit=30"
ROUTE_CATALOG = PAC_API_GATEWAY_URL + "marketplace/v1/pim/route-catalog/"
CATEGORIES_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/categories?offset=1"
TAX_URL = PAC_API_GATEWAY_URL + "marketplace/v1/store/tax/"
PRODUCTS_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/product/"
UI_TEMPLATE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/ui-template"
AIRPORTS_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/airports"
UPDATE_PRICE_URL = (
    PAC_API_GATEWAY_URL + "marketplace/v1/pim/route-catalog/86604410/price"
)
DEPLOYMENT_INVENTORY_URL = (
    PAC_API_GATEWAY_URL
    + "marketplace/v1/pim/route-catalog/86604411/deploymentInventory"
)
GET_PRICE = PAC_API_GATEWAY_URL + "marketplace/v1/pim/route-catalog/86604410/"
INVENTORY_CHECK_URL = PAC_API_GATEWAY_URL + "marketplace/v1/inventoryCheck/"
UNPUBLISHED_PRODUCT = (
    PAC_API_GATEWAY_URL + "marketplace/v1/pim/product?offset=1&unpublished=true"
)
UNPUBLISHED_PRODUCT_BY_ID = (
    PAC_API_GATEWAY_URL
    + "marketplace/v1/pim/product?offset=1&ids=14029&unpublished=true"
)
PRODUCT_UPDATE_CATEGORIES = PAC_API_GATEWAY_URL + "marketplace/v1/pim/product/86604356"
WORKFLOW_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/route-catalog-workflow"
PUBLISH_PRODUCT = PAC_API_GATEWAY_URL + "marketplace/v1/pim/publishProduct"
AUTHENTICATION_URL = PAC_API_GATEWAY_URL + "marketplace/v1/auth/token"
CATALOG_BY_STORE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/store/86604316/catalog"
CONSUME_ORDER_URL = PAC_API_GATEWAY_URL + "orders"
GET_ORDER_URL = PAC_API_GATEWAY_URL+ "marketplace/v1/oms/order/"
ORDER_LINE_ITEM_STATUS = PAC_API_GATEWAY_URL + "marketplace/v1/oms/orderItemStatus/"
UPDATE_SHIPMENT = PAC_API_GATEWAY_URL + "marketplace/v1/oms/ordershipment/"
DELIVERY_URL = PAC_API_GATEWAY_URL + "marketplace/v1/store/delivery-rule"
STORE_LOCATION_URL = PAC_API_GATEWAY_URL + "marketplace/v1/store/location"
ROUTE_SECTOR_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/sector/"

# ID's:

AUTOMATION_CATALOGASSIGNMENT = "86604402"
PRODUCT_ID_STATIC = "9332"
AUTOMATION_CATALOG = "10850"


LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE = LOCAL_DOWNLOADED_PATH + "Flight_Sample.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_ROUTEGROUP_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "RouteGroup_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_CATLOG_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "Catalog_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_AIRPOT_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "Airports_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_CATEGORY_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "Category_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "Airline_Category_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_ROUTEGROUP_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "AirlineRouteGroup_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_AIRCRAFT_CATEGORY_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "AirCraft_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_SECTOR_SAMPLE = LOCAL_DOWNLOADED_PATH + "Sector_Sample.csv"

LOCAL_DOWNLOADABLE_FILE_PATH_PRICEUPDATE_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "PriceUpdate.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE = LOCAL_DOWNLOADED_PATH + "mealcode_import.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_INVENTORYUPDATE_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "QtyUpdate.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE = LOCAL_DOWNLOADED_PATH
LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_EXPORT = (
    LOCAL_DOWNLOADED_PATH + "export.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_TRACKINGINFO = (LOCAL_DOWNLOADED_PATH + "trackingInfo.csv")
LOCAL_DOWNLOADABLE_FILE_PATH_MAP_AIRLINE_CATEGORIES = (
    LOCAL_DOWNLOADED_PATH + "catalog_product_ac.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_MAP_AIRLINE_CATEGORIES_LOG = (
    LOCAL_DOWNLOADED_PATH + "catalog_product_ac_log.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_CATALOG_PRODUCT_AC = (
    LOCAL_DOWNLOADED_PATH + "catalog_product_ac.csv"
)
LOCAL_DOWNLOADED_FILE_PATH_ROOT_TYPE = LOCAL_DOWNLOADED_PATH + "RootType_Sample.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE = LOCAL_DOWNLOADED_PATH
LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_SAMPLE = LOCAL_DOWNLOADED_PATH + "UiTemplate.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_MEALCODE_SAMPLE = LOCAL_DOWNLOADED_PATH + "Mealcode.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_EXPORT_SAMPLE = LOCAL_DOWNLOADED_PATH
LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE_EXPORT_SAMPLE = LOCAL_DOWNLOADED_PATH
LOCAL_DOWNLOADABLE_FILE_PATH_CATALOG_EXPORT_SAMPLE = LOCAL_DOWNLOADED_PATH

ACTUAL_LIST_1065 = ["Marketplace", "D2C"]
ACTUAL_LIST_1065_011 = [
    "Exclusively cooked at home_3 (Cred Automation Store/Exclusively cooked at home_3)",
    "Exclusively cooked at home_7 (Cred Automation Store/Exclusively cooked at home_7)",
    "Non-alcoholic (Cred Automation Store/Beverages/Non-alcoholic)",
]
DROPDOWN_LISTING = [
    "Map Airline Categories",
    "Load Updated Catalog",
]
CSV_SAMPLE_UI_TEMPLATE = [['name']]

CSV_SAMPLE_MEAL_CODE = [['mealcodes']]

CSV_SAMPLE_OREDER_TRACKINGINFO = [['OrderId', 'ShippingId', 'OrderLineItemId', 'TrackingId', 'InventoryLocationId', 'OrderLineStatus', 'TrackingLink'],
                                  ['2341', '2341', '2431', 'AJHS1233KSm-sd_a', '1233', 'PENDING', 'www.abc.com']
                                  ]
CSV_SAMPLE_PRODUCT = [['name_en', 'name_es', 'name_hi', 'shortDescription_en', 'shortDescription_es', 'shortDescription_hi', 'description_en', 'description_es', 'description_hi', 'brand_en', 'brand_es', 'brand_hi', 'sku', 'barcode', 'deliveryMethod', 'nextFlightLeadTime(Business Hrs)', 'gatePickupLeadTime(Business Hrs)', 'productType', 'productSet', 'notApplicableCountries', 'newFrom', 'newTo', 'spotLight', 'ageVerificationRequire', 'isAvailableToSell', 'requireShipping', 'isvariantDefault', 'isPerishable',
'image1_1', 'image2_1', 'image4_3', 'image2_3', 'image5_6', 'gallery', 'price_USD', 'specialPrice_USD', 'isTaxable', 'minQuantityAllowed/Order', 'maxQuantityAllowed/Order', 'weight', 'weightUnit', 'shippingLength', 'shippingLengthUnit', 'shippingWidth', 'shippingWidthUnit', 'shippingHeight', 'shippingHeightUnit', 'customAttribute', 'specialityAttribute', 'productId', 'isVariant', 'parentSku', 'variantAttributes'],

['LG', 'LG', 'एलजी', 'LG Gram', 'LG gramo', 'एलजी उत्पाद', 'LG Gram', 'LG gramo', 'एलजी उत्पाद', 'kellogs', 'Nombre del catálogo',
'केलॉग', 'LG', '969696', 'Onboard', '11', '11', 'Amenities', 'Simple', 'IN', '2023-06-16', '2023-06-16', '1', '1', '1', '1', '1', '1', 'https://placehold.jp/1200x1200.png', 'https://placehold.jp/1480x740.png', 'https://placehold.jp/760x570.png', 'https://placehold.jp/944x1416.png', 'https://placehold.jp/670x804.png', '', '', '', '1', '2', '10', '22', 'LB', '12', 'IN', '15', 'IN', '15', 'IN', 'flightCapacity,300;pilotName,Ramesh', '243,367,19', '976', '0', '', '']]
CSV_FORMAT_FLIGHT_SAMPLE = [
    [
        "flightRouteNumber",
        "flightBeginDateTime",
        "flightEndDateTime",
        "sectorsName",
        "airlineICAOCode",
    ],
    ["SIA 98", "04/22/2023 06:21", "04/22/2023 06:20", "IND-UK,1;SIN-SYD,2;", "DQ"],
    ["SIA 99", "04/22/2023 16:21", "04/22/2023 06:20", "SIN-SYD,3;IND-UK,5;", "DQ"],
]
CSV_SAMPLE_ROUTEGROUP = [
    ["name_en", "code", "airlineICAOCode"],
    ["Star Alliance", "RG01", "SIA"],
]

CSV_SAMPLE_ROUTEGROUP_AIRLINE = [
    ["name_en", "code"],
    ["Star Alliance", "RG01"],
]

CSV_SAMPLE_CATLOG = [
    ["name_en", "name_es", "name_hi", "products"],
    [
        "Exclusively cooked at home",
        "Exclusivamente cocinado en casa",
        "विशेष रूप से पका हुआ भोजन",
        "PAC1123",
    ],
    ["", "", "", "PACSKU112"],
    ["", "", "", "PACSKU1113"],
    ["Catalog name", "Nombre del catálogo", "कैटलॉग का नाम", "PAC1123"],
    ["", "", "", "PACSKU112"],
    ["", "", "", "PACSKU1113"],
]

CSV_SAMPLE_AIRPOTS = [
    [
        "airportName_en",
        "icao_code",
        "IATACode",
        "address",
        "city",
        "state",
        "zipCode",
        "country",
        "timeZone",
    ],
    [
        "Chhatrapati Shivaji Maharaj",
        "BOM",
        "BOM",
        "Mumbai, Maharashtra 400099",
        "Mumbai",
        "Maharashtra",
        "400099",
        "IN",
        "Pacific/Niue",
    ],
]

CSV_SAMPLE_CATEGORY = [
    [
        "name_en",
        "name_es",
        "name_hi",
        "disclaimer_en",
        "disclaimer_es",
        "disclaimer_hi",
        "shortDescription_en",
        "shortDescription_es",
        "shortDescription_hi",
        "description_en",
        "description_es",
        "description_hi",
        "parent",
        "url",
        "image1_1",
        "image2_1",
        "image4_3",
        "image5_6",
        "bannerImage5_1",
        "backgroundImage16_9",
        "attributeSet",
        "variantThemeName_1",
        "variantAttribute_1",
        "variantThemeName_2",
        "variantAttribute_2",
    ],
    [
        "Exclusively cooked at home",
        "Exclusivamente cocinado en casa",
        "विशेष रूप से पका हुआ भोजन",
        "Exclusively cooked at home",
        "Exclusivamente cocinado en casa",
        "विशेष रूप से पका हुआ भोजन",
        "Exclusively cooked at home",
        "Exclusivamente cocinado en casa",
        "विशेष रूप से पका हुआ भोजन",
        "Exclusively cooked at home",
        "Exclusivamente cocinado en casa",
        "विशेष रूप से पका हुआ भोजन",
        "/Food & Beverage",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Food & Beverages;Electronics",
        "Theme1",
        "OS;Allergies",
        "Theme2",
        "OS;Allergies",
    ],
    [
        "Exclusively cooked at home_2",
        "Exclusivamente cocinado en casa_2",
        "विशेष रूप से पका हुआ भोजन_2",
        "Exclusively cooked at home_2",
        "Exclusivamente cocinado en casa_2",
        "विशेष रूप से पका हुआ भोजन_2",
        "Exclusively cooked at home_2",
        "Exclusivamente cocinado en casa_2",
        "विशेष रूप से पका हुआ भोजन_2",
        "Exclusively cooked at home_2",
        "Exclusivamente cocinado en casa_2",
        "विशेष रूप से पका हुआ भोजन_2",
        "/Food & Beverage",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Food & Beverages;Electronics",
        "Theme1",
        "OS;Allergies",
        "Theme2",
        "OS;Allergies",
    ],
    [
        "Exclusively cooked at home_3",
        "Exclusivamente cocinado en casa_3",
        "विशेष रूप से पका हुआ भोजन_3",
        "Exclusively cooked at home_3",
        "Exclusivamente cocinado en casa_3",
        "विशेष रूप से पका हुआ भोजन_3",
        "Exclusively cooked at home_3",
        "Exclusivamente cocinado en casa_3",
        "विशेष रूप से पका हुआ भोजन_3",
        "Exclusively cooked at home_3",
        "Exclusivamente cocinado en casa_3",
        "विशेष रूप से पका हुआ भोजन_3",
        "",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Electronics",
        "Theme1",
        "RAM",
        "Theme2",
        "OS",
    ],
    [
        "Exclusively cooked at home_4",
        "Exclusivamente cocinado en casa_4",
        "विशेष रूप से पका हुआ भोजन_4",
        "Exclusively cooked at home_4",
        "Exclusivamente cocinado en casa_4",
        "विशेष रूप से पका हुआ भोजन_4",
        "Exclusively cooked at home_4",
        "Exclusivamente cocinado en casa_4",
        "विशेष रूप से पका हुआ भोजन_4",
        "Exclusively cooked at home_4",
        "Exclusivamente cocinado en casa_4",
        "विशेष रूप से पका हुआ भोजन_4",
        "/Food & Beverage",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "Food & Beverages;Electronics",
        "Theme1",
        "OS;Allergies",
        "Theme2",
        "OS;Allergies",
    ],
    [
        "Exclusively cooked at home_5",
        "Exclusivamente cocinado en casa_5",
        "विशेष रूप से पका हुआ भोजन_5",
        "Exclusively cooked at home_5",
        "Exclusivamente cocinado en casa_5",
        "विशेष रूप से पका हुआ भोजन_5",
        "Exclusively cooked at home_5",
        "Exclusivamente cocinado en casa_5",
        "विशेष रूप से पका हुआ भोजन_5",
        "Exclusively cooked at home_5",
        "Exclusivamente cocinado en casa_5",
        "विशेष रूप से पका हुआ भोजन_5",
        "/Food & Beverage",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Food & Beverages;Electronics",
        "Theme1",
        "",
        "",
        "",
    ],
    [
        "Exclusively cooked at home_6",
        "Exclusivamente cocinado en casa_6",
        "विशेष रूप से पका हुआ भोजन_6",
        "",
        "",
        "",
        "/Food & Beverage",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Food & Beverages;Electronics",
        "Theme1",
        "",
        "",
        "",
    ],
    [
        "Exclusively cooked at home_7",
        "Exclusivamente cocinado en casa_7",
        "विशेष रूप से पका हुआ भोजन_7",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
    ],
]


CSV_SAMPLE_FLIGHT_ADMIN = [
    ["flightRouteNumber", "flightBeginDateTime", "flightEndDateTime", "sectorsName"],
    ["SIA 98", "04/22/2023 06:21", "04/22/2023 06:20", "IND-UK,1;SIN-SYD,2;"],
    ["SIA 99", "04/22/2023 16:21", "04/22/2023 06:20", "SIN-SYD,2;IND-UK,3;"],
]

CSV_SAMPLE_AIRLINE = [
    [
        "name_en",
        "description_en",
        "disclaimer_en",
        "shortDescription_en",
        "parent",
        "url",
        "UITemplate",
        "image1_1",
        "image2_1",
        "image4_3",
        "image5_6",
        "bannerImage5_1",
        "backgroundImage16_9",
        "sequenceNumber",
        "rootType",
    ],
    [
        "Airline 1",
        "Airline",
        "Airline",
        "Airline",
        "",
        "https://i-converter.com/files/jpg-to-ttttrr",
        "TemplateName",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "100",
        "Root Type1",
    ],
    [
        "Airline 2",
        "Airline",
        "Airline",
        "Airline",
        "/Airline 1",
        "https://i-converter.com/files/jpg-to-ttttrr1",
        "TemplateName",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "101",
        "Root Type2",
    ],
]


CSV_SAMPLE_AIRLINE_ROUTEGROUP = [["name_en", "code"], ["Star Alliance", "RG01"]]

CSV_SAMPLE_AIRCRAFT = [
    ["OEMName_en", "description_en", "familyName", "modelName", "image"],
    [
        "Airbus A380",
        "Airbus is an international pioneer in the aerospace industry",
        "Airbus",
        "tt",
        "https://i.ytimg.com/vi/_gF0fLBYlZ8/maxresdefault.jpg",
    ],
    [
        "Airbus A381",
        "Airbus is an international pioneer in the aerospace industry",
        "Airbus",
        "tt",
        "https://i.ytimg.com/vi/_gF0fLBYlZ8/maxresdefault.jpg",
    ],
]


CSV_SAMPLE_SECTOR = [
    [
        "sectorName_en",
        "summary_en",
        "routeGroup",
        "originairportICAOCode",
        "destinationairportICAOCode",
        "distance",
        "unit",
    ],
    [
        "SIN-SYD",
        "Singapore (SIN) to Sydney (SYD)",
        "Star Alliance",
        "SP",
        "YOM",
        "780",
        "Miles",
    ],
]
CSV_SAMPLE_PRICEUPDATE = [["ProductName", "SkuCode", "CatalogAssignmentId", "Price"]]

CSV_MEAL_CODE_SAMPLE = [
    ["ProductId", "ProductPacSku", "ProductName", "Store", "MealCode"],
    ["40700", "T2002", "T2002", "Cred Automation Store", "Automation_mealcode"],
    [
        "9332",
        "T2001",
        "Coca-Cola Zero Sugar Â®",
        "Cred Automation Store",
        "",
    ],
    ["9333", "T2002", "Diet Coke Â®", "Cred Automation Store", ""],
    ["9334", "T3003", "Sprite Â®", "Cred Automation Store", ""],
]

CSV_SAMPLE_INVENTORYUPDATE = [["ProductName", "SkuCode", "CatalogAssignmentId", "Qty"]]

CSV_AIRLINE_CATEGORY = [
    [
        "name_en",
        "description_en",
        "disclaimer_en",
        "shortDescription_en",
        "parent",
        "url",
        "UITemplate",
        "image1_1",
        "image2_1",
        "image4_3",
        "image5_6",
        "bannerImage5_1",
        "backgroundImage16_9",
        "sequenceNumber",
        "rootType",
    ]
]

CSV_MAP_AIRLINE_CATEGORIES = [
    [
        "ProductId",
        "ProductSku",
        "Name",
        "AssignmentType",
        "BackendCategory",
        "AirlineCategory",
    ],
    ["86604392", "T2002", "T2002", "RouteGroup", "Non-alcoholic", ""],
    [
        "69005175",
        "F3017",
        "AHA® Blueberry + Pomegranate Sparkling Water",
        "RouteGroup",
        "Non-alcoholic",
        "",
    ],
]

CSV_CATALOG_PRODUCT = [
    [
        "ProductId",
        "ProductSku",
        "Name",
        "AssignmentType",
        "BackendCategory",
        "AirlineCategory",
    ],
    [
        "86604392",
        "T2002",
        "T2002",
        "RouteGroup",
        "Exclusively cooked at home_7",
        "86604404",
    ],
    ["86604394", "dontdelete", "dontdelete", "RouteGroup", "Non-alcoholic", "86604406"],
    [
        "86604360",
        "cred_product",
        "cred_product",
        "RouteGroup",
        "Exclusively cooked at home_3",
        "86604403",
    ],
]
CSV_CATALOG_UPDATE_PRODUCT_AC = [
    [
        "ProductId",
        "ProductSku",
        "Name",
        "AssignmentType",
        "BackendCategory",
        "AirlineCategory",
        "Status",
        "Message",
    ],
    [
        "69002810",
        "Automation_product",
        "Automation_product",
        "Sector",
        "Exclusively cooked at home_3",
        "82951",
        "failure",
        "Airline category is(are) not valid | Product is not associated with catalog",
    ],
]
CSV_ROOTTYPE_SAMPLE = [['name', 'description'],
                       ['Root Type1', 'Root Desc'],
                       ['Root Type2', 'Root Desc 2']]

# PAC_ADMIN
# done
HOME_ID = 1

DEDICATED_CLIENT_ID_STORE = "bsn2won3dw"
DEDICATED_CLIENT_SECRET_STORE = "yj640t7yoeh1fg98orup"

DEDICATED_CLIENT_ID_AIRLINE = "pdxga7zij1"
DEDICATED_CLIENT_SECRET_AIRLINE = "woi0svztjj&hts8affvq"


DEDICATED_AIRLINE = "Cred Automation Airline"
DEDICATED_AIRLINE_ID = "86604348"

DEDICATED_STORE = "Cred Automation Store"
DEDICATED_STORE_ID = "86604316"
# done
DEDICATED_STORE_1_NAME = "Flipkart"
DEDICATED_STORE_1_ID = "74109"
DEDICATED_STORE_USERID = "86604315"
# done
DEDICATED_STORE_2_NAME = "Cred Automation D2C"
DEDICATED_STORE_2_ID = "86605270"
# done
DEDICATED_STORE_MARKETPLACE = "Cred Automation Marketplace"
DEDICATED_STORE_MARKETPLACE_ID = "86605271"

DEDICATED_SECTOR_1_NAME = "Automation_Sector"
DEDICATED_SECTOR_1_ID = "86604386"

DEDICATED_SECTOR_2_NAME = "Automation_Sector_2"
DEDICATED_SECTOR_2_ID = "86604389"

DEDICATED_SECTOR_3_NAME = "Unpublish_Sector"
DEDICATED_SECTOR_3_ID = "86604526"

DEDICATED_SECTOR_4_NAME = "Sector_Unpublish_For_Csv"
DEDICATED_SECTOR_4_ID = "86605134"

DEDICATED_DIFFRENT_CATALOG_ID_1 = 86600421
DEDICATED_DIFFRENT_PRODUCT_ID_1 = 100443

DEDICATED_CATALOG_ASSIGNMENT_1_NAME = "Automation_Assignment"
DEDICATED_CATALOG_ASSIGNMENT_1_ID = "41172"

DEDICATED_CATALOG_ASSIGNMENT_2_NAME = "new catalog"
DEDICATED_CATALOG_ASSIGNMENT_2_ID = "86604400"

DEDICATED_CATALOG_ASSIGNMENT_3_NAME = "Automation_CatalogAssignment"
DEDICATED_CATALOG_ASSIGNMENT_3_ID = "86604402"

DEDICATED_CATALOG_ASSIGNMENT_4_NAME = "Catalog Creation"
DEDICATED_CATALOG_ASSIGNMENT_4_ID = "86604408"

DEDICATED_CATALOG_ASSIGNMENT_5_NAME = "CatalogAssignment"
DEDICATED_CATALOG_ASSIGNMENT_5_ID = "86604410"
# done
DEDICATED_CATALOG_ASSIGNMENT_6_NAME = "Automation_Catalog"
DEDICATED_CATALOG_ASSIGNMENT_6_ID = "86604409"

DEDICATED_CATALOG_ASSIGNMENT_7_NAME = "Automation_Catalog_assignment_map_1"
DEDICATED_CATALOG_ASSIGNMENT_7_ID = "86604468"

DEDICATED_CATALOG_ASSIGNMENT_8_NAME = "Catalog_Assignment_For_Editbasedata"
DEDICATED_CATALOG_ASSIGNMENT_8_ID = "86604512"

DEDICATED_CATALOG_ASSIGNMENT_9_NAME = "Automation_Flight_CatalogAssignment"
DEDICATED_CATALOG_ASSIGNMENT_9_ID = "86606196"

# --------------------------------------------------

DEDICATED_ROOT_TYPE_1_NAME = "Automation_Roottype"
DEDICATED_ROOT_TYPE_1_ID = "86604385"

DEDICATED_ROOT_TYPE_2_NAME = "Automation_RootType_1"
DEDICATED_ROOT_TYPE_2_ID = "86622592"

DEDICATED_ROUTE_GROUP_1_NAME = "Automation_Route_Group"
DEDICATED_ROUTE_GROUP_1_ID = "86604383"

DEDICATED_UNPUBLISHED_ROOTTYPE_NAME = "UNPUBLISHED_ROOTTYPE"
DEDICATED_UNPUBLISHED_ROOTTYPE_ID = "86615784"

DEDICATED_UI_TEMPLATE_DIFFERENT_AIRLINE_NAME = "amenities_category"
DEDICATED_UI_TEMPLATE_DIFFERENT_AIRLINE_ID = "56191"

#----------------------------------------------------
DEDICATED_CATALOG_1_NAME = "Automation_Catalog"
DEDICATED_CATALOG_1_ID = "86604391"

# DEDICATED_CATALOG_2_NAME = "Automation_Catalog"
# DEDICATED_CATALOG_2_ID = "9958"

DEDICATED_CATALOG_2_NAME = "Automation_Catalog_Assignment_1"
DEDICATED_CATALOG_2_ID = "86604411"

DEDICATED_CATALOG_3_NAME = "BksMEOC"
DEDICATED_CATALOG_3_ID = "86604396"

DEDICATED_CATALOG_4_NAME = "Automation_Catalog_1"
DEDICATED_CATALOG_4_ID = "86604397"


DEDICATED_FLIGHT_1_NAME = "Automation_Flight"
DEDICATED_FLIGHT_1_ID = "86604398"

DEDICATED_SKU_1_NAME = "T2002"
DEDICATED_SKU_1_ID = "86604392"

DEDICATED_UI_TEMPLATE_1_NAME = "Automation_UITemplate"
DEDICATED_UI_TEMPLATE_1_ID = "86604372"

DEDICATED_UI_TEMPLATE_2_NAME = "Unpublish_UI_template"
DEDICATED_UI_TEMPLATE_2_ID = "86605359"

DEDICATED_MEAL_CODE_1_NAME = "hbacb"
DEDICATED_MEAL_CODE_1_ID = "86604371"

DEDICATED_MEALCODE_2_NAME = "Automation_mealcode"
DEDICATED_MEALCODE_2_ID = "86604370"

DEDICATED_PRODUCT_1_NAME = "Automation_Product_1"
DEDICATED_PRODUCT_1_ID = "86604365"

DEDICATED_PRODUCT_2_NAME = "automation_product_2"
DEDICATED_PRODUCT_2_ID = "86604356"

DEDICATED_PRODUCT_3_NAME = "cred_product"
DEDICATED_PRODUCT_3_ID = "86604360"

DEDICATED_PRODUCT_4_NAME = "Automation_Perishable"
DEDICATED_PRODUCT_4_ID = "86604351"

DEDICATED_PRODUCT_5_NAME = "Automation_Product_3"
DEDICATED_PRODUCT_5_ID = " 86604361"

DEDICATED_PRODUCT_6_NAME = "t3003"
DEDICATED_PRODUCT_6_ID = "86604368"

# category not found
DEDICATED_PRODUCT_7_NAME = "sku_10"
DEDICATED_PRODUCT_7_ID = "14341"

DEDICATED_PRODUCT_8_NAME = "unpublish_product"
DEDICATED_PRODUCT_8_ID = "86604362"

DEDICATED_PRODUCT_9_NAME = "Automation_Product"
DEDICATED_PRODUCT_9_ID = "86604363"

DEDICATED_PRODUCT_10_NAME = "product_for_Categorydropdown"
DEDICATED_PRODUCT_10_ID = "86604369"

DEDICATED_PRODUCT_11_NAME = "product_api"
DEDICATED_PRODUCT_11_ID = " 86604466"

DEDICATED_INVALID_SECTOR = "86594027"
SECTOR_DIFFERENT_AIRLINE = "86603796"
FLIGHT_DIFFERENT_AIRLINE = "85987"
PRODUCT_DIFFERENT_STORE = "86593489"
PRODUCT_DIFFERENT_STORE_2 = "87361"

DEDICATED_CATEGORY_1_NAME = "Exclusively cooked at home_3"
DEDICATED_CATEGORY_1_ID = 86604350

DEDICATED_UNPUBLISHED_AIRLINE_NAME = "Unpublished_Airline"
DEDICATED_UNPUBLISHED_AIRLINE_ID = 86615739

DEDICATED_UNPUBLISHED_STORE_NAME = "Unpublished_Store"
DEDICATED_UNPUBLISHED_STORE_ID = 86615753
DEDICATED_CATEGORY_2_NAME = "Non-alcoholic"
DEDICATED_CATEGORY_2_ID = "86604364"

DEDICATED_CATEGORY_3_NAME = "Beverages"
DEDICATED_CATEGORY_3_ID = "86604354"

DEDICATED_CATEGORY_4_NAME = "Exclusively cooked at home_7"
DEDICATED_CATEGORY_4_ID = "86604353"

# done
DEDICATED_CATEGORY_5_NAME = "CredAutomationStoreCategory1"
DEDICATED_CATEGORY_5_ID = "86605293"
# done
DEDICATED_CATEGORY_6_NAME = "CredAutomationStoreCategory2"
DEDICATED_CATEGORY_6_ID = "86605294"

DEDICATED_UNPUBLISH_CATEGORY_NAME = "Unpublish_airline_category"
DEDICATED_UNPUBLISH_CATEGORY_ID = "86604467"

DEDICATED_AIRLINE_CATEGORY_7_NAME = "Automation_Category"
DEDICATED_AIRLINE_CATEGORY_7_ID = "86622593"

DEDICATED_AIRLINE_CATEGORY_4_NAME = "Exclusively cooked at home_7"
DEDICATED_ORDER_1_NAME = 'Automation_Order_1'
DEDICATED_ORDER_1_ID = '86609537'
DEDICATED_ORDER_1_ORDER_SHIPMENT_ID = '86609539'
DEDICATED_ORDER_1_LINE_ITEM_NAME = 'Demo Lineitem'
DEDICATED_ORDER_1_LINE_ITEM_ID = '86609541'

DEDICATED_ORDER_2_NAME = 'TEST_Multiple_line_items_1'
DEDICATED_ORDER_2_ID = '86615705'
DEDICATED_ORDER_2_ORDER_SHIPMENT_ID = '86615707'
DEDICATED_ORDER_2_LINE_ITEM_ID = '86615709'

DEDICATED_INVALID_ORDER_NAME = "Automation_without_required_information"
DEDICATED_INVALID_ORDER_ID = "86615596"

DEDICATED_FAILED_ORDER_ID = '86609537'
DEDICATED_FAILED_ORDER_ORDER_SHIPMENT_ID = '86609541'
DEDICATED_FAILED_ORDER_LINE_ITEM_ID = '86609539'

DEDICATED_DIFFERENT_STORE_ORDER_ID = "86603515"
DEDICATED_DIFFERENT_STORE_SHIPMENT_ID = "86603519"
DEDICATED_DIFFERENT_STORE_LINE_ITEM_ID = "86603523"
DEDICATED_DIFFERENT_STORE_INVENTORY_LOCATION_ID = "80316"
DEDICATED_INVENTORY_LOCATION_ID = "86604327"

DEDICATED_AIRLINE_CATEGORY_4_ID = "86604404"

DEDICATED_FOLDER_1 = "Exclusively cooked at home_3"
DEDICATED_FOLDER_1_ID = "86604352"

DEDICATED_FOLDER_2 = "Non-alcoholic"
DEDICATED_FOLDER_2_ID = "86604367"

DEDICATED_FOLDER_3 = "Meal Code"
DEDICATED_FOLDER_3_ID = "86604331"
# done
DEDICATED_FOLDER_4 = "RouteGroup"
DEDICATED_FOLDER_4_ID = "86604384"

DEDICATED_FOLDER_5 = "Unpublished Products"
DEDICATED_FOLDER_5_ID = "86604321"
# done
DEDICATED_STORE_LOCATION_1_NAME = "Default Inventory"
DEDICATED_STORE_LOCATION_1_ID = "86604327"
# done
DEDICATED_CLIENT_ID = "bsn2won3dw"
DEDICATED_CLIENT_SECRET = "yj640t7yoeh1fg98orup"

DEDICATED_VARIENT_PARENT_PRODUCT_NAME = "Automation_Config_Variant_Product"
DEDICATED_VARIENT_PARENT_PRODUCT_ID = "86604974"

DEDICATED_AIRLINE_CATEGORY_1_NAME = "Exclusively cooked at home_3"
DEDICATED_AIRLINE_CATEGORY_1_ID = "86604403"


DEDICATED_AIRLINE_CATEGORY_5_NAME = "Automation_category_1"
DEDICATED_AIRLINE_CATEGORY_5_ID = "86605295"

DEDICATED_OTHER_AIRLINE_SECTOR_NAME = "LAX-NRT"
DEDICATED_OTHER_AIRLINE_SECTOR_ID = "89772"


DEDICATED_FULFILMENT_NAME = "Onboard"
DEDICATED_FULFILMENT_ID = "61"
ROOT_TYPE_AIRLINE_FOLDER = 86604337
UI_TYPE_AIRLINE_FOLDER = 86604336

DEDICATED_CAMPAIGN_NAME = "Campaign1"
DEDICATED_CAMPAIGN_ID = "86606332"

# done
UI_TEMPLATE_FOLDER = "8659"
# done
ROOT_TYPE_FOLDER = "52067"
# done
AIRPORTS_FOLDER = "80"
# done
USERS_FOLDER = "86604339"
# done
AIRLINES_FOLDER = "24"
# done
PAYMENTMETHODS_FOLDER = "8670"
# done
FULFILLMENT_FOLDER = "19"
# done
CATEGORY_FOLDER = "86604335"
# done
CRED_AUTOMATION_AIRLINE_FOLDER = "86604330"

CRED_AUTOMATION_AIRLINE_CATEGORY_FOLDER = "86604335"

STORE_CATALOG_FOLDER = " 86604318"
ORDER_FOLDER = '30'
DEDICATED_STORE_IN_AIRLINE = "86604401"
AIRLINEROUTEGOUPNAME = "QAroute"
STORE_USERS_FOLDER = "86604328"
AIRCRAFT_FOLDER = "74"

STORES_FOLDER = "5"
DEDICATED_STORE_IN_AIRLINE = "86604401"

MEAL_CODE_FOLDER_AIRLINE_LOGIN = '86604331'
ORDER_FOLDER_24 = '86609536'

DEDICATED_ORDER_1_NAME = "Automation_Order_1"
DEDICATED_ORDER_1_ID = "86609537"

DEDICATED_ORDER_SHIPMENT_1_NAME = "home_delivery"
DEDICATED_ORDER_SHIPMENT_1_ID = "86609539"

DEDICATED_ONLINE_SHOP_ORDER_ITEM_1_NAME = "Demo Lineitem"
DEDICATED_ONLINE_SHOP_ORDER_ITEM_1_ID = "86609541"

LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_RECEIPT = (
    LOCAL_DOWNLOADED_PATH + "OrderReceipt_"+DEDICATED_ORDER_1_ID+".pdf"
)

DEDICATED_AIRLINE_NO_STORE = "Cred Automation Airline No Store"
DEDICATED_AIRLINE_NO_STORE_ID = "86615722"

DEDICATED_OTHER_AIRLINE_CATEGORY_ID = "74286"
