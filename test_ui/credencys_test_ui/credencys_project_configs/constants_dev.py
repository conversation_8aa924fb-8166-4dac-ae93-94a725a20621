import os

LOCAL_DOWNLOADED_PATH = os.getcwd() + "/downloads/"
# LOCAL_DOWNLOADED_PATH = "C:\\Users\\<USER>\\Downloads\\"
# LOCAL_DOWNLOADED_PATH = "C:\\Users\\<USER>\\Downloads\\"
# LOCAL_DOWNLOADED_PATH = "C:\\Users\\<USER>\\Downloads\\"

# region URL and Credentails
# PAC_URL = "https://qa.marketplace-qa.nextcloud.aero/admin/login"
PAC_URL = "https://marketplace-dev.nextcloud.aero/admin/login"

PAC_API_GATEWAY_URL = "https://api.marketplace-dev.nextcloud.aero/"

PAC_USER_MAIL = "<EMAIL>"
PAC_USER_PASSWORD = "PACadmin@12345"
# PAC_USER_MAIL = "admin"
# PAC_USER_PASSWORD = "admin@123"

PAC_STORE_USER_MAIL = "<EMAIL>"
PAC_STORE_USER_PASSWORD = "Autostoread@123"

PAC_AIRLINE_MAIL = "<EMAIL>"
PAC_PASSWORD_MAIL = "Airlinead@123"

PAC_AIRLINE_MANAGER_MAIL = "<EMAIL>"
PAC_AIRLINE_MANAGER_PASSWORD = "Airlinead@123"

PAC_STORE_MANAGER_MAIL = "<EMAIL>"
PAC_STORE_MANAGER_PASSWORD = "Autostoread@123"

PAC_STORE_MARKETPLACE_USER_MAIL = "<EMAIL>"
PAC_STORE_MARKETPLACE_PASSWORD = "MPStoreAdmin@1234"

PAC_UNPUBLISHED_STORE_USER_MAIL = "<EMAIL>"
PAC_UNPUBLISHED_STORE_PASSWORD = "Unpublished@123"

PAC_UNPUBLISHED_AIRLINE_USER_MAIL = "<EMAIL>"
PAC_UNPUBLISHED_AIRLINE_PASSWORD = "Unpublished@123"
#endregion URL and Credentails

CRED_AUTOMATION_AIRLINE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/flight/"
CRED_AUTOMATION_AIRLINE_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjgyOTI1LCJzdWIiOiJhaXJsaW5lIiwieC1hbXpuLXRyYWNlLWlkIjoiUm9vdD0xLTY2MjYyMjcwLTcyYmJiZmEyODllOTJiYTFmMmQzZGEyNDtQYXJlbnQ9MDFjYjEzYzQ3YTJlOTJjNztTYW1wbGVkPTEiLCJpYXQiOjE3MTM3NzUyMTYsImV4cCI6MTc0NTMxMTIxNn0.IdCi462Lq9VTRCEEXnccBgRQ5xsAQFgbgn5ZsZhccdE"
CRED_AUTOMATION_STORE_MARKETPLACE_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjY5MDE2MzU3LCJzdWIiOiJzdG9yZSIsImlhdCI6MTcwNzQ3MDgxMSwiZXhwIjoxNzM5MDkzMjExfQ.OKa-2VPp4Gl9FtGNhU0XvmONpxNw6DzFeQA2WzkN99k"
CRED_AUTOMATION_STORE_CATALOG_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/catalog/"
CRED_AUTOMATION_STORE_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjgyOTM0LCJzdWIiOiJzdG9yZSIsIngtYW16bi10cmFjZS1pZCI6IlJvb3Q9MS02NjI2MjIxZi0wNTZhMTMwOTgyYzVhZGU2MThjODVkMGY7UGFyZW50PTI5ZjUxYWY0YjViOWVkYmM7U2FtcGxlZD0xIiwiaWF0IjoxNzEzNzc1MTM1LCJleHAiOjE3NDUzMTExMzV9.ZnvYW-mwCco7mdHtUxh6V214L1dxI5CwSbTPN2Fcq4A"
AIRLINE_CATEGORY_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/airline-category/"
CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL = (
    PAC_API_GATEWAY_URL + "marketplace/v1/ams/routegroups/"
)
JWT_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJTSUEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNjg5ODM0MTczLCJleHAiOjI1MzY0MzE0MDB9.LqZ4Mzqjv_PRey7aHG8OQ-0ewCXmAEjUiRV9e-_PZig"

ORDER_JWT_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJDUkUiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNzAzMDY1MTc5LCJleHAiOjI1MzY0MzE0MDB9.lTCmSLvVxAEuZm0PttDl4GwqD3ZyfIDxA8cYGuNiYq0"
FULFILLMENT_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/fulfillment"
CABIN_CLASS_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/cabinClass"
STORE_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/store/information"
AIRLINE_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/airline"
AIRCRAFT_GET_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/aircrafts?limit=121"
ROUTE_SECTOR_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/sector/"
MEAL_CODE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/meal-code?offset=1"
PRODUCT_MEALCODE_URL = (
    PAC_API_GATEWAY_URL + "marketplace/v1/ams/meal-code/products?offset=1&limit=1000"
)
UPDATE_MEALCODE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/product-configuration"
KIT_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/prepare-kit/"
ROOT_TYPE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/root-type?limit=30"
ROUTE_CATALOG = PAC_API_GATEWAY_URL + "marketplace/v1/pim/route-catalog/"
CATEGORIES_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/categories?offset=1"
TAX_URL = PAC_API_GATEWAY_URL + "marketplace/v1/store/tax/"
PRODUCTS_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/product/"
UI_TEMPLATE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/ui-template"
AIRPORTS_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/airports"
UPDATE_PRICE_URL = (
    PAC_API_GATEWAY_URL + "marketplace/v1/pim/route-catalog/69005243/price"
)
DEPLOYMENT_INVENTORY_URL = (
    PAC_API_GATEWAY_URL
    + "marketplace/v1/pim/route-catalog/69005239/deploymentInventory"
)
GET_PRICE = PAC_API_GATEWAY_URL + "marketplace/v1/pim/route-catalog/69005243/"
INVENTORY_CHECK_URL = PAC_API_GATEWAY_URL + "marketplace/v1/inventoryCheck/"
UNPUBLISHED_PRODUCT = (
    PAC_API_GATEWAY_URL + "marketplace/v1/pim/product?offset=1&unpublished=true"
)
UNPUBLISHED_PRODUCT_BY_ID = (
    PAC_API_GATEWAY_URL
    + "marketplace/v1/pim/product?offset=1&ids=14029&unpublished=true"
)
PRODUCT_UPDATE_CATEGORIES = PAC_API_GATEWAY_URL + "marketplace/v1/pim/product/69005227"
WORKFLOW_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/route-catalog-workflow"
PUBLISH_PRODUCT = PAC_API_GATEWAY_URL + "marketplace/v1/pim/publishProduct"
AUTHENTICATION_URL = PAC_API_GATEWAY_URL + "marketplace/v1/auth/token"
CATALOG_BY_STORE_URL = PAC_API_GATEWAY_URL + "marketplace/v1/pim/store/82934/catalog"
CONSUME_ORDER_URL = PAC_API_GATEWAY_URL + "orders"
GET_ORDER_URL = PAC_API_GATEWAY_URL+ "marketplace/v1/oms/order/"
ORDER_LINE_ITEM_STATUS = PAC_API_GATEWAY_URL + "marketplace/v1/oms/orderItemStatus/"
UPDATE_SHIPMENT = PAC_API_GATEWAY_URL + "marketplace/v1/oms/ordershipment/"
DELIVERY_URL = PAC_API_GATEWAY_URL + "marketplace/v1/store/delivery-rule"
STORE_LOCATION_URL = PAC_API_GATEWAY_URL + "marketplace/v1/store/location"
ROUTE_SECTOR_URL = PAC_API_GATEWAY_URL + "marketplace/v1/ams/sector/"
# ID's:

AUTOMATION_CATALOGASSIGNMENT = "14674"
PRODUCT_ID_STATIC = "69005224"
AUTOMATION_CATALOG = "10850"


LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE = LOCAL_DOWNLOADED_PATH + "Flight_Sample.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_ROUTEGROUP_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "RouteGroup_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_CATLOG_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "Catalog_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_AIRPOT_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "Airports_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_CATEGORY_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "Category_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "Airline_Category_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_ROUTEGROUP_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "AirlineRouteGroup_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_AIRCRAFT_CATEGORY_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "AirCraft_Sample.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_SECTOR_SAMPLE = LOCAL_DOWNLOADED_PATH + "Sector_Sample.csv"

LOCAL_DOWNLOADABLE_FILE_PATH_PRICEUPDATE_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "PriceUpdate.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE = LOCAL_DOWNLOADED_PATH + "mealcode_import.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_INVENTORYUPDATE_SAMPLE = (
    LOCAL_DOWNLOADED_PATH + "QtyUpdate.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE = LOCAL_DOWNLOADED_PATH

LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_EXPORT = (
    LOCAL_DOWNLOADED_PATH + "export.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_TRACKINGINFO = (LOCAL_DOWNLOADED_PATH + "trackingInfo.csv")
LOCAL_DOWNLOADABLE_FILE_PATH_MAP_AIRLINE_CATEGORIES = (
    LOCAL_DOWNLOADED_PATH + "catalog_product_ac.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_MAP_AIRLINE_CATEGORIES_LOG = (
    LOCAL_DOWNLOADED_PATH + "catalog_product_ac_log.csv"
)
LOCAL_DOWNLOADABLE_FILE_PATH_CATALOG_PRODUCT_AC = (
    LOCAL_DOWNLOADED_PATH + "catalog_product_ac.csv"
)
LOCAL_DOWNLOADED_FILE_PATH_ROOT_TYPE = LOCAL_DOWNLOADED_PATH + "RootType_Sample.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE = LOCAL_DOWNLOADED_PATH
LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_SAMPLE = LOCAL_DOWNLOADED_PATH + "UiTemplate.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_MEALCODE_SAMPLE = LOCAL_DOWNLOADED_PATH + "Mealcode.csv"
LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_EXPORT_SAMPLE = LOCAL_DOWNLOADED_PATH
LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE_EXPORT_SAMPLE = LOCAL_DOWNLOADED_PATH
LOCAL_DOWNLOADABLE_FILE_PATH_CATALOG_EXPORT_SAMPLE = LOCAL_DOWNLOADED_PATH

ACTUAL_LIST_1065 = ["Marketplace", "D2C"]
ACTUAL_LIST_1065_011 = [
    "Exclusively cooked at home_3 (Cred Automation Store/Exclusively cooked at home_3)",
    "Exclusively cooked at home_7 (Cred Automation Store/Exclusively cooked at home_7)",
    "Non-alcoholic (Cred Automation Store/Beverages/Non-alcoholic)",
]
DROPDOWN_LISTING = [
    "Map Airline Categories",
    "Load Updated Catalog",
]
CSV_SAMPLE_UI_TEMPLATE = [['name']]

CSV_SAMPLE_MEAL_CODE = [['mealcodes']]

CSV_SAMPLE_OREDER_TRACKINGINFO = [['OrderId', 'ShippingId', 'OrderLineItemId', 'TrackingId', 'InventoryLocationId', 'OrderLineStatus', 'TrackingLink'],
                                  ['2341', '2341', '2431', 'AJHS1233KSm-sd_a', '1233', 'PENDING', 'www.abc.com']
                                  ]
CSV_SAMPLE_PRODUCT = [['name_en', 'name_es', 'name_hi', 'shortDescription_en', 'shortDescription_es', 'shortDescription_hi', 'description_en', 'description_es', 'description_hi', 'brand_en', 'brand_es', 'brand_hi', 'sku', 'barcode', 'deliveryMethod', 'nextFlightLeadTime(Business Hrs)', 'gatePickupLeadTime(Business Hrs)', 'productType', 'productSet', 'notApplicableCountries', 'newFrom', 'newTo', 'spotLight', 'ageVerificationRequire', 'isAvailableToSell', 'requireShipping', 'isvariantDefault', 'isPerishable',
'image1_1', 'image2_1', 'image4_3', 'image2_3', 'image5_6', 'gallery', 'price_USD', 'specialPrice_USD', 'isTaxable', 'minQuantityAllowed/Order', 'maxQuantityAllowed/Order', 'weight', 'weightUnit', 'shippingLength', 'shippingLengthUnit', 'shippingWidth', 'shippingWidthUnit', 'shippingHeight', 'shippingHeightUnit', 'customAttribute', 'specialityAttribute', 'productId', 'isVariant', 'parentSku', 'variantAttributes'],

['LG', 'LG', 'एलजी', 'LG Gram', 'LG gramo', 'एलजी उत्पाद', 'LG Gram', 'LG gramo', 'एलजी उत्पाद', 'kellogs', 'Nombre del catálogo',
'केलॉग', 'LG', '969696', 'Onboard', '11', '11', 'Amenities', 'Simple', 'IN', '2023-06-16', '2023-06-16', '1', '1', '1', '1', '1', '1', 'https://placehold.jp/1200x1200.png', 'https://placehold.jp/1480x740.png', 'https://placehold.jp/760x570.png', 'https://placehold.jp/944x1416.png', 'https://placehold.jp/670x804.png', '', '', '', '1', '2', '10', '22', 'LB', '12', 'IN', '15', 'IN', '15', 'IN', 'flightCapacity,300;pilotName,Ramesh', '243,367,19', '976', '0', '', '']]
CSV_FORMAT_FLIGHT_SAMPLE = [
    [
        "flightRouteNumber",
        "flightBeginDateTime",
        "flightEndDateTime",
        "sectorsName",
        "airlineICAOCode",
    ],
    ["SIA 98", "04/22/2023 06:21", "04/22/2023 06:20", "IND-UK,1;SIN-SYD,2;", "DQ"],
    ["SIA 99", "04/22/2023 16:21", "04/22/2023 06:20", "SIN-SYD,3;IND-UK,5;", "DQ"],
]
CSV_SAMPLE_ROUTEGROUP = [
    ["name_en", "code", "airlineICAOCode"],
    ["Star Alliance", "RG01", "SIA"],
]

CSV_SAMPLE_ROUTEGROUP_AIRLINE = [
    ["name_en", "code"],
    ["Star Alliance", "RG01"],
]

CSV_SAMPLE_CATLOG = [
    ["name_en", "name_es", "name_hi", "products"],
    [
        "Exclusively cooked at home",
        "Exclusivamente cocinado en casa",
        "विशेष रूप से पका हुआ भोजन",
        "PAC1123",
    ],
    ["", "", "", "PACSKU112"],
    ["", "", "", "PACSKU1113"],
    ["Catalog name", "Nombre del catálogo", "कैटलॉग का नाम", "PAC1123"],
    ["", "", "", "PACSKU112"],
    ["", "", "", "PACSKU1113"],
]

CSV_SAMPLE_AIRPOTS = [
    [
        "airportName_en",
        "icao_code",
        "IATACode",
        "address",
        "city",
        "state",
        "zipCode",
        "country",
        "timeZone",
    ],
    [
        "Chhatrapati Shivaji Maharaj",
        "BOM",
        "BOM",
        "Mumbai, Maharashtra 400099",
        "Mumbai",
        "Maharashtra",
        "400099",
        "IN",
        "Pacific/Niue",
    ],
]

CSV_SAMPLE_CATEGORY = [
    [
        "name_en",
        "disclaimer_en",
        "shortDescription_en",
        "description_en",
        "parent",
        "url",
        "image1_1",
        "image2_1",
        "image4_3",
        "image5_6",
        "bannerImage5_1",
        "backgroundImage16_9",
        "attributeSet",
        "variantThemeName_1",
        "variantAttribute_1",
        "variantThemeName_2",
        "variantAttribute_2",
    ],
    [
        "Exclusively cooked at home",
        "Exclusively cooked at home",
        "Exclusively cooked at home",
        "Exclusively cooked at home",
        "/Food & Beverage",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Food & Beverages;Electronics",
        "Theme1",
        "OS;Allergies",
        "Theme2",
        "OS;Allergies",
    ],
    [
        "Exclusively cooked at home_2",
        "Exclusively cooked at home_2",
        "Exclusively cooked at home_2",
        "Exclusively cooked at home_2",
        "/Food & Beverage",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Food & Beverages;Electronics",
        "Theme1",
        "OS;Allergies",
        "Theme2",
        "OS;Allergies",
    ],
    [
        "Exclusively cooked at home_3",
        "Exclusively cooked at home_3",
        "Exclusively cooked at home_3",
        "Exclusively cooked at home_3",
        "",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Electronics",
        "Theme1",
        "RAM",
        "Theme2",
        "OS",
    ],
    [
        "Exclusively cooked at home_4",
        "Exclusively cooked at home_4",
        "Exclusively cooked at home_4",
        "Exclusively cooked at home_4",
        "/Food & Beverage",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "Food & Beverages;Electronics",
        "Theme1",
        "OS;Allergies",
        "Theme2",
        "OS;Allergies",
    ],
    [
        "Exclusively cooked at home_5",
        "Exclusively cooked at home_5",
        "Exclusively cooked at home_5",
        "Exclusively cooked at home_5",
        "/Food & Beverage",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Food & Beverages;Electronics",
        "Theme1",
        "",
        "",
        "",
    ],
    [
        "Exclusively cooked at home_6",
        "",
        "",
        "",
        "/Food & Beverage",
        "",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "Food & Beverages;Electronics",
        "Theme1",
        "",
        "",
        "",
    ],
    [
        "Exclusively cooked at home_7",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
    ],
]


CSV_SAMPLE_FLIGHT_ADMIN = [
    ["flightRouteNumber", "flightBeginDateTime", "flightEndDateTime", "sectorsName"],
    ["SIA 98", "04/22/2023 06:21", "04/22/2023 06:20", "IND-UK,1;SIN-SYD,2;"],
    ["SIA 99", "04/22/2023 16:21", "04/22/2023 06:20", "SIN-SYD,2;IND-UK,3;"],
]

CSV_SAMPLE_AIRLINE = [
    [
        "name_en",
        "description_en",
        "disclaimer_en",
        "shortDescription_en",
        "parent",
        "url",
        "UITemplate",
        "image1_1",
        "image2_1",
        "image4_3",
        "image5_6",
        "bannerImage5_1",
        "backgroundImage16_9",
        "sequenceNumber",
        "rootType",
    ],
    [
        "Airline 1",
        "Airline",
        "Airline",
        "Airline",
        "",
        "https://i-converter.com/files/jpg-to-ttttrr",
        "TemplateName",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "100",
        "Root Type1",
    ],
    [
        "Airline 2",
        "Airline",
        "Airline",
        "Airline",
        "/Airline 1",
        "https://i-converter.com/files/jpg-to-ttttrr1",
        "TemplateName",
        "https://placehold.jp/1200x1200.jpg",
        "https://placehold.jp/1480x740.png",
        "https://placehold.jp/760x570.png",
        "https://placehold.jp/665x798.png",
        "https://placehold.jp/3160x632.png",
        "https://placehold.jp/3840x2160.png",
        "101",
        "Root Type2",
    ],
]

CSV_SAMPLE_AIRLINE_ROUTEGROUP = [["name_en", "code"], ["Star Alliance", "RG01"]]

CSV_SAMPLE_AIRCRAFT = [
    ["OEMName_en", "description_en", "familyName", "modelName", "image"],
    [
        "Airbus A380",
        "Airbus is an international pioneer in the aerospace industry",
        "Airbus",
        "tt",
        "https://i.ytimg.com/vi/_gF0fLBYlZ8/maxresdefault.jpg",
    ],
    [
        "Airbus A381",
        "Airbus is an international pioneer in the aerospace industry",
        "Airbus",
        "tt",
        "https://i.ytimg.com/vi/_gF0fLBYlZ8/maxresdefault.jpg",
    ],
]


CSV_SAMPLE_SECTOR = [
    [
        "sectorName_en",
        "summary_en",
        "routeGroup",
        "originairportICAOCode",
        "destinationairportICAOCode",
        "distance",
        "unit",
    ],
    [
        "SIN-SYD",
        "Singapore (SIN) to Sydney (SYD)",
        "Star Alliance",
        "SP",
        "YOM",
        "780",
        "Miles",
    ],
]

CSV_SAMPLE_PRICEUPDATE = [["ProductName", "SkuCode", "CatalogAssignmentId", "Price"]]

CSV_MEAL_CODE_SAMPLE = [
    ["ProductId", "ProductPacSku", "ProductName", "Store", "MealCode"],
    [
        "69005175",
        "3036-P4V1",
        "3036-P4V1",
        "Cred Automation Store",
        "Automation_mealcode",
    ],
    [
        "9332",
        "T2001",
        "Coca-Cola Zero Sugar Â®",
        "Cred Automation Store",
        "",
    ],
    ["9333", "T2002", "Diet Coke Â®", "Cred Automation Store", ""],
    ["9334", "T3003", "Sprite Â®", "Cred Automation Store", ""],
]

CSV_SAMPLE_INVENTORYUPDATE = [["ProductName", "SkuCode", "CatalogAssignmentId", "Qty"]]

CSV_AIRLINE_CATEGORY = [
    [
        "name_en",
        "description_en",
        "disclaimer_en",
        "shortDescription_en",
        "parent",
        "url",
        "UITemplate",
        "image1_1",
        "image2_1",
        "image4_3",
        "image5_6",
        "bannerImage5_1",
        "backgroundImage16_9",
        "sequenceNumber",
        "rootType",
    ]
]

CSV_MAP_AIRLINE_CATEGORIES = [
    [
        "ProductId",
        "ProductSku",
        "Name",
        "AssignmentType",
        "BackendCategory",
        "AirlineCategory",
    ],
    ["69005175", "F3017", "r4frfr", "RouteGroup", "Non-alcoholic", ""],
    [
        "69005175",
        "F3017",
        "AHA® Blueberry + Pomegranate Sparkling Water",
        "RouteGroup",
        "Non-alcoholic",
        "",
    ],
]

CSV_CATALOG_PRODUCT = [['ProductId', 'ProductSku', 'Name', 'AssignmentType', 'BackendCategory', 'AirlineCategory'],
                       ['69005224', 'T2002', 'Diet Coke ®', 'Sector', 'Non-alcoholic', '69002815'],
                       ['69005229', 'cred_product', 'cred_product', 'Sector', 'Exclusively cooked at home_3', '82951']
                       ]

CSV_CATALOG_UPDATE_PRODUCT_AC = [
    [
        "ProductId",
        "ProductSku",
        "Name",
        "AssignmentType",
        "BackendCategory",
        "AirlineCategory",
        "Status",
        "Message",
    ],
    [
        "69002810",
        "Automation_product",
        "Automation_product",
        "Sector",
        "Exclusively cooked at home_3",
        "82951",
        "failure",
        "Airline category is(are) not valid | Product is not associated with catalog",
    ],
]
CSV_ROOTTYPE_SAMPLE = [['name', 'description'],
                       ['Root Type1', 'Root Desc'],
                       ['Root Type2', 'Root Desc 2']]
HOME_ID = 1

DEDICATED_CLIENT_ID_STORE = "i0wl54jayl"
DEDICATED_CLIENT_SECRET_STORE = "mppfnim62e19ugx1jegt"

DEDICATED_CLIENT_ID_AIRLINE = "bshbycwrjg"
DEDICATED_CLIENT_SECRET_AIRLINE = "jh-dv4w5oc5ayofj6fxe"

DEDICATED_AIRLINE = "Cred Automation Airline"
DEDICATED_AIRLINE_ID = "82925"

DEDICATED_STORE = "Cred Automation Store"
DEDICATED_STORE_ID = "82934"

DEDICATED_STORE_1_NAME = "Flipkart"
DEDICATED_STORE_1_ID = "271"
DEDICATED_STORE_USERID = "82948"

# done
DEDICATED_STORE_2_NAME = "Cred Automation D2C"
DEDICATED_STORE_2_ID = "69016937"
# done
DEDICATED_STORE_MARKETPLACE = "Cred Automation Marketplace"
DEDICATED_STORE_MARKETPLACE_ID = "69016357"

DEDICATED_SECTOR_1_NAME = "Automation_Sector"
DEDICATED_SECTOR_1_ID = "69005240"

DEDICATED_SECTOR_2_NAME = "Automation_Sector_2"
DEDICATED_SECTOR_2_ID = "69005242"

DEDICATED_SECTOR_3_NAME = "Unpublish_Sector"
DEDICATED_SECTOR_3_ID = "69015009"

DEDICATED_SECTOR_4_NAME = "Sector_Unpublish_For_Csv"
DEDICATED_SECTOR_4_ID = "69016079"

DEDICATED_DIFFRENT_CATALOG_ID_1 = 18602
DEDICATED_DIFFRENT_PRODUCT_ID_1 = 18597

DEDICATED_CATALOG_ASSIGNMENT_1_NAME = "Automation_Assignment"
DEDICATED_CATALOG_ASSIGNMENT_1_ID = "69067684"

DEDICATED_CATALOG_ASSIGNMENT_2_NAME = "new catalog"
DEDICATED_CATALOG_ASSIGNMENT_2_ID = "69067685"

DEDICATED_CATALOG_ASSIGNMENT_3_NAME = "Automation_CatalogAssignment"
DEDICATED_CATALOG_ASSIGNMENT_3_ID = "69067686"

DEDICATED_CATALOG_ASSIGNMENT_4_NAME = "Catalog Creation"
DEDICATED_CATALOG_ASSIGNMENT_4_ID = "69067691"

DEDICATED_CATALOG_ASSIGNMENT_5_NAME = "CatalogAssignment"
DEDICATED_CATALOG_ASSIGNMENT_5_ID = "69067690"

DEDICATED_CATALOG_ASSIGNMENT_6_NAME = "Automation_Catalog"
DEDICATED_CATALOG_ASSIGNMENT_6_ID = "69067689"

DEDICATED_CATALOG_ASSIGNMENT_7_NAME = "Automation_Catalog_assignment_map_1"
DEDICATED_CATALOG_ASSIGNMENT_7_ID = "69067688"

DEDICATED_CATALOG_ASSIGNMENT_8_NAME = "Catalog_Assignment_For_Editbasedata"
DEDICATED_CATALOG_ASSIGNMENT_8_ID = "69067692"

DEDICATED_CATALOG_ASSIGNMENT_9_NAME = "Automation_Flight_CatalogAssignment"
DEDICATED_CATALOG_ASSIGNMENT_9_ID = "69067687"

# DEDICATED_CATALOG_ASSIGNMENT_1_NAME = "Automation_Assignment_1"
# DEDICATED_CATALOG_ASSIGNMENT_1_ID = "69007491"

# DEDICATED_CATALOG_ASSIGNMENT_2_NAME = "new catalog"
# DEDICATED_CATALOG_ASSIGNMENT_2_ID = "69005221"

# DEDICATED_CATALOG_ASSIGNMENT_3_NAME = "Automation_CatalogAssignment"
# DEDICATED_CATALOG_ASSIGNMENT_3_ID = "69005236"

# DEDICATED_CATALOG_ASSIGNMENT_4_NAME = "Catalog Creation"
# DEDICATED_CATALOG_ASSIGNMENT_4_ID = "69005328"

# DEDICATED_CATALOG_ASSIGNMENT_5_NAME = "Automation_Assignment"
# DEDICATED_CATALOG_ASSIGNMENT_5_ID = "69005243"

# DEDICATED_CATALOG_ASSIGNMENT_7_NAME = "Automation_Catalog_assignment_map"
# DEDICATED_CATALOG_ASSIGNMENT_7_ID = "69013107"

# DEDICATED_CATALOG_ASSIGNMENT_8_NAME = "Catalog_Assignment_For_Editbasedata"
# DEDICATED_CATALOG_ASSIGNMENT_8_ID = "69014994"

# DEDICATED_CATALOG_ASSIGNMENT_6_NAME = "Automation_Catalog"
# DEDICATED_CATALOG_ASSIGNMENT_6_ID = "69005239"

# DEDICATED_CATALOG_ASSIGNMENT_9_NAME = "Automation_Flight_CatalogAssignment"
# DEDICATED_CATALOG_ASSIGNMENT_9_ID = "69023307"


DEDICATED_ROOT_TYPE_1_NAME = "Automation_Roottype"
DEDICATED_ROOT_TYPE_1_ID = "69005244"

DEDICATED_ROOT_TYPE_2_NAME = "Automation_RootType_1"
DEDICATED_ROOT_TYPE_2_ID = "69066161"

DEDICATED_ROUTE_GROUP_1_NAME = "Automation_Route_Group"
DEDICATED_ROUTE_GROUP_1_ID = "69005237"

DEDICATED_UNPUBLISHED_ROOTTYPE_NAME = "UNPUBLISHED_ROOTTYPE"
DEDICATED_UNPUBLISHED_ROOTTYPE_ID = "69062676"

DEDICATED_UI_TEMPLATE_DIFFERENT_AIRLINE_NAME = "SIA UIT6"
DEDICATED_UI_TEMPLATE_DIFFERENT_AIRLINE_ID = "10629"

DEDICATED_CATALOG_1_NAME = "Automation_Catalog"
DEDICATED_CATALOG_1_ID = "69005231"

DEDICATED_CATALOG_2_NAME = "Automation_Catalog"
DEDICATED_CATALOG_2_ID = "69005239"

DEDICATED_CATALOG_3_NAME = "CASC1"
DEDICATED_CATALOG_3_ID = "83059"

DEDICATED_FLIGHT_1_NAME = "Automation_Flight"
DEDICATED_FLIGHT_1_ID = "69005245"

DEDICATED_SKU_1_NAME = "T2002"
DEDICATED_SKU_1_ID = 69005224

DEDICATED_UI_TEMPLATE_1_NAME = "Automation_UITemplate"
DEDICATED_UI_TEMPLATE_1_ID = "69005246"

DEDICATED_UI_TEMPLATE_2_NAME = "Unpublish_UI_template"
DEDICATED_UI_TEMPLATE_2_ID = "69018887"

DEDICATED_MEAL_CODE_1_NAME = "hbacb"
DEDICATED_MEAL_CODE_1_ID = "69005247"

DEDICATED_MEALCODE_2_NAME = "Automation_mealcode"
DEDICATED_MEALCODE_2_ID = "69005248"

DEDICATED_PRODUCT_1_NAME = "F3017"
DEDICATED_PRODUCT_1_ID = " 69005175"

DEDICATED_PRODUCT_2_NAME = "3036-P4V1"
DEDICATED_PRODUCT_2_ID = "69005227"

DEDICATED_PRODUCT_3_NAME = "cred_product"
DEDICATED_PRODUCT_3_ID = "69005229"

DEDICATED_PRODUCT_4_NAME = "Automation_Perishable"
DEDICATED_PRODUCT_4_ID = "69005235"

DEDICATED_PRODUCT_5_NAME = "varabcd_2"
DEDICATED_PRODUCT_5_ID = "14673"

DEDICATED_PRODUCT_6_NAME = "t3003"
DEDICATED_PRODUCT_6_ID = "69005225"

DEDICATED_PRODUCT_7_NAME = "sku_10"
DEDICATED_PRODUCT_7_ID = "69005339"

DEDICATED_PRODUCT_8_NAME = "unpublish_product"
DEDICATED_PRODUCT_8_ID = "69005249"

DEDICATED_PRODUCT_9_NAME = "Automation_Product"
DEDICATED_PRODUCT_9_ID = "69002810"

DEDICATED_PRODUCT_10_NAME = "product_for_Categorydropdown"
DEDICATED_PRODUCT_10_ID = "69002811"

DEDICATED_PRODUCT_11_NAME = "product_api"
DEDICATED_PRODUCT_11_ID = " 69014484"

DEDICATED_INVALID_SECTOR = "83061"
SECTOR_DIFFERENT_AIRLINE = "226"
FLIGHT_DIFFERENT_AIRLINE = "9716"
PRODUCT_DIFFERENT_STORE = "18580"
PRODUCT_DIFFERENT_STORE_2 = "10618"

DEDICATED_CATEGORY_1_NAME = "Exclusively cooked at home_3"
DEDICATED_CATEGORY_1_ID = 82950

DEDICATED_AIRLINE_CATEGORY_1_NAME = "Exclusively cooked at home_3"
DEDICATED_AIRLINE_CATEGORY_1_ID = "82951"

DEDICATED_AIRLINE_CATEGORY_5_NAME = "Automation_category_1"
DEDICATED_AIRLINE_CATEGORY_5_ID = "69017361"

DEDICATED_UNPUBLISHED_AIRLINE_NAME = "Unpublished_Airline"
DEDICATED_UNPUBLISHED_AIRLINE_ID = 69062168

DEDICATED_UNPUBLISHED_STORE_NAME = "Unpublished_Store"
DEDICATED_UNPUBLISHED_STORE_ID = 69062231
DEDICATED_CATEGORY_2_NAME = "Non-alcoholic"
DEDICATED_CATEGORY_2_ID = "69002814"

DEDICATED_CATEGORY_3_NAME = "Beverages"
DEDICATED_CATEGORY_3_ID = "69002812"

DEDICATED_CATEGORY_4_NAME = "Exclusively cooked at home_7"
DEDICATED_CATEGORY_4_ID = " 69002816"

# done
DEDICATED_CATEGORY_5_NAME = "CredAutomationStoreCategory1"
DEDICATED_CATEGORY_5_ID = "69016949"
# done
DEDICATED_CATEGORY_6_NAME = "CredAutomationStoreCategory2"
DEDICATED_CATEGORY_6_ID = "69016950"

DEDICATED_UNPUBLISH_CATEGORY_NAME = "Unpublish_airline_category"
DEDICATED_UNPUBLISH_CATEGORY_ID = "69043643"

DEDICATED_AIRLINE_CATEGORY_7_NAME = "Automation_Category"
DEDICATED_AIRLINE_CATEGORY_7_ID = "69066162"

DEDICATED_ORDER_1_NAME = 'Automation_Order_1'
DEDICATED_ORDER_1_ID = '69039476'
DEDICATED_ORDER_1_ORDER_SHIPMENT_ID = '69039478'
DEDICATED_ORDER_1_LINE_ITEM_NAME = 'Demo Lineitem'
DEDICATED_ORDER_1_LINE_ITEM_ID = '69039480'

DEDICATED_ORDER_2_NAME = 'TEST_TRACKINGINFO_1'
DEDICATED_ORDER_2_ID = '69059694'
DEDICATED_ORDER_2_ORDER_SHIPMENT_ID = '69059696'
DEDICATED_ORDER_2_LINE_ITEM_ID = '69059698'

DEDICATED_INVALID_ORDER_NAME = "Automation_without_required_information"
DEDICATED_INVALID_ORDER_ID = "69059307"

DEDICATED_FAILED_ORDER_ID = '69047839'
DEDICATED_FAILED_ORDER_ORDER_SHIPMENT_ID = '69047841'
DEDICATED_FAILED_ORDER_LINE_ITEM_ID = '69047843'

DEDICATED_DIFFERENT_STORE_ORDER_ID = "69013381"
DEDICATED_DIFFERENT_STORE_SHIPMENT_ID = "69013383"
DEDICATED_DIFFERENT_STORE_LINE_ITEM_ID = "69013384"
DEDICATED_DIFFERENT_STORE_INVENTORY_LOCATION_ID = "236"
DEDICATED_INVENTORY_LOCATION_ID = "82945"

DEDICATED_AIRLINE_CATEGORY_4_NAME = "Exclusively cooked at home_7"
DEDICATED_AIRLINE_CATEGORY_4_ID = " 69002817"

DEDICATED_FOLDER_1 = "Exclusively cooked at home_3"
DEDICATED_FOLDER_1_ID = "82954"

DEDICATED_FOLDER_2 = "Non-alcoholic"
DEDICATED_FOLDER_2_ID = "69002823"

DEDICATED_FOLDER_3 = "Meal Code"
DEDICATED_FOLDER_3_ID = "82927"

DEDICATED_FOLDER_4 = "RouteGroup"
DEDICATED_FOLDER_4_ID = 69005238

DEDICATED_FOLDER_5 = "Unpublished Products"
DEDICATED_FOLDER_5_ID = 82939

DEDICATED_STORE_LOCATION_1_NAME = "Default Inventory"
DEDICATED_STORE_LOCATION_1_ID = 82945

DEDICATED_STORE_LOCATION_2_ID = "69059658"

DEDICATED_CLIENT_ID = "i0wl54jayl"
DEDICATED_CLIENT_SECRET = "mppfnim62e19ugx1jegt"

DEDICATED_VARIENT_PARENT_PRODUCT_NAME = "Automation_Config_Variant_Product"
DEDICATED_VARIENT_PARENT_PRODUCT_ID = "69015727"

DEDICATED_OTHER_AIRLINE_SECTOR_NAME = "LAX-NRT"
DEDICATED_OTHER_AIRLINE_SECTOR_ID = "10078"

DEDICATED_FULFILMENT_NAME = "Onboard"
DEDICATED_FULFILMENT_ID = "154"

DEDICATED_AIRLINE_CATEGORY_5_NAME = "Automation_category_1"
DEDICATED_AIRLINE_CATEGORY_5_ID = "69017361"

DEDICATED_CAMPAIGN_NAME = "Campaign12"
DEDICATED_CAMPAIGN_ID = "69024386"
ROOT_TYPE_AIRLINE_FOLDER = 82933
UI_TYPE_AIRLINE_FOLDER = 82932
UI_TEMPLATE_FOLDER = "10630"
ROOT_TYPE_FOLDER = "12512"
AIRPORTS_FOLDER = "198"
USERS_FOLDER = "82947"
AIRLINES_FOLDER = "112"
PAYMENTMETHODS_FOLDER = "1334"
FULFILLMENT_FOLDER = "155"
CATEGORY_FOLDER = "82931"
CRED_AUTOMATION_AIRLINE_CATEGORY_FOLDER = "82931"
STORE_USERS_FOLDER = "82949"
CRED_AUTOMATION_AIRLINE_FOLDER = "82926"
MEAL_CODE_FOLDER_AIRLINE_LOGIN = '82927'
STORE_CATALOG_FOLDER = "82936"
ORDER_FOLDER = '645'
DEDICATED_STORE_IN_AIRLINE = "69005222"
AIRLINEROUTEGOUPNAME = "Dubai_TO_Goa"
AIRCRAFT_FOLDER = "68865"
STORES_FOLDER = "165"
DEDICATED_ORDER_1_NAME = "Automation_Order_1"
DEDICATED_ORDER_1_ID = "69039476"

DEDICATED_ORDER_SHIPMENT_1_NAME = "home_delivery"
DEDICATED_ORDER_SHIPMENT_1_ID = "69039478"

DEDICATED_ONLINE_SHOP_ORDER_ITEM_1_NAME = "Demo Lineitem"
DEDICATED_ONLINE_SHOP_ORDER_ITEM_1_ID = "69039480"

LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_RECEIPT = (
    LOCAL_DOWNLOADED_PATH + "OrderReceipt_"+DEDICATED_ORDER_1_ID+".pdf"
)

DEDICATED_AIRLINE_NO_STORE = "Cred Automation Airline No Store"
DEDICATED_AIRLINE_NO_STORE_ID = "69061162"

DEDICATED_OTHER_AIRLINE_CATEGORY_ID = "69017422"
