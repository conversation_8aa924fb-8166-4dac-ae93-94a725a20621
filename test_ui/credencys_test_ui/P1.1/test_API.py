import logging, os, random, string, requests, json
import credencys_test_ui.credencys_project_configs.utils as utils
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

def test_MKTPL_1356_FulfillmentAPI_001():
    try:
        # GET
        payload = {}
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request(
            "GET", FULFILLMENT_GET_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info(resp_data)

        assert response.status_code == 200 or response.status_code == 201
        assert resp_data["data"]["fulfillments"]
        assert resp_data["data"]["fulfillments"][0]["id"]
        assert resp_data["data"]["fulfillments"][0]["name"]
        try:
            assert resp_data["data"]["fulfillments"][0]["code"] == ""
        except:
            pass
        logging.info(
            f"API respond with 200 status code and it  display following parameters in the"
            f" response:- id, name, code"
        )
        Success_List_Append(
            "test_MKTPL_1356_FulfillmentAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1356_FulfillmentAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1356_FulfillmentAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_1358_CabinclassAPI_001():
    try:
        # GET
        payload = {}
        headers = {
            "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request(
            "GET", CABIN_CLASS_GET_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info(resp_data)

        assert response.status_code == 200 or response.status_code == 201
        assert resp_data["data"]["cabinClasses"][0]["id"]
        assert resp_data["data"]["cabinClasses"][0]["name"]
        assert "code" in resp_data["data"]["cabinClasses"][0]

        logging.info(
            f"API respond with 200 status code and it  display following parameters in the"
            f" response:- id, name, code"
        )
        Success_List_Append(
            "test_MKTPL_1358_CabinclassAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1358_CabinclassAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1358_CabinclassAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_691_AirlineAPI_001():
    try:
        # GET
        payload = {}
        headers = {
            "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request(
            "GET", AIRLINE_GET_URL, headers=headers, data=payload
        )
        data = response.json()
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        # Extract data
        airline_data = data.get("data", {}).get("airline", {})
        assert "id" in airline_data
        assert "icaoCode" in airline_data
        assert "iataCode" in airline_data
        assert "airlineName" in airline_data
        assert "logo" in airline_data
        assert "firstName" in airline_data
        assert "lastName" in airline_data
        assert "company" in airline_data
        assert "addressLine1" in airline_data
        assert "addressLine2" in airline_data
        assert "city" in airline_data
        assert "state" in airline_data
        assert "zipCode" in airline_data
        assert "country" in airline_data
        assert "phone" in airline_data
        assert "email" in airline_data
        assert "description" in airline_data

        logging.info(
            f"API respond with 200 status code and it  display all parameters in the response"
        )
        Success_List_Append(
            "test_MKTPL_691_AirlineAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_691_AirlineAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_691_AirlineAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_304_AircraftAPI_001():
    try:
        # GET
        payload = {}
        headers = {
            "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request(
            "GET", AIRCRAFT_GET_URL, headers=headers, data=payload
        )
        data = response.json()
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        for aircraft in data["data"]["aircrafts"]:
            assert "id" in aircraft
            assert "oemname" in aircraft
            assert "description" in aircraft
            assert "familyName" in aircraft
            assert "modelName" in aircraft
            assert "image" in aircraft

        logging.info(
            f"API respond with 200 status code and it  display all parameters in the response"
        )
        Success_List_Append(
            "test_MKTPL_304_AircraftAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_304_AircraftAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_304_AircraftAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_2278_MealcodeAPI_001_005_009():
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
        response = requests.request("GET", MEAL_CODE_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info("-------GET -------")
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        assert "id" in resp_data["data"]["mealCodes"][0]
        assert "mealCode" in resp_data["data"]["mealCodes"][0]
        logging.info(
            f"API  respond with 200 status code and it should display following parameters in the response:"
            f"- id"
            f"- mealCode"
        )

        response = requests.request(
            "GET", PRODUCT_MEALCODE_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info("-------GET - PRODUCT -------")
        logging.info(resp_data)
        # first_product_id = resp_data["data"]["products"][0]["productId"]
        logging.info(DEDICATED_SKU_1_ID)
        assert response.status_code == 200 or response.status_code == 201
        assert "productId" in resp_data["data"]["products"][0]
        assert "productMealCode" in resp_data["data"]["products"][0]
        logging.info(
            f"API should respond with 200 status code and it should display following parameters in the"
            f" response:"
            f"- productId"
            f"- productMealCode"
        )

        payload = json.dumps(
            [
                {
                    "productId": int(DEDICATED_SKU_1_ID),
                    "productMealCode": [DEDICATED_MEALCODE_2_NAME],
                }
            ]
        )
        response = requests.request(
            "PATCH", UPDATE_MEALCODE_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info("-------PATCH - UPDATE MEAL CODE -------")
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        expected_message = "Meal Code Updated Successfully"
        assert expected_message in response.json()["message"]
        logging.info(
            f"The meal code details of the products updated in the pimcore with all the details provided in"
            f" the API"
        )
        Success_List_Append(
            "test_MKTPL_2278_MealcodeAPI_001_005_009",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2278_MealcodeAPI_001_005_009",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2278_MealcodeAPI_001_005_009",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_2473_RoottypeAPI_001():
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
        response = requests.request("GET", ROOT_TYPE_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info("-------GET-------")
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        for item in resp_data["data"]["rootTypes"]:
            assert "id" in item
            assert "rootTypeName" in item
            assert "description" in item

        logging.info(
            f"API respond with 200 status code and it should display following parameters in the response:"
            f"- id"
            f"- rootTypeName"
            f"- description"
        )

        Success_List_Append(
            "test_MKTPL_2473_RoottypeAPI_001", "Verify the GET RootType API", "Pass"
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2473_RoottypeAPI_001", "Verify the GET RootType API", "Fail"
        )
        Failure_Cause_Append(
            "test_MKTPL_2473_RoottypeAPI_001", "Verify the GET RootType API", e
        )
        raise e

def test_MKTPL_2429_2430_CatalogassignmentAPI_001_002_003():
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
        ROUTE_CATALOG_ID = ROUTE_CATALOG + DEDICATED_CATALOG_2_ID
        logging.info(ROUTE_CATALOG_ID)
        response = requests.request(
            "GET", ROUTE_CATALOG_ID, headers=headers, data=payload
        )
        data = response.json()
        logging.info("-------GET BY ID SECTOR-------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        catalogs_assignments = data.get("data", {}).get("catalogsAssignments", {})
        sector_info = catalogs_assignments.get("sector", [{}])[0]
        # logging.info(sector_info)
        first_id = sector_info["id"]
        first_id = str(first_id)
        logging.info(first_id)

        expected_parameters = {
            "id": DEDICATED_SECTOR_1_ID,
            "sectorName": DEDICATED_SECTOR_1_NAME,
            "summary": None,
            "routeGroup": "Automation_Route_Group",
            "origin": "OMDB",
            "destination": "OMAL",
            "distance": " Miles",
            "airline": "Cred Automation Airline",
            "flights": [
                8468,
                8766,
                8767,
                8768,
                8769,
                8770,
                8771,
                8773,
                8774,
                8775,
                8869,
                8870,
                8871,
                8930,
                8965,
                9080,
                9260,
            ],
        }
        assert DEDICATED_SECTOR_1_ID in first_id
        logging.info(
            "API should respond with 200 status code and it should display following parameters in the response:"
            "- id"
            "- sectorName"
            "- summary"
            "- routeGroup"
            "- origin"
            "- destination"
            "- distance"
            "- airline"
            "- flights"
        )

        ROUTE_GROUP = ROUTE_CATALOG + DEDICATED_CATALOG_ASSIGNMENT_5_ID
        response = requests.request("GET", ROUTE_GROUP, headers=headers, data=payload)
        data = response.json()
        logging.info("-------GET ID ROUTE GROUP-------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        route_group_info = (
            data.get("data", {}).get("catalogsAssignments", {}).get("routeGroup", {})
        )

        # Define the expected parameters and their values
        expected_parameters = [
            {"sectorId": DEDICATED_SECTOR_1_ID, "flight": [], "excluded": False}
        ]

        for expected_value in enumerate(expected_parameters):
            for i in route_group_info:
                if i in expected_value:
                    actual_value = route_group_info.get("sector", [])[i]
                    assert actual_value == expected_value
        logging.info(
            f"API respond with 200 status code and it should display following parameters in the response:"
            f"- sectorId"
            f"- flight"
            f"- excluded"
        )

        FLIGHT_GROUP = ROUTE_CATALOG + DEDICATED_CATALOG_ASSIGNMENT_9_ID
        response = requests.request("GET", FLIGHT_GROUP, headers=headers, data=payload)
        data = response.json()
        logging.info("-------GET ID FLIGHT-------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        assert "data" in data
        data = data["data"]
        catalogs_assignments = data.get("catalogsAssignments")
        assert "id" in catalogs_assignments
        flights = catalogs_assignments.get("flights")
        for flight in flights:
            assert "id" in flight
            assert "flightRouteNumber" in flight
            assert "sector" in flight
        logging.info(
            "API should respond with 200 status code and it should display following parameters in the response:"
            "- id"
            "- flightRouteNumber"
            "- sector"
        )
        Success_List_Append(
            "test_MKTPL_2429_2430_CatalogassignmentAPI_001_002_003",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2429_2430_CatalogassignmentAPI_001_002_003",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2429_2430_CatalogassignmentAPI_001_002_003",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_478_CategoryAPI_001():
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}
        response = requests.request(
            "GET", CATEGORIES_URL, headers=headers, data=payload
        )
        data = response.json()
        logging.info("-------GET CATEGORIES -------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        for category in data["data"]["categories"]:
            assert "id" in category
            assert "name" in category
            assert "description" in category
            assert "shortDescription" in category
            assert "disclaimer" in category
            assert "url" in category
            assert "publishedAt" in category
            assert "images" in category
            assert "bannerImage5_1" in category
            assert "attributes" in category
            assert "attributesets" in category["attributes"]
            assert "variantThemes" in category["attributes"]
            assert "subCategory" in category

        logging.info(
            "API should respond with 200 status code and it should display following parameters in the response:"
            "- id"
            "- name"
            "- description"
            "- shortDescription"
            "- disclaimer"
            "- url"
            "- publishedAt"
            "- image"
            "- bannerImage"
            "- attributesets"
            "- variantThemes"
            "- subCategory"
        )
        Success_List_Append(
            "test_MKTPL_478_CategoryAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_478_CategoryAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_478_CategoryAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_1675_CatalogAssignmentAPI_001():
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
        response = requests.request(
            "GET", ROUTE_CATALOG + "?offset=2", headers=headers, data=payload
        )
        data = response.json()
        logging.info("-------GET-------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        for assignment in data["data"]["catalogsAssignments"]:
            logging.info(f"Assignment ID: {assignment['id']}")
            logging.info(f"Catalog ID: {assignment['catalog']['id']}")
            logging.info(f"Catalog Name: {assignment['catalog']['name']}")
            logging.info(f"Products: {assignment['catalog']['products']}")

            if "assignmentType" in assignment:
                logging.info(f"Assignment Type: {assignment['assignmentType']}")

            if "catalogFromDate" in assignment:
                logging.info(f"Catalog From Date: {assignment['catalogFromDate']}")

            if "catalogToDate" in assignment:
                logging.info(f"Catalog To Date: {assignment['catalogToDate']}")

            if "cabinClass" in assignment:
                logging.info(f"Cabin Class: {assignment['cabinClass']}")

            if "routeGroup" in assignment:
                logging.info(f"Route Group: {assignment['routeGroup']['name']}")
                logging.info(f"Sectors: {assignment['routeGroup']['sector']}")

                # Loop through sectors and print flights if available
                for sector in assignment["routeGroup"]["sector"]:
                    logging.info(f"Sector ID: {sector['sectorId']}")
                    if "flight" in sector:
                        logging.info(f"Flights: {sector['flight']}")

        logging.info(
            "API should respond with 200 status code and it should display following parameters in the response:"
            "- id"
            "- catalog id, name and products "
            "- assignmentType"
            "- catalogFromDate"
            "- catalogToDate"
            "- cabinClass"
            "- sector"
            "- routeGroup"
            "- flights"
        )
        Success_List_Append(
            "test_MKTPL_1675_CatalogAssignmentAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1675_CatalogAssignmentAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1675_CatalogAssignmentAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_1264_TaxpercentageAPI_001_007():
    random_float = random.uniform(10.32, 11.34)
    random_float_rounded = round(random_float, 2)
    logging.info(random_float_rounded)
    try:
        payload = json.dumps({"taxPercentage": random_float_rounded})
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request("POST", TAX_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(resp_data["message"])
        assert resp_data["message"] in "Tax Percentage updated successfully"
        logging.info(f"The tax details updated in the store")
        # GET
        payload = {}
        response = requests.request("GET", STORE_GET_URL, headers=headers, data=payload)
        data = response.json()
        assert response.status_code == 200 or response.status_code == 201
        # Extract data
        store_data = data.get("data", {}).get("store", {})
        logging.info(data["data"]["store"]["taxPercentage"])
        assert "taxPercentage" in store_data
        assert data["data"]["store"]["taxPercentage"] == random_float_rounded
        logging.info(
            f"API  respond with 200 status code and it should display following parameters in the"
            f" response:"
            f"- Tax percentage"
        )

        Success_List_Append(
            "test_MKTPL_1264_TaxpercentageAPI_001_007",
            "Verify the newly added parameter in the response",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1264_TaxpercentageAPI_001_007",
            "Verify the newly added parameter in the response",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1264_TaxpercentageAPI_001_007",
            "Verify the newly added parameter in the response",
            e,
        )
        raise e

def test_MKTPL_2225_UITemplateAPI_001():
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
        response = requests.request(
            "GET", UI_TEMPLATE_URL, headers=headers, data=payload
        )
        data = response.json()
        logging.info("-------GET-------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        assert "data" in data, "Response does not contain 'data' key"
        ui_templates = data["data"].get("uiTemplates", [])
        for template in ui_templates:
            assert "id" in template
            assert "templateName" in template
        logging.info(
            "API should respond with 200 status code and it should display following parameters in the response:"
            "- id"
            "- templateName"
        )
        Success_List_Append(
            "test_MKTPL_2225_UITemplateAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2225_UITemplateAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2225_UITemplateAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_310_AirportAPI_001():
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
        response = requests.request("GET", AIRPORTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info("-------GET AIRPOTS -------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        for airport in data["data"]["airports"]:
            assert "id" in airport
            assert "airportName" in airport
            assert "icaoCode" in airport
            assert "iataCode" in airport
            assert "address" in airport
            assert "city" in airport
            assert "state" in airport
            assert "zipCode" in airport
            assert "country" in airport

        logging.info(
            "API should respond with 200 status code and it should display following parameters in the response:"
            "- id"
            "- airportName"
            "- ICAOCode"
            "- IATACode"
            "- address"
            "- city"
            "- state"
            "- zipCode"
            "- country"
            "- timeZone"
        )
        Success_List_Append(
            "test_MKTPL_310_AirportAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_310_AirportAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_310_AirportAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_3763_Pricezero_004_005():
    try:
        payload = json.dumps(
            {
                "catalog": {
                    "id": int(DEDICATED_CATALOG_1_ID),
                    "products": [
                        {"productId": int(DEDICATED_SKU_1_ID), "productPrice": 0}
                    ],
                }
            }
        )
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request(
            "PUT", UPDATE_PRICE_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info(resp_data)
        logging.info(response.reason)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API responded with 200 status code")
        resp_data = response.json()
        logging.info(resp_data)
        expected_message = "Price Updated successfully. SNS message sent successfully"
        assert expected_message in response.json()["message"]
        logging.info(f"Price=0 is allowed from API in catalog assignment")
        # GET
        response = requests.request("GET", GET_PRICE, headers=headers, data=payload)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API responded with 200 status code")
        resp_data = response.json()
        logging.info(resp_data)
        product_price = None
        for product in resp_data["data"]["catalogsAssignments"]["catalog"]["products"]:
            if str(product["productId"]) == DEDICATED_SKU_1_ID:
                product_price = product["productPrice"]
                break  # Stop iterating once the product is found
        # product_price = resp_data["data"]["catalogsAssignments"]["catalog"]["products"][0]["productPrice"]
        logging.info(product_price)
        assert product_price == "" or product_price == 0
        logging.info("Price=0  display in GET catalog assignment API")
        # Change price for next time
        payload = json.dumps(
            {
                "catalog": {
                    "id": int(DEDICATED_CATALOG_1_ID),
                    "products": [
                        {"productId": int(DEDICATED_SKU_1_ID), "productPrice": 233}
                    ],
                }
            }
        )
        response = requests.request(
            "PUT", UPDATE_PRICE_URL, headers=headers, data=payload
        )
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API responded with 200 status code")
        Success_List_Append(
            "test_MKTPL_3763_Pricezero_004_005",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3763_Pricezero_004_005",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3763_Pricezero_004_005",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_1253_InventoryCheckAPIairside_001():
    try:
        # Add
        payload = json.dumps(
            {
                "home": {
                    "icaoCode": "",
                    "items": [{"vendorProductVariantId": "72372", "quantity": 25}],
                },
                "inflightFutureStandard": {
                    "icaoCode": "",
                    "items": [{"vendorProductVariantId": "123", "quantity": 5}],
                },
                "inflightFutureExpress": {
                    "icaoCode": "",
                    "items": [{"vendorProductVariantId": "123", "quantity": 5}],
                },
            }
        )
        headers = {"Authorization": ORDER_JWT_TOKEN, "Content-Type": "application/json"}

        response = requests.request(
            "POST", INVENTORY_CHECK_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info("------ADD------")
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API for Airlinecategory responded with 200 status code")
        logging.info(
            f"The Front end category created in the pimcore with all the details provide"
        )

        logging.info(
            f"API should respond with 200 status code and it should display following parameters in the response:"
            f"- icaoCode"
            f"- vendorProductVariantId"
            f"- status"
            f"- numAvailable"
            f"- numRequested"
            f"- error_code"
            f"- text_code"
            f"- vendorProductVariantId"
            f"- error_message"
        )
        Success_List_Append(
            "test_MKTPL_1253_InventoryCheckAPIairside_001",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1253_InventoryCheckAPIairside_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1253_InventoryCheckAPIairside_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_2504_Unpublishedproduct_008_009():
    try:
        # GET
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}
        response = requests.request(
            "GET", UNPUBLISHED_PRODUCT, headers=headers, data=payload
        )
        data = response.json()
        logging.info("-------GET-------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info("GET ALL Product API return unpublished product")
        id = data["data"]["products"][0]["id"]
        # GET BY ID
        UNPUBLISHED_PRODUCT_BY_ID = (
            PAC_API_GATEWAY_URL
            + "marketplace/v1/pim/product?offset=1&ids="+str(id)+"&unpublished=true"
        )
        payload = {}
        response = requests.request(
            "GET", UNPUBLISHED_PRODUCT_BY_ID, headers=headers, data=payload
        )
        data_ID = response.json()
        logging.info("-------GET BY ID-------")
        logging.info(data_ID)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(
            "GET BY ID Product API return unpublished product in response when we pass unpublished product ID in URL"
        )
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_008_009",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_008_009",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_008_009",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_2039_CatalogAssignmentAPI_001():
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
        response = requests.request(
            "GET", ROUTE_CATALOG + "?offset=2", headers=headers, data=payload
        )
        data = response.json()
        logging.info("-------GET-------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        assert "data" in data, "Response does not contain 'data' key"
        assert "data" in data, "Response does not contain 'data' key"
        assignments = data["data"].get("catalogsAssignments", [])
        for assignment in assignments:
            assert "workflowStatus" in assignment
        logging.info(
            "API should respond with 200 status code and it should display following parameters in the response:"
            "- workflowStatus"
        )
        Success_List_Append(
            "test_MKTPL_2039_CatalogAssignmentAPI_001",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2039_CatalogAssignmentAPI_001",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2039_CatalogAssignmentAPI_001",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_3036_Associatemultiplecategories_003():
    try:
        payload = json.dumps(
            {
                "sku": DEDICATED_PRODUCT_2_NAME,
                "name": DEDICATED_PRODUCT_2_NAME,
                "shortDescription": "<ol>\n\t<li>this is</li>\n</ol>\n\n<p>shortDescription</p>\n\n<p>ofproduct</p>\n",
                "description": "<ul>\n\t<li>this is</li>\n</ul>\n\n<p>Description<br />\nofproduct</p>\n",
                "barcode": "TCGDA0011",
                "deliveryMethod": ["Next Flight", "Gate Pick Up", "Onboard"],
                "category": [
                    int(DEDICATED_CATEGORY_1_ID),
                    int(DEDICATED_CATEGORY_2_ID),
                ],
                "ageVerificationRequire": True,
                "isAvailableToSell": False,
                "productSet": "Simple",
                "storeProductId": "",
                "productType": "Duty Free",
            }
        )
        headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}
        response = requests.request(
            "PUT", PRODUCT_UPDATE_CATEGORIES, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info("-------PUT-------")
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(
            f"Users is able to add multiple categories in the Category parameter"
        )
        Success_List_Append(
            "test_MKTPL_3036_Associatemultiplecategories_003",
            "Verify the GET Store API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3036_Associatemultiplecategories_003",
            "Verify the GET Store API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3036_Associatemultiplecategories_003",
            "Verify the GET Store API",
            e,
        )
        raise e

def test_MKTPL_1103_Leadtimeproduct_016_MKTPL_5835_ProductAPI_001():
    global RANDOM_NAME
    RANDOM_NAME = "".join(random.choices(string.ascii_letters, k=7))
    logging.info(RANDOM_NAME)
    try:
        # ADD
        payload = json.dumps({
            "sku": RANDOM_NAME,
            "name": RANDOM_NAME,
            "shortDescription": "<ol>\n\t<li>this is</li>\n</ol>\n\n<p>shortDescription</p>\n\n<p>ofproduct</p>\n",
            "description": "<ul>\n\t<li>this is</li>\n</ul>\n\n<p>Description<br />\nofproduct</p>\n",
            "barcode": "TCGDA0011",
            "deliveryMethod": [
                "Next Flight",
                "Gate Pick Up",
                "Onboard"
            ],
            "nextFlightLeadTime": 1,
            "gatePickupLeadTime": 889,
            "category": [
                int(DEDICATED_CATEGORY_1_ID)
            ],
            "shippingAttributes": {
                "weight": 0,
                "weightUnit": "",
                "height": 10,
                "heightUnit": "IN",
                "width": 10,
                "widthUnit": "IN",
                "length": 10,
                "lengthUnit": "IN"
            },
            "PriceTaxation": {
                "price": [
                    {
                        "value": 0,
                        "currency": "USD"
                    }
                ],
                "specialPrice": [
                    {
                        "value": 190,
                        "currency": "USD"
                    }
                ],
                # "cost": [
                #     {
                #         "value": 910,
                #         "currency": "USD"
                #     }
                # ],
                "isTaxable": True,
                "orderMaxQuantityAllowed": 300,
                "orderMinQuantityAllowed": 10
            },
            "brand": "Spark1",
            "ageVerificationRequire": True,
            "isAvailableToSell": False,
            "Attributes": {
                "storeSpecificAttributes": [
                    {
                        "key": "",
                        "value": ""
                    }
                ],
                "productCategoryAttributes": [
                    {
                        "key": "Display",
                        "value": "20"
                    }
                ]
            },
            "notApplicableCountries": [
                "IN",
                "US"
            ],
            "productSet": "Simple",
            # "storeProductId": int(DEDICATED_STORE_ID),
            "productType": "Duty Free",
            "spotLight": True,
            "isPerishable": False,
            "requireShipping": True
        })
        headers = {
            'language': 'en',
            'Authorization': CRED_AUTOMATION_STORE_TOKEN,
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        data = response.json()
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        expected_message = "Product added successfully. "
        assert expected_message in response.json()['message']
        logging.info("In API added Next Flight,Gate Pick Up in delivery type and added value in Next Flight Lead Time,"
                     "Gate Pick Up Time")
        id_product = data['id']
        #test_MKTPL_5835_ProductAPI_001():
        response = requests.request("POST", PRODUCTS_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 400
        logging.info(
            f"POST API for Product responded with 400 status code")
        assert "A product already exists with same sku and store." in resp_data["message"]
        assert resp_data["id"] == id_product
        logging.info(f"Product Id- {id_product}")
        url_id = PRODUCTS_URL + str(id_product)
        logging.info(url_id)
        # utils.Delete
        payload = {}
        headers = {
            'language': 'en',
            'Authorization': CRED_AUTOMATION_STORE_TOKEN,
            'Content-Type': 'application/json'
        }
        response = requests.request("Delete", url_id, headers=headers, data=payload)
        resp_data = response.json()
        logging.info("-------utils.Delete-------")
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"Data cleaned up successfully.")
        Success_List_Append("test_MKTPL_1103_Leadtimeproduct_016_MKTPL_5835_ProductAPI_001",
                            "Check that in API add Next Flight,Gate Pick Up in delivery type and add value in Next "
                            "Flight Lead Time,Gate Pick Up Time", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1103_Leadtimeproduct_016_MKTPL_5835_ProductAPI_001",
                            "Check that in API add Next Flight,Gate Pick Up in delivery type and add value in Next"
                            " Flight Lead Time,Gate Pick Up Time", "Fail")
        Failure_Cause_Append("test_MKTPL_1103_Leadtimeproduct_016_MKTPL_5835_ProductAPI_001",
                             "Check that in API add Next Flight,Gate Pick Up in delivery type and add value in Next"
                             " Flight Lead Time,Gate Pick Up Time", e)
        raise e

def test_MKTPL_2504_Unpublishedproduct_010():
    RANDOM_SKU = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    logging.info(RANDOM_SKU)
    try:
        product_id = utils.create_Simple_Product("Onboard", "Duty Free", RANDOM_SKU)
        logging.info(f"Product Id- {product_id}")
        assert product_id != None
        # product_id = f"UID {product_id}"
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        # ----------------------------utils.Delete THE PRODUCT------------------------------
        # status_code = utils.Delete(product_id, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
        # logging.info(status_code)
        # assert status_code == 200 or status_code == 201
        response = requests.request(
            "DELETE", PRODUCTS_URL + str(product_id), headers=headers, data={}
        )
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"Product {product_id} has been Deleted")

        # ----------------------------PUBLISH THE PRODUCT-------------------------------

        # product_id = (product_id.split())[1]
        # logging.info(product_id)

        payload = json.dumps({"products": [int(product_id)]})
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }

        response = requests.request(
            "POST", PUBLISH_PRODUCT, headers=headers, data=payload
        )
        logging.info(response.text)
        assert response.status_code == 200 or response.status_code == 201

        logging.info(f"Product Id- {product_id} has been published")

        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_010",
            "Verify if the mandatory parameters are filled and non-mandatory parameters are empty in the request",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_010",
            "Verify if the mandatory parameters are filled and non-mandatory parameters are empty in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_010",
            "Verify if the mandatory parameters are filled and non-mandatory parameters are empty in the request",
            e,
        )
        raise e

def test_MKTPL_579_APIAuthentication_001():
    try:
        payload = json.dumps(
            {"clientId": DEDICATED_CLIENT_ID, "clientSecret": DEDICATED_CLIENT_SECRET}
        )
        headers = {"Content-Type": "application/json"}

        response = requests.request(
            "POST", AUTHENTICATION_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info("------AUTH------")
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API for Authentication responded with 200 status code")
        Success_List_Append(
            "test_MKTPL_579_APIAuthentication_001",
            "Verify if the mandatory parameters are filled and non-mandatory parameters are empty in the request",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_579_APIAuthentication_001",
            "Verify if the mandatory parameters are filled and non-mandatory parameters are empty in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_579_APIAuthentication_001",
            "Verify if the mandatory parameters are filled and non-mandatory parameters are empty in the request",
            e,
        )
        raise e

def test_MKTPL_3763_Pricezero_009():
    try:
        PRODUCTS_URL_ID_URL = PRODUCTS_URL + DEDICATED_PRODUCT_1_ID
        logging.info(PRODUCTS_URL_ID_URL)
        payload = {}
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }

        response = requests.request(
            "GET", PRODUCTS_URL_ID_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        assert "price" in resp_data["data"]["product"]["PriceTaxation"]
        assert "specialPrice" in resp_data["data"]["product"]["PriceTaxation"]
        assert "cost" in resp_data["data"]["product"]["PriceTaxation"]

        logging.info(f"POST API for Authentication responded with 200 status code")
        Success_List_Append(
            "test_MKTPL_3763_Pricezero_009",
            "Verify the GET product API when Price, special price and cost=0",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3763_Pricezero_009",
            "Verify the GET product API when Price, special price and cost=0",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3763_Pricezero_009",
            "Verify the GET product API when Price, special price and cost=0",
            e,
        )
        raise e
