import logging, os
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
import credencys_test_ui.credencys_project_configs.utils as utils
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append, Skip_List_Append
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import string, random
from random import randint
import time
import csv, requests, json
import pytest
from jamatest import jamatest

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

@pytest.fixture(scope = 'module')
def airline_Login():
    try:
        with utils.services_context_wrapper_optimized(
                    True, None, False
                ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
    except:
        logging.info("Login not happened")
        Skip_List_Append(33)
        assert False

@jamatest.test(11975401)
@jamatest.test(11975402)
def test_MKTPL_1240_Selectmultiplesectorsinflight_008_009(airline_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1240_Selectmultiplesectorsinflight_008_009.png", False
        ) as driver:
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 10)
                .until(
                    EC.presence_of_element_located(
                        (By.ID, xpath.pimcoreMenu_id)
                    )
                )
                .click()
            )
            driver.find_element(By.XPATH, xpath.csvImport).click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//input[@name="importType"]')
                    )
                )
                .send_keys("Flight")
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
                .click()
            )
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE, "r", encoding="utf-8-sig"
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_SAMPLE_FLIGHT_ADMIN == ACTUAL_CSV_DATA
            logging.info("User should be able to Download the Predefined CSV")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(
                LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE
            )
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.upload).click()
            saved_message = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            xpath.csvUploadMessage,
                        )
                    )
                )
                .is_displayed()
            )
            assert saved_message
            logging.info("CSV data should be successfully uploaded in the system")
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE)
            Success_List_Append(
                "test_MKTPL_1240_Selectmultiplesectorsinflight_008_009",
                "Verify by clicking on the Download Sample file of flight button",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1240_Selectmultiplesectorsinflight_008_009",
            "Verify by clicking on the Download Sample file of flight button",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1240_Selectmultiplesectorsinflight_008_009",
            "Verify by clicking on the Download Sample file of flight button",
            e,
        )
        raise e

@jamatest.test(14966775)
def test_MKTPL_1240_Selectmultiplesectorsinflight_003(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_NUMBER = "".join([str(randint(0, 9)) for i in range(7)])
    logging.info(RANDOM_NAME)
    try:
       with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1240_Selectmultiplesectorsinflight_003.png", False
        ) as driver:

            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Airline Operation"]/../../../../..//span[text()="'
                            + DEDICATED_AIRLINE
                            + '"])',
                        )
                    )
                )
            )
            sector = driver.find_element(
                By.XPATH,
                '(//div[text()="Airline Operation"]/../../../../..//span[text()="'
                + DEDICATED_AIRLINE
                + '"])',
            )

            action = ActionChains(driver)
            action.context_click(sector).perform()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "(//span[text()='Refresh'])"))
            )
            driver.find_element(By.XPATH, "(//span[text()='Refresh'])").click()
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Airline Operation"]/../../../../..//span[text()="'
                            + DEDICATED_AIRLINE
                            + '"])',
                        )
                    )
                )
            )
            sector = driver.find_element(
                By.XPATH,
                '(//div[text()="Airline Operation"]/../../../../..//span[text()="'
                + DEDICATED_AIRLINE
                + '"])',
            )

            action = ActionChains(driver)
            action.context_click(sector).perform()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            flight = driver.find_element(
                By.XPATH,
                '//span[text()="Flight" and @class = "x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator"]',
            )
            flight.click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.flightRouteNumberFlightFieldXpath)
                    )
                )
                .send_keys(RANDOM_NUMBER)
            )
            driver.find_element(
                By.XPATH, '(//input[@title="Expected date format Y-m-d."])[1]'
            ).send_keys("2023-08-24")
            driver.find_element(
                By.XPATH,
                '(//span[text()="Flight Begin Date Time "]//parent::span//parent::label//'
                "parent::div//input)[2]",
            ).send_keys("01:45")
            driver.find_element(
                By.XPATH, '(//input[@title="Expected date format Y-m-d."])[2]'
            ).send_keys("2023-08-26")
            driver.find_element(
                By.XPATH,
                '(//span[text()="Flight End Date Time "]//parent::span//parent::label//'
                "parent::div//input)[2]",
            ).send_keys("4:45")
            driver.find_element(
                By.XPATH, '//input[@name="fulfillmentCutOffTimeLimit"]'
            ).send_keys("1")
            time.sleep(2)
            driver.find_element(
                By.XPATH, '//div[text()="Sectors"]/../../../../..//a'
            ).click()
            WebDriverWait(driver, 10).until(EC.visibility_of_element_located((By.XPATH, '//input[@name="sector"]')))
            driver.find_element(By.XPATH, '//input[@name="sector"]').send_keys(
                DEDICATED_SECTOR_1_NAME
            )
            (WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//li[contains(text(),'"+DEDICATED_SECTOR_1_NAME+"')]")
                    )
                )
                .click())
            driver.find_element(By.XPATH, '//input[@name="sector_sequence"]').send_keys(
                "1"
            )
            UID = utils.get_uid(driver)
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info("Flight successfully created/updated in the system")
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Flight", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
                )
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/'
                            + DEDICATED_AIRLINE
                            + "/Flight/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert published
            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_AIRLINE_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

            logging.info(
                "Flight object  move automatically under Airline Operation section/Flight folder"
            )
            Success_List_Append(
                "test_MKTPL_1240_Selectmultiplesectorsinflight_003",
                "Verify by entering/updating data " "in below fields",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1240_Selectmultiplesectorsinflight_003",
            "Verify by entering/updating data in " "below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1240_Selectmultiplesectorsinflight_003",
            "Verify by entering/updating data in" " below fields",
            e,
        )
        raise e

@jamatest.test(14966775)
def test_MKTPL_2219_AssignUITemplate_003(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_DESCRIPTION = "".join(random.choices(string.ascii_letters, k=15))

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2219_AssignUITemplate_003.png", False
        ) as driver:

            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "no", None)
            # utils.wait_for_style_attribute(driver, 50)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Category Management"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                        )
                    )
                )
                .click()
            )
            category = driver.find_element(By.XPATH, '//span[text()="Category"]')
            action = ActionChains(driver)
            action.context_click(category).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(airline).perform()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//span[text()="AirlineCategory"])[1]')
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "name")))
                .send_keys(RANDOM_NAME)
            )
            driver.find_element(By.NAME, "disclaimer").send_keys(RANDOM_DESCRIPTION)
            driver.find_element(By.NAME, "shortDescription").send_keys(
                RANDOM_DESCRIPTION
            )
            driver.find_element(By.NAME, "description").send_keys(RANDOM_DESCRIPTION)
            driver.find_element(By.NAME, "url").send_keys(RANDOM_DESCRIPTION)
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 50).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_UI_TEMPLATE_1_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, '//div[text()="' + DEDICATED_UI_TEMPLATE_1_ID + '"]'
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a UI Template")
            UID = utils.get_uid(driver)
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Airline Category should be successfully created/updated in the system = {saved}"
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Category Management"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                        )
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//span[text()="Category"]/..//a)[2]')
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="Category"]/..//input[@name="filter"]',
                        )
                    )
                )
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            airline_category = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//span[text()="' + RANDOM_NAME + '"])[1]')
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"Airline Category object should move automatically under Category Management section"
                f" = {airline_category}"
            )

            Success_List_Append(
                "test_MKTPL_2219_AssignUITemplate_003",
                "Verify by entering/updating data in below fields",
                "Pass",
            )
            try:
                RT_VALUE = utils.Delete(
                    UID, AIRLINE_CATEGORY_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2219_AssignUITemplate_003",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2219_AssignUITemplate_003",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

@jamatest.test(11975951)
@jamatest.test(11975952)
@jamatest.test(11975955)
@jamatest.test(11975956)
def test_MKTPL_2336_Assignsequencenumber_001_002_005_006(airline_Login):
    RANDOM_NUMBER = "".join([str(randint(1, 9)) for i in range(1)])

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2336_Assignsequencenumber_001_002_005_006.png", False
        ) as driver:
            
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(
                By.XPATH, xpath.sequencingModuleXpath
            ).click()
            redirection = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[text()="Airline Category Sequencing Module"]')
                    )
                )
                .is_displayed()
            )
            assert redirection
            logging.info(
                f"Admin/manager should redirect to the Airline category Sequencing screen = {redirection}"
            )
            id = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="Id" and @class="x-column-header-text-inner"]',
                        )
                    )
                )
                .is_displayed()
            )
            path = driver.find_element(
                By.XPATH,
                '//span[text()="Path" and @class="x-column-header-text-inner"]',
            ).is_displayed()
            category = driver.find_element(
                By.XPATH,
                '//span[text()="Category" and @class="x-column-header-text-inner"]',
            ).is_displayed()
            parent_category = driver.find_element(
                By.XPATH,
                '//span[text()="Parent Category" and'
                ' @class="x-column-header-text-inner"]',
            ).is_displayed()
            sequence_number = driver.find_element(
                By.XPATH,
                '//span[text()="Sequence Number" and'
                ' @class="x-column-header-text-inner"]',
            ).is_displayed()
            assert [id, path, category, parent_category, sequence_number]
            logging.info(
                f"Admin/manager should be able to View the list of airline category in the grid. "
                f"Following field should display:"
                f"Id = {id}, "
                f"Path = {path},  "
                f"Category= {category},  "
                f"Parent Category = {parent_category}, "
                f"Sequence Number  = {sequence_number}"
            )
            driver.find_element(
                By.XPATH,
                '//span[text()="Sequence Number" and @class="x-column-header-text-inner"]',
            ).click()
            time.sleep(1)
            before_click = driver.find_element(
                By.XPATH,
                '//span[text()="Sequence Number" and'
                ' @class="x-column-header-text-inner"]/../../../../..',
            ).get_attribute("aria-sort")
            logging.info(before_click)
            driver.find_element(
                By.XPATH,
                '//span[text()="Sequence Number" and @class="x-column-header-text-inner"]',
            ).click()
            time.sleep(1)
            after_click = driver.find_element(
                By.XPATH,
                '//span[text()="Sequence Number" and'
                ' @class="x-column-header-text-inner"]/../../../../..',
            ).get_attribute("aria-sort")
            logging.info(after_click)
            assert after_click == "descending" and before_click == "ascending"

            updating_seq_num_category = driver.find_element(
                By.XPATH, '(//div[@class= "x-grid-cell-inner "])[2]'
            ).text
            logging.info(f"Sequence number updating for {updating_seq_num_category}")
            seq_num_field = driver.find_element(
                By.XPATH, '(//div[@class= "x-grid-cell-inner "])[5]'
            )
            time.sleep(3)
            old_seq_num = seq_num_field.text
            logging.info(seq_num_field.text)
            ele = driver.find_element(
                By.XPATH, '//div[text()="' + seq_num_field.text + '"]'
            )
            seq_num_field.click()
            # driver.find_element(By.XPATH, '(//div[@class= "x-grid-cell-inner "])[5]').clear()
            ActionChains(driver).move_to_element(ele).click(seq_num_field).send_keys(
                Keys.BACKSPACE, Keys.BACKSPACE
            ).perform()
            ActionChains(driver).move_to_element(ele).click(seq_num_field).send_keys(
                RANDOM_NUMBER
            ).perform()
            driver.find_element(
                By.XPATH, '(//div[@class= "x-grid-cell-inner "])[2]'
            ).click()
            # seq_num_field.click()
            driver.find_element(By.XPATH, '//span[text()= "Submit"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()= "Airline Category Sequence Updated Successfully"]',
                    )
                )
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[text()= "Category Management"]')
                    )
                )
                .click()
            )
            utils.search_by_id(driver, CATEGORY_FOLDER)
            driver.find_element(By.XPATH, "//span[contains(@class,'reload')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//input[@name="query"]'))
            ).send_keys(updating_seq_num_category, Keys.ENTER)
            time.sleep(3)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        f"//div[text()='/{DEDICATED_AIRLINE}/Category/{updating_seq_num_category}']",
                    )
                )
            )
            open_ele = driver.find_element(
                By.XPATH,
                f"//div[text()='/{DEDICATED_AIRLINE}/Category/{updating_seq_num_category}']",
            )
            action = ActionChains(driver)
            action.context_click(open_ele).perform()
            driver.find_element(By.XPATH, xpath.openXpath).click()
            time.sleep(3)
            try:
                driver.find_element(By.XPATH, "//span[text()='Yes']").click()
            except:
                pass
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'sequenceNumber')))
            updated_seq_num = driver.find_element(
                By.NAME, "sequenceNumber"
            ).get_attribute("value")
            assert str(updated_seq_num) in str(old_seq_num) + str(RANDOM_NUMBER)
            logging.info(
                "Admin/manager can assign sequence number to the airline category successfully"
            )
            Success_List_Append(
                "test_MKTPL_2336_Assignsequencenumber_001_002_005_006",
                "Verify by clicking on the 'Sequencing Module' icon at left side bar",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2336_Assignsequencenumber_001_002_005_006",
            "Verify by clicking on the 'Sequencing Module' icon at left side bar",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2336_Assignsequencenumber_001_002_005_006",
            "Verify by clicking on the 'Sequencing Module' icon at left side bar",
            e,
        )
        raise e

@jamatest.test(11976061)
@jamatest.test(11976062)
@jamatest.test(11976063)
@jamatest.test(11976068)
@jamatest.test(11976295)
def test_MKTPL_303_Routegroupimport_011_012_013_18_MKTPL_356_Login_001(airline_Login):
    ACTUAL_CSV_DATA = []

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_303_Routegroupimport_011_012_013_18_MKTPL_356_Login_001.png", False
        ) as driver:
            
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            redirect = store.is_displayed()
            logging.info(redirect)
            assert redirect
            logging.info("User is navigate to the CSV import screen")
            store.send_keys("RouteGroup")
            time.sleep(1)
            store.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
            )
            download_button = driver.find_element(By.ID, xpath.downloadCsvButtonId)
            download_button.is_enabled()
            logging.info(download_button)
            assert download_button
            logging.info(
                "Download Sample File Of RouteGroup button is enabled besides import data type dropdown"
            )
            driver.find_element(
                By.XPATH, '//span[@id="download_CSV_button-btnEl"]'
            ).click()
            time.sleep(2)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_ROUTEGROUP_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_ROUTEGROUP_SAMPLE,
                "r",
                encoding="utf-8-sig",
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_SAMPLE_AIRLINE_ROUTEGROUP == ACTUAL_CSV_DATA
            logging.info(
                f"CSV file should be download on the system and sample records should display in CSV"
            )
            time.sleep(2)
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_ROUTEGROUP_SAMPLE)
            logging.info("Removed")
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )

            utils.update_csv("AirlineRouteGroup_Sample.csv", "name_en", RANDOM_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("AirlineRouteGroup_Sample.csv", file_path)
            assert saved_message
            logging.info(
                f"RouteGroup should be successfully imported in the system {saved_message}"
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("RouteGroup", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
                )
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                RANDOM_NAME, Keys.ENTER
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f'(//div[text()="' + RANDOM_NAME + '"])[1]')
                )
            )
            logging.info(
                "RouteGroup object move automatically under Airline Operation section/RouteGroup folder"
            )
            UID = utils.get_uid(driver)
            try:
                RT_VALUE = utils.Delete(UID, CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL)
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            logging.info(
                f"RouteGroup object should move automatically under Airline Operation section/RouteGroup folder"
            )
            Success_List_Append(
                "test_MKTPL_303_Routegroupimport_011_012_013_18_MKTPL_356_Login_001",
                "Verify by clicking on the Download Sample file of RouteGroup button",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_303_Routegroupimport_011_012_013_18_MKTPL_356_Login_001",
            "Verify by clicking on the Download Sample file of RouteGroup button",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_303_Routegroupimport_011_012_013_18_MKTPL_356_Login_001",
            "Verify by clicking on the Download Sample file of RouteGroup button",
            e,
        )
        raise e

@jamatest.test(11977712)
@jamatest.test(11977713)
@jamatest.test(11977714)
@jamatest.test(11977716)
@jamatest.test(11975970)
@jamatest.test(11975971)
@jamatest.test(11975972)
@jamatest.test(11975974)
def test_MKTPL_2471_AssignRoottype_001_002_003_005_MKTPL_2379_Assignsequencenumber_001_002_003_005(airline_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2471_AssignRoottype_001_002_003_005_MKTPL_2379_Assignsequencenumber_001_002_003_005.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.revert_auto_populate_auto_map_airline_categories(driver)
            # utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "no", None)
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            import_screen = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.ID, "csv_import_form_fieldset-legendTitle")
                    )
                )
                .is_displayed()
            )
            assert import_screen
            logging.info(
                f"User should navigate to the CSV import screen = {import_screen}"
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "importType")))
                .send_keys("AirlineCategory")
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "importType")))
                .send_keys(Keys.ENTER)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
                .click()
            )
            logging.info(
                "Download Sample file of AirlineCategory button should be enabled besides import data type"
            )
            
            time.sleep(6)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE,
                "r",
                encoding="utf-8-sig",
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_SAMPLE_AIRLINE == ACTUAL_CSV_DATA
            logging.info(
                f"CSV file should be download on the system and sample records should display in CSV"
            )

            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE)

            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            # UPDATE Airline category name, UITemplate, Root type in CSV
            utils.update_csv("Airline_Category_Sample.csv", "name_en", RANDOM_NAME)
            utils.update_csv(
                "Airline_Category_Sample.csv",
                "UITemplate",
                DEDICATED_UI_TEMPLATE_1_NAME,
            )  # eeeee
            utils.update_csv(
                "Airline_Category_Sample.csv", "rootType", DEDICATED_ROOT_TYPE_1_NAME
            )  # BjsJYkB

            # Upload Airline Category Updated CSV
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Airline_Category_Sample.csv", file_path)
            assert saved_message
            logging.info(
                f"Airline Category should be successfully imported in the system = {saved_message}"
            )
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, CATEGORY_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
                )
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//div[contains(text(),"' + RANDOM_NAME + '")])[1]')
                )
            )
            logging.info(
                "Airline Category object  move automatically under Category Management section"
            )
            cat = driver.find_element(
                By.XPATH, '(//div[contains(text(),"' + RANDOM_NAME + '")])[1]'
            )
            action = ActionChains(driver)
            action.context_click(cat).perform()
            driver.find_element(By.XPATH, xpath.open).click()
            UID = utils.get_uid(driver)
            try:
                RT_VALUE = utils.Delete(
                    UID, AIRLINE_CATEGORY_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_2471_AssignRoottype_001_002_003_005_MKTPL_2379_Assignsequencenumber_001_002_003_005",
                "Verify by clicking on the CSV import" " icon in the left side menu",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2471_AssignRoottype_001_002_003_005_MKTPL_2379_Assignsequencenumber_001_002_003_005",
            "Verify by clicking on the CSV " "import icon in the left side menu",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2471_AssignRoottype_001_002_003_005_MKTPL_2379_Assignsequencenumber_001_002_003_005",
            "Verify by clicking on the CSV " "import icon in the left side menu",
            e,
        )
        raise e

@jamatest.test(11976129)
@jamatest.test(11976132)
@jamatest.test(11976136)
@jamatest.test(11976139)
def test_MKTPL_316_RoutegroupAPI_001_004_008_011(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_316_RoutegroupAPI_001_004_008_011.png", False
        ) as driver:
            # Add
            payload = json.dumps({"name": RANDOM_NAME})

            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request(
                "POST",
                CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL,
                headers=headers,
                data=payload,
            )
            data = response.json()
            logging.info("------ADD------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API responded with 200 status code")
            airline_category_id = data["id"]
            logging.info(f"Airline Id- {airline_category_id}")

            # Login and verifying the object created or not.

             
            
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("RouteGroup", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
                )
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="' + RANDOM_NAME + '"]')
                    )
                )
                .is_displayed()
            )
            assert published
            logging.info(
                f"Route group go automatically under Airline Operation section/RouteGroup folder = {published}"
            )

            # GET_BY_ID
            url_id = CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL + str(airline_category_id)
            logging.info(url_id)
            response = requests.request("GET", url_id, headers=headers, data=payload)
            data_id = response.json()
            logging.info("-------GET BY ID-------")
            logging.info(data_id)
            assert response.status_code == 200 or response.status_code == 201
            sector = data_id.get("data", {}).get("route_group", {})
            assert "id" in sector
            assert "name" in sector
            logging.info(
                "API  respond with 200 status code and it should display following parameters in the response :"
                "- id"
                "- name"
            )
            # GET
            payload = {}
            response = requests.request(
                "GET",
                CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL,
                headers=headers,
                data=payload,
            )
            data = response.json()
            logging.info("-------GET-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # utils.Delete
            response = requests.request("Delete", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("-------utils.Delete-------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Data cleaned up successfully.")
            Success_List_Append(
                "test_MKTPL_316_RoutegroupAPI_001_004_008_011",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_316_RoutegroupAPI_001_004_008_011",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_316_RoutegroupAPI_001_004_008_011",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11976074)
@jamatest.test(11976077)
@jamatest.test(11976081)
@jamatest.test(11976084)
@jamatest.test(11976093)
def test_MKTPL_306_RouteAPI_01_004_008_011_020(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        # Add
        payload = json.dumps(
            {
                "sectorName": RANDOM_NAME,
                "summary": "test summary",
                "routeGroup": "",
                "destination": "OMAM",
                "origin": "OMAD",
                "distance": 90,
            }
        )

        headers = {
            "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
            "Content-Type": "application/json",
        }

        response = requests.request(
            "POST", ROUTE_SECTOR_URL, headers=headers, data=payload
        )
        data = response.json()
        logging.info("------ADD------")
        logging.info(data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API for Airlinecategory responded with 200 status code")
        airline_category_id = data["id"]
        logging.info(f"Airline Id- {airline_category_id}")

        # Login and verifying the object created or not.
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_306_RouteAPI_01_004_008_011_020.png", False
        ) as driver:
             
            
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Sector", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)

            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
                )
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                RANDOM_NAME, Keys.ENTER
            )
            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/'
                            + DEDICATED_AIRLINE
                            + "/Sectors/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert published
            logging.info(
                f"Sector should go automatically under Airline Operation section/Sectors folder = {published}"
            )

            # Update
            url_id = ROUTE_SECTOR_URL + str(airline_category_id)
            logging.info(url_id)
            random_summary = "".join(random.choices(string.ascii_letters, k=10))
            payload = json.dumps(
                {
                    "sectorName": RANDOM_NAME,
                    "summary": random_summary,
                    "routeGroup": "",
                    "destination": "OMAM",
                    "origin": "OMAD",
                    "distance": 90,
                }
            )

            response = requests.request("PUT", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("------UPDATE------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                f"UPDATE API for Airlinecategory responded with 200 status code"
            )
            logging.info(
                f"The sector is updated in the pimcore with all the details provided in the API "
            )

            # GET_BY_ID
            response = requests.request("GET", url_id, headers=headers, data=payload)
            data_id = response.json()
            logging.info("-------GET BY ID-------")
            logging.info(data_id)
            assert response.status_code == 200 or response.status_code == 201
            sector = data_id.get("data", {}).get("sector", {})
            assert "id" in sector
            assert "sectorName" in sector
            assert "summary" in sector
            assert "routeGroup" in sector
            assert "origin" in sector
            assert "destination" in sector
            assert "distance" in sector
            assert "airline" in sector

            # GET
            payload = {}
            response = requests.request(
                "GET", ROUTE_SECTOR_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------GET-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # utils.Delete
            response = requests.request("DELETE", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("-------utils.Delete-------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Data cleaned up successfully.")
            Success_List_Append(
                "test_MKTPL_306_RouteAPI_01_004_008_011_020",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_306_RouteAPI_01_004_008_011_020",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_306_RouteAPI_01_004_008_011_020",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11976103)
@jamatest.test(11976106)
@jamatest.test(11976110)
@jamatest.test(11976113)
@jamatest.test(11976116)
@jamatest.test(11975409)
@jamatest.test(11975413)
@jamatest.test(11975418)
@jamatest.test(11975424)
def test_MKTPL_309_FlightAPI_01_004_008_011_014_MKTPL_1240_016_020_025_031(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=4)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_309_FlightAPI_01_004_008_011_014_MKTPL_1240_016_020_025_031.png", False
        ) as driver:
            # Add
            payload = json.dumps(
                {
                    "flightRouteNumber": RANDOM_NAME,
                    "fullfilmentCutOffTimeLimit": 1111,
                    "sectors": [
                        {"id": int(DEDICATED_SECTOR_1_ID)},
                        {"id": int(DEDICATED_SECTOR_2_ID)},
                    ],
                }
            )

            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request(
                "POST", CRED_AUTOMATION_AIRLINE_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("------ADD------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API for Airlinecategory responded with 200 status code")
            airline_category_id = data["id"]
            logging.info(f"Airline Id- {airline_category_id}")

            # Update
            url_id = CRED_AUTOMATION_AIRLINE_URL + str(airline_category_id)
            logging.info(url_id)
            payload = json.dumps(
                {
                    "flightRouteNumber": RANDOM_NAME,
                    "fullfilmentCutOffTimeLimit": 111,
                    "flightBeginDateTime": "",
                    "flightEndDateTime": "",
                    "sectors": [
                        {"id": int(DEDICATED_SECTOR_1_ID)},
                        {"id": int(DEDICATED_SECTOR_2_ID)},
                    ],
                }
            )

            response = requests.request("PUT", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("------UPDATE------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                f"Updated the API for Airline Category responded with 200 status code"
            )
            logging.info(
                f"The flight should be updated in the pimcore with all the details provided in the API"
            )
            # Login and verifying the object created or not.

             
            
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Flight", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
                )
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                RANDOM_NAME, Keys.ENTER
            )
            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//div[text()='" + RANDOM_NAME + "']")
                    )
                )
                .is_displayed()
            )
            assert published
            logging.info(
                f"The flight created in the pimcore with all the details provided in the API and it is publish"
                f" in the pimcore"
            )

            # GET_BY_ID
            response = requests.request("GET", url_id, headers=headers, data=payload)
            data_id = response.json()
            logging.info("-------GET BY ID-------")
            logging.info(data_id)
            assert response.status_code == 200 or response.status_code == 201
            flights = data_id.get("data", {}).get("flight", {})
            assert "id" in flights
            assert "flightRouteNumber" in flights
            assert "flightBeginDateTime" in flights
            assert "flightEndDateTime" in flights
            assert "sectors" in flights

            # GET
            payload = {}
            response = requests.request(
                "GET", CRED_AUTOMATION_AIRLINE_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------GET-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # utils.Delete
            response = requests.request("Delete", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("-------utils.Delete-------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Data cleaned up successfully.")
            Success_List_Append(
                "test_MKTPL_309_FlightAPI_01_004_008_011_014_MKTPL_1240_016_020_025_031",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_309_FlightAPI_01_004_008_011_014_MKTPL_1240_016_020_025_031",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_309_FlightAPI_01_004_008_011_014_MKTPL_1240_016_020_025_031",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11977688)
def test_MKTPL_2467_RoottypeView_002(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2467_RoottypeView_002.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            
            utils.search_by_id(driver, DEDICATED_ROOT_TYPE_1_ID)
            # (
            #     WebDriverWait(driver, 60)
            #     .until(
            #         EC.visibility_of_element_located(
            #             (By.XPATH, '//div[text()="'+DEDICATED_ROOT_TYPE_1_NAME+'"]')
            #         )
            #     )
            #     .click()
            # )
            # driver.find_element(
            #     By.XPATH, '//span[text()="'+DEDICATED_ROOT_TYPE_1_NAME+'"]'
            # ).click()
            read_only = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            xpath.readModeLock,
                        )
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"Admin/manager able to view the data of Root Type in read only mode = {read_only}"
            )
            Success_List_Append(
                "test_MKTPL_2467_RoottypeView_002",
                "VVerify by clicking on the Root Type",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2467_RoottypeView_002",
            "Verify by clicking on the Root Type",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2467_RoottypeView_002", "Verify by clicking on the Root Type", e
        )
        raise e

@jamatest.test(11976278)
def test_MKTPL_350_CRUDRoutegroup_003(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_NUMBER = "".join([str(randint(0, 9)) for i in range(7)])

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_350_CRUDRoutegroup_003.png", False
        ) as driver:
             
            
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//div[text()='Airline Operation']//ancestor::div[5]//span[text()='"+DEDICATED_AIRLINE+"']")
                    )
                )
            )

            Cred_Airline = driver.find_element(
                By.XPATH, "//div[text()='Airline Operation']//ancestor::div[5]//span[text()='"+DEDICATED_AIRLINE+"']"
            )
            time.sleep(1)
            action = ActionChains(driver)
            action.context_click(Cred_Airline).perform()
            time.sleep(1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            driver.find_element(By.XPATH, '(//a//span[text()="RouteGroup"])[1]').click()
            logging.info(f"Clicked on RouteGroup.")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.visibility_of_element_located((By.NAME, "name")))
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.visibility_of_element_located((By.NAME, "code")))
                .send_keys(RANDOM_NUMBER)
            )
            saved = utils.save_and_publish(driver)
            assert saved
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("RouteGroup", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="query"]')
                    )
                )
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//td//div[text()="' + RANDOM_NAME + '"])[1]')
                )
            )
            logging.info(
                "RouteGroup object move automatically under Airline Operation section/RouteGroup folder"
            )
            UID = utils.get_uid(driver)

            try:
                RT_VALUE = utils.Delete(
                    UID,
                    CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL + UID,
                    CRED_AUTOMATION_AIRLINE_TOKEN,
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_350_CRUDRoutegroup_003",
                "Verify by entering/updating data in below fields",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_350_CRUDRoutegroup_003",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_350_CRUDRoutegroup_003",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

@jamatest.test(11976254)
@jamatest.test(11976255)
@jamatest.test(11976256)
@jamatest.test(11976267)
def test_MKTPL_348_Routeimport_001_002_003_014(airline_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_348_Routeimport_001_002_003_014.png", False
        ) as driver:
             
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            import_screen = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.ID, "csv_import_form_fieldset-legendTitle")
                    )
                )
                .is_displayed()
            )
            assert import_screen
            logging.info(
                f"User should navigate to the CSV import screen = {import_screen}"
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            )
            store = driver.find_element(By.NAME, "importType")
            store.send_keys("Sector")
            time.sleep(1)
            store.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
            )
            download_button = driver.find_element(By.ID, xpath.downloadCsvButtonId)
            download_button.is_enabled()
            assert download_button
            logging.info(
                f"Download Sample file of Sector button should be enabled besides import data type dropdown"
            )
            download_button.click()
            logging.info("clicked")
            time.sleep(2)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_SECTOR_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_SECTOR_SAMPLE, "r", encoding="utf-8-sig"
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_SAMPLE_SECTOR == ACTUAL_CSV_DATA
            logging.info(
                f"CSV file should be download on the system and sample records should display in CSV"
            )
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_SECTOR_SAMPLE)

            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=5)
            )
            utils.update_csv("Sector_Sample.csv", "sectorName_en", RANDOM_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Sector_Sample.csv", file_path)
            assert saved_message

            logging.info(
                f"Sector should be successfully imported in the system = {saved_message}"
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Sector", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(EC.element_to_be_clickable((By.XPATH, '//input[@name="query"]')))
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="/'
                        + DEDICATED_AIRLINE
                        + "/Sectors/"
                        + RANDOM_NAME
                        + '"]',
                    )
                )
            )
            logging.info(
                "Sector object  move automatically under Airline Operation section/Sectors folder"
            )
            # UID = utils.get_uid(driver)
            # try:
            #     RT_VALUE = utils.Delete(UID, AIRLINE_CATEGORY_URL, CRED_AUTOMATION_AIRLINE_TOKEN)
            #     assert RT_VALUE == 200, RT_VALUE
            #     logging.info(f"Data cleaned up successfully.")
            # except Exception as e:
            #     logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_348_Routeimport_001_002_003_014",
                "Verify by clicking on the CSV " "import icon in the left side " "menu",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_348_Routeimport_001_002_003_014",
            "Verify by clicking on the CSV " "import icon in the left side menu",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_348_Routeimport_001_002_003_014",
            "Verify by clicking on the CSV " "import icon in the left side menu",
            e,
        )
        raise e

@jamatest.test(11976283)
def test_MKTPL_352_CRUDUser_003(airline_Login):
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_352_CRUDUser_003.png", False
        ) as driver:
             
            utils.move_modules_right_to_left(driver)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.presence_of_element_located(
                    ("xpath", xpath.UserManagementTab)
                )
            ).click()
            users = wait.until(
                EC.presence_of_element_located(("xpath", "//div[contains(text(),'User Management')]//ancestor::div[contains(@role, 'treegrid')]//span[contains(text(),'Users')]"))
            )
            action = ActionChains(driver)
            action.context_click(users).perform()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            logging.info("Add Object")
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            user = driver.find_element(
                By.XPATH, '(//span[text()="Users"])[last()]'
            )
            mouse_hover.move_to_element(user).click().perform()
            driver.implicitly_wait(0)
            logging.info("Add Users")
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "firstName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            driver.find_element(By.NAME, "userRole").send_keys("airlinemanager")
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Mail Subscriptions"]')
                )
            ).click()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"User is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            try:
                wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="Users"]//..//a[2]',
                        )
                    )
                ).click()
                wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="Users"]/..//input[@name="filter"]',
                        )
                    )
                ).send_keys(RANDOM_NAME, Keys.ENTER)
                new_user = wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()= "'
                            + RANDOM_NAME
                            + '" and @class ="x-tree-node-text  "]',
                        )
                    )
                ).is_displayed()
            except:
                new_user = wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="Users"]//..//..//..//..//..//../table//span[text()="'
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                ).is_displayed()
            assert new_user
            logging.info(
                f"New created user is moved under User Management = {new_user}"
            )
            logging.info(
                "Verify User is created/updated in the system, moved to User Management and receive an email "
                "for successfull registration."
            )
            Success_List_Append(
                "test_MKTPL_352_CRUDUser_003",
                "Verify User is created/updated in the system, "
                "moved to User Management and receive an email "
                "for successfull registration.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_352_CRUDUser_003",
            "Verify User is created/updated in the system, "
            "moved to User Management and receive an email "
            "for successfull registration.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_352_CRUDUser_003",
            "Verify User is created/updated in the system, "
            "moved to User Management and receive an email "
            "for successfull registration.",
            e,
        )
        raise e

@jamatest.test(11976946)
@jamatest.test(11976947)
@jamatest.test(11976948)
@jamatest.test(11976953)
def test_MKTPL_635_637_Flightimport_001_002_003_008(airline_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_635_637_Flightimport_001_002_003_008.png", False
        ) as driver:
             
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            import_screen = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.ID, "csv_import_form_fieldset-legendTitle")
                    )
                )
                .is_displayed()
            )
            assert import_screen
            logging.info(
                f"User should navigate to the CSV import screen = {import_screen}"
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            )
            store = driver.find_element(By.NAME, "importType")
            store.send_keys("Flight")
            time.sleep(3)
            store.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
            )
            download_button = driver.find_element(By.ID, xpath.downloadCsvButtonId)
            download_button.is_enabled()
            assert download_button
            logging.info(
                f"Download Sample file of Sector button should be enabled besides import data type dropdown"
                f" = {download_button}"
            )
            download_button.click()
            logging.info("clicked")
            time.sleep(2)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE, "r", encoding="utf-8-sig"
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_SAMPLE_FLIGHT_ADMIN == ACTUAL_CSV_DATA
            logging.info(
                f"CSV file should be download on the system and sample records should display in CSV"
            )
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE)

            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=2)
            )
            RANDOM_NUMBER = "".join([str(randint(0, 2)) for i in range(2)])
            random_num = RANDOM_NAME + RANDOM_NUMBER + RANDOM_NAME
            logging.info(random_num)

            utils.update_csv("Flight_Sample.csv", "flightRouteNumber", random_num)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Flight_Sample.csv", file_path)
            assert saved_message

            # df = pd.read_csv(os.getcwd()+"/credencys_test_ui/"+"Flight_Sample.csv")
            # df.loc[0, 'flightRouteNumber'] = random_num
            # df.to_csv(os.getcwd()+"/credencys_test_ui/"+"Flight_Sample.csv", index=False)
            # path = os.path.join(os.getcwd(), "Flight_Sample.csv")
            # driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            # driver.find_element(By.XPATH, xpath.upload).click()
            # logging.info("clicked")
            # saved_message = (WebDriverWait(driver, 60).until(
            #     EC.visibility_of_element_located((By.XPATH,
            #                                     '//div[text()="CSV Uploaded Successfully. Import in Progress. A log file will be sent via Mail."]'))).is_displayed())
            #
            # assert saved_message
            logging.info(
                f"Sector should be successfully imported in the system = {saved_message}"
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Flight", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
                )
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                random_num, Keys.ENTER
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f'(//div[text()="/{DEDICATED_AIRLINE}/Flight/{random_num}"])[1]',
                    )
                )
            )
            fli = driver.find_element(
                By.XPATH, f'(//div[text()="/{DEDICATED_AIRLINE}/Flight/{random_num}"])'
            )
            logging.info(
                "Sector object  move automatically under Airline Operation section/Sectors folder"
            )
            time.sleep(3)
            actions = ActionChains(driver)
            actions.double_click(fli).perform()
            # driver.find_element(By.XPATH, xpath.open).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, f"//input[@value='{random_num}']")
                )
            )
            UID = utils.get_uid(driver)
            try:
                RT_VALUE = utils.Delete(
                    UID, AIRLINE_CATEGORY_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_635_637_Flightimport_001_002_003_008",
                "Verify by clicking on the CSV " "import icon in the left side " "menu",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_635_637_Flightimport_001_002_003_008",
            "Verify by clicking on the CSV " "import icon in the left side menu",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_635_637_Flightimport_001_002_003_008",
            "Verify by clicking on the CSV " "import icon in the left side menu",
            e,
        )
        raise e

@jamatest.test(11976271)
def test_MKTPL_349_CRUDRoute_003(airline_Login):
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_349_CRUDRoute_003.png", False
        ) as driver:
             
            
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="' + DEDICATED_AIRLINE + '"]')
                )
            )
            airline = driver.find_element(
                By.XPATH, '//span[text()="' + DEDICATED_AIRLINE + '"]'
            )
            action = ActionChains(driver)
            action.context_click(airline).perform()
            wait.until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="Refresh"])'))
            )
            driver.find_element(By.XPATH, '(//span[text()="Refresh"])').click()
            home = driver.find_element(
                By.XPATH, '//span[text()="' + DEDICATED_AIRLINE + '"]'
            )
            action = ActionChains(driver)
            action.context_click(home).perform()
            driver.implicitly_wait(0)
            wait.until(EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath)))
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            Sector = driver.find_element(By.XPATH, '(//span[text()="Sector"])[1]')
            Sector.click()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "sectorName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "summary").send_keys(
                "Sector summary for testing.."
            )
            from_search = driver.find_element(
                By.XPATH,
                '(//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "])[2]',
            )
            from_search.click()
            elem = wait.until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '//div[@class="x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-unselectable x-scroller"]//div//table[1]//tr//td',
                    )
                )
            )
            ActionChains(driver).double_click(elem).perform()

            to_search = driver.find_element(
                By.XPATH,
                '(//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "])[3]',
            )
            to_search.click()
            elem = wait.until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '//div[@class="x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-unselectable x-scroller"]//div//table[2]//tr//td',
                    )
                )
            )
            ActionChains(driver).double_click(elem).perform()

            scroll = driver.find_element(By.XPATH, '//span[text()="Distance:"]')
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)

            driver.find_element(
                By.XPATH, '(//span[text()="Distance:"]//..//..//..//div//input)[1]'
            ).send_keys("4279")
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Sector is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)

            # driver.refresh()
            logging.info(RANDOM_NAME)
            driver.find_element(
                By.XPATH, '//span[text()="' + DEDICATED_AIRLINE + '"]'
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
                .send_keys("Sector", Keys.ENTER)
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
            ).send_keys(Keys.ENTER)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
            ).send_keys(RANDOM_NAME, Keys.ENTER)
            new_sector = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div[text() ="/'
                        + DEDICATED_AIRLINE
                        + "/Sectors/"
                        + RANDOM_NAME
                        + '"]',
                    )
                )
            ).is_displayed()
            assert new_sector
            logging.info(
                f"New created Sector is moved under Airline operation Section/Sector folder = {new_sector}"
            )
            try:
                RT_VALUE = utils.Delete(
                    UID, ROUTE_SECTOR_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            logging.info(
                "Verify Sector is created/updated in the system, moved to Airline operation Section/Sector folder."
            )
            Success_List_Append(
                "test_MKTPL_349_CRUDRoute_003",
                "Verify Sector is created/updated in the system, "
                "moved to Airline operation Section/Sector folder.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_349_CRUDRoute_003",
            "Verify Sector is created/updated in the system, "
            "moved to Airline operation Section/Sector folder.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_349_CRUDRoute_003",
            "Verify Sector is created/updated in the system, "
            "moved to Airline operation Section/Sector folder.",
            e,
        )
        raise e

@jamatest.test(11977635)
@jamatest.test(11977636)
@jamatest.test(11977637)
def test_MKTPL_2421_Catalogassignment_001_002_003(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2421_Catalogassignment_001_002_003.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_Airline(driver)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                    )
                )
            ).click()
            catalog = wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )
            action = ActionChains(driver)
            action.context_click(catalog).perform()
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            catalog_assignment = driver.find_element(
                By.XPATH,
                '(//div[@class="x-box-target"]//span[text()="CatalogAssignment"])[1]',
            )
            mouse_hover.move_to_element(catalog_assignment).click().perform()
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            search = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Catalog "]//..//..//../div//a)[3]')
                )
            )
            driver.execute_script("arguments[0].click();", search)
            driver.find_element(By.NAME, "query").send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//td//div[text()="' + DEDICATED_CATALOG_1_ID + '"]')
                )
            )
            elm = driver.find_element(
                By.XPATH, '//td//div[text()="' + DEDICATED_CATALOG_1_ID + '"]'
            )
            action = ActionChains(driver)
            action.double_click(elm).perform()

            assignment_type = wait.until(
                EC.visibility_of_element_located((By.NAME, "assignmentType"))
            )
            assignment_type.clear()
            assignment_type.send_keys("RouteGroup", Keys.TAB)
            associate_route_group = wait.until(
                EC.visibility_of_element_located((By.NAME, "associateRouteGroup"))
            )
            associate_route_group.clear()
            associate_route_group.send_keys(DEDICATED_ROUTE_GROUP_1_NAME, Keys.ENTER)
            associate_route_group.send_keys(Keys.DOWN, Keys.ENTER)
            sectors = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div//b[text()="Sector Sectors will be auto-populated based on selection of RouteGroup, '
                        'Please save and refresh once to get list."]',
                    )
                )
            )
            assert sectors.is_displayed()
            sector = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Sector Name"]')
                )
            ).is_displayed()
            flight = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Sector Name"]//..//..//..//..//..//../div//span[text()="Flight"]',
                    )
                )
            ).is_displayed()
            assert sector and flight
            logging.info(
                "Sectors data are auto populated in the Sector grid, "
                "All the Associated published flights are displayed in the Flight column"
            )
            data = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[@class="x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-scroller"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert data
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "(//span[text()='Flight'])[1]")
                )
            )
            if (
                len(
                    driver.find_elements(
                        By.XPATH, "//td[contains(@class,'checkcolumn')]"
                    )
                )
                != 0
            ):
                flight_data = WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//div[@class="x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-scroller"]//table//tr)[1]//td[4]',
                        )
                    )
                )
            else:
                flight_data = WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//div[@class="x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-scroller"]//table//tr)[1]//td[3]',
                        )
                    )
                )
            sector_name = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//div[@class="x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-scroller"]//table//tr)[1]//td[2]//div',
                        )
                    )
                )
                .text
            )
            logging.info(sector_name)
            action.double_click(flight_data).perform()
            logging.info("Double click on flight")
            time.sleep(10)
            popup = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Select Flights (If No Flights is/are Selected Then All Flights will be considered selected.)"])',
                        )
                    )
                )
                .is_displayed()
            )
            assert popup
            driver.refresh()
            logging.info("Selected Sector and associated flight details are displayed.")
            logging.info(
                "Sectors data are auto populated in the Sector grid, "
                "All the Associated published flights are displayed in the Flight column"
            )

            Success_List_Append(
                "test_MKTPL_2421_Catalogassignment_001_002_003",
                "Verify Catalog Assignment is created in the system, "
                "Sectors data should are auto populated in the Sector grid, "
                "All the Associated published flights are displayed in "
                "the Flight column",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2421_Catalogassignment_001_002_003",
            "Verify Catalog Assignment is created in the system, "
            "Sectors data should are auto populated in the Sector grid, "
            "All the Associated published flights are displayed in "
            "the Flight column",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2421_Catalogassignment_001_002_003",
            "Verify Catalog Assignment is created in the system, "
            "Sectors data should are auto populated in the Sector grid, "
            "All the Associated published flights are displayed in "
            "the Flight column",
            e,
        )
        raise e

@jamatest.test(11977640)
@jamatest.test(11977641)
@jamatest.test(11977642)
def test_MKTPL_2422_Catalogassignment_001_002_003(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2422_Catalogassignment_001_002_003.png", False
        ) as driver:
             
            
            try:
                for x in range(6):
                    driver.find_element(
                        By.XPATH,
                        '(//div[@class="x-tool-tool-el x-tool-img x-tool-left "])[last()]',
                    ).click()
            except:
                logging.info(" Screen loaded on one side properly.")
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    ("xpath", xpath.catalogAssignment)
                )
            ).click()

            catalog = wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '(//div[text()="Catalog Assignment"]//../ancestor::div)[9]//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )
            action = ActionChains(driver)
            action.context_click(catalog).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            driver.implicitly_wait(0)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            # time.sleep(1)
            driver.implicitly_wait(0)
            catalog_assignment = driver.find_element(
                By.XPATH,
                '(//div[@class="x-box-target"]//span[text()="CatalogAssignment"])[1]',
            )
            mouse_hover.move_to_element(catalog_assignment).click().perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()

            search = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Catalog "]//..//..//../div//a)[3]')
                )
            )
            driver.execute_script("arguments[0].click();", search)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//input[@name = "query"]'))
            )
            driver.find_element(By.XPATH, '//input[@name = "query"]').send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            search_object = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//td//div[text()=" + DEDICATED_CATALOG_1_ID + "]")
                )
            )
            action = ActionChains(driver)
            action.double_click(search_object).perform()
            time.sleep(1)
            assignment_type = wait.until(
                EC.visibility_of_element_located((By.NAME, "assignmentType"))
            )
            assignment_type.clear()
            assignment_type.send_keys("Sector", Keys.TAB)
            sectors = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div//b[text()="Sector"]')
                )
            )
            assert sectors.is_displayed()
            flight = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div//b[text()="Sector"]//..//..//..//..//../div)[11]//span[text()="Flight"]',
                    )
                )
            ).is_displayed()
            assert flight
            logging.info(
                "All the Associated published flights are displayed in the Flight column"
            )
            search_sector = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div//b[text()="Sector"]//..//..//../a)[3]')
                )
            )
            driver.execute_script("arguments[0].click();", search_sector)
            driver.implicitly_wait(0)

            search_object = wait.until(
                EC.visibility_of_element_located((By.XPATH, '//input[@name="query"]'))
            )
            search_object.send_keys(DEDICATED_SECTOR_1_ID, Keys.ENTER)

            select_flight = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'" + DEDICATED_SECTOR_1_ID + "')]",
                    )
                )
            )
            action.double_click(select_flight).perform()
            driver.implicitly_wait(0)
            add_sector = wait.until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Select"]'))
            )
            add_sector.click()
            try:
                ele_message = wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="All unsaved changes will be lost, are you really sure?"]',
                        )
                    )
                ).is_displayed()
                assert ele_message
                wait.until(
                    EC.visibility_of_element_located((By.XPATH, '//span[text()="No"]'))
                ).click()
            except:
                logging.info("Model not shown")
                pass
            time.sleep(5)
            driver.implicitly_wait(0)
            flights = wait.until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '((//div[contains(text(),"Automation_")]//..//../td)[3]//div)[1]',
                    )
                )
            )
            flight_value = flights.text
            time.sleep(2)
            driver.execute_script("arguments[0].click();", flights)
            logging.info(flight_value)
            driver.implicitly_wait(0)
            wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[@class="x-grid-checkcolumn"])[1]')
                )
            )
            driver.implicitly_wait(0)
            flight_checkbox = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[@class="x-grid-checkcolumn"])[1]')
                )
            )
            action.click(flight_checkbox).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Save" and @class="x-btn-inner x-btn-inner-default-small"]',
                    )
                )
            ).click()
            driver.implicitly_wait(0)
            logging.info("flights uncheck")
            flights1 = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '((//div[contains(text(),"Automation_")]//..//../td)[3]//div)[1]',
                    )
                )
            )
            update_flight_value = flights1.text
            logging.info(update_flight_value)
            assert flight_value != update_flight_value
            driver.implicitly_wait(0)
            flights = wait.until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '((//div[contains(text(),"Automation_")]//..//../td)[3]//div)[1]',
                    )
                )
            )
            flight_value = flights.text
            time.sleep(2)
            driver.execute_script("arguments[0].click();", flights)
            logging.info(flight_value)
            driver.implicitly_wait(0)
            wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[@class="x-grid-checkcolumn"])[1]')
                )
            )
            driver.implicitly_wait(0)
            flight_checkbox = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[@class="x-grid-checkcolumn"])[1]')
                )
            )
            action.click(flight_checkbox).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Save" and @class="x-btn-inner x-btn-inner-default-small"]',
                    )
                )
            ).click()
            driver.implicitly_wait(0)
            logging.info("flights check")
            flights1 = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '((//div[contains(text(),"Automation_")]//..//../td)[3]//div)[1]',
                    )
                )
            )
            update_flight_value = flights1.text
            logging.info(update_flight_value)
            assert flight_value != update_flight_value
            logging.info(
                "All the Associated published flights are displayed in the Flight column"
            )

            Success_List_Append(
                "test_MKTPL_2422_Catalogassignment_001_002_003",
                "Verify Catalog Assignment is created in the system, "
                "All the Associated published flights are displayed in "
                "the Flight column",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2422_Catalogassignment_001_002_003",
            "Verify Catalog Assignment is created in the system, "
            "All the Associated published flights are displayed in "
            "the Flight column",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2422_Catalogassignment_001_002_003",
            "Verify Catalog Assignment is created in the system, "
            "All the Associated published flights are displayed in "
            "the Flight column",
            e,
        )
        raise e

@jamatest.test(11977654)
@jamatest.test(11977655)
@jamatest.test(11977656)
def test_MKTPL_2425_Catalogassignment_001_002_003(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2425_Catalogassignment_001_002_003.png", False
        ) as driver:
             
            
            wait = WebDriverWait(driver, 60)
            try:
                for x in range(6):
                    driver.find_element(
                        By.XPATH,
                        '(//div[@class="x-tool-tool-el x-tool-img x-tool-left "])[last()]',
                    ).click()
            except:
                logging.info(" Screen loaded on one side properly.")
            wait.until(
                EC.visibility_of_element_located(
                    ("xpath", xpath.catalogAssignment)
                )
            ).click()
            catalog = wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '(//div[text()="Catalog Assignment"]//../ancestor::div)[9]//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )
            action = ActionChains(driver)
            action.context_click(catalog).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            catalog_assignment = driver.find_element(
                By.XPATH,
                '(//div[@class="x-box-target"]//span[text()="CatalogAssignment"])[1]',
            )
            mouse_hover.move_to_element(catalog_assignment).click().perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            search = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Catalog "]//..//..//../div//a)[3]')
                )
            )
            driver.execute_script("arguments[0].click();", search)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//input[@name = "query"]'))
            )
            driver.find_element(By.XPATH, '//input[@name = "query"]').send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )

            search_object = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//td//div[text()="' + DEDICATED_CATALOG_1_ID + '"]')
                )
            )
            action = ActionChains(driver)
            action.double_click(search_object).perform()
            # driver.implicitly_wait(0)
            assignment_type = wait.until(
                EC.visibility_of_element_located((By.NAME, "assignmentType"))
            )
            assignment_type.clear()
            assignment_type.send_keys("Flight", Keys.TAB)
            time.sleep(3)
            search_flight = wait.until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '(//div//b[text()="Flight"]//..//..//../a)[3]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", search_flight)
            search_object = wait.until(
                EC.visibility_of_element_located((By.XPATH, '//input[@name="query"]'))
            )
            search_object.send_keys(DEDICATED_FLIGHT_1_ID, Keys.ENTER)
            # select_flight = wait.until(
            #     EC.visibility_of_element_located(
            #         (
            #             By.XPATH,
            #             '//td//div[text()="/'+DEDICATED_AIRLINE+'/Flight/'+DEDICATED_FLIGHT_1_NAME+'"]',
            #         )
            #     )
            # )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'" + DEDICATED_FLIGHT_1_ID + "')]",
                    )
                )
            )
            select_flight = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_FLIGHT_1_ID + "')]"
            )
            action.double_click(select_flight).perform()
            driver.implicitly_wait(0)
            add_flight = wait.until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Select"]'))
            )
            add_flight.click()
            try:
                ele_message = wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="All unsaved changes will be lost, are you really sure?"]',
                        )
                    )
                ).is_displayed()
                assert ele_message
                wait.until(
                    EC.visibility_of_element_located((By.XPATH, '//span[text()="No"]'))
                ).click()
                saved = (
                    WebDriverWait(driver, 60)
                    .until(
                        EC.visibility_of_element_located(
                            (By.XPATH, '//div[text()="Saved successfully!"]')
                        )
                    )
                    .is_displayed()
                )
                assert saved
            except:
                logging.info(
                    "Not Shown - All unsaved changes will be lost, are you really sure?"
                )

            sector = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[contains(text(),"/'
                        + DEDICATED_AIRLINE
                        + '/Flight/")]//..//../td)[3]//div',
                    )
                )
            )
            logging.info(sector.text)
            # time.sleep(20)

            # click = driver.find_element(By.XPATH, '(//div[contains(text(),"Automation_Sector_2,Automation_Sector")]//..//..)[3]')
            click = driver.find_element(
                By.XPATH, '(//b[text()="Flight"]/../../../../..//tbody//tr//td//div)[3]'
            )
            action = ActionChains(driver)
            action.double_click(click).perform()
            logging.info("double clicked")
            chkbox = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[@class="x-grid-checkcolumn"]')
                )
            ).is_displayed()
            logging.info(chkbox)
            save = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Save" and @class="x-btn-inner x-btn-inner-default-small"]',
                    )
                )
            ).is_displayed()
            logging.info(save)
            assert chkbox and save
            checkbox = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[@class="x-grid-cell-inner x-grid-checkcolumn-cell-inner"])[1]',
                    )
                )
            )
            checkbox.click()
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Save" and @class="x-btn-inner x-btn-inner-default-small"]',
                    )
                )
            ).click()
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[contains(text(),"/'
                        + DEDICATED_AIRLINE
                        + '/Flight/")]//..//../td)[3]//div',
                    )
                )
            ).click()
            chkbox = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[@class="x-grid-checkcolumn"]')
                )
            ).is_displayed()
            logging.info(chkbox)
            save = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Save" and @class="x-btn-inner x-btn-inner-default-small"]',
                    )
                )
            ).is_displayed()
            logging.info(save)
            assert chkbox and save
            logging.info(
                "All the associated sectors are displayed in the Sector column, Checkbox and Save is displayed."
            )
            logging.info(
                "All the associated sectors are displayed in the Sector column"
            )
            Success_List_Append(
                "test_MKTPL_2425_Catalogassignment_001_002_003",
                "Verify Catalog Assignment is created in the system,"
                " All the Associated sectors are displayed in the Sector column",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2425_Catalogassignment_001_002_003",
            "Verify Catalog Assignment is created in the system, "
            "All the Associated sectors are displayed in "
            "the Sector column",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2425_Catalogassignment_001_002_003",
            "Verify Catalog Assignment is created in the system, "
            "All the Associated sectors are displayed in "
            "the Sector column",
            e,
        )
        raise e

@jamatest.test(11976251)
def test_MKTPL_347_CRUDFlight_003(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_347_CRUDFlight_003.png", False
        ) as driver:
             
            
            wait = WebDriverWait(driver, 60)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//span[text()="' + DEDICATED_AIRLINE + '"])[1]')
                    )
                )
            )
            cred_automation_airline = driver.find_element(
                By.XPATH, '(//span[text()="' + DEDICATED_AIRLINE + '"])[1]'
            )
            action = ActionChains(driver)
            action.context_click(cred_automation_airline).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="Refresh"])'))
            )
            driver.find_element(By.XPATH, '(//span[text()="Refresh"])').click()
            cred_automation_airline = driver.find_element(
                By.XPATH, '//span[text()="' + DEDICATED_AIRLINE + '"]'
            )

            action = ActionChains(driver)
            action.context_click(cred_automation_airline).perform()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(5)
            driver.implicitly_wait(10)
            # mouse_hover_2 = ActionChains(driver)
            flight = driver.find_element(By.XPATH, '(//a//span[text()="Flight"])[1]')
            driver.execute_script("arguments[0].click();", flight)
            # mouse_hover_2.move_to_element(flight).click().perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.flightRouteNumberFlightFieldXpath)
                    )
                )
                .send_keys("13" + RANDOM_NAME + "34")
            )
            driver.find_element(
                By.XPATH, '//input[@name="fulfillmentCutOffTimeLimit"]'
            ).send_keys("1")
            driver.find_element(
                By.XPATH, '(//div[text()="Sectors"]/../../../../..//span//span)[1]'
            ).click()
            driver.find_element(By.XPATH, '//input[@name="sector"]').send_keys(
                DEDICATED_SECTOR_1_NAME
            )
            (WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//li[contains(text(),'"+DEDICATED_SECTOR_1_NAME+"')]")
                    )
                )
                .click())
            driver.find_element(By.XPATH, '//input[@name="sector_sequence"]').send_keys(
                "1"
            )
            saved = utils.save_and_publish(driver)
            assert saved
            # utils.close_All_Tabs(driver)
            logging.info(
                f"Flight should be successfully created/updated in the system = {saved}"
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Flight", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))
                )
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            assert (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//div[text()='13" + RANDOM_NAME + "34']")
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"Flight object should move automatically under Airline Operation section/Flight folder"
            )
            UID = utils.get_uid(driver)
            try:
                RT_VALUE = utils.Delete(
                    UID, AIRLINE_CATEGORY_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_347_CRUDFlight_003",
                "Flight object should move automatically under Airline Operation section/Flight folde",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_347_CRUDFlight_003",
            "Verify Catalog Assignment is created in the system, "
            "Sectors data should are auto populated in the Sector grid, "
            "All the Associated published flights are displayed in "
            "the Flight column",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_347_CRUDFlight_003",
            "Flight object should move automatically under Airline Operation section/Flight folde",
            e,
        )
        raise e

@jamatest.test(11977553)
@jamatest.test(11977554)
@jamatest.test(11977555)
def test_MKTPL_2276_AssignMealcode_001_002_003(airline_Login):
    ACTUAL_CSV_DATA = []

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2276_AssignMealcode_001_002_003.png", False
        ) as driver:
             
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(
                By.XPATH, '//span[text()="Open Mealcode Module"]'
            ).click()
            utils.wait_for_style_attribute(driver, 40)
            module_screen = driver.find_element(
                By.XPATH, '//div[text()="Mealcode Module"]'
            ).is_displayed()
            assert module_screen
            logging.info(
                f"Admin/manager should redirect to the Meal Code Module screen = {module_screen}"
            )
            up = driver.find_element(
                By.XPATH, '//span[text()="Product Id"]'
            ).is_displayed()
            ep = driver.find_element(
                By.XPATH, '//span[text()="Product PAC SKU"]'
            ).is_displayed()
            ip = driver.find_element(
                By.XPATH, '//span[text()="Product Name"]'
            ).is_displayed()
            ud = driver.find_element(By.XPATH, '//span[text()="Store"]').is_displayed()
            mc = driver.find_element(
                By.XPATH, '(//span[text()="Meal Code"])[1]'
            ).is_displayed()
            assert [up, ep, ip, ud, mc]
            logging.info(
                f"Following field should display:Product Id, Product PAC SKU, Product Name, Store, Meal Code"
            )
            driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE, "r", encoding="utf-8-sig"
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_MEAL_CODE_SAMPLE[1][3] == ACTUAL_CSV_DATA[1][3]
            logging.info(
                f"Amin/manager should be able to export the data in the CSV by clicking on the Export CSV "
                f"button with "
            )
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE)
            Success_List_Append(
                "test_MKTPL_2276_AssignMealcode_001_002_003",
                "Verify the Download the Predefined CSV Using the download button",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2276_AssignMealcode_001_002_003",
            "Verify the Download " "the Predefined CSV Using the download" " button",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2276_AssignMealcode_001_002_003",
            "Verify the Download the Predefined CSV" " Using the download button",
            e,
        )
        raise e

@jamatest.test(11977762)
def test_MKTPL_3031_CatalogAssignment_001(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3031_CatalogAssignment_001.png", False
        ) as driver:
            # driver.maximize_window()
            # # Login through PAC admin user
            # utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.catalogAssignment)
                    )
                )
            ).click()
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                            + DEDICATED_AIRLINE
                            + '"]',
                        )
                    )
                )
            )
            home = driver.find_element(
                By.XPATH,
                '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                + DEDICATED_AIRLINE
                + '"]',
            )
            action = ActionChains(driver)
            action.context_click(home).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(
                By.XPATH, "(//span[text()='CatalogAssignment'])[1]"
            ).click()
            # Enter catalog assignment name
            ca_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                ca_name
            )
            # Click on ok
            driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {ca_name}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_CATALOG_1_NAME + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            # assignment_type.click()
            assignment_type.send_keys("RouteGroup")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//li[contains(text(),'RouteGroup')]")
                    )
                )
                .click()
            )
            time.sleep(5)
            # assignment_type.send_keys()
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys("Automation_Route_Group")
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, "//li[contains(text(),'Automation_Route_Group')]")
                    )
                )
                .click()
            )
            logging.info("Selected Route Group")
            time.sleep(1)
            driver.implicitly_wait(0)
            # Check for the validation
            # WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,xpath.saveSuccessfullyMessageXpath)))
            # WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, "(//div[text()='-'])[1]"))
            # )
            # assert driver.find_element(
            #     By.XPATH, "(//div[text()='-'])[1]"
            # ).is_displayed()

            Success_List_Append(
                "test_MKTPL_3031_CatalogAssignment_001",
                "Verify when catalog assignment is created, updated and unpublished via UI",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3031_CatalogAssignment_001",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3031_CatalogAssignment_001",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            e,
        )
        raise e

@jamatest.test(11977537)
@jamatest.test(11977538)
def test_MKTPL_2274_AssignMealcode_001_002(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2274_AssignMealcode_001_002.png", False
        ) as driver:
             
            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(
                By.XPATH, '//span[text()="Open Mealcode Module"]'
            ).click()
            redirect = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//div[text()="Mealcode Module"])[1]')
                    )
                )
                .is_displayed()
            )
            assert redirect
            logging.info("Admin/manager redirect to the Meal Code Module screen")
            product_id = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        ("xpath", '//span[text()="Product Id"]')
                    )
                )
                .is_displayed()
            )
            logging.info(product_id)
            sku = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        ("xpath", '//span[text()="Product PAC SKU"]')
                    )
                )
                .is_displayed()
            )
            logging.info(sku)
            product_name = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        ("xpath", '(//span[text()="Product Name"])[1]')
                    )
                )
                .is_displayed()
            )
            logging.info(product_name)
            store = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        ("xpath", '//span[text()="Store"]')
                    )
                )
                .is_displayed()
            )
            logging.info(store)
            meal_code_module = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        ("xpath", '(//span[text()="Meal Code"])[1]')
                    )
                )
                .is_displayed()
            )
            logging.info(meal_code_module)
            assert product_id and sku and product_name
            assert store and meal_code_module
            logging.info(
                "Admin/manager able to View the list of unique products belonging to the catalog assignments "
                "whose workflow status is the Approve For Association & Airline Category Mapped in the grid"
                "Following field display:"
                "Product Id"
                "Product PAC SKU"
                "Product Name "
                "Store"
                "Meal Code"
            )
            Success_List_Append(
                "test_MKTPL_2274_AssignMealcode_001_002",
                "Verify by clicking on the 'Meal Code Module' icon at left side bar",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2274_AssignMealcode_001_002",
            "Verify by clicking on the 'Meal Code Module' icon at left side bar",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2274_AssignMealcode_001_002",
            "Verify by clicking on the 'Meal Code Module' icon at left side bar",
            e,
        )
        raise e

@jamatest.test(11977541)
def test_MKTPL_2274_AssignMealcode_005(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2274_AssignMealcode_005.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_Airline(driver)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(
                By.XPATH, '//span[text()="Open Mealcode Module"]'
            ).click()
            time.sleep(5)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]")
                )
            )
            code = driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]"
            )
            code.click()
            time.sleep(5)
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]//div//input"
            ).send_keys(Keys.BACKSPACE, Keys.BACKSPACE)
            # driver.find_element(By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[2]//td[1]").click()
            code.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]//div//input",
                    )
                )
            )
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]//div//input"
            ).send_keys(Keys.BACKSPACE, Keys.BACKSPACE)
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]//div//input"
            ).send_keys(DEDICATED_MEALCODE_2_NAME)
            time.sleep(3)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//li[text()="' + DEDICATED_MEALCODE_2_NAME + ' "]')
                )
            )
            driver.find_element(
                By.XPATH, '//li[text()="' + DEDICATED_MEALCODE_2_NAME + ' "]'
            ).click()
            logging.info(f"{DEDICATED_MEALCODE_2_NAME} selected.")
            # time.sleep(5)
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[1]"
            ).click()
            SUBMIT = driver.find_element(By.XPATH, '//span[text()="Submit"]')
            driver.execute_script("arguments[0].click();", SUBMIT)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Meal Code Updated Successfully"]')
                )
            )
            # time.sleep(10)
            GRID = driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]"
            ).text
            logging.info(f"Meal Code added- {GRID}")
            assert DEDICATED_MEALCODE_2_NAME in GRID
            logging.info("Meal code data for the products is reflected in the grid")
            Success_List_Append(
                "test_MKTPL_2274_AssignMealcode_005",
                "Verify by selecting and assigning meal code to the products and clicking on the "
                "submit button",
                "Pass",
            )
            logging.info(
                "Successfully removed the Meal code from the respective product."
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2274_AssignMealcode_005",
            "Verify by selecting and assigning meal code to the products and clicking on the submit "
            "button",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2274_AssignMealcode_005",
            "Verify by selecting and assigning meal code to the products and clicking on the submit "
            "button",
            e,
        )
        raise e

@jamatest.test(11977058)
def test_MKTPL_832_Productview_001(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_832_Productview_001.png", False
        ) as driver:
            driver.maximize_window()
            # utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_CATALOG_1_ID)
            try:
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, '//span[text()="Yes"]'))
                )
                driver.find_element("xpath", '//span[text()="Yes"]').click()
            except:
                pass
            wait.until(
                EC.presence_of_element_located(("xpath", xpath.productsTabXpath))
            ).click()

            record = wait.until(
                EC.presence_of_element_located(
                    ("xpath", "(//img[@data-qtip='Open'])[1]")
                )
            )
            driver.execute_script("arguments[0].click();", record)
            driver.implicitly_wait(0)
            name_lock = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//span[text()="Name "]/span[@class="pimcore_object_label_icon pimcore_icon_gray_lock"])[2]',
                    )
                )
            ).is_displayed()
            description_lock = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="Short Description "]/span[@class="pimcore_object_label_icon pimcore_icon_gray_lock"]',
                    )
                )
            ).is_displayed()
            assert name_lock and description_lock
            logging.info(
                "Verify User is able to View Product details and it is Read only."
            )
            Success_List_Append(
                "test_MKTPL_832_Productview_001",
                "Verify User is able to View Product details",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_832_Productview_001",
            "Verify User is able to View Product details",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_832_Productview_001",
            "Verify User is able to View Product details",
            e,
        )
        raise e

@jamatest.test(11977061)
def test_MKTPL_832_Productview_004(airline_Login):
    global driver
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_832_Productview_004.png", False
        ) as driver:
             
            
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_CATALOG_1_ID)
            try:
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, '//span[text()="Yes"]'))
                )
                driver.find_element("xpath", '//span[text()="Yes"]').click()
            except:
                pass
            wait.until(
                EC.presence_of_element_located(("xpath", xpath.productsTabXpath))
            ).click()
            sku = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//img[@class="x-action-col-icon x-action-col-0  "])[1]//..//..//../td[2]//div',
                    )
                )
            ).text

            record = wait.until(
                EC.presence_of_element_located(
                    ("xpath", '(//img[@class="x-action-col-icon x-action-col-0  "])[1]')
                )
            )
            driver.execute_script("arguments[0].click();", record)
            driver.implicitly_wait(0)

            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//input[starts-with(@value, "' + sku + '")]')
                )
            )

            variants = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.variantsXpath)
                )
            ).is_displayed()

            assert variants
            logging.info(
                "Verify User is able to View Variants details and it is Read only."
            )
            Success_List_Append(
                "test_MKTPL_832_Productview_004",
                "Verify User is able to View Variants details",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_832_Productview_004",
            "Verify User is able to View Variants details",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_832_Productview_004",
            "Verify User is able to View Variants details",
            e,
        )
        raise e


def test_MKTPL_1675_CatalogAssignmentAPI_004_036_MKTPL_2431_2432_2433_CatalogassignmentAPI_011_007(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=5)
    )
    logging.info(RANDOM_NAME)
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1675_CatalogAssignmentAPI_004_036_MKTPL_2431_2432_2433_CatalogassignmentAPI_011_007.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.auto_populate_auto_map_airline_categories(driver)
            
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "yes", None)
            # Add
            payload = utils.get_RouteCatalog_Payload(
                RANDOM_NAME,
                int(DEDICATED_CATALOG_1_ID),
                int(DEDICATED_SKU_1_ID),
                int(DEDICATED_AIRLINE_CATEGORY_1_ID),
                int(DEDICATED_ROUTE_GROUP_1_ID),
                int(DEDICATED_SECTOR_1_ID),
                False,
                "2024-01-24",
                "2024-01-24",
                [],
            )

            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request(
                "POST", ROUTE_CATALOG, headers=headers, data=payload
            )
            data = response.json()
            logging.info("------ADD------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API responded with 200 status code")
            airline_category_id = data["id"]
            logging.info(f"Airline Id- {airline_category_id}")
            logging.info(
                f"Waiting for 120 sec, as API success response is taking time to update in server."
            )
            # time.sleep(120)
            # Login and verifying the object created or not.

            # utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            
            utils.search_by_id(driver, DEDICATED_STORE_IN_AIRLINE)
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.XPATH, xpath.queryXpath)))
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, f'//div[contains(text(),"{RANDOM_NAME}")]')
                    )
                )
                .is_displayed()
            )
            assert published
            logging.info(
                f"Catalog assignment go automatically under Catalog Assignment section/Store Name folder = {published}"
            )
            # GET_BY_ID
            url_id = ROUTE_CATALOG + str(airline_category_id)
            logging.info(url_id)
            response = requests.request("GET", url_id, headers=headers, data=payload)
            data_id = response.json()
            logging.info("-------GET BY ID-------")
            logging.info(data_id)
            assert response.status_code == 200 or response.status_code == 201
            catalogs_assignments = data_id.get("data", {}).get(
                "catalogsAssignments", {}
            )
            catalog_id = catalogs_assignments.get("catalog", {}).get("id")
            catalog_name = catalogs_assignments.get("catalog", {}).get("name")
            products = catalogs_assignments.get("catalog", {}).get("products")
            assignment_type = catalogs_assignments.get("assignmentType")
            catalog_from_date = catalogs_assignments.get("catalogFromDate")
            catalog_to_date = catalogs_assignments.get("catalogToDate")
            cabin_class = catalogs_assignments.get("cabinClass")
            sector = catalogs_assignments.get("routeGroup", {}).get("sector")
            logging.info(sector)
            route_group = catalogs_assignments.get("routeGroup", {}).get("name")

            assert catalog_id
            assert catalog_name
            assert products
            assert assignment_type
            assert catalog_from_date is not None
            assert catalog_to_date is not None
            assert cabin_class is not None
            assert sector
            assert route_group is not None
            logging.info(
                "Sector data  display as selected in the sector column for the particular flight in the pimcore"
            )
            logging.info(
                "API should respond with 200 status code and it should display following parameters in the response:"
                "- id"
                "- catalog id, name and products "
                "- assignmentType"
                "- catalogFromDate"
                "- catalogToDate"
                "- cabinClass"
                "- sector"
                "- routeGroup"
            )

            Success_List_Append(
                "test_MKTPL_1675_CatalogAssignmentAPI_004_036_MKTPL_2431_2432_2433_CatalogassignmentAPI_011_007",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1675_CatalogAssignmentAPI_004_036_MKTPL_2431_2432_2433_CatalogassignmentAPI_011_007",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1675_CatalogAssignmentAPI_004_036_MKTPL_2431_2432_2433_CatalogassignmentAPI_011_007",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11977753)
def test_MKTPL_2847_KIT_001(airline_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
                    False, "test_MKTPL_2847_KIT_001.png", False
                ) as driver:             
            
            wait = WebDriverWait(driver, 60)
            (
                wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Airline Operation"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                        )
                    )
                ).click()
            )
            (
                wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Airline Profile"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                        )
                    )
                ).click()
            )
            catalog = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="' + DEDICATED_AIRLINE + '"]')
                )
            )
            action = ActionChains(driver)
            action.context_click(catalog).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            (
                wait.until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//span[text()="MonthlyCatalogsLoads"])[1]')
                    )
                ).click()
            )
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//span[text()="Get Data"]')
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//span[text()="Review All & Publish"]')
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="Yes"]'))
                )
                .click()
            )
            saved_message = wait.until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Success"]'))
            ).is_displayed()
            assert saved_message
            logging.info(
                "KIT is generated from UI & API if catalog assignments for the flights are in workflow"
                " state Approved for association+Airline category mapped or Rejected for dissociation+Airline Category Mapped"
            )
            Success_List_Append(
                "test_MKTPL_2847_KIT_001",
                "Verify Catalog Assignment is created in the system, "
                "Sectors data should are auto populated in the Sector grid, "
                "All the Associated published flights are displayed in "
                "the Flight column",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2847_KIT_001",
            "Verify Catalog Assignment is created in the system, "
            "Sectors data should are auto populated in the Sector grid, "
            "All the Associated published flights are displayed in "
            "the Flight column",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2847_KIT_001",
            "Verify Catalog Assignment is created in the system, "
            "Sectors data should are auto populated in the Sector grid, "
            "All the Associated published flights are displayed in "
            "the Flight column",
            e,
        )
        raise e

@jamatest.test(11975896)
def test_MKTPL_2218_UITemplate_002(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2218_UITemplate_002.png", False
        ) as driver:
             
            
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="UI Templates"]')
                    )
                )
            )
            utils.search_by_id(driver, DEDICATED_UI_TEMPLATE_1_ID)
            read_only = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            xpath.readModeLock,
                        )
                    )
                )
                .is_displayed()
            )
            assert read_only
            logging.info(
                f"Admin/manage able to view the data of UI Template in read only mode = {read_only}"
            )
            Success_List_Append(
                "test_MKTPL_2218_UITemplate_002",
                "Verify by clicking on the UI Template",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2218_UITemplate_002",
            "Verify by clicking on the UI Template",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2218_UITemplate_002", "Verify by clicking on the UI Template", e
        )
        raise e


def test_MKTPL_3965_categorypath_005_006_007(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3965_categorypath_005_006_007.png", False
        ) as driver:
            driver.maximize_window()
            # utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            
            time.sleep(3)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.catalogAssignment)
                    )
                )
            ).click()

            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                            + DEDICATED_AIRLINE
                            + '"]',
                        )
                    )
                )
            )
            home = driver.find_element(
                By.XPATH,
                '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                + DEDICATED_AIRLINE
                + '"]',
            )
            action = ActionChains(driver)
            action.context_click(home).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(
                By.XPATH, "(//span[text()='CatalogAssignment'])[1]"
            ).click()
            ca_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                ca_name
            )
            # Click on ok
            driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {ca_name}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_STORE, Keys.ENTER
            )
            time.sleep(2)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"(//div[contains(text(),'/" + DEDICATED_STORE + "/Catalog')])[2]")))
            cred = driver.find_element(
                By.XPATH,
                "(//div[contains(text(),'/" + DEDICATED_STORE + "/Catalog')])[2]",
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            logging.info(UID)
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
           
            (WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "//li[contains(text(),'RouteGroup')]"))).click())
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(By.XPATH,
                                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div").click()
            driver.find_element(By.XPATH, "//input[contains(@name,'associateRouteGroup')]").click()
            driver.find_element(By.XPATH, "//input[contains(@name,'associateRouteGroup')]").send_keys(
                f"{DEDICATED_ROUTE_GROUP_1_NAME}")
            time.sleep(1)
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located(
                (By.XPATH, f"//li[contains(text(),'{DEDICATED_ROUTE_GROUP_1_NAME}')]"))).click())
            logging.info("Selected Route Group")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
                .click()
            )
            check_format = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//div[contains(text(),"'
                            + DEDICATED_CATEGORY_2_NAME
                            + " ("
                            + DEDICATED_STORE
                            + "/"
                            + DEDICATED_CATEGORY_3_NAME
                            + "/"
                            + DEDICATED_CATEGORY_2_NAME
                            + ')")])[1]',
                        )
                    )
                )
                .text
            )
            logging.info(check_format)
            logging.info(
                "ensure that the catalog assignment successfully binds catalog and all the products"
            )
            assert DEDICATED_CATEGORY_2_NAME in check_format
            logging.info(
                "The category and Airline category path contains key of  categories binded for the product,"
                " and they are displayed accurately in the product Tab."
            )
            # assert check_format == ''+DEDICATED_CATEGORY_2_NAME+' (/'+DEDICATED_AIRLINE+'/Category/'+DEDICATED_CATEGORY_3_NAME+'/'+DEDICATED_CATEGORY_2_NAME+'),'+DEDICATED_CATEGORY_4_NAME+' (/'+DEDICATED_AIRLINE+'/Category/'+DEDICATED_CATEGORY_4_NAME+')'
            logging.info(
                "observe in path that all the components 'Name/Store Name/Key' are displayed in a format."
            )
            try:
                RT_VALUE = utils.Delete(UID, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            Success_List_Append(
                "test_MKTPL_3965_categorypath_005_006_007",
                "Verify the catalog Assignment creation, Verify Key in Path Format",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3965_categorypath_005_006_007",
            "Verify the catalog Assignment creation, Verify Key in Path Format",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3965_categorypath_005_006_007",
            "Verify the catalog Assignment creation, Verify Key in Path Format",
            e,
        )
        raise e

@jamatest.test(11977081)
@jamatest.test(11977083)
def test_MKTPL_846_CRUDAirlinecategory_003_005(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_846_CRUDAirlinecategory_003_005.png", False
        ) as driver:
            driver.maximize_window()
            # utils.revert_auto_populate_auto_map_airline_categories(driver)
            # utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 60)
            # driver.refresh()
            # utils.wait_for_style_attribute(driver, 60)
            
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "no", None)
            try:
                for x in range(6):
                    driver.find_element(
                        By.XPATH,
                        '(//div[@class="x-tool-tool-el x-tool-img x-tool-left "])[last()]',
                    ).click()
            except:
                logging.info(" Screen loaded on one side properly.")
            driver.find_element(
                By.XPATH, '//div[text()= "Category Management"]'
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Category"]'))
            )
            CATEGORY = driver.find_element(By.XPATH, '//span[text()="Category"] ')
            action = ActionChains(driver)
            action.context_click(CATEGORY).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            ADD_OBJECT = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouseOver = ActionChains(driver)
            mouseOver.move_to_element(ADD_OBJECT).perform()
            time.sleep(1)
            AIRLINE = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            AIRLINE.click()
            driver.find_element(
                By.XPATH, '(//span[text()="AirlineCategory"])[1]'
            ).click()

            # changing
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            )
            driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.airlineProductCatAssetsTabXpath))
            )
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            time.sleep(1)
            driver.find_element(By.NAME, "name").send_keys(RANDOM_NAME)
            driver.find_element(By.XPATH, xpath.airlineProductCatAssetsTabXpath).click()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(f"Category Created: {RANDOM_NAME}")
            driver.refresh()
            utils.wait_for_style_attribute(driver, 60)
            try:
                for x in range(6):
                    driver.find_element(
                        By.XPATH,
                        '(//div[@class="x-tool-tool-el x-tool-img x-tool-left "])[last()]',
                    ).click()
            except:
                logging.info(" Screen loaded on one side properly.")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()= "Category Management"]')
                )
            )
            driver.find_element(
                By.XPATH, '//div[text()= "Category Management"]'
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//span[@class = "x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter "])[last()]',
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                '(//span[@class = "x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter "])[last()]',
            ).click()
            driver.find_element(By.NAME, "filter").send_keys(RANDOM_NAME, Keys.ENTER)
            WebDriverWait(driver, 50).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="'
                        + RANDOM_NAME
                        + '" and starts-with(@class,"x-tree-node-text  ")]',
                    )
                )
            )
            logging.info(f"Successfully added {RANDOM_NAME} under Category folder. ")
            utils.deleteObjectViaUI(driver, UID)
            
            Success_List_Append(
                "test_MKTPL_846_CRUDAirlinecategory_003_005",
                "Create the Category in Airline admin and Enter/update the data",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_846_CRUDAirlinecategory_003_005",
            "Create the Category in Airline admin and Enter/update the data",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_846_CRUDAirlinecategory_003_005",
            "Create the Category in Airline admin and Enter/update the data",
            e,
        )
        raise e


# p1.1 REMAINING


def test_MKTPL_5529_ExportImportMealcode_001_004_005(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5529_ExportImportMealcode_001_004_005.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_Airline(driver)
            # # driver.implicitly_wait(10)
            # act_title = driver.find_element(
            #     By.XPATH, xpath.logout
            # ).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, MEAL_CODE_FOLDER_AIRLINE_LOGIN)
            utils.explicitWaitUntilPresenceOfElementLocatedByXpath(driver, xpath.exportCsvXpath)
            driver.find_element(By.XPATH, xpath.exportCsvXpath).is_displayed()
            try:
                select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
                select_class.click()
                select_class.send_keys("MealCode", Keys.ENTER)
                select_class.send_keys(Keys.ENTER)
            except:
                pass
            utils.explicitWaitUntilPresenceOfElementLocatedByXpath(driver, xpath.queryXpath)
            logging.info('Export CSV button should be visible in the UI Templates>>{Airline Name} folder grid')
            driver.find_element(By.XPATH,
                                '(//div[contains(text(),"/Cred Automation Airline/Meal Code/")])[1]').click()
            time.sleep(5)
            driver.find_element(By.XPATH, xpath.exportCsvXpath).click()
            logging.info('clicked for one')
            time.sleep(5)
            path = utils.random_numer_file_download_path(LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE_EXPORT_SAMPLE)
            assert 'Mealcode' in path
            time.sleep(2)
            driver.find_element(By.XPATH,
                                '(//div[contains(text(),"/Cred Automation Airline/Meal Code/")])[1]').click()
            time.sleep(2)
            driver.find_element(By.XPATH,
                                '(//div[contains(text(),"/Cred Automation Airline/Meal Code/")])[1]').click()
            driver.find_element(By.XPATH, xpath.exportCsvXpath).click()
            time.sleep(2)
            pathOfSecondTime = utils.random_numer_file_download_path(LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE_EXPORT_SAMPLE)
            assert 'Mealcode' in pathOfSecondTime
            
            Success_List_Append("test_MKTPL_5529_ExportImportMealcode_001_004_005",
                                "Verify by selecting checkbox against single row", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5529_ExportImportMealcode_001_004_005",
                            "Verify by selecting checkbox against single row", "Fail")
        Failure_Cause_Append("test_MKTPL_5529_ExportImportMealcode_001_004_005",
                             "Verify by selecting checkbox against single row",
                             e)
        raise e


def test_MKTPL_5529_ExportImportMealcode_006(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5529_ExportImportMealcode_006.png", False
        ) as driver:
            
            
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_FOLDER_3_ID)
            logging.info("Opened")
            time.sleep(5)
            try:
                wait.until(EC.visibility_of_element_located((By.NAME, xpath.selectClassName)))
                driver.find_element(By.NAME, xpath.selectClassName).send_keys("MealCode")
                driver.find_element(By.NAME, xpath.selectClassName).send_keys(Keys.ENTER)
            except:
                pass
            num = int((len(driver.find_elements(By.XPATH, "//div[contains(@class,'checkcolumn')]")))/2)
            logging.info(num)
            # for i in range(1, num+1):
            #     driver.find_element(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)[" +str(i)+ "]" ).click()
            driver.find_element(By.XPATH, "(//div[contains(@id, 'headercontainer') and contains(@class,'ct x-docked')]//div[contains(@class, 'checkbox')]//div)[2]").click()
            time.sleep(2)
            logging.info("click on All checkbox")
            driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
            logging.info("Export CSV")
            time.sleep(5)
            try:
                pathOfSecondTime = utils.random_numer_file_download_path(
                    LOCAL_DOWNLOADED_PATH)
                logging.info("Mealcode")
                assert 'Mealcodes' in pathOfSecondTime
                with open(
                    pathOfSecondTime, "r", encoding="utf-8-sig"
                ) as file:
                    logging.info("Into")
                    csv_reader = csv.reader(file)
                    # Skip the header row
                    next(csv_reader)
                    line_count = 0
                    # Iterate over each row in the CSV file
                    for row in csv_reader:
                        logging.info(row)
                        line_count += 1
                    logging.info(line_count)
                assert line_count == num
                logging.info("Asserted all entries")
                logging.info(
                    f"CSV file should be download on the system and sample records should display in CSV"
                )
            except:
                logging.info("File not downloaded")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Please Select only Published Meal Code')]")))
                driver.find_element(By.XPATH, "//div[contains(text(),'Please Select only Published Meal Code')]")         
                driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()   
                logging.info("Validation shown")
            os.remove(pathOfSecondTime)
            Success_List_Append(
                "test_MKTPL_5529_ExportImportMealcode_006",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5529_ExportImportMealcode_006",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5529_ExportImportMealcode_006",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e


def test_MKTPL_5529_ExportImportMealcode_020(airline_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5529_ExportImportMealcode_020.png", True
        ) as driver:
            
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_FOLDER_3_ID)
            logging.info("Opened")
            try:
                wait.until(EC.visibility_of_element_located((By.NAME, xpath.selectClassName)))
                driver.find_element(By.NAME, xpath.selectClassName).send_keys("MealCode")
                driver.find_element(By.NAME, xpath.selectClassName).send_keys(Keys.ENTER)
            except:
                pass
            wait.until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(@id, 'trigger-picker') and not(contains(@style, 'none'))])")))
            logging.info("Click on dropdown")
            driver.find_element(By.XPATH, "(//div[contains(@id, 'trigger-picker') and not(contains(@style, 'none'))])").click()
            wait.until(EC.visibility_of_element_located((By.XPATH, "//li[text()='100']")))
            driver.find_element(By.XPATH, "//li[text()='100']").click()
            logging.info("Select 100")
            utils.wait_for_style_attribute(driver, 40)
            time.sleep(5)
            list = driver.find_elements(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)")
            num = len(list)
            logging.info(num)
            for i in range(1, num+1):
                    wait.until(EC.element_to_be_clickable((By.XPATH, "((//span[contains(@class, 'checked')]//ancestor::tr)["+str(i)+"]//td//div)[1]")))
                    driver.find_element(By.XPATH, "((//span[contains(@class, 'checked')]//ancestor::tr)["+str(i)+"]//td//div)[1]" ).click()
                    driver.find_element(By.XPATH, "//div[contains(text(),'Current language: English')]" ).click()
                    logging.info("((//span[contains(@class, 'checked')]//ancestor::tr)["+str(i)+"]//td//div)[1]")
            # driver.find_element(By.XPATH, "(//div[contains(@id, 'headercontainer') and contains(@class,'ct x-docked')]//div[contains(@class, 'checkbox')]//div)[2]").click()
            time.sleep(2)
            logging.info("click on checkboxes")
            driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
            logging.info("Export CSV")
            time.sleep(5)
            pathOfSecondTime = utils.random_numer_file_download_path(
                LOCAL_DOWNLOADED_PATH)
        
            logging.info("Mealcode")
            assert 'Mealcodes' in pathOfSecondTime
            with open(
                pathOfSecondTime, "r", encoding="utf-8-sig"
            ) as file:
                logging.info("Into")
                csv_reader = csv.reader(file)

                # Skip the header row
                next(csv_reader)
                line_count = 0
                # Iterate over each row in the CSV file
                for row in csv_reader:
                    logging.info(row)
                    line_count += 1
                logging.info(line_count)
                assert line_count == num 
            logging.info(
                f"CSV file should be download on the system and sample records should display in CSV"
            )
            
            os.remove(pathOfSecondTime)
            Success_List_Append(
                "test_MKTPL_5529_ExportImportMealcode_020",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5529_ExportImportMealcode_020",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5529_ExportImportMealcode_020",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

