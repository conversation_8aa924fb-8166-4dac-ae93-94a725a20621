import random
from random import randint
from datetime import datetime
from jamatest import jamatest
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import logging, os, string, time, csv, requests, json
import credencys_test_ui.credencys_project_configs.utils as utils
from selenium.webdriver.support.wait import WebDriver<PERSON>ait
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

@jamatest.test(14167699)
@jamatest.test(14167700)
@jamatest.test(14167702)
def test_MKTPL_3862_KITUIAPI_001_002_004_MKTPL_1646_1893_1894_1979_SNS_011_012():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper("test_MKTPL_3862_KITUIAPI_001_002_004_MKTPL_1646_1893_1894_1979_SNS_011_012.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//div[contains(text(),'Monthly Catalog Load')]")
                    )
                )
            )
            autom = driver.find_element(
                By.XPATH, '(//span[text()="' + DEDICATED_AIRLINE + '"])[2]'
            )
            action = ActionChains(driver)
            action.context_click(autom).perform()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(3)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(2)
            driver.find_element(
                By.XPATH, '(//span[text()="MonthlyCatalogsLoads"])[1]'
            ).click()
            logging.info("Clicked on Monthly Catalogs Loads")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.newObjectInputFieldXpath)
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            logging.info("Entered name")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//span[contains(text(),'Get Data')]")
                    )
                )
                .click()
            )
            logging.info("Clicked on Get Data")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"//span[contains(text(),'Review All & Publish')]")))
            assert driver.find_element(
                By.XPATH, "//span[contains(text(),'Review All & Publish')]"
            ).is_displayed()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Review All & Publish')]"
            ).click()
            logging.info("Clicked on Review All & Publish")
            assert driver.find_element(
                By.XPATH, "(//span[contains(text(),'Yes')])[last()]"
            ).is_displayed()
            assert driver.find_element(
                By.XPATH, xpath.noXpath
            ).is_displayed()
            driver.find_element(By.XPATH, "(//span[contains(text(),'Yes')])[last()]").click()
            logging.info("Clicked on Yes")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Monthly loaded successfully')]")
                )
            )
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                )
            )
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Save & Publish')]"
            ).click()
            logging.info("Clicked on Save & Publish")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Saved successfully!"]')
                )
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'kit_ready')]"
            )
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//div[contains(text(),'kit_ready')])[{num-1}]")
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'kit_ready')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Click on Open
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            logging.info(("Opened the entry"))
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "kit_ready")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            Success_List_Append(
                "test_MKTPL_3862_KITUIAPI_001_002_004_SNS_011_012",
                "Verify When Monthly Catalogs Loads is created via UI",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3862_KITUIAPI_001_002_004_MKTPL_1646_1893_1894_1979_SNS_011_012",
            "Verify When Monthly Catalogs Loads is created via UI",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3862_KITUIAPI_001_002_004_MKTPL_1646_1893_1894_1979_SNS_011_012",
            "Verify When Monthly Catalogs Loads is created via UI",
            e,
        )
        raise e

@jamatest.test(11977514)
@jamatest.test(11977610)
def test_MKTPL_2272_CRUDMealcode_003_MKTPL_2348_SNS_007():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper("test_MKTPL_2272_CRUDMealcode_003_MKTPL_2348_SNS_007.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            cred_automation_airline = driver.find_element(
                By.XPATH, '(//span[text()="' + DEDICATED_AIRLINE + '"])[1]'
            )
            action = ActionChains(driver)
            action.context_click(cred_automation_airline).perform()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(5)
            driver.find_element(By.XPATH, '(//span[text()="MealCode"])[1]').click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            name = (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "mealCode")))
                .send_keys(RANDOM_NAME)
            )
            utils.get_uid(driver)
            saved = utils.save_and_publish(driver)
            assert saved
            try:
                driver.find_element(
                    By.XPATH,
                    "//span[contains(text(),'Log')]//parent::div//div[contains(@class,'expander')]",
                ).is_displayed()
                driver.find_element(
                    By.XPATH, "//div[contains(text(),'utils.Assets')]"
                ).click()
            except:
                pass
            close_button = driver.find_element(
                By.XPATH, "(//span[@class='x-tab-close-btn'])[1]"
            )
            actions = ActionChains(driver)
            actions.context_click(close_button).perform()
            driver.find_element(
                By.XPATH, "(//span[contains(text(),'Close All')])[1]"
            ).click()
            logging.info("Closed all tabs")
            logging.info("Meal Code successfully created/updated in the system")
            driver.refresh()
            utils.wait_for_style_attribute(driver, 50)
            logging.info(DEDICATED_FOLDER_3_ID)
            utils.search_by_id(driver, DEDICATED_FOLDER_3_ID)
            try:
                (
                    WebDriverWait(driver, 60)
                    .until(
                        EC.presence_of_element_located(
                            (By.XPATH, '//input[@name="selectClass"]')
                        )
                    )
                    .send_keys("MealCode", Keys.ENTER)
                )
                driver.find_element(By.XPATH, '//input[@name="selectClass"]').send_keys(
                    Keys.ENTER
                )
            except:
                driver.find_element(By.XPATH, '//input[@name="query"]').click()
                pass
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(EC.element_to_be_clickable((By.XPATH, '//input[@name="query"]')))
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            listed = (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (
                            By.XPATH,
                            '//div[text()="/'
                            + DEDICATED_AIRLINE
                            + "/Meal Code/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert listed
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            meal_code = RANDOM_NAME
            product_id = DEDICATED_SKU_1_ID
            payload = json.dumps(
                [{"productId": int(product_id), "productMealCode": [f"{meal_code}"]}]
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request(
                "PATCH", UPDATE_MEALCODE_URL, headers=headers, data=payload
            )
            assert response.status_code == 200 or response.status_code == 201
            logging.info(response.text)
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears

            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{product_id}')]")
                )
            )
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys("mealcode")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{meal_code}')]"
            ).is_displayed()

            logging.info(
                "Meal Code object move automatically under Airline Operation section/Meal Code folder"
            )

            Success_List_Append(
                "test_MKTPL_2272_CRUDMealcode_003_MKTPL_2348_SNS_007",
                "Verify by entering/updating data in below fields:- Meal Code",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2272_CRUDMealcode_003_MKTPL_2348_SNS_007",
            "Verify by entering/updating data in below fields:- Meal Code",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2272_CRUDMealcode_003_MKTPL_2348_SNS_007",
            "Verify by entering/updating data in below fields:- Meal Code",
            e,
        )
        raise e

@jamatest.test(11977568)
@jamatest.test(11977608)
@jamatest.test(11977609)
def test_MKTPL_2276_AssignMealcode_016_MKTPL_2348_SNS_005_006():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_2276_AssignMealcode_016_MKTPL_2348_SNS_005_006.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(
                By.XPATH, '//span[text()="Open Mealcode Module"]'
            ).click()
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, "//legend//div[contains(text(),'Mealcode Module')]")
                    )
                )
            )
            logging.info("Opened the Mealcode module")
            code = driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]"
            )
            code.click()
            time.sleep(10)
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]//div//input"
            ).send_keys(Keys.BACKSPACE, Keys.BACKSPACE)
            time.sleep(3)
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]"
            ).click()
            code.click()
            code.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f'//li[text()="{DEDICATED_MEAL_CODE_1_NAME} "]')
                )
            )
            driver.find_element(
                By.XPATH, f'//li[text()="{DEDICATED_MEAL_CODE_1_NAME} "]'
            ).click()
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'mealCode')]//tr)[1]//td[5]"
            ).click()
            SUBMIT = driver.find_element(By.XPATH, '//span[text()="Submit"]')
            driver.execute_script("arguments[0].click();", SUBMIT)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Meal Code Updated Successfully"]')
                )
            )
            logging.info("Meal Code Updated Successfully")
            driver.find_element(
                By.XPATH, '//div[text()="Mealcode Module"]'
            ).is_displayed()
            driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE)

            logging.info(
                f"Admin/manager should be able to export the data in the CSV by clicking on the Export CSV "
                f"button"
            )
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_MEAL_CODE)
            utils.update_csv("mealcode_import.csv", "ProductId", DEDICATED_SKU_1_ID)
            utils.update_csv(
                "mealcode_import.csv", "MealCode", DEDICATED_MEALCODE_2_NAME
            )
            driver.find_element(By.XPATH, '//span[text()="Import CSV"]').click()
            time.sleep(3)

            # Upload Price Update CSV
            ele_upload = '//input[@name="mealcodefile"]'
            saved_message = utils.upload_csv("mealcode_import.csv", ele_upload)
            logging.info(saved_message)
            assert saved_message
            logging.info(
                f"Admin/manager should be able to import the data from the CSV by clicking on the Import CSV "
            )
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            # verify SNS
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open1 = driver.find_element(By.XPATH, xpath.open)
            # open1.click()
            # Open Code if modal appears

            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(),'title')]")
                )
            )
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            time.sleep(1)
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(DEDICATED_MEALCODE_2_NAME, Keys.ENTER)
            time.sleep(2)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{DEDICATED_MEALCODE_2_NAME}')]"
            ).is_displayed()
            Success_List_Append(
                "test_MKTPL_2276_AssignMealcode_016_MKTPL_2348_SNS_005_006",
                "Verify Meal code should be successfully "
                "mapped to the product in the system",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2276_AssignMealcode_016_MKTPL_2348_SNS_005_006",
            "Verify Meal code should be successfully "
            "mapped to the product in the system",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2276_AssignMealcode_016_MKTPL_2348_SNS_005_006",
            "Verify Meal code should be successfully mapped "
            "to the product in the system",
            e,
        )
        raise e

@jamatest.test(11977604)
@jamatest.test(11977605)
def test_MKTPL_2348_SNS_001_002():
    # DEDICATED_MEAL_CODE_1_NAME = 'hbacb'
    try:
        with utils.services_context_wrapper("test_MKTPL_2348_SNS_001_002.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//li[contains(@data-menu-tooltip,'All Actions')]")
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//span[contains(text(),'Open Mealcode Module')]")
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, "//legend//div[contains(text(),'Mealcode Module')]")
                    )
                )
            )
            logging.info("Opened the Mealcode module")
            (
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, '((//tr[@class="  x-grid-row"])[last()])//td[5]')
                    )
                )
            )
            time.sleep(3)
            code = driver.find_element(
                By.XPATH, '((//tr[@class="  x-grid-row"])[last()])//td[5]'
            )
            code.click()
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '((//tr[@class="  x-grid-row"])[last()])//td[5]//div//input',
                        )
                    )
                )
            )
            driver.find_element(
                By.XPATH, '((//tr[@class="  x-grid-row"])[last()])//td[5]//div//input'
            ).send_keys(Keys.BACKSPACE, Keys.BACKSPACE, Keys.BACKSPACE)
            driver.find_element(By.XPATH, "//span[text()='Mealcode Module']").click()
            time.sleep(5)
            # driver.find_element(By.XPATH, '((//tr[@class="  x-grid-row"])[5]//td)[1]').click()
            SUBMIT = driver.find_element(By.XPATH, '//span[text()="Submit"]')
            driver.execute_script("arguments[0].click();", SUBMIT)
            logging.info("Clicked on SUBMIT button")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Meal Code Updated Successfully"]')
                )
            )
            logging.info("Confirmation message shown")
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, "//legend//div[contains(text(),'Mealcode Module')]")
                    )
                )
            )
            code.click()
            code.click()
            # WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, f'//li[text()="{DEDICATED_MEAL_CODE_1_NAME} "]')))
            # driver.find_element(By.XPATH, f'//li[text()="{DEDICATED_MEAL_CODE_1_NAME} "]').click()
            logging.info("Clicked")
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '((//tr[@class="  x-grid-row"])[last()])//td[5]//div//input',
                        )
                    )
                )
            )
            driver.find_element(
                By.XPATH, '((//tr[@class="  x-grid-row"])[last()])//td[5]//div//input'
            ).send_keys(DEDICATED_MEAL_CODE_1_NAME)
            logging.info("Sent meal code")
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, f'//li[text()="{DEDICATED_MEAL_CODE_1_NAME} "]')
                )
            )
            driver.find_element(
                By.XPATH, f'//li[text()="{DEDICATED_MEAL_CODE_1_NAME} "]'
            ).click()
            time.sleep(5)
            # driver.find_element(
            #     By.XPATH, '((//tr[@class="  x-grid-row"])[2]//td)[1]'
            # ).click()
            driver.find_element(By.XPATH, "//span[text()='Mealcode Module']").click()
            logging.info("Before SUBMIT button")
            SUBMIT = driver.find_element(By.XPATH, '//span[text()="Submit"]')
            driver.execute_script("arguments[0].click();", SUBMIT)
            logging.info("Clicked on SUBMIT button")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Meal Code Updated Successfully"]')
                )
            )
            # time.sleep(10)
            logging.info("Meal Code Updated Successfully")
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            # verify SNS
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
 
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(),'title')]")
                )
            )
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(DEDICATED_MEAL_CODE_1_NAME, Keys.ENTER)
            time.sleep(3)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{DEDICATED_MEAL_CODE_1_NAME}')]"
            ).is_displayed()
 
            Success_List_Append(
                "test_MKTPL_2348_SNS_001_002",
                "Verify when Airline Category is created/updated via CSV and Airline Category is published",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2348_SNS_001_002",
            "Verify when Airline Category is created/updated via CSV and Airline Category is published",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2348_SNS_001_002",
            "Verify when Airline Category is created/updated via CSV and Airline Category is published",
            e,
        )
        raise e

# Catalog Assignment - Status required - Approved for Association and Airline Category Mapped
def test_MKTPL_1646_1893_1894_1979_SNS_015_016():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1646_1893_1894_1979_SNS_015_016.png"
        ) as driver:
            # Login through store user
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_CATALOG_2_ID)
            # Navigate to Products tab
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//span[text()='Products' and contains(@class,'inner')]",
                        )
                    )
                )
            )
            products = driver.find_element(
                By.XPATH, "//span[text()='Products' and contains(@class,'inner')]"
            )
            driver.execute_script("arguments[0].click();", products)
            logging.info("Clicked on Products")
            # Choose Update Deployment Inventory
            utils.clickOnDropDownForChooseAction(driver, DEDICATED_CATALOG_2_ID, "Update Deployment Inventory")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//span[contains(text(),'Add/Update Deployment Inventory')]",
                    )
                )
            )
            logging.info(("Navigated to Add/Update Deployment Inventory page"))
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]")
                )
            )
            random_integer = random.randint(1, 99)
            driver.find_element(
                By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]"
            ).click()
            # time.sleep(5)
            # actions = ActionChains(driver)
            # actions.key_down(Keys.CONTROL).key_down(Keys.SHIFT).send_keys(Keys.ARROW_LEFT).key_up(Keys.CONTROL).key_up(
            #     Keys.SHIFT).perform()
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]"
            ).clear()
            logging.info("Cleared")
            time.sleep(3)
            driver.find_element(
                By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]"
            ).click()
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]"
            ).send_keys(random_integer)
            driver.find_element(By.XPATH, "(//div[text()='Products'])[2]").click()
            logging.info(f"Add deployment inventory as {random_integer}")
            time.sleep(2)
            submit = driver.find_element(By.XPATH, "//span[contains(text(),'Submit')]")
            driver.execute_script("arguments[0].click();", submit)
            logging.info("Clicked on Submit")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'Sent message to sns successfully')]",
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'deployment_inventory')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{DEDICATED_CATALOG_2_ID}')]")
                )
            )
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys("quantity")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{random_integer}')]"
            ).is_displayed()
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "deployment_inventory_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            Success_List_Append(
                "test_MKTPL_1646_1893_1894_1979_SNS_015_016",
                "Verify when Deployment inventory is created via UI and catalog assignment is published",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1646_1893_1894_1979_SNS_015_016",
            "Verify when Deployment inventory is created via UI and catalog assignment is published",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1646_1893_1894_1979_SNS_015_016",
            "Verify when Deployment inventory is created via UI and catalog assignment is published",
            e,
        )
        raise e

def test_MKTPL_1646_1893_1894_1979_SNS_017_018():
    RANDOM_CODE = "".join(random.choices(string.digits, k=4))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1646_1893_1894_1979_SNS_017_018.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)

            utils.search_by_id(driver, DEDICATED_CATALOG_2_ID)

            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productsTabXpath)
                )
            )
            utils.clickOnDropDownForChooseAction(driver, DEDICATED_CATALOG_2_ID, "Export Deployment Inventory CSV")
            time.sleep(5)
            # dwnld_file = "C:\\Users\\<USER>\\Downloads\\QtyUpdate.csv"

            utils.update_csv(
                "QtyUpdate.csv", "CatalogAssignmentId", DEDICATED_CATALOG_2_ID
            )
            utils.update_csv("QtyUpdate.csv", "Qty", RANDOM_CODE)

            logging.info("Qty updated in CSV")

            utils.clickOnDropDownForChooseAction(driver, DEDICATED_CATALOG_2_ID, "Import Deployment Inventory CSV")
            
            time.sleep(10)
            file_path = '//input[@name="pricefile"]'
            saved_message = utils.upload_csv("QtyUpdate.csv", file_path)
            assert saved_message

            wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '//span[text()="Close & Reload"]')
                )
            ).click()
            time.sleep(5)
            updated_price = wait.until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[text()="' + RANDOM_CODE + '"])[1]')
                    )
                )
            ).is_displayed()

            assert updated_price

            logging.info("Updated Price can be seen in Product grid")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )

            # verify SNS
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'deployment_inventory')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears

            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{DEDICATED_CATALOG_2_ID}')]")
                )
            )
            driver.implicitly_wait(10)
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(RANDOM_CODE, Keys.ENTER)
            driver.find_element(
                By.XPATH, "//span[contains(text()," + RANDOM_CODE + ")]"
            )
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "deployment_inventory_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            Success_List_Append(
                "test_MKTPL_1646_1893_1894_1979_SNS_017_018",
                "Verify User can see popup for Import Price CSV  - Update Price in CSV - "
                "Import Updated CSV - View updated price value in product grid",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1646_1893_1894_1979_SNS_017_018",
            "Verify User can see popup for Import Price CSV  - Update Price in CSV -"
            "Import Updated CSV - View updated price value in product grid",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1646_1893_1894_1979_SNS_017_018",
            "Verify User can see popup for Import Price CSV  - Update Price in CSV - "
            "Import Updated CSV - View updated price value in product grid",
            e,
        )
        raise e

@jamatest.test(11976220)
@jamatest.test(11976221)
@jamatest.test(11976224)
def test_MKTPL_336_CRUDProduct_001_002_005_MKTPL_1646_1893_1894_1979_SNS_038_039_040():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=3)
    )
    RANDOM_NAME_PRODUCT = "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_336_CRUDProduct_001_002_005_MKTPL_1646_1893_1894_1979_SNS_038_039_040.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            wait = WebDriverWait(driver, 60)
            menu = wait.until(
                EC.presence_of_element_located(
                    (By.ID, xpath.pimcoreMenu_id)
                )
            )
            menu.click()
            product = driver.find_element(
                By.XPATH, xpath.createNewProduct
            )
            action = ActionChains(driver)
            action.move_to_element(product).click().perform()
            time.sleep(1)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[text()="Add new Object of type Product"])[1]')
                )
            )

            wait.until(EC.presence_of_element_located((By.NAME, "sku"))).send_keys(
                RANDOM_NAME, Keys.TAB
            )
            logging.info(RANDOM_NAME)
            time.sleep(1)

            cat_dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="category"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", cat_dropdown)
            time.sleep(1)
            category = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//li[text()="'
                        + DEDICATED_CATEGORY_1_NAME
                        + " ("
                        + DEDICATED_STORE
                        + "/"
                        + DEDICATED_CATEGORY_1_NAME
                        + ')"])[1]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", category)
            time.sleep(1)
            driver.execute_script("arguments[0].click();", cat_dropdown)
            time.sleep(1)

            product_set = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//input[@name="type"]//../following-sibling::div')
                )
            )
            driver.execute_script("arguments[0].click();", product_set)

            time.sleep(1)
            p_set = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//li[text()="Configurable"]')
                )
            )
            driver.execute_script("arguments[0].click();", p_set)
            logging.info("Configurable")
            driver.execute_script("arguments[0].click();", product_set)

            variant_set = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//input[contains(@name,'variant')]//../following-sibling::div",
                    )
                )
            )
            driver.execute_script("arguments[0].click();", variant_set)
            time.sleep(1)
            driver.find_element(
                By.XPATH, "//input[contains(@name,'variant')]"
            ).send_keys(Keys.ENTER)
            # v_set = wait.until(EC.presence_of_element_located(
            #     (By.XPATH, "//li[text()='Theme1 [ "+DEDICATED_CATEGORY_1_NAME+" ("+DEDICATED_STORE+") ]']")
            # ))
            # driver.execute_script("arguments[0].click();", v_set)
            # driver.implicitly_wait(0)
            # logging.info("Theme1 [ "+DEDICATED_CATEGORY_1_NAME+" ("+DEDICATED_STORE+") ]")
            # driver.execute_script("arguments[0].click();", variant_set)

            ok = driver.find_element(By.XPATH, '(//span[text()="OK"])[1]')
            logging.info(ok.is_displayed())
            driver.execute_script("arguments[0].click();", ok)

            wait.until(EC.visibility_of_element_located((By.NAME, "name"))).send_keys(
                RANDOM_NAME_PRODUCT
            )

            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            BARCODE_NAME = "".join(random.choices(string.ascii_letters, k=5))
            driver.find_element(By.NAME, "barcode").send_keys(BARCODE_NAME)
            delivery_method_dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="deliveryType"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", delivery_method_dropdown)
            add_gate = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//li[text()="Gate Pick Up"])[1]')
                )
            )
            action = ActionChains(driver)
            action.move_to_element(add_gate).click().perform()
            time.sleep(1)

            driver.execute_script("arguments[0].click();", delivery_method_dropdown)
            wait.until(
                EC.visibility_of_element_located((By.NAME, "gatePickupLeadTime"))
            ).send_keys("8")

            driver.find_element(By.NAME, "productType").send_keys("Food and Beverage")
            driver.find_element(By.XPATH, '//span[text()="Pricing & Taxation"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Price:"]'))
            )
            time.sleep(2)
            driver.find_element(
                By.XPATH, '(//span[text()="Price:"]/../../..//input)[1]'
            ).send_keys("1")
            saved = utils.save_and_publish(driver)
            assert saved
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            logging.info(num)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            # UID = DEDICATED_CATALOG_2_ID
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            # Open Code if modal appears
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[text()='{UID}']")
                )
            )
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "product_publish")
            utils.validateSnsId(driver)
            # Update
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_store(driver)
            utils.search_by_id(driver, UID)
            driver.find_element(By.NAME, "productType").send_keys("Duty Free")
            saved = utils.save_and_publish(driver)
            assert saved
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            # verify SNS
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            # UID = DEDICATED_CATALOG_2_ID
            # Open Code if modal appears
            logging.info("Opened the log file - json")

            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[text()='{UID}']")
                )
            )
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "product_publish")
            utils.validateSnsId(driver)
            # Unpublish
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_store(driver)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Unpublish')]")
                )
            )
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Unpublish')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Unpublished the Product!")
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            # verify SNS
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_unpublish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_unpublish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            # UID = DEDICATED_CATALOG_2_ID
            # Open Code if modal appears
            logging.info("Opened the log file - json")

            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[text()='{UID}']")
                )
            )
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "product_unpublish")
            utils.validateSnsId(driver)
            Success_List_Append(
                "test_MKTPL_336_CRUDProduct_001_002_005_MKTPL_1646_1893_1894_1979_SNS_038_039_040",
                "Verify by clicking on the Create New product icon in the left panel",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_336_CRUDProduct_001_002_005_MKTPL_1646_1893_1894_1979_SNS_038_039_040",
            "Verify by clicking on the Create New product icon in the left panel"
            "of the Product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_336_CRUDProduct_001_002_005_MKTPL_1646_1893_1894_1979_SNS_038_039_040",
            "Verify by clicking on the Create New product icon in the left panel",
            e,
        )
        raise e

@jamatest.test(11976468)
@jamatest.test(11976474)
@jamatest.test(11976482)
@jamatest.test(11976490)
@jamatest.test(11976495)
@jamatest.test(11976512)
def test_MKTPL_479_ProductAPI_001_007_015_023_028_045_MKTPL_1646_1893_1894_1979_SNS_043_044_045():
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    logging.info(RANDOM_NAME)
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_479_ProductAPI_001_007_015_023_028_045_MKTPL_1646_1893_1894_1979_SNS_043_044_045.png"
        ) as driver:
            # ADD
            id_product = utils.create_Simple_Product(
                "Onboard", "Duty Free", RANDOM_NAME
            )
            logging.info(f"Product Id- {id_product}")
            assert id_product != None
            # Login and verifying the object created or not.
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, id_product)
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            # verify SNS
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears

            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{id_product}')]")
                )
            )
            assert (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, f"//span[contains(text(),'{id_product}')]")
                    )
                )
            ).is_displayed()
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "product_publish")
            utils.validateSnsId(driver)
            url_id = PRODUCTS_URL + str(id_product)
            logging.info(url_id)
            # Update
            updated_name = "AC" + RANDOM_NAME
            logging.info(updated_name)
            payload = json.dumps(
                {
                    "sku": RANDOM_NAME,
                    "name": updated_name,
                    "deliveryMethod": ["Onboard"],
                    "category": [int(DEDICATED_CATEGORY_1_ID)],
                    "productSet": "Simple",
                    "productType": "Duty Free",
                }
            )
            headers = {
                "language": "en",
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("PUT", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("------UPDATE------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Updated the API responded with 200 status code")
            logging.info(f"The product is update")
            # GET_BY_ID
            payload = {}
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            }
            response = requests.request("GET", url_id, headers=headers, data=payload)
            data_id = response.json()
            logging.info("-------GET BY ID-------")
            logging.info(data_id)
            assert response.status_code == 200 or response.status_code == 201
            product = data_id["data"]["product"]
            assert "id" in product
            assert "sku" in product
            assert "barCode" in product
            assert "deliveryMethod" in product
            assert "PriceTaxation" in product
            assert "specialPrice" in product["PriceTaxation"]
            assert "cost" in product["PriceTaxation"]
            assert "isTaxable" in product["PriceTaxation"]
            assert "orderMaxQuantityAllowed" in product["PriceTaxation"]
            assert "orderMinQuantityAllowed" in product["PriceTaxation"]
            assert "name" in product
            assert "shippingAttributes" in product
            assert "assets" in product
            assert "images" in product["assets"]
            assert "gallery" in product["assets"]
            # GET
            payload = {}
            headers = {
                "language": "en",
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "GET", PRODUCTS_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------GET-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            # verify SNS
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            # utils.wait_for_style_attribute(driver, 40)
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{id_product}')]")
                )
            )
            assert (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, f"//span[contains(text(),'{id_product}')]")
                    )
                )
            ).is_displayed()
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys("AC" + RANDOM_NAME)
            # assert (driver.find_element(By.XPATH, f"//span[contains(text(),AC{RANDOM_NAME})]").is_displayed())
            assert (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, f"//span[contains(text(),'AC{RANDOM_NAME}')]")
                    )
                )
            ).is_displayed()
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "product_publish")
            utils.validateSnsId(driver)
            logging.info(
                f"Asserted the updated name from {RANDOM_NAME} to AC{RANDOM_NAME}"
            )
            # GET API for product/parent_product/varient
            URL_GET_VARIENT = (
                PRODUCTS_URL + DEDICATED_VARIENT_PARENT_PRODUCT_ID + "/varient"
            )
            payload = {}
            headers = {
                "language": "en",
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "GET", URL_GET_VARIENT, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------GET Product Varient-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            # utils.Delete
            payload = {}
            headers = {
                "language": "en",
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("DELETE", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("-------utils.Delete-------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Data cleaned up successfully.")
            # verify SNS
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_unpublish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_unpublish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{id_product}')]")
                )
            )
            assert (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, f"//span[contains(text(),'{id_product}')]")
                    )
                )
            ).is_displayed()
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "product_unpublish")
            utils.validateSnsId(driver)
            Success_List_Append(
                "test_MKTPL_479_ProductAPI_001_007_015_023_028_045_MKTPL_1646_1893_1894_1979_SNS_043_044_045",
                "Verify after successfully executing the API",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_479_ProductAPI_001_007_015_023_028_045_MKTPL_1646_1893_1894_1979_SNS_043_044_045",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_479_ProductAPI_001_007_015_023_028_045_MKTPL_1646_1893_1894_1979_SNS_043_044_045",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11975748)
@jamatest.test(11975749)
@jamatest.test(11975750)
@jamatest.test(11975751)
@jamatest.test(11975752)
@jamatest.test(11975753)
@jamatest.test(11975755)
def test_MKTPL_1551_WorkflowStatusAPI_004_005_006_007_008_009_011_MKTPL_1646_1893_1894_1979_SNS_029():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1551_WorkflowStatusAPI_004_005_006_007_008_009_011_MKTPL_1646_1893_1894_1979_SNS_029.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[contains(text(),'" + DEDICATED_STORE + "')]")
                    )
                )
            ).click()
            # Open Code if modal appears
            utils.click_on_yes_no(driver)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[text()='Configuration']")
                    )
                )
            ).click()
            x = driver.find_element(By.NAME, "autoapprovecatalog").is_selected()
            if x:
                logging.info("The store has auto approve - ON")
                driver.find_element(By.NAME, "autoapprovecatalog").click()
                assert not driver.find_element(
                    By.NAME, "autoapprovecatalog"
                ).is_selected()
                logging.info("Changed to OFF")
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                    )
                )
                save_and_publish = driver.find_element(
                    By.XPATH, "//span[contains(text(),'Save & Publish')]"
                )
                save_and_publish.click()
                # Check for the validation
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                    )
                )
            else:
                logging.info("The store has auto approve - OFF")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.catalogAssignment)
                    )
                )
            ).click()
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                            + DEDICATED_AIRLINE
                            + '"]',
                        )
                    )
                )
            )
            home = driver.find_element(
                By.XPATH,
                '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                + DEDICATED_AIRLINE
                + '"]',
            )
            action = ActionChains(driver)
            action.context_click(home).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(
                By.XPATH, "(//span[text()='CatalogAssignment'])[1]"
            ).click()
            ca_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                ca_name
            )
            # Click on ok
            ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
            ok.click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {ca_name}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, "//input[@name='query']")))
            )
            driver.find_element(By.XPATH, "//input[@name='query']").send_keys(
                "cred", Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH,
                "(//div[contains(text(),'/" + DEDICATED_STORE + "/Catalog')])[2]",
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            logging.info(UID)
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys(Keys.DOWN, Keys.ENTER)
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(Keys.DOWN, Keys.ENTER)
            logging.info("Selected Route Group")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            # Request for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "request"})
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-----Request for association------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "'Request for association' Status Api is accessible by the Airline"
            )

            # Rejected for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "reject"})
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data_ID = response.json()
            logging.info("-------Rejected for association-------")
            logging.info(data_ID)
            assert response.status_code == 200 or response.status_code == 201
            logging.info("'Rejected for association' Status Api is accessible by the Store")

            # Request for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "request"})
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-----Request for association------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # Approved for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "approve"})
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data_ID = response.json()
            logging.info("-------Approved for association-------")
            logging.info(data_ID)
            assert response.status_code == 200 or response.status_code == 201
            logging.info("'Approved for association' Status Api is accessible by the Store")
            # ==============================================================================================================
            # REQUEST FOR DISASSOCIATON FROM AIRLINE
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "requestDisassociation"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-------Request for Dissociation-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "'Request for Dissociation' Status Api is accessible by the Airline"
            )

            # Reject for Dissociation
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "rejectDisassociation"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-------Reject for Dissociation-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "'Rejected for dissociation' Status Api is accessible by the Store"
            )

            # REQUEST FOR DISASSOCIATON FROM AIRLINE
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "requestDisassociation"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-------Request for Dissociation-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # Approved for association
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "approveDisassociation"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data_ID = response.json()
            logging.info("-------Approved for dissociation-------")
            logging.info(data_ID)
            assert response.status_code == 200 or response.status_code == 201
            expected_message = "Workflow Status Updated Successfully"
            assert expected_message in response.json()["message"]
            logging.info(
                "After send request message showing 'Workflow status updated successfully'"
            )
            logging.info(
                "'Approved for dissociation' Status Api is accessible by the Airline"
            )
            logging.info("Checking SNS from UI for Approved Dissociation")
        
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_unpublish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_unpublish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "catalog_assignment_unpublish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            logging.info(
                f"Validated the Unpublish SNS for Catalog Assignment - ID {UID}"
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
        # ++++++++++++++++  request from store approve from airline ++++++++++++++++

            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.catalogAssignment)
                    )
                )
            ).click()
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                            + DEDICATED_AIRLINE
                            + '"]',
                        )
                    )
                )
            )
            home = driver.find_element(
                By.XPATH,
                '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                + DEDICATED_AIRLINE
                + '"]',
            )
            action = ActionChains(driver)
            action.context_click(home).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(
                By.XPATH, "(//span[text()='CatalogAssignment'])[1]"
            ).click()
            ca_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                ca_name
            )
            # Click on ok
            ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
            ok.click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {ca_name}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, "//input[@name='query']")))
            )
            driver.find_element(By.XPATH, "//input[@name='query']").send_keys(
                "cred", Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH,
                "(//div[contains(text(),'/" + DEDICATED_STORE + "/Catalog')])[2]",
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            logging.info(UID)
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys(Keys.DOWN, Keys.ENTER)
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(Keys.DOWN, Keys.ENTER)
            logging.info("Selected Route Group")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            # Request for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "request"})
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-----Request for association------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "'Request for association' Status Api is accessible by the Airline"
            )

            # Rejected for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "reject"})
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data_ID = response.json()
            logging.info("-------Rejected for association-------")
            logging.info(data_ID)
            assert response.status_code == 200 or response.status_code == 201
            logging.info("'Rejected for association' Status Api is accessible by the Store")

            # Request for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "request"})
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-----Request for association------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # Approved for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "approve"})
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data_ID = response.json()
            logging.info("-------Approved for association-------")
            logging.info(data_ID)
            assert response.status_code == 200 or response.status_code == 201
            logging.info("'Approved for association' Status Api is accessible by the Store")
            # ==============================================================================================================
            # REQUEST FOR DISASSOCIATON FROM AIRLINE
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "requestDisassociation"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-------Request for Dissociation-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info("'Request for Dissociation' Status Api is accessible by the Store")

            # Reject for Dissociation
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "rejectDisassociation"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-------Reject for Dissociation-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "'Rejected for dissociation' Status Api is accessible by the Airline"
            )

            # REQUEST FOR DISASSOCIATON FROM AIRLINE
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "requestDisassociation"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-------Request for Dissociation-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # Approved for disassociation
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "approveDisassociation"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("POST", WORKFLOW_URL, headers=headers, data=payload)
            data_ID = response.json()
            logging.info("-------Approved for dissociation-------")
            logging.info(data_ID)
            assert response.status_code == 200 or response.status_code == 201
            expected_message = "Workflow Status Updated Successfully"
            assert expected_message in response.json()["message"]
            logging.info(
                "After send request message showing 'Workflow status updated successfully'"
            )
            logging.info(
                "'Approved for dissociation' Status Api is accessible by the Airline"
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_unpublish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_unpublish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears

            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            logging.info(
                f"Validated the Unpublish SNS for Catalog Assignment - ID {UID}"
            )
        Success_List_Append(
            "test_MKTPL_1551_WorkflowStatusAPI_004_005_006_007_008_009_011_MKTPL_1646_1893_1894_1979_SNS_029",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1551_WorkflowStatusAPI_004_005_006_007_008_009_011_MKTPL_1646_1893_1894_1979_SNS_029",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1551_WorkflowStatusAPI_004_005_006_007_008_009_011_MKTPL_1646_1893_1894_1979_SNS_029",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11976386)
# test_MKTPL_383_CRUDUser_003
def test_MKTPL_383_CRUDUser_003():
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_PASSWORD = "".join(
        random.choices(string.ascii_letters, k=7)
    ) + "*123"
    logging.info(RANDOM_NAME)
    logging.info(RANDOM_PASSWORD)
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_383_CRUDUser_003.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Users")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "firstName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys("testlastname")
            driver.find_element(By.NAME, "isActive").click()
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            driver.find_element(By.NAME, "userType").send_keys("Airline")
            driver.find_element(By.NAME, "userRole").send_keys("airlineadmin")
            time.sleep(2)
            airline = wait.until(EC.visibility_of_element_located((By.NAME, "airline")))
            airline.clear()
            airline.send_keys(DEDICATED_AIRLINE)
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Mail Subscriptions"]')
                )
            ).click()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"User is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            utils.close_All_Tabs(driver)
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, USERS_FOLDER)
            time.sleep(3)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "(//span[contains(@class,'reload')])[1]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'reload')])[1]"
            ).click()
            driver.implicitly_wait(0)
            (
                wait.until(
                    EC.element_to_be_clickable((By.XPATH, '//input[@name="query"]'))
                ).send_keys(RANDOM_NAME, Keys.ENTER)
            )
            new_user = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="' + RANDOM_NAME + '"]')
                )
            ).is_displayed()
            assert new_user
            logging.info(
                f"New created user is moved under Airline name folder/Users folder = {new_user}"
            )
            logging.info(
                "Verify User is created/updated in the system, moved to Airline name folder/Users folder and receive an email "
                "for successfull registration."
            )
            # 009
            utils.close_All_Tabs(driver)
            driver.find_element(By.XPATH, xpath.settings).click()
            # driver.find_element(By.XPATH, xpath.usersOrRoles).click()
            action = ActionChains(driver)
            action.move_to_element(driver.find_element(By.XPATH, xpath.usersOrRoles)).perform()
            driver.find_element(By.XPATH, "(//span[text()='Users'])[last()]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//span[text()='Search']")))
            driver.find_element(By.XPATH, "//span[text()='Search']").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//input)[last()]")))
            driver.find_element(By.XPATH, "(//input)[last()]").send_keys(RANDOM_NAME)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//h3[contains(text(),'"+RANDOM_NAME+"')]")))
            driver.find_element(By.XPATH, "(//input)[last()]").send_keys(Keys.ENTER)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "password")))
            logging.info("Opened the user")
            driver.find_element(By.NAME, "password").send_keys(RANDOM_PASSWORD)
            driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
            logging.info("Saved Successfully")
            #Logout
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            driver.find_element(By.XPATH, xpath.userName).send_keys(
                    RANDOM_NAME+"@gmail.com"
                )
            driver.find_element(By.XPATH, xpath.password).send_keys(
                RANDOM_PASSWORD
            )
            driver.find_element(By.XPATH, xpath.submit).click()
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            Success_List_Append(
                "test_test_MKTPL_383_CRUDUser_003",
                "Password should be successfully created for the user",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_383_CRUDUser_003",
            "Password should be successfully created for the user",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_383_CRUDUser_003",
            "Password should be successfully created for the user",
            e,
        )
        raise e

@jamatest.test(11976376)
# test_MKTPL_382_CRUDUser_003
def test_MKTPL_382_CRUDUser_003():
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_PASSWORD = "".join(
        random.choices(string.ascii_letters, k=7)
    ) + "*123"
    logging.info(RANDOM_NAME)
    logging.info(RANDOM_PASSWORD)
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_382_CRUDUser_003.png"
        ) as driver:
            # driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Users")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "firstName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys("testlastname")
            driver.find_element(By.NAME, "isActive").click()
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            driver.find_element(By.NAME, "userType").send_keys("Store")
            driver.find_element(By.NAME, "userRole").send_keys("storeadmin")
            time.sleep(2)
            store = wait.until(EC.visibility_of_element_located((By.NAME, "store")))
            store.clear()
            store.send_keys(DEDICATED_STORE)
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Mail Subscriptions"]')
                )
            ).click()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"User is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="Home"]/..//a)[2]')
                )
            ).click()
            driver.find_element(By.NAME, "filter").send_keys(
                DEDICATED_STORE, Keys.ENTER
            )
            driver.implicitly_wait(10)
            time.sleep(1)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '(//span[text()="'
                        + DEDICATED_STORE
                        + '" and @class = "x-tree-node-text  "])//..//div[3]',
                    )
                )
            ).click()
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Users", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            driver.implicitly_wait(0)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//input[@name="query"])')
                    )
                )
            )
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[text()="' + RANDOM_NAME + '"]')
                    )
                )
                .is_displayed()
            )
            assert published
            logging.info(
                f"New created user is moved under Store name folder/Users folder = {published}"
            )
            logging.info(
                "Verify User is created/updated in the system, moved to Store name folder/Users folder and receive an email "
                "for successfull registration."
            )
            # 009
            utils.close_All_Tabs(driver)
            driver.find_element(By.XPATH, xpath.settings).click()
            # driver.find_element(By.XPATH, xpath.usersOrRoles).click()
            action = ActionChains(driver)
            action.move_to_element(driver.find_element(By.XPATH, xpath.usersOrRoles)).perform()
            driver.find_element(By.XPATH, "(//span[text()='Users'])[last()]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//span[text()='Search']")))
            driver.find_element(By.XPATH, "//span[text()='Search']").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//input)[last()]")))
            driver.find_element(By.XPATH, "(//input)[last()]").send_keys(RANDOM_NAME)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//h3[contains(text(),'"+RANDOM_NAME+"')]")))
            driver.find_element(By.XPATH, "(//input)[last()]").send_keys(Keys.ENTER)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "password")))
            logging.info("Opened the user")
            driver.find_element(By.NAME, "password").send_keys(RANDOM_PASSWORD)
            driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
            logging.info("Saved Successfully")
            #Logout
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            driver.find_element(By.XPATH, xpath.userName).send_keys(
                    RANDOM_NAME+"@gmail.com"
                )
            driver.find_element(By.XPATH, xpath.password).send_keys(
                RANDOM_PASSWORD
            )
            driver.find_element(By.XPATH, xpath.submit).click()
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            Success_List_Append(
                "test_MKTPL_382_CRUDUser_003",
                "Password should be successfully created for the user",
                "Pass"
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_382_CRUDUser_003",
            "Password should be successfully created for the user",
            "Fail"
        )
        Failure_Cause_Append(
            "test_MKTPL_382_CRUDUser_003",
            "Password should be successfully created for the user",
            e,
        )
        raise e

def test_MKTPL_5529_ExportImportMealcode_007_008_009_013_014():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_5529_ExportImportMealcode_007_008_009_013_014.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            try:
                for x in range(6):
                    driver.find_element(
                        By.XPATH,
                        '(//div[@class="x-tool-tool-el x-tool-img x-tool-left "])[last()]',
                    ).click()
            except:
                logging.info(" Screen loaded on one side properly.")
            utils.csv_import_airline(driver, 'Mealcode')
            logging.info('User navigate to the CSV import screen')
            buttonEnabled = driver.find_element(By.XPATH, xpath.mealCodeSampleFileDownloadButtonXpath)
            verifyButton = buttonEnabled.is_displayed()
            assert verifyButton
            buttonEnabled.click()
            logging.info('Download Sample file of MealCode button enabled besides import data type dropdown')
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_MEALCODE_SAMPLE)
            with open(LOCAL_DOWNLOADABLE_FILE_PATH_MEALCODE_SAMPLE, "r", encoding='utf-8-sig') as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
                    break
            assert CSV_SAMPLE_MEAL_CODE == ACTUAL_CSV_DATA
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_MEALCODE_SAMPLE)
            utils.update_csv('Mealcode.csv', 'mealcodes', '')
            utils.update_csv('Mealcode.csv', 'mealcodes', RANDOM_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Mealcode.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            utils.search_by_id(driver, MEAL_CODE_FOLDER_AIRLINE_LOGIN)
            try:
                (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.selectClassXpath))))
                select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
                select_class.click()
                select_class.send_keys("MealCode", Keys.ENTER)
                select_class.send_keys(Keys.ENTER)
            except:
                logging.info("Select class not shown")
                pass
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))))
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(RANDOM_NAME, Keys.ENTER)
            WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '(//div[contains(text(),"' + RANDOM_NAME + '")])[1]')))
            logging.info('UI Template successfully imported in the system, UI Template object move automatically under'
                         ' Master Data/UI Templates/{Airline Name} folder')
            driver.find_element(By.XPATH,
                                '//span[text()="CSV Import" and @class= "x-tab-inner x-tab-inner-default"]').click()
            utils.update_csv('Mealcode.csv', 'mealcodes', DEDICATED_MEALCODE_2_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Mealcode.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC AIRLINE")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.Assets_import_Airline(driver)
            utils.verify_import_logs_airline(driver, "mealcode_", 'Row Inserted\/updated Successfully')
            today_date = datetime.now().strftime("%d.%m.%Y")
            logging.info(today_date)
            dateText = driver.find_element(By.XPATH, xpath.logsTimeVerifyXpath).text
            logging.info(dateText)
            assert today_date in dateText
            logging.info('014. It updated the old object after csv import')

            logging.info('CSV file download on the system and sample records display in CSV')
            Success_List_Append("test_MKTPL_5529_ExportImportMealcode_007_008_009_013_014",
                                "Verify by clicking on the CSV import icon in the left side menu", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5529_ExportImportMealcode_007_008_009_013_014",
                            "Verify by clicking on the CSV import icon in the left side menu", "Fail")
        Failure_Cause_Append("test_MKTPL_5529_ExportImportMealcode_007_008_009_013_014",
                             "Verify by clicking on the CSV import icon in the left side menu",
                             e)
        raise e

def test_MKTPL_4960_Generate_Compareprofile_028_029_030_031_032_033_034_035_036():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_4960_Generate_Compareprofile_028_029_030_031_032_033_034_035_036.png"
        ) as driver:
            
            driver.maximize_window()
            # 029
            utils.Pac_Credentials.Login_Unpublished_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_UNPUBLISHED_AIRLINE_ID)
            assert not len(driver.find_elements(By.XPATH, xpath.generateProfileXpath))
            assert not len(driver.find_elements(By.XPATH, xpath.compareProfileXpath))
            logging.info("Verified - Airline Object should be open and Both buttons Generate Profile and Compare Profile should not be dispalyed")
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 40)
            # 028
            utils.search_by_id(driver, DEDICATED_AIRLINE_ID)
            assert driver.find_element(By.XPATH, xpath.generateProfileXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.compareProfileXpath).is_displayed()
            logging.info("Verified - Airline Object should be open and Both buttons Generate Profile and Compare Profile should be dispalyed")
            # 030
            driver.find_element(By.XPATH, xpath.generateProfileXpath).click()
            time.sleep(2)
            pathOfSecondTime = utils.random_numer_file_download_path(
                LOCAL_DOWNLOADED_PATH)
            logging.info("airline")
            assert 'airline' in pathOfSecondTime
            # 031
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            assert driver.find_element(By.XPATH, xpath.importAirlineData).is_displayed()
            logging.info("Asserted the import airline pop up")
            # 032
            path = os.getcwd() + "/credencys_test_ui/" + "Airports_Sample.csv"
            time.sleep(5)
            close = driver.find_element(By.XPATH, "(//div[@data-qtip= 'Close panel']//div)[last()]")
            close.click()
            time.sleep(3)
            logging.info("Clicked on close button")
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the invalid file")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="File Import: Only json file is allowed."]')))
            logging.info("Validation shown: File Import: Only json file is allowed.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # 033
            path = os.getcwd() + "/credencys_test_ui/" + "Empty_json.json"
            time.sleep(5)
            close = driver.find_element(By.XPATH, "(//div[@data-qtip= 'Close panel']//div)[last()]")
            close.click()
            time.sleep(3)
            logging.info("Clicked on close button")
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the empty json file")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="The provided file is blank."]')))
            logging.info("Validation shown: The provided file is blank.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # 034
            path = os.getcwd() + "/credencys_test_ui/" + "Invalid_json.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the invalid json")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="JSON Import: Invalid json format."]')))
            logging.info("Validation shown: JSON Import: Invalid json format.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()            
            # 035
            path = os.getcwd() + "/credencys_test_ui/" + "CredAutomationAirlineEdited.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(3)
            logging.info("Uploaded the valid json with changes")
            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(5)
            textChanges = driver.find_element(By.NAME, "jsonImportMessage").get_attribute("value")
            logging.info(textChanges)
            assert '"from":' in textChanges
            assert '"to":' in textChanges
            logging.info("File should be uploaded successfully and Changes should be displayed")
            # 036
            # path = os.getcwd() + "/credencys_test_ui/" + "CredAutomationAirline.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(pathOfSecondTime)
            time.sleep(3)
            logging.info("Uploaded the valid json")
            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(3)
            textChanges = driver.find_element(By.NAME, "jsonImportMessage").get_attribute("value")
            logging.info(textChanges)
            assert '"changes": []' in textChanges
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            logging.info("File should be uploaded successfully and Changes should be displayed as empty")
            os.remove(pathOfSecondTime)
            Success_List_Append(
                "test_MKTPL_4960_Generate_Compareprofile_028_029_030_031_032_033_034_035_036",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_4960_Generate_Compareprofile_028_029_030_031_032_033_034_035_036",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_4960_Generate_Compareprofile_028_029_030_031_032_033_034_035_036",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

@jamatest.test(16359443)
@jamatest.test(16359444)
@jamatest.test(16359445)
@jamatest.test(16359446)
@jamatest.test(16359447)
def test_MKTPL_6315_ReviewOptionsProvider_001_002_003_004_005():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_6315_ReviewOptionsProvider_001_002_003_004_005.png"
        ) as driver:
            logging.info(os.name)
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            # 001 "Verify Removal of Options Provider from Sector in Flight"
            # //li[@id="pimcore_menu_settings"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.settingsIcon_li)))
            driver.find_element(By.XPATH, xpath.settingsIcon_li).click()
            # //span[text()="Data Objects"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.dataObjects_span)))
            driver.find_element(By.XPATH, xpath.dataObjects_span).click()
            # //span[text()="Classes"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.classes_span)))
            driver.find_element(By.XPATH, xpath.classes_span).click()
            # //span[text()="Flight (FLT)"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Flight (FLT)"]')))
            driver.find_element(By.XPATH, '//span[text()="Flight (FLT)"]').click()
            # //span[text()="sector"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="sector"]')))
            driver.find_element(By.XPATH, '//span[text()="sector"]').click()
            # //input[@name="optionsProviderClass"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="optionsProviderClass"]')))
            assert len(driver.find_element(By.XPATH, '//input[@name="optionsProviderClass"]').get_attribute("value")) == 0
            # //input[@name="optionsProviderData"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="optionsProviderData"]')))
            assert len(driver.find_element(By.XPATH, '//input[@name="optionsProviderData"]').get_attribute("value")) == 0
            logging.info("Verified The Options Provider field should be empty.")

            # 002 "Verify Removal of Options Provider in sector from Custom Layout"
            customLayoutElementsData = []
            # //span[text()="Configure Custom Layouts"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Configure Custom Layouts"]')))
            driver.find_element(By.XPATH, '//span[text()="Configure Custom Layouts"]').click()
            # (//span[text()="Layout"]//ancestor::div[@class="x-grid-item-container"])[2]//table//span
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Layout"]')))
            customLayoutElements = driver.find_elements(By.XPATH, '(//span[text()="Layout"]//ancestor::div[@class="x-grid-item-container"])[2]//table//span')
            for element in customLayoutElements:
                customLayoutElementsData.append(element.text)
            assert "Options Provider" not in customLayoutElementsData
            # //div[contains(@class, "x-tool-close")]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[contains(@class, "x-tool-close")]')))
            driver.find_element(By.XPATH, '//div[contains(@class, "x-tool-close")]').click()
            logging.info("Verified Options Provider should be removed from the Custom Layout.")

            # 003 "Verify that when a flight object is created with an initial airline selection, the corresponding sector value is set correctly."
            # utils.close_All_Tabs(driver= driver)
            # //span[text()="Home"]
            utils.search_by_path(driver, '/Cred Automation Airline/Sectors')
            # //div[contains(text(), "/Cred Automation Airline/Sectors")]//ancestor::tr//td[4]//span[contains(@class, "checked")]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f'//div[contains(text(), "/{DEDICATED_AIRLINE}/Sectors")]')))
            checked_sectors = driver.find_elements(By.XPATH, f'//div[contains(text(), "/{DEDICATED_AIRLINE}/Sectors")]//ancestor::tr//td[4]//span[contains(@class, "checked")]')
            logging.info("Before Close all tabs")
            utils.close_All_Tabs(driver= driver)
            
            utils.search_by_id(driver= driver,
                               id= DEDICATED_FLIGHT_1_ID)
            # //input[@name="sector"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="sector"]')))
            # (//input[@name="sector"])[1]//parent::div//following-sibling::div
            driver.find_element(By.XPATH, '(//input[@name="sector"])[1]//parent::div//following-sibling::div').click()
            # //li[text()="Automation_Sector"]//parent::ul//li
            flightSectors = driver.find_elements(By.XPATH, f'//li[contains(text(),"{DEDICATED_SECTOR_1_NAME}")]//parent::ul//li')
            assert len(checked_sectors) <= len(flightSectors)
            # Reload
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.reloadXpath)))
            driver.find_element(By.XPATH, xpath.reloadXpath).click()
            logging.info("Verified the sector value is set correctly based on the chosen airline option.")

            # 004 "Verify Automatic Update of Sector Value when Changing Airline Selection"
            # (//input[@name="sector"])[1]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//input[@name="sector"])[1]')))
            value = driver.find_element(By.XPATH, '(//input[@name="sector"])[1]').get_attribute("value")
            # //span[contains(@class, "pimcore_icon_search")]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.searchIconName)))
            driver.find_element(By.XPATH, xpath.searchIconName + "//ancestor::a").click()
            # //input[@name="query"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.queryXpath)))
            query_input = driver.find_element(By.XPATH, xpath.queryXpath)
            query_input.click()
            query_input.send_keys("Cred")
            query_input.send_keys(Keys.ENTER)
            # //div[text()="Cred Automation Airline No Store"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, f'//div[text()="{DEDICATED_AIRLINE_NO_STORE}"]')))
            x = driver.find_element(By.XPATH, f'//div[text()="{DEDICATED_AIRLINE_NO_STORE}"]')
            action = ActionChains(driver)
            action.double_click(x).perform()
            time.sleep(3)
            # (//input[@name="sector"])[1]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//input[@name="sector"])[1]')))
            assert value not in driver.find_element(By.XPATH, '(//input[@name="sector"])[1]').get_attribute("value")
            logging.info("6")
            # Reload
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.reloadXpath)))
            driver.find_element(By.XPATH, xpath.reloadXpath).click()
            logging.info("Verified The updated sector value corresponds accurately to the selected airline.")
            
            
            # 006 "Verify that when a flight object is created with an initial airline selection, the corresponding sector value is set correctly."
            RANDOM_NAME_1 = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            RANDOM_NAME_2 = random.choices(string.ascii_letters, k=6)
            # logout
            driver.find_element(By.XPATH, xpath.logout).click()
            # login airline
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            
            # //div[text()="Airline Operation"]//ancestor::div[contains(@class, "x-grid")]//span[text()="Cred Automation Airline"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="Airline Operation"]//ancestor::div[contains(@class, \
                    "x-grid")]//span[text()="Cred Automation Airline"]')))
            # //div[text()="Airline Operation"]//ancestor::div[contains(@class, "x-grid")]//span[text()="Flight"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="Airline Operation"]//ancestor::div[contains(@class, "x-grid")]//span[text()="Flight"]')))
            x = driver.find_element(By.XPATH, '//div[text()="Airline Operation"]//ancestor::div[contains(@class, "x-grid")]//span[text()="Flight"]')
            actions = ActionChains(driver)
            actions.context_click(x).perform()
            logging.info(f"Right clicked on Flight")
            # //span[text()="Add Object"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath)))
            driver.find_element(By.XPATH, xpath.addObjectXpath).click()
            time.sleep(1)
            # //div[contains(@class, "x-menu-bodyWrap")]//span[text()="Flight"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[contains(@class, "x-menu-bodyWrap")]//span[text()="Flight"]')))
            driver.find_element(By.XPATH, '//div[contains(@class, "x-menu-bodyWrap")]//span[text()="Flight"]').click()
            # (//div[text()="Enter the name of the new item"])[1]//parent::div//input
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//div[text()="Enter the name of the new item"])[1]//parent::div//input')))
            driver.find_element(By.XPATH, '(//div[text()="Enter the name of the new item"])[1]//parent::div//input').send_keys(RANDOM_NAME_1)
            # //span[text()="OK"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.okButtonXpath)))
            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            # //input[@name="flightRouteNumber"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="flightRouteNumber"]')))
            driver.find_element(By.XPATH, '//input[@name="flightRouteNumber"]').send_keys(RANDOM_NAME_2)
            # //span[contains(@class, "pimcore_icon_plus")]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[contains(@class, "pimcore_icon_plus")]')))
            driver.find_element(By.XPATH, '//span[contains(@class, "pimcore_icon_plus")]').click()
            time.sleep(1)
            # //input[@name="sector"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="sector"]')))
            sector_input = driver.find_element(By.XPATH, '//input[@name="sector"]')
            sector_input.send_keys(DEDICATED_SECTOR_1_NAME)
            # assert //li[text()="Automation_Sector"]
            assert driver.find_element(By.XPATH, "//li[contains(text(),'"+DEDICATED_SECTOR_1_NAME+"')]")
            sector_input.send_keys(Keys.ENTER)
            # //input[@name="sector_sequence"]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="sector_sequence"]')))
            driver.find_element(By.XPATH, '//input[@name="sector_sequence"]').send_keys("1")
            # Clicked on Save
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            # Saved Successfully
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            assert driver.find_element(By.XPATH, xpath.saveSuccessfullyMessageXpath)
            logging.info("Verified the sector value is set correctly based on the Existing airline Profile.")

            time.sleep(5)
            Success_List_Append(
                "test_MKTPL_6315_ReviewOptionsProvider_001_002_003_004_005",
                "Verify Removal of Options Provider from Sector in Flight",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_6315_ReviewOptionsProvider_001_002_003_004_005",
            "Verify Removal of Options Provider from Sector in Flight",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_6315_ReviewOptionsProvider_001_002_003_004_005",
            "Verify Removal of Options Provider from Sector in Flight",
            e,
        )
        raise e

@jamatest.test(15082595)
@jamatest.test(15082596)
def test_MKTPL_4865_Productdate_002_003():
    try:
        with utils.services_context_wrapper("test_MKTPL_4865_Productdate_002_003.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
 
            # 002
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
 
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()
 
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
 
            category = driver.find_element(By.NAME, 'category[]')
            category.click()
            category.send_keys(Keys.DOWN, Keys.DOWN)
           
            driver.find_element(By.XPATH, "//li[contains(text(),'"+DEDICATED_AIRLINE_CATEGORY_1_NAME+"')]").click()
            # category.send_keys(Keys.ENTER)
 
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Download Sample File Of Products"]')))
            driver.find_element(By.XPATH, '//span[text()="Download Sample File Of Products"]').click()
 
            os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE)
            date_obj = datetime.strptime('2024-05-08', '%Y-%m-%d')
           
            utils.update_csv("Product_Sample.csv", "newFrom", date_obj)
            utils.update_csv("Product_Sample.csv", "newTo", date_obj)
            path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()
 
            utils.update_csv("Product_Sample.csv", "newFrom", "16-06-2027")
            utils.update_csv("Product_Sample.csv", "newTo", "20-06-2027")
 
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()
 
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
 
            utils.Assets_import_Store(driver)
            time.sleep(3)
            list1 = driver.find_elements(By.XPATH, '(//div[contains(text(),"/Import/'+PAC_STORE_USER_MAIL+'/products")])')
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(By.XPATH, '(//div[contains(text(),"/Import/'+PAC_STORE_USER_MAIL+'/products")])["'+str(num)+'"]')
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            time.sleep(3)
            # open1 = driver.find_element(By.XPATH, "//span[contains(text(),'Open')]")
            # open1.click()
            wait = WebDriverWait(driver, 60)
            logging.info("Opened the log file - json")
            logging.info('"Failed Order Log" is created for the order with invalid json.')
            assert wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[contains(text(),"newFrom and newTo date should be different")]'))).is_displayed()
            # assert wait.until(
            #     EC.visibility_of_element_located(
            #         (By.XPATH, '//div[contains(text(),"Invalid Date format in newTo")]'))).is_displayed()
 
            logging.info(' 002 System should validate "From and To date should be different"with a failure message.')
            utils.close_All_Tabs(driver)
 
            # 003
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
 
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()
 
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(1)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.storeName)))
            store = driver.find_element(By.NAME, xpath.storeName)
            store.send_keys(DEDICATED_STORE)
            store.send_keys(Keys.ENTER)
            time.sleep(1)
            category = driver.find_element(By.NAME, 'category[]')
            category.click()
            category.send_keys(Keys.DOWN, Keys.DOWN)            
            driver.find_element(By.XPATH, "//li[contains(text(),'"+DEDICATED_AIRLINE_CATEGORY_1_NAME+"')]").click()
 
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Download Sample File Of Products"]')))
            driver.find_element(By.XPATH, '//span[text()="Download Sample File Of Products"]').click()
 
            os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE)
 
            utils.update_csv("Product_Sample.csv", "newFrom", "23-54-2546")
            utils.update_csv("Product_Sample.csv", "newTo", "12-23-6521")
            path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()
 
            utils.update_csv("Product_Sample.csv", "newFrom", "16-06-2027")
            utils.update_csv("Product_Sample.csv", "newTo", "20-06-2027")
 
            utils.Assets_import_Pac_admin(driver)
            time.sleep(3)
            list1 = driver.find_elements(By.XPATH,
                                         '(//div[contains(text(),"/Import/'+PAC_USER_MAIL+'/products")])')
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(By.XPATH,
                                      '(//div[contains(text(),"/Import/'+PAC_USER_MAIL+'/products")])["'+str(num)+'"]')
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            time.sleep(3)
            # open1 = driver.find_element(By.XPATH, "//span[contains(text(),'Open')]")
            # open1.click()
            wait = WebDriverWait(driver, 60)
            logging.info("Opened the log file - json")
            logging.info('"Failed Order Log" is created for the order with invalid json.')
 
            assert wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[contains(text(),"Invalid Date format in newFrom")]'))).is_displayed()
            assert wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[contains(text(),"Invalid Date format in newTo")]'))).is_displayed()
 
            logging.info(' 003 System should validate "From and To date should be different"with a failure message.')
 
            Success_List_Append(
                "test_MKTPL_4865_Productdate_002_003",
                "Verify the newFrom & newTo fields in product",
                "Pass")
 
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_4865_Productdate_002_003",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_4865_Productdate_002_003",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_1646_1893_1894_1979_SNS_041_MKTPL_1646_1893_1894_1979_SNS_042():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=4)
        )
        with utils.services_context_wrapper("test_MKTPL_1646_1893_1894_1979_SNS_041_MKTPL_1646_1893_1894_1979_SNS_042.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            # 041
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            # data_type.send_keys(Keys.ENTER)
            data_type.send_keys(Keys.TAB, Keys.DOWN, Keys.DOWN)
            # category = driver.find_element(By.XPATH, "//span[contains(text(),'Category')]//ancestor::label//following-sibling::div//div[contains(@id, 'trigger-picker')]")
            # driver.execute_script("arguments[0].click();", category)
            # category.send_keys(DEDICATED_AIRLINE_CATEGORY_1_NAME)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//li[contains(text(),"'+ DEDICATED_CATEGORY_1_NAME+'")]'
                        )
                    )
                )
                .click()
            )

            utils.update_csv("Product_Sample.csv", "name_en", RANDOM_NAME)
            utils.update_csv("Product_Sample.csv", "newFrom", "2027-03-03")
            utils.update_csv("Product_Sample.csv", "newTo", "2027-09-02")
            path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.Assets_import_Store(driver)
            time.sleep(3)
            list1 = driver.find_elements(By.XPATH, f'(//div[contains(text(),"/Import/{PAC_STORE_USER_MAIL}/products")])')
            num = len(list1)
            row = driver.find_element(By.XPATH, f'(//div[contains(text(),"/Import/{PAC_STORE_USER_MAIL}/products")])[{num}]')
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # time.sleep(3)
            # open1 = driver.find_element(By.XPATH, "//span[contains(text(),'Open')]")
            # open1.click()
            # wait = WebDriverWait(driver, 60)
            logging.info("Opened the log file - json")
            logging.info('"Failed Order Log" is created for the order with invalid json.')

            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//div[@class="ace_line"])[4]')))
            msg = driver.find_element(By.XPATH,
                                    '(//div[@class="ace_line"])[4]').text
            logging.info(msg)
            assert "Row Inserted\/updated Successfully" in msg

            logging.info('SNS verified')
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            # 042
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.TAB, Keys.DOWN, Keys.DOWN)

            # category = driver.find_element(By.NAME, 'category[]')
            # category.send_keys(DEDICATED_AIRLINE_CATEGORY_1_NAME)
            # category.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//li[contains(text(),"'+ DEDICATED_CATEGORY_1_NAME+'")]'
                        )
                    )
                )
                .click()
            )

            utils.update_csv("Product_Sample.csv", "name_en", RANDOM_NAME)
            utils.update_csv("Product_Sample.csv", "newFrom", "2027-03-03")
            utils.update_csv("Product_Sample.csv", "newTo", "2027-09-02")
            path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.Assets_import_Store(driver)
            time.sleep(3)
            list1 = driver.find_elements(By.XPATH, f'(//div[contains(text(),"/Import/{PAC_STORE_USER_MAIL}/products")])')
            num = len(list1)
            # time.sleep(3)
            row = driver.find_element(By.XPATH, f'(//div[contains(text(),"/Import/{PAC_STORE_USER_MAIL}/products")])[{num}]')
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # time.sleep(3)
            # open1 = driver.find_element(By.XPATH, "//span[contains(text(),'Open')]")
            # open1.click()
            # wait = WebDriverWait(driver, 60)
            logging.info("Opened the log file - json")
            logging.info('"Failed Order Log" is created for the order with invalid json.')

            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//div[@class="ace_line"])[4]')))
            msg = driver.find_element(By.XPATH,
                                    '(//div[@class="ace_line"])[4]').text
            logging.info(msg)
            assert "Row Inserted\/updated Successfully" in msg

            logging.info('SNS verified')
            Success_List_Append(
                "test_MKTPL_1646_1893_1894_1979_SNS_041_MKTPL_1646_1893_1894_1979_SNS_042",
                "Verify When a new product (base product or variant) is created via CSV",
                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1646_1893_1894_1979_SNS_041_MKTPL_1646_1893_1894_1979_SNS_042",
                            "Verify When a new product (base product or variant) is created via CSV",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_1646_1893_1894_1979_SNS_041_MKTPL_1646_1893_1894_1979_SNS_042",
                             "Verify When a new product (base product or variant) is created via CSV",
                             e)
        raise e

@jamatest.test(15637270)
@jamatest.test(15637271)
def test_MKTPL_4967_datetimeinproduct_001_MKTPL_4967_datetimeinproduct_002_MKTPL_4950_Product_001_MKTPL_4950_Product_002():
    try:
        RANDOM_NAME = "".join(
            random.choices(string.ascii_letters, k=7)
        )
        with utils.services_context_wrapper(
            "test_MKTPL_4967_datetimeinproduct_001_MKTPL_4967_datetimeinproduct_002_MKTPL_4950_Product_001_MKTPL_4950_Product_002.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_SKU_1_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "name"))
            )
            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            driver.find_element(By.NAME, "newFrom").click()
            driver.find_element(By.NAME, "newFrom").clear()
            driver.find_element(By.NAME, "newFrom").send_keys("2021-02-02")
            driver.find_element(By.NAME, "newTo").click()
            driver.find_element(By.NAME, "newTo").clear()
            driver.find_element(By.NAME, "newTo").send_keys("2022-02-02")
            driver.find_element(By.NAME, "barcode").send_keys(RANDOM_NAME)
            utils.save_and_publish(driver)
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            wait.until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, f"//span[contains(text(),'{DEDICATED_SKU_1_ID}')]"))
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[text()='{DEDICATED_SKU_1_ID}']"
            ).is_displayed()
            newFrom = driver.find_element(By.XPATH, "(//span[contains(text(),'newFrom')]//parent::div//span)[last()]").text
            newTo = driver.find_element(By.XPATH, "(//span[contains(text(),'newTo')]//parent::div//span)[last()]").text
            newFromgmt = time.gmtime(int(newFrom))
            newTogmt = time.gmtime(int(newTo))
            logging.info(newFromgmt)
            logging.info(newTogmt)
            assert newFromgmt.tm_hour == 0
            assert newFromgmt.tm_sec == 0
            assert newTogmt.tm_hour == 23
            assert newTogmt.tm_sec == 59
            logging.info("Asserted the SNS")

            # 002
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.TAB, Keys.DOWN, Keys.DOWN)

            # category = driver.find_element(By.NAME, 'category[]')
            # category.send_keys(DEDICATED_AIRLINE_CATEGORY_1_NAME)
            # category.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//li[contains(text(),"'+ DEDICATED_CATEGORY_1_NAME+'")]'
                        )
                    )
                )
                .click()
            )

            utils.update_csv("Product_Sample.csv", "name_en", DEDICATED_SKU_1_NAME)
            utils.update_csv("Product_Sample.csv", "newFrom", "2021-03-03")
            utils.update_csv("Product_Sample.csv", "newTo", "2021-09-02")
            path = os.getcwd() + "/credencys_test_ui/" + "Product_Sample.csv"
            logging.info(path)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.find_element(By.XPATH, xpath.upload).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.Assets_import_Store(driver)
            time.sleep(3)
            list1 = driver.find_elements(By.XPATH, f'(//div[contains(text(),"/Import/{PAC_STORE_USER_MAIL}/products")])')
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(By.XPATH, f'(//div[contains(text(),"/Import/{PAC_STORE_USER_MAIL}/products")])[{num}]')
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            time.sleep(3)
            # open1 = driver.find_element(By.XPATH, "//span[contains(text(),'Open')]")
            # open1.click()
            wait = WebDriverWait(driver, 60)
            logging.info("Opened the log file - json")

            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//div[@class="ace_line"])[4]')))
            msg = driver.find_element(By.XPATH,
                                    '(//div[@class="ace_line"])[4]').text
            logging.info(msg)
            assert "Row Inserted\/updated Successfully" in msg
            logging.info("Past newFrom and newTo date allowed")
            utils.close_All_Tabs(driver)
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'product_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'product_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            time.sleep(5)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, "(//span[contains(text(),'newFrom')]//parent::div//span)[last()]"))
            )
            
            newFrom = driver.find_element(By.XPATH, "(//span[contains(text(),'newFrom')]//parent::div//span)[last()]").text
            newTo = driver.find_element(By.XPATH, "(//span[contains(text(),'newTo')]//parent::div//span)[last()]").text
            newFromgmt = time.gmtime(int(newFrom))
            newTogmt = time.gmtime(int(newTo))
            logging.info(newFromgmt)
            logging.info(newTogmt)
            assert newFromgmt.tm_hour == 0
            assert newFromgmt.tm_sec == 0
            assert newTogmt.tm_hour == 23
            assert newTogmt.tm_sec == 59
            logging.info("Asserted the SNS")
            logging.info('SNS verified')
            Success_List_Append(
                "test_MKTPL_4967_datetimeinproduct_001_MKTPL_4967_datetimeinproduct_002_MKTPL_4950_Product_001_MKTPL_4950_Product_002",
                "Verify the newFrom & newTo parameter in product_publish SNS via UI",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_4967_datetimeinproduct_001_MKTPL_4967_datetimeinproduct_002_MKTPL_4950_Product_001_MKTPL_4950_Product_002",
            "Verify the newFrom & newTo parameter in product_publish SNS via UI",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_4967_datetimeinproduct_001_MKTPL_4967_datetimeinproduct_002_MKTPL_4950_Product_001_MKTPL_4950_Product_002",
            "Verify the newFrom & newTo parameter in product_publish SNS via UI",
            e,
        )
        raise e

def test_MKTPL_4960_Generate_Compareprofile_019_020_021_022_023_024_025_026_027():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_4960_Generate_Compareprofile_019_020_021_022_023_024_025_026_027.png"
        ) as driver:
            
            driver.maximize_window()
            
            # 019
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_UNPUBLISHED_STORE_ID)
            assert not len(driver.find_elements(By.XPATH, xpath.generateProfileXpath))
            assert not len(driver.find_elements(By.XPATH, xpath.compareProfileXpath))
            logging.info("Verified - Store Object should be open and Both buttons Generate Profile and Compare Profile should not be dispalyed")
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 40)
            # 020
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            assert driver.find_element(By.XPATH, xpath.generateProfileXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.compareProfileXpath).is_displayed()
            logging.info("Verified - Store Object should be open and Both buttons Generate Profile and Compare Profile should be dispalyed")
            # 021
            driver.find_element(By.XPATH, xpath.generateProfileXpath).click()
            time.sleep(10)
            pathOfSecondTime = utils.random_numer_file_download_path(
                LOCAL_DOWNLOADED_PATH)
            logging.info("store")
            assert 'store' in pathOfSecondTime
            # 022
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            assert driver.find_element(By.XPATH, xpath.importAirlineData).is_displayed()
            logging.info("Asserted the import airline pop up")
            # 023
            path = os.getcwd() + "/credencys_test_ui/" + "Airports_Sample.csv"
            time.sleep(5)
            close = driver.find_element(By.XPATH, "(//div[@data-qtip= 'Close panel']//div)[last()]")
            close.click()
            time.sleep(3)
            logging.info("Clicked on close button")
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the invalid file")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="File Import: Only json file is allowed."]')))
            logging.info("Validation shown: File Import: Only json file is allowed.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # 024
            path = os.getcwd() + "/credencys_test_ui/" + "Empty_json.json"
            time.sleep(5)
            close = driver.find_element(By.XPATH, "(//div[@data-qtip= 'Close panel']//div)[last()]")
            close.click()
            time.sleep(3)
            logging.info("Clicked on close button")
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the empty json file")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="The provided file is blank."]')))
            logging.info("Validation shown: The provided file is blank.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # 025
            path = os.getcwd() + "/credencys_test_ui/" + "Invalid_json.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the invalid json")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="JSON Import: Invalid json format."]')))
            logging.info("Validation shown: JSON Import: Invalid json format.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()            
            # 026
            path = os.getcwd() + "/credencys_test_ui/" + "CredAutomationStoreEdited.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(3)
            logging.info("Uploaded the valid json with changes")
            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(3)
            textChanges = driver.find_element(By.NAME, "jsonImportMessage").get_attribute("value")
            logging.info(textChanges)
            assert '"from":' in textChanges
            assert '"to":' in textChanges
            logging.info("File should be uploaded successfully and Changes should be displayed")
            # 027
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(pathOfSecondTime)
            time.sleep(3)
            logging.info("Uploaded the valid json")
            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(3)
            textChanges = driver.find_element(By.NAME, "jsonImportMessage").get_attribute("value")
            logging.info(textChanges)
            assert '"changes": []' in textChanges
            os.remove(pathOfSecondTime)
            logging.info("File should be uploaded successfully and Changes should be displayed as empty")
            Success_List_Append(
                "test_MKTPL_4960_Generate_Compareprofile_019_020_021_022_023_024_025_026_027",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_4960_Generate_Compareprofile_019_020_021_022_023_024_025_026_027",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_4960_Generate_Compareprofile_019_020_021_022_023_024_025_026_027",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

def test_MKTPL_4960_Generate_Compareprofile_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_4960_Generate_Compareprofile_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018.png"
        ) as driver:
            
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            wait = WebDriverWait(driver, 60)
            utils.wait_for_style_attribute(driver, 40)
            # 001
            utils.search_by_id(driver, DEDICATED_UNPUBLISHED_AIRLINE_ID)
            assert not len(driver.find_elements(By.XPATH, xpath.generateProfileXpath))
            assert not len(driver.find_elements(By.XPATH, xpath.compareProfileXpath))
            logging.info("Verified - Airline Object should be open and Both buttons Generate Profile and Compare Profile should not be dispalyed")
            utils.close_All_Tabs(driver)
            # 002
            utils.search_by_id(driver, DEDICATED_AIRLINE_ID)
            assert driver.find_element(By.XPATH, xpath.generateProfileXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.compareProfileXpath).is_displayed()
            logging.info("Verified - Airline Object should be open and Both buttons Generate Profile and Compare Profile should be dispalyed")
            # 003  
            driver.find_element(By.XPATH, xpath.generateProfileXpath).click()
            time.sleep(2)
            pathOfSecondTime = utils.random_numer_file_download_path(
                LOCAL_DOWNLOADED_PATH)
            logging.info("airline")
            assert 'airline' in pathOfSecondTime
            # 004
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            # 005
            assert driver.find_element(By.XPATH, xpath.importAirlineData).is_displayed()
            logging.info("Asserted the import airline pop up")
            # 006
            path = os.getcwd() + "/credencys_test_ui/" + "Airports_Sample.csv"
            time.sleep(5)
            close = driver.find_element(By.XPATH, "(//div[@data-qtip= 'Close panel']//div)[last()]")
            close.click()
            time.sleep(3)
            logging.info("Clicked on close button")
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the invalid file")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="File Import: Only json file is allowed."]')))
            logging.info("Validation shown: File Import: Only json file is allowed.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # 007
            path = os.getcwd() + "/credencys_test_ui/" + "Invalid_json.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the invalid json")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="JSON Import: Invalid json format."]')))
            logging.info("Validation shown: JSON Import: Invalid json format.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()            
            # 008
            path = os.getcwd() + "/credencys_test_ui/" + "CredAutomationAirlineEdited.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(3)
            logging.info("Uploaded the valid json with changes")
            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(5)
            textChanges = driver.find_element(By.NAME, "jsonImportMessage").get_attribute("value")
            logging.info(textChanges)
            assert '"from":' in textChanges
            assert '"to":' in textChanges
            logging.info("File should be uploaded successfully and Changes should be displayed")
            # 009
            # path = os.getcwd() + "/credencys_test_ui/" + "CredAutomationAirline.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(pathOfSecondTime)
            time.sleep(3)
            logging.info("Uploaded the valid json")
            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(3)
            textChanges = driver.find_element(By.NAME, "jsonImportMessage").get_attribute("value")
            logging.info(textChanges)
            assert '"changes": []' in textChanges
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            logging.info("File should be uploaded successfully and Changes should be displayed as empty")
            os.remove(pathOfSecondTime)
            utils.close_All_Tabs(driver)
            # 010
            utils.search_by_id(driver, DEDICATED_UNPUBLISHED_STORE_ID)
            assert not len(driver.find_elements(By.XPATH, xpath.generateProfileXpath))
            assert not len(driver.find_elements(By.XPATH, xpath.compareProfileXpath))
            logging.info("Verified - Store Object should be open and Both buttons Generate Profile and Compare Profile should not be dispalyed")
            utils.close_All_Tabs(driver)
            # 011
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            assert driver.find_element(By.XPATH, xpath.generateProfileXpath).is_displayed()
            assert driver.find_element(By.XPATH, xpath.compareProfileXpath).is_displayed()
            logging.info("Verified - Store Object should be open and Both buttons Generate Profile and Compare Profile should be dispalyed")
            # 012  
            driver.find_element(By.XPATH, xpath.generateProfileXpath).click()
            time.sleep(10)
            pathOfSecondTime = utils.random_numer_file_download_path(
                LOCAL_DOWNLOADED_PATH)
            logging.info("store")
            assert 'store' in pathOfSecondTime
            # 013
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            assert driver.find_element(By.XPATH, xpath.importAirlineData).is_displayed()
            logging.info("Asserted the import airline pop up")
            # 014
            path = os.getcwd() + "/credencys_test_ui/" + "Airports_Sample.csv"
            time.sleep(5)
            close = driver.find_element(By.XPATH, "(//div[@data-qtip= 'Close panel']//div)[last()]")
            close.click()
            time.sleep(3)
            logging.info("Clicked on close button")
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the invalid file")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="File Import: Only json file is allowed."]')))
            logging.info("Validation shown: File Import: Only json file is allowed.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # 015
            path = os.getcwd() + "/credencys_test_ui/" + "Empty_json.json"
            time.sleep(5)
            close = driver.find_element(By.XPATH, "(//div[@data-qtip= 'Close panel']//div)[last()]")
            close.click()
            time.sleep(3)
            logging.info("Clicked on close button")
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the empty json file")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="The provided file is blank."]')))
            logging.info("Validation shown: The provided file is blank.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # 016
            path = os.getcwd() + "/credencys_test_ui/" + "Invalid_json.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(1)
            logging.info("Uploaded the invalid json")
            driver.find_element(By.XPATH, xpath.upload).click()
            wait.until(EC.visibility_of_element_located((By.XPATH, '//div[text()="JSON Import: Invalid json format."]')))
            logging.info("Validation shown: JSON Import: Invalid json format.")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()            
            # 017
            path = os.getcwd() + "/credencys_test_ui/" + "CredAutomationStoreEdited.json"
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(path)
            time.sleep(3)
            logging.info("Uploaded the valid json with changes")
            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(3)
            textChanges = driver.find_element(By.NAME, "jsonImportMessage").get_attribute("value")
            logging.info(textChanges)
            assert '"from":' in textChanges
            assert '"to":' in textChanges
            logging.info("File should be uploaded successfully and Changes should be displayed")
            # 018
            driver.find_element(By.XPATH, "(//div[contains(@class, 'close ')])[last()]").click()
            driver.find_element(By.XPATH, xpath.compareProfileXpath).click()
            logging.info("Compare Profile button clicked")
            driver.find_element(By.XPATH, "//input[@name = 'jsonFileAirline']").send_keys(pathOfSecondTime)
            time.sleep(3)
            logging.info("Uploaded the valid json")
            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(3)
            textChanges = driver.find_element(By.NAME, "jsonImportMessage").get_attribute("value")
            logging.info(textChanges)
            assert '"changes": []' in textChanges
            os.remove(pathOfSecondTime)
            logging.info("File should be uploaded successfully and Changes should be displayed as empty")
            Success_List_Append(
                "test_MKTPL_4960_Generate_Compareprofile_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_4960_Generate_Compareprofile_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_4960_Generate_Compareprofile_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

@jamatest.test(11977063)
@jamatest.test(11977064)
@jamatest.test(11977065)
@jamatest.test(11977067)
@jamatest.test(11977068)
@jamatest.test(11977069)
@jamatest.test(11977070)
@jamatest.test(11977071)
@jamatest.test(11977073)
@jamatest.test(11977020)
@jamatest.test(11977021)
@jamatest.test(11977022)
@jamatest.test(11977031)
@jamatest.test(11977032)
@jamatest.test(11977033)
@jamatest.test(11977035)
@jamatest.test(11977036)
@jamatest.test(11976396)
def test_MKTPL_833_Resetpassword_001_002_003_005_006_007_008_009_011_MKTPL_782_PasswordSecurity_001_002_003_012_013_014_016_17_MKTPL_384_CRUDUser_003():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_PASSWORD = "".join(
        random.choices(string.ascii_letters, k=7)
    ) + "*123"
    NEW_RANDOM_PASSWORD = "".join(
        random.choices(string.ascii_letters, k=7)
    ) + "@123"
    logging.info(RANDOM_NAME)
    logging.info(RANDOM_PASSWORD)
    logging.info(NEW_RANDOM_PASSWORD)
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_833_Resetpassword_001_002_003_005_006_007_008_009_011_MKTPL_782_PasswordSecurity_001_002_003_012_013_014_016_17_MKTPL_384_CRUDUser_003.png"
        ) as driver:
            # driver.maximize_window()
            # 384 - 003 -009
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Users")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "firstName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys("testlastname")
            driver.find_element(By.NAME, "isActive").click()
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            driver.find_element(By.NAME, "userType").send_keys("PAC admin")
            time.sleep(1)
            driver.find_element(By.NAME, "userType").send_keys(Keys.ENTER)
            driver.find_element(By.NAME, "userRole").send_keys("pacadmin")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Mail Subscriptions"]')
                )
            ).click()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"User is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_path(driver, "/Users")
            driver.implicitly_wait(0)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//input[@name="query"])')
                    )
                )
            )
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[text()="' + RANDOM_NAME + '"]')
                    )
                )
                .is_displayed()
            )
            assert published
            logging.info(
                f"New created user is moved under Store name folder/Users folder = {published}"
            )
            utils.close_All_Tabs(driver)
            driver.find_element(By.XPATH, xpath.settings).click()
            action = ActionChains(driver)
            action.move_to_element(driver.find_element(By.XPATH, xpath.usersOrRoles)).perform()
            driver.find_element(By.XPATH, "(//span[text()='Users'])[last()]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//span[text()='Search']")))
            driver.find_element(By.XPATH, "//span[text()='Search']").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//input)[last()]")))
            driver.find_element(By.XPATH, "(//input)[last()]").send_keys(RANDOM_NAME)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//h3[contains(text(),'"+RANDOM_NAME+"')]")))
            driver.find_element(By.XPATH, "(//input)[last()]").send_keys(Keys.ENTER)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "password")))
            logging.info("Set")
            time.sleep(5)
            # MKTPL-833-Resetpassword-002
            # MKTPL-782-PasswordSecurity-003
            driver.find_element(By.XPATH, "//span[contains(@class, 'pimcore_icon_info')]").click()
            textarea = driver.find_element(By.XPATH, "//span[contains(@class, 'pimcore_icon_info')]//ancestor::a").get_attribute("data-qtip")
            assert "Must be at least ten characters in length" in textarea
            assert "At least one uppercase character [A - Z]" in textarea
            assert "At least one lowercase character [a - z]" in textarea
            assert "At least one digit [0 - 9]" in textarea
            assert "Must be different from the last 4 passwords" in textarea
            assert "At least one special character (for example, !, $, #,%)" in textarea
            assert "Cannot contain 2 consecutive characters that are also part of your username" in textarea
            logging.info("Asserted the info lines")
            driver.find_element(By.NAME, "password").send_keys(RANDOM_PASSWORD)
            driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
            driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
            # 003
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Last 4 password cannot be used')]")))
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            #Logout
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            driver.find_element(By.XPATH, xpath.userName).send_keys(
                    RANDOM_NAME+"@gmail.com"
                )
            driver.find_element(By.XPATH, xpath.password).send_keys(
                RANDOM_PASSWORD
            )
            driver.find_element(By.XPATH, xpath.submit).click()
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            # MKTPL-782-PasswordSecurity-002
            driver.find_element(By.ID, "pimcore_avatar").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"old_password")))
            driver.find_element(By.NAME, "new_password").send_keys(NEW_RANDOM_PASSWORD+"123")
            driver.find_element(By.NAME, "retype_password").send_keys(NEW_RANDOM_PASSWORD)
            driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'New Password and Retype password are not same')]")))
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"old_password")))
            logging.info("Validation shown as: New Password and Retype password are not same")
            driver.find_element(By.NAME, "old_password").send_keys(RANDOM_PASSWORD)
            driver.find_element(By.NAME, "new_password").clear()
            driver.find_element(By.NAME, "new_password").send_keys(NEW_RANDOM_PASSWORD)
            driver.find_element(By.NAME, "retype_password").clear()
            driver.find_element(By.NAME, "retype_password").send_keys(NEW_RANDOM_PASSWORD)
            logging.info(NEW_RANDOM_PASSWORD)
            # MKTPL-833-Resetpassword-006
            driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
            # MKTPL-782-PasswordSecurity-012
            driver.find_element(By.NAME, "old_password").clear()
            driver.find_element(By.NAME, "old_password").send_keys(NEW_RANDOM_PASSWORD)
            driver.find_element(By.NAME, "new_password").clear()
            driver.find_element(By.NAME, "new_password").send_keys("qweqwe")
            driver.find_element(By.NAME, "retype_password").clear()
            driver.find_element(By.NAME, "retype_password").send_keys("qweqwe")
            driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(), 'password policy')]")))
            logging.info("Validation displayed for psasword policy")
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            # MKTPL-833-Resetpassword-003
            # MKTPL-833-Resetpassword-005
            # MKTPL-782-PasswordSecurity-016
            driver.find_element(By.NAME, "old_password").clear()
            driver.find_element(By.NAME, "old_password").send_keys(NEW_RANDOM_PASSWORD)
            driver.find_element(By.NAME, "new_password").clear()
            driver.find_element(By.NAME, "new_password").send_keys(NEW_RANDOM_PASSWORD)
            driver.find_element(By.NAME, "retype_password").clear()
            driver.find_element(By.NAME, "retype_password").send_keys(NEW_RANDOM_PASSWORD)
            driver.find_element(By.XPATH, '(//span[text()="Save"])[1]').click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Last 4 password cannot be used')]")))
            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            logging.info("Validation displayed for Last 4 password cannot be used")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//a[@id='pimcore_logout']")))
            logging.info("Admin can set own password through profile section")
            # MKTPL-782-PasswordSecurity-013
            #Logout
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            driver.find_element(By.XPATH, xpath.userName).send_keys(
                    RANDOM_NAME+"@gmail.com"
                )
            driver.find_element(By.XPATH, xpath.password).send_keys(
                RANDOM_PASSWORD
            )
            driver.find_element(By.XPATH, xpath.submit).click()
            # MKTPL-782-PasswordSecurity-014
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"//div[contains(text(), 'Login failed!')]")))
            logging.info("Login failed!")
            # MKTPL-782-PasswordSecurity-017
            driver.find_element(By.XPATH, xpath.userName).send_keys(
                    RANDOM_NAME+"@gmail.com"
                )
            driver.find_element(By.XPATH, xpath.submit).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"//div[contains(text(), 'Login failed!')]")))
            logging.info("Login failed!")
            # Login
            time.sleep(3)
            driver.refresh()
            # MKTPL-833-Resetpassword-007
            driver.find_element(By.XPATH, xpath.userName).send_keys(
                    RANDOM_NAME+"@gmail.com"
                )
            driver.find_element(By.XPATH, xpath.password).send_keys(
                NEW_RANDOM_PASSWORD
            )
            driver.find_element(By.XPATH, xpath.submit).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            #Logout
            driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
            logging.info("Logged out")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            # MKTPL-833-Resetpassword-008
            driver.find_element(By.XPATH, "//a[contains(text(),'Forgot your password?')]").click()
            logging.info("Clicked on Forget password")
            # MKTPL-833-Resetpassword-009
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"//div[contains(text(), 'Enter your username and Pimcore will send a login link to your email address')]")))
            assert driver.find_element(By.XPATH, "//input[@placeholder = 'Username']").is_displayed()
            logging.info("Username field displayed")
            # MKTPL-833-Resetpassword-011
            assert driver.find_element(By.XPATH, "//a[contains(text(), 'Back to Login')]").is_displayed()
            driver.find_element(By.XPATH, "//a[contains(text(), 'Back to Login')]").click()
            logging.info("Back to Login displayed")
            time.sleep(3)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            assert driver.find_element(By.NAME, "password").is_displayed()
            
            Success_List_Append(
                "test_MKTPL_833_Resetpassword_001_002_003_005_006_007_008_009_011_MKTPL_782_PasswordSecurity_001_002_003_012_013_014_016_17_MKTPL_384_CRUDUser_003",
                "Verify User is created/updated in the system, "
                "moved to Store name folder/Users folder and receive an email "
                "for successfull registration.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_833_Resetpassword_001_002_003_005_006_007_008_009_011_MKTPL_782_PasswordSecurity_001_002_003_012_013_014_016_17_MKTPL_384_CRUDUser_003",
            "Verify User is created/updated in the system, "
            "moved to Store name folder/Users folder and receive an email "
            "for successfull registration.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_833_Resetpassword_001_002_003_005_006_007_008_009_011_MKTPL_782_PasswordSecurity_001_002_003_012_013_014_016_17_MKTPL_384_CRUDUser_003",
            "Verify User is created/updated in the system, "
            "moved to Store name folder/Users folder and receive an email "
            "for successfull registration.",
            e,
        )
        raise e

# ---------------------------------------------------------------------------
# region auto_approve_catalog_on auto map on

def test_MKTPL_5802_DeploymentInventory_001_002_003():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5802_DeploymentInventory_001_002_003.png"
        ) as driver:
            driver.maximize_window()
            utils.auto_approve_catalog_on(driver, DEDICATED_STORE)
            # utils.auto_populate_auto_map_airline_categories(driver)
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "yes", None)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                    )
                )
            ).click()
            catalog = wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )
            action = ActionChains(driver)
            action.context_click(catalog).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            driver.implicitly_wait(0)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            driver.implicitly_wait(0)
            catalog_assignment = driver.find_element(
                By.XPATH,
                '(//div[@class="x-box-target"]//span[text()="CatalogAssignment"])[1]',
            )
            mouse_hover.move_to_element(catalog_assignment).click().perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {RANDOM_NAME}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            time.sleep(1)
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_CATALOG_1_ID + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys("RouteGroup")
            time.sleep(2)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//li[contains(text(),'RouteGroup')]")
                    )
                )
                .click()
            )
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(f"{DEDICATED_ROUTE_GROUP_1_NAME}")
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            f"//li[contains(text(),'{DEDICATED_ROUTE_GROUP_1_NAME}')]",
                        )
                    )
                )
                .click()
            )
            logging.info("Selected Route Group")
            # time.sleep()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )

            # -------------------- Request For Association--------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request for Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request for Association')]")
                )
            )
            # --------------------EDIT BASE DATA--------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            action = driver.find_element(By.XPATH, xpath.actionsXpath)
            driver.execute_script("arguments[0].click();", action)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Edit Base Data')]"
            ).click()
            # Check for validation for request for Edit Base Data
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Edit Base Data')]")
                )
            )
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//input[contains(@name,'assignmentType')]")
                )
            )
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            # assignment_type.click()
            assignment_type.send_keys(Keys.DOWN, Keys.DOWN, Keys.ENTER)
            time.sleep(5)
            # assignment_type.send_keys()
            logging.info("Selected Sector as Assignment Type")
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_SECTOR_2_ID, Keys.ENTER
            )
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                (
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            "//div[contains(text(),'" + DEDICATED_SECTOR_2_ID + "')]",
                        )
                    )
                )
            )
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_SECTOR_2_ID + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            time.sleep(5)
            logging.info("Selected a catalog")
            select = driver.find_element(By.XPATH, "//span[contains(text(),'Select')]")
            driver.execute_script("arguments[0].click();", select)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Saved successfully')]")
                )
            )
            time.sleep(5)
            try:
                driver.find_element(By.XPATH, "//div[contains(@class,'close')]").click()
            except:
                logging.info("Modal is not shown")
            try:
                driver.find_element(By.XPATH, '//span[text()="Draft"]').click()
            except:
                logging.info("Draft is not shown")
            time.sleep(5)
            # WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH,xpath.saveXpath))).click()
            # WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Saved successfully')]")))
            # -------------------- REQUEST FOR ASSOCIATION --------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request For Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request For Association')]")
                )
            )
            logging.info("Request for Association")
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # ------------- APPROVE FROM STORE and DEPLOYMENT INVENTORY -------------
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            logging.info(UID)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
            )
            utils.clickOnDropDownForChooseAction(driver, UID, "Update Deployment Inventory")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//span[contains(text(),'Add/Update Deployment Inventory')]",
                    )
                )
            )
            logging.info(("Navigated to Add/Update Deployment Inventory page"))
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]")
                )
            )
            random_integer = random.randint(0, 99)
            driver.find_element(
                By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]"
            ).click()
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]"
            ).send_keys(Keys.BACKSPACE, Keys.BACKSPACE)
            logging.info("Cleared")
            time.sleep(3)
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]"
            ).send_keys(random_integer)
            driver.find_element(By.XPATH, "(//div[text()='Products'])[2]").click()
            logging.info(f"Add deployment inventory as {random_integer}")
            time.sleep(2)
            submit = driver.find_element(By.XPATH, "//span[contains(text(),'Submit')]")
            driver.execute_script("arguments[0].click();", submit)
            logging.info("Clicked on Submit")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'Sent message to sns successfully')]",
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )
            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")
            # UID = DEDICATED_CATALOG_2_ID
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            driver.find_element(
                By.XPATH, '(//span[@class="x-tab-close-btn"])[2]'
            ).click()
            # CHECK FOR DEPLOYMENT INVENTORY
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            # UID = DEDICATED_CATALOG_2_ID
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            logging.info(
                "catalog_assignment_publish & deployment_inventory_base_data_publish SNS should be triggered"
            )

            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # ------------- UPDATE ONLY BASE DATA -------------
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            action = driver.find_element(By.XPATH, xpath.actionsXpath)
            driver.execute_script("arguments[0].click();", action)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Edit Base Data')]"
            ).click()
            # Check for validation for request for Edit Base Data
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Edit Base Data')]")
                )
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//input[contains(@name,'assignmentType')]")
                    )
                )
                .clear()
            )
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys("RouteGroup")
            time.sleep(2)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//li[contains(text(),'RouteGroup')]")
                    )
                )
                .click()
            )
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).clear()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(f"{DEDICATED_ROUTE_GROUP_1_NAME}")
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            f"//li[contains(text(),'{DEDICATED_ROUTE_GROUP_1_NAME}')]",
                        )
                    )
                )
                .click()
            )
            logging.info("Selected Route Group")
            # time.sleep()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            try:
                driver.implicitly_wait(10)
                driver.find_element(By.XPATH, '//span[text()="Draft"]').click()
            except:
                logging.info("Draft is not shown")
            driver.implicitly_wait(0)

            # -------------------- REQUEST FOR ASSOCIATION --------------------
            time.sleep(10)
            logging.info("Request for association")
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request For Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request For Association')]")
                )
            )
            logging.info("Request for Association")
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            # UID = DEDICATED_CATALOG_2_ID
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )

            Success_List_Append(
                "test_MKTPL_5802_DeploymentInventory_001_002_003",
                "Verify when catalog assignment base data is updated via UI and it is published and there is no change in deployment inventory data",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5802_DeploymentInventory_001_002_003",
            "Verify when catalog assignment base data is updated via UI and it is published and there is no change in deployment inventory data",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5802_DeploymentInventory_001_002_003",
            "Verify when catalog assignment base data is updated via UI and it is published and there is no change in deployment inventory data",
            e,
        )
        raise e


def test_MKTPL_5802_DeploymentInventory_006_007():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    random_number = "".join([str(randint(1, 9)) for i in range(2)])
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5802_DeploymentInventory_006_007.png"
        ) as driver:
            driver.maximize_window()
            utils.auto_approve_catalog_on(driver, DEDICATED_STORE)
            utils.auto_populate_auto_map_airline_categories(driver)
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                    )
                )
            ).click()
            catalog = wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )
            action = ActionChains(driver)
            action.context_click(catalog).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            driver.implicitly_wait(0)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            driver.implicitly_wait(0)
            catalog_assignment = driver.find_element(
                By.XPATH,
                '(//div[@class="x-box-target"]//span[text()="CatalogAssignment"])[1]',
            )
            mouse_hover.move_to_element(catalog_assignment).click().perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {RANDOM_NAME}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_CATALOG_1_ID + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            logging.info("Created UID is-" + UID)
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys("RouteGroup")
            time.sleep(2)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//li[contains(text(),'RouteGroup')]")
                    )
                )
                .click()
            )
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(f"{DEDICATED_ROUTE_GROUP_1_NAME}")
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            f"//li[contains(text(),'{DEDICATED_ROUTE_GROUP_1_NAME}')]",
                        )
                    )
                )
                .click()
            )
            logging.info("Selected Route Group")
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )

            # -------------------- Request For Association--------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request for Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request for Association')]")
                )
            )
            # --------------------EDIT BASE DATA--------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            action = driver.find_element(By.XPATH, xpath.actionsXpath)
            driver.execute_script("arguments[0].click();", action)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Edit Base Data')]"
            ).click()
            # Check for validation for request for Edit Base Data
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Edit Base Data')]")
                )
            )
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//input[contains(@name,'assignmentType')]")
                )
            )
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            # assignment_type.click()
            assignment_type.send_keys(Keys.DOWN, Keys.DOWN, Keys.ENTER)
            time.sleep(5)
            # assignment_type.send_keys()
            logging.info("Selected Sector as Assignment Type")
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_SECTOR_2_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_SECTOR_2_ID + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            time.sleep(5)
            logging.info("Selected a catalog")
            select = driver.find_element(By.XPATH, "//span[contains(text(),'Select')]")
            driver.execute_script("arguments[0].click();", select)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Saved successfully')]")
                )
            )
            time.sleep(5)
            try:
                driver.find_element(By.XPATH, "//div[contains(@class,'close')]").click()
            except:
                logging.info("Modal is not shown")
            try:
                driver.find_element(By.XPATH, '//span[text()="Draft"]').click()
            except:
                logging.info("Draft is not shown")
            time.sleep(5)
            # WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH,xpath.saveXpath))).click()
            # WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Saved successfully')]")))
            # -------------------- REQUEST FOR ASSOCIATION --------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request For Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request For Association')]")
                )
            )
            logging.info("Request for Association")
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )
            # ------------- DEPLOYMENT INVENTORY FROM API-------------
            random_number = "".join([str(randint(1, 9)) for i in range(2)])
            payload = json.dumps(
                {
                    "catalog": {
                        "id": int(DEDICATED_CATALOG_1_ID),
                        "products": [
                            {
                                "productId": int(DEDICATED_SKU_1_ID),
                                "productQuantity": int(random_number),
                            }
                        ],
                    }
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            URL = ROUTE_CATALOG + UID + "/deploymentInventory"
            response = requests.request("PUT", URL, headers=headers, data=payload)
            logging.info(response.json())
            assert response.status_code == 200 or response.status_code == 201

            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")
            # UID = DEDICATED_CATALOG_2_ID
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            driver.find_element(
                By.XPATH, '(//span[@class="x-tab-close-btn"])[2]'
            ).click()
            # CHECK FOR DEPLOYMENT INVENTORY
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            logging.info(
                "catalog_assignment_publish & deployment_inventory_base_data_publish SNS should be triggered"
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )
            # ------------- ONLY DEPLOYMENT INVENTORY CHANGE -------------
            payload = json.dumps(
                {
                    "catalog": {
                        "id": int(DEDICATED_CATALOG_1_ID),
                        "products": [
                            {
                                "productId": int(DEDICATED_SKU_1_ID),
                                "productQuantity": int(random_number),
                            }
                        ],
                    }
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            URL = ROUTE_CATALOG + UID + "/deploymentInventory"
            response = requests.request("PUT", URL, headers=headers, data=payload)
            logging.info(response.json())
            assert response.status_code == 200 or response.status_code == 201

            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            logging.info("Only deployment_inventory_publish SNS is triggered")

            Success_List_Append(
                "test_MKTPL_5802_DeploymentInventory_006_007",
                "Verify when catalog assignment is created, updated and unpublished via UI",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5802_DeploymentInventory_006_007",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5802_DeploymentInventory_006_007",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            e,
        )
        raise e


def test_MKTPL_5802_DeploymentInventory_004_005():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_CODE = "".join(random.choices(string.digits, k=5))

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5802_DeploymentInventory_004_005.png"
        ) as driver:
            driver.maximize_window()
            utils.auto_approve_catalog_on(driver, DEDICATED_STORE)
            utils.auto_populate_auto_map_airline_categories(driver)
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                    )
                )
            ).click()
            catalog = wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )
            action = ActionChains(driver)
            action.context_click(catalog).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            driver.implicitly_wait(0)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            driver.implicitly_wait(0)
            catalog_assignment = driver.find_element(
                By.XPATH,
                '(//div[@class="x-box-target"]//span[text()="CatalogAssignment"])[1]',
            )
            mouse_hover.move_to_element(catalog_assignment).click().perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {RANDOM_NAME}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_CATALOG_1_ID + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys("RouteGroup")
            time.sleep(2)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//li[contains(text(),'RouteGroup')]")
                    )
                )
                .click()
            )
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(f"{DEDICATED_ROUTE_GROUP_1_NAME}")
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            f"//li[contains(text(),'{DEDICATED_ROUTE_GROUP_1_NAME}')]",
                        )
                    )
                )
                .click()
            )
            logging.info("Selected Route Group")
            # time.sleep()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )

            # -------------------- Request For Association--------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request for Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request for Association')]")
                )
            )
            time.sleep(3)
            # --------------------EDIT BASE DATA--------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            action = driver.find_element(By.XPATH, xpath.actionsXpath)
            driver.execute_script("arguments[0].click();", action)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Edit Base Data')]"
            ).click()
            # Check for validation for request for Edit Base Data
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Edit Base Data')]")
                )
            )
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//input[contains(@name,'assignmentType')]")
                )
            )
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            # assignment_type.click()
            assignment_type.send_keys(Keys.DOWN, Keys.DOWN, Keys.ENTER)
            time.sleep(5)
            # assignment_type.send_keys()
            logging.info("Selected Sector as Assignment Type")
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_SECTOR_2_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_SECTOR_2_ID + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            time.sleep(5)
            logging.info("Selected a catalog")
            select = driver.find_element(By.XPATH, "//span[contains(text(),'Select')]")
            driver.execute_script("arguments[0].click();", select)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Saved successfully')]")
                )
            )
            time.sleep(5)
            try:
                driver.find_element(By.XPATH, "//div[contains(@class,'close')]").click()
            except:
                logging.info("Modal is not shown")
            try:
                driver.find_element(By.XPATH, '//span[text()="Draft"]').click()
            except:
                logging.info("Draft is not shown")
            time.sleep(5)
            # WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH,xpath.saveXpath))).click()
            # WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Saved successfully')]")))
            # -------------------- REQUEST FOR ASSOCIATION --------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request For Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request For Association')]")
                )
            )
            logging.info("Request for Association")
            time.sleep(3)
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # ------------- APPROVE FROM STORE AND DEPLOYMENT INVENTORY BY CSV -------------
            wait = WebDriverWait(driver, 60)
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            logging.info(UID)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
            )
            # driver.find_element(By.XPATH, xpath.productsTabXpath).click()
            # logging.info("Clicked on Products")
            driver.implicitly_wait(0)
            utils.update_csv("QtyUpdate.csv", "Qty", RANDOM_CODE)
            utils.update_csv("QtyUpdate.csv", "CatalogAssignmentId", UID)
            logging.info("Qty and UID updated in CSV")
            utils.clickOnDropDownForChooseAction(driver, UID, "Import Deployment Inventory CSV")
            time.sleep(5)
            file_path = '//input[@name="pricefile"]'
            saved_message = utils.upload_csv("QtyUpdate.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            try:
                wait.until(
                    EC.element_to_be_clickable(
                        (By.XPATH, '//span[text()="Close & Reload"]')
                    )
                ).click()
                
                time.sleep(5)
            except:
                logging.info("Click & Reload not found")
            updated_price = wait.until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[text()="' + RANDOM_CODE + '"])[1]')
                    )
                )
            ).is_displayed()
            logging.info(updated_price)
            assert updated_price
            logging.info("Updated Price can be seen in Product grid")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            driver.find_element(
                By.XPATH, '(//span[@class="x-tab-close-btn"])[2]'
            ).click()
            # CHECK FOR DEPLOYMENT INVENTORY
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")

            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            logging.info(
                "catalog_assignment_publish & deployment_inventory_base_data_publish SNS should be triggered"
            )
            logging.info(
                "catalog_assignment_publish & deployment_inventory_base_data_publish SNS should be triggered"
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # ------------- ONLY DEPLOYMENT INVENTORY BY CSV -------------
            RANDOM_CODE = "".join(random.choices(string.digits, k=5))
            wait = WebDriverWait(driver, 60)
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            logging.info(UID)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
            )
            # driver.find_element(By.XPATH, xpath.productsTabXpath).click()
            # logging.info("Clicked on Products")
            driver.implicitly_wait(0)
            utils.update_csv("QtyUpdate.csv", "Qty", RANDOM_CODE)
            utils.update_csv("QtyUpdate.csv", "CatalogAssignmentId", UID)
            logging.info("Qty and UID updated in CSV")
            utils.clickOnDropDownForChooseAction(driver, UID, "Import Deployment Inventory CSV")
            time.sleep(5)
            file_path = '//input[@name="pricefile"]'
            saved_message = utils.upload_csv("QtyUpdate.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '//span[text()="Close & Reload"]')
                )
            ).click()
            time.sleep(5)
            updated_price = wait.until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[text()="' + RANDOM_CODE + '"])[1]')
                    )
                )
            ).is_displayed()
            logging.info(updated_price)
            assert updated_price
            logging.info("Updated Price can be seen in Product grid")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )
            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")
            # UID = DEDICATED_CATALOG_2_ID
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            Success_List_Append(
                "test_MKTPL_5802_DeploymentInventory_004_005",
                "Verify when catalog assignment is created, updated and unpublished via UI",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5802_DeploymentInventory_004_005",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5802_DeploymentInventory_004_005",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            e,
        )
        raise e


def test_MKTPL_5802_DeploymentInventory_008_009_010_011_012():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_CODE = "".join(random.choices(string.digits, k=5))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5802_DeploymentInventory_008_009_010_011_012.png"
        ) as driver:
            driver.maximize_window()
            wait = WebDriverWait(driver, 60)
            utils.auto_approve_catalog_on(driver, DEDICATED_STORE)
            utils.auto_populate_auto_map_airline_categories(driver)
            payload = utils.get_RouteCatalog_Payload(
                RANDOM_NAME,
                int(DEDICATED_CATALOG_1_ID),
                int(DEDICATED_SKU_1_ID),
                DEDICATED_AIRLINE_CATEGORY_1_ID,
                int(DEDICATED_ROUTE_GROUP_1_ID),
                int(DEDICATED_SECTOR_1_ID),
                False,
                "2024-01-24",
                "2024-01-24",
                [],
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", ROUTE_CATALOG, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------POST - CA -------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Catalog assigned successfully" in response.json()["message"]
            UID = response.json()["id"]
            logging.info(UID)
            URL = ROUTE_CATALOG + str(UID)
            logging.info(URL)
            time.sleep(10)
            # CHANGE WORK FLOW - edit base data
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "editBaseData"})
            response = requests.request(
                "POST", WORKFLOW_URL, headers=headers, data=payload
            )
            logging.info("-------POST - workflow -------")
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            # EDIT BASE DATA - API
            payload = json.dumps(
                {
                    "name": RANDOM_NAME + "EDIT",
                    "catalog": {"id": int(DEDICATED_CATALOG_1_ID)},
                }
            )
            response = requests.request("PATCH", URL, headers=headers, data=payload)
            data = response.json()
            logging.info("-------PATCH - CA -------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert (
                "catalog"
                in response.json()["message"] and "is updated successfully" in response.json()["message"]
            )
            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # -- AIRLINE - REQUEST FOR ASSOCIATION --
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            logging.info(UID)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request For Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request For Association')]")
                )
            )
            logging.info("Request for Association")
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )
            # 10 --- UPDATE DEPLOYMENTRY VIA UI -------
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            logging.info(UID)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
            )
            utils.clickOnDropDownForChooseAction(driver, UID, "Update Deployment Inventory")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//span[contains(text(),'Add/Update Deployment Inventory')]",
                    )
                )
            )
            logging.info(("Navigated to Add/Update Deployment Inventory page"))
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]")
                )
            )
            random_integer = random.randint(0, 99)
            driver.find_element(
                By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]"
            ).click()
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]"
            ).send_keys(Keys.BACKSPACE, Keys.BACKSPACE)
            logging.info("Cleared")
            time.sleep(3)
            driver.find_element(
                By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]"
            ).send_keys(random_integer)
            driver.find_element(By.XPATH, "(//div[text()='Products'])[2]").click()
            logging.info(f"Add deployment inventory as {random_integer}")
            time.sleep(2)
            submit = driver.find_element(By.XPATH, "//span[contains(text(),'Submit')]")
            driver.execute_script("arguments[0].click();", submit)
            logging.info("Clicked on Submit")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'Sent message to sns successfully')]",
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            # CHECK FOR DEPLOYMENT INVENTORY
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")

            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            logging.info(
                "catalog_assignment_publish & deployment_inventory_base_data_publish SNS should be triggered"
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )
            # ------- UPDATE DEPLOYMENT VIA CSV -------
            
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            logging.info(UID)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
            )
            # driver.find_element(By.XPATH, xpath.productsTabXpath).click()
            # logging.info("Clicked on Products")
            driver.implicitly_wait(0)
            utils.update_csv("QtyUpdate.csv", "Qty", RANDOM_CODE)
            utils.update_csv("QtyUpdate.csv", "CatalogAssignmentId", UID)
            logging.info("Qty and UID updated in CSV")
            utils.clickOnDropDownForChooseAction(driver, UID, "Import Deployment Inventory CSV")
            time.sleep(5)
            file_path = '//input[@name="pricefile"]'
            saved_message = utils.upload_csv("QtyUpdate.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '//span[text()="Close & Reload"]')
                )
            ).click()
            time.sleep(5)
            updated_price = wait.until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[text()="' + RANDOM_CODE + '"])[1]')
                    )
                )
            ).is_displayed()
            logging.info(updated_price)
            assert updated_price
            logging.info("Updated Price can be seen in Product grid")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )
            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            # CHECK FOR DEPLOYMENT INVENTORY
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")

            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            logging.info(
                "catalog_assignment_publish & deployment_inventory_base_data_publish SNS should be triggered"
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            # ----- UPDATE DEPLOYMENT VIA API  ---------
            random_number = "".join([str(randint(1, 9)) for i in range(3)])
            payload = json.dumps(
                {
                    "catalog": {
                        "id": int(DEDICATED_CATALOG_1_ID),
                        "products": [
                            {
                                "productId": int(DEDICATED_SKU_1_ID),
                                "productQuantity": int(random_number),
                            }
                        ],
                    }
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            URL = ROUTE_CATALOG + str(UID) + "/deploymentInventory"
            logging.info(URL)
            response = requests.request("PUT", URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            # -------------  LOGIN TO ADMIN FOR SNS -------------
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            # CHECK FOR DEPLOYMENT INVENTORY
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # actions.context_click(row).perform()
            # open = driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[1]")
            # open.click()
            logging.info("open clicked")
            # Open Code if modal appears
            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            logging.info(
                "catalog_assignment_publish & deployment_inventory_base_data_publish SNS should be triggered"
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.userName)
                    )
                )
            )

            Success_List_Append(
                "test_MKTPL_5802_DeploymentInventory_008_009_010_011_012",
                "Verify when catalog assignment is created, updated and unpublished via UI",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5802_DeploymentInventory_008_009_010_011_012",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5802_DeploymentInventory_008_009_010_011_012",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            e,
        )
        raise e

# endregion

# region auto_approve_catalog_on auto map off
@jamatest.test(11975623)
@jamatest.test(11975631)
@jamatest.test(11975632)
@jamatest.test(11975633)
@jamatest.test(11975636)
@jamatest.test(11975639)
def test_MKTPL_1344_Mapairlinecategory_003_011_012_013_016_019():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    LISTING = []
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1344_Mapairlinecategory_003_011_012_013_016_019.png"
        ) as driver:
            # driver.maximize_window()
            utils.auto_approve_catalog_on(driver, DEDICATED_STORE)
            # utils.revert_auto_populate_auto_map_airline_categories(driver)
            # utils.airline_auto_update_catalog_check_off(driver)
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "no", "no")
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                    )
                )
            ).click()
            catalog = wait.until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                        + DEDICATED_AIRLINE
                        + '"]',
                    )
                )
            )
            action = ActionChains(driver)
            action.context_click(catalog).perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            driver.implicitly_wait(0)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            driver.implicitly_wait(0)
            catalog_assignment = driver.find_element(
                By.XPATH,
                '(//div[@class="x-box-target"]//span[text()="CatalogAssignment"])[1]',
            )
            mouse_hover.move_to_element(catalog_assignment).click().perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {RANDOM_NAME}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                (
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            "//div[contains(text(),'" + DEDICATED_CATALOG_1_ID + "')]",
                        )
                    )
                )
            )
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_CATALOG_1_ID + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys("RouteGroup")
            time.sleep(2)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//li[contains(text(),'RouteGroup')]")
                    )
                )
                .click()
            )
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(f"{DEDICATED_ROUTE_GROUP_1_NAME}")
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            f"//li[contains(text(),'{DEDICATED_ROUTE_GROUP_1_NAME}')]",
                        )
                    )
                )
                .click()
            )
            logging.info("Selected Route Group")
            # time.sleep()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )

            # -------------------- Request For Association--------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request for Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request for Association')]")
                )
            )
            utils.click_on_yes_no(driver)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.ID, "options" + UID + "-trigger-picker")
                    )
                )
                .click()
            )
            time.sleep(3)
            list_of_cat = driver.find_elements(
                By.XPATH,
                '//div[@class = "x-boundlist-list-ct x-unselectable x-scroller"]//li',
            )
            logging.info(len(list_of_cat))
            for i in list_of_cat:
                logging.info(i.text)
                LISTING.append(i.text)
            logging.info(LISTING)
            assert set(DROPDOWN_LISTING) == set(LISTING)
            logging.info(
                "Dropdown listing like Load Updated Catalogs, Map Airline Categories"
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            "xpath",
                            '//span[text()="Choose Action To perform:"]/../../..//input',
                        )
                    )
                )
                .send_keys("Map Airline Categories", Keys.TAB)
            )
            products_and_redirect = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            "xpath",'//span[contains(text(),"Map Categories For:- ")]'
                        )
                    )
                )
                .is_displayed()
            )
            assert products_and_redirect

            logging.info(
                f"User is redirected to Map Airline Category Page.. = {products_and_redirect}"
            )
            logging.info(
                f"User is able to select Map Airline Categories.. = {products_and_redirect}"
            )
            submit = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        ("xpath", '//span[text()="Submit"]')
                    )
                )
                .is_displayed()
            )
            export_csv = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        ("xpath", '//span[text()="Export CSV"]')
                    )
                )
                .is_displayed()
            )
            import_csv = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        ("xpath", '//span[text()="Import CSV"]')
                    )
                )
                .is_displayed()
            )
            logging.info(f"submit button is displayed: {submit}")
            logging.info(f"Export CSV button is displayed:{export_csv}")
            logging.info(f"Export CSV button is displayed:{import_csv}")
            assert submit and export_csv and import_csv
            logging.info(
                "User can see Export CSV, Import CSV and Submit button in same Page.."
            )
            # verify header of Products List
            product_id = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        ("xpath", '//span[text()="Product Id"]')
                    )
                )
                .is_displayed()
            )
            logging.info(product_id)
            sku = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        ("xpath", '//span[text()="Product Sku"]')
                    )
                )
                .is_displayed()
            )
            logging.info(sku)

            product_name = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        ("xpath", '(//span[text()="Name"])[2]')
                    )
                )
                .is_displayed()
            )
            logging.info(product_name)

            assignment_type = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        ("xpath", '//span[text()="Assignment Type"]')
                    )
                )
                .is_displayed()
            )
            logging.info(assignment_type)

            backend_category = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        ("xpath", '//span[text()="Backend Category"]')
                    )
                )
                .is_displayed()
            )
            logging.info(backend_category)

            airline_category = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        ("xpath", '(//span[text()="Airline Category"])[2]')
                    )
                )
                .is_displayed()
            )
            logging.info(airline_category)

            assert product_id and sku and product_name
            assert assignment_type and backend_category and airline_category
            logging.info(
                "User can see all headers like Product_id, SKU, Product_Name, Assignment Type,Backend Category"
                " and Airline Category in Product list"
            )
            drop_down_2 = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        "((//fieldset[contains(@id,'map_airline_categories')]//tr)[last()]//td)[6]",
                    )
                )
            )
            drop_down_2.click()
            drop_down_2.click()
            # drop_down = WebDriverWait(driver, 60).until(EC.visibility_of_element_located(("xpath", '(//div[@class="x-grid-cell-inner "]/../..//td)[34]//div')))
            # action = ActionChains(driver)
            # action.double_click(on_element=drop_down).perform()
            # time.sleep(1)
            list_opt = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        '//li[starts-with(@class,"x-boundlist-item")][3]',
                    )
                )
            )
            list_opt.click()
            logging.info("option selected")
            time.sleep(2)
            driver.find_element(By.XPATH, '//span[text() = "Refresh"]').click()
            drop_down1 = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        "xpath",
                        "((//fieldset[contains(@id,'map_airline_categories')]//tr)[last()]//td)[6]",
                    )
                )
            )
            logging.info(drop_down1.is_displayed())

            drop_down1_text = drop_down1.get_attribute("innerText")
            logging.info(drop_down1_text)
            time.sleep(3)
            logging.info(len(drop_down1_text))
            if len(drop_down1_text) > 2:
                option = True
            assert option

            logging.info(
                "User can multi-select the airline category and map them to the SKU"
            )
            Success_List_Append(
                "test_MKTPL_1344_Mapairlinecategory_003_011_012_013_016_019",
                "Verify the Listing in Dropdown selection",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1344_Mapairlinecategory_003_011_012_013_016_019",
            "Verify the Listing in Dropdown selection",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1344_Mapairlinecategory_003_011_012_013_016_019",
            "Verify the Listing in Dropdown selection",
            e,
        )
        raise e

@jamatest.test(11975640)
@jamatest.test(11975641)
def test_MKTPL_1344_Mapairlinecategory_020_021():
    global driver
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1344_Mapairlinecategory_020_021.png"
        ) as driver:
            driver.maximize_window()
            utils.auto_approve_catalog_on(driver, DEDICATED_STORE)
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.set_cred_airline_auto_populate_map_update_airline_categories(
                driver, "no", None
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.catalogAssignment)
                    )
                )
            ).click()

            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                            + DEDICATED_AIRLINE
                            + '"]',
                        )
                    )
                )
            )
            home = driver.find_element(
                By.XPATH,
                '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                + DEDICATED_AIRLINE
                + '"]',
            )
            action = ActionChains(driver)
            action.context_click(home).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(
                By.XPATH, "(//span[text()='CatalogAssignment'])[1]"
            ).click()
            ca_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                ca_name
            )
            # Click on ok
            driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {ca_name}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "(//div[contains(text(),'" + DEDICATED_CATALOG_1_ID + "')])"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            logging.info(UID)
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys(Keys.DOWN, Keys.ENTER)
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(Keys.DOWN, Keys.ENTER)
            logging.info("Selected Route Group")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            # -------------------- Request For Association--------------------
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request for Association')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request for Association')]")
                )
            )
            utils.click_on_yes_no(driver)

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
                .click()
            )
            logging.info("Click on Products Tab")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            "xpath",
                            '//span[text()="Choose Action To perform:"]/../../..//input',
                        )
                    )
                )
                .send_keys("Map Airline Categories", Keys.TAB)
            )
            driver.implicitly_wait(2)
            assert wait.until(
                EC.presence_of_element_located(
                    ("xpath", '//span[contains(text(),"Map Categories For")]')
                )
            ).is_displayed()
            utils.update_csv("catalog_product_ac.csv", "ProductId", DEDICATED_SKU_1_ID)
            utils.update_csv(
                "catalog_product_ac.csv", "ProductSku", DEDICATED_SKU_1_NAME
            )
            utils.update_csv(
                "catalog_product_ac.csv",
                "AirlineCategory",
                DEDICATED_AIRLINE_CATEGORY_1_ID,
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//span[text()="Import CSV"]')
                    )
                )
                .click()
            )
            saved_message = utils.upload_csv(
                "catalog_product_ac.csv", '//input[@name="categoryfile"]'
            )
            assert saved_message
            wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '//span[text()="Close & Reload"]')
                )
            ).click()
            # verify dropdown of Airline category

            # wait.until(EC.presence_of_element_located(
            #     (By.XPATH, '//span[text()="Close & Reload"]')
            # )).click()
            # logging.info("close")
            view_grid = wait.until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="Products"]'))
            ).is_displayed()
            logging.info(view_grid)
            assert view_grid
            Airline_category = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "(//div[text()='"
                        + str(DEDICATED_SKU_1_ID)
                        + "']//parent::td//following-sibling::td)[6]//div",
                    )
                )
            ).text
            assert DEDICATED_AIRLINE_CATEGORY_1_ID in Airline_category
            logging.info("User is able to view product changes made via import CSV ")
            Success_List_Append(
                "test_MKTPL_1344_Mapairlinecategory_020_021",
                "Verify click on Submit post making changes via CSV or UI",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1344_Mapairlinecategory_020_021",
            "Verify click on Submit post making changes via CSV or UI",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1344_Mapairlinecategory_020_021",
            "Verify click on Submit post making changes via CSV or UI",
            e,
        )
        raise e

@jamatest.test(11975564)
@jamatest.test(11975565)
@jamatest.test(11975566)
@jamatest.test(11975567)
@jamatest.test(11975569)
def test_MKTPL_1342_Mappriceanddeploymentinventory_024_025_026_027_029():
    RANDOM_CODE = "".join(random.choices(string.digits, k=5))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_024_025_026_027_029.png"
        ) as driver:
            driver.maximize_window()
            utils.auto_approve_catalog_on(driver, DEDICATED_STORE)
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            utils.update_csv(
                "QtyUpdate.csv",
                "CatalogAssignmentId",
                DEDICATED_CATALOG_ASSIGNMENT_3_ID,
            )
            utils.update_csv("QtyUpdate.csv", "Qty", RANDOM_CODE)
            logging.info("Qty updated in CSV")
            utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_3_ID)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productsTabXpath)
                )
            )
            utils.clickOnDropDownForChooseAction(driver, DEDICATED_CATALOG_ASSIGNMENT_3_ID, "Import Deployment Inventory CSV")
            time.sleep(5)
            file_path = '//input[@name="pricefile"]'
            saved_message = utils.upload_csv("QtyUpdate.csv", file_path)
            assert saved_message
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Close & Reload"]')
                )
            ).click()
            updated_qty = wait.until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//div[text()="' + RANDOM_CODE + '"])[1]')
                    )
                )
            ).is_displayed()
            assert updated_qty
            logging.info("Updated Qty can be seen in Product grid")
            Success_List_Append(
                "test_MKTPL_1342_Mappriceanddeploymentinventory_024_025_026_027_029",
                "Verify User can see popup for Import Deployment Inventory CSV  - Update Qty in CSV - "
                "Import Updated CSV - View updated Qty value in product grid",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_024_025_026_027_029",
            "Verify User can see popup for Import Deployment Inventory CSV  - Update Qty in CSV -"
            "Import Updated CSV - View updated Qty value in product grid",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_024_025_026_027_029",
            "Verify User can see popup for Import Deployment Inventory CSV  - Update Qty in CSV - "
            "Import Updated CSV - View updated Qty value in product grid",
            e,
        )
        raise e

@jamatest.test(11975924)
def test_MKTPL_2222_AssignUITemplate_005_MKTPL_2298_Logs_001():
    try:
        with utils.services_context_wrapper("test_MKTPL_2222_AssignUITemplate_005_MKTPL_2298_Logs_001.png") as driver:
            driver.maximize_window()
            # utils.revert_auto_populate_auto_map_airline_categories(driver)
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "no", None)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            store.send_keys("AirlineCategory")
            time.sleep(1)
            store.send_keys(Keys.ENTER)
            logging.info("Import data type is selected")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
                .click()
            )
            # driver.find_element(By.ID, xpath.downloadCsvButtonId).click()
            time.sleep(6)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE)
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE)

            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            utils.update_csv("Airline_Category_Sample.csv", "name_en", RANDOM_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Airline_Category_Sample.csv", file_path)
            assert saved_message
            logging.info(
                f"Airline Category should be successfully imported in the system = {saved_message}"
            )
            driver.refresh()
            utils.search_by_id(driver, CATEGORY_FOLDER)
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "query")))
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[contains(text(),"' + RANDOM_NAME + '")])[1]')
                )
            )
            cat = driver.find_element(
                By.XPATH, '(//div[contains(text(),"' + RANDOM_NAME + '")])[1]'
            )
            
            logging.info(
                "Airline Category object  move automatically under Category Management section"
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets_import_Airline(driver)
            time.sleep(3)
            list1 = driver.find_elements(
                By.XPATH, "(//div[contains(text(),'airline_category')])"
            )
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'airline_category')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            time.sleep(3)
            # open1 = driver.find_element(By.XPATH, xpath.open)
            # open1.click()
            wait = WebDriverWait(driver, 60)

            logging.info("Opened the log file - json")
            assert wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[contains(text(),"airline_category")])[1]')
                )
            ).is_displayed()

            assert driver.find_element(
                By.XPATH, '//div[contains(text(),"Row Inserted\/updated Successfully")]'
            ).is_displayed()

            Success_List_Append(
                "test_MKTPL_2222_AssignUITemplate_005_MKTPL_2298_Logs_001",
                "Verify by adding valid csv file with valid data ",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2222_AssignUITemplate_005_MKTPL_2298_Logs_001",
            "Verify by adding valid csv file with valid data ",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2222_AssignUITemplate_005_MKTPL_2298_Logs_001",
            "Verify by adding valid csv file with valid data",
            e,
        )
        raise e


def test_MKTPL_1646_1893_1894_1979_sns_033_034():
    ACTUAL_CSV_DATA = []
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    logging.info(RANDOM_NAME)
    try:
        with utils.services_context_wrapper("test_MKTPL_1646_1893_1894_1979_sns_033_034.png") as driver:
            driver.maximize_window()
            utils.revert_auto_populate_auto_map_airline_categories(driver)
            for i in range(2):
                RANDOM_NUMBER = "".join([str(randint(1, 9)) for i in range(2)])
                logging.info(RANDOM_NUMBER)
                utils.Pac_Credentials.Login_Airline(driver)
                driver.implicitly_wait(10)
                act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
                assert act_title == "pimcore_logout"
                logging.info("Login is successful.")
                utils.wait_for_style_attribute(driver, 40)
                wait = WebDriverWait(driver, 60)

                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
                )
                driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
                driver.find_element(By.XPATH, xpath.csvImport).click()
                (
                    WebDriverWait(driver, 60)
                    .until(EC.presence_of_element_located((By.NAME, "importType")))
                    .send_keys("AirlineCategory")
                )
                if i == 0:
                    (
                        WebDriverWait(driver, 60)
                        .until(
                            EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                        )
                        .click()
                    )
                    # driver.find_element(By.ID, xpath.downloadCsvButtonId).click()
                    time.sleep(3)
                    logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE)
                    with open(
                        LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE,
                        "r",
                        encoding="utf-8-sig",
                    ) as file:
                        csvFile = csv.reader(file)
                        for lines in csvFile:
                            logging.info(lines)
                            ACTUAL_CSV_DATA.append(lines)
                            break
                    assert CSV_AIRLINE_CATEGORY == ACTUAL_CSV_DATA
                    os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE)
                    logging.info("CSV file is downloaded on the system")

                # UPDATE Airline category name, UITemplate, Root type in CSV
                utils.update_csv("Airline_Category_Sample.csv", "name_en", RANDOM_NAME)
                utils.update_csv(
                    "Airline_Category_Sample.csv",
                    "UITemplate",
                    DEDICATED_UI_TEMPLATE_1_NAME,
                )  # eeeee
                utils.update_csv(
                    "Airline_Category_Sample.csv", "rootType", DEDICATED_ROOT_TYPE_1_NAME
                )  # BjsJYkB
                utils.update_csv(
                    "Airline_Category_Sample.csv", "sequenceNumber", RANDOM_NUMBER
                ) 
                # Upload Airline Category Updated CSV
                file_path = xpath.csvFileSelectButton
                saved_message = utils.upload_csv("Airline_Category_Sample.csv", file_path)
                assert saved_message

                logging.info("Import Updated Airline Category CSV")

                # Logout
                driver.find_element(By.XPATH, xpath.logout).click()
                logging.info("Logged out from Airline Admin")
                wait.until(EC.visibility_of_element_located((By.NAME, "username")))
                
                # verify SNS
                utils.Pac_Credentials.Login_Pac_Admin(driver)
                utils.goCheckSNS(driver,"airline_category_publish", RANDOM_NAME, True)
                assert driver.find_element(By.XPATH, "(//span[contains(text(),'sequenceNumber')]//parent::div//span)[3]").text == str(RANDOM_NUMBER)
                driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                logging.info("Logged out from PAC Admin")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
            # utils.Assets(driver)
            # time.sleep(3)
            # list1 = driver.find_elements(
            #     By.XPATH, "(//div[contains(text(),'airline_category_publish')])"
            # )
            # num = len(list1)

            # time.sleep(3)
            # row = driver.find_element(
            #     By.XPATH,
            #     f"(//div[contains(text(),'airline_category_publish')])[{num - 1}]",
            # )
            # actions = ActionChains(driver)
            # actions.context_click(row).perform()
            # open1 = driver.find_element(By.XPATH, xpath.open)
            # open1.click()
            # wait = WebDriverWait(driver, 60)
            # # Open Code if modal appears
            # try:
            #     if wait.until(
            #         EC.visibility_of_element_located(
            #             (By.XPATH, xpath.yes)
            #         )
            #     ).is_displayed():
            #         driver.find_element(
            #             By.XPATH, xpath.yes
            #         ).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")

            # logging.info("Opened the log file - json")
            # id_span = wait.until(
            #     EC.presence_of_element_located(
            #         (
            #             By.XPATH,
            #             '(//span[contains(text(),"airline_category_publish")])[1]',
            #         )
            #     )
            # ).is_displayed()
            # logging.info(id_span)
            # x = wait.until(
            #     EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            # )
            # x.send_keys(Keys.CONTROL, "f")
            # driver.find_element(
            #     By.XPATH, "//input[contains(@placeholder,'Search for')]"
            # ).send_keys(RANDOM_NAME)
            # time.sleep(1)
            # assert driver.find_element(
            #     By.XPATH, "//span[contains(text(),'" + RANDOM_NAME + "')]"
            # ).is_displayed()

            # utils.validateSnsMessageAttributes(driver, x)
            # utils.validateSnsEventType(driver, "airline_category_publish")
            # utils.validateSnsId(driver)
            # utils.validateSnsAirlineCode(driver)
            
            Success_List_Append(
                "test_MKTPL_1646_1893_1894_1979_sns_033_034",
                "Verify when Airline Category is created/updated via CSV and Airline Category is published",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1646_1893_1894_1979_sns_033_034",
            "Verify when Airline Category is created/updated via CSV and Airline Category is published",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1646_1893_1894_1979_sns_033_034",
            "Verify when Airline Category is created/updated via CSV and Airline Category is published",
            e,
        )
        raise e

@jamatest.test(11977696)
def test_MKTPL_2468_AssignRoottype_003_MKTPL_1646_1893_1894_1979_SNS_030_031_032():
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_DESCRIPTION = "".join(random.choices(string.ascii_letters, k=15))

    try:
        with utils.services_context_wrapper("test_MKTPL_2468_AssignRoottype_003_MKTPL_1646_1893_1894_1979_SNS_030_031_032.png") as driver:
            driver.maximize_window()
            # utils.revert_auto_populate_auto_map_airline_categories(driver)
            utils.Pac_Credentials.Login_Airline(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "no", None)
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Category Management"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                        )
                    )
                )
                .click()
            )
            category = driver.find_element(By.XPATH, '//span[text()="Category"]')
            action = ActionChains(driver)
            action.context_click(category).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(2)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(airline).perform()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//span[text()="AirlineCategory"])[1]')
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "name")))
                .send_keys(RANDOM_NAME)
            )
            driver.find_element(By.NAME, "url").send_keys(RANDOM_DESCRIPTION)
            driver.find_element(By.NAME, "rootType").send_keys(
                "EetBcSt (CRE)", Keys.ENTER
            )
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID_1 = UID[1]
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Airline Category should be successfully created/updated in the system = {saved}"
            )
            logout_button = driver.find_element(By.XPATH, xpath.logout)
            logout_button.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.userName))
            )

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            time.sleep(5)
            # Generate list and get the last row number with catalog assignment publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'airline_category_publish')]"
            )
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'airline_category_publish')])[{num-1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'airline_category_publish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID_1}')]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID_1}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID_1}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "airline_category_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            # Update
            logout_button = driver.find_element(By.XPATH, xpath.logout)
            logout_button.click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.userName)
                )
            )
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, UID_1)
            driver.find_element(By.NAME, "disclaimer").send_keys(RANDOM_DESCRIPTION)
            logging.info(f"Updated Disclaimer as: {RANDOM_DESCRIPTION}")
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Airline Category should be successfully created/updated in the system = {saved}"
            )
            logout_button = driver.find_element(By.XPATH, xpath.logout)
            logout_button.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.userName))
            )

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with catalog assignment publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'airline_category_publish')]"
            )
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'airline_category_publish')])[{num-1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'airline_category_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID_1}')]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID_1}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID_1}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "airline_category_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            # Unpublish
            logout_button = driver.find_element(By.XPATH, xpath.logout)
            logout_button.click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.userName)
                )
            )
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, UID_1)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.unpublishButtonXpath))
            )
            driver.find_element(By.XPATH, xpath.unpublishButtonXpath).click()
            logging.info("Clicked on Un-publish")
            logout_button = driver.find_element(By.XPATH, xpath.logout)
            logout_button.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.userName))
            )
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with catalog assignment publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'airline_category_unpublish')]"
            )
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'airline_category_unpublish')])[{num-1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'airline_category_unpublish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            logging.info(("Opened the entry"))
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID_1}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID_1}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "airline_category_unpublish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            Success_List_Append(
                "test_MKTPL_2468_AssignRoottype_003_MKTPL_1646_1893_1894_1979_SNS_030_031_032",
                "Verify by entering/updating data in below fields",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2468_AssignRoottype_003_MKTPL_1646_1893_1894_1979_SNS_030_031_032",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2468_AssignRoottype_003_MKTPL_1646_1893_1894_1979_SNS_030_031_032",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

# endregion

# region auto_approve_catalog_off auto map on
def test_MKTPL_1646_1893_1894_1979_SNS_025_026_028():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1646_1893_1894_1979_SNS_025_026_028.png"
        ) as driver:
            driver.maximize_window()
            utils.auto_populate_auto_map_airline_categories(driver)
            utils.auto_approve_catalog_off(driver, DEDICATED_STORE)
            # Login through PAC admin user
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.click_on_add_object(driver, "Catalog")
            # Move to Catalog
            catalog = driver.find_element(By.XPATH, "(//a//span[text()='Catalog'])[1]")
            action = ActionChains(driver)
            action.move_to_element(catalog).perform()
            action.click(catalog).perform()
            # Click on Catalog Assignment
            ca_assignment = driver.find_element(
                By.XPATH, "(//a//span[text()='CatalogAssignment'])[1]"
            )
            driver.execute_script("arguments[0].click();", ca_assignment)
            logging.info("Clicked on Catalog Assignment")
            # Enter catalog assignment name
            ca_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                ca_name
            )
            # Click on ok
            ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
            ok.click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {ca_name}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, "//input[@name='query']")))
            )
            driver.find_element(By.XPATH, "//input[@name='query']").send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_CATALOG_1_NAME + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            driver.find_element(By.NAME, "airline").send_keys(
                DEDICATED_AIRLINE, Keys.ENTER
            )
            driver.find_element(By.XPATH, "//span[text()='Catalog To Date:']").click()
            logging.info("Selected Airline")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            # assignment_type.click()
            assignment_type.send_keys("RouteGroup")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//li[contains(text(),'RouteGroup')]")
                    )
                )
                .click()
            )
            time.sleep(5)
            # assignment_type.send_keys()
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys("Automation_Route_Group")
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, "//li[contains(text(),'Automation_Route_Group')]")
                    )
                )
                .click()
            )
            logging.info("Selected Route Group")
            # time.sleep()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Saved Successfully")
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request for Association')]"
            ).click()
            # Check for validation for request for association
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Request for Association')]")
                )
            )
            logging.info("Requested for Association")
            # driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                (
                    EC.element_to_be_clickable(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            time.sleep(5)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Approve For Association')]"
            ).click()
            # Check for validation for request for association
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Approve For Association')]")
                )
            )
            logging.info("Approved for Association")
            # Navigate to the Triggered SNS - List View
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with catalog assignment publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]")
                )
            )
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]"
            )
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'catalog_assignment_publish')])[{num-1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Click on Open
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            time.sleep(5)
            # id = driver.find_element(By.XPATH, f"//span[contains(text(),'{UID}')]").text
            # assert UID in id
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "catalog_assignment_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            # Update
            logging.info("Update")
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            action = driver.find_element(By.XPATH, xpath.actionsXpath)
            driver.execute_script("arguments[0].click();", action)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Edit Base Data')]"
            ).click()
            # Check for validation for request for Edit Base Data
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Edit Base Data')]")
                )
            )
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//input[contains(@name,'assignmentType')]")
                )
            )
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            # assignment_type.click()
            assignment_type.send_keys(Keys.DOWN, Keys.DOWN, Keys.ENTER)
            time.sleep(5)
            # assignment_type.send_keys()
            logging.info("Selected Sector as Assignment Type")
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, "//input[@name='query']")))
            )
            driver.find_element(By.XPATH, "//input[@name='query']").send_keys(
                DEDICATED_SECTOR_2_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_SECTOR_2_ID + "')]"
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            time.sleep(5)
            logging.info("Selected a catalog")
            select = driver.find_element(By.XPATH, "//span[contains(text(),'Select')]")
            driver.execute_script("arguments[0].click();", select)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Saved successfully')]")
                )
            )
            try:
                driver.find_element(By.XPATH, "//div[contains(@class,'close')]").click()
            except:
                logging.info("Modal is not shown")
            time.sleep(5)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, xpath.saveXpath))
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Saved successfully')]")
                )
            )
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            time.sleep(1)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Request For Association to Store')]"
            ).click()
            logging.info("Request for Association")
            # Check for validation for request for association to Store
            # driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'Request For Association to Store')]",
                    )
                )
            )
            WebDriverWait(driver, 60).until(
                (
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.actionsXpath).click()
            time.sleep(1)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Approve For Association')]"
            ).click()
            logging.info("Approve for Association")
            # Check for validation for request for association to Store
            # driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Approve For Association')]")
                )
            )
            # Navigate to the Triggered SNS - List View
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with catalog assignment publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]"
            )
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'catalog_assignment_publish')])[{num-1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Click on Open
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            logging.info(("Opened the entry"))
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "catalog_assignment_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            # Request for Disassociation
            utils.close_All_Tabs(driver)
            # Wait and enter "stores" in search input field
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.actionsXpath)
                    )
                )
            )
            action = driver.find_element(By.XPATH, xpath.actionsXpath)
            driver.execute_script("arguments[0].click();", action)
            driver.find_element(
                By.XPATH,
                "//span[contains(text(),'Request for Dissociation to Airline')]",
            ).click()
            # Check for validation for request for Request for dissociation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'Request for Dissociation to Airline')]",
                    )
                )
            )
            logging.info("Request for Dissociation")
            time.sleep(5)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, xpath.actionsXpath)
                )
            )
            action = driver.find_element(By.XPATH, xpath.actionsXpath)
            driver.execute_script("arguments[0].click();", action)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Approve for Dissociation')]"
            ).click()
            # Check for validation for request for Approve for dissociation
            # driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'Approve for Dissociation')]")
                )
            )
            logging.info("Approve for Dissociation")
            # Navigate to the Triggered SNS - List View
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with catalog assignment publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_unpublish')]"
            )
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'catalog_assignment_unpublish')])[{num-1}]",
                    )
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_unpublish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Click on Open
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            logging.info(("Opened the entry"))
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{UID}')]")
                )
            )
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "catalog_assignment_unpublish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from PAC Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.revert_auto_populate_auto_map_airline_categories(driver)
            Success_List_Append(
                "test_MKTPL_1646_1893_1894_1979_SNS_025_026_028",
                "Verify when catalog assignment is created, updated and unpublished via UI",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1646_1893_1894_1979_SNS_025_026_028",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1646_1893_1894_1979_SNS_025_026_028",
            "Verify when catalog assignment is created, updated and unpublished via UI",
            e,
        )
        raise e

@jamatest.test(13416283)
def test_MKTPL_3738_WorkflowAPI_002_003():
    try:
        with utils.services_context_wrapper("test_MKTPL_3738_WorkflowAPI_002_003.png") as driver:
            driver.maximize_window()
            utils.auto_populate_auto_map_airline_categories(driver)
            utils.auto_approve_catalog_off(driver, DEDICATED_STORE)
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.catalogAssignment)
                    )
                )
            ).click()
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                            + DEDICATED_AIRLINE
                            + '"]',
                        )
                    )
                )
            )
            home = driver.find_element(
                By.XPATH,
                '//div[text()="Catalog Assignment"]/../../../../..//span[text()="'
                + DEDICATED_AIRLINE
                + '"]',
            )
            action = ActionChains(driver)
            action.context_click(home).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            airline = driver.find_element(
                By.XPATH, "(//span[text()='CatalogAssignment'])[1]"
            ).click()
            ca_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                ca_name
            )
            # Click on ok
            driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            logging.info(
                f"Clicked on ok after entering Catalog Assignment name as {ca_name}"
            )
            # search icon
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Base Data')]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'pimcore_icon_search ')])[1]"
            ).click()
            WebDriverWait(driver, 60).until(
                (EC.visibility_of_element_located((By.XPATH, xpath.queryXpath)))
            )
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(
                DEDICATED_CATALOG_1_ID, Keys.ENTER
            )
            time.sleep(2)
            cred = driver.find_element(
                By.XPATH, '//div[text()="' + DEDICATED_CATALOG_1_ID + '"]'
            )
            action = ActionChains(driver)
            action.double_click(cred).perform()
            logging.info("Selected a catalog")
            # Get UID
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = int(UID[1])
            logging.info(UID)

            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'assignmentType')]//parent::div//following-sibling::div",
            ).click()
            time.sleep(2)
            assignment_type = driver.find_element(
                By.XPATH, "//input[contains(@name,'assignmentType')]"
            )
            assignment_type.send_keys(Keys.DOWN, Keys.ENTER)
            time.sleep(5)
            logging.info("Selected Route Group as Assignment Type")
            driver.find_element(
                By.XPATH,
                "//input[contains(@name,'RouteGroup')]//parent::div//following-sibling::div",
            ).click()
            driver.find_element(
                By.XPATH, "//input[contains(@name,'associateRouteGroup')]"
            ).send_keys(Keys.DOWN, Keys.ENTER)
            logging.info("Selected Route Group")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.revert_auto_populate_auto_map_airline_categories(driver)
            # Request for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "request"})
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request(
                "POST", WORKFLOW_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-----Request for association------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "'Request for association' Status Api is accessible by the Airline"
            )

            # Approved for association
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "approve"})
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", WORKFLOW_URL, headers=headers, data=payload
            )
            data_ID = response.json()
            logging.info("-------Approved for association-------")
            logging.info(data_ID)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "'Approved for association' Status Api is accessible by the Store"
            )
            # ==============================================================================================================
            # REQUEST FOR "editAirlineCategory"
            payload = json.dumps({"catalogAssignmentId": int(UID), "status": "editBaseData"})
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", WORKFLOW_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------editBaseData-------")

            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # REQUEST FOR "editBaseData"
            payload = json.dumps(
                {"catalogAssignmentId": int(UID), "status": "editAirlineCategory"}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", WORKFLOW_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------editAirlineCategory-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "Catalog assignment workflow state change to Edit Base Data+Airline Category Mapped"
            )
            Success_List_Append(
                "test_MKTPL_3738_WorkflowAPI_002_003",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3738_WorkflowAPI_002_003",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3738_WorkflowAPI_002_003",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11975624)
@jamatest.test(11975626)
@jamatest.test(11975628)
def test_MKTPL_1344_Mapairlinecategory_004_006_008():
    try:
        with utils.services_context_wrapper("test_MKTPL_1344_Mapairlinecategory_004_006_008.png") as driver:
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            UID = utils.create_Simple_Product("Onboard", "Duty Free", RANDOM_NAME)
            assert UID is not None
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, None, "no")
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_4_ID)
            driver.find_element(By.XPATH, "//span[contains(@class,'icon_open')]").click()
            time.sleep(3)
            # Open Code if modal appears
            utils.click_on_yes_no(driver)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "(//span[text()='Products'])[2]")))
            driver.find_element(By.XPATH, "(//span[text()='Products'])[2]").click()
            driver.find_element(By.XPATH, "//span[contains(@class,'icon_search')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME,
                                                  "query")))
            driver.find_element(By.NAME,"query").send_keys(UID, Keys.ENTER)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,
                                                  "//div[text()='"+str(UID)+"']")))
            product = driver.find_element(By.XPATH,
                                          "//div[text()='"+str(UID)+"']")
            action = ActionChains(driver)
            action.double_click(product).perform()
            time.sleep(4)
            driver.find_element(By.XPATH, "//span[text()='Select']").click()
            time.sleep(10)
            saved = utils.save_and_publish(driver)
            assert saved
            time.sleep(10)
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            # Login through Airline user
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_4_ID)
            driver.find_element(By.XPATH, "//span[contains(@class,'reload')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//span[text()='Products']")))
            driver.find_element(By.XPATH, "//span[text()='Products']").click()
            driver.find_element(By.XPATH,
                                "//span[contains(text(),'Choose')]//ancestor::div[1]//div[contains(@class,'arrow-trigger')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//li[contains(text(),'Load Updated Catalog')]")))
            driver.find_element(By.XPATH, "//li[contains(text(),'Load Updated Catalog')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[contains(text(),"Changes has been loaded")]')
                )
            ).is_displayed()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Delta Products')]")
                )
            )
            driver.find_element(By.XPATH, "//span[contains(text(),'Delta Products')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//span[contains(text(),'Update Catalog')]")))
            driver.find_element(By.XPATH, "//span[contains(text(),'Update Catalog')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[contains(text(),"Changes has been published")]')
                )
            ).is_displayed()
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            # Login through store user
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_4_ID)
            driver.find_element(By.XPATH,"//span[contains(@class,'icon_open')]").click()
            time.sleep(3)
            # Open Code if modal appears
            utils.click_on_yes_no(driver)

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"(//span[text()='Products'])[2]")))
            driver.find_element(By.XPATH,"(//span[text()='Products'])[2]").click()
            driver.find_element(By.XPATH,"(//div[text()='"+str(UID)+"']//parent::td//parent::tr//td)[6]//img").click()
            driver.find_element(By.XPATH, xpath.yes).click()
            time.sleep(3)
            saved = utils.save_and_publish(driver)
            assert saved
            time.sleep(2)
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            # Login through Airline user
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_4_ID)
            driver.find_element(By.XPATH,"//span[contains(@class,'reload')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//span[text()='Products']")))
            driver.find_element(By.XPATH, "//span[text()='Products']").click()
            driver.find_element(By.XPATH,"//span[contains(text(),'Choose')]//ancestor::div[1]//div[contains(@class,'arrow-trigger')]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"//li[contains(text(),'Load Updated Catalog')]")))
            driver.find_element(By.XPATH,"//li[contains(text(),'Load Updated Catalog')]").click()
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[contains(text(),"Changes has been loaded")]')
                    )
                ).is_displayed()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Delta Products')]")
                )
            )
            driver.find_element(By.XPATH,"//span[contains(text(),'Delta Products')]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//span[contains(text(),'Update Catalog')]")))
            driver.find_element(By.XPATH, "//span[contains(text(),'Update Catalog')]").click()
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[contains(text(),"Changes has been published")]')
                    )
                ).is_displayed()
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Airline Admin")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            try:
                RT_VALUE = utils.Delete(
                    "ID "+str(UID), PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

            # Login through store user
            Success_List_Append("test_MKTPL_1344_Mapairlinecategory_004_006_008",
                                "Verify select Load Updated Catalogs actions, Verify the go to delta product tab when select load catalogs, Check that Update the delta products by the click on 'Update catalog' button",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1344_Mapairlinecategory_004_006_008",
                            "Verify select Load Updated Catalogs actions, Verify the go to delta product tab when select load catalogs, Check that Update the delta products by the click on 'Update catalog' button",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_1344_Mapairlinecategory_004_006_008",
                             "Verify select Load Updated Catalogs actions, Verify the go to delta product tab when select load catalogs, Check that Update the delta products by the click on 'Update catalog' button", e)
        raise e

def test_MKTPL_3965_categorypath_004_008():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_3965_categorypath_004_008.png"
        ) as driver:
            utils.airline_auto_update_catalog_check_on(driver)
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_CATALOG_1_ID)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "//span[text()='Products']"))
            )
            driver.find_element(By.XPATH, "//span[text()='Products']").click()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        "(//div[text()='"
                        + str(DEDICATED_SKU_1_ID)
                        + "']//ancestor::tr//td)[5]//img",
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                "(//div[text()='"
                + str(DEDICATED_SKU_1_ID)
                + "']//ancestor::tr//td)[5]//img",
            ).click()
            time.sleep(3)
            # Open Code if modal appears
            try:
                driver.find_element(By.XPATH, xpath.yes).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the Product")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "(//div[contains(@class,'category')]//input)")
                )
            )
            driver.find_element(
                By.XPATH, "(//div[contains(@class,'category')]//input)"
            ).clear()
            logging.info("Cleared the category input field")
            driver.find_element(
                By.XPATH, "//div[contains(text(),'Product Needs')]"
            ).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "(//div[contains(@class,'category')]//input)")
                )
            )
            driver.find_element(
                By.XPATH, "(//div[contains(@class,'category')]//input)"
            ).send_keys(Keys.DOWN, Keys.DOWN, Keys.ENTER)
            time.sleep(3)
            logging.info("Chose a category")
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "(//span[@class='x-tab-close-btn'])[1]")
                )
            )
  
            driver.find_element(
                By.XPATH, "(//span[@class='x-tab-close-btn'])[1]"
            ).click()
            logging.info("Closed this tab")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Save & Publish"]')
                )
            )
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[contains(text(),"Saved")]')
                )
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "(//div[contains(@class,'category')]//ul[@data-ref='itemList']//div)[1]",
                    )
                )
            )
            categories_new = driver.find_element(
                By.XPATH,
                "(//div[contains(@class,'category')]//ul[@data-ref='itemList']//div)[1]",
            ).text
            logging.info(categories_new)
            # Logout
            driver.find_element(By.XPATH, xpath.logout).click()
            logging.info("Logged out from Store Admin")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "username"))
            )
            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # utils.search_by_id(driver, Automation_Catalog)
            # utils.close_All_Tabs(driver)
            utils.search_by_id(driver, DEDICATED_CATALOG_1_ID)
            driver.find_element(By.XPATH, "//span[contains(@class,'reload')]").click()
            time.sleep(3)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "//span[text()='Products']"))
            )
            driver.find_element(By.XPATH, "//span[text()='Products']").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "(//div[text()='"
                        + str(DEDICATED_SKU_1_ID)
                        + "']//ancestor::tr//td)[4]//div",
                    )
                )
            )
            categories = driver.find_element(
                By.XPATH,
                "(//div[text()='"
                + str(DEDICATED_SKU_1_ID)
                + "']//ancestor::tr//td)[4]//div",
            ).text
            logging.info(categories)
            assert categories_new in categories
            logging.info("Asserted the updated path in Catalog")
            # utils.close_All_Tabs(driver)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "(//span[@class='x-tab-close-btn'])[1]")
                )
            )
            driver.find_element(
                By.XPATH, "(//span[@class='x-tab-close-btn'])[1]"
            ).click()
            logging.info("Closed this tab")
            time.sleep(5)
            utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_5_ID)
            driver.find_element(By.XPATH, "//span[contains(@class,'reload')]").click()
            time.sleep(3)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "//span[text()='Products']"))
            )
            driver.find_element(By.XPATH, "//span[text()='Products']").click()
            time.sleep(3)
            categories1 = driver.find_element(
                By.XPATH,
                "(//div[text()='"
                + str(DEDICATED_SKU_1_ID)
                + "']//ancestor::tr//td)[7]//div",
            ).text
            logging.info(categories1)
            categories_new = categories_new.split("(")[0]
            assert categories_new in categories1
            logging.info("Asserted the updated path in Catalog Assignment")
            Success_List_Append(
                "test_MKTPL_3965_categorypath_004_008",
                "Verify select Load Updated Catalogs actions, Verify the go to delta product tab when select load catalogs, Check that Update the delta products by the click on 'Update catalog' button",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3965_categorypath_004_008",
            "Verify select Load Updated Catalogs actions, Verify the go to delta product tab when select load catalogs, Check that Update the delta products by the click on 'Update catalog' button",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3965_categorypath_004_008",
            "Verify select Load Updated Catalogs actions, Verify the go to delta product tab when select load catalogs, Check that Update the delta products by the click on 'Update catalog' button",
            e,
        )
        raise e
#endregion

