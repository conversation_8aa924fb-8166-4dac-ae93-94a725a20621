import random
from random import randint
from datetime import datetime
from jamatest import jamatest
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import credencys_test_ui.credencys_project_configs.utils as utils
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
import string, logging, os, time, csv, requests, json, pandas as pd, pytest
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append, Skip_List_Append

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

@pytest.fixture(scope = 'module')
def PAC_Login():
    try:
        with utils.services_context_wrapper_optimized(
                    True, None, False
                ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
    except:
        logging.info("Login not happened")
        Skip_List_Append(58)
        assert False

@jamatest.test(11975433)
def test_MKTPL_1241_Selectmultiplesectorsinflight_003(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1241_Selectmultiplesectorsinflight_003.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "Flight")
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            RANDOM_NUMBER = "".join([str(randint(0, 9)) for i in range(4)])
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.flightRouteNumberFlightFieldXpath)
                    )
                )
                .send_keys(RANDOM_NUMBER)
            )
            driver.find_element(
                By.XPATH, '(//input[@title="Expected date format Y-m-d."])[1]'
            ).send_keys("2023-08-24")
            driver.find_element(
                By.XPATH,
                '(//span[text()="Flight Begin Date Time "]//parent::span//parent::label//'
                "parent::div//input)[2]",
            ).send_keys("01:45")
            driver.find_element(
                By.XPATH, '(//input[@title="Expected date format Y-m-d."])[2]'
            ).send_keys("2023-08-26")
            driver.find_element(
                By.XPATH,
                '(//span[text()="Flight End Date Time "]//parent::span//parent::label//'
                "parent::div//input)[2]",
            ).send_keys("4:45")
            driver.find_element(
                By.XPATH, '//input[@name="fulfillmentCutOffTimeLimit"]'
            ).send_keys("1")
            driver.find_element(
                By.XPATH,
                '(//span[text()="Airline "]//parent::span//parent::span//parent::label//parent::div//a)[3]',
            ).click()
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "query"))
                )
            )
            driver.find_element(By.NAME, "query").send_keys(
                "Cred Automation Airline"
            )
            driver.find_element(By.NAME, "query").send_keys(
                Keys.ENTER
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]')
                    )
                )
            )
            select_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]'
            )
            action = ActionChains(driver)
            action.double_click(select_airline).perform()
            time.sleep(1)
            driver.find_element(
                By.XPATH, '(//div[text()="Sectors"]/../../../../..//span//span)[1]'
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="sector"]'))
            )
            driver.find_element(By.XPATH, '//input[@name="sector"]').send_keys(
                DEDICATED_SECTOR_1_NAME
            )
            (WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//li[contains(text(),'"+DEDICATED_SECTOR_1_NAME+"')]")
                    )
                )
                .click())
            driver.find_element(By.XPATH, '//input[@name="sector_sequence"]').send_keys(
                "1"
            )

            saved = utils.save_and_publish(driver)
            assert saved
            UID = utils.get_uid(driver)
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, HOME_ID)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Flight", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//input[@name="query"])')
                    )
                )
            )
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/'
                            + DEDICATED_AIRLINE
                            + "/Flight/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert published

            logging.info(f"Created Flight under Airline folder = {published}")
            Success_List_Append(
                "test_MKTPL_1241_Selectmultiplesectorsinflight_003",
                "Flight object should move automatically under Airline Name/Flight folder"
                " stock is YES in store profile",
                "Pass",
            )
            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_AIRLINE_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            logging.info("Admin should receives the pimcore notification")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1241_Selectmultiplesectorsinflight_003",
            "Flight object should move automatically under Airline Name/Flight folder",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1241_Selectmultiplesectorsinflight_003",
            "Flight object should move automatically under Airline Name/Flight folder"
            "manage stock is YES in store profile",
            e,
        )
        raise e

@jamatest.test(11975203)
@jamatest.test(11975205)
def test_MKTPL_1102_InventoryModule_001_003(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1102_InventoryModule_001_003.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(
                By.XPATH, '//span[text()="Open Inventory Module"]'
            ).click()
            redirection = driver.find_element(
                By.ID, "inventory_module-legendTitle"
            ).is_displayed()
            # store = driver.find_element(By.XPATH, '//input[@name="store"]')
            store = driver.find_element(
                By.XPATH, '//div[@id="storeComboBox-trigger-picker"]'
            )
            store.click()
            driver.find_element(
                By.XPATH, '//li[text()="' + DEDICATED_STORE + '"]'
            ).click()
            # store.send_keys(DEDICATED_STORE)
            time.sleep(1)
            # store.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "store")))
                .get_attribute("value")
            )
            value = driver.find_element(By.NAME, "store").get_attribute("value")
            logging.info(value)
            assert redirection and value == DEDICATED_STORE
            logging.info(
                "User is redirect to the Inventory Module screen and user should be able to select the store"
            )
            sku = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="SKU"]'))
                )
                .is_displayed()
            )
            name = driver.find_element(
                By.XPATH, '//span[text()="Name "]'
            ).is_displayed()
            variant_attributes = driver.find_element(
                By.XPATH, '//span[text()="Variant Attributes"]'
            ).is_displayed()
            default_inventory = driver.find_element(
                By.XPATH,
                '(//span[contains(text(),"Default Inventory")])[1]',
            ).is_displayed()
            always_in_Stock = driver.find_element(
                By.XPATH, '//span[text()="Always in Stock"]'
            ).is_displayed()
            qty_threshold = driver.find_element(
                By.XPATH, '//span[text()="Qty Threshold"]'
            ).is_displayed()
            total_qty = driver.find_element(
                By.XPATH, '//span[text()="Total Qty"]'
            ).is_displayed()
            edit = driver.find_element(By.XPATH, '//span[text()="Edit"]').is_displayed()
            open = driver.find_element(By.XPATH, '//span[text()="Open"]').is_displayed()
            assert [
                sku,
                name,
                variant_attributes,
                default_inventory,
                always_in_Stock,
                qty_threshold,
                total_qty,
                edit,
                open,
            ]
            Success_List_Append(
                "test_MKTPL_1102_InventoryModule_001_003",
                "Verify the grid view in the Inventory Module screen",
                "Pass",
            )
            logging.info(
                "User is able to view the list of Product variants in the grid"
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1102_InventoryModule_001_003",
            "Verify the grid view in the Inventory Module screen",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1102_InventoryModule_001_003",
            "Verify the grid view in the Inventory Module screen",
            e,
        )
        raise e

@jamatest.test(11977096)
@jamatest.test(11977097)
@jamatest.test(11977098)
@jamatest.test(11977115)
def test_MKTPL_858_Airlinecategoryimport_001_002_003_020(PAC_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_858_Airlinecategoryimport_001_002_003_020.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "importType")))
                .send_keys("Flight")
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "importType")))
                .send_keys(Keys.ENTER)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
                .click()
            )
            # driver.find_element(By.ID, xpath.downloadCsvButtonId).click()
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE)
            logging.info("User is navigate to the CSV import screen")
            sample_download_button = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
                .is_enabled()
            )
            logging.info(sample_download_button)
            assert sample_download_button
            logging.info(
                "Download Sample file of Front end category button is enabled besides import data"
                " type dropdown"
            )
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE, "r", encoding="utf-8-sig"
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_FORMAT_FLIGHT_SAMPLE == ACTUAL_CSV_DATA
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_FLIGHT_SAMPLE)
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            utils.update_csv(
                "Flight_Sample_Admin.csv", "flightRouteNumber", RANDOM_NAME
            )
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Flight_Sample_Admin.csv", file_path)
            assert saved_message
            # assert save_message
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, HOME_ID)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("Flight", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//input[@name="query"])')
                    )
                )
            )
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/'
                            + DEDICATED_AIRLINE
                            + "/Flight/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert published
            flight = driver.find_element(By.XPATH,
                            '//div[text()="/'
                            + DEDICATED_AIRLINE
                            + "/Flight/"
                            + RANDOM_NAME
                            + '"]')
            actions = ActionChains(driver)
            actions.double_click(flight).perform()
            utils.click_on_yes_no(driver)
            UID = utils.get_uid(driver)
            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_AIRLINE_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

            logging.info(
                "CSV file is be download on the system and sample records should display in CSV"
                "file of Front end category button"
            )

            Success_List_Append(
                "test_MKTPL_858_Airlinecategoryimport_001_002_003_020",
                "Verify by selecting the Front end category"
                "from import data type dropdown",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_858_Airlinecategoryimport_001_002_003_020",
            "Verify by selecting the Front end category "
            "from import data type dropdown",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_858_Airlinecategoryimport_001_002_003_020",
            "Verify by selecting the Front end category "
            "from import data type dropdown",
            e,
        )
        raise e

@jamatest.test(11976051)
@jamatest.test(11976052)
@jamatest.test(11976053)
@jamatest.test(11976059)
def test_MKTPL_303_Routegroupimport_001_002_003_009(PAC_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_303_Routegroupimport_001_002_003_009.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            redirect = store.is_displayed()
            logging.info(redirect)
            assert redirect
            logging.info("User is navigate to the CSV import screen")
            store.send_keys("RouteGroup")
            time.sleep(1)
            store.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
            )
            download_button = driver.find_element(By.ID, xpath.downloadCsvButtonId)
            download_button.is_displayed()
            assert download_button
            logging.info(
                "Download Sample File Of RouteGroup button is enabled besides import data type dropdown"
            )
            download_button.click()
            time.sleep(8)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_ROUTEGROUP_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_ROUTEGROUP_SAMPLE,
                "r",
                encoding="utf-8-sig",
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_SAMPLE_ROUTEGROUP == ACTUAL_CSV_DATA
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_ROUTEGROUP_SAMPLE)

            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            # UPDATE Airline category name, UITemplate, Root type in CSV
            utils.update_csv("RouteGroup_Sample_Admin.csv", "name_en", RANDOM_NAME)

            # Upload Airline Category Updated CSV
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("RouteGroup_Sample_Admin.csv", file_path)
            assert saved_message
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, CRED_AUTOMATION_AIRLINE_FOLDER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))
                )
            )
            select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
            select_class.click()
            select_class.send_keys("RouteGroup", Keys.ENTER)
            select_class.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//input[@name="query"])')
                    )
                )
            )
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                RANDOM_NAME, Keys.ENTER
            )

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//td//div[text()="' + RANDOM_NAME + '"])')
                )
            )
            # UID = utils.get_uid(driver)
            # try:
            #     RT_VALUE = utils.Delete(
            #         UID,
            #         CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL,
            #         CRED_AUTOMATION_AIRLINE_TOKEN,
            #     )
            #     assert RT_VALUE == 200, RT_VALUE
            #     logging.info(f"Data cleaned up successfully.")
            # except Exception as e:
            #     logging.info(f"Error in cleaning up the data.")

            logging.info(
                "CSV file is download on the system and sample records is display in CSV"
            )
            Success_List_Append(
                "test_MKTPL_303_Routegroupimport_001_002_003_009",
                "Verify by clicking on the CSV import" " icon in the left side menu",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_303_Routegroupimport_001_002_003_009",
            "Verify by clicking on the CSV import icon " "in the left side menu",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_303_Routegroupimport_001_002_003_009",
            "Verify by clicking on the CSV import icon " "in the left side menu",
            e,
        )
        raise e

@jamatest.test(11976311)
@jamatest.test(11976312)
def test_MKTPL_366_CRUDCatalog_001_002(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_366_CRUDCatalog_001_002.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "Catalog")
            # time.sleep(5)
            catalog_folder = driver.find_element(
                By.XPATH, '(//span[text()="Catalog"])[1]'
            )
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(catalog_folder).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//span[text()="Catalog" and @class="x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator"])',
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                '(//span[text()="Catalog" and @class="x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator"])',
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )

            name = (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "name")))
                .is_displayed()
            )
            logging.info(f"name display = {name}")

            store = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//span[text()="Store "]')
                    )
                )
                .is_displayed()
            )
            logging.info(f"store display = {store}")

            products = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
                .is_displayed()
            )
            logging.info(f"products display = {products}")
            assert [name, store, products]
            logging.info(
                "Following field should display:" "- Name" "- Store" "- Products"
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "name")))
                .send_keys(RANDOM_NAME)
            )
            driver.find_element(By.XPATH, xpath.saveXpath).click()
            validation = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[text()="Store is Mandatory"]')
                    )
                )
                .is_displayed()
            )
            logging.info(f"validation message display = {validation}")
            assert validation
            logging.info("Validation message is display")
            Success_List_Append(
                "test_MKTPL_366_CRUDCatalog_001_002",
                "Verify the fields displaying in catalog screen "
                "and Verify if mandatory fields are empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_366_CRUDCatalog_001_002",
            "Verify the fields displaying in catalog screen and"
            " Verify if mandatory fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_366_CRUDCatalog_001_002",
            "Verify the fields displaying in catalog screen and"
            " Verify if mandatory fields are empty",
            e,
        )
        raise e

@jamatest.test(11976313)
@jamatest.test(11976314)
def test_MKTPL_366_CRUDCatalog_003_004(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_366_CRUDCatalog_003_004.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "Catalog")
            catalog_folder = driver.find_element(
                By.XPATH, '(//span[text()="Catalog"])[1]'
            )
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(catalog_folder).perform()

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//span[text()="Catalog" and @class="x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator"])',
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                '(//span[text()="Catalog" and @class="x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator"])',
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "name")))
                .send_keys(RANDOM_NAME)
            )
            driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).click()
            driver.implicitly_wait(0)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "query"))
                )
            )
            driver.find_element(By.NAME, "query").send_keys(
                "Cred Automation Store", Keys.ENTER
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[text()="/Stores/' + DEDICATED_STORE + '"]')
                    )
                )
            )
            select_store = driver.find_element(
                By.XPATH, '//div[text()="/Stores/' + DEDICATED_STORE + '"]'
            )
            action = ActionChains(driver)
            action.double_click(select_store).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Saved successfully!"]')
                )
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search "]',
                        )
                    )
                )
                .click()
            )
            driver.find_element(By.NAME, "query").send_keys(
                DEDICATED_PRODUCT_6_ID,
                Keys.ENTER,
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="/'
                        + DEDICATED_STORE
                        + "/Products/"
                        + DEDICATED_CATEGORY_3_NAME
                        + "/"
                        + DEDICATED_CATEGORY_2_NAME
                        + "/"
                        + DEDICATED_PRODUCT_6_NAME
                        + '"]',
                    )
                )
            )
            select_product = driver.find_element(
                By.XPATH,
                '//div[text()="/'
                + DEDICATED_STORE
                + "/Products/"
                + DEDICATED_CATEGORY_3_NAME
                + "/"
                + DEDICATED_CATEGORY_2_NAME
                + "/"
                + DEDICATED_PRODUCT_6_NAME
                + '"]',
            )
            action = ActionChains(driver)
            action.double_click(select_product).perform()
            driver.find_element(By.XPATH, '//span[text()="Select"]').click()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Catalog successfully created/updated in the system =  {saved}"
            )
            UID = utils.get_uid(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, STORE_CATALOG_FOLDER)
            time.sleep(2)
            (
                WebDriverWait(driver, 60)
                .until(EC.element_to_be_clickable((By.XPATH, '//input[@name="query"]')))
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )

            listed = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="/'
                            + DEDICATED_STORE
                            + "/Catalog/"
                            + RANDOM_NAME
                            + '"])',
                        )
                    )
                )
                .is_displayed()
            )
            assert listed
            logging.info(
                f"Catalog object should move automatically under Store Name folder/Catalog folder = {listed}"
            )
            logging.info(
                f"Catalog folder should be automatically created and catalog object should move automatically "
                f"under Store Name folder/Catalog folder = {listed}"
            )

            Success_List_Append(
                "test_MKTPL_366_CRUDCatalog_003_004",
                "Verify by entering/updating data in below fields"
                " and Verify if Catalog folder is not created"
                " inside the Store Name folder",
                "Pass",
            )
            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_STORE_CATALOG_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_366_CRUDCatalog_003_004",
            "Verify by entering/updating data in below fields and"
            " Verify if Catalog folder is not created inside the "
            "Store Name folder",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_366_CRUDCatalog_003_004",
            "Verify by entering/updating data in below fields "
            "andVerify if Catalog folder is not created inside "
            "the Store Name folder",
            e,
        )
        raise e

@jamatest.test(11976315)
def test_MKTPL_366_CRUDCatalog_005(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_366_CRUDCatalog_005.png", False
        ) as driver:
            
            utils.search_by_id(driver, STORE_CATALOG_FOLDER)
            id = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="ID" and @class="x-column-header-text-inner"]',
                        )
                    )
                )
                .is_displayed()
            )
            path = driver.find_element(
                By.XPATH,
                '//span[text()="Path" and @class="x-column-header-text-inner"]',
            ).is_displayed()
            creation_date = driver.find_element(
                By.XPATH,
                '//span[text()="Creation Date (System)" and'
                ' @class="x-column-header-text-inner"]',
            ).is_displayed()
            modification_date = driver.find_element(
                By.XPATH,
                '//span[text()="Modification Date (System)" and'
                ' @class="x-column-header-text-inner"]',
            ).is_displayed()
            name = driver.find_element(
                By.XPATH,
                '//span[text()="Name" and @class="x-column-header-text-inner"]',
            ).is_displayed()
            assert [id, path, creation_date, modification_date, name]
            logging.info(
                f"User should able to view the data of catalog  ="
                f" {id, path, creation_date, modification_date, name}"
            )

            Success_List_Append(
                "test_MKTPL_366_CRUDCatalog_005",
                "Verify by clicking on the catalog",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_366_CRUDCatalog_005",
            "Verify by clicking on the catalog",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_366_CRUDCatalog_005", "Verify by clicking on the catalog", e
        )
        raise e

@jamatest.test(11976316)
def test_MKTPL_366_CRUDCatalog_006(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_366_CRUDCatalog_006.png", False
        ) as driver:
            
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Home')]")
                )
            )
            # Move to home icon
            home = driver.find_element(By.XPATH, "//span[contains(text(),'Home')]")
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "(//span[text()='Refresh'])"))
            )
            driver.find_element(By.XPATH, "(//span[text()='Refresh'])").click()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Home')]")
                )
            )
            # Move to home icon
            home = driver.find_element(By.XPATH, "//span[contains(text(),'Home')]")
            actions = ActionChains(driver)
            actions.context_click(home).perform()
            # Move to Add object
            time.sleep(2)
            ADD_OBJECT = driver.find_element(By.XPATH, xpath.addObjectXpath)
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            logging.info("Clicked on Add Object")
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, '(//span[text()="Catalog"])[1]'))
            )
            # Click on Stores
            elm = driver.find_element(By.XPATH, '(//span[text()="Catalog"])[1]')
            ActionChains(driver).move_to_element(elm).perform()
            logging.info(f"Clicked on Catalog")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//span[text()="Catalog" and @class="x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator"])',
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                '(//span[text()="Catalog" and @class="x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator"])',
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "name")))
                .send_keys(RANDOM_NAME)
            )
            driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).click()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name = "query"]'))
            )
            driver.find_element(By.XPATH, '//input[@name = "query"]').send_keys(
                DEDICATED_STORE_ID, Keys.ENTER
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//td//div[text()="' + DEDICATED_STORE_ID + '"]')
                    )
                )
            )

            select_store = driver.find_element(
                By.XPATH, '//td//div[text()="' + DEDICATED_STORE_ID + '"]'
            )
            action = ActionChains(driver)
            action.double_click(select_store).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Saved successfully!"]')
                )
            )
            UID = utils.get_uid(driver)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, xpath.productsTabXpath)
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search "]',
                        )
                    )
                )
                .click()
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name = "query"]'))
            )
            driver.find_element(By.XPATH, '//input[@name = "query"]').send_keys(
                PRODUCT_DIFFERENT_STORE_2, Keys.ENTER
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//td//div[text()="' + PRODUCT_DIFFERENT_STORE_2 + '"]',
                        )
                    )
                )
            )
            select_product = driver.find_element(
                By.XPATH, '//td//div[text()="' + PRODUCT_DIFFERENT_STORE_2 + '"]'
            )
            action = ActionChains(driver)
            action.double_click(select_product).perform()
            driver.find_element(By.XPATH, '//span[text()="Select"]').click()
            logging.info("PRODUCT_DIFFERENT_STORE_2 added.")

            # # Click on Save and Publish
            # WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'Save & Publish')]")))
            # save_and_publish = driver.find_element(By.XPATH, "//span[contains(text(),'Save & Publish')]")
            # save_and_publish.click()
            validation = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(),'from different store')]")
                )
            )
            assert validation
            logging.info(f"Validation message should display {validation}")
            Success_List_Append(
                "test_MKTPL_366_CRUDCatalog_006",
                "Verify if products entered is of different stores",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_366_CRUDCatalog_006",
            "Verify if products entered is of different stores",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_366_CRUDCatalog_006",
            "Verify if products entered is of different stores",
            e,
        )
        raise e

@jamatest.test(11975882)
@jamatest.test(11975883)
@jamatest.test(11975884)
def test_MKTPL_2217_UITemplate_001_002_003(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2217_UITemplate_001_002_003.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "UITemplate")

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            name_field = (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "templateName")))
                .is_displayed()
            )
            driver.find_element(By.NAME, "templateName").send_keys(RANDOM_NAME)
            airline = driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).is_displayed()
            assert [name_field, airline]
            logging.info(
                f"Following field should display:- Name = {name_field} , Airline = {airline}"
            )
            UID = utils.get_uid(driver)
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            validation = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="Validation failed: Empty mandatory field [ airline ]"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert validation
            logging.info(f"Validation message should display  =  {validation}")
            driver.find_element(By.XPATH, '(//span[text()="OK"])[2]').click()
            driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search "]',
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(EC.element_to_be_clickable((By.XPATH, '//input[@name="query"]')))
                .send_keys("Cred Automation Airline", Keys.ENTER)
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="/Airlines/Cred Automation Airline"]')
                )
            )
            select_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/Cred Automation Airline"]'
            )
            action = ActionChains(driver)
            action.double_click(select_airline).perform()
            time.sleep(1)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="/Airlines/Cred Automation Airline" and @class = "x-form-display-field x-form-display-field-default"]',
                    )
                )
            )
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"UI template should be successfully created/updated in the system = {saved}"
            )
            utils.clickOnReload(driver)
            # driver.refresh()
            # utils.wait_for_style_attribute(driver, 40)
            read_only = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            xpath.readModeLock,
                        )
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"Airline field should become read only once object is published = {read_only}"
            )
            utils.search_by_id(driver, UI_TEMPLATE_FOLDER)
            time.sleep(3)
            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "(//span[contains(@class,'reload')])[last()]")
                    )
                )
                driver.find_element(
                    By.XPATH, "(//span[contains(@class,'reload')])[last()]"
                ).click()
            except:
                logging.info("No reload")
                pass
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="query"]')
                    )
                )
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            ui_template = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Master Data/UI Templates/'
                            + DEDICATED_AIRLINE
                            + "/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"UI template object should move automatically under Master Data/UI Templates/Airline"
                f" Name folder = {ui_template}"
            )

            Success_List_Append(
                "test_MKTPL_2217_UITemplate_001_002_003",
                "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2217_UITemplate_001_002_003",
            "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2217_UITemplate_001_002_003",
            "Verify the fields displaying in UI template screen and Verify if mandatory fields are empty",
            e,
        )
        raise e

@jamatest.test(11977675)
@jamatest.test(11976407)
def test_MKTPL_2466_CRUDRoottype_003_MKTPL_387_Login_001(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_DESC = "".join(random.choices(string.ascii_letters, k=20))

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2466_CRUDRoottype_003_MKTPL_387_Login_001.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "RootType")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "rootTypeName")))
                .send_keys(RANDOM_NAME)
            )
            driver.find_element(By.NAME, "description").send_keys(RANDOM_DESC)
            driver.find_element(
                By.XPATH,
                '(//span[text()="Airline "]//parent::span//parent::span//parent::label//'
                "parent::div//a)[3]",
            ).click()
            WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.NAME,
                            'query',
                        )
                    )
                )
            driver.find_element(By.NAME, "query").send_keys(DEDICATED_AIRLINE)
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]',
                        )
                    )
                )
            )
            select_and_airline = driver.find_element(
                By.XPATH, '//div[text()="/Airlines/' + DEDICATED_AIRLINE + '"]'
            )
            time.sleep(2)
            action = ActionChains(driver)
            action.double_click(select_and_airline).perform()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Root Type should be successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            utils.clickOnReload(driver)
            # driver.refresh()
            # utils.wait_for_style_attribute(driver, 40)
            read_only = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            xpath.readModeLock,
                        )
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"Airline field should become read only once object is published = {read_only}"
            )
            utils.search_by_id(driver, ROOT_TYPE_FOLDER)
            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "(//span[contains(@class,'reload')])[last()]")
                    )
                )
                driver.find_element(
                    By.XPATH, "(//span[contains(@class,'reload')])[last()]"
                ).click()
            except:
                logging.info("No reload")
                pass
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//input[@name="query"])')
                    )
                )
            )
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                RANDOM_NAME, Keys.ENTER
            )
            created_root_type = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Master Data/Root Type/'
                            + DEDICATED_AIRLINE
                            + "/"
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            logging.info(
                f"Root Type object should move automatically under Master Data/Root Type/Airline Name"
                f" folder = {created_root_type}"
            )
            Success_List_Append(
                "test_MKTPL_2466_CRUDRoottype_003_MKTPL_387_Login_001",
                "Verify by entering/updating data in below fields",
                "Pass",
            )
            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_AIRLINE_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2466_CRUDRoottype_003_MKTPL_387_Login_001",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2466_CRUDRoottype_003_MKTPL_387_Login_001",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

# def test_MKTPL_387_Login_001(PAC_Login):
#     try:
#         with utils.services_context_wrapper_optimized(
#             False, "test_MKTPL_387_Login_001.png", True
#         ) as driver:
#             driver.maximize_window()
#             utils.Pac_Credentials.Login_Pac_Admin(driver)
#             driver.implicitly_wait(10)
#             act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
#             assert act_title == "pimcore_logout"
#             logging.info("PAC Admin Login successfully.")
#             Success_List_Append(
#                 "test_MKTPL_387_Login_001",
#                 "Verify by entering Airline valid credentials",
#                 "Pass",
#             )
#     except Exception as e:
#         logging.info(f"Error- {e}")
#         Success_List_Append(
#             "test_MKTPL_387_Login_001",
#             "Verify by entering Airline valid credentials",
#             "Fail",
#         )
#         Failure_Cause_Append(
#             "test_MKTPL_387_Login_001",
#             "Verify by entering Airline valid credentials",
#             e,
#         )
#         raise e

@jamatest.test(11976327)
@jamatest.test(11976328)
@jamatest.test(11976329)
@jamatest.test(11975879)
def test_MKTPL_368_Onboardstore_003_004_005_MKTPL_1679_BypassPayment_001(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_NAME_STORE = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_368_Onboardstore_003_004_005_MKTPL_1679_BypassPayment_001.png", False
        ) as driver:
            
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="Home"]'))
                )
            )
            utils.click_on_add_object(driver, "Stores")

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME_STORE)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "storeName")))
                .send_keys(RANDOM_NAME)
            )
            driver.find_element(By.NAME, "description").send_keys("creating a store")
            driver.find_element(
                By.XPATH, '//span[text()="Address Information"]'
            ).click()
            driver.find_element(By.NAME, "firstName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "addressLine1").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "addressLine2").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "city").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "state").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            driver.find_element(By.XPATH, xpath.configuration_tab).click()
            driver.find_element(By.NAME, "manageStock").send_keys("Yes", Keys.ENTER)
            driver.find_element(By.NAME, "manageShipping").send_keys("Yes", Keys.ENTER)
            # driver.find_element(By.NAME, 'unit_of_measure').send_keys('Imperial System', Keys.ENTER)
            # driver.find_element(By.NAME, 'defaultCurrency').send_keys('US Dollar', Keys.ENTER)
            driver.find_element(By.NAME, "taxPercentage").send_keys("1", Keys.ENTER)
            driver.find_element(By.NAME, "orderProvider").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "salesStrategy").send_keys("D2C", Keys.ENTER)
            time.sleep(1)
            driver.find_element(By.NAME, "storeLanguage").send_keys("Hindi", Keys.ENTER)
            time.sleep(1)
            payment_configuration = driver.find_element(
                By.XPATH, '//span[text()="Payment Configuration"]'
            ).is_displayed()
            associated_airline = driver.find_element(
                By.XPATH, '//span[text()="Associated Airline"]'
            ).is_displayed()
            assert payment_configuration, associated_airline
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            driver.implicitly_wait(0)
            saved = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
                .is_displayed()
            )
            logging.info(f"saved message = {saved}")
            assert saved
            logging.info(
                "Store profile information should be successfully created/updated in the system"
            )
            try:
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[@class = "x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default"])[1]',
                        )
                    )
                )
                UID = driver.find_element(
                    By.XPATH,
                    '(//div[@class = "x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default"])[1]',
                ).text
                logging.info(f"Unique ID: {UID}")
            except Exception as e:
                UID = None
                pass
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_path(driver, "/Stores")
            # (
            #     WebDriverWait(driver, 60)
            #     .until(
            #         EC.presence_of_element_located(
            #             (By.XPATH, '(//span[text()="Home"]/..//a)[2]')
            #         )
            #     )
            #     .click()
            # )
            # driver.find_element(By.NAME, "filter").send_keys("Stores", Keys.ENTER)
            # (
            #     WebDriverWait(driver, 60)
            #     .until(
            #         EC.presence_of_element_located(
            #             (By.XPATH, '(//span[text()="Stores"]/..//div)[2]')
            #         )
            #     )
            #     .click()
            # )
            try:
                (
                    WebDriverWait(driver, 60)
                    .until(
                        EC.presence_of_element_located(
                            (By.NAME, 'selectClass')
                        )
                    )
                    .send_keys("Stores")
                )
                (
                    WebDriverWait(driver, 60)
                    .until(
                        EC.presence_of_element_located(
                            (By.XPATH, "//li[text()='Stores']")
                        )
                    )
                )
                driver.find_element(By.XPATH, "//li[text()='Stores']").click()
            except:
                pass
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.NAME, 'query')
                    )
                )
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )

            listed = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[text()="'+RANDOM_NAME+'"])[1]')
                    )
                )
                .is_displayed()
            )
            logging.info(f"created store under store folder = {listed}")
            assert listed
            logging.info(
                "Stores folder is automatically created and store object should move automatically under "
                "Stores folder"
            )
            store = driver.find_element(
                By.XPATH, '(//div[text()="'+RANDOM_NAME+'"])[1]'
            )
            action = ActionChains(driver)
            action.context_click(store).perform()
            # Click on Open
            open = driver.find_element(By.XPATH, xpath.open)
            open.click()
            details = (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "storeName")))
                .is_displayed()
            )
            logging.info(f"Showing details of store = {details}")
            assert details
            logging.info("User is able to view the information of store profile")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//span[text()="Payment Configuration"]')
                    )
                )
                .click()
            )
            driver.find_element(By.NAME, "bypassPACPayment").click()
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            driver.implicitly_wait(0)
            saved = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
                .is_displayed()
            )
            logging.info(f"saved message = {saved}")
            assert saved
            # driver.refresh()
            utils.clickOnReload(driver)
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//span[text()="Payment Configuration"]')
                    )
                )
                .click()
            )
            check_box = driver.find_element(By.NAME, "bypassPACPayment").is_selected()
            assert check_box == True
            time.sleep(3)
            logging.info(f"Checkbox selected: {check_box}")
            driver.find_element(By.NAME, "bypassPACPayment").click()
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            driver.implicitly_wait(0)
            saved = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
                .is_displayed()
            )
            logging.info(f"saved message = {saved}")
            assert saved
            logging.info(
                f"Bypass PAC Payment should be successfully created/updated in the store = {check_box}"
            )
            check_box = driver.find_element(By.NAME, "bypassPACPayment").is_selected()
            assert check_box == False
            time.sleep(3)
            logging.info(f"Checkbox selected: {check_box}")
            driver.find_element(By.NAME, "bypassPACPayment").click()
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            driver.implicitly_wait(0)
            saved = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
                .is_displayed()
            )
            logging.info(f"saved message = {saved}")
            assert saved
            logging.info(
                f"Bypass PAC Payment should be successfully created/updated in the store = {check_box}"
            )
            logging.info(f"Bypass PAC Payment can be checked and unchecked")

            Success_List_Append(
                "test_MKTPL_368_Onboardstore_003_004_005_MKTPL_1679_BypassPayment_001",
                "Verify by entering/updating data in below fields",
                "Pass",
            )
            # try:
            #     RT_VALUE = utils.Delete(UID, CRED_AUTOMATION_AIRLINE_URL, CRED_AUTOMATION_AIRLINE_TOKEN)
            #     assert RT_VALUE == 200, RT_VALUE
            #     logging.info(f"Data cleaned up successfully.")
            # except Exception as e:
            #     logging.info(f"Error in cleaning up the data.")
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_368_Onboardstore_003_004_005_MKTPL_1679_BypassPayment_001",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_368_Onboardstore_003_004_005_MKTPL_1679_BypassPayment_001",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

def test_MKTPL_1065_CategorychangesinProduct_002(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1065_CategorychangesinProduct_002.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.createNewProduct).click()
            store = driver.find_element(By.NAME, "store")
            store.click()
            store.send_keys(DEDICATED_STORE_1_NAME, Keys.ENTER)
            time.sleep(3)
            # store.send_keys(Keys.ENTER)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Product Set')]"
            ).click()
            (
                WebDriverWait(driver, 60)
                .until(EC.element_to_be_clickable((By.NAME, "category")))
                .click()
            )
            time.sleep(2)
            list_of_cat = driver.find_elements(
                By.XPATH,
                '(//div[@class="x-boundlist-list-ct x-unselectable x-scroller"])[2]//li',
            )
            logging.info(len(list_of_cat))
            for i in list_of_cat:
                logging.info(i.text)
                assert DEDICATED_STORE_1_NAME in i.text
            logging.info(
                f"Category dropdown show category based on the store sales strategy"
            )
            Success_List_Append(
                "test_MKTPL_1065_CategorychangesinProduct_002",
                "Verify the category field",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1065_CategorychangesinProduct_002",
            "Verify the category field",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1065_CategorychangesinProduct_002",
            "Verify the category field",
            e,
        )
        raise e

def test_MKTPL_1065_CategorychangesinProduct_009_010(PAC_Login):
    CAT_LIST = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1065_CategorychangesinProduct_009_010.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            data_type = driver.find_element(By.NAME, "importType")
            data_type.send_keys("Category")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.ID, "salesStrategy-trigger-picker")
                    )
                )
                .click()
            )
            time.sleep(2)
            list_of_cat = driver.find_elements(
                By.XPATH, '//div[@id="salesStrategy-picker-listWrap"]//li'
            )
            logging.info(len(list_of_cat))
            for i in list_of_cat:
                logging.info(i.text)
                CAT_LIST.append(i.text)
            logging.info(CAT_LIST)
            assert ACTUAL_LIST_1065 == CAT_LIST
            logging.info(
                f"Category dropdown show category based on the store sales strategy"
            )
            sales = driver.find_element(By.NAME, "salesStrategy")
            sales.send_keys("Marketplace")
            time.sleep(1)
            sales.send_keys(Keys.ENTER)
            download_icon = driver.find_element(
                By.ID, xpath.downloadCsvButtonId
            ).is_enabled()
            assert download_icon
            logging.info(
                f"It should only be enable after selecting store and category = {download_icon}"
            )
            Success_List_Append(
                "test_MKTPL_1065_CategorychangesinProduct_009_010",
                "Verify the category field",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1065_CategorychangesinProduct_009_010",
            "Verify the category field",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1065_CategorychangesinProduct_009_010",
            "Verify the category field",
            e,
        )
        raise e

@jamatest.test(11976693)
@jamatest.test(11976694)
@jamatest.test(11976695)
@jamatest.test(11976696)
@jamatest.test(11976702)
def test_MKTPL_516_379_Airportimport__001_002_003_004_010(PAC_Login):
    global driver
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_516_379_Airportimport__001_002_003_004_010.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()

            data = wait.until(
                EC.presence_of_element_located(
                    ("xpath", '//div[@id="importType-trigger-picker"]')
                )
            )
            data.click()
            logging.info("Import data type selected..")
            category = wait.until(
                EC.presence_of_element_located(("xpath", '//input[@name="importType"]'))
            )
            time.sleep(2)
            category.send_keys("Airports")
            logging.info("Airports entered..")
            time.sleep(2)

            download = wait.until(
                EC.element_to_be_clickable(
                    ("xpath", '//span[@id="download_CSV_button-btnInnerEl"]')
                )
            )
            download.click()
            logging.info("Download button clicked..")
            time.sleep(2)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_AIRPOT_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_AIRPOT_SAMPLE, "r", encoding="utf-8-sig"
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
                logging.info(ACTUAL_CSV_DATA)
            assert CSV_SAMPLE_AIRPOTS == ACTUAL_CSV_DATA
            logging.info(
                f"CSV file should be download on the system and sample records should display in CSV"
            )
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_AIRPOT_SAMPLE)

            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            random_number = "".join([str(randint(0, 9)) for i in range(4)])
            random_num = "".join([str(randint(0, 9)) for i in range(3)])

            utils.update_csv("Airports_Sample.csv", "airportName_en", RANDOM_NAME)
            utils.update_csv("Airports_Sample.csv", "icao_code", random_number)
            utils.update_csv("Airports_Sample.csv", "IATACode", random_num)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Airports_Sample.csv", file_path)
            assert saved_message
            logging.info(
                f"Airports should be successfully imported in the system = {saved_message}"
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, AIRPORTS_FOLDER)
            time.sleep(3)
            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "(//span[contains(@class,'reload')])[last()]")
                    )
                )
                driver.find_element(
                    By.XPATH, "(//span[contains(@class,'reload')])[last()]"
                ).click()
            except:
                logging.info("No reload")
                pass
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="query"]'))
            ).send_keys(RANDOM_NAME, Keys.ENTER)
            record = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[text()="' + RANDOM_NAME + '"])[1]')
                    )
                )
                .is_displayed()
            )
            assert record
            logging.info(
                f"Airport Category object move automatically under Master Data section =  {record}"
            )
            UID = utils.get_uid(driver)

            logging.info(
                "Verify that User is able to download Sample file and it moves to Master Data.."
            )
            Success_List_Append(
                "test_MKTPL_516_379_Airportimport__001_002_003_004_010",
                "Verify that User is able to download Sample file and it moves to Master Data",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_516_379_Airportimport__001_002_003_004_010",
            "Verify that User is able to download Sample file and it moves to Master Data",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_516_379_Airportimport__001_002_003_004_010",
            "Verify that User is able to download Sample file and it moves to Master Data",
            e,
        )
        raise e

@jamatest.test(11976704)
@jamatest.test(11976705)
@jamatest.test(11976706)
@jamatest.test(11976708)
@jamatest.test(11976711)
def test_MKTPL_517_377_Aircraftimport_001_002_003_004_008(PAC_Login):
    # global driver
    ACTUAL_CSV_DATA = []

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_517_377_Aircraftimport_001_002_003_004_008.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            redirect = store.is_displayed()
            logging.info(redirect)
            assert redirect
            logging.info(f"User navigate to the CSV import screen = {redirect}")
            wait = WebDriverWait(driver, 60)
            data = wait.until(
                EC.presence_of_element_located(
                    ("xpath", '//div[@id="importType-trigger-picker"]')
                )
            )
            data.click()
            category = wait.until(
                EC.presence_of_element_located(("xpath", '//input[@name="importType"]'))
            )
            category.send_keys("AirCraft", Keys.ENTER)
            time.sleep(2)
            selected_category = driver.find_element(
                "xpath", '//input[@id="importType-inputEl"]'
            ).get_attribute("value")
            logging.info(selected_category)
            if selected_category == "AirCraft":
                success = True
            download = wait.until(
                EC.element_to_be_clickable((By.ID, xpath.downloadCsvButtonId))
            ).is_displayed()
            logging.info(download)
            assert download and success
            logging.info(
                "User is navigate to the CSV import screen and able to select Import data type "
                "and download Sample file button is clickable.."
            )
            # driver.find_element(By.ID, xpath.downloadCsvButtonId).click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
                .click()
            )
            time.sleep(2)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_AIRCRAFT_CATEGORY_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_AIRCRAFT_CATEGORY_SAMPLE,
                "r",
                encoding="utf-8-sig",
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_SAMPLE_AIRCRAFT == ACTUAL_CSV_DATA
            logging.info(
                f"CSV file should be download on the system and sample records should display in CSV"
            )
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_AIRCRAFT_CATEGORY_SAMPLE)

            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            df = pd.read_csv(os.getcwd() + "/credencys_test_ui/" + "AirCraft_Sample.csv")
            df.loc[0, "OEMName_en"] = RANDOM_NAME
            df.to_csv(os.getcwd() + "/credencys_test_ui/" + "AirCraft_Sample.csv", index=False)
            path = os.getcwd() + "/credencys_test_ui/" + "AirCraft_Sample.csv"
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.upload).click()
            saved_message = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[contains(text(),"CSV Uploaded Successfully.")]',
                        )
                    )
                )
                .is_displayed()
            )
            assert saved_message
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, AIRCRAFT_FOLDER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//input[@name="query"])')
                    )
                )
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            created_aircraft = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[text()="' + RANDOM_NAME + '"])[1]')
                    )
                )
                .click()
            )
            UID = utils.get_uid(driver)
            logging.info(
                f"Root Type object should move automatically under Master Data/Root Type/Airline Name"
                f" folder = {created_aircraft}"
            )
            Success_List_Append(
                "test_MKTPL_517_377_Aircraftimport_001_002_003_004_008",
                "Verify by entering/updating data in below fields",
                "Pass",
            )

    except Exception as e:
        Success_List_Append(
            "test_MKTPL_517_377_Aircraftimport_001_002_003_004_008",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_517_377_Aircraftimport_001_002_003_004_008",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

@jamatest.test(11976345)
def test_MKTPL_372_CRUDRoutegroup_003(PAC_Login):
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_372_CRUDRoutegroup_003.png", False
        ) as driver:
            

            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "RouteGroup")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(EC.presence_of_element_located((By.NAME, "name"))).send_keys(
                RANDOM_NAME
            )
            driver.find_element(By.NAME, "code").send_keys("testcode")
            airline = driver.find_element(By.NAME, "airline")
            airline.clear()
            airline.send_keys(DEDICATED_AIRLINE)

            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Route group is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)

            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_FOLDER_4_ID)
            driver.implicitly_wait(0)
            utils.clickOnReload(driver)
            # //input[@name="selectClass"]
            try:
                (
                    WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located(
                            (By.XPATH, xpath.selectClassXpath)
                        )
                    )
                )
                select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
                select_class.click()
                select_class.send_keys("RouteGroup", Keys.ENTER)
                select_class.send_keys(Keys.ENTER)
            except:
                logging.info("SelectClass UI doesnot show")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="query"]')
                    )
                )
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            new_route = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//td//div[text()="' + RANDOM_NAME + '"]')
                    )
                )
                .is_displayed()
            )
            assert new_route
            logging.info(
                f"New created RouteGroup is moved under Airline name folder/RouteGroup folder = {new_route}"
            )
            logging.info(
                "Verify RouteGroup is created/updated in the system, moved to Airline name folder/RouteGroup folder."
            )
            try:
                RT_VALUE = utils.Delete(
                    UID,
                    CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL,
                    CRED_AUTOMATION_AIRLINE_TOKEN,
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            logging.info(
                "Verify RouteGroup is created/updated in the system, moved to Airline name folder/RouteGroup folder."
            )
            Success_List_Append(
                "test_MKTPL_372_CRUDRoutegroup_003",
                "Verify RouteGroup is created/updated in the system, "
                "moved to Airline name folder/RouteGroup folder.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_372_CRUDRoutegroup_003",
            "Verify RouteGroup is created/updated in the system, "
            "moved to Airline name folder/RouteGroup folder.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_372_CRUDRoutegroup_003",
            "Verify RouteGroup is created/updated in the system, "
            "moved to Airline name folder/RouteGroup folder.",
            e,
        )
        raise e

@jamatest.test(11976350)
def test_MKTPL_374_AirlineProfile_003(PAC_Login):
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_374_AirlineProfile_003.png", False
        ) as driver:
            

            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Airline")
            time.sleep(1)
            mouse_hover = ActionChains(driver)
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            mouse_hover.move_to_element(airline).perform()
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[2]')
            airline.click()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "airlineName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "description").send_keys(
                "creating airline description for automation test"
            )
            driver.find_element(By.NAME, "ICAOCode").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "iataCode").send_keys(RANDOM_NAME)
            driver.find_element(
                By.XPATH, '//span[text()="Contact & Address Information"]'
            ).click()
            driver.find_element(By.NAME, "firstName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "company").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "addressLine1").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "addressLine2").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "city").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "state").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "zipCode").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@Gmail.com")
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Airline is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            # wait.until(
            #     EC.presence_of_element_located((By.XPATH, '(//span[text()="Home"]/..//a)[2]')))
            utils.search_by_id(driver, AIRLINES_FOLDER)
            time.sleep(3)
            try:
                (
                    WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located(
                            (By.XPATH, xpath.selectClassXpath)
                        )
                    )
                )
                select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
                select_class.click()
                select_class.send_keys("Airline", Keys.ENTER)
                select_class.send_keys(Keys.ENTER)
            except:
                logging.info("SelectCalss doesnot show")
                pass
            # WebDriverWait(driver, 60).until(
            #     EC.element_to_be_clickable((By.XPATH, "(//span[contains(@class,'reload')])[1]")))
            # driver.find_element(By.XPATH, "(//span[contains(@class,'reload')])[1]").click()
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//input[@name="query"]'))
            )
            driver.find_element(By.XPATH, '//input[@name="query"]').send_keys(
                RANDOM_NAME, Keys.ENTER
            )
            new_airline = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[text()="' + RANDOM_NAME + '"])[1]')
                )
            ).is_displayed()
            assert new_airline
            logging.info(
                f"New created Airline is moved under Airlines folder = {new_airline}"
            )
            logging.info(
                "Verify Airline is created/updated in the system, moved to Airlines folder."
            )
            Success_List_Append(
                "test_MKTPL_374_AirlineProfile_003",
                "Verify Airline is created/updated in the system, "
                "moved to Airlines folder.",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_374_AirlineProfile_003",
            "Verify Airline is created/updated in the system, moved to Airlines folder.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_374_AirlineProfile_003",
            "Verify Airline is created/updated in the system, moved to Airlines folder.",
            e,
        )
        raise e

@jamatest.test(11976368)
def test_MKTPL_380_CRUDAirport_003(PAC_Login):
    global driver
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_CODE = "".join(random.choices(string.digits, k=5))
    icao_code_dummy = "".join(random.choices(string.ascii_letters, k=6))
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_380_CRUDAirport_003.png", False
        ) as driver:
            

            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Airports")
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "airportName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "icao_code").send_keys(icao_code_dummy)
            driver.find_element(By.NAME, "IATACode").send_keys(RANDOM_CODE)
            driver.find_element(By.NAME, "address").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "city").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "state").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "zipCode").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "country").send_keys("India")
            driver.find_element(By.NAME, "timeZone").send_keys("Asia/Kolkata")
            driver.find_element(By.NAME, "timeZone").send_keys(Keys.ENTER)

            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Airport is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)

            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, AIRPORTS_FOLDER)

            airport_search = wait.until(
                EC.visibility_of_element_located((By.XPATH, '(//input[@name="query"])'))
            )
            airport_search.send_keys(RANDOM_NAME, Keys.ENTER)
            time.sleep(1)
            new_airport = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[text()="' + RANDOM_NAME + '"])[last()]')
                )
            ).is_displayed()
            assert new_airport
            logging.info(
                f"New created Airport is moved under Master Data/Airport folder = {new_airport}"
            )
            # utils.Delete API not avail from Airport.
            logging.info(
                "Verify Airport is created/updated in the system, moved to Master Data/Airport folder."
            )
            Success_List_Append(
                "test_MKTPL_380_CRUDAirport_003",
                "Verify Airport is created/updated in the system, "
                "moved to Master Data/Airport folder.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_380_CRUDAirport_003",
            "Verify Airport is created/updated in the system, "
            "moved to Master Data/RouteGroup folder.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_380_CRUDAirport_003",
            "Verify Airport is created/updated in the system, "
            "moved to Master Data/RouteGroup folder.",
            e,
        )
        raise e

def test_MKTPL_2959_Defaultcurrencystore_001_002_004(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_NAME_STORE = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2959_Defaultcurrencystore_001_002_004.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "Stores")
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME_STORE)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "storeName"))
            ).send_keys(RANDOM_NAME_STORE)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Address Information"]')
                )
            ).click()
            driver.find_element(By.NAME, "firstName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.configuration_tab)
                )
            ).click()
            driver.find_element(By.NAME, "manageStock").send_keys("Yes")
            driver.find_element(By.NAME, "salesStrategy").send_keys("D2C")
            saved = utils.save_and_publish(driver)
            assert saved
            utils.clickOnReload(driver)
			# driver.refresh()
            # utils.wait_for_style_attribute(driver, 40)
            # --------------------------------------------------------------------------------

            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.configuration_tab)
                    )
                )
                .click()
            )
            scroll = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//input[@name="defaultCurrency"]')
                )
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="defaultCurrency"]')
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="defaultCurrency"]')
                    )
                )
                .clear()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="defaultCurrency"]')
                    )
                )
                .send_keys("JPY")
            )
            time.sleep(1)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="defaultCurrency"]')
                    )
                )
                .send_keys(Keys.ENTER)
            )
            logging.info("changed currency")
            saved = utils.save_and_publish(driver)
            assert saved
            default_currency = driver.find_element(By.NAME, "defaultCurrency")
            currency_text = default_currency.get_attribute("value")
            assert "JPY" in currency_text and saved
            logging.info(
                f"Store is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)

            logging.info(
                "PAC Users should be able to add default currency in store profile at the time of "
                "onboarding the store"
            )
            payload = {}
            headers = {"Authorization": CRED_AUTOMATION_STORE_TOKEN}
            response = requests.request(
                "GET", STORE_GET_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info("-------GET-------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "defaultCurrency" in resp_data["data"]["store"]
            logging.info(
                f"defaultCurrency parameter display in the response of GET Store API"
            )

            Success_List_Append(
                "test_MKTPL_2959_Defaultcurrencystore_001_002_004",
                "Verify default currency is added in "
                "store profile at onboarding process",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_2959_Defaultcurrencystore_001_002_004",
            "Verify default currency is added in "
            "store profile at onboarding process",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2959_Defaultcurrencystore_001_002_004",
            "Verify default currency is added in "
            "store profile at onboarding process",
            e,
        )
        raise e

def test_MKTPL_1646_SNS_001_002_003_MKTPL_1972_SNS_001_MKTPL_2959_Defaultcurrencystore_003(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1646_SNS_001_002_003_MKTPL_1972_SNS_001_MKTPL_2959_Defaultcurrencystore_003.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "Stores")
            # Enter store name
            store_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                store_name
            )
            # Click on ok
            ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
            ok.click()
            logging.info(f"Clicked on ok after entering Store name as {store_name}")
            # input store name
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='storeName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='storeName']").send_keys(
                store_name
            )
            # Click on address info
            address_information = driver.find_element(
                By.XPATH, "//span[contains(text(),'Address Information')]"
            )
            address_information.click()
            # Enter first name
            driver.find_element(By.XPATH, "//input[@name='firstName']").send_keys(
                "First Name - Dummy"
            )
            logging.info("Entered first name")
            # Enter email
            driver.find_element(By.XPATH, "//input[@name='email']").send_keys(
                "<EMAIL>"
            )
            # Click on Configuration
            configuration = driver.find_element(
                By.XPATH, "(//span[contains(text(),'Configuration')])[1]"
            )
            configuration.click()
            # Enter into manage stock and sales strategy
            driver.find_element(By.XPATH, "//input[@name='manageStock']").send_keys(
                "Yes", Keys.ENTER
            )
            driver.find_element(By.XPATH, "//input[@name='salesStrategy']").send_keys(
                "D2C", Keys.ENTER
            )
            # Get UID
            UID = utils.get_uid(driver)
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                )
            )
            save_and_publish = driver.find_element(
                By.XPATH, "//span[contains(text(),'Save & Publish')]"
            )
            save_and_publish.click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'store_publish')]"
            )
            num = len(list1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]")
                )
            )
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Click on Open
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # logging.info(("Opened the entry"))
            # try:
            #     driver.find_element("xpath", '//span[text()="Yes"]').click()
            #     logging.info("Click on message")
            # except:
            #     pass
            UID = UID.split()
            UID = UID[1]
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]")
                )
            )
            time.sleep(5)
            # assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]"))).is_displayed()
            assert (
                len(
                    driver.find_elements(
                        By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]"
                    )
                )
                == 1
            )
            logging.info(f"Asserted the store name as {store_name}")

            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "store_publish")
            utils.validateSnsId(driver)
            price = driver.find_element(By.XPATH, "(//span[contains(text(), 'currencyRules')]//parent::div//following-sibling::div//span[contains(text(), 'default')]//parent::div//span)[5]")
            assert price.is_displayed()
            # driver.execute_script("arguments[0].click();",)
            # UPDATING THE STORE
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, UID)
            # Enter description
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//textarea[@name='description']")
                )
            )
            description = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(20)
            )
            driver.find_element(By.XPATH, "//textarea[@name='description']").clear()
            driver.find_element(By.XPATH, "//textarea[@name='description']").send_keys(
                f"Description for {store_name}: {description}"
            )
            logging.info(f"Description added as {description}")
            time.sleep(5)
            # click on Save & Publish
            save_and_publish_after_edit = driver.find_element(
                By.XPATH, "(//span[contains(text(),'Save & Publish')]//parent::span)[1]"
            )
            driver.execute_script("arguments[0].click();", save_and_publish_after_edit)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'store_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # try:
            #     driver.find_element("xpath", '//span[text()="Yes"]').click()
            #     logging.info("Click on message")
            # except:
            #     pass
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]")
                )
            )
            time.sleep(5)
            assert (
                len(
                    driver.find_elements(
                        By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]"
                    )
                )
                == 1
            )
            logging.info(f"Asserted the store name as {store_name}")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID}")
            # assert driver.find_element(By.XPATH, f"//span[contains(text(),'{description}')]").is_displayed()
            assert (
                len(
                    driver.find_elements(
                        By.XPATH, f"(//span[contains(text(),'{description}')])"
                    )
                )
                == 1
            )
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "store_publish")
            utils.validateSnsId(driver)
            # Unpublish
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, UID)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Unpublish')]")
                )
            )
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Unpublish')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            logging.info("Unpublished the store!")
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'store_unpublish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'store_unpublish')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # try:
            #     driver.find_element("xpath", '//span[text()="Yes"]').click()
            #     logging.info("Click on message")
            # except:
            #     pass
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]")
                )
            )
            time.sleep(5)
            assert (
                len(
                    driver.find_elements(
                        By.XPATH, f"(//span[contains(text(),'{store_name}')])[1]"
                    )
                )
                == 1
            )
            logging.info(f"Asserted the store name as {store_name}")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "store_unpublish")
            utils.validateSnsId(driver)
            
            Success_List_Append(
                "test_MKTPL_1646_SNS_001_002_003_MKTPL_1972_SNS_001_MKTPL_2959_Defaultcurrencystore_003",
                "Verify when a new store or delivery rule object is created via UI and published",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1646_SNS_001_002_003_MKTPL_1972_SNS_001_MKTPL_2959_Defaultcurrencystore_003",
            "Verify when a new store or delivery rule object is created via UI and published",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1646_SNS_001_002_003_MKTPL_1972_SNS_001_MKTPL_2959_Defaultcurrencystore_003",
            "Verify when a new store or delivery rule object is created via UI and published",
            e,
        )
        raise e

@jamatest.test(11974299)
def test_MKTPL_295_CRUDPaymentmethods_003(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_295_CRUDPaymentmethods_003.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "PaymentMethods")
            # Enter fulfillment name
            payment_methods_name = "From_Automation_" + "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                payment_methods_name
            )
            # Click on ok
            ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
            ok.click()
            logging.info(
                f"Clicked on ok after entering payment methods name as {payment_methods_name}"
            )
            payment_methods_code = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(4)
            )
            # input name and code
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//input[@name='name']"))
            )
            driver.find_element(By.XPATH, "//input[@name='name']").send_keys(
                payment_methods_name
            )
            driver.find_element(By.XPATH, "//input[@name='code']").send_keys(
                payment_methods_code
            )
 
            # Get UID
            UID = utils.get_uid(driver)
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                )
            )
            save_and_publish = driver.find_element(
                By.XPATH, "//span[contains(text(),'Save & Publish')]"
            )
            save_and_publish.click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            # Temporary information
            # store_name = "zi4CPajRov"
            # UID = "ID 11553"
            utils.close_All_Tabs(driver)
            # open folder
            utils.search_by_id(driver, PAYMENTMETHODS_FOLDER)
            time.sleep(3)
            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "(//span[contains(@class,'reload')])[last()]")
                    )
                )
                driver.find_element(
                    By.XPATH, "(//span[contains(@class,'reload')])[last()]"
                ).click()
            except:
                logging.info("No reload")
                pass

            time.sleep(5)
            # Navigate to last page
            try:
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[1]",
                        )
                    )
                )
                driver.find_element(
                    By.XPATH,
                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[1]",
                ).click()
                logging.info("Navigated to last page")
            except Exception as e:
                logging.info("last page error")
                logging.info(e)
                pass
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//div[contains(text(),'{payment_methods_name}')]")
                )
            )
            # Open the payment method
            open_element = driver.find_element(
                By.XPATH, f"//div[contains(text(),'{payment_methods_name}')]"
            )
            actions = ActionChains(driver)
            actions.double_click(open_element).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            utils.click_on_yes_no(driver)
            logging.info("Payment method opened")
            time.sleep(10)
            name = driver.find_element(By.XPATH, "//input[@name='name']").text
            code = driver.find_element(By.XPATH, "//input[@name='code']").text
 
            assert name in payment_methods_name
            logging.info(f"Asserted the name as {payment_methods_name}")
            assert code in payment_methods_name
            logging.info(f"Asserted the code as {payment_methods_code}")
            Success_List_Append(
                "test_MKTPL_295_CRUDPaymentmethods_003",
                "Verify by entering/updating data in below fields: - Name and Code",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_295_CRUDPaymentmethods_003",
            "Verify by entering/updating data in below fields: - Name and Code",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_295_CRUDPaymentmethods_003",
            "Verify by entering/updating data in below fields: - Name and Code",
            e,
        )
        raise e

@jamatest.test(11976357)
def test_MKTPL_376_CRUDFulfilmentmethods_003(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_376_CRUDFulfilmentmethods_003.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "Fulfillment")
            # Enter fulfillment name
            fulfillment_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                fulfillment_name
            )
            # Click on ok
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            logging.info(
                f"Clicked on ok after entering fulfillment name as {fulfillment_name}"
            )
            fulfillment_code = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(4)
            )
            # input name and code
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//input[@name='name']"))
            )
            driver.find_element(By.XPATH, "//input[@name='name']").send_keys(
                fulfillment_name
            )
            driver.find_element(By.XPATH, "//input[@name='code']").send_keys(
                fulfillment_code
            )
            # Get UID
            UID = utils.get_uid(driver)
            # Click on Save and Publish
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Save & Publish')]")
                )
            )
            save_and_publish = driver.find_element(
                By.XPATH, "//span[contains(text(),'Save & Publish')]"
            )
            save_and_publish.click()
            # Check for the validation
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )

            utils.close_All_Tabs(driver)
            # open folder
            utils.search_by_id(driver, FULFILLMENT_FOLDER)
            utils.clickOnReload(driver)
            # Navigate to last page
            try:
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH,
                                                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]")))
                driver.find_element(By.XPATH,
                                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]").click()
                logging.info("Navigated to last page")
            except Exception as e:
                logging.info("last page error")
                logging.info(e)
                pass
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//div[contains(text(),'{fulfillment_name}')]")
                )
            )
            # Open the store
            store_open_element = driver.find_element(
                By.XPATH, f"//div[contains(text(),'{fulfillment_name}')]"
            )
            actions = ActionChains(driver)
            actions.context_click(store_open_element).perform()
            open = driver.find_element(By.XPATH, xpath.open)
            open.click()
            utils.click_on_yes_no(driver)
            logging.info("Fulfillment opened")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//input[@name='name']"))
            )
            name = driver.find_element(By.XPATH, "//input[@name='name']").text
            code = driver.find_element(By.XPATH, "//input[@name='code']").text

            assert name in fulfillment_name
            logging.info(f"Asserted the name as {fulfillment_name}")
            assert code in fulfillment_code
            logging.info(f"Asserted the code as {fulfillment_code}")
            Success_List_Append(
                "test_MKTPL_376_CRUDFulfilmentmethods_003",
                "Verify by entering/updating data in below fields: - Name and Code",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_376_CRUDFulfilmentmethods_003",
            "Verify by entering/updating data in below fields: - Name and Code",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_376_CRUDFulfilmentmethods_003",
            "Verify by entering/updating data in below fields: - Name and Code",
            e,
        )
        raise e

@jamatest.test(11977140)
@jamatest.test(11977146)
@jamatest.test(11977153)
@jamatest.test(11977156)
@jamatest.test(11977164)
@jamatest.test(11977613)
@jamatest.test(11977614)
@jamatest.test(11977615)
@jamatest.test(11977619)
@jamatest.test(11975928)
@jamatest.test(11975929)
@jamatest.test(11975930)
@jamatest.test(11975935)
@jamatest.test(11977721)
@jamatest.test(11977722)
@jamatest.test(11977727)
def test_MKTPL_860_AirlinecategoryAPI_001_007_014_017_024_MKTPL_2381_AssignsequencenumberviaAPI_001_002_003_007_MKTPL_2223_UITemplateAPI_001_002_003_008_MKTPL_2472_RoottypeAPI_002_003_008_MKTPL_1646_1893_1894_1979_SNS_035_036_037(PAC_Login):
    global AIRLINE_Id, RANDOM_NAME
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_860_AirlinecategoryAPI_001_007_014_017_024_MKTPL_2381_AssignsequencenumberviaAPI_001_002_003_007_MKTPL_2223_UITemplateAPI_001_002_003_008_MKTPL_2472_RoottypeAPI_002_003_008_MKTPL_1646_1893_1894_1979_SNS_035_036_037.png", False
        ) as driver:
            # utils.revert_auto_populate_auto_map_airline_categories(driver)
            utils.set_cred_airline_auto_populate_map_update_airline_categories(driver, "no", None)
            # Add
            RANDOM_NAME_AIRLINE_CATEGORY = "".join(
                random.choices(string.ascii_letters, k=7)
            )
            payload = json.dumps(
                {
                    "name": RANDOM_NAME_AIRLINE_CATEGORY,
                    "disclaimer": "disclaimer add for automation testing",
                    "shortDescription": "this is for api automation testing",
                    "description": "automation testing",
                    "url": "",
                    "uiTemplate": "",
                    "sequenceNumber": 75,
                    "rootType": "",
                    "bannerImage": "",
                    "parentCategory": 0,
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", AIRLINE_CATEGORY_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info("------ADD------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API for Airlinecategory responded with 200 status code")
            logging.info(
                f"The Front end category created in the pimcore with all the details provide"
            )
            airline_category_id = resp_data["id"]
            logging.info(f"Airline Id- {airline_category_id}")
            # airline_category_id = '26789'
            # verify SNS
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'airline_category_publish')]"
            )
            logging.info(list1)
            num = len(list1)
            logging.info(num)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'airline_category_publish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            time.sleep(2)
            # Open Code if modal appears
            # try:
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            time.sleep(5)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{airline_category_id}')]")
                )
            )
            logging.info(f"Asserted Airline Category as: {airline_category_id}")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "airline_category_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            # driver.find_element(By.XPATH, xpath.logout).click()
            # logging.info("Logged out from PAC Admin")
            # utils.close_All_Tabs_optimized(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            # update
            AIRLINE_CATEGORY_URL_ID = AIRLINE_CATEGORY_URL + str(airline_category_id)
            RANDOM_DESCRIPTION = "".join(random.choices(string.ascii_letters, k=10))
            # payload['description'] = RANDOM_DESCRIPTION
            payload = json.dumps(
                {
                    "name": RANDOM_NAME_AIRLINE_CATEGORY,
                    "disclaimer": "",
                    "shortDescription": "this is for api automation testing",
                    "description": RANDOM_DESCRIPTION,
                    "url": "",
                    "uiTemplate": "",
                    "sequenceNumber": 75,
                    "rootType": "",
                    "bannerImage": "",
                    "parentCategory": 0,
                }
            )
            response = requests.request(
                "PUT", AIRLINE_CATEGORY_URL_ID, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info("------UPDATE------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                f"UPDATE API for Airlinecategory responded with 200 status code"
            )
            logging.info(
                f"The Front end category updated in the pimcore with all the details provided in the API"
            )

            response = requests.request(
                "GET", AIRLINE_CATEGORY_URL_ID, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info("-------GET BY ID-------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            # assert resp_data['data']
            product = resp_data["data"]["airlineCategory"]
            assert "id" in product
            assert "name" in product
            assert "disclaimer" in product
            assert "shortDescription" in product
            assert "description" in product
            assert "uiTemplate" in product
            assert "rootType" in product
            assert "url" in product
            assert "sequenceNumber" in product
            assert "images" in product
            assert "bannerImage" in product
            assert "backgroundImage" in product
            assert "rootType" in product
            assert "subCategory" in product
            logging.info(
                f"API should respond with 200 status code and it should display following parameters in the "
                f"response:"
                f"- id"
                f"- disclaimer"
                f"- name"
                f"- short Description"
                f"- description"
                f"- url"
                f"- image"
                f"- banner Image"
                f"- rootType"
                f"- uiTemplate"
                f"- subCategory"
            )
            response = requests.request(
                "GET", AIRLINE_CATEGORY_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info("-------GET BY ALL-------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201

            logging.info(
                f"The Front end category updated in the pimcore with all the details provided in the API"
            )
            # verify SNS
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'airline_category_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'airline_category_publish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            time.sleep(3)
            # Open Code if modal appears
            # try:
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            # driver.implicitly_wait(0)
            time.sleep(5)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{airline_category_id}')]")
                )
            )
            logging.info("Opened the log file - json")
            logging.info(f"Asserted Airline Category as: {airline_category_id}")
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            time.sleep(3)
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(RANDOM_DESCRIPTION, Keys.ENTER)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{RANDOM_DESCRIPTION}')]")
                )
            )
            logging.info(f"Asserted description as: {RANDOM_DESCRIPTION}")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "airline_category_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            # driver.find_element(By.XPATH, xpath.logout).click()
            # logging.info("Logged out from PAC Admin")
            # utils.close_All_Tabs_optimized(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            # utils.Delete
            response = requests.request(
                "DELETE", AIRLINE_CATEGORY_URL_ID, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Data cleaned up successfully.")
            # verify SNS
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'airline_category_unpublish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'airline_category_unpublish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            # time.sleep(3)
            # try:
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            # driver.implicitly_wait(0)
            time.sleep(5)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//span[contains(text(),'{airline_category_id}')]")
                )
            )
            logging.info("Opened the log file - json")
            logging.info(f"Asserted Airline Category as: {airline_category_id}")
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "airline_category_unpublish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            
            Success_List_Append(
                "test_MKTPL_860_AirlinecategoryAPI_001_007_014_017_024_MKTPL_2381_AssignsequencenumberviaAPI_001_002_003_007_MKTPL_2223_UITemplateAPI_001_002_003_008_MKTPL_2472_RoottypeAPI_002_003_008_MKTPL_1646_1893_1894_1979_SNS_035_036_037",
                "Verify after successfully executing the Airline Category API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_860_AirlinecategoryAPI_001_007_014_017_024_MKTPL_2381_AssignsequencenumberviaAPI_001_002_003_007_MKTPL_2223_UITemplateAPI_001_002_003_008_MKTPL_2472_RoottypeAPI_002_003_008_MKTPL_1646_1893_1894_1979_SNS_035_036_037",
            "Verify after successfully executing the Airline Category API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_860_AirlinecategoryAPI_001_007_014_017_024_MKTPL_2381_AssignsequencenumberviaAPI_001_002_003_007_MKTPL_2223_UITemplateAPI_001_002_003_008_MKTPL_2472_RoottypeAPI_002_003_008_MKTPL_1646_1893_1894_1979_SNS_035_036_037",
            "Verify after successfully executing the Airline Category API",
            e,
        )
        raise e

@jamatest.test(11975651)
@jamatest.test(11975653)
@jamatest.test(11976975)
def test_MKTPL_1372_OrderProvider_001_003_MKTPL_692_001(PAC_Login):
    random_num = "".join(random.choices(string.ascii_letters, k=5))
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1372_OrderProvider_001_003_MKTPL_692_001.png", False
        ) as driver:
            
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            try:
                driver.find_element("xpath", '//span[text()="Yes"]').click()
                logging.info("Click on message")
            except:
                pass
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.configuration_tab)
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.visibility_of_element_located((By.NAME, "orderProvider")))
                .clear()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.visibility_of_element_located((By.NAME, "orderProvider")))
                .send_keys(random_num)
            )
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(f"Order Provider  successfully created/updated in the store")
            # GET
            payload = {}
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "GET", STORE_GET_URL, headers=headers, data=payload
            )
            data = response.json()
            assert response.status_code == 200 or response.status_code == 201
            # Extract data
            store_data = data.get("data", {}).get("store", {})
            logging.info(data["data"]["store"]["orderProvider"])
            assert "orderProvider" in store_data
            assert "id" in store_data
            assert "storeName" in store_data
            assert "description" in store_data
            assert "logo" in store_data
            assert "firstName" in store_data
            assert "lastName" in store_data
            assert "addressLine1" in store_data
            assert "addressLine2" in store_data
            assert "city" in store_data
            assert "state" in store_data
            assert "zipCode" in store_data
            assert "country" in store_data
            assert "zipCode" in store_data
            assert "phone" in store_data
            assert "email" in store_data
            assert "orderThresholdValue" in store_data
            assert "autoapprovecatalog" in store_data
            assert "managestock" in store_data
            assert "clientId" in store_data
            assert "clientSecret" in store_data
            assert "taxPercentage" in store_data
            assert "associatedLanguages" in store_data
            assert "defaultCurrency" in store_data
            assert data["data"]["store"]["orderProvider"] == random_num
            logging.info(
                f"API  respond with 200 status code and it should display following parameters in the"
                f" response:"
                f"- Order Provider"
            )
            Success_List_Append(
                "test_MKTPL_1372_OrderProvider_001_003_MKTPL_692_001",
                "Verify by entering/updating data in below " "fields",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1372_OrderProvider_001_003_MKTPL_692_001",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1372_OrderProvider_001_003_MKTPL_692_001",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

@jamatest.test(11976353)
def test_MKTPL_375_DeliveryType_001(PAC_Login):
    RANDOM_NAME = "SKU".join(random.choices(string.ascii_letters, k=3))
    RANDOM_NAME_PRODUCT = "".join(random.choices(string.ascii_letters, k=7))

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_375_DeliveryType_001.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            menu = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.ID, xpath.pimcoreMenu_id)
                )
            )
            menu.click()
            product = driver.find_element(
                By.XPATH, xpath.createNewProduct
            )
            action = ActionChains(driver)
            action.move_to_element(product).click().perform()
            time.sleep(1)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[text()="Add new Object of type Product"])[1]')
                )
            )

            wait.until(EC.presence_of_element_located((By.NAME, "sku"))).send_keys(
                RANDOM_NAME, Keys.TAB
            )
            logging.info(RANDOM_NAME)
            time.sleep(1)
            store_dropdown = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//input[@name="store"]//../following-sibling::div')
                )
            )
            driver.execute_script("arguments[0].click();", store_dropdown)
            time.sleep(1)
            store = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//li[text()="' + DEDICATED_STORE + '"]')
                )
            )
            driver.execute_script("arguments[0].click();", store)
            time.sleep(1)
            driver.execute_script("arguments[0].click();", store_dropdown)
            time.sleep(1)

            cat_dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="category"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", cat_dropdown)
            time.sleep(1)
            category = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//li[text()="'
                        + DEDICATED_CATEGORY_2_NAME
                        + " ("
                        + DEDICATED_STORE
                        + "/"
                        + DEDICATED_CATEGORY_3_NAME
                        + "/"
                        + DEDICATED_CATEGORY_2_NAME
                        + ')"])[1]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", category)
            time.sleep(1)
            driver.execute_script("arguments[0].click();", cat_dropdown)
            time.sleep(1)
            product_set = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//input[@name="type"]//../following-sibling::div')
                )
            )
            driver.execute_script("arguments[0].click();", product_set)
            time.sleep(1)
            p_set = wait.until(
                EC.presence_of_element_located((By.XPATH, '//li[text()="Simple"]'))
            )
            driver.execute_script("arguments[0].click();", p_set)
            driver.implicitly_wait(0)
            logging.info("simple")
            ok = driver.find_element(By.XPATH, '(//span[text()="OK"])[1]')
            logging.info(ok.is_displayed())
            driver.execute_script("arguments[0].click();", ok)
            driver.implicitly_wait(0)
            wait.until(EC.visibility_of_element_located((By.NAME, "name"))).send_keys(
                RANDOM_NAME_PRODUCT
            )
            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Store "]'))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="deliveryType"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            add_gate = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//li[text()="Gate Pick Up"])[1]')
                )
            )
            action = ActionChains(driver)
            action.move_to_element(add_gate).click().perform()
            time.sleep(1)
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.NAME, "gatePickupLeadTime"))
            ).send_keys("8")
            driver.find_element(By.NAME, "productType").send_keys("Food and Beverage")
            time.sleep(2)
            driver.find_element(By.XPATH, '//span[text()="Pricing & Taxation"]').click()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="Price:"]/../../..//input)[1]')
                )
            )
            driver.implicitly_wait(10)
            driver.find_element(
                By.XPATH, '(//span[text()="Price:"]/../../..//input)[1]'
            ).send_keys("1")
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(f"Product is successfully created in the system = {saved}")
            UID = utils.get_uid(driver)
            utils.clickOnReload(driver)
            # driver.refresh()
            utils.wait_for_style_attribute(driver, 40)            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.airlineProductCatBaseDataTabXpath))
            )
            driver.find_element(By.XPATH, xpath.airlineProductCatBaseDataTabXpath).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "name"))
            )
            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            ).text
            logging.info(delivery_method)

            if "Gate Pick Up" in delivery_method:
                remove_pickup = wait.until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//li//div[text()="Gate Pick Up"]//../div)[2]')
                    )
                )
                driver.execute_script("arguments[0].click();", remove_pickup)
                driver.implicitly_wait(0)
                logging.info("Removed Gate Pick Up")
            else:
                pass
            dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="deliveryType"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            add_flight = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//li[text()="Next Flight"])[1]')
                )
            )
            action = ActionChains(driver)
            action.move_to_element(add_flight).click().perform()
            driver.implicitly_wait(0)
            driver.execute_script("arguments[0].click();", dropdown)

            saved = utils.save_and_publish(driver)
            assert saved
            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            ).text
            logging.info("After updating Delivery Method")
            logging.info(delivery_method)
            logging.info(f"Product is successfully updated in the system = {saved}")
            UID = utils.get_uid(driver)
            Success_List_Append(
                "test_MKTPL_375_DeliveryType_001",
                "Verify by entering/updating data in Delivery Methods",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_375_DeliveryType_001",
            "Verify by entering/updating data in Delivery Methods",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_375_DeliveryType_001",
            "Verify by entering/updating data in Delivery Methods",
            e,
        )
        raise e

@jamatest.test(11976354)
def test_MKTPL_375_DeliveryType_002(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_375_DeliveryType_002.png", False
        ) as driver:
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Home"]'))
            )
            # home.click()
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_PRODUCT_2_ID)
            try:
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="Yes"]'))
                )
                driver.find_element("xpath", '//span[text()="Yes"]').click()
            except:
                pass
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Name "])[1]')
                )
            )

            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="Delivery Method "])[1]')
                )
            )
            driver.execute_script("arguments[0].scrollIntoView();", delivery_method)
            time.sleep(1)
            delivery_method_text = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            )
            logging.info(delivery_method_text.text)
            assert delivery_method_text.is_displayed()
            logging.info(
                "User is able to view Fulfillment(Delivery) Methods of the Product"
            )
            Success_List_Append(
                "test_MKTPL_375_DeliveryType_002",
                "Verify User is able to view Fulfillment Methods " "of the Product",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_375_DeliveryType_002",
            "Verify User is able to view Fulfillment Methods " "of the Product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_375_DeliveryType_002",
            "Verify User is able to view Fulfillment Methods " "of the Product",
            e,
        )
        raise e

def test_MKTPL_536_CRUDCabinclass_003(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_536_CRUDCabinclass_003.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "CabinClass")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )

            name = (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "name")))
                .send_keys(RANDOM_NAME)
            )

            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Cabin class should be successfully created/updated in the system = {saved}"
            )
            utils.close_All_Tabs(driver)
            wait = WebDriverWait(driver, 60)
            utils.search_by_path(driver, "/Master Data/Cabin Class")
            driver.find_element(By.NAME, 'query').send_keys(
                RANDOM_NAME
            )
            driver.find_element(By.NAME, 'query').send_keys(
                Keys.ENTER
            )
            listed = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[contains(text(),"'+RANDOM_NAME+'")])[1]')
                    )
                )
                .is_displayed()
            )
            assert listed
            logging.info(
                f"Cabin class object should move automatically under Master Data/Cabin class folder  = {listed}"
            )
            Success_List_Append(
                "test_MKTPL_536_CRUDCabinclass_003",
                "Verify by entering/updating data in below fields",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_536_CRUDCabinclass_003",
            "Verify by entering/updating data in below fields",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_536_CRUDCabinclass_003",
            "Verify by entering/updating data in below fields",
            e,
        )
        raise e

@jamatest.test(11975338)
def test_MKTPL_1134_Locationobject_001(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    RANDOM_NAME_STORE = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1134_Locationobject_001.png", False
        ) as driver:
            
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Stores")
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME_STORE)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "storeName"))
            ).send_keys(RANDOM_NAME_STORE)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Address Information"]')
                )
            ).click()
            driver.find_element(By.NAME, "firstName").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.configuration_tab)
                )
            ).click()
            driver.find_element(By.NAME, "manageStock").send_keys("Yes")
            driver.find_element(By.NAME, "salesStrategy").send_keys("D2C")
            driver.find_element(By.NAME, "defaultCurrency").send_keys("USD", Keys.ENTER)
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Store is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            search_home = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//span[text()="Home"]//..//a[2]',
                    )
                )
            )
            logging.info(search_home.is_displayed())
            driver.execute_script("arguments[0].click();", search_home)
            driver.implicitly_wait(0)

            wait.until(
                EC.visibility_of_element_located((By.XPATH, '//input[@name="filter"]'))
            ).send_keys(RANDOM_NAME_STORE, Keys.ENTER)
            logging.info("store name searched")
            expand_store = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '((//span[text()="' + RANDOM_NAME_STORE + '"])[1]//../div)[2]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", expand_store)
            driver.implicitly_wait(0)

            inventory_location = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '((//span[text()="Inventory Location"])[1]//../div)[3]')
                )
            )
            driver.execute_script("arguments[0].click();", inventory_location)
            driver.implicitly_wait(0)
            logging.info("inventory_location")

            default_inventory = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Default Inventory"]')
                )
            )

            assert default_inventory.is_displayed()
            driver.execute_script("arguments[0].click();", default_inventory)
            driver.implicitly_wait(0)
            logging.info("default_inventory")

            fulfillment = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[@class="x-form-trigger-wrap x-form-trigger-wrap-default"]//span[@class="x-hidden-clip"])[1]',
                    )
                )
            ).text
            logging.info(fulfillment)
            assert fulfillment != None

            logging.info(
                "Store is Created and Default Location object should be created under "
                "Store Name>Inventory Location folder>Default Inventory"
            )
            Success_List_Append(
                "test_MKTPL_1134_Locationobject_001",
                "Verify Store Created and Default Location object "
                "should be created under "
                "Store Name>Inventory Location folder>Default Inventory",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_1134_Locationobject_001",
            "Verify Store Created and Default Location object "
            "should be created under "
            "Store Name>Inventory Location folder>Default Inventory",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1134_Locationobject_001",
            "Verify Store Created and Default Location object "
            "should be created under "
            "Store Name>Inventory Location folder>Default Inventory",
            e,
        )
        raise e

@jamatest.test(11975346)
def test_MKTPL_1134_Locationobject_008(PAC_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=4)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1134_Locationobject_008.png", False
        ) as driver:
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Home"]'))
            )
            driver.implicitly_wait(0)
            wait = WebDriverWait(driver, 60)
            search_home = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Home"]/..//a)[2]')
                )
            )
            utils.search_by_id(driver, DEDICATED_STORE_LOCATION_1_ID)
            location_name = wait.until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="locationName"]')
                    )
                )
            )
            lname_text = location_name.get_attribute("value")
            location_name.clear()
            lname = "Default Inventory " + RANDOM_NAME
            location_name.send_keys(lname)
            logging.info(lname_text)

            fulfillment = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '((//li[@class="x-tagfield-item"])[1]//div)[1]')
                )
            ).text
            logging.info(fulfillment)

            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="fullfillmentOption"]')
                    )
                )
            )
            Delete_fulfillment = driver.find_element(
                By.XPATH, '//input[@name="fullfillmentOption"]'
            )
            Delete_fulfillment.clear()
            saved = utils.save_and_publish(driver)
            assert saved

            location_name = driver.find_element(By.NAME, "locationName").get_attribute(
                "value"
            )
            logging.info(location_name)
            fulfillment_edit = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[@class="x-form-trigger-wrap x-form-trigger-wrap-default"]//span[@class="x-hidden-clip"])[1]',
                    )
                )
            ).text
            logging.info(fulfillment_edit)

            assert location_name != lname_text and saved
            assert fulfillment != fulfillment_edit

            try:
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[@class = "x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default"])[1]',
                        )
                    )
                )
                UID = driver.find_element(
                    By.XPATH,
                    '(//div[@class = "x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default"])[1]',
                ).text
                logging.info(f"Unique ID: {UID}")
            except Exception as e:
                UID = None
                pass

            logging.info(
                "PAC admin is able to update default Inventory Name and Fulfillment"
            )
            Success_List_Append(
                "test_MKTPL_1134_Locationobject_008",
                "Verify default Inventory Name and Fulfillment" " is updated.",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_1134_Locationobject_008",
            "Verify default Inventory Name and Fulfillment" " is updated.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1134_Locationobject_008",
            "Verify default Inventory Name and Fulfillment" " is updated.",
            e,
        )
        raise e

def test_MKTPL_2504_Unpublishedproduct_001(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2504_Unpublishedproduct_001.png", False
        ) as driver:
            
            utils.search_by_id(driver, DEDICATED_PRODUCT_8_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Unpublish"])[1]')
                )
            ).click()
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
            )
            logging.info("Saved successfully shown to user")
            utils.search_by_id(driver, DEDICATED_FOLDER_5_ID)
            driver.find_element(By.NAME, "query").send_keys(
                DEDICATED_PRODUCT_8_ID, Keys.ENTER
            )
            display = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/'
                            + DEDICATED_STORE
                            + "/"
                            + DEDICATED_FOLDER_5
                            + "/"
                            + DEDICATED_PRODUCT_8_NAME
                            + '"]',
                        )
                    )
                )
                .is_displayed()
            )
            assert display
            logging.info(
                f"Product move to Store Name folder/Unpublished Products folder when product is unpublished for PAC Users = {display}"
            )
            driver.find_element(
                By.XPATH, '//span[text()="' + DEDICATED_PRODUCT_8_NAME + '"]'
            ).click()
            saved = utils.save_and_publish(driver)
            assert saved
            Success_List_Append(
                "test_MKTPL_2504_Unpublishedproduct_001",
                "Verify when product is unpublished for PAC Users",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_001",
            "Verify when product is unpublished for PAC Users",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_001",
            "Verify when product is unpublished for PAC Users",
            e,
        )
        raise e

@jamatest.test(11975819)
def test_MKTPL_1614_KIT_001_MKTPL_1646_1893_1894_1979_SNS_013_014(PAC_Login):
    try:
        payload = {}
        headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
        response = requests.request("POST", KIT_URL, headers=headers, data=payload)
        resp_data = response.json()
        logging.info("-------POST-------")
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        expected_message = "Message sent successfully"
        assert expected_message in response.json()["message"]
        logging.info(
            f"API should respond with 200 status code and 'Message sent successfully' message"
        )
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1614_KIT_001_MKTPL_1646_1893_1894_1979_SNS_013_014.png", False
        ) as driver:
            # Verify SNS
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'kit_ready')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'kit_ready')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            # try:
            #     WebDriverWait(driver, 60).until(
            #         EC.visibility_of_element_located(
            #             (By.XPATH, xpath.yes)
            #         )
            #     )
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            logging.info("Opened the log file - json")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "kit_ready")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            Success_List_Append(
                "test_MKTPL_1614_KIT_001_MKTPL_1646_1893_1894_1979_SNS_013_014",
                "Verify the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1614_KIT_001_MKTPL_1646_1893_1894_1979_SNS_013_014",
            "Verify the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1614_KIT_001_MKTPL_1646_1893_1894_1979_SNS_013_014",
            "Verify the API",
            e,
        )
        raise e

def test_MKTPL_1065_CategorychangesinProduct_005(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1065_CategorychangesinProduct_005.png", False
        ) as driver:
            
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_SKU_1_ID)
            scroll = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//span[text()="Store " and @class ="x-form-item-label-text"])[last()]',
                    )
                )
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            logging.info("Store locator")
            time.sleep(1)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//span[text()="Category "]//..//../following-sibling::div//div)[last()]',
                    )
                )
            ).click()
            time.sleep(3)
            dropdown_1 = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//ul[@class="x-list-plain"]//li)[1]')
                )
            ).text
            dropdown_2 = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//ul[@class="x-list-plain"]//li)[2]')
                )
            ).text
            dropdown_3 = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//ul[@class="x-list-plain"]//li)[3]')
                )
            ).text
            logging.info(dropdown_1)
            logging.info(dropdown_2)
            logging.info(dropdown_3)
            templist = [
                "Exclusively cooked at home_3 (Cred Automation Store/Exclusively cooked at home_3)",
                "Exclusively cooked at home_7 (Cred Automation Store/Exclusively cooked at home_7)",
                "Non-alcoholic (Cred Automation Store/Beverages/Non-alcoholic)",
            ]
            assert (dropdown_1 and dropdown_2 and dropdown_3) in templist
            logging.info(
                "Category dropdown should show category based on the store sales strategy"
            )
            Success_List_Append(
                "test_MKTPL_1065_CategorychangesinProduct_005",
                "Verify Category dropdown should show category based on the store sales strategy",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_1065_CategorychangesinProduct_005",
            "Verify Category dropdown should show category based on the store sales strategy",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1065_CategorychangesinProduct_005",
            "Category dropdown should show category based on the store sales strategy",
            e,
        )
        raise e

@jamatest.test(11975765)
def test_MKTPL_1568_CatalogAssignmentPriceAPI_001_MKTPL_1646_1893_1894_1979_SNS_024(PAC_Login):
    random_number = "".join([str(randint(1, 9)) for i in range(6)])
    logging.info(random_number)

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1568_CatalogAssignmentPriceAPI_001_MKTPL_1646_1893_1894_1979_SNS_024.png", False
        ) as driver:
            payload = json.dumps(
                {
                    "catalog": {
                        "id": int(DEDICATED_CATALOG_1_ID),
                        "products": [
                            {
                                "productId": int(DEDICATED_SKU_1_ID),
                                "productPrice": int(random_number),
                            }
                        ],
                    }
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "PUT", UPDATE_PRICE_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API for Airlinecategory responded with 200 status code")
            expected_message = (
                "Price Updated successfully. SNS message sent successfully"
            )
            assert expected_message in response.json()["message"]
            logging.info(
                f"The Deployment Inventory details updated in the pimcore with all the details provided in the API"
            )
            # Verify SNS
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            # utils.click_on_yes_no(driver)
            logging.info("Opened the log file - json")
            time.sleep(5)
            x = driver.find_element(By.XPATH, "//textarea[contains(@class,'input')]")
            # x.click()
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys("value")
            time.sleep(2)
            # val = (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'value')]//following-sibling::span")))).text
            # assert val == random_number
            assert (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (By.XPATH, f"//span[contains(text(),'{random_number}')]")
                    )
                )
            ).is_displayed()
            logging.info(f"Asserted the {random_number} in quantity")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "catalog_assignment_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)
            Success_List_Append(
                "test_MKTPL_1568_CatalogAssignmentPriceAPI_001_MKTPL_1646_1893_1894_1979_SNS_024",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1568_CatalogAssignmentPriceAPI_001_MKTPL_1646_1893_1894_1979_SNS_024",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1568_CatalogAssignmentPriceAPI_001_MKTPL_1646_1893_1894_1979_SNS_024",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11976780)
@jamatest.test(11975984)
def test_MKTPL_528_Taxpercentage_001_MKTPL_296_Orderthresholdlimits_001(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_528_Taxpercentage_001_MKTPL_296_Orderthresholdlimits_001.png", False
        ) as driver:
            
            utils.click_on_add_object(driver, "Stores")
            # Enter store name
            store_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            logging.info("Clicked on ok after entering Store name as {store_name}")
            # input store name
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='storeName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='storeName']").send_keys(
                store_name
            )
            # Click on address info
            address_information = driver.find_element(
                By.XPATH, "//span[contains(text(),'Address Information')]"
            )
            address_information.click()
            # Enter first name
            driver.find_element(By.XPATH, "//input[@name='firstName']").send_keys(
                "First Name - Dummy"
            )
            logging.info("Entered first name")
            # Enter email
            driver.find_element(By.XPATH, "//input[@name='email']").send_keys(
                "<EMAIL>"
            )
            # Click on Configuration
            configuration = driver.find_element(
                By.XPATH, "(//span[contains(text(),'Configuration')])[1]"
            )
            configuration.click()
            # Enter into manage stock and sales strategy
            driver.find_element(By.XPATH, "//input[@name='manageStock']").send_keys(
                "Yes", Keys.ENTER
            )
            driver.find_element(By.XPATH, "//input[@name='salesStrategy']").send_keys(
                "D2C"
            )
            driver.find_element(By.XPATH, "//input[@name='salesStrategy']").send_keys(
                Keys.ENTER
            )
            time.sleep(1)
            driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'Order Threshold Value')]//ancestor::div[1]//input)[1]",
            ).click()
            driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'Order Threshold Value')]//ancestor::div[1]//input)[1]",
            ).send_keys("1", Keys.ENTER)
            driver.find_element(By.XPATH, "//input[@name='taxPercentage']").send_keys(
                "1", Keys.ENTER
            )

            # Get UID
            UID = utils.get_uid(driver)
            # Click on Save and Publish
            saved = utils.save_and_publish(driver)
            assert saved
            # driver.find_element(By.XPATH, xpath.logout).click()
            # logging.info("Logged out from PAC Admin")
            # WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "username")))
            # utils.Pac_Credentials.Login_store(driver)

            # WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, "//div[text()='Store Profile']")))

            Success_List_Append(
                "test_MKTPL_528_Taxpercentage_001_MKTPL_296_Orderthresholdlimits_001",
                "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_528_Taxpercentage_001_MKTPL_296_Orderthresholdlimits_001",
            "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_528_Taxpercentage_001_MKTPL_296_Orderthresholdlimits_001",
            "When user select D2C while creating store object, user should able to enter tax percentage and Order Threshold value.",
            e,
        )
        raise e

# Catalog Assignment - Status required - Approved for Association and Airline Category Mapped
@jamatest.test(11975654)
def test_MKTPL_1423_DeploymentInventoryAPI_001_MKTPL_1646_1893_1894_1979_SNS_019(PAC_Login):
    random_number = "".join([str(randint(1, 9)) for i in range(3)])
    logging.info(random_number)

    try:
        payload = json.dumps(
            {
                "catalog": {
                    "id": int(DEDICATED_CATALOG_1_ID),
                    "products": [
                        {
                            "productId": int(DEDICATED_SKU_1_ID),
                            "productQuantity": int(random_number),
                        }
                    ],
                }
            }
        )
        headers = {
            "Authorization": CRED_AUTOMATION_STORE_TOKEN,
            "Content-Type": "application/json",
        }
        response = requests.request(
            "PUT", DEPLOYMENT_INVENTORY_URL, headers=headers, data=payload
        )
        resp_data = response.json()
        logging.info(resp_data)
        assert response.status_code == 200 or response.status_code == 201
        logging.info(f"POST API for Airlinecategory responded with 200 status code")
        expected_message = (
            "Deployment Inventory Updated successfully. SNS message sent successfully"
        )
        assert expected_message in response.json()["message"]
        logging.info(
            f"The Deployment Inventory details updated in the pimcore with all the details provided in the API"
        )
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1423_DeploymentInventoryAPI_001_MKTPL_1646_1893_1894_1979_SNS_019.png", False
        ) as driver:
            # Verify SNS
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'deployment_inventory_publish')]"
            )
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'deployment_inventory_publish')])[{num-1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            # utils.click_on_yes_no(driver)
            logging.info("Opened the log file - json")
            x = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            utils.validateSnsMessageAttributes(driver, x)
            utils.validateSnsEventType(driver, "deployment_inventory_publish")
            utils.validateSnsId(driver)
            utils.validateSnsAirlineCode(driver)

        Success_List_Append(
            "test_MKTPL_1423_DeploymentInventoryAPI_001_MKTPL_1646_1893_1894_1979_SNS_019",
            "Verify after successfully executing the API",
            "Pass",
        )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1423_DeploymentInventoryAPI_001_MKTPL_1646_1893_1894_1979_SNS_019",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1423_DeploymentInventoryAPI_001_MKTPL_1646_1893_1894_1979_SNS_019",
            "Verify after successfully executing the API",
            e,
        )
        raise e

def test_MKTPL_1911_SNS_001(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1911_SNS_001.png", False
        ) as driver:
            driver.maximize_window()
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Airline")
            # driver.execute_script("arguments[0].click();", stores)
            driver.implicitly_wait(10)
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            action = ActionChains(driver)
            action.move_to_element(airline).perform()
            airline2 = driver.find_element(By.XPATH, '(//span[text()="Airline"])[2]')
            driver.execute_script("arguments[0].click();", airline2)
            logging.info("Clicked on Airline")
            airline_name = "From_Automation_" + "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                airline_name
            )
            ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
            ok.click()
            logging.info(f"Clicked on ok after entering Airline name as {airline_name}")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='airlineName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='airlineName']").send_keys(
                airline_name
            )
            logging.info(f"Name: {airline_name}")
            ICAOCode = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(6)
            )
            iataCode = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(6)
            )
            driver.find_element(By.NAME, "ICAOCode").send_keys(ICAOCode)
            driver.find_element(By.NAME, "iataCode").send_keys(iataCode)
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            address_information = driver.find_element(
                By.XPATH, "//span[contains(text(),'Address Information')]"
            )
            address_information.click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='firstName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='firstName']").send_keys(
                airline_name
            )
            logging.info("Entered first name")
            driver.find_element(By.XPATH, "//input[@name='email']").send_keys(
                "<EMAIL>"
            )
            logging.info("Entered email: <EMAIL>")
            driver.find_element(By.XPATH, "//a//span//span[text()='Stores']").click()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "(//a//span//span[contains(@class,'icon_search')])[2]")
                )
            )
            driver.find_element(
                By.XPATH, "(//a//span//span[contains(@class,'icon_search')])[2]"
            ).click()
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//input[@name='query']"))
            )
            driver.find_element(By.XPATH, "//input[@name='query']").send_keys(
                DEDICATED_STORE_ID, Keys.ENTER
            )
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "(//div[contains(text(),'" + DEDICATED_STORE + "')])[1]")
                )
            )
            store = driver.find_element(
                By.XPATH, "(//div[contains(text(),'" + DEDICATED_STORE + "')])[1]"
            )
            action = ActionChains(driver)
            action.double_click(store).perform()
            driver.find_element(By.XPATH, "//span[contains(text(),'Select')]").click()
            time.sleep(2)
            save_and_publish = driver.find_element(
                By.XPATH, "//span[contains(text(),'Save & Publish')]"
            )
            save_and_publish.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            time.sleep(5)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "//img[@data-qtip='Open']"))
            )
            driver.find_element(By.XPATH, "//img[@data-qtip='Open']").click()
            time.sleep(3)
            # Open Code if modal appears
            try:
                driver.find_element(By.XPATH, xpath.yes).click()
            except:
                logging.info("Modal is not shown, store not opened on any other device")
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "(//span[contains(text(),'Description:')])[2]")
                )
            )
            driver.find_element(
                By.XPATH, "(//textarea[@name='description'])[2]"
            ).clear()
            desc = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(
                By.XPATH, "(//textarea[@name='description'])[2]"
            ).send_keys(desc)
            logging.info(f"Edited the description as {desc}")
            save_and_publish = driver.find_element(
                By.XPATH, "(//span[contains(text(),'Save & Publish')])[2]"
            )
            save_and_publish.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            driver.find_element(
                By.XPATH, "(//span[contains(@class,'close-btn')])[2]"
            ).click()
            # Navigate to the Triggered SNS - List View
            # utils.close_All_Tabs(driver)
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'store_publish')]"
            )
            num = len(list1)
            logging.info(num)
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]")
                )
            )
            time.sleep(5)
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'store_publish')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Click on Open
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            time.sleep(5)
            # try:
            #     # if driver.find_element(By.XPATH, xpath.yes).is_displayed()
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "(//span[contains(text()," + DEDICATED_STORE_ID + ")])[1]",
                    )
                )
            )
            logging.info(("Opened the entry"))
            time.sleep(5)
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            time.sleep(3)
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(UID)
            time.sleep(3)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the associated airline {UID} in store payload")
            # Second
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'store_publish')]"
            )
            num = len(list1)
            logging.info(num)
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]")
                )
            )
            time.sleep(5)
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'store_publish')])[{num-1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Click on Open
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # time.sleep(5)
            # try:
            #     # if driver.find_element(By.XPATH, xpath.yes).is_displayed()
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{DEDICATED_STORE_ID}')])[1]")
                )
            )
            logging.info(("Opened the entry"))
            time.sleep(5)
            x = driver.find_element(By.XPATH, "(//textarea)[1]")
            x.send_keys(Keys.CONTROL, "f")
            time.sleep(3)
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(UID)
            time.sleep(3)
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the associated airline {UID} in store payload")
            # Navigate to the Triggered SNS - List View
            utils.close_All_Tabs(driver)
            utils.Assets(driver)
            # Generate list and get the last row number with store publish
            list1 = driver.find_elements(
                By.XPATH, "//div[contains(text(),'auth_token_update')]"
            )
            num = len(list1)
            logging.info(num)
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        f"(//div[contains(text(),'auth_token_update')])[{num-1}]",
                    )
                )
            )
            time.sleep(5)
            # Click on the last created entry
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'auth_token_update')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # Click on Open
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # time.sleep(5)
            # try:
            #     # if driver.find_element(By.XPATH, xpath.yes).is_displayed()
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")
            driver.implicitly_wait(0)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"(//span[contains(text(),'{UID}')])[1]")
                )
            )
            logging.info(("Opened the entry"))
            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"(//span[contains(text(),'{ICAOCode}')])[1]"
            ).is_displayed()
            logging.info(f"Asserted the ICAO code as {ICAOCode}")
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{UID}')]"
            ).is_displayed()
            logging.info(f"Asserted the UID as {UID}")
            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_AIRLINE_URL, CRED_AUTOMATION_AIRLINE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")

            Success_List_Append(
                "test_MKTPL_1911_SNS_001",
                "Verify when a new airline object is created, updated and unpublished via UI and published",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1911_SNS_001",
            "Verify when a new airline object is created, updated and unpublished via UI and published",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1911_SNS_001",
            "Verify when a new airline object is created, updated and unpublished via UI and published",
            e,
        )
        raise e

@jamatest.test(11977746)
def test_MKTPL_1646_SNS_008_009_010_MKTPL_3335_SNS_001_002_MKTPL_2523_Authorizationtoken_004(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1646_SNS_008_009_010_MKTPL_3335_SNS_001_002_MKTPL_2523_Authorizationtoken_004.png", False
        ) as driver:
            driver.maximize_window()
            wait = WebDriverWait(driver, 60)
            utils.click_on_add_object(driver, "Airline")
            airline = driver.find_element(By.XPATH, '(//span[text()="Airline"])[1]')
            action = ActionChains(driver)
            action.move_to_element(airline).perform()
            airline2 = driver.find_element(By.XPATH, '(//span[text()="Airline"])[2]')
            driver.execute_script("arguments[0].click();", airline2)
            logging.info("Clicked on Airline")
            airline_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                airline_name
            )
            ok = driver.find_element(By.XPATH, "//span[contains(text(),'OK')]")
            ok.click()
            logging.info(f"Clicked on ok after entering Airline name as {airline_name}")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='airlineName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='airlineName']").send_keys(
                airline_name
            )
            logging.info(f"Name: {airline_name}")
            ICAOCode = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(6)
            )
            iataCode = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(6)
            )
            driver.find_element(By.NAME, "ICAOCode").send_keys(ICAOCode)
            driver.find_element(By.NAME, "iataCode").send_keys(iataCode)
            UID = utils.get_uid(driver)
            UID = UID.split()
            UID = UID[1]
            address_information = driver.find_element(
                By.XPATH, "//span[contains(text(),'Address Information')]"
            )
            address_information.click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='firstName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='firstName']").send_keys(
                airline_name
            )
            logging.info("Entered first name")
            driver.find_element(By.XPATH, "//input[@name='email']").send_keys(
                "<EMAIL>"
            )
            logging.info("Entered email: <EMAIL>")
            driver.find_element(By.XPATH, "//a//span//span[text()='Stores']").click()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "(//a//span//span[contains(@class,'icon_search')])[2]")
                )
            )
            driver.find_element(
                By.XPATH, "(//a//span//span[contains(@class,'icon_search')])[2]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//input[@name='query']"))
            )
            driver.find_element(By.XPATH, "//input[@name='query']").send_keys(
                DEDICATED_STORE_ID, Keys.ENTER
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "(//div[contains(text(),'" + DEDICATED_STORE + "')])[1]")
                )
            )
            store = driver.find_element(
                By.XPATH, "(//div[contains(text(),'" + DEDICATED_STORE + "')])[1]"
            )
            action = ActionChains(driver)
            action.double_click(store).perform()
            driver.find_element(By.XPATH, "//span[contains(text(),'Select')]").click()
            time.sleep(2)
            save_and_publish = driver.find_element(
                By.XPATH, "//span[contains(text(),'Save & Publish')]"
            )
            save_and_publish.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            time.sleep(5)
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, AIRLINES_FOLDER)
            try:
                (
                    WebDriverWait(driver, 60)
                    .until(
                        EC.presence_of_element_located(
                            (By.NAME, 'selectClass')
                        )
                    )
                    .send_keys("Airline")
                )
                (
                    WebDriverWait(driver, 60)
                    .until(
                        EC.presence_of_element_located(
                            (By.XPATH, "//li[text()='Airline']")
                        )
                    )
                )
                driver.find_element(By.XPATH, "//li[text()='Airline']").click()
            except:
                pass
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//input[@name="query"])')
                    )
                )
            )
            driver.find_element(By.XPATH, '(//input[@name="query"])').send_keys(
                airline_name, Keys.ENTER
            )
            new_airline = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, f'//div[text()="{airline_name}"]')
                )
            ).is_displayed()
            assert new_airline
            logging.info(
                f"New created Airline is moved under Airlines folder = {new_airline}"
            )
            logging.info(
                "Verify Airline is created/updated in the system, moved to Airlines folder."
            )
            
            utils.goCheckSNS(driver, "airline_publish", UID, True)
            
            assert driver.find_element(
                By.XPATH, f"//span[contains(text(),'{DEDICATED_STORE_ID}')]"
            ).is_displayed()
            logging.info(f"Asserted the store id as {DEDICATED_STORE_ID}")

            utils.close_All_Tabs(driver)
            utils.goCheckSNS(driver, "auth_token_update", UID, True)

            time.sleep(5)
            assert driver.find_element(
                By.XPATH, f"(//span[contains(text(),'{ICAOCode}')])[1]"
            ).is_displayed()
            logging.info(f"Asserted the ICAO code as {ICAOCode}")

            # Update
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, UID)
            update_airline_name = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(10)
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//input[@name='airlineName']")
                )
            )
            driver.find_element(By.XPATH, "//input[@name='airlineName']").clear()
            driver.find_element(By.XPATH, "//input[@name='airlineName']").send_keys(
                update_airline_name
            )
            logging.info(f"Updated name to: {update_airline_name}")
            save_and_publish = driver.find_element(
                By.XPATH, "//span[contains(text(),'Save & Publish')]"
            )
            save_and_publish.click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            # Navigate to the Triggered SNS - List View
            utils.close_All_Tabs(driver)
            utils.goCheckSNS(driver, "airline_publish", update_airline_name, True)
            
            time.sleep(5)
            
            # Unpublish
            utils.close_All_Tabs(driver)
            utils.search_by_id(driver, UID)
            driver.find_element(
                By.XPATH, "//span[contains(text(),'Unpublish')]"
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.saveSuccessfullyMessageXpath)
                )
            )
            # Navigate to the Triggered SNS - List View
            utils.close_All_Tabs(driver)
            utils.goCheckSNS(driver, "airline_unpublish", UID, True)
            
            Success_List_Append(
                "test_MKTPL_1646_SNS_008_009_010_MKTPL_3335_SNS_001_002_MKTPL_2523_Authorizationtoken_004",
                "Verify when a new airline object is created, updated and unpublished via UI and published",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1646_SNS_008_009_010_MKTPL_3335_SNS_001_002_MKTPL_2523_Authorizationtoken_004",
            "Verify when a new airline object is created, updated and unpublished via UI and published",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1646_SNS_008_009_010_MKTPL_3335_SNS_001_002_MKTPL_2523_Authorizationtoken_004",
            "Verify when a new airline object is created, updated and unpublished via UI and published",
            e,
        )
        raise e

@jamatest.test(14167697)
def test_MKTPL_3763_Pricezero_008(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3763_Pricezero_008.png", False
        ) as driver:
            ROUTE_CATALOG_ID_URL = (
                ROUTE_CATALOG + DEDICATED_CATALOG_ASSIGNMENT_5_ID + "/price"
            )
            logging.info(ROUTE_CATALOG_ID_URL)
            RANDOM_NUMBER = "".join([str(randint(0, 9)) for i in range(4)])
            logging.info(RANDOM_NUMBER)
            payload = json.dumps(
                {
                    "catalog": {
                        "id": int(DEDICATED_CATALOG_1_ID),
                        "products": [
                            {
                                "productId": int(DEDICATED_SKU_1_ID),
                                "productPrice": int(RANDOM_NUMBER),
                            }
                        ],
                    }
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request(
                "PUT", ROUTE_CATALOG_ID_URL, headers=headers, data=payload
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API for Authentication responded with 200 status code")
            # SNS

            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            utils.Assets(driver)
            list1 = driver.find_elements(
                By.XPATH, "(//div[contains(text(),'catalog_assignment_publish')])"
            )
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(
                By.XPATH,
                f"(//div[contains(text(),'catalog_assignment_publish')])[{num - 1}]",
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open1 = driver.find_element(By.XPATH, xpath.open)
            # open1.click()
            wait = WebDriverWait(driver, 60)
            # try:
            #     wait.until(
            #         EC.presence_of_element_located(
            #             (By.XPATH, xpath.yes)
            #         )
            #     )
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")

            logging.info("Opened the log file - json")
            id_span = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//span[contains(text(),"catalog_assignment_publish")])[1]',
                    )
                )
            ).is_displayed()
            logging.info(id_span)
            assert id_span
            x = wait.until(
                EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
            )
            x.send_keys(Keys.CONTROL, "f")
            driver.find_element(
                By.XPATH, "//input[contains(@placeholder,'Search for')]"
            ).send_keys(RANDOM_NUMBER, Keys.ENTER)
            time.sleep(2)
            published = driver.find_element(
                By.XPATH, "//span[contains(text()," + RANDOM_NUMBER + ")]"
            )
            ActionChains(driver).move_to_element(published).perform()
            assert published.is_displayed()
            logging.info("Updated price successfully shown in SNS payload")
            Success_List_Append(
                "test_MKTPL_3763_Pricezero_008",
                "Verify if user enters Price, special price and cost=0 in product",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3763_Pricezero_008",
            "Verify if user enters Price, special price and cost=0 in product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3763_Pricezero_008",
            "Verify if user enters Price, special price and cost=0 in product",
            e,
        )
        raise e

def test_MKTPL_2298_Logs_002(PAC_Login):
    externalId = "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2298_Logs_002.png", False
        ) as driver:
            payload = json.dumps(
                {
                    "meta": {
                        "airlineCodeICAO": "CRE",
                        "flightNumber": "",
                        "arrivalAirportCodeIATA": "",
                        "departureAirportCodeIATA": "",
                        "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                        "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00",
                    },
                    "orders": [
                        {
                            "orderKey": "From_Automation_3328 improvement current flight shipment",
                            "externalId": externalId,
                            "basketKey": "From_Automation_3328 improvement current flight shipment",
                            "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                            "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                            "createdTime": "2024-05-24T17:39:51.287+00:00",
                            "lineItems": [
                                {
                                    "title": "KRIS ⋮ JUNIOR POLAR BEAR BY JELLYCAT®",
                                    "PACVariantID": "hospitality:mktplvb48943-var",
                                    "retailerCode": "random",
                                    "associateStore": int(DEDICATED_STORE_ID),
                                    "fulfillmentType": "inHouse",
                                    "alwaysInStock": False,
                                    "imageUrl": "https://images.test/file.jpg",
                                    "vendorProductVariantID": str(DEDICATED_PRODUCT_9_ID),
                                    "unitPrice": {"value": 49, "currency": "USD"},
                                    "unitTax": {"value": 10, "currency": "USD"},
                                    "unitDiscount": {"value": 4.9, "currency": "USD"},
                                    "unitNet": {"value": 44.1, "currency": "USD"},
                                    "unitGross": {"value": 44.1, "currency": "USD"},
                                    "quantity": 2,
                                    "status": "DELIVERED",
                                    "discountTotal": {"currency": "USD", "value": 9.8},
                                    "taxTotal": {"value": 10, "currency": "USD"},
                                    "discountAdjustments": [
                                        {
                                            "discountAmount": {
                                                "currency": "USD",
                                                "value": 12,
                                            },
                                            "adjustType": "DISCOUNT",
                                            "rate": 10,
                                            "promotionCode": "asd",
                                            "promotionName": "Percentage discount 10%",
                                        }
                                    ],
                                    "taxAdjustments": [
                                        {
                                            "type": "shipping_rule_tax",
                                            "taxAmount": {
                                                "currency": "USD",
                                                "value": 12,
                                            },
                                            "rate": 7,
                                        }
                                    ],
                                    "salePrice": {"value": 49, "currency": "USD"},
                                    "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third",
                                }
                            ],
                            "status": "CALL_CREW",
                            "payment": [
                                {
                                    "paymentService": "INTERNAL",
                                    "paymentMethod": "CARD",
                                    "authAmount": {"currency": "USD", "value": 230},
                                    "paymentId": "BBAA-0324111-88",
                                    "technicalServiceProviderTransactionId": "20220324173949153132",
                                    "gatewayTransactionId": "A60087687",
                                    "status": "AUTHORIZED",
                                    "billingAddress": {
                                        "firstName": "karan",
                                        "lastName": "bhatt",
                                        "address1": "123",
                                        "city": "arizona",
                                        "state": "Los angeles",
                                        "postalCode": "78945",
                                        "countryCode": "US",
                                        "email": "<EMAIL>",
                                    },
                                }
                            ],
                            "shipments": [
                                {
                                    "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                                    "rate": {"currency": "USD", "value": 50},
                                    "shippingMethod": "STANDARD",
                                    "carrier": "DHL",
                                    "taxTotal": [{"currency": "USD", "value": 12}],
                                    "address": {
                                        "firstName": "ismail",
                                        "lastName": "rangwala",
                                        "address1": "456",
                                        "city": "Manchester",
                                        "state": "England",
                                        "postalCode": "12345",
                                        "countryCode": "GB",
                                        "email": "<EMAIL>",
                                    },
                                    "itemKeys": [
                                        "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                                    ],
                                    "item": ["21074012"],
                                    "mode": "home_delivery",
                                }
                            ],
                            "orderSummary": {
                                "grossTotal": {"currency": "USD", "value": 200},
                                "discounts": [
                                    {
                                        "discountAmount": {
                                            "value": 20,
                                            "currency": "USD",
                                        },
                                        "promotionCode": "New",
                                        "promotionName": "Percentage discount 10%",
                                        "rate": 0,
                                    }
                                ],
                                "adjustmentTotal": {"currency": "USD", "value": 20},
                                "taxes": [
                                    {
                                        "type": "shipping_rule_tax",
                                        "taxAmount": {"currency": "USD", "value": 12},
                                        "rate": 7,
                                    }
                                ],
                                "totalTaxAmount": {"currency": "USD", "value": 12},
                                "shippingTotal": {"currency": "USD", "value": 50},
                                "currency": "USD",
                                "netTotal": {"currency": "USD", "value": 230},
                            },
                            "user": {
                                "firstName": "John",
                                "lastName": "Doe",
                                "email": "<EMAIL>",
                            },
                            "itinerary": {
                                "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                                "confirmationCode": "6kcb2s",
                                "airline": "SQ322",
                                "firstName": "RAJU",
                                "lastName": "sasi",
                                "middleName": "s",
                            },
                            "seatInfo": {
                                "seatClass": "Outside demo",
                                "seatNumber": "Outside demo",
                            },
                            "groundSystem": "GA",
                        }
                    ],
                }
            )
            headers = {
                "Authorization": ORDER_JWT_TOKEN,
                "Content-Type": "application/json",
            }

            response = requests.request(
                "POST", CONSUME_ORDER_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            time.sleep(5)
            WebDriverWait(driver, 60).until(
                (
                    EC.element_to_be_clickable(
                        (By.XPATH, xpath.assets)
                    )
                )
            )
            driver.find_element(By.XPATH, xpath.assets).click()
            logexpander = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]",
                    )
                )
            )
            driver.execute_script("arguments[0].scrollIntoView();", logexpander)
            driver.find_element(
                By.XPATH,
                "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]",
            ).click()
            logging.info("Clicked on Log")
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'Orders')]")
                )
            )
            time.sleep(5)
            driver.find_element(By.XPATH, "//span[contains(text(),'Orders')]").click()
            time.sleep(8)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(),'List')]")
                )
            )
            driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
            logging.info("Navigated to triggered SNS - List View")
            time.sleep(5)
            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable(
                        (
                            By.XPATH,
                            "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
                        )
                    )
                )
                driver.find_element(
                    By.XPATH,
                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]",
                ).click()
                logging.info("Navigated to last page")
            except Exception as e:
                logging.info("last page error")
                logging.info(e)
                pass
            time.sleep(10)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'order')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(
                By.XPATH, f"(//div[contains(text(),'order')])[{num - 1}]"
            )
            actions = ActionChains(driver)
            actions.double_click(row).perform()
            # open = driver.find_element(By.XPATH, xpath.open)
            # open.click()
            # Open Code if modal appears
            # try:
            #     driver.find_element(By.XPATH, xpath.yes).click()
            # except:
            #     logging.info("Modal is not shown, store not opened on any other device")

            logging.info("Opened the log file - json")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'Successful request to sync orders')]",
                    )
                )
            )

            Success_List_Append(
                "test_MKTPL_2298_Logs_002", "Verify the logs of Orders", "Pass"
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2298_Logs_002", "Verify the logs of Orders", "Fail"
        )
        Failure_Cause_Append("test_MKTPL_2298_Logs_002", "Verify the logs of Orders", e)
        raise e

def test_MKTPL_1655_PatchAPI_002(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_002.png", False
        ) as driver:

            URL = CRED_AUTOMATION_AIRLINE_URL + DEDICATED_FLIGHT_1_ID
            logging.info(URL)
            RANDOM_NUMBER = random.randint(1, 99)
            # 002 - FLIGHT
            payload = json.dumps(
                {
                    "flightRouteNumber": "GGH1232",
                    "fullfilmentCutOffTimeLimit": RANDOM_NUMBER,
                    "sectors": [{"id": int(DEDICATED_SECTOR_1_ID)}],
                }
            )
            logging.info(payload)
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("PATCH", URL, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Flight updated successfully" in resp_data["message"]
            logging.info(f"PATCH API for FLIGHT responded with 200 status code")
            utils.search_by_id(driver, DEDICATED_FLIGHT_1_ID)
            value = driver.find_element(
                By.NAME, "fulfillmentCutOffTimeLimit"
            ).get_attribute("value")
            assert str(RANDOM_NUMBER) in value
            Success_List_Append(
                "test_MKTPL_1655_PatchAPI_002",
                " Verify by updating any parameter in the request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_002",
            " Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_002",
            " Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_003(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_003.png", False
        ) as driver:
            RANDOM_NUMBER = random.randint(1, 99)
            URL = ROUTE_SECTOR_URL + DEDICATED_SECTOR_1_ID
            payload = json.dumps({"distance": RANDOM_NUMBER})
            logging.info(payload)
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("PATCH", URL, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Sector updated successfully" in resp_data["message"]
            logging.info(f"PATCH API for SECTOR responded with 200 status code")
            utils.search_by_id(driver, DEDICATED_SECTOR_1_ID)
            value = driver.find_element(
                By.XPATH,
                "(//span[text()='Distance:']//ancestor::label//following-sibling::div//input)[1]",
            ).get_attribute("value")
            assert str(RANDOM_NUMBER) in value
            Success_List_Append(
                "test_MKTPL_1655_PatchAPI_003",
                " Verify by updating any parameter in the request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_003",
            " Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_003",
            " Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_004(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_004.png", False
        ) as driver:
            # POST
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST",
                CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL,
                headers=headers,
                data=json.dumps({"name": "From_Automation_ROUTE_GROUP_API"}),
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            id = data["id"]
            # PATCH
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            response = requests.request(
                "PATCH",
                CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL + str(id),
                headers=headers,
                data=json.dumps({"name": RANDOM_NAME}),
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Route Group updated successfully" in resp_data["message"]

            utils.search_by_id(driver, id)
            value = driver.find_element(By.NAME, "name").get_attribute("value")
            assert str(RANDOM_NAME) in value
            # delete
            response = requests.request(
                "DELETE",
                CRED_AUTOMATION_AIRLINE_ROUTEGROUP_URL + str(id),
                headers=headers,
                data={},
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Route Group deleted successfully" in resp_data["message"]
            logging.info(f"PATCH API for Route Group responded with 200 status code")
            Success_List_Append(
                "test_MKTPL_1655_PatchAPI_004",
                " Verify by updating any parameter in the request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_004",
            " Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_004",
            " Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_005(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_005.png", False
        ) as driver:
            # Requires auto populate OFF
            RANDOM_NUMBER = random.randint(1, 99)
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            utils.set_cred_airline_auto_populate_map_update_airline_categories(
                driver, "no", None
            )

            # 005 - CATALOG ASSIGNMENT
            payload = utils.get_RouteCatalog_Payload(
                RANDOM_NAME,
                int(DEDICATED_CATALOG_1_ID),
                int(DEDICATED_SKU_1_ID),
                int(DEDICATED_AIRLINE_CATEGORY_1_ID),
                int(DEDICATED_ROUTE_GROUP_1_ID),
                int(DEDICATED_SECTOR_1_ID),
                False,
                "2024-01-24",
                "2024-01-24",
                [],
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", ROUTE_CATALOG, headers=headers, data=payload
            )
            data = response.json()
            logging.info("------ADD------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"POST API responded with 200 status code")
            id = data["id"]
            time.sleep(10)
            # editBaseData
            payload = json.dumps({"catalogAssignmentId": int(id), "status": "editBaseData"})
            response = requests.request(
                "POST", WORKFLOW_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("------editBaseData------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Workflow API responded with 200 status code")
            assert "Workflow Status Updated Successfully" in data["message"]
            # PATCH
            payload = json.dumps(
                {
                    "catalog": {
                        "id": int(DEDICATED_CATALOG_1_ID),
                        "products": [
                            {
                                "productId": int(DEDICATED_SKU_1_ID),
                                "airlineCategory": [DEDICATED_AIRLINE_CATEGORY_1_ID],
                            }
                        ],
                    },
                    "catalogFromDate": "2027-02-02",
                    "catalogToDate": "2029-02-02",
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "PATCH", ROUTE_CATALOG + str(id), headers=headers, data=payload
            )
            data = response.json()
            logging.info("------PATCH------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"PATCH API responded with 200 status code")
            assert ("catalog"
                in data["message"] and "is updated successfully" in data["message"]
            )
            utils.search_by_id(driver, id)
            catalogFromDate = driver.find_element(
                By.NAME, "catalogFromDate"
            ).get_attribute("value")
            assert str("2027-02-02") in catalogFromDate
            catalogToDate = driver.find_element(By.NAME, "catalogToDate").get_attribute(
                "value"
            )
            assert str("2029-02-02") in catalogToDate
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_005",
            "CATALOG ASSIGNMENT Verify by updating any parameter in the request",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_005",
            "CATALOG ASSIGNMENT Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_005",
            "CATALOG ASSIGNMENT Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_006(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_006.png", False
        ) as driver:
            # Requires auto populate OFF
            RANDOM_NUMBER = random.randint(1, 99)
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            utils.set_cred_airline_auto_populate_map_update_airline_categories(
                driver, "no", None
            )

            headers = {
                "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                "Content-Type": "application/json",
            }
            payload = json.dumps(
                {
                    "name": DEDICATED_AIRLINE_CATEGORY_5_NAME,
                    "sequenceNumber": RANDOM_NUMBER,
                }
            )
            response = requests.request(
                "PATCH",
                AIRLINE_CATEGORY_URL + str(DEDICATED_AIRLINE_CATEGORY_5_ID),
                headers=headers,
                data=payload,
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Category updated successfully" in resp_data["message"]
            utils.search_by_id(driver, DEDICATED_AIRLINE_CATEGORY_5_ID)
            sequenceNumber = driver.find_element(
                By.NAME, "sequenceNumber"
            ).get_attribute("value")
            assert str(RANDOM_NUMBER) in sequenceNumber
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_006",
            "CATEGORY - Verify by updating any parameter in the request",
            "Pass",
        )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_006",
            "CATEGORY - Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_006",
            "CATEGORY - Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_007(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_007.png", False
        ) as driver:
            # 007 PRODUCT
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )

            UID = utils.create_Simple_Product("Onboard", "Duty Free", RANDOM_NAME)
            assert UID != None
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            # PATCH
            response = requests.request(
                "PATCH",
                PRODUCTS_URL + str(UID),
                headers=headers,
                data=json.dumps({"deliveryMethod": ["Next Flight"]}),
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Product updated successfully" in resp_data["message"]
            logging.info(f"PATCH API for PRODUCTS responded with 200 status code")

            utils.search_by_id(driver, UID)
            value = driver.find_element(
                By.XPATH,
                "((//input[@name='deliveryType']//ancestor::ul//li)[1]//div)[1]",
            ).text
            assert "Next Flight" in value
            # delete
            response = requests.request(
                "DELETE", PRODUCTS_URL + str(UID), headers=headers, data={}
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Product deleted successfully" in resp_data["message"]
            Success_List_Append(
                "test_MKTPL_1655_PatchAPI_007",
                " Verify by updating any parameter in the request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_007",
            " Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_007",
            " Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_008(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_008.png", False
        ) as driver:
            # 007 PRODUCT
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )

            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            payload = json.dumps({"name": RANDOM_NAME, "products": ["T2002"]})
            response = requests.request(
                "POST", CRED_AUTOMATION_STORE_CATALOG_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            catalog_id = data["id"]
            # PATCH
            name = RANDOM_NAME + "1"
            payload = json.dumps({"name": name})
            response = requests.request(
                "PATCH",
                CRED_AUTOMATION_STORE_CATALOG_URL + str(catalog_id),
                headers=headers,
                data=payload,
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Catalog updated successfully" in resp_data["message"]
            logging.info(f"PATCH API for CATALOG responded with 200 status code")

            utils.search_by_id(driver, catalog_id)
            value = driver.find_element(By.NAME, "name").get_attribute("value")
            assert name in value
            # delete
            response = requests.request(
                "DELETE",
                CRED_AUTOMATION_STORE_CATALOG_URL + str(catalog_id),
                headers=headers,
                data={},
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Catalog deleted successfully" in resp_data["message"]
            Success_List_Append(
                "test_MKTPL_1655_PatchAPI_008",
                " Verify by updating any parameter in the request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_008",
            " Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_008",
            " Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_009(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_009.png", False
        ) as driver:
            RANDOM_NUMBER = random.randint(1, 99)
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )

            # 009 STORE LOCATION
            payload = json.dumps(
                {
                    "name": RANDOM_NAME,
                    "fulfillmentOption": [int(DEDICATED_FULFILMENT_ID)],
                    "icaoCode": "OMDB",
                }
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", STORE_LOCATION_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(STORE_LOCATION_URL)
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            id = data["id"]
            # PATCH
            name = RANDOM_NAME + "1"
            payload = json.dumps({"name": name})
            response = requests.request(
                "PATCH",
                STORE_LOCATION_URL + "/" + str(id),
                headers=headers,
                data=payload,
            )
            logging.info(STORE_LOCATION_URL + "/" + str(id))

            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Store Location updated successfully" in resp_data["message"]
            utils.search_by_id(driver, id)
            value = driver.find_element(By.NAME, "locationName").get_attribute("value")
            assert name in value
            # delete
            response = requests.request(
                "DELETE", STORE_LOCATION_URL + "/" + str(id), headers=headers, data={}
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Store Location deleted successfully" in resp_data["message"]
            logging.info(f"PATCH API for STORE LOCATION responded with 200 status code")
            Success_List_Append(
                "test_MKTPL_1655_PatchAPI_009",
                " Verify by updating any parameter in the request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_009",
            " Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_009",
            " Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_1655_PatchAPI_012(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1655_PatchAPI_012.png", False
        ) as driver:
            # DELIVERY URL
            RANDOM_NUMBER = random.randint(1, 99)
            payload = json.dumps(
                {
                    "deliveryRuleType": "shipping",
                    "shipmentGroupingType": "fulfillment_type",
                    "shippingDestination": "domestic",
                    "deliveryType": ["homeShippingDomesticPriority"],
                    "title": "express",
                    "description": "express",
                    "price": 44,
                    "priceUnit": "USD",
                    "duration": 44,
                    "durationType": "days",
                }
            )
            response = requests.request(
                "POST",
                DELIVERY_URL,
                headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                data=payload,
            )
            resp_data = response.json()
            logging.info(resp_data)
            id = resp_data["id"]
            payload = json.dumps({"price": RANDOM_NUMBER, "priceUnit": "USD"})
            response = requests.request(
                "PATCH",
                DELIVERY_URL + "/" + str(id),
                headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                data=payload,
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200
            resp_data = response.json()
            assert "Delivery Rule updated successfully" in resp_data["message"]
            logging.info(f"PATCH API for DELIVERY RULE responded with 200 status code")

            utils.search_by_id(driver, id)
            value = driver.find_element(
                By.XPATH,
                "(//span[contains(text(),'Price')]//ancestor::label//following-sibling::div//input)[1]",
            ).get_attribute("value")
            assert str(RANDOM_NUMBER) == str(value)
            # delete
            response = requests.request(
                "DELETE",
                DELIVERY_URL + "/" + str(id),
                headers={"Authorization": CRED_AUTOMATION_STORE_TOKEN},
                data="",
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Delivery Rule deleted successfully" in resp_data["message"]
            Success_List_Append(
                "test_MKTPL_1655_PatchAPI_012",
                " Verify by updating any parameter in the request",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1655_PatchAPI_012",
            " Verify by updating any parameter in the request",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1655_PatchAPI_012",
            " Verify by updating any parameter in the request",
            e,
        )
        raise e

def test_MKTPL_5846_Mappingcategories_001_002_003_004(PAC_Login):

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5846_Mappingcategories_001_002_003_004.png", False
        ) as driver:
            
            # ----------------------------------------------------------------------------------------------------------
            utils.set_cred_airline_auto_populate_map_update_airline_categories(
                driver, "no", None
            )
            logging.info(
                "001. The 'Auto Populate Airline Category' option is set to 'no.'"
            )
            # ----------------------------------------------------------------------------------------------------------
            # CREATE THE AIRLINE CATEGORY - API
            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            logging.info(RANDOM_NAME)
            payload = json.dumps(
                {"name": RANDOM_NAME, "disclaimer": "disclaimer add", "url": "SEJ  14"}
            )
            response = requests.request(
                "POST",
                AIRLINE_CATEGORY_URL,
                headers={
                    "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                    "Content-Type": "application/json",
                },
                data=payload,
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Category added successfully. " in resp_data["message"]
            airlineCategoryApiId = resp_data["id"]
            logging.info(airlineCategoryApiId)
            #  Create the  Category
            utils.click_on_add_object(driver, "Category")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.NAME, "name"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "salesStrategy").send_keys("D2C")
            store_dropdown = driver.find_element(
                By.XPATH, '//input[@name="store"]//../following-sibling::div'
            )
            driver.execute_script("arguments[0].click();", store_dropdown)

            time.sleep(1)
            store = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//li[text()="' + DEDICATED_STORE + '"]')
                )
            )
            driver.execute_script("arguments[0].click();", store)
            time.sleep(1)
            # driver.execute_script("arguments[0].click();", store_dropdown)
            time.sleep(1)
            utils.save_and_publish(driver)
            # Get UID
            UID = utils.get_uid(driver)
            logging.info(UID)
            logging.info(
                "002. There is a successful creation of store categories with matching names."
            )
            utils.close_All_Tabs(driver)
            # ----------------------------------------------------------------------------------------------------------
            utils.set_cred_airline_auto_populate_map_update_airline_categories(
                driver, "Yes", None
            )
            logging.info(
                "003. 'Auto Populate Airline Categories' option is set  to 'yes'. "
            )
            # ----------------------------------------------------------------------------------------------------------
            utils.search_by_id(driver, 40661)
            driver.find_element(By.NAME, "query").send_keys(RANDOM_NAME, Keys.ENTER)
            logging.info("entered random name")
            # display = (WebDriverWait(driver, 60).until(
            #     EC.visibility_of_element_located((By.XPATH, '//div[text()="/'+DEDICATED_STORE+'/Category/'+RANDOM_NAME+"_1"+'"]'))).is_displayed())
            display = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[text()="/Cred Automation Airline/Category/'
                            + RANDOM_NAME
                            + '_1"]',
                        )
                    )
                )
                .is_displayed()
            )
            logging.info(display)
            assert display
            logging.info(
                "004. There is a successful creation  of associated airline categories with the exact matching "
                "name of  store categories. "
            )
            # ----------------------------------------------------------------------------------------------------------
            utils.set_cred_airline_auto_populate_map_update_airline_categories(
                driver, "no", None
            )
            logging.info(" SET NO TO DELETE THE AIRLINE CATEGORY")
            # ---------------------------------------------------------------------------------------------------------
            # DELETE THE AIRLINE CATEGORY - API
            response = requests.request(
                "DELETE",
                AIRLINE_CATEGORY_URL + str(airlineCategoryApiId),
                headers={
                    "Authorization": CRED_AUTOMATION_AIRLINE_TOKEN,
                    "Content-Type": "application/json",
                },
                data={},
            )
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Category deleted successfully." in resp_data["message"]
            logging.info("AIRLINE CATEGORY - Deleted successfully")
            Success_List_Append(
                "test_MKTPL_5846_Mappingcategories_001_002_003_004",
                "Verify if exact matching name store categories are created in associated airline categories.",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_5846_Mappingcategories_001_002_003_004",
            "Verify if exact matching name store categories are created in associated airline categories.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5846_Mappingcategories_001_002_003_004",
            "Verify if exact matching name store categories are created in associated airline categories.",
            e,
        )
        raise e

# P1.1 REMAINING---------------------------------------------------------------------------------------------------------------

@jamatest.test(11976363)
def test_MKTPL_378_CRUDAircraft_003(PAC_Login):
    try:
        RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
        logging.info(RANDOM_NAME)
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_378_CRUDAircraft_003.png", False
        ) as driver:
            utils.create_aircraft_pac_admin(driver, RANDOM_NAME)
            logging.info('Aircraft successfully created/updated in the system')
            utils.search_by_path(driver, "/Master Data/Aircraft")
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, "query")))
            driver.find_element(By.NAME, "query").send_keys(RANDOM_NAME, Keys.ENTER)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(text(),'"+RANDOM_NAME+"')])[1]")))
            assert driver.find_element(By.XPATH, "(//div[contains(text(),'"+RANDOM_NAME+"')])[1]").is_displayed()
            Success_List_Append("test_MKTPL_378_CRUDAircraft_003",
                                "Verify by entering/updating data in below fields:- OEM Name- Description- Family Name- Model Name- Image", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_378_CRUDAircraft_003",
                            "Verify by entering/updating data in below fields:- OEM Name- Description- Family Name- Model Name- Image", "Fail")
        Failure_Cause_Append("test_MKTPL_378_CRUDAircraft_003",
                             "Verify by entering/updating data in below fields:- OEM Name- Description- Family Name- Model Name- Image",
                             e)
        raise e

def test_MKTPL_5458_ExportImportUiTemplate_001_004_005(PAC_Login):
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5458_ExportImportUiTemplate_001_004_005.png", False
        ) as driver:
            
            utils.search_by_path(driver, "/Master Data/UI Templates/Cred Automation Airline")
            utils.explicitWaitUntilPresenceOfElementLocatedByXpath(driver, xpath.exportCsvXpath)
            driver.find_element(By.XPATH, xpath.exportCsvXpath).is_displayed()
            logging.info('Export CSV button should be visible in the UI Templates>>{Airline Name} folder grid')
            driver.find_element(By.XPATH, '(//div[contains(text(),"/Master Data/UI Templates/Cred Automation Airline")])[1]').click()
            time.sleep(2)
            driver.find_element(By.XPATH, xpath.exportCsvXpath).click()
            logging.info('clicked for one')
            time.sleep(5)
            path = utils.random_numer_file_download_path(LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_EXPORT_SAMPLE)
            assert 'UITemplate' in path
            time.sleep(2)
            driver.find_element(By.XPATH, '(//div[contains(text(),"/Master Data/UI Templates/Cred Automation Airline")])[1]').click()
            time.sleep(2)
            driver.find_element(By.XPATH, '(//div[contains(text(),"/Master Data/UI Templates/Cred Automation Airline")])[2]').click()
            driver.find_element(By.XPATH, xpath.exportCsvXpath).click()
            time.sleep(5)
            pathOfSecondTime = utils.random_numer_file_download_path(LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_EXPORT_SAMPLE)
            assert 'UITemplate' in pathOfSecondTime
            os.remove(pathOfSecondTime)
            os.remove(path)
            logging.info('User can export multiple published Meal Code data')
            Success_List_Append("test_MKTPL_5458_ExportImportUiTemplate_001_004_005",
                                "Verify by selecting checkbox against single row", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5458_ExportImportUiTemplate_001_004_005",
                            "Verify by selecting checkbox against single row", "Fail")
        Failure_Cause_Append("test_MKTPL_5458_ExportImportUiTemplate_001_004_005",
                             "Verify by selecting checkbox against single row",
                             e)
        raise e

def test_MKTPL_5458_ExportImportUiTemplate_007_008_009_013_014(PAC_Login):
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    ACTUAL_CSV_DATA =[]
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5458_ExportImportUiTemplate_007_008_009_013_014.png", False
        ) as driver:
            utils.csv_import_pac_admin(driver, 'UiTemplate')
            logging.info('07. User navigate to the CSV import screen')
            buttonEnabled = driver.find_element(By.XPATH, xpath.UiTemplateSampleFileDownloadButtonXpath)
            verifyButton = buttonEnabled.is_displayed()
            assert verifyButton
            buttonEnabled.click()
            logging.info('08. Download Sample file of UiTemplate button enabled besides import data type dropdown')
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_SAMPLE)
            with open(LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_SAMPLE, "r", encoding='utf-8-sig') as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
                    break
            assert CSV_SAMPLE_UI_TEMPLATE == ACTUAL_CSV_DATA
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_UI_TEMPLATE_SAMPLE)
            logging.info('09. CSV file should be download on the system and sample records should display in CSV')
            utils.update_csv('UiTemplate.csv', 'name', '')
            utils.update_csv('UiTemplate.csv', 'name', RANDOM_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("UiTemplate.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            utils.search_by_path(driver, "/Master Data/UI Templates/Cred Automation Airline")
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))))
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(RANDOM_NAME, Keys.ENTER)
            WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                    (By.XPATH, '(//div[contains(text(),"' + RANDOM_NAME + '")])[1]')))
            logging.info('013. UI Template successfully imported in the system, UI Template object move automatically under'
                         ' Master Data/UI Templates/{Airline Name} folder')
            driver.find_element(By.XPATH, '//span[text()="CSV Import" and @class= "x-tab-inner x-tab-inner-default"]').click()
            utils.update_csv('UiTemplate.csv', 'name', DEDICATED_UI_TEMPLATE_1_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("UiTemplate.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            utils.close_All_Tabs(driver)
            utils.Assets_import_Pac_admin(driver)
            utils.verify_import_logs_pac_admin(driver, "uitemplate_", 'Row Inserted\/updated Successfully')
            today_date = datetime.now().strftime("%d.%m.%Y")
            logging.info(today_date)
            dateText = driver.find_element(By.XPATH, xpath.logsTimeVerifyXpath).text
            logging.info(dateText)
            assert today_date in dateText
            logging.info('014. It updated the old object after csv import')
            Success_List_Append("test_MKTPL_5458_ExportImportUiTemplate_007_008_009_013_014",
                                "Verify by clicking on the CSV import icon in the left side menu", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5458_ExportImportUiTemplate_007_008_009_013_014",
                            "Verify by clicking on the CSV import icon in the left side menu", "Fail")
        Failure_Cause_Append("test_MKTPL_5458_ExportImportUiTemplate_007_008_009_013_014",
                             "Verify by clicking on the CSV import icon in the left side menu",
                             e)
        raise e

def test_MKTPL_5528_ExportImportRoottype_001_002_007_008_009_010(PAC_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5528_ExportImportRoottype_001_002_007_008_009_010.png", False
        ) as driver:
            # 001
            utils.search_by_id(driver, ROOT_TYPE_AIRLINE_FOLDER)

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.Exportcsvbutton))).is_displayed()

            logging.info(" 001 Export CSV button should be visible in the route group folder grid")

            # 002
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//span[@class="x-column-header-checkbox"])[1]')))
            driver.find_element(By.XPATH, '(//span[@class="x-column-header-checkbox"])[1]').click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//tr[@class="  x-grid-row"][@aria-selected="true"])[1]'))).is_displayed()

            logging.info(" 002 The user should be able to successfully select all available root types")

            driver.find_element(By.XPATH, '(//span[@class="x-column-header-checkbox"])[1]').click()

            # 007 008 009 010
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            logging.info(" 007 User should navigate to the CSV import screen")

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("RootType")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.airlineName)))
            airline = driver.find_element(By.NAME, xpath.airlineName)
            time.sleep(1)
            airline.send_keys(DEDICATED_AIRLINE)
            time.sleep(2)
            airline.send_keys(Keys.ENTER)

            logging.info(" 008 Download Sample file of root type button should be enabled besides import data type dropdown")

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.downloadSampleFileroottpe)))
            driver.find_element(By.XPATH, xpath.downloadSampleFileroottpe).click()
            time.sleep(5)

            logging.info(LOCAL_DOWNLOADED_FILE_PATH_ROOT_TYPE)
            with open(
                    LOCAL_DOWNLOADED_FILE_PATH_ROOT_TYPE, "r", encoding='utf-8-sig'
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert ACTUAL_CSV_DATA == CSV_ROOTTYPE_SAMPLE
            os.remove(LOCAL_DOWNLOADED_FILE_PATH_ROOT_TYPE)

            logging.info(" 009 CSV file should be download on the system and sample records should display in CSV")

            RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_lowercase, k=7))
            utils.update_csv("RootType_Sample.csv", "name", RANDOM_NAME)
            
            utils.upload_csv("RootType_Sample.csv", xpath.csvFileSelectButton)

            # path = os.getcwd() + "/credencys_test_ui/" + "RootType_Sample.csv"
            # logging.info(path)
            # driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            # driver.find_element(By.XPATH, xpath.upload).click()

            utils.search_by_path(driver, "/Master Data/Root Type/Cred Automation Airline")

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, xpath.queryName)))
            search = driver.find_element(By.NAME, xpath.queryName)
            search.send_keys(RANDOM_NAME, Keys.ENTER)

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, f'//div[text()="/Master Data/Root Type/Cred Automation Airline/{RANDOM_NAME}"]'))).is_displayed()

            logging.info(" 010 Root type should be successfully imported in the system Root type object should move automatically under Master Data/Root Type/{Airline Name} folder")

            Success_List_Append(
                "test_MKTPL_5528_ExportImportRoottype_001_002_007_008_009_010",
                "Verify the root type folder grid, Verify by clicking on the CSV import icon in the left side menu",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5528_ExportImportRoottype_001_002_007_008_009_010",
                            "Verify the root type folder grid, Verify by clicking on the CSV import icon in the left side menu",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_5528_ExportImportRoottype_001_002_007_008_009_010",
                             "Verify the root type folder grid, Verify by clicking on the CSV import icon in the left side menu",
                             e)
        raise e

def test_MKTPL_5458_ExportImportUiTemplate_006_MKTPL_5528_ExportImportRoottype_006(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5458_ExportImportUiTemplate_006_MKTPL_5528_ExportImportRoottype_006.png", False
        ) as driver:
            wait = WebDriverWait(driver, 60)
            data = ["UITemplate", "RootType"]
            for i in data:
                if i is "UITemplate":
                    utils.search_by_path(driver, "/Master Data/UI Templates/Cred Automation Airline")
                elif i is "RootType":
                    utils.search_by_path(driver, "/Master Data/Root Type/Cred Automation Airline")
                wait.until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(@id, 'trigger-picker')])[1]")))
                logging.info("Click on dropdown")
                driver.find_element(By.XPATH, "(//div[contains(@id, 'trigger-picker')])[1]").click()
                wait.until(EC.visibility_of_element_located((By.XPATH, "//li[text()='100']")))
                driver.find_element(By.XPATH, "//li[text()='100']").click()
                logging.info("Select 100")
                utils.wait_for_style_attribute(driver, 40)
                time.sleep(5)
                list = driver.find_elements(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)")
                num = len(list)
                logging.info("Number of published items: " + str(num))
                for i in range(1, num+1):
                    wait.until(EC.element_to_be_clickable((By.XPATH, "((//span[contains(@class, 'checked')]//ancestor::tr)["+str(i)+"]//td//div)[1]")))
                    driver.find_element(By.XPATH, "((//span[contains(@class, 'checked')]//ancestor::tr)["+str(i)+"]//td//div)[1]" ).click()
                    driver.find_element(By.XPATH, "//div[contains(text(),'Current language: English')]" ).click()
                    logging.info("((//span[contains(@class, 'checked')]//ancestor::tr)["+str(i)+"]//td//div)[1]")
                time.sleep(2)
                logging.info("click on checkboxes")
                time.sleep(5)
                driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
                logging.info("Export CSV")
                time.sleep(5)
                pathOfSecondTime = utils.random_numer_file_download_path(
                    LOCAL_DOWNLOADED_PATH)
                if i is "UITemplate":
                    logging.info("UITemplate")
                    assert 'UITemplate' in pathOfSecondTime
                elif i is "RootType":
                    logging.info("RootType")
                    assert 'RootType' in pathOfSecondTime
                with open(
                    pathOfSecondTime, "r", encoding="utf-8-sig"
                ) as file:
                    logging.info("Into")
                    csv_reader = csv.reader(file)

                    # Skip the header row
                    next(csv_reader)
                    line_count = 0
                    # Iterate over each row in the CSV file
                    for row in csv_reader:
                        logging.info(row)
                        line_count += 1
                    logging.info(line_count)
                    assert line_count == num            
                logging.info(
                    f"CSV file should be download on the system and sample records should display in CSV"
                )
                
                os.remove(pathOfSecondTime)
                utils.close_All_Tabs(driver)
            Success_List_Append(
                "test_MKTPL_5458_ExportImportUiTemplate_006_MKTPL_5528_ExportImportRoottype_006",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5458_ExportImportUiTemplate_006_MKTPL_5528_ExportImportRoottype_006",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5458_ExportImportUiTemplate_006_MKTPL_5528_ExportImportRoottype_006",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

def test_MKTPL_5528_ExportImportRoottype_003_MKTPL_5528_ExportImportRoottype_004_MKTPL_5528_ExportImportRoottype_005(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5528_ExportImportRoottype_003_MKTPL_5528_ExportImportRoottype_004_MKTPL_5528_ExportImportRoottype_005.png", False
        ) as driver:
            # 003 - single row
            # 004 - multiple row
            # 005 - All rows
            test_case = ["All", "single", "multiple"]
            wait = WebDriverWait(driver, 60)
            # utils.search_by_path(driver, "/Master Data/Root Type/Cred Automation Airline")
            # logging.info("Opened")
            # time.sleep(5)
            # total = int((len(driver.find_elements(By.XPATH, "//div[contains(@class,'checkcolumn')]")))/2)
            for i in test_case:
                logging.info(i)
                utils.search_by_path(driver, "/Master Data/Root Type/Cred Automation Airline")
                logging.info("Opened")
                time.sleep(5)
                total = (len(driver.find_elements(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)")))
                if i is "All":
                    wait.until(EC.visibility_of_element_located((By.XPATH, "(//div[contains(@id, 'trigger-picker')])[1]")))
                    logging.info("Click on dropdown")
                    driver.find_element(By.XPATH, "(//div[contains(@id, 'trigger-picker')])[1]").click()
                    wait.until(EC.visibility_of_element_located((By.XPATH, "//li[text()='100']")))
                    driver.find_element(By.XPATH, "//li[text()='100']").click()
                    logging.info("Select 100")
                    time.sleep(5)
                    num = int((len(driver.find_elements(By.XPATH, "//div[contains(@class,'checkcolumn')]")))/2)
                    logging.info(num)
                    # for i in range(1, num+1):
                    #     driver.find_element(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)[" +str(i)+ "]" ).click()
                    driver.find_element(By.XPATH, "(//div[contains(@id, 'headercontainer') and contains(@class,'ct x-docked')]//div[contains(@class, 'checkbox')]//div)[2]").click()
                    time.sleep(2)
                    logging.info("click on All checkbox")
                elif i is "single":
                    if total>0:
                        driver.find_element(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)[1]" ).click()
                        logging.info("Selected one")
                    else:
                        logging.info("There are no published UI templates")
                elif i is "multiple":
                    if total>1:
                        driver.find_element(By.XPATH, "((//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)[1]//td//div)[1]" ).click()
                        driver.find_element(By.XPATH, "((//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)[2]//td//div)[1]" ).click()
                        logging.info("Selected two")
                    else:
                        logging.info("There are no published UI templates")
                driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
                logging.info("Export CSV")
                time.sleep(5)
                if (i is "single") or (i is "multiple"):
                    pathOfSecondTime = utils.random_numer_file_download_path(
                        LOCAL_DOWNLOADED_PATH)
                    logging.info("RootType")
                    assert 'RootType' in pathOfSecondTime
                    with open(
                        pathOfSecondTime, "r", encoding="utf-8-sig"
                    ) as file:
                        logging.info("Into")
                        csv_reader = csv.reader(file)
                        # Skip the header row
                        next(csv_reader)
                        line_count = 0
                        # Iterate over each row in the CSV file
                        for row in csv_reader:
                            logging.info(row)
                            line_count += 1
                        logging.info(line_count)
                if i is "All":
                    try:
                        pathOfSecondTime = utils.random_numer_file_download_path(
                        LOCAL_DOWNLOADED_PATH)
                        logging.info("RootType")
                        assert 'RootType' in pathOfSecondTime
                        with open(
                            pathOfSecondTime, "r", encoding="utf-8-sig"
                        ) as file:
                            logging.info("Into")
                            csv_reader = csv.reader(file)
                            # Skip the header row
                            next(csv_reader)
                            line_count = 0
                            # Iterate over each row in the CSV file
                            for row in csv_reader:
                                logging.info(row)
                                line_count += 1
                            logging.info(line_count)
                        assert line_count == num
                        logging.info("Asserted all entries")
                        
                    except:
                        logging.info("Could not export")
                        assert driver.find_element(By.XPATH, "//div[contains(text(),'Please Select only Published Root Type')]").is_displayed()
                        driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
                elif i is "single":
                    if total>0:
                        assert line_count == 1
                        logging.info("Asserted single entry")
                elif i is "multiple":
                    if total>1:
                        assert line_count == 2
                        logging.info("Asserted two entries")
                                
                logging.info(
                    f"CSV file should be download on the system and sample records should display in CSV"
                )
                
                
                utils.close_All_Tabs(driver)
            Success_List_Append(
                "test_MKTPL_5528_ExportImportRoottype_003_MKTPL_5528_ExportImportRoottype_004_MKTPL_5528_ExportImportRoottype_005",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5528_ExportImportRoottype_003_MKTPL_5528_ExportImportRoottype_004_MKTPL_5528_ExportImportRoottype_005",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5528_ExportImportRoottype_003_MKTPL_5528_ExportImportRoottype_004_MKTPL_5528_ExportImportRoottype_005",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

def test_MKTPL_5528_ExportImportRoottype_006(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5528_ExportImportRoottype_006.png", False
        ) as driver:
            
            
            utils.search_by_path(driver, "/Master Data/Root Type/Cred Automation Airline")
            logging.info("Opened")
            time.sleep(5)
            num = int((len(driver.find_elements(By.XPATH, "//div[contains(@class,'checkcolumn')]")))/2)
            logging.info(num)
            # for i in range(1, num+1):
            #     driver.find_element(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)[" +str(i)+ "]" ).click()
            driver.find_element(By.XPATH, "(//div[contains(@id, 'headercontainer') and contains(@class,'ct x-docked')]//div[contains(@class, 'checkbox')]//div)[2]").click()
            time.sleep(2)
            logging.info("click on All checkbox")
            driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
            logging.info("Export CSV")
            time.sleep(5)
            try:
                pathOfSecondTime = utils.random_numer_file_download_path(
                    LOCAL_DOWNLOADED_PATH)
                logging.info("RootType")
                assert 'RootType' in pathOfSecondTime
                with open(
                    pathOfSecondTime, "r", encoding="utf-8-sig"
                ) as file:
                    logging.info("Into")
                    csv_reader = csv.reader(file)
                    # Skip the header row
                    next(csv_reader)
                    line_count = 0
                    # Iterate over each row in the CSV file
                    for row in csv_reader:
                        logging.info(row)
                        line_count += 1
                    logging.info(line_count)
                assert line_count == num
                logging.info("Asserted all entries")
                logging.info(
                    f"CSV file should be download on the system and sample records should display in CSV"
                )
            except:
                logging.info("File not downloaded")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//div[contains(text(),'Please Select only Published Root Type')]")))
                driver.find_element(By.XPATH, "//div[contains(text(),'Please Select only Published Root Type')]")         
                driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()   
                logging.info("Validation shown")
            os.remove(pathOfSecondTime)
            Success_List_Append(
                "test_MKTPL_5528_ExportImportRoottype_006",
                "Verify the maximum limit of export data in the CSV",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5528_ExportImportRoottype_006",
            "Verify the maximum limit of export data in the CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_5528_ExportImportRoottype_006",
            "Verify the maximum limit of export data in the CSV",
            e,
        )
        raise e

def test_MKTPL_5529_ExportImportMealcode_021_022_023_027_028(PAC_Login):
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5529_ExportImportMealcode_021_022_023_027_028.png", False
        ) as driver:
           
            utils.csv_import_pac_admin(driver, 'Mealcode')
            logging.info('User navigate to the CSV import screen')
            buttonEnabled = driver.find_element(By.XPATH, xpath.mealCodeSampleFileDownloadButtonXpath)
            verifyButton = buttonEnabled.is_displayed()
            assert verifyButton
            buttonEnabled.click()
            logging.info('Download Sample file of MealCode button enabled besides import data type dropdown')
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_MEALCODE_SAMPLE)
            with open(LOCAL_DOWNLOADABLE_FILE_PATH_MEALCODE_SAMPLE, "r", encoding='utf-8-sig') as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
                    break
            assert CSV_SAMPLE_MEAL_CODE == ACTUAL_CSV_DATA
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_MEALCODE_SAMPLE)
            utils.update_csv('Mealcode.csv', 'mealcodes', '')
            utils.update_csv('Mealcode.csv', 'mealcodes', RANDOM_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Mealcode.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            utils.search_by_id(driver, MEAL_CODE_FOLDER_AIRLINE_LOGIN)
            try:
                (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.selectClassXpath))))
                select_class = driver.find_element(By.XPATH, xpath.selectClassXpath)
                select_class.click()
                select_class.send_keys("MealCode", Keys.ENTER)
                select_class.send_keys(Keys.ENTER)
            except:
                logging.info("Select class not shown")
                pass
            (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.queryXpath))))
            driver.find_element(By.XPATH, xpath.queryXpath).send_keys(RANDOM_NAME, Keys.ENTER)
            WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '(//div[contains(text(),"' + RANDOM_NAME + '")])[1]')))
            logging.info('Meal code should be successfully imported in the system, '
                         'Meal code object should move automatically under {Airline Name} folder/Meal Code folder')
            driver.find_element(By.XPATH,
                                '//span[text()="CSV Import" and @class= "x-tab-inner x-tab-inner-default"]').click()
            utils.update_csv('Mealcode.csv', 'mealcodes', DEDICATED_MEALCODE_2_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Mealcode.csv", file_path)
            logging.info(saved_message)
            assert saved_message
            utils.close_All_Tabs(driver)
            utils.Assets_import_Pac_admin(driver)
            utils.verify_import_logs_pac_admin(driver, "mealcode_", 'Row Inserted\/updated Successfully')
            today_date = datetime.now().strftime("%d.%m.%Y")
            logging.info(today_date)
            dateText = driver.find_element(By.XPATH, xpath.logsTimeVerifyXpath).text
            logging.info(dateText)
            assert today_date in dateText
            logging.info('It updated the old object after csv import')
            Success_List_Append("test_MKTPL_5529_ExportImportMealcode_021_022_023_027_028",
                                "Verify by clicking on the CSV import icon in the left side menu", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5529_ExportImportMealcode_021_022_023_027_028",
                            "Verify by clicking on the CSV import icon in the left side menu", "Fail")
        Failure_Cause_Append("test_MKTPL_5529_ExportImportMealcode_021_022_023_027_028",
                             "Verify by clicking on the CSV import icon in the left side menu",
                             e)
        raise e

def test_MKTPL_5529_ExportImportMealcode_015_018_019(PAC_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_5529_ExportImportMealcode_015_018_019.png", True
        ) as driver:
            
            test_case = ["single", "multiple"]
            for i in test_case:
                logging.info(i)
                utils.search_by_id(driver, MEAL_CODE_FOLDER_AIRLINE_LOGIN)
                logging.info("Opened")
                time.sleep(5)
                try:
                    driver.find_element(By.NAME, xpath.selectClassName).send_keys("MealCode")
                    driver.find_element(By.NAME, xpath.selectClassName).send_keys(Keys.ENTER)
                    
                except:
                    pass
                total = (len(driver.find_elements(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)")))
                logging.info(total)
                if i is "single":
                    if total>0:
                        driver.find_element(By.XPATH, "(//span[contains(@class, 'checked')]//parent::div//parent::td//parent::tr)[1]" ).click()
                        logging.info("Selected one")
                    else:
                        logging.info("There are no published UI templates")
                elif i is "multiple":
                    if total>1:
                        driver.find_element(By.XPATH, "((//span[contains(@class, 'checked')]//ancestor::tr)[1]//td//div)[1]" ).click()
                        driver.find_element(By.XPATH, "//div[contains(text(),'Current language: English')]" ).click()
                        driver.find_element(By.XPATH, "((//span[contains(@class, 'checked')]//ancestor::tr)[2]//td//div)[1]" ).click()
                        logging.info("Selected two")
                    else:
                        logging.info("There are no published UI templates")
                driver.find_element(By.XPATH, '//span[text()="Export CSV"]').click()
                logging.info("Export CSV")
                time.sleep(5)
                pathOfSecondTime = utils.random_numer_file_download_path(
                    LOCAL_DOWNLOADED_PATH)
                logging.info("Mealcode")
                assert 'Mealcode' in pathOfSecondTime
                with open(
                    pathOfSecondTime, "r", encoding="utf-8-sig"
                ) as file:
                    logging.info("Into")
                    csv_reader = csv.reader(file)
                    # Skip the header row
                    next(csv_reader)
                    line_count = 0
                    # Iterate over each row in the CSV file
                    for row in csv_reader:
                        logging.info(row)
                        line_count += 1
                    logging.info(line_count)
                if i is "single":
                    if total>0:
                        assert line_count == 1
                        logging.info("Asserted single entry")
                elif i is "multiple":
                    if total>1:
                        assert line_count == 2
                        logging.info("Asserted two entries")
                                
                logging.info(
                    f"CSV file should be download on the system and sample records should display in CSV"
                )
                utils.close_All_Tabs(driver)
            Success_List_Append("test_MKTPL_5529_ExportImportMealcode_015_018_019",
                                "Verify the Meal Code folder grid", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5529_ExportImportMealcode_015_018_019",
                            "Verify the Meal Code folder grid", "Fail")
        Failure_Cause_Append("test_MKTPL_5529_ExportImportMealcode_015_018_019",
                             "Verify the Meal Code folder grid",
                             e)
        raise e


