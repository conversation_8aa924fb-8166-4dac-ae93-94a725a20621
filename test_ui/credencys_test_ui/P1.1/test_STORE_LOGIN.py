import random
from random import randint
from jamatest import jamatest
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import credencys_test_ui.credencys_project_configs.utils as utils
from selenium.webdriver.support.wait import WebDriverWait
import logging, os, string, time, csv, requests, json, pytest
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append, Skip_List_Append

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

@pytest.fixture(scope = 'module')
def store_Login():
    try:
        with utils.services_context_wrapper_optimized(
                    True, None, False
                ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            driver.implicitly_wait(10)
            act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
    except:
        logging.info("Login not happened")
        Skip_List_Append(36)
        assert False

@jamatest.test(11975185)
@jamatest.test(11975186)
def test_MKTPL_1101_InventoryModule_002_003(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1101_InventoryModule_002_003.png", False
        ) as driver:
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(
                By.XPATH, '//span[text()="Open Inventory Module"]'
            ).click()
            redirection = driver.find_element(
                By.ID, "inventory_module-legendTitle"
            ).is_displayed()
            assert redirection
            logging.info("User is redirect to the Inventory Module screen")
            sku = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="SKU"]'))
                )
                .is_displayed()
            )
            inventory_module = driver.find_element(
                By.XPATH, '//span[text()="Open Inventory Module"]'
            ).is_displayed()
            name = driver.find_element(
                By.XPATH, '//span[text()="Name "]'
            ).is_displayed()
            variant_attributes = driver.find_element(
                By.XPATH, '//span[text()="Variant Attributes"]'
            ).is_displayed()
            default_inventory = driver.find_element(
                By.XPATH, '(//span[starts-with(text(),"Default Inventory")])[1]'
            ).is_displayed()
            always_in_Stock = driver.find_element(
                By.XPATH, '//span[text()="Always in Stock"]'
            ).is_displayed()
            qty_threshold = driver.find_element(
                By.XPATH, '//span[text()="Qty Threshold"]'
            ).is_displayed()
            total_qty = driver.find_element(
                By.XPATH, '//span[text()="Total Qty"]'
            ).is_displayed()
            edit = driver.find_element(By.XPATH, '//span[text()="Edit"]').is_displayed()
            open = driver.find_element(By.XPATH, '//span[text()="Open"]').is_displayed()
            assert [
                sku,
                inventory_module,
                name,
                variant_attributes,
                default_inventory,
                always_in_Stock,
                qty_threshold,
                total_qty,
                edit,
                open,
            ]
            Success_List_Append(
                "test_MKTPL_1101_InventoryModule_002_003",
                "Verify the grid view in the Inventory Module screen",
                "Pass",
            )
            logging.info(
                "User is able to view the list of Product variants in the grid"
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1101_InventoryModule_002_003",
            "Verify the grid view in the Inventory Module screen",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1101_InventoryModule_002_003",
            "Verify the grid view in the Inventory Module screen",
            e,
        )
        raise e

@jamatest.test(11977321)
@jamatest.test(11977322)
@jamatest.test(11977323)
@jamatest.test(11977324)
def test_MKTPL_941_Categoryimport_001_002_003_004(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_941_Categoryimport_001_002_003_004.png", False
        ) as driver:
             
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            store.send_keys("Category")
            time.sleep(1)
            store.send_keys(Keys.ENTER)
            impdatatype = driver.find_element(By.NAME, "importType").is_displayed()
            logging.info(impdatatype)
            assert impdatatype
            logging.info("Import data type is selected")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
            )
            ui_button = driver.find_element(
                By.ID, xpath.downloadCsvButtonId
            ).is_displayed()
            logging.info(ui_button)
            assert ui_button
            logging.info(
                "Showing UI Button of 'Download the Sample file of the Category'"
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
                .click()
            )
            # driver.find_element(By.ID, xpath.downloadCsvButtonId).click()
            logging.info(
                "User able to Download the Predefined CSV Using the download button"
            )
            Success_List_Append(
                "test_MKTPL_941_Categoryimport_001_002_003_004",
                "Verify the Download"
                " the Predefined CSV Using the"
                " download button",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_941_Categoryimport_001_002_003_004",
            "Verify the Download " "the Predefined CSV Using the download" " button",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_941_Categoryimport_001_002_003_004",
            "Verify the Download the Predefined CSV" " Using the download button",
            e,
        )
        raise e

@jamatest.test(11976443)
@jamatest.test(11976444)
@jamatest.test(11976445)
@jamatest.test(11976452)
def test_MKTPL_392_332_Catalogimport_001_002_003_010(store_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_392_332_Catalogimport_001_002_003_010.png", False
        ) as driver:
            #  
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            redirect = store.is_displayed()
            logging.info(redirect)
            assert redirect
            logging.info("User is navigate to the CSV import screen")
            store.send_keys("Catalog")
            time.sleep(1)
            store.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
            )
            download_button = driver.find_element(By.ID, xpath.downloadCsvButtonId)
            download_button.is_displayed()
            logging.info(download_button)
            assert download_button
            logging.info(
                "Download Sample File Of Catalog button is enabled besides import data type dropdown"
            )
            download_button.click()
            time.sleep(6)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_CATLOG_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_CATLOG_SAMPLE, "r", encoding="utf-8-sig"
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert CSV_SAMPLE_CATLOG == ACTUAL_CSV_DATA
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_CATLOG_SAMPLE)

            RANDOM_NAME = "From_Automation_" + "".join(
                random.choices(string.ascii_letters, k=7)
            )
            utils.update_csv("Catalog_Sample.csv", "name_en", RANDOM_NAME)
            utils.update_csv("Catalog_Sample.csv", "products", DEDICATED_PRODUCT_9_NAME)
            file_path = xpath.csvFileSelectButton
            saved_message = utils.upload_csv("Catalog_Sample.csv", file_path)
            assert saved_message
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, STORE_CATALOG_FOLDER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//input[@name="query"])[1]')
                    )
                )
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[contains(text(),"'+RANDOM_NAME+'")])[last()]',
                    )
                )
            )
            logging.info(
                "CSV file is download on the system and sample records is display in CSV"
            )
            Success_List_Append(
                "test_MKTPL_392_332_Catalogimport_001_002_003_010",
                "Verify by clicking on the CSV import " "icon in the left side menu",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_392_332_Catalogimport_001_002_003_010",
            "Verify by clicking on the CSV import icon" " in the left side menu",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_392_332_Catalogimport_001_002_003_010",
            "Verify by clicking on the CSV import icon" " in the left side menu",
            e,
        )
        raise e

@jamatest.test(11977325)
@jamatest.test(11977233)
@jamatest.test(11976241)
def test_MKTPL_941_Categoryimport_005_013_MKTPL_341_Login_001(store_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_941_Categoryimport_005_013_MKTPL_341_Login_001.png", False
        ) as driver:
            
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            store.send_keys("Category")
            time.sleep(1)
            store.send_keys(Keys.ENTER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
                .click()
            )
            # driver.find_element(By.ID, xpath.downloadCsvButtonId).click()
            time.sleep(3)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_CATEGORY_SAMPLE)
            # with open(LOCAL_DOWNLOADABLE_FILE_PATH_CATEGORY_SAMPLE, 'r') as file:
            #     csvFile = csv.reader(file)
            #     for lines in csvFile:
            #         logging.info(lines)
            #         ACTUAL_CSV_DATA.append(lines)
            # assert CSV_SAMPLE_CATEGORY == ACTUAL_CSV_DATA
            logging.info(
                "User able to Download the helper file which will have attribute sets of PAC"
            )
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(
                LOCAL_DOWNLOADABLE_FILE_PATH_CATEGORY_SAMPLE
            )
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.upload).click()
            saved_message = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//div[contains(text(),"CSV Uploaded Successfully.")]',
                        )
                    )
                )
                .is_displayed()
            )
            assert saved_message
            logging.info("File uploaded successfully with valid values")
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_CATEGORY_SAMPLE)
            Success_List_Append(
                "test_MKTPL_941_Categoryimport_005_013_MKTPL_341_Login_001",
                "Make sure the Download the helper file which will have attribute sets of PAC",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_941_Categoryimport_005_013_MKTPL_341_Login_001",
            "Make sure the Download the helper file which will have attribute sets of PAC",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_941_Categoryimport_005_013_MKTPL_341_Login_001",
            "Make sure the Download the helper file which will have attribute sets of PAC",
            e,
        )
        raise e

def test_MKTPL_1065_CategorychangesinProduct_003(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1065_CategorychangesinProduct_003.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.createNewProduct).click()
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "category")))
                .click()
            )
            time.sleep(3)
            list_of_cat = driver.find_elements(
                By.XPATH,
                '//div[@class="x-boundlist-list-ct x-unselectable x-scroller"]//li',
            )
            logging.info(len(list_of_cat))
            for i in list_of_cat:
                logging.info(i.text)
                assert DEDICATED_STORE in i.text
            logging.info(
                f"Category dropdown show category based on the store sales strategy"
            )
            Success_List_Append(
                "test_MKTPL_1065_CategorychangesinProduct_003",
                "Verify the category field",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1065_CategorychangesinProduct_003",
            "Verify the category field",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1065_CategorychangesinProduct_003",
            "Verify the category field",
            e,
        )
        raise e

def test_MKTPL_1065_CategorychangesinProduct_011(store_Login):
    CAT_LIST = []

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1065_CategorychangesinProduct_011.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_store(driver)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "importType"))
            )
            data_type = driver.find_element(By.NAME, "importType")
            data_type.send_keys("Products")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(3)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[@id="categoryField-trigger-picker"]')
                    )
                )
                .click()
            )
            time.sleep(2)
            list_of_cat = driver.find_elements(
                By.XPATH, '//div[@id="categoryField-picker-listWrap"]//li'
            )
            logging.info(len(list_of_cat))
            for i in list_of_cat:
                logging.info(i.text)
                CAT_LIST.append(i.text)
            logging.info(CAT_LIST)
            assert ACTUAL_LIST_1065_011[0] in CAT_LIST[0]
            assert DEDICATED_STORE in i.text
            logging.info(
                "Category dropdown should show category based on the store sales strategy"
            )
            Success_List_Append(
                "test_MKTPL_1065_CategorychangesinProduct_011",
                "Verify the category field",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1065_CategorychangesinProduct_011",
            "Verify the category field",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1065_CategorychangesinProduct_011",
            "Verify the category field",
            e,
        )
        raise e

@jamatest.test(11976527)
@jamatest.test(11976530)
@jamatest.test(11976534)
@jamatest.test(11976537)
@jamatest.test(11976542)
@jamatest.test(11976548)
def test_MKTPL_480_CatalogAPI_001_004_008_011_016_022(store_Login):
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_480_CatalogAPI_001_004_008_011_016_022.png", False
        ) as driver:
            # ADD
            payload = json.dumps(
                {"name": RANDOM_NAME, "products": [DEDICATED_PRODUCT_1_NAME]}
            )
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "POST", CRED_AUTOMATION_STORE_CATALOG_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(
                "The Catalog is created in the pimcore with all the details provided in the API"
            )
            id_catalog = data["id"]
            logging.info(f"Catalog Id- {id_catalog}")

            # Login and verifying the object created or not.

             
            utils.search_by_id(driver, STORE_CATALOG_FOLDER)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//input[@name ="query"])')
                    )
                )
                .send_keys(RANDOM_NAME, Keys.ENTER)
            )
            published = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//div[text()="' + RANDOM_NAME + '"])[1]')
                    )
                )
                .is_displayed()
            )
            assert published
            logging.info(
                f"Catalog goes automatically under Catalog Management section/Catalog folder in the pimcore = "
                f"{published}"
            )
            # Update
            url_id = CRED_AUTOMATION_STORE_CATALOG_URL + str(id_catalog)
            logging.info(url_id)
            updated_name = "AC" + RANDOM_NAME
            logging.info(updated_name)
            payload = json.dumps(
                {"name": updated_name, "products": [DEDICATED_PRODUCT_1_NAME]}
            )
            response = requests.request("PUT", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("------UPDATE------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Updated the API responded with 200 status code")
            logging.info(
                f"The Catalog is updated with all the details provided in the API"
            )

            # GET_BY_ID
            response = requests.request("GET", url_id, headers=headers, data=payload)
            data_id = response.json()
            logging.info("-------GET BY ID-------")
            logging.info(data_id)
            assert response.status_code == 200 or response.status_code == 201
            catalogs = data_id.get("data", {}).get("catalog", {})
            assert "id" in catalogs
            assert "name" in catalogs
            assert "store" in catalogs
            assert "products" in catalogs

            # GET
            payload = {}
            response = requests.request(
                "GET", CRED_AUTOMATION_STORE_CATALOG_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------GET-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # GET BY AIRLINE TOKEN
            payload = {}
            headers = {"Authorization": CRED_AUTOMATION_AIRLINE_TOKEN}
            response = requests.request(
                "GET", CATALOG_BY_STORE_URL, headers=headers, data=payload
            )
            data = response.json()
            logging.info("-------GET BY AIRLINE TOKEN-------")
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            # utils.Delete
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request("DELETE", url_id, headers=headers, data=payload)
            resp_data = response.json()
            logging.info("-------utils.Delete-------")
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info(f"Data cleaned up successfully.")
            Success_List_Append(
                "test_MKTPL_480_CatalogAPI_001_004_008_011_016_022",
                "Verify after successfully executing the API",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_480_CatalogAPI_001_004_008_011_016_022",
            "Verify after successfully executing the API",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_480_CatalogAPI_001_004_008_011_016_022",
            "Verify after successfully executing the API",
            e,
        )
        raise e

@jamatest.test(11977776)
def test_MKTPL_3036_Associatemultiplecategories_001(store_Login):
    global driver
    Success = False
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3036_Associatemultiplecategories_001.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_store(driver)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (By.ID, xpath.pimcoreMenu_id)
                )
            ).click()
            wait.until(
                EC.visibility_of_element_located(
                    ("xpath", xpath.createNewProduct)
                )
            ).click()
            wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '//ul[@class="x-tagfield-list"]')
                )
            ).click()
            select_category = wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '(//li[@class="x-boundlist-item"])[1]')
                )
            )
            cat1 = select_category.get_attribute("innerText")
            logging.info(cat1)
            select_category.click()

            select_category = wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '(//li[@class="x-boundlist-item"])[1]')
                )
            )
            cat2 = select_category.get_attribute("innerText")
            select_category.click()
            logging.info(cat2)
            selected_category = wait.until(
                EC.presence_of_all_elements_located(
                    ("xpath", '//div[starts-with(@class,"x-tagfield-item")]')
                )
            )
            logging.info(len(selected_category))

            assert len(selected_category) / 2 > 1
            logging.info(
                "Verify that Multiple category can be selected while creating new products"
            )
            Success_List_Append(
                "test_MKTPL_3036_Associatemultiplecategories_001",
                "Verify associate_multiplecategories",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3036_Associatemultiplecategories_001",
            "Verify associate_multiplecategories",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3036_Associatemultiplecategories_001",
            "Verify associate_multiplecategories",
            e,
        )
        raise e

@jamatest.test(14167687)
def test_MKTPL_3364_Product_001(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3364_Product_001.png", False
        ) as driver:
             
            utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Inventory"])')
                )
            ).click()
            text = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Save your changes first before editing an Inventory"])',
                        )
                    )
                )
                .is_displayed()
            )
            assert text
            logging.info(
                f"Infomative message 'Save your changes first before editing an Inventory' should display in "
                f"inventory tab of the product object = {text}"
            )
            Success_List_Append(
                "test_MKTPL_3364_Product_001",
                "Verify the inventory tab of the product object",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3364_Product_001",
            "Verify the inventory tab of the product object",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3364_Product_001",
            "Verify the inventory tab of the product object",
            e,
        )
        raise e

def test_MKTPL_4591_Categorydropdown_001(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_4591_Categorydropdown_001.png", False
        ) as driver:
            driver.maximize_window()
            # driver = webdriver.Chrome()
            # utils.Pac_Credentials.Login_store(driver)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (By.ID, xpath.pimcoreMenu_id)
                )
            ).click()
            wait.until(
                EC.visibility_of_element_located(
                    ("xpath", xpath.createNewProduct)
                )
            ).click()
            wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '//ul[@class="x-tagfield-list"]')
                )
            ).click()
            select_category = wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '(//li[@class="x-boundlist-item"])[2]')
                )
            )
            cat1 = select_category.get_attribute("innerText")
            logging.info(cat1)
            select_category.click()
            assert "/" in cat1
            logging.info(
                "Verify that Category is displayed - "
                "{Category Name}(/{Store folder Name}/{Category Object Key Name}"
            )
            Success_List_Append(
                "test_MKTPL_4591_Categorydropdown_001",
                "Verify that Category is displayed - "
                "{Category Name}(/{Store folder Name}/{Category Object Key Name}",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_4591_Categorydropdown_001",
            "Verify that Category is displayed - "
            "{Category Name}(/{Store folder Name}/{Category Object Key Name}",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_4591_Categorydropdown_001",
            "Verify that Category is displayed - "
            "{Category Name}(/{Store folder Name}/{Category Object Key Name}",
            e,
        )
        raise e

def test_MKTPL_4591_Categorydropdown_002(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_4591_Categorydropdown_002.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="Catalog Management"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                    )
                )
            )
            utils.search_by_id(driver, DEDICATED_PRODUCT_10_ID)
            driver.execute_script("window.scrollBy(0,200)")
            selected_category = wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '(//div[@class="x-tagfield-item-text"])[1]')
                )
            )
            cat1 = selected_category.get_attribute("innerText")
            logging.info(cat1)
            assert "/" in cat1
            logging.info(
                "Verify that Category is displayed - "
                "{Category Name}(/{Store folder Name}/{Category Object Key Name} in Product Page"
            )
            Success_List_Append(
                "test_MKTPL_4591_Categorydropdown_002",
                "Verify that Category is displayed - "
                "{Category Name}(/{Store folder Name}/{Category Object Key Name} in Product Page",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_4591_Categorydropdown_002",
            "Verify that Category is displayed - "
            "{Category Name}(/{Store folder Name}/{Category Object Key Name} in Product Page",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_4591_Categorydropdown_002",
            "Verify that Category is displayed - "
            "{Category Name}(/{Store folder Name}/{Category Object Key Name} in Product Page",
            e,
        )
        raise e

def test_MKTPL_4591_Categorydropdown_003(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_4591_Categorydropdown_003.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            redirect = store.is_displayed()
            assert redirect
            logging.info("User is navigate to the CSV import screen")
            store.send_keys("Products")
            store.send_keys(Keys.ENTER)
            wait = WebDriverWait(driver, 60)
            select_category = wait.until(
                EC.presence_of_element_located(
                    ("xpath", '//input[@id="categoryField-inputEl"]')
                )
            )
            time.sleep(3)
            driver.execute_script("arguments[0].click();", select_category)
            driver.execute_script("arguments[0].click();", select_category)
            category = wait.until(
                EC.presence_of_element_located(
                    ("xpath", '//div[@id="categoryField-picker-listWrap"]//li[3]')
                )
            )
            category_text = category.get_attribute("innerText")
            logging.info(category_text)
            assert "/" in category_text
            logging.info(
                "User can see - Category is displayed - "
                "{Category Name}(/{Store folder Name}/{Category Object Key Name} in Product CSV import Page"
            )
            Success_List_Append(
                "test_MKTPL_4591_Categorydropdown_003",
                "Verify that Category is displayed - "
                "{Category Name}(/{Store folder Name}/{Category Object Key Name} in Product CSV import Page",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_4591_Categorydropdown_003",
            "Verify that Category is displayed - "
            "{Category Name}(/{Store folder Name}/{Category Object Key Name} in Product CSV import Page",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_4591_Categorydropdown_003",
            "Verify that Category is displayed - "
            "{Category Name}(/{Store folder Name}/{Category Object Key Name} in Product CSV import Page",
            e,
        )
        raise e

@jamatest.test(11976229)
def test_MKTPL_338_CRUDUser_003(store_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_338_CRUDUser_003.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="User Management"]//..//following-sibling::div)[2]',
                    )
                )
            ).click()
            users = wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Users"]'))
            )
            action = ActionChains(driver)
            action.context_click(users).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.refresh))
            )
            driver.find_element(By.XPATH, xpath.refresh).click()
            users = wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Users"]'))
            )
            action = ActionChains(driver)
            action.context_click(users).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Add Object"]')
                )
            )
            time.sleep(1)
            add_object = driver.find_element(By.XPATH, '//span[text()="Add Object"]')
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            time.sleep(1)
            user = driver.find_element(
                By.XPATH, '(//div[@class="x-box-target"]//span[text()="Users"])[2]'
            )
            mouse_hover.move_to_element(user).click().perform()
            driver.implicitly_wait(0)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="Enter the name of the new item"])[1]'
                        "//parent::div//input",
                    )
                )
            ).send_keys(RANDOM_NAME)
            wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
            ).click()
            wait.until(
                EC.presence_of_element_located((By.NAME, "firstName"))
            ).send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "lastName").send_keys("testlastname")
            driver.find_element(By.NAME, "isActive").click()
            driver.find_element(By.NAME, "email").send_keys(RANDOM_NAME + "@gmail.com")
            driver.find_element(By.NAME, "userRole").send_keys("storemanager")
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//span[text()="Mail Subscriptions"]')
                )
            ).click()
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"User is successfully created/updated in the system = {saved}"
            )
            UID = utils.get_uid(driver)
            try:
                wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="Users"]//..//a[2]',
                        )
                    )
                ).click()
                wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//input[@name="filter"]',
                        )
                    )
                ).click()
                wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//input[@name="filter"]',
                        )
                    )
                ).send_keys(RANDOM_NAME, Keys.ENTER)
                new_user = wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()= "'
                            + RANDOM_NAME
                            + '" and @class ="x-tree-node-text  "]',
                        )
                    )
                ).is_displayed()

            except:
                new_user = wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="Users"]//..//..//..//..//..//../table//span[text()="'
                            + RANDOM_NAME
                            + '"]',
                        )
                    )
                ).is_displayed()
            assert new_user
            logging.info(
                f"New created user is moved under User Management = {new_user}"
            )
            logging.info(
                "Verify User is created/updated in the system, moved to User Management and receive an email "
                "for successfull registration."
            )
            Success_List_Append(
                "test_MKTPL_338_CRUDUser_003",
                "Verify User is created/updated in the system, "
                "moved to User Management and receive an email "
                "for successfull registration.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_338_CRUDUser_003",
            "Verify User is created/updated in the system, "
            "moved to User Management and receive an email "
            "for successfull registration.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_338_CRUDUser_003",
            "Verify User is created/updated in the system, "
            "moved to User Management and receive an email "
            "for successfull registration.",
            e,
        )
        raise e

@jamatest.test(11976239)
def test_MKTPL_339_Storeprofile_003(store_Login):
    global driver
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_339_Storeprofile_003.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)

            store = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[text()="Store Profile"]//..//following-sibling::div)[2]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", store)

            cred = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//span[@class= "x-tree-node-text  " and text()="'
                        + DEDICATED_STORE
                        + '"])[2]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", cred)

            try:
                wait.until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="Yes"]'))
                )
                driver.find_element("xpath", '//span[text()="Yes"]').click()
            except:
                pass

            wait.until(
                EC.presence_of_element_located(
                    ("xpath", xpath.airlineProductCatDescriptionXpath)
                )
            ).send_keys("testing description", Keys.TAB)

            wait.until(
                EC.presence_of_element_located(
                    ("xpath", '//span[text()="Address Information"]')
                )
            ).click()

            last_name = wait.until(
                EC.presence_of_element_located(("xpath", '//input[@name="lastName"]'))
            )
            last_name.clear()
            last_name.send_keys("Patelll", Keys.TAB)

            addressLine1 = wait.until(
                EC.presence_of_element_located(
                    ("xpath", '//input[@name="addressLine1"]')
                )
            )
            addressLine1.clear()
            addressLine1.send_keys("Automation Factory", Keys.TAB)

            city = wait.until(
                EC.presence_of_element_located(("xpath", '//input[@name="city"]'))
            )
            city.clear()
            city.send_keys("Surat", Keys.TAB)

            zip_code = wait.until(
                EC.presence_of_element_located(("xpath", '//input[@name="zipCode"]'))
            )
            zip_code.clear()
            zip_code.send_keys("395017", Keys.TAB)

            saved = utils.save_and_publish(driver)
            assert saved

            logging.info("Verify User is able to update Store Profile information")
            Success_List_Append(
                "test_MKTPL_339_Storeprofile_003",
                "Verify update Store Profile information",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_339_Storeprofile_003",
            "Verify update Store Profile information",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_339_Storeprofile_003",
            "Verify update Store Profile information",
            e,
        )
        raise e

@jamatest.test(11976410)
@jamatest.test(11976411)
@jamatest.test(11976412)
def test_MKTPL_390_388_Productimport_001_002_003(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_390_388_Productimport_001_002_003.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            import_screen = (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.ID, "csv_import_form_fieldset-legendTitle")
                    )
                )
                .is_displayed()
            )
            assert import_screen
            logging.info(
                f"User should navigate to the CSV import screen = {import_screen}"
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            )
            store = driver.find_element(By.NAME, "importType")
            store.send_keys("Products")
            time.sleep(3)
            store.send_keys(Keys.ENTER)
            # driver.find_element(By.NAME, 'category[]').click()
            time.sleep(3)
            driver.find_element(By.NAME, "category[]").click()
            driver.implicitly_wait(0)
            # //li[text()="Exclusively cooked at home_3 (Cred Automation Store/Exclusively cooked at home_3)"]
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//li[text()="'
                            + DEDICATED_CATEGORY_1_NAME
                            + " ("
                            + DEDICATED_STORE
                            + "/"
                            + DEDICATED_CATEGORY_1_NAME
                            + ')"]',
                        )
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.ID, xpath.downloadCsvButtonId))
                )
            )
            download_button = driver.find_element(By.ID, xpath.downloadCsvButtonId)
            download_button.is_enabled()
            assert download_button
            logging.info(
                f"Download Sample file of Product button should be enabled besides import data type dropdown"
                f" = {download_button}"
            )
            Success_List_Append(
                "test_MKTPL_390_388_Productimport_001_002_003_004_032",
                "Verify by clicking on the CSV " "import icon in the left side " "menu",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_390_388_Productimport_001_002_003",
            "Verify by clicking on the CSV " "import icon in the left side menu",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_390_388_Productimport_001_002_003",
            "Verify by clicking on the CSV " "import icon in the left side menu",
            e,
        )
        raise e

@jamatest.test(11975356)
def test_MKTPL_1134_Locationobject_018(store_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=4)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1134_Locationobject_018.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)

            inventory_location = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[text()="Inventory Location"]//..//following-sibling::div)[2]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", inventory_location)
            default_inventory = wait.until(
                EC.presence_of_element_located(
                    ("xpath", '(//span[text()="Default Inventory"])[1]')
                )
            )
            driver.execute_script("arguments[0].click();", default_inventory)
            try:
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(("xpath", '//span[text()="Yes"]'))
                )
                driver.find_element("xpath", '//span[text()="Yes"]').click()
            except:
                pass

            location_name = wait.until(
                (
                    EC.visibility_of_element_located(
                        (By.XPATH, '//input[@name="locationName"]')
                    )
                )
            )
            lname_text = location_name.get_attribute("value")
            location_name.clear()
            lname = "Default Inventory " + RANDOM_NAME
            location_name.send_keys(lname)
            fulfillment = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '((//li[@class="x-tagfield-item"])[1]//div)[1]')
                )
            ).text
            logging.info(fulfillment)
            Delete_fulfillment = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '((//li[@class="x-tagfield-item"])[1]//div)[2]')
                )
            )
            driver.execute_script("arguments[0].click();", Delete_fulfillment)
            time.sleep(1)
            driver.find_element(By.NAME, "fullfillmentOption").send_keys(
                "Shipping", Keys.ENTER
            )
            saved = utils.save_and_publish(driver)
            assert saved
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "locationName"))
            )
            location_name = driver.find_element(By.NAME, "locationName").get_attribute(
                "value"
            )
            fulfillment_edit = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[@class="x-form-trigger-wrap x-form-trigger-wrap-default"]//span[@class="x-hidden-clip"])[1]',
                    )
                )
            ).text
            logging.info(fulfillment_edit)
            assert location_name != lname_text and saved
            assert fulfillment not in fulfillment_edit

            UID = utils.get_uid(driver)
            Delete_fulfillment = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '((//li[@class="x-tagfield-item"])[1]//div)[2]')
                )
            )
            driver.execute_script("arguments[0].click();", Delete_fulfillment)
            driver.find_element(By.NAME, "fullfillmentOption").send_keys(
                fulfillment, Keys.ENTER
            )
            saved = utils.save_and_publish(driver)
            assert saved

            logging.info(
                "Store admin is able to update default Inventory Name and Fulfillment"
            )
            Success_List_Append(
                "test_MKTPL_1134_Locationobject_018",
                "Verify default Inventory Name and Fulfillment" " is updated.",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1134_Locationobject_018",
            "Verify default Inventory Name and Fulfillment is updated.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1134_Locationobject_018",
            "Verify default Inventory Name and Fulfillment is updated.",
            e,
        )
        raise e

@jamatest.test(11975549)
@jamatest.test(11975550)
@jamatest.test(11975551)
@jamatest.test(11975552)
def test_MKTPL_1342_Mappriceanddeploymentinventory_009_010_011_012(store_Login):
    RANDOM_CODE = "".join(random.choices(string.digits, k=5))
    logging.info(RANDOM_CODE)
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1342_Mappriceanddeploymentinventory_009_010_011_012.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_store(driver)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_CATALOG_2_ID)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productsTabXpath)
                )
            )
            utils.clickOnDropDownForChooseAction(driver, DEDICATED_CATALOG_2_ID, "Update Deployment Inventory")
            view_tab = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        f'//span[text()="Add/Update Deployment Inventory For :-  {DEDICATED_CATALOG_2_NAME}"]',
                    )
                )
            ).is_displayed()
            assert view_tab
            logging.info(
                f"Asserted Add/Update Deployment Inventory For :-  {DEDICATED_CATALOG_2_NAME}"
            )
            time.sleep(3)
            name = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Product/Variant Name"]')
                )
            ).is_displayed()
            sku = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Product SKU"]')
                )
            ).is_displayed()
            inventory = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Deployment Inventory"]')
                )
            ).is_displayed()
            assert name, sku and inventory
            logging.info(
                f"Asserted Product/Variant Name, Product SKU and Deployment Inventory visibility"
            )
            action = ActionChains(driver)
            edit_name = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '((//span[text()="Deployment Inventory"]//..//..//..//..//..//..//..//../following-sibling::div)[6]//table//tr//td[1]//div)[1]',
                    )
                )
            )
            logging.info("edit_name")
            product_name = edit_name.get_attribute("innerText")
            action.move_to_element(edit_name).click(edit_name).send_keys(
                "TestEdit", Keys.ENTER
            ).perform()
            edit_sku = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '((//span[text()="Deployment Inventory"]//..//..//..//..//..//..//..//../following-sibling::div)[6]//table//tr//td[2])[1]',
                    )
                )
            )
            logging.info("edit_sku")
            product_sku = edit_sku.get_attribute("innerText")
            action.move_to_element(edit_sku).click(edit_sku).send_keys(
                "T123", Keys.ENTER
            ).perform()
            edit_inventory = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '((//span[text()="Deployment Inventory"]//..//..//..//..//..//..//..//../following-sibling::div)[6]//table//tr//td[3])[1]',
                    )
                )
            )
            logging.info("edit_inventory")
            old_inventory = edit_inventory.get_attribute("innerText")
            action.move_to_element(edit_inventory).double_click(
                edit_inventory
            ).send_keys("", Keys.ENTER).perform()
            action.send_keys(RANDOM_CODE, Keys.ENTER).perform()
            time.sleep(3)
            assert product_name != "TestEdit" and product_sku != "T123"
            logging.info("Asserted product_name and product_sku")
            assert old_inventory != RANDOM_CODE
            logging.info("Asserted old_inventory")
            submit = wait.until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Submit"]'))
            )
            submit.click()
            saved = wait.until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Success"]'))
            ).is_displayed()
            assert (old_inventory != RANDOM_CODE) and saved
            logging.info("Asserted old_inventory and saved")
            # wait.until(
            #     EC.visibility_of_element_located(
            #         (By.XPATH, '//span[text()="' + DEDICATED_CATALOG_2_NAME + '"]')
            #     )
            # ).is_displayed()
            qty = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[text()="' + RANDOM_CODE + '"])[1]')
                )
            ).text
            assert qty == RANDOM_CODE
            logging.info("Asserted qty")
 
            logging.info(
                "Verify User able to Redirect to a new window and Products attributes --"
                "Product/Variant Name - READ ONLY, SKU - READ-ONLY, "
                "Deployment Inventory - EDITABLE -- Save the changes "
                "when select deployment inventory"
            )
            Success_List_Append(
                "test_MKTPL_1342_Mappriceanddeploymentinventory_009_010_011_012",
                "Verify User able to Redirect to a new window and Products attributes --"
                "Product/Variant Name - READ ONLY, SKU - READ-ONLY, "
                "Deployment Inventory - EDITABLE -- Save the changes"
                "when select deployment inventory",
                "Pass",
            )
 
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_009_010_011_012",
            "Verify User able to Redirect to a new window and Products attributes --"
            "Product/Variant Name - READ ONLY, SKU - READ-ONLY, "
            "Deployment Inventory - EDITABLE -- Save the changes"
            "when select deployment inventory",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_009_010_011_012",
            "Verify User able to Redirect to a new window and Products attributes --"
            "Product/Variant Name - READ ONLY, SKU - READ-ONLY, "
            "Deployment Inventory - EDITABLE -- Save the changes"
            "when select deployment inventory",
            e,
        )
        raise e

# def test_MKTPL_1342_Mappriceanddeploymentinventory_014_015(store_Login):
#     RANDOM_CODE = "".join(random.choices(string.digits, k=5))
#     ACTUAL_CSV_DATA = []
#     try:
#         with utils.services_context_wrapper_optimized(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_014_015.png"
#         ) as driver:
#             driver.maximize_window()
#             utils.Pac_Credentials.Login_store(driver)
#             driver.implicitly_wait(10)
#             act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
#             assert act_title == "pimcore_logout"
#             logging.info("Login is successful.")
#             utils.wait_for_style_attribute(driver, 40)
#             wait = WebDriverWait(driver, 60)
#             utils.search_by_id(driver, DEDICATED_CATALOG_2_ID)
#             wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, xpath.productsTabXpath)
#                 )
#             ).click()
#             dropdown = wait.until(
#                 EC.visibility_of_element_located(
#                     (
#                         By.XPATH,
#                         '(//span[contains(text(),"Choose")]//..//..//..//div)[4]',
#                     )
#                 )
#             )
#             driver.execute_script("arguments[0].click();", dropdown)
#             driver.implicitly_wait(0)
#             wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '//li[text()="Export Price CSV"]')
#                 )
#             ).click()
#             time.sleep(5)
#             logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_PRICEUPDATE_SAMPLE)
#             with open(
#                 LOCAL_DOWNLOADABLE_FILE_PATH_PRICEUPDATE_SAMPLE,
#                 "r",
#                 encoding="utf-8-sig",
#             ) as file:
#                 csvFile = csv.reader(file)
#                 for lines in csvFile:
#                     logging.info(lines)
#                     ACTUAL_CSV_DATA.append(lines)
#                     break
#             assert CSV_SAMPLE_PRICEUPDATE == ACTUAL_CSV_DATA
#             os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_PRICEUPDATE_SAMPLE)
#             logging.info(
#                 "CSV file is download on the system and sample records is display in CSV"
#             )
#             logging.info(
#                 "Verify User is able to download export price CSV with Fields - "
#                 "Product/Variant Name, SKU, Catalog assignment ID, Price"
#             )
#             Success_List_Append(
#                 "test_MKTPL_1342_Mappriceanddeploymentinventory_014_015",
#                 "Verify User is able to download export price CSV",
#                 "Pass",
#             )

#     except Exception as e:
#         logging.info(f"Error- {e}")
#         Success_List_Append(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_014_015",
#             "Verify User is able to download export price CSV",
#             "Fail",
#         )
#         Failure_Cause_Append(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_014_015",
#             "Verify User is able to download export price CSV",
#             e,
#         )
#         raise e

@jamatest.test(11975222)
@jamatest.test(11975227)
@jamatest.test(11975228)
def test_MKTPL_1103_Leadtimeproduct_001_006_007(store_Login):
    RANDOM_CODE = "".join(random.choices(string.digits, k=2))
    RANDOM_HRS = "".join(random.choices(string.digits, k=2))
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1103_Leadtimeproduct_001_006_007.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_PRODUCT_2_ID)
            scroll = wait.until(
                EC.presence_of_element_located(("xpath", xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            ).text
            logging.info(delivery_method)

            if "Next Flight" not in delivery_method:
                dropdown = wait.until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '((//div[@class="x-form-item-body x-form-item-body-default x-form-text-field-body x-form-text-field-body-default x-form-text-grow x-tagfield-body"])[2]/div/div)[2]',
                        )
                    )
                )
                driver.execute_script("arguments[0].click();", dropdown)
                driver.implicitly_wait(0)
                add_flight = wait.until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//li[text()="Next Flight"])[1]')
                    )
                )
                action = ActionChains(driver)
                action.move_to_element(add_flight).click().perform()
                driver.implicitly_wait(0)
                driver.execute_script("arguments[0].click();", dropdown)
            else:
                pass
            if "Gate Pick Up" not in delivery_method:
                dropdown = wait.until(
                    EC.element_to_be_clickable(
                        (
                            By.XPATH,
                            '((//div[@class="x-form-item-body x-form-item-body-default x-form-text-field-body x-form-text-field-body-default x-form-text-grow x-tagfield-body"])[2]/div/div)[2]',
                        )
                    )
                )
                driver.execute_script("arguments[0].click();", dropdown)
                driver.implicitly_wait(0)
                gate_pickup = wait.until(
                    EC.presence_of_element_located(
                        (By.XPATH, '(//li[text()="Gate Pick Up"])[1]')
                    )
                )
                action = ActionChains(driver)
                action.move_to_element(gate_pickup).click().perform()
                driver.implicitly_wait(0)
                driver.execute_script("arguments[0].click();", dropdown)
            else:
                pass
            next_flight_leadtime = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//input[@name="nextFlightLeadTime"]')
                )
            )
            flight_old_time = next_flight_leadtime.get_attribute("value")
            logging.info(flight_old_time)
            next_flight_leadtime.clear()
            next_flight_leadtime.send_keys(RANDOM_CODE, Keys.ENTER)
            logging.info(RANDOM_CODE)
            gate_leadtime = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//input[@name="gatePickupLeadTime"]')
                )
            )
            gate_old_time = gate_leadtime.get_attribute("value")
            logging.info(gate_old_time)
            gate_leadtime.clear()
            gate_leadtime.send_keys(RANDOM_HRS, Keys.ENTER)
            logging.info(RANDOM_HRS)

            assert flight_old_time != RANDOM_CODE and gate_old_time != RANDOM_HRS
            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            ).text
            logging.info(delivery_method)
            if "Next Flight" in delivery_method:
                remove_flight = wait.until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//li//div[text()="Next Flight"]//../div)[2]')
                    )
                )
                driver.execute_script("arguments[0].click();", remove_flight)
                driver.implicitly_wait(0)
                logging.info("removed")
            else:
                pass
            dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '((//div[@class="x-form-item-body x-form-item-body-default x-form-text-field-body x-form-text-field-body-default x-form-text-grow x-tagfield-body"])[2]/div/div)[2]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            add_flight = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//li[text()="Next Flight"])[1]')
                )
            )
            action = ActionChains(driver)
            action.move_to_element(add_flight).click().perform()
            driver.implicitly_wait(0)
            driver.execute_script("arguments[0].click();", dropdown)

            next_flight_leadtime = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//span[@class="x-form-item-label-inner x-form-item-label-inner-default"])[8]',
                    )
                )
            )
            leadtime_visible = next_flight_leadtime.is_displayed()
            comulsory_leadtime = next_flight_leadtime.get_attribute("innerText")
            assert "*" not in comulsory_leadtime and leadtime_visible
            logging.info(
                "Verify User select Next Flight in delivery type enable new field is Next Flight Lead Time "
                "which is non mandatory field"
            )
            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            ).text
            logging.info(delivery_method)
            if "Gate Pick Up" in delivery_method:
                remove_pickup = wait.until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//li//div[text()="Gate Pick Up"]//../div)[2]')
                    )
                )
                driver.execute_script("arguments[0].click();", remove_pickup)
                driver.implicitly_wait(0)
            else:
                pass
            dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '((//div[@class="x-form-item-body x-form-item-body-default x-form-text-field-body x-form-text-field-body-default x-form-text-grow x-tagfield-body"])[2]/div/div)[2]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            add_gate = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//li[text()="Gate Pick Up"])[1]')
                )
            )
            action = ActionChains(driver)
            action.move_to_element(add_gate).click().perform()
            driver.implicitly_wait(0)
            driver.execute_script("arguments[0].click();", dropdown)
            gate_leadtime = wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//span[@class="x-form-item-label-inner x-form-item-label-inner-default"])[9]',
                    )
                )
            )
            leadtime_visible = gate_leadtime.is_displayed()
            comulsory_leadtime = gate_leadtime.get_attribute("innerText")

            assert "*" not in comulsory_leadtime and leadtime_visible

            logging.info("Verify User is able to input Lead Time for the product")
            Success_List_Append(
                "test_MKTPL_1103_Leadtimeproduct_001_006_007",
                "Verify input Lead Time for the product",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1103_Leadtimeproduct_001_006_007",
            "Verify input Lead Time for the product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1103_Leadtimeproduct_001_006_007",
            "Verify input Lead Time for the product",
            e,
        )
        raise e

@jamatest.test(11975562)
@jamatest.test(11975563)
def test_MKTPL_1342_Mappriceanddeploymentinventory_022_023(store_Login):
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1342_Mappriceanddeploymentinventory_022_023.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_CATALOG_2_ID)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.productsTabXpath)
                )
            )
            utils.clickOnDropDownForChooseAction(driver, DEDICATED_CATALOG_2_ID, "Export Deployment Inventory CSV")
            time.sleep(5)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_INVENTORYUPDATE_SAMPLE)
            with open(
                LOCAL_DOWNLOADABLE_FILE_PATH_INVENTORYUPDATE_SAMPLE,
                "r",
                encoding="utf-8-sig",
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
                    break
            assert CSV_SAMPLE_INVENTORYUPDATE == ACTUAL_CSV_DATA
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_INVENTORYUPDATE_SAMPLE)
            logging.info(
                "CSV file is download on the system and sample records is display in CSV"
            )
            logging.info(
                "Verify User is able to download Export Deployment Inventory with Fields - "
                "Product/Variant Name, SKU, Catalog assignment ID, Qty"
            )
            Success_List_Append(
                "test_MKTPL_1342_Mappriceanddeploymentinventory_022_023",
                "Verify User is able to download Export Deployment Inventory CSV",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_022_023",
            "Verify User is able to download Export Deployment Inventory CSV",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_022_023",
            "Verify User is able to download Export Deployment Inventory CSV",
            e,
        )
        raise e

@jamatest.test(11976142)
def test_MKTPL_317_DeliveryType_001(store_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=3)
    )
    RANDOM_NAME_PRODUCT = "".join(random.choices(string.ascii_letters, k=7))

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_317_DeliveryType_001.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            menu = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.ID, xpath.pimcoreMenu_id)
                )
            )
            menu.click()
            product = driver.find_element(
                By.XPATH, xpath.createNewProduct
            )
            action = ActionChains(driver)
            action.move_to_element(product).click().perform()
            time.sleep(1)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[text()="Add new Object of type Product"])[1]')
                )
            )
            wait.until(EC.presence_of_element_located((By.NAME, "sku"))).send_keys(
                RANDOM_NAME, Keys.TAB
            )
            logging.info(RANDOM_NAME)
            time.sleep(1)
            cat_dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="category"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", cat_dropdown)
            time.sleep(1)
            category = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//li[text()="'
                        + DEDICATED_CATEGORY_2_NAME
                        + " ("
                        + DEDICATED_STORE
                        + "/"
                        + DEDICATED_CATEGORY_3_NAME
                        + "/"
                        + DEDICATED_CATEGORY_2_NAME
                        + ')"])[1]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", category)
            time.sleep(1)
            driver.execute_script("arguments[0].click();", cat_dropdown)
            time.sleep(1)

            product_set = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '//input[@name="type"]//../following-sibling::div')
                )
            )
            driver.execute_script("arguments[0].click();", product_set)
            time.sleep(1)
            p_set = wait.until(
                EC.presence_of_element_located((By.XPATH, '//li[text()="Simple"]'))
            )
            driver.execute_script("arguments[0].click();", p_set)
            driver.implicitly_wait(0)
            logging.info("simple")
            ok = driver.find_element(By.XPATH, '(//span[text()="OK"])[1]')
            logging.info(ok.is_displayed())
            driver.execute_script("arguments[0].click();", ok)
            driver.implicitly_wait(0)
            wait.until(EC.visibility_of_element_located((By.NAME, "name"))).send_keys(
                RANDOM_NAME_PRODUCT
            )

            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="deliveryType"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            add_gate = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//li[text()="Gate Pick Up"])[1]')
                )
            )
            action = ActionChains(driver)
            action.move_to_element(add_gate).click().perform()
            time.sleep(1)
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            wait.until(
                EC.visibility_of_element_located((By.NAME, "gatePickupLeadTime"))
            ).send_keys("8")

            driver.find_element(By.NAME, "productType").send_keys("Food and Beverage")

            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(f"Product is successfully created in the system = {saved}")
            UID = utils.get_uid(driver)
            # driver.refresh()
            # utils.wait_for_style_attribute(driver, 40)
            utils.clickOnReload(driver)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, "name"))
            )
            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            ).text
            logging.info(delivery_method)

            if "Gate Pick Up" in delivery_method:
                remove_pickup = wait.until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//li//div[text()="Gate Pick Up"]//../div)[2]')
                    )
                )
                driver.execute_script("arguments[0].click();", remove_pickup)
                driver.implicitly_wait(0)
                logging.info("Removed Gate Pick Up")
            else:
                pass
            dropdown = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//input[@name="deliveryType"]//..//..//..//../following-sibling::div',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", dropdown)
            driver.implicitly_wait(0)
            add_flight = wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//li[text()="Next Flight"])[1]')
                )
            )
            action = ActionChains(driver)
            action.move_to_element(add_flight).click().perform()
            driver.implicitly_wait(0)
            driver.execute_script("arguments[0].click();", dropdown)
            saved = utils.save_and_publish(driver)
            assert saved
            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            ).text
            logging.info("After updating Delivery Method")
            logging.info(delivery_method)
            logging.info(f"Product is successfully updated in the system = {saved}")
            UID = utils.get_uid(driver)
            Success_List_Append(
                "test_MKTPL_317_DeliveryType_001",
                "Verify by entering/updating data in Delivery Methods",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_317_DeliveryType_001",
            "Verify by entering/updating data in Delivery Methods",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_317_DeliveryType_001",
            "Verify by entering/updating data in Delivery Methods",
            e,
        )
        raise e

# def test_MKTPL_1342_Mappriceanddeploymentinventory_017_018_019_021_MKTPL_1646_1893_1894_1979_SNS_022_023(store_Login):
#     RANDOM_CODE = "".join(random.choices(string.digits, k=3))
#     try:
#         with utils.services_context_wrapper_optimized(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_017_018_019_021_MKTPL_1646_1893_1894_1979_SNS_022_023.png"
#         ) as driver:
#             driver.maximize_window()
#             utils.Pac_Credentials.Login_store(driver)
#             act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
#             assert act_title == "pimcore_logout"
#             logging.info("Login is successful.")
#             utils.wait_for_style_attribute(driver, 40)
#             wait = WebDriverWait(driver, 60)
#             utils.update_csv(
#                 "PriceUpdate.csv", "CatalogAssignmentId", DEDICATED_CATALOG_2_ID
#             )
#             utils.update_csv("PriceUpdate.csv", "Price", RANDOM_CODE)
#             utils.search_by_id(driver, DEDICATED_CATALOG_2_ID)
#             wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, xpath.productsTabXpath)
#                 )
#             ).click()
#             dropdown = wait.until(
#                 EC.visibility_of_element_located(
#                     (
#                         By.XPATH,
#                         '(//span[contains(text(),"Choose")]//..//..//..//div)[4]',
#                     )
#                 )
#             )
#             driver.execute_script("arguments[0].click();", dropdown)
#             wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '//li[text()="Import Price CSV"]')
#                 )
#             ).click()
#             time.sleep(5)
#             file_path = '//input[@name="pricefile"]'
#             saved_message = utils.upload_csv("PriceUpdate.csv", file_path)
#             assert saved_message
#             wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '//span[text()="Close & Reload"]')
#                 )
#             ).click()
#             updated_price = wait.until(
#                 (
#                     EC.visibility_of_element_located(
#                         (By.XPATH, '(//div[text()="' + RANDOM_CODE + '"])[1]')
#                     )
#                 )
#             ).is_displayed()
#             assert updated_price
#             logging.info("Updated Price can be seen in Product grid")
#             driver.find_element(By.XPATH, xpath.logout).click()
#             logging.info("Logged out from Store Admin")
#             wait.until(EC.visibility_of_element_located((By.NAME, "username")))
#             # verify SNS
#             # RANDOM_CODE = '095'
#             # wait = WebDriverWait(driver, 60)
#             utils.Pac_Credentials.Login_Pac_Admin(driver)
#             utils.Assets(driver)
#             list1 = driver.find_elements(
#                 By.XPATH, "//div[contains(text(),'catalog_assignment')]"
#             )
#             num = len(list1)
#             time.sleep(5)
#             row = driver.find_element(
#                 By.XPATH, f"(//div[contains(text(),'catalog_assignment')])[{num-1}]"
#             )
#             actions = ActionChains(driver)
#             actions.context_click(row).perform()
#             open = driver.find_element(By.XPATH, xpath.open)
#             open.click()
#             # Open Code if modal appears
#             try:
#                 if driver.find_element(
#                     By.XPATH, xpath.yes
#                 ).is_displayed()
#                     driver.find_element(
#                         By.XPATH, xpath.yes
#                     ).click()
#             except:
#                 logging.info("Modal is not shown, store not opened on any other device")

#             logging.info("Opened the log file - json")
#             wait.until(
#                 EC.presence_of_element_located(
#                     (By.XPATH, f"//span[contains(text(),'{DEDICATED_CATALOG_2_ID}')]")
#                 )
#             )
#             x = driver.find_element(By.XPATH, "(//textarea)[1]")
#             x.send_keys(Keys.CONTROL, "f")
#             time.sleep(2)
#             driver.find_element(
#                 By.XPATH, "//input[contains(@placeholder,'Search for')]"
#             ).send_keys(DEDICATED_CATALOG_2_ID)
#             catalog_is_2 = driver.find_element(
#                 By.XPATH,
#                 "(//span[contains(text(),'" + DEDICATED_CATALOG_2_ID + "')])[1]",
#             )
#             ActionChains(driver).move_to_element(catalog_is_2).perform()
#             assert catalog_is_2.is_displayed()
#             logging.info(f"Asserted catalog")
#             x = driver.find_element(By.XPATH, "(//textarea)[1]")
#             x.send_keys(Keys.CONTROL, "f")
#             driver.find_element(
#                 By.XPATH, "//input[contains(@placeholder,'Search for')]"
#             ).send_keys("airlineCode")
#             airline_code = driver.find_element(
#                 By.XPATH, f"//span[contains(text(),'airlineCode')]"
#             )
#             ActionChains(driver).move_to_element(airline_code).perform()
#             assert airline_code.is_displayed()
#             logging.info(f"Asserted airline code")
#             x = wait.until(
#                 EC.presence_of_element_located((By.XPATH, "(//textarea)[1]"))
#             )
#             x.send_keys(Keys.CONTROL, "f")
#             driver.find_element(
#                 By.XPATH, "//input[contains(@placeholder,'Search for')]"
#             ).send_keys("price", Keys.ENTER)
#             published = driver.find_elements(
#                 By.XPATH,
#                 "//span[contains(text(),'price')]//parent::div//following-sibling::div//span[contains(text(),'value')]//following-sibling::span",
#             )
#             cnt = 0
#             for i in published:
#                 try:
#                     ActionChains(driver).move_to_element(i).perform()
#                     assert i.text in RANDOM_CODE
#                     cnt = cnt + 1
#                 except:
#                     pass
#             if cnt == 0:
#                 assert False
#             logging.info(f"Asserted random code {RANDOM_CODE}")
#             Success_List_Append(
#                 "test_MKTPL_1342_Mappriceanddeploymentinventory_017_018_019_021_MKTPL_1646_1893_1894_1979_SNS_022_023",
#                 "Verify User can see popup for Import Price CSV  - Update Price in CSV - "
#                 "Import Updated CSV - View updated price value in product grid",
#                 "Pass",
#             )

#     except Exception as e:
#         logging.info(f"Error- {e}")
#         Success_List_Append(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_017_018_019_021_MKTPL_1646_1893_1894_1979_SNS_022_023",
#             "Verify User can see popup for Import Price CSV  - Update Price in CSV -"
#             "Import Updated CSV - View updated price value in product grid",
#             "Fail",
#         )
#         Failure_Cause_Append(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_017_018_019_021_MKTPL_1646_1893_1894_1979_SNS_022_023",
#             "Verify User can see popup for Import Price CSV  - Update Price in CSV - "
#             "Import Updated CSV - View updated price value in product grid",
#             e,
#         )
#         raise e

@jamatest.test(11975542)
def test_MKTPL_1342_Mappriceanddeploymentinventory_002(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1342_Mappriceanddeploymentinventory_002.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_store(driver)
            # # driver.implicitly_wait(10)
            # act_title = driver.find_element(
            #     By.XPATH, xpath.logout
            # ).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            # utils.wait_for_style_attribute(driver,40)
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_CATALOG_2_ID)
            WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located(
                            (By.XPATH, xpath.productsTabXpath)
                        )
                    ).click()
            dropdown = driver.find_element(
                            By.ID,
                            f"options{str(DEDICATED_CATALOG_2_ID)}-trigger-picker"
                        )
            dropdown.click()
            logging.info("Dropdown")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            "xpath",
                            '//span[text()="Choose Action To perform:"]/../../..//input',
                        )
                    )
                )
                .send_keys("Deployment Inventory")
            )
            logging.info("Deployment Inventory")
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            "xpath",
                            '//span[text()="Choose Action To perform:"]/../../..//input',
                        )
                    )
                )
                .send_keys(Keys.DOWN, Keys.DOWN)
            )
            logging.info("Input")
            time.sleep(4)
            ud = driver.find_element(
                By.XPATH, "//li[contains(text(),'Update Deployment Inventory')]"
            ).is_displayed()
            ed = driver.find_element(
                By.XPATH, "//li[contains(text(),'Export Deployment Inventory CSV')]"
            ).is_displayed()
            id = driver.find_element(
                By.XPATH, "//li[contains(text(),'Import Deployment Inventory CSV')]"
            ).is_displayed()
            assert [ud, ed, id]
            logging.info(
                f"Dropdown listing like: Update Price, Update Deployment Inventory, Export Price CSV, "
                f"Import Price CSV, Export Deployment Inventory CSV, Import Deployment Inventory CSV"
            )


            Success_List_Append(
                "test_MKTPL_1342_Mappriceanddeploymentinventory_002",
                "Verify the Listing in Dropdown selection",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_002",
            "Verify the Listing in Dropdown selection",

            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1342_Mappriceanddeploymentinventory_002",
            "Verify the Listing in Dropdown selection",
            e,
        )
        raise e

# def test_MKTPL_1342_Mappriceanddeploymentinventory_002_004_005_006_007(store_Login):
#     RANDOM_CODE = "".join(random.choices(string.digits, k=3))
#     try:
#         with utils.services_context_wrapper_optimized(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_002_004_005_006_007.png"
#         ) as driver:
#             driver.maximize_window()
#             utils.Pac_Credentials.Login_store(driver)
#             # driver.implicitly_wait(10)
#             act_title = driver.find_element(
#                 By.XPATH, xpath.logout
#             ).get_attribute("id")
#             assert act_title == "pimcore_logout"
#             logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
#             utils.wait_for_style_attribute(driver,40)
#             wait = WebDriverWait(driver, 60)
#             utils.search_by_id(driver, DEDICATED_CATALOG_2_ID)
#             wait.until(
#                 EC.visibility_of_element_located(("xpath", xpath.productsTabXpath))
#             ).click()
#             wait.until(
#                 EC.visibility_of_element_located(
#                     (
#                         "xpath",
#                         '//span[text()="Choose Action To perform:"]/../../..//input',
#                     )
#                 )
#             ).send_keys(Keys.ARROW_DOWN)
#             up = driver.find_element(
#                 By.XPATH, "//li[contains(text(),'Update Price')]"
#             ).is_displayed()
#             ep = driver.find_element(
#                 By.XPATH, "//li[contains(text(),'Export Price CSV')]"
#             ).is_displayed()
#             ip = driver.find_element(
#                 By.XPATH, "//li[contains(text(),'Import Price CSV')]"
#             ).is_displayed()
#             ud = driver.find_element(
#                 By.XPATH, "//li[contains(text(),'Update Deployment Inventory')]"
#             ).is_displayed()
#             ed = driver.find_element(
#                 By.XPATH, "//li[contains(text(),'Export Deployment Inventory CSV')]"
#             ).is_displayed()
#             id = driver.find_element(
#                 By.XPATH, "//li[contains(text(),'Import Deployment Inventory CSV')]"
#             ).is_displayed()
#             assert [up, ep, ip, ud, ed, id]
#             logging.info(
#                 f"Dropdown listing like: Update Price, Update Deployment Inventory, Export Price CSV, "
#                 f"Import Price CSV, Export Deployment Inventory CSV, Import Deployment Inventory CSV"
#             )

#             wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '//li[text()="Update Price"]')
#                 )
#             ).click()

#             view_tab = wait.until(
#                 EC.visibility_of_element_located(
#                     (
#                         By.XPATH,
#                         '//span[text()="Add/Update Price For :-  '+DEDICATED_CATALOG_2_NAME+'"]',
#                     )
#                 )
#             ).is_displayed()
#             assert view_tab
#             logging.info(
#                 "User is Redirected to the new window when select Update Price"
#             )

#             name = wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '//span[text()="Product/Variant Name"]')
#                 )
#             ).is_displayed()

#             sku = wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '//span[text()="Product SKU"]')
#                 )
#             ).is_displayed()

#             price = wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '(//span[text()="Price"])[2]')
#                 )
#             ).is_displayed()

#             assert name, sku and price
#             action = ActionChains(driver)

#             edit_name = wait.until(
#                 EC.presence_of_element_located(
#                     (
#                         By.XPATH,
#                         '((//span[text()="Price"])[2]//..//..//..//..//..//..//..//../following-sibling::div)[6]//table//tr//td[1]//div',
#                     )
#                 )
#             )

#             product_name = edit_name.get_attribute("innerText")
#             action.move_to_element(edit_name).click(edit_name).send_keys(
#                 "TestEdit", Keys.ENTER
#             ).perform()
#             driver.implicitly_wait(0)
#             logging.info("Tried to Edit Product Name")
#             edit_sku = wait.until(
#                 EC.presence_of_element_located(
#                     (
#                         By.XPATH,
#                         '((//span[text()="Price"])[2]//..//..//..//..//..//..//..//../following-sibling::div)[6]'
#                         "//table//tr//td[2]//div",
#                     )
#                 )
#             )

#             product_sku = edit_sku.get_attribute("innerText")
#             action.move_to_element(edit_sku).click(edit_sku).send_keys(
#                 "T123", Keys.ENTER
#             ).perform()
#             logging.info("Tried to Edit Product SKU")
#             edit_price = wait.until(
#                 EC.presence_of_element_located(
#                     (
#                         By.XPATH,
#                         '((//span[text()="Price"])[2]//..//..//..//..//..//..//..//../following-sibling::div)[6]//table//tr//td[3]//div',
#                     )
#                 )
#             )
#             old_price = edit_price.get_attribute("innerText")
#             logging.info(old_price)
#             action.move_to_element(edit_price).double_click(edit_price).send_keys(
#                 "", Keys.ENTER
#             ).perform()
#             driver.implicitly_wait(0)
#             action.send_keys(RANDOM_CODE, Keys.ENTER).perform()
#             time.sleep(3)

#             assert product_name != "TestEdit" and product_sku != "T123"
#             assert old_price != RANDOM_CODE
#             driver.implicitly_wait(0)
#             submit = wait.until(
#                 EC.visibility_of_element_located((By.XPATH, '//span[text()="Submit"]'))
#             )
#             submit.click()
#             logging.info("Clicked on Submit")
#             # WebDriverWait(driver, 60).until(
#             #     EC.presence_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))

#             assert (old_price != RANDOM_CODE)

#             wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '//span[text()="'+DEDICATED_CATALOG_2_NAME+'"]')
#                 )
#             ).is_displayed()
#             qty = wait.until(
#                 EC.visibility_of_element_located(
#                     (By.XPATH, '(//div[text()="' + RANDOM_CODE + '"])[1]')
#                 )
#             ).text
#             assert qty == RANDOM_CODE

#             logging.info(
#                 "User is able to Redirect to a new window and Products attributes --"
#                 "Product/Variant Name - READ ONLY, SKU - READ-ONLY, "
#                 "Price - EDITABLE -- Save the changes "
#                 "when select Update Price"
#             )

#             Success_List_Append(
#                 "test_MKTPL_1342_Mappriceanddeploymentinventory_002_004_005_006_007",
#                 "Verify the Listing in Dropdown selection "
#                 "User able to Redirect to a new window and Products attributes - -"
#                 "Product/Variant Name - READ ONLY, SKU - READ-ONLY, "
#                 "Price - EDITABLE -- Save the changes "
#                 "when select Update Price",
#                 "Pass",
#             )

#     except Exception as e:
#         logging.info(f"Error- {e}")
#         Success_List_Append(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_002_004_005_006_007",
#             "Verify the Listing in Dropdown selection "
#             "User able to Redirect to a new window and Products attributes - -"
#             "Product/Variant Name - READ ONLY, SKU - READ-ONLY, "
#             "Price - EDITABLE -- Save the changes "
#             "when select Update Price",
#             "Fail",
#         )
#         Failure_Cause_Append(
#             "test_MKTPL_1342_Mappriceanddeploymentinventory_002_004_005_006_007",
#             "Verify the Listing in Dropdown selection "
#             "User able to Redirect to a new window and Products attributes - -"
#             "Product/Variant Name - READ ONLY, SKU - READ-ONLY, "
#             "Price - EDITABLE -- Save the changes "
#             "when select Update Price",
#             e,
#         )
#         raise e




# def test_MKTPL_3763_Pricezero_001_002_003(store_Login):
#     RANDOM_CODE = "".join(random.choices(string.digits, k=3))
#     ACTUAL_CSV_DATA=[]
#     try:
#         with utils.services_context_wrapper_optimized("test_MKTPL_3763_Pricezero_001_002_003.png") as driver:
#             driver.maximize_window()
#             utils.Pac_Credentials.Login_store(driver)
#             # driver.implicitly_wait(10)
#             act_title = driver.find_element(
#                 By.XPATH, xpath.logout
#             ).get_attribute("id")
#             assert act_title == "pimcore_logout"
#             logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
#             wait = WebDriverWait(driver, 60)
#             utils.update_csv("PriceUpdate.csv", 'Price', RANDOM_CODE)
#             utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_3_ID)
#             wait.until(EC.visibility_of_element_located((By.XPATH, xpath.productsTabXpath))).click()
#             driver.find_element(By.XPATH, "//span[contains(text(),'Choose Action')]//ancestor::label//following-sibling::div//div[contains(@id,'trigger-picker')]").click()
#             deploy = driver.find_element(By.XPATH, "//li[contains(text(),'Update Price')]")
#             action = ActionChains(driver)
#             action.move_to_element(deploy).perform()
#             action.click(deploy).perform()
#             WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Add/Update Price')]")))
#             WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]")))
#             val = driver.find_element(By.XPATH,"(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]").text
#             if val=='0':
#                 logging.info("Already value is zero!")
#                 pass
#             else:
#                 driver.find_element(By.XPATH,"(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]").click()
#                 time.sleep(5)
#                 actions = ActionChains(driver)
#                 actions.key_down(Keys.CONTROL).key_down(Keys.SHIFT).send_keys(Keys.ARROW_LEFT).key_up(Keys.CONTROL).key_up(Keys.SHIFT).perform()
#                 driver.find_element(By.XPATH,"(//div[contains(@id,'celleditor')]//input)[1]").send_keys(Keys.BACKSPACE)
#                 logging.info("Cleared")
#                 time.sleep(5)
#                 driver.find_element(By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]").send_keys("0")
#                 driver.find_element(By.XPATH, "(//div[text()='Products'])[2]").click()
#                 logging.info(f"Add price as 0")
#                 time.sleep(5)
#                 submit = driver.find_element(By.XPATH, "//span[contains(text(),'Submit')]")
#                 driver.execute_script("arguments[0].click();", submit)
#                 logging.info("Clicked on Submit")
#                 WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Sent message to sns successfully')]"))) or WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'No Change in Price')]")))
#                 logging.info(f"Price=0 is allowed from UI in catalog assignment")

#             try:
#                 driver.find_element(
#                     By.XPATH,
#                     "//span[contains(text(),'Log')]//parent::div//div[contains(@class,'expander')]",
#                 ).is_displayed()
#                 driver.find_element(By.XPATH, "//div[contains(text(),'utils.Assets')]").click()
#             except:
#                 pass
#             close_button = driver.find_element(
#                 By.XPATH, "(//span[@class='x-tab-close-btn'])[1]"
#             )
#             actions = ActionChains(driver)
#             actions.context_click(close_button).perform()
#             driver.find_element(By.XPATH, "(//span[contains(text(),'Close All')])[1]").click()
#             logging.info("Closed all tabs")
#             driver.refresh()
#             utils.wait_for_style_attribute(driver,40)
#             utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_3_ID)
#             wait.until(EC.visibility_of_element_located((By.XPATH, xpath.productsTabXpath))).click()
#             dropdown = wait.until(EC.visibility_of_element_located((By.XPATH, '(//span[contains(text(),"Choose")]//..//..//..//div)[4]')))
#             driver.execute_script("arguments[0].click();", dropdown)
#             wait.until(EC.visibility_of_element_located((By.XPATH, '//li[text()="Export Price CSV"]'))).click()
#             time.sleep(5)
#             logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_PRICEUPDATE_SAMPLE)
#             with open(LOCAL_DOWNLOADABLE_FILE_PATH_PRICEUPDATE_SAMPLE, 'r', encoding='utf-8-sig') as file:
#                 csvFile = csv.reader(file)
#                 for lines in csvFile:
#                     logging.info(lines)
#                     ACTUAL_CSV_DATA.append(lines)
#                     break
#                 logging.info("printed")
#                 for row in csvFile:
#                     price = row[3]
#                     logging.info(price)
#                     if not price or price == '0':
#                         logging.info(f"price is zero")
#                         logging.info("Price=0 exported in Export Price CSV in catalog assignment")
#                     else:
#                         logging.info("Price=0 is not exported in Export Price CSV in catalog assignment")
#             os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_PRICEUPDATE_SAMPLE)
#             utils.update_csv("PriceUpdate.csv", 'CatalogAssignmentId', DEDICATED_CATALOG_ASSIGNMENT_3_ID)
#             utils.update_csv("PriceUpdate.csv", 'Price', 0)
#             dropdown = wait.until(EC.visibility_of_element_located((By.XPATH, '(//span[contains(text(),"Choose")]//..//..//..//div)[4]')))
#             driver.execute_script("arguments[0].click();", dropdown)
#             wait.until(EC.visibility_of_element_located((By.XPATH, '//li[text()="Import Price CSV"]'))).click()
#             time.sleep(5)
#             file_path = '//input[@name="pricefile"]'
#             saved_message = utils.upload_csv("PriceUpdate.csv", file_path)
#             assert saved_message
#             wait.until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Close & Reload"]'))).click()
#             updated_price = wait.until((EC.visibility_of_element_located((By.XPATH, '(//div[text()="0"])[1]')))).is_displayed()
#             assert updated_price
#             logging.info("Price=0 allowed from CSV in catalog assignment")
#             driver.find_element(By.XPATH, "//span[contains(text(),'Choose Action')]//ancestor::label//following-sibling::div//div[contains(@id,'trigger-picker')]").click()
#             deploy = driver.find_element(By.XPATH, "//li[contains(text(),'Update Price')]")
#             action = ActionChains(driver)
#             action.move_to_element(deploy).perform()
#             action.click(deploy).perform()
#             WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Add/Update Price')]")))
#             WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, f"(//div[contains(text(),'{DEDICATED_SKU_1_NAME}')]//parent::td)[2]//following-sibling::td")))
#             driver.find_element(By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]").click()
#             time.sleep(5)
#             actions = ActionChains(driver)
#             actions.key_down(Keys.CONTROL).key_down(Keys.SHIFT).send_keys(Keys.ARROW_LEFT).key_up(Keys.CONTROL).key_up(
#                 Keys.SHIFT).perform()
#             driver.find_element(By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]").send_keys(Keys.BACKSPACE)
#             logging.info("Cleared")
#             time.sleep(5)
#             driver.find_element(By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]").send_keys(RANDOM_CODE)
#             driver.find_element(By.XPATH, "(//div[text()='Products'])[2]").click()
#             logging.info(f"Add price as 0")
#             time.sleep(5)
#             submit = driver.find_element(By.XPATH, "//span[contains(text(),'Submit')]")
#             driver.execute_script("arguments[0].click();", submit)
#             logging.info("Clicked on Submit")
#             WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'Sent message to sns successfully')]")))
#             Success_List_Append("test_MKTPL_3763_Pricezero_001_002_003", "Verify if user enters Price=0 in catalog assignment", "Pass")

#     except Exception as e:
#         logging.info(f"Error- {e}")
#         Success_List_Append("test_MKTPL_3763_Pricezero_001_002_003", "Verify if user enters Price=0 in catalog assignment", "Fail")
#         Failure_Cause_Append("test_MKTPL_3763_Pricezero_001_002_003", "Verify if user enters Price=0 in catalog assignment", e)
#         raise e

@jamatest.test(14167695)
def test_MKTPL_3763_Pricezero_006(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3763_Pricezero_006.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_PRODUCT_3_ID)
            wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Pricing & Taxation"]')
                )
            ).click()
            price = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="Price:"]/../../..//input)[1]')
                )
            )
            price.clear()
            time.sleep(2)
            price.send_keys("0")
            special_price = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="Special Price:"]/../../..//input)[1]')
                )
            )
            special_price.clear()
            time.sleep(2)
            special_price.send_keys("0")
            # cost = (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '(//span[text()="Cost:"]/../../..//input)[1]'))))
            # cost.clear()
            # time.sleep(2)
            # cost.send_keys("0")
            saved = utils.save_and_publish(driver)
            assert saved
            price_field = driver.find_element(
                By.XPATH, '(//span[text()="Price:"]/../../..//input)[1]'
            ).get_attribute("aria-valuenow")
            special_price_field = special_price.get_attribute("aria-valuenow")
            # cost_field = cost.get_attribute('aria-valuenow')
            logging.info(price_field)
            assert price_field == "0"
            assert special_price_field == "0"
            # assert cost_field == "0"
            logging.info("Price, special price and cost=0 allowed from UI in product")
            Success_List_Append(
                "test_MKTPL_3763_Pricezero_006",
                "Verify if user enters Price=0 in catalog assignment",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3763_Pricezero_006",
            "Verify if user enters Price=0 in catalog assignment",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3763_Pricezero_006",
            "Verify if user enters Price=0 in catalog assignment",
            e,
        )
        raise e

@jamatest.test(14167696)
def test_MKTPL_3763_Pricezero_007(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3763_Pricezero_007.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            store.send_keys("Products")
            store.send_keys(Keys.ENTER)
            time.sleep(3)
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '//div[@id="categoryField-listWrapper"]')
                )
            )
            li = driver.find_element("xpath", '//div[@id="categoryField-listWrapper"]')
            li.click()
            time.sleep(2)
            category = wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '(//div[@id="categoryField-picker-listWrap"]//li)[1]')
                )
            )
            cat1 = category.get_attribute("innerText")
            logging.info(cat1)
            category.click()
            logging.info("User is able to select categories while import")
            download_button = wait.until(
                EC.visibility_of_element_located((By.ID, xpath.downloadCsvButtonId))
            )
            download_button.click()
            os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE)
            time.sleep(2)
            filelist = []
            last_file_time = 0
            for current_file in os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE):
                filelist.append(current_file)
                current_file_fullpath = os.path.join(
                    LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, current_file
                )
                current_file_time = os.path.getctime(current_file_fullpath)
                if os.path.isfile(current_file_fullpath):
                    if last_file_time == 0:
                        last_file = current_file
                    last_file_time = os.path.getctime(
                        os.path.join(
                            LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file
                        )
                    )
                    if current_file_time > last_file_time and os.path.isfile(
                        current_file_fullpath
                    ):
                        last_file = current_file
                last_file_fullpath = os.path.join(
                    LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file
                )
            logging.info(last_file_fullpath)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(
                last_file_fullpath
            )
            driver.find_element(By.XPATH, xpath.upload).click()
            saved_message = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            xpath.csvUploadMessage,
                        )
                    )
                )
                .is_displayed()
            )

            assert saved_message
            logging.info(
                f"Products should be successfully imported in the system = {saved_message}"
            )
            # driver.refresh()
            # utils.wait_for_style_attribute(driver, 40)
            # time.sleep(3)

            os.remove(last_file_fullpath)
            logging.info(
                "User can select multiple categories while importing CSV for Products."
            )
            Success_List_Append(
                "test_MKTPL_3763_Pricezero_007",
                "Verify multiple category is selected while " "CSV import",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3763_Pricezero_007",
            "Verify multiple category is selected while " "CSV import",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3763_Pricezero_007",
            "Verify multiple category is selected while " "CSV import",
            e,
        )
        raise e

@jamatest.test(11977777)
def test_MKTPL_3036_Associatemultiplecategories_002(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3036_Associatemultiplecategories_002.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.NAME, "importType"))
                )
            store = driver.find_element(By.NAME, "importType")
            redirect = store.is_displayed()
            logging.info(redirect)
            assert redirect
            logging.info("User is navigate to the CSV import screen")
            store.send_keys("Products")
            time.sleep(3)
            store.send_keys(Keys.ENTER)

            wait = WebDriverWait(driver, 60)

            wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '//div[@id="categoryField-listWrapper"]')
                )
            ).click()
            time.sleep(3)
            category = wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '(//div[@id="categoryField-picker-listWrap"]//li)[1]')
                )
            )
            cat1 = category.get_attribute("innerText")
            logging.info(cat1)
            category.click()
            category = wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '(//div[@id="categoryField-picker-listWrap"]//li)[2]')
                )
            )
            cat2 = category.get_attribute("innerText")
            logging.info(cat2)
            category.click()
            time.sleep(2)
            category1 = wait.until(
                EC.visibility_of_element_located(
                    ("xpath", '//div[@id="categoryField-listWrapper"]//span')
                )
            )
            selected_category = category1.get_attribute("innerHTML")
            logging.info("Selected")
            logging.info(selected_category)
            assert cat1 and cat2 in selected_category
            logging.info("User is able to select multiple categories while import")
            download_button = wait.until(
                EC.visibility_of_element_located((By.ID, xpath.downloadCsvButtonId))
            )
            download_button.click()
            os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE)
            time.sleep(2)
            filelist = []
            last_file_time = 0
            for current_file in os.listdir(LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE):
                filelist.append(current_file)
                current_file_fullpath = os.path.join(
                    LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, current_file
                )
                current_file_time = os.path.getctime(current_file_fullpath)
                if os.path.isfile(current_file_fullpath):
                    if last_file_time == 0:
                        last_file = current_file
                    last_file_time = os.path.getctime(
                        os.path.join(
                            LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file
                        )
                    )
                    if current_file_time > last_file_time and os.path.isfile(
                        current_file_fullpath
                    ):
                        last_file = current_file
                last_file_fullpath = os.path.join(
                    LOCAL_DOWNLOADABLE_FILE_PATH_PRODUCT_SAMPLE, last_file
                )
            logging.info(last_file_fullpath)
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(
                last_file_fullpath
            )

            driver.find_element(By.XPATH, xpath.upload).click()
            saved_message = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[contains(text(),"CSV Uploaded Successfully.")]',
                        )
                    )
                )
                .is_displayed()
            )

            assert saved_message
            logging.info(
                f"Products should be successfully imported in the system = {saved_message}"
            )
            # driver.refresh()
            # utils.wait_for_style_attribute(driver, 40)
            # time.sleep(3)

            os.remove(last_file_fullpath)
            logging.info(
                "User can select multiple categories while importing CSV for Products."
            )
            Success_List_Append(
                "test_MKTPL_3036_Associatemultiplecategories_002",
                "Verify multiple category is selected while " "CSV import",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_3036_Associatemultiplecategories_002",
            "Verify multiple category is selected while " "CSV import",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3036_Associatemultiplecategories_002",
            "Verify multiple category is selected while " "CSV import",
            e,
        )
        raise e

def test_MKTPL_2504_Unpublishedproduct_002(store_Login):
    global RANDOM_NAME
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_lowercase, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_2504_Unpublishedproduct_002.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.createNewProduct).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.NAME, "sku"))
            )
            driver.find_element(By.NAME, "sku").send_keys(RANDOM_NAME)
            time.sleep(3)
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "category")))
                .click()
            )
            time.sleep(1)
            driver.find_element(
                By.XPATH,
                '//li[text()="'
                + DEDICATED_CATEGORY_1_NAME
                + " ("
                + DEDICATED_STORE
                + "/"
                + DEDICATED_CATEGORY_1_NAME
                + ')"]',
            ).click()
            driver.find_element(By.XPATH, "//span[contains(text(),'Category')]").click()
            time.sleep(1)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.NAME, "type"))
            )
            driver.find_element(By.NAME, "type").send_keys("Simple")
            driver.find_element(By.XPATH, "//li[contains(text(),'Simple')]").click()
            driver.find_element(By.XPATH, '(//span[text()="OK"])').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveXpath))
            )
            logging.info(f"Successfully shown New Product details screen.")
            driver.find_element(By.NAME, "name").send_keys(RANDOM_NAME)
            driver.find_element(By.NAME, "deliveryType").send_keys("Onboard")
            driver.find_element(By.XPATH, "//li[contains(text(),'Onboard')]").click()
            driver.find_element(By.NAME, "productType").send_keys("Duty Free")
            driver.find_element(By.XPATH, "//li[contains(text(),'Duty Free')]").click()
            driver.find_element(By.XPATH, '//span[text()="Pricing & Taxation"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Price:"]'))
            )
            time.sleep(2)
            driver.find_element(
                By.XPATH, '//input[starts-with(@name,"price_USD")]'
            ).send_keys("1")

            saved = utils.save_and_publish(driver)
            assert saved
            UID = utils.get_uid(driver)
            logging.info(f"Successfully created and saved New Product- {RANDOM_NAME}")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Unpublish"])[1]')
                )
            ).click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="Saved successfully!"]')
                )
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (By.XPATH, '//div[text()="Unpublished Products"]')
                    )
                )
                .click()
            )
            up = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Unpublished Products"]')
                )
            )
            action = ActionChains(driver)
            action.context_click(up).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="Refresh"])[1]')
                )
            )
            time.sleep(1)
            driver.find_element(By.XPATH, '(//span[text()="Refresh"])[1]').click()
            up = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Unpublished Products"]')
                )
            )
            up.click()
            time.sleep(5)
            driver.find_element(By.NAME, "query").send_keys(RANDOM_NAME, Keys.ENTER)
            unpublished_product = (
                WebDriverWait(driver, 60)
                .until(
                    (
                        EC.visibility_of_element_located(
                            (By.XPATH, '(//div[text()="' + RANDOM_NAME + '"])[1]')
                        )
                    )
                )
                .is_displayed()
            )
            assert unpublished_product
            try:
                RT_VALUE = utils.Delete(UID, PRODUCTS_URL, CRED_AUTOMATION_STORE_TOKEN)
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
            logging.info(
                f"Product  move to Unpublished Products panel when product is unpublished for Store Users"
            )
            Success_List_Append(
                "test_MKTPL_2504_Unpublishedproduct_002",
                "Verify when product is unpublished for Store Users",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_2504_Unpublishedproduct_002",
            "Verify when product is unpublished for Store Users",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_2504_Unpublishedproduct_002",
            "Create New product icon and Enter the data in the fields",
            e,
        )
        raise e

# def test_MKTPL_1646_1893_1894_1979_SNS_020_021(store_Login):
#     try:
#         with utils.services_context_wrapper_optimized("test_MKTPL_1646_1893_1894_1979_SNS_020_021.png") as driver:
#             # Login through store user
#             utils.Pac_Credentials.Login_store(driver)
#             driver.implicitly_wait(10)
#             act_title = driver.find_element(
#                 By.XPATH, xpath.logout
#             ).get_attribute("id")
#             assert act_title == "pimcore_logout"
#             logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
#             utils.search_by_id(driver, DEDICATED_CATALOG_ASSIGNMENT_3_ID)

#             WebDriverWait(driver, 60).until((EC.presence_of_element_located((By.XPATH,"//span[text()='Products' and contains(@class,'inner')]"))))
#             products = driver.find_element(By.XPATH,"//span[text()='Products' and contains(@class,'inner')]")
#             driver.execute_script("arguments[0].click();",products)
#             logging.info("Clicked on Products")
#             WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"//span[contains(text(),'Choose Action')]//ancestor::label//following-sibling::div//div[contains(@id,'trigger-picker')]")))
#             driver.find_element(By.XPATH,"//span[contains(text(),'Choose Action')]//ancestor::label//following-sibling::div//div[contains(@id,'trigger-picker')]").click()
#             deploy = driver.find_element(By.XPATH, "//li[contains(text(),'Update Price')]")
#             action = ActionChains(driver)
#             action.move_to_element(deploy).perform()
#             action.click(deploy).perform()
#             WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,"//div[contains(text(),'Add/Update Price')]")))
#             logging.info(("Navigated to Add/Update price page"))
#             WebDriverWait(driver, 60).until(EC.presence_of_element_located(
#                 (By.XPATH, "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]")))
#             random_integer = random.randint(0, 99)
#             driver.find_element(By.XPATH,
#                                 "(//tr[contains(@class,'  x-grid-row')])[last()]//td[3]").click()
#             driver.find_element(By.XPATH,
#                                 "(//div[contains(@id,'celleditor')]//input)[1]").send_keys(Keys.BACKSPACE,
#                                                                                            Keys.BACKSPACE)
#             logging.info("Cleared")
#             time.sleep(3)
#             driver.find_element(By.XPATH, "(//div[contains(@id,'celleditor')]//input)[1]").send_keys(random_integer)
#             driver.find_element(By.XPATH, "(//div[text()='Products'])[2]").click()
#             logging.info(f"Add price as {random_integer}")
#             time.sleep(5)
#             submit = driver.find_element(By.XPATH,"//span[contains(text(),'Submit')]")
#             driver.execute_script("arguments[0].click();",submit)
#             logging.info("Clicked on Submit")
#             WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH,"//div[contains(text(),'Sent message to sns successfully')]")))
#             driver.find_element(By.XPATH,xpath.logout).click()
#             logging.info("Logged out from Store Admin")
#             WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
#             utils.Pac_Credentials.Login_Pac_Admin(driver)
#             utils.Assets(driver)
#             list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'catalog_assignment_publish')]")
#             num = len(list1)
#             time.sleep(5)
#             row = driver.find_element(By.XPATH, f"(//div[contains(text(),'catalog_assignment_publish')])[{num-1}]")
#             actions = ActionChains(driver)
#             actions.context_click(row).perform()
#             open = driver.find_element(By.XPATH, xpath.open)
#             open.click()
#             # Open Code if modal appears
#             try:
#                 if driver.find_element(By.XPATH, xpath.yes).is_displayed()
#                     driver.find_element(By.XPATH, xpath.yes).click()
#             except:
#                 logging.info("Modal is not shown, store not opened on any other device")
#             logging.info("Opened the log file - json")
#             WebDriverWait(driver, 60).until(
#                 EC.presence_of_element_located((By.XPATH, f"//span[contains(text(),'{DEDICATED_CATALOG_ASSIGNMENT_3_ID}')]")))
#             time.sleep(5)
#             x = driver.find_element(By.XPATH,"(//textarea)[1]")
#             x.send_keys(Keys.CONTROL,"f")
#             driver.find_element(By.XPATH,"//input[contains(@placeholder,'Search for')]").send_keys("value")
#             assert (WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,f"//span[contains(text(),'{random_integer}')]"))).is_displayed())
#             logging.info(f"Asserted the quantity as {random_integer}")
#             Success_List_Append("test_MKTPL_1646_1893_1894_1979_SNS_020_021",
#                                 "Verify when Price is created via UI and catalog assignment is published",
#                                 "Pass")
#     except Exception as e:
#         logging.info(f"Error- {e}")
#         Success_List_Append("test_MKTPL_1646_1893_1894_1979_SNS_020_021",
#                             "Verify when Price is created via UI and catalog assignment is published",
#                             "Fail")
#         Failure_Cause_Append("test_MKTPL_1646_1893_1894_1979_SNS_020_021",
#                              "Verify when Price is created via UI and catalog assignment is published", e)
#         raise e

def test_MKTPL_1065_CategorychangesinProduct_007(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1065_CategorychangesinProduct_007.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_SKU_1_ID)
            scroll = wait.until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Brand:"]'))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            time.sleep(1)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//span[text()='Category ']//..//../following-sibling::div//div[contains(@id,'trigger-picker')]",
                    )
                )
            ).click()
            time.sleep(3)
            dropdown = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//ul[@class= "x-list-plain"]')
                )
            ).is_displayed()
            logging.info(dropdown)
            assert dropdown
            categories = wait.until(
                EC.presence_of_all_elements_located(
                    (By.XPATH, '//ul[@class= "x-list-plain"]//li')
                )
            )
            for cat_text in categories:
                cat_text = cat_text.text
                logging.info(cat_text)
                assert (
                    ""
                    + DEDICATED_CATEGORY_1_NAME
                    + " ("
                    + DEDICATED_STORE
                    + "/"
                    + DEDICATED_CATEGORY_1_NAME
                    + ")"
                    or ""
                    + DEDICATED_CATEGORY_4_NAME
                    + " ("
                    + DEDICATED_STORE
                    + "/"
                    + DEDICATED_CATEGORY_4_NAME
                    + ")"
                    or ""
                    + DEDICATED_CATEGORY_2_NAME
                    + " ("
                    + DEDICATED_STORE
                    + "/"
                    + DEDICATED_CATEGORY_3_NAME
                    + "/"
                    + DEDICATED_CATEGORY_2_NAME
                    + ")"
                    in cat_text
                )
            logging.info(
                "Category dropdown should show category based on the store sales strategy"
            )
            Success_List_Append(
                "test_MKTPL_1065_CategorychangesinProduct_007",
                "Verify Category dropdown should show category based on the store sales strategy",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_1065_CategorychangesinProduct_007",
            "Verify Category dropdown should show category based on the store sales strategy",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1065_CategorychangesinProduct_007",
            "Category dropdown should show category based on the store sales strategy",
            e,
        )
        raise e

@jamatest.test(11976146)
def test_MKTPL_320_Catalogexport_001(store_Login):
    # global driver
    ACTUAL_EXCEL_DATA = []
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_320_Catalogexport_001.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_CATALOG_1_ID)
            driver.implicitly_wait(2)
            xls = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//span[text()="Export XLSX"]')
                )
            )
            driver.execute_script("arguments[0].click();", xls)

            logging.info("Verify that User is able to download XLSX file")
            Success_List_Append(
                "test_MKTPL_320_Catalogexport_001",
                "Verify that User is able to download XLSX file",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Failure_Cause_Append(
            "test_MKTPL_320_Catalogexport_001",
            "Verify that User is able to download XLSX file",
            e,
        )
        Success_List_Append(
            "test_MKTPL_320_Catalogexport_001",
            "Verify that User is able to download XLSX file",
            "Fail",
        )
        raise e

def test_MKTPL_3965_categorypath_001_002_003(store_Login):
    try:
        RANDOM_NAME = "From_Automation_" + "".join(
            random.choices(string.ascii_letters, k=7)
        )
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3965_categorypath_001_002_003.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//div[text()="Catalog Management"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                    )
                )
            ).click()
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="Catalog"]/..//div)[2]')
                )
            )
            sector = driver.find_element(
                By.XPATH, '(//span[text()="Catalog"]/..//div)[2]'
            )
            action = ActionChains(driver)
            action.context_click(sector).perform()
            time.sleep(5)
            driver.implicitly_wait(0)
            # WebDriverWait(driver, 60).until(
            #     EC.element_to_be_clickable((By.XPATH, "(//span[text()='Refresh'])"))
            # ).click()
            #
            # wait.until(EC.presence_of_element_located((By.XPATH, '(//span[text()="Catalog"]/..//div)[2]')))
            # sector = driver.find_element(By.XPATH, '(//span[text()="Catalog"]/..//div)[2]')
            # action = ActionChains(driver)
            # action.context_click(sector).perform()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            add_object = driver.find_element(By.XPATH, xpath.addObjectXpath)
            mouse_hover = ActionChains(driver)
            mouse_hover.move_to_element(add_object).perform()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Catalog"])[2]')
                )
            )
            catalog = driver.find_element(By.XPATH, '(//span[text()="Catalog"])[2]')
            catalog.click()
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[text()="Enter the name of the new item"])[1]'
                            "//parent::div//input",
                        )
                    )
                )
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, '//span[text()="OK"]'))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "name")))
                .send_keys(RANDOM_NAME)
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located((By.XPATH, xpath.saveXpath))
                )
                .click()
            )
            (
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '//div[text()="Saved successfully!"]')
                    )
                )
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//span[text()="Products" and starts-with'
                            '(@class,"x-tab-inner x-tab-inner-default")]',
                        )
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search "]',
                        )
                    )
                )
                .click()
            )
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//div[text()="Search Objects"])[1]')
                )
            )
            driver.find_element(By.NAME, "query").send_keys(
                DEDICATED_PRODUCT_2_ID, Keys.ENTER
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//div[contains(text(),'" + DEDICATED_PRODUCT_2_ID + "')]",
                    )
                )
            )
            elm = driver.find_element(
                By.XPATH, "//div[contains(text(),'" + DEDICATED_PRODUCT_2_ID + "')]"
            )
            action = ActionChains(driver)
            action.context_click(elm).perform()
            driver.find_element(By.XPATH, '//span[text()="Add"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Key"]'))
            )
            driver.find_element(By.XPATH, '//span[text()="Select"]').click()
            check_format = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//div[@class="x-grid-cell-inner "])[4]')
                    )
                )
                .text
            )
            logging.info(check_format)
            assert "/" in check_format
            logging.info("catalog successfully binds all the products")
            assert DEDICATED_CATEGORY_1_NAME in check_format
            logging.info(
                "The category path contains key of  categories binded for the product, and they are displayed"
                " accurately in the product Tab."
            )
            assert (
                ""
                + DEDICATED_CATEGORY_1_NAME
                + " ("
                + DEDICATED_STORE
                + "/"
                + DEDICATED_CATEGORY_1_NAME
                + ")"
                in check_format
            )
            logging.info("All components 'Name/Store Name/Key' are displayed in format")

            Success_List_Append(
                "test_MKTPL_3965_categorypath_001_002_003",
                "Verify Category dropdown should show category based on the store sales strategy",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_3965_categorypath_001_002_003",
            "Verify Category dropdown should show category based on the store sales strategy",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3965_categorypath_001_002_003",
            "Category dropdown should show category based on the store sales strategy",
            e,
        )
        raise e

@jamatest.test(11975260)
def test_MKTPL_1105_Perishableproduct_001(store_Login):

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1105_Perishableproduct_001.png", False
        ) as driver:
             
            utils.search_by_id(driver, DEDICATED_PRODUCT_4_ID)
            scroll = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    ("xpath", '//span[text()="Perishable:"]')
                )
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            (
                WebDriverWait(driver, 60)
                .until(EC.presence_of_element_located((By.NAME, "isPerishable")))
                .click()
            )
            driver.find_element(By.XPATH, '//span[text()="Pricing & Taxation"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Price:"]'))
            )
            time.sleep(2)
            driver.find_element(
                By.XPATH, '//input[starts-with(@name,"price_USD")]'
            ).clear()
            time.sleep(1)
            driver.find_element(
                By.XPATH, '//input[starts-with(@name,"price_USD")]'
            ).send_keys("1")
            logging.info(f"Value added into Price and Taxation field.")
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info("Verify User is able to input Lead Time for the product")
            Success_List_Append(
                "test_MKTPL_1105_Perishableproduct_001",
                "Verify in UI flag a store's product if they"
                " are perishable so that the airside can know "
                "if the products can be carried over to the "
                "next flights.",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1105_Perishableproduct_001",
            "Verify in UI flag a store's product if they"
            " are perishable so that the airside can know "
            "if the products can be carried over to the "
            "next flights.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1105_Perishableproduct_001",
            "Verify in UI flag a store's product if they"
            " are perishable so that the airside can know "
            "if the products can be carried over to the "
            "next flights.",
            e,
        )
        raise e

@jamatest.test(11975232)
def test_MKTPL_1103_Leadtimeproduct_011(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_1103_Leadtimeproduct_011.png", False
        ) as driver:
            utils.wait_for_style_attribute(driver, 40)
            wait = WebDriverWait(driver, 60)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id))
            )
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            driver.find_element(By.XPATH, xpath.csvImport).click()
            data = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    ("xpath", '//div[@id="importType-trigger-picker"]')
                )
            )
            data.click()
            import_type = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(("xpath", '//li[text()="Products"]'))
            )
            driver.execute_script("arguments[0].click();", import_type)
            time.sleep(2)
            logging.info("Products entered..")
            data = WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    ("xpath", '//input[@name="category[]"]')
                )
            )
            action = ActionChains(driver)
            action.double_click(data).perform()
            # data = WebDriverWait(driver, 60).until(EC.visibility_of_element_located(
            #     ("xpath", '(//div[@id="categoryField-bodyEl"]//div)[4]')))
            # driver.execute_script("arguments[0].click();", data)
            time.sleep(1)
            category = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '//li[text()="'
                        + DEDICATED_CATEGORY_2_NAME
                        + " ("
                        + DEDICATED_STORE
                        + "/Beverages/"
                        + DEDICATED_CATEGORY_2_NAME
                        + ')"]',
                    )
                )
            )
            driver.execute_script("arguments[0].click();", category)
            time.sleep(2)
            logging.info("category entered..")
            path = os.path.join(os.getcwd() + "/credencys_test_ui/", "Product_Sample.csv")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)
            driver.implicitly_wait(0)
            driver.find_element(By.XPATH, xpath.upload).click()
            saved_message = (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '//div[contains(text(),"CSV Uploaded Successfully.")]',
                        )
                    )
                )
                .is_displayed()
            )
            assert saved_message
            logging.info(
                f"Upload CSV sample file with Next Flight,Gate Pick Up in delivery type and "
                f"add value in Next Flight Lead Time,Gate Pick Up Time = {saved_message}"
            )
            logging.info(
                "Verify that User is able to upload Sample file with Next Flight,"
                "Gate Pick Up in delivery type.."
            )
            Success_List_Append(
                "test_MKTPL_1103_Leadtimeproduct_011",
                "Verify that User is able to upload Sample file with Next Flight,Gate Pick Up in "
                "delivery type",
                "Pass",
            )

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_1103_Leadtimeproduct_011",
            "Verify that User is able to upload "
            "Sample file with Next Flight,Gate Pick Up in delivery type",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_1103_Leadtimeproduct_011",
            "Verify that User is able to upload "
            "Sample file with Next Flight,Gate Pick Up in delivery type",
            e,
        )
        raise e

@jamatest.test(12112459)
def test_MKTPL_3237_Disassociatemultiplecategories_005(store_Login):
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_3237_Disassociatemultiplecategories_005.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_VARIENT_PARENT_PRODUCT_ID)
            driver.find_element(By.XPATH, xpath.variantsPageXpath).click()
            id = driver.find_element(By.XPATH, "((//div[@class= 'x-grid-item-container'])[last()]//tr//td)[2]//div")
            action = ActionChains(driver)
            action.double_click(id).perform()
            wait.until(EC.visibility_of_element_located((By.XPATH, "(//span[contains(@class,'material_icon_rename')])[2]")))
            category = wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "(((//span[text()='Category '])[last()]//ancestor::label//parent::div//div[contains(@class, 'trigger-wrap')]//div)[1]//div)[1]",
                    )
                )
            )
            category_read_only = category.get_attribute("aria-disabled")
            assert category_read_only 
            logging.info(
                "Verify Category dropdown should be read only in Variant Objects"
            )
            Success_List_Append(
                "test_MKTPL_3237_Disassociatemultiplecategories_005",
                "Verify Category dropdown should be read only in Variant Objects",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories_005",
            "Verify Category dropdown should be read only in Variant Objects",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_3237_Disassociatemultiplecategories_005",
            "Verify Category dropdown should be read only in Variant Objects",
            e,
        )
        raise e

@jamatest.test(11976143)
def test_MKTPL_317_DeliveryType_002(store_Login):

    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_317_DeliveryType_002.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_store(driver)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # driver.implicitly_wait(10)
            wait = WebDriverWait(driver, 60)
             
            catalog_management = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '//div[text()="Catalog Management"]/../..//div[@class="x-tool-tool-el x-tool-img x-tool-right "]',
                    )
                )
            )
            utils.search_by_id(driver, DEDICATED_FOLDER_2_ID)
            product = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//tbody//tr[@class="  x-grid-row"]//td)[3]')
                )
            )
            action = ActionChains(driver)
            action.double_click(product).perform()
            try:
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            "xpath",
                            '(//div[text()="The desired element is currently opened by another person:"])[1]',
                        )
                    )
                )
                driver.find_element("xpath", '//span[text()="Yes"]').click()
            except:
                pass
            scroll = wait.until(
                EC.presence_of_element_located(("xpath", xpath.sku))
            )
            driver.execute_script("arguments[0].scrollIntoView();", scroll)
            driver.implicitly_wait(0)
            delivery_method = wait.until(
                EC.presence_of_element_located(
                    (
                        "xpath",
                        '(//div[@class="x-tagfield x-form-field x-form-text x-form-text-default"]//span[@class="x-hidden-clip"])[2]',
                    )
                )
            )
            logging.info(delivery_method.text)
            assert delivery_method.is_displayed()
            logging.info(
                "User is able to view Fulfillment(Delivery) Methods of the Product"
            )
            Success_List_Append(
                "test_MKTPL_317_DeliveryType_002",
                "Verify User is able to view Fulfillment Methods " "of the Product",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_317_DeliveryType_002",
            "Verify User is able to view Fulfillment Methods " "of the Product",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_317_DeliveryType_002",
            "Verify User is able to view Fulfillment Methods " "of the Product",
            e,
        )
        raise e

@jamatest.test(11977273)
def test_MKTPL_938_CRUDcategory_003(store_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_938_CRUDcategory_003.png", False
        ) as driver:
             
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="Categories"]'))
            )
            driver.find_element(By.XPATH, '//div[text()="Categories"]').click()
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="' + DEDICATED_STORE + '"])[last()]')
                )
            )
            STORE_NAME = driver.find_element(
                By.XPATH, '(//span[text()="' + DEDICATED_STORE + '"])[last()]'
            )
            action = ActionChains(driver)
            action.context_click(STORE_NAME).perform()
            # driver.find_element(By.XPATH, xpath.refresh).click()
            # time.sleep(1)
            # FLIPKART_NAME = driver.find_element(
            #     By.XPATH, '(//span[text()="' + DEDICATED_STORE + '"])[1]'
            # )
            # action = ActionChains(driver)
            # action.context_click(FLIPKART_NAME).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.addObjectXpath))
            )
            time.sleep(1)
            ADD_OBJECT = driver.find_element(By.XPATH, xpath.addObjectXpath)
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            time.sleep(5)
            driver.find_element(
                By.XPATH,
                '//span[text()="Category" and starts-with(@class,"x-menu-item-text x-menu-item-text-default x-menu-item-indent-no-separator")]',
            ).click()
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                RANDOM_NAME
            )
            driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveXpath))
            )
            driver.find_element(By.NAME, "name").send_keys(RANDOM_NAME)
            time.sleep(5)
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(f"Category Created: {RANDOM_NAME}")

            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()= "Categories"]')
                )
            )
            driver.find_element(By.XPATH, '//div[text()= "Categories"]').click()
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="' + DEDICATED_STORE + '"])[last()]')
                )
            ).click()
            utils.wait_for_style_attribute(driver, 40)

            driver.find_element(By.NAME, "query").send_keys(RANDOM_NAME, Keys.ENTER)
            driver.find_element(By.NAME, "query").click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[contains(text(),"' + RANDOM_NAME + '")])[last()]',
                    )
                )
            )
            Success_List_Append(
                "test_MKTPL_938_CRUDcategory_003",
                "Create the Category and Enter/update the data",
                "Pass",
            )
            logging.info(f"Successfully added {RANDOM_NAME} under Category folder. ")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_938_CRUDcategory_003",
            "Create the Category and Enter/update the data",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_938_CRUDcategory_003",
            "Create the Category and Enter/update the data",
            e,
        )
        raise e

@jamatest.test(11976217)
def test_MKTPL_333_CRUDCatalog_003(store_Login):
    RANDOM_NAME = "From_Automation_" + "".join(
        random.choices(string.ascii_letters, k=7)
    )
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_333_CRUDCatalog_003.png", False
        ) as driver:
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_store(driver)
            # # driver.implicitly_wait(10)
            # act_title = driver.find_element(By.XPATH, xpath.logout).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
             
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()= "Catalog Management"]')
                )
            )
            driver.find_element(By.XPATH, '//div[text()= "Catalog Management"]').click()
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (By.XPATH, '(//span[text()="' + DEDICATED_STORE + '"])[2]')
                )
            )
            FLIPKART_NAME = driver.find_element(
                By.XPATH, '(//span[text()="' + DEDICATED_STORE + '"])[2]'
            )
            action = ActionChains(driver)
            action.context_click(FLIPKART_NAME).perform()
            driver.find_element(By.XPATH, xpath.refresh).click()
            time.sleep(1)
            FLIPKART_NAME = driver.find_element(
                By.XPATH, '(//span[text()="' + DEDICATED_STORE + '"])[2]'
            )
            action = ActionChains(driver)
            action.context_click(FLIPKART_NAME).perform()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[text()="Add Object"])[2]')
                )
            )
            time.sleep(1)
            ADD_OBJECT = driver.find_element(
                By.XPATH, '(//span[text()="Add Object"])[2]'
            )
            action = ActionChains(driver)
            action.move_to_element(ADD_OBJECT).perform()
            time.sleep(1)
            driver.find_element(
                By.XPATH,
                '//span[text()="Catalog" and starts-with(@class,"x-menu-item-text x-menu-item-text-default")]',
            ).click()
            driver.find_element(By.NAME, "messagebox-1001-textfield-inputEl").send_keys(
                RANDOM_NAME
            )
            driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.saveXpath))
            )
            try:
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '(//div[@class = "x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default"])[1]',
                        )
                    )
                )
                UID = driver.find_element(
                    By.XPATH,
                    '(//div[@class = "x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default"])[1]',
                ).text
                logging.info(f"Unique ID: {UID}")
            except Exception as e:
                UID = None
                pass
            driver.find_element(By.NAME, "name").send_keys(RANDOM_NAME)
            driver.find_element(By.XPATH, xpath.saveXpath).click()
            # WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//span[(text()="Save & Publish")]')))
            logging.info(f"Catalog Created: {RANDOM_NAME}")
            time.sleep(3)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '//span[text()="Products" and starts-with(@class,"x-tab-inner x-tab-inner-default")]',
                    )
                )
            )
            PRODUCTS = driver.find_element(
                By.XPATH,
                '//span[text()="Products" and starts-with(@class,"x-tab-inner x-tab-inner-default")]',
            )
            PRODUCTS.click()
            time.sleep(2)
            WebDriverWait(driver, 60).until(
                EC.element_to_be_clickable(
                    (
                        By.XPATH,
                        '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search "]',
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                '//span[@class="x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search "]',
            ).click()
            # DEDICATED_SKU_1_ID = str(DEDICATED_SKU_1_ID)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//div[text()="Search Objects"])[1]')
                )
            )
            driver.find_element(By.NAME, "query").send_keys(
                DEDICATED_SKU_1_ID, Keys.ENTER
            )
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        '(//div[text()="' + str(DEDICATED_SKU_1_ID) + '"])[last()]',
                    )
                )
            )
            elm = driver.find_element(
                By.XPATH, '(//div[text()="' + str(DEDICATED_SKU_1_ID) + '"])[last()]'
            )
            # elm = driver.find_element(By.XPATH, '(//div[@class="x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-unselectable x-scroller"]//div//table[1]//tr//td)[1]')
            action = ActionChains(driver)
            action.double_click(elm).perform()
            # driver.find_element(By.XPATH, '//span[text()="Add"]').click()
            driver.find_element(By.XPATH, '//span[text()="Select"]').click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Save & Publish"]'))
            )
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Unpublish"]'))
            )
            driver.refresh()
            utils.wait_for_style_attribute(driver, 40)
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()= "Catalog Management"]')
                )
            )
            driver.find_element(By.XPATH, '//div[text()= "Catalog Management"]').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '(//div[@class = "x-tool-tool-el x-tool-img x-tool-right "])[2]',
                    )
                )
            )
            driver.find_element(
                By.XPATH,
                '(//div[@class = "x-tool-tool-el x-tool-img x-tool-right "])[2]',
            ).click()
            time.sleep(2)
            CATALOG = driver.find_element(By.XPATH, '//span[text()="Catalog"]')
            action = ActionChains(driver)
            action.double_click(CATALOG).perform()
            driver.find_element(By.NAME, "query").send_keys(RANDOM_NAME, Keys.ENTER)
            # driver.find_element(By.NAME, 'query').click()
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//div[text()="' + RANDOM_NAME + '"]')
                )
            )
            Success_List_Append(
                "test_MKTPL_333_CRUDCatalog_003",
                "Create the catalog and Enter/update the data",
                "Pass",
            )
            logging.info(f"Successfully added {RANDOM_NAME} under Catalog folder. ")
            logging.info(f"Start for cleaning up the catalog.")

            try:
                RT_VALUE = utils.Delete(
                    UID, CRED_AUTOMATION_STORE_CATALOG_URL, CRED_AUTOMATION_STORE_TOKEN
                )
                assert RT_VALUE == 200, RT_VALUE
                logging.info(f"Data cleaned up successfully.")
            except Exception as e:
                logging.info(f"Error in cleaning up the data.")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_333_CRUDCatalog_003",
            "Create the catalog and Enter/update the data",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_333_CRUDCatalog_003",
            "Create the catalog and Enter/update the data",
            e,
        )
        raise e

@jamatest.test(11976554)
def test_MKTPL_488_Taxpercentage_001(store_Login):
    RANDOM_CODE = "".join(random.choices(string.digits, k=1))
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_488_Taxpercentage_001.png", False
        ) as driver:
             
            wait = WebDriverWait(driver, 60)
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, xpath.configuration_tab)
                )
            ).click()
            wait.until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '((//span[text()="Tax Percentage:"]//..//..//../following-sibling::div)[1]//div)[4]',
                    )
                )
            ).click()
            percentage = driver.find_element(By.XPATH, '//input[@name="taxPercentage"]')
            percentage.clear()
            percentage.send_keys(RANDOM_CODE)
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"user is able to enter tax percentage and then able to save & publish. = {saved}"
            )
            utils.retryForProperLoading(driver, os.environ.get("MAX_RETRIES", 7))
            Success_List_Append(
                "test_MKTPL_488_Taxpercentage_001",
                "Verify user should able to enter tax percentage and then able to save & publish.",
                "Pass",
            )
    except Exception as e:
        Success_List_Append(
            "test_MKTPL_488_Taxpercentage_001",
            "Verify user should able to enter tax percentage and then able to save & publish.",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_488_Taxpercentage_001",
            "Verify user should able to enter tax percentage and then able to save & publish.",
            e,
        )
        raise e

@jamatest.test(11976124)
@jamatest.test(11976128)
def test_MKTPL_315_Orderthresholdlimits_001_005(store_Login):
    random_num = "".join([str(randint(0, 9)) for i in range(1)])
    try:
        with utils.services_context_wrapper_optimized(
            False, "test_MKTPL_315_Orderthresholdlimits_001_005.png", True
        ) as driver:
             
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (By.XPATH, xpath.configuration_tab)
                    )
                )
                .click()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//span[text()="Order Threshold Value:"]/../../..//input)[1]',
                        )
                    )
                )
                .clear()
            )
            (
                WebDriverWait(driver, 60)
                .until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            '(//span[text()="Order Threshold Value:"]/../../..//input)[1]',
                        )
                    )
                )
                .send_keys(random_num)
            )
            saved = utils.save_and_publish(driver)
            assert saved
            logging.info(
                f"Order Threshold value with currency type successfully created/updated in the store"
            )
            # GET
            payload = {}
            headers = {
                "Authorization": CRED_AUTOMATION_STORE_TOKEN,
                "Content-Type": "application/json",
            }
            response = requests.request(
                "GET", STORE_GET_URL, headers=headers, data=payload
            )
            data = response.json()
            assert response.status_code == 200 or response.status_code == 201
            # Extract data
            store_data = data.get("data", {}).get("store", {})
            logging.info(data["data"]["store"]["orderThresholdValue"])
            assert "orderThresholdValue" in store_data
            assert data["data"]["store"]["orderThresholdValue"] == random_num + " USD"
            logging.info(
                f"API  respond with 200 status code and it display following parameters in the"
                f" response:"
                f"- Order Threshold value with currency type"
            )
            utils.retryForProperLoading(driver, os.environ.get("MAX_RETRIES", 7))
            Success_List_Append(
                "test_MKTPL_315_Orderthresholdlimits_001_005",
                "Verify by entering/updating data in below "
                "fields and Verify the newly added parameter in the response",
                "Pass",
            )
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_315_Orderthresholdlimits_001_005",
            "Verify by entering/updating data in below "
            "fields and Verify the newly added parameter in"
            " the response",
            "Fail",
        )
        Failure_Cause_Append(
            "test_MKTPL_315_Orderthresholdlimits_001_005",
            "Verify by entering/updating data in below "
            "fields and Verify the newly added parameter "
            "in the response",
            e,
        )
        raise e
