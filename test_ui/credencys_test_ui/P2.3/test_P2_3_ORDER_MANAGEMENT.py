from random import randint
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import credencys_test_ui.credencys_project_configs.utils as utils
from selenium.webdriver.support.wait import WebDriverWait
import logging, os, csv, requests, json, string, random, time
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

def test_MKTPL_1265_OrderAPI_001():
    RANDOM_NAME = "(From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    RANDOM_NUMBER = ("".join([str(randint(0, 9)) for i in range(7)]))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1265_OrderAPI_001.png"
        ) as driver:
            # 001.
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.222", "33.444", "21.343", "200.332",
                                                                              "67.555", "12.123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]
            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            logging.info(externalId)
            # -------
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
             send_keys(externalId, Keys.ENTER))
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located(
                 (By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            orderId = driver.find_element(By.XPATH,
                                          '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            logging.info(orderId)
            logging.info("001. POST - Pimcore Groundtool should be able to consume the order payload sent from the"
                         " airside and save the orders in the ground tool so that the airlines, stores and PAC admins"
                         " can view them in the order grids")
            driver.find_element(By.XPATH, xpath.logout).click()
            Success_List_Append("test_MKTPL_1265_OrderAPI_001",
                                "Verify the order payload sent by the airside side.", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1265_OrderAPI_001",
                            "Verify the order payload sent by the airside side.", "Fail",)
        Failure_Cause_Append("test_MKTPL_1265_OrderAPI_001",
                             "Verify the order payload sent by the airside side.", e,)
        raise e

def test_MKTPL_1600_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013():
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_1600_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # 005. Check that view item Details
            utils.manageshipping(driver, "yes")
            # 002 GET ALL
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }
            response = requests.request("GET", GET_ORDER_URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info("Get All order by Store with success status")

            # 001 GET BY ID
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }
            response = requests.request("GET", GET_ORDER_URL, headers=headers,
                                        data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            # 005 
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN
            }
            response = requests.request("GET", GET_ORDER_URL+str(DEDICATED_ORDER_1_ID), headers=headers,
                                        data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info("Stores and airlines should be able to fetch order using these APIs")

            # 003 006 GET BY ID
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }
            response = requests.request("GET", GET_ORDER_URL + str(DEDICATED_ORDER_1_ID), headers=headers,
                                        data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Demo Test" in data["data"]["orders"]["orderKey"]
            logging.info("Asserted orderKey")
            assert "Automation_Order_1" in data["data"]["orders"]["externalId"]
            logging.info("Asserted externalId")
            assert "3328 improvement current flight shipment" in data["data"]["orders"]["basketKey"]
            logging.info("Asserted basketKey")
            assert "3328 improvement 1234bcc10" in data["data"]["orders"]["orderProvider"]
            logging.info("Asserted orderProvider")
            assert "Processing" in data["data"]["orders"]["orderState"] or "Completed" in data["data"]["orders"]["orderState"]
            logging.info("Asserted orderState")
            assert "INTERNAL" in data["data"]["orders"]["payment"][0]["paymentService"]
            logging.info("Asserted paymentService")
            assert "Outside demo" in data["data"]["orders"]["seatInfo"]["seatClass"]
            logging.info("Asserted seatClass")
            assert "Outside demo" in data["data"]["orders"]["seatInfo"]["seatNumber"]
            logging.info("Get All by ID order by Store with success status")
            logging.info("All get data verify with UI")

            #004 GET ALL
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN
            }
            response = requests.request("GET", GET_ORDER_URL, headers=headers,
                                        data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201

            logging.info("Stores and airlines should be able to fetch order using these APIs")

            # 011 PUT
            payload = json.dumps({
                "orderItemId": [
                    int(DEDICATED_ORDER_1_LINE_ITEM_ID)
                ],
                "status": "PENDING"
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PUT", ORDER_LINE_ITEM_STATUS +str(DEDICATED_ORDER_1_ID), headers=headers,
                                        data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            logging.info('"Status" and "Lineitem ID" Pass in Payload')

            # 007 PUT
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            payload = json.dumps({
                "orderItemId": [
                    DEDICATED_ORDER_1_LINE_ITEM_ID
                ],
                "status": "FAILED"
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PUT", ORDER_LINE_ITEM_STATUS + RANDOM_NUMBER,
                                        headers=headers,
                                        data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Order not Found." in data["message"]
            logging.info('Validation like "Order/line items not found" when pass wrong ID')

            # 008 PUT
            payload = json.dumps({
                "orderItemId": [
                    DEDICATED_ORDER_1_LINE_ITEM_ID
                ],
                "status": "FAILED"
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PUT", ORDER_LINE_ITEM_STATUS +str(DEDICATED_ORDER_1_ID), headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 400 or response.status_code == 401
            assert "Invalid status" in data["message"]
            logging.info('When status not matched showing message "Invalid status"')

            # 009 PUT 010 by store
            payload = json.dumps({
                "orderItemId": [int(DEDICATED_ORDER_1_LINE_ITEM_ID)],
                "status": "PENDING"
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PUT", ORDER_LINE_ITEM_STATUS+ str(DEDICATED_ORDER_1_ID),
                                        headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Status update successfully" in data["message"]
            logging.info('Success status message showing "Status update successfully"')
            # 012 013
            payload = json.dumps({
                "trackingId": "12345678",
                "trackingLink": "BBB",
                "inventoryLocation": ""
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PUT", UPDATE_SHIPMENT + str(DEDICATED_ORDER_1_ORDER_SHIPMENT_ID),
                                        headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            assert "Shipping details updated successfully" in data["message"]

            logging.info(' 013 "Tracking ID","Tracking Link" and "Inventory Location" fields are in payload')
            Success_List_Append("test_MKTPL_1600_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013",
                            "Verify the active catalog assignments associated", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1600_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_1600_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_563_Order_001_003():
    RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_563_Order_001_003.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            orderInformation = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.orderInformationXpath))).is_displayed())
            customerInformation = driver.find_element(By.XPATH, xpath.customerInformationXpath).is_displayed()
            paymentInformation = driver.find_element(By.XPATH, xpath.paymentInformationXpath).is_displayed()
            orderShipment = driver.find_element(By.XPATH, xpath.orderShipmentDivXpath).is_displayed()
            orderSummary = driver.find_element(By.XPATH, xpath.orderSummaryXpath).is_displayed()
            itinerary = driver.find_element(By.XPATH, xpath.itineraryXpath).is_displayed()
            flightInformationXpath = driver.find_element(By.XPATH, xpath.flightInformationXpath).is_displayed()
            assert [orderInformation, customerInformation, paymentInformation, orderSummary, orderShipment,
                         itinerary, flightInformationXpath]
            # User Info and Fulfilment is not there so didn't assert - 001
            logging.info("001. Admin should be able to view order object and see all the order information :"
                         "Following information should be in read only mode:"
                         "Order Information, Fulfillment, Customer Information, Payment, Order Shipment, Order Summary,"
                         "Itinerary, Flight Information, User Info")
            # 002. Admin should be able to view fulfillment object and see all the fulfillment information in read only
            # mode - Fulfillment is not there
            # 003. Verify by clicking on the shipment
            utils.search_by_id(driver, DEDICATED_ORDER_1_ORDER_SHIPMENT_ID)
            shipmentID = driver.find_element(By.XPATH, xpath.shipmentIdXpath).is_displayed()
            shipmentMethod = driver.find_element(By.XPATH, xpath.shipmentMethodXpath).is_displayed()
            shipmentMode = driver.find_element(By.XPATH, xpath.shipmentModeXpath).is_displayed()
            oli = driver.find_element(By.XPATH, xpath.orderLineItemXpath).is_displayed()
            oliId = driver.find_element(By.XPATH,
                                        '//span[text()="ID"]').is_displayed()
            deliveryInformatio = driver.find_element(By.XPATH, xpath.deliveryInformatioXpath).is_displayed()
            trackingId = driver.find_element(By.XPATH, xpath.trackingIdXpath).is_displayed()
            trackingCarrier = driver.find_element(By.XPATH, xpath.trackingCarrierXpath).is_displayed()
            trackingLink = driver.find_element(By.XPATH, xpath.trackingLinkXpath).is_displayed()
            inventoryLocation = driver.find_element(By.XPATH, xpath.inventoryLocationXpath).is_displayed()
            rateShipment = driver.find_element(By.XPATH, '//div[text()="Base Data"]/../..//span[contains(text(),"Rate")]').is_displayed()
            taxShipment = driver.find_element(By.XPATH, '//div[text()="Base Data"]/../..//span[contains(text(),"Tax")]').is_displayed()
            assert [shipmentID, shipmentMethod, shipmentMode, oli, oliId, trackingId, trackingCarrier, trackingLink,
                    inventoryLocation, rateShipment, taxShipment, deliveryInformatio]
            logging.info('003. Following information should be in read only mode:'
                         'Shipping ID, Shipment Mode, Order, Order Line Item, Shipment Method'
                         'Following information should be editable:'
                         'Tracking Carrier, Delivery Information, Tracking ID, Tracking Link, Inventory Location, '
                         'Rate, Tax')
            Success_List_Append("test_MKTPL_563_Order_001_003",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_563_Order_001_003",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_563_Order_001_003",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_515_Order_001_003():
    RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_515_Order_001_003.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            orderInformation = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.orderInformationXpath))).is_displayed())
            customerInformation = driver.find_element(By.XPATH, xpath.customerInformationXpath).is_displayed()
            paymentInformation = driver.find_element(By.XPATH, xpath.paymentInformationXpath).is_displayed()
            orderShipment = driver.find_element(By.XPATH, xpath.orderShipmentDivXpath).is_displayed()
            orderSummary = driver.find_element(By.XPATH, xpath.orderSummaryXpath).is_displayed()
            itinerary = driver.find_element(By.XPATH, xpath.itineraryXpath).is_displayed()
            flightInformationXpath = driver.find_element(By.XPATH, xpath.flightInformationXpath).is_displayed()
            assert [orderInformation, customerInformation, paymentInformation, orderSummary, orderShipment,
                         itinerary, flightInformationXpath]
            # User Info and Fulfilment is not there so didn't assert - 001
            logging.info("001. Admin should be able to view order object and see all the order information :"
                         "Following information should be in read only mode:"
                         "Order Information, Fulfillment, Customer Information, Payment, Order Shipment, Order Summary,"
                         "Itinerary, Flight Information, User Info")
            # 002. Admin should be able to view fulfillment object and see all the fulfillment information in read only
            # mode - Fulfillment is not there
            # 003. Verify by clicking on the shipment
            utils.search_by_id(driver, DEDICATED_ORDER_1_ORDER_SHIPMENT_ID)
            shipmentID = driver.find_element(By.XPATH, xpath.shipmentIdXpath).is_displayed()
            shipmentMethod = driver.find_element(By.XPATH, xpath.shipmentMethodXpath).is_displayed()
            shipmentMode = driver.find_element(By.XPATH, xpath.shipmentModeXpath).is_displayed()
            oli = driver.find_element(By.XPATH, xpath.orderLineItemXpath).is_displayed()
            oliId = driver.find_element(By.XPATH,
                                        xpath.orderLineItemXpath + '/../../../../..//span[text()="ID"]').is_displayed()
            deliveryInformatio = driver.find_element(By.XPATH, xpath.deliveryInformatioXpath).is_displayed()
            trackingId = driver.find_element(By.XPATH, xpath.trackingIdXpath).is_displayed()
            trackingCarrier = driver.find_element(By.XPATH, xpath.trackingCarrierXpath).is_displayed()
            trackingLink = driver.find_element(By.XPATH, xpath.trackingLinkXpath).is_displayed()
            inventoryLocation = driver.find_element(By.XPATH, xpath.inventoryLocationXpath).is_displayed()
            rateShipment = driver.find_element(By.XPATH, '//div[text()="Base Data"]/../..//span[contains(text(),"Rate")]').is_displayed()
            taxShipment = driver.find_element(By.XPATH, '//div[text()="Base Data"]/../..//span[contains(text(),"Tax")]').is_displayed()
            assert [shipmentID, shipmentMethod, shipmentMode, oli, oliId, trackingId, trackingCarrier, trackingLink,
                    inventoryLocation, rateShipment, taxShipment, deliveryInformatio]
            logging.info('003. Following information should be in read only mode:'
                         'Shipping ID, Shipment Mode, Order, Order Line Item, Shipment Method'
                         'Following information should be editable:'
                         'Tracking Carrier, Delivery Information, Tracking ID, Tracking Link, Inventory Location, '
                         'Rate, Tax')
            Success_List_Append("test_MKTPL_515_Order_001_003",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_515_Order_001_003",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_515_Order_001_003",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_330_Order_001_003():
    RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_330_Order_001_003.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            orderInformation = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.orderInformationXpath))).is_displayed())
            customerInformation = driver.find_element(By.XPATH, xpath.customerInformationXpath).is_displayed()
            paymentInformation = driver.find_element(By.XPATH, xpath.paymentInformationXpath).is_displayed()
            orderShipment = driver.find_element(By.XPATH, xpath.orderShipmentDivXpath).is_displayed()
            orderSummary = driver.find_element(By.XPATH, xpath.orderSummaryXpath).is_displayed()
            itinerary = driver.find_element(By.XPATH, xpath.itineraryXpath).is_displayed()
            flightInformationXpath = driver.find_element(By.XPATH, xpath.flightInformationXpath).is_displayed()
            assert [orderInformation, customerInformation, paymentInformation, orderSummary, orderShipment,
                         itinerary, flightInformationXpath]
            # User Info and Fulfilment is not there so didn't assert - 001
            logging.info("001. Admin should be able to view order object and see all the order information :"
                         "Following information should be in read only mode:"
                         "Order Information, Fulfillment, Customer Information, Payment, Order Shipment, Order Summary,"
                         "Itinerary, Flight Information, User Info")
            # 002. Admin should be able to view fulfillment object and see all the fulfillment information in read only
            # mode - Fulfillment is not there
            # 003. Verify by clicking on the shipment
            utils.search_by_id(driver, DEDICATED_ORDER_1_ORDER_SHIPMENT_ID)
            shipmentID = driver.find_element(By.XPATH, xpath.shipmentIdXpath).is_displayed()
            shipmentMethod = driver.find_element(By.XPATH, xpath.shipmentMethodXpath).is_displayed()
            shipmentMode = driver.find_element(By.XPATH, xpath.shipmentModeXpath).is_displayed()
            oli = driver.find_element(By.XPATH, xpath.orderLineItemXpath).is_displayed()
            oliId = driver.find_element(By.XPATH,
                                        xpath.orderLineItemXpath + '/../../../../..//span[text()="ID"]').is_displayed()
            deliveryInformatio = driver.find_element(By.XPATH, xpath.deliveryInformatioXpath).is_displayed()
            trackingId = driver.find_element(By.XPATH, '//span[text()="Tracking ID "]').is_displayed()
            trackingCarrier = driver.find_element(By.XPATH, xpath.trackingCarrierXpath).is_displayed()
            trackingLink = driver.find_element(By.XPATH, xpath.trackingLinkXpath).is_displayed()
            inventoryLocation = driver.find_element(By.XPATH, xpath.inventoryLocationXpath).is_displayed()
            rateShipment = driver.find_element(By.XPATH, '//div[text()="Base Data"]/../..//span[contains(text(),"Rate")]').is_displayed()
            taxShipment = driver.find_element(By.XPATH, '//div[text()="Base Data"]/../..//span[contains(text(),"Tax")]').is_displayed()
            assert [shipmentID, shipmentMethod, shipmentMode, oli, oliId, trackingId, trackingCarrier, trackingLink,
                    inventoryLocation, rateShipment, taxShipment, deliveryInformatio]
            logging.info('003. Following information should be in read only mode:'
                         'Shipping ID, Shipment Mode, Order, Order Line Item, Shipment Method'
                         'Following information should be editable:'
                         'Tracking Carrier, Delivery Information, Tracking ID, Tracking Link, Inventory Location, '
                         'Rate, Tax')
            Success_List_Append("test_MKTPL_330_Order_001_003",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_330_Order_001_003",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_330_Order_001_003",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_562_OrderGrid_001_002_003():
    RANDOM_NAME = "Frm_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_562_OrderGrid_001_002_003.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Airline(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            orderKey = driver.find_element(By.XPATH, xpath.orderKeyXpath).is_displayed()
            externalId = driver.find_element(By.XPATH, xpath.externalIdXpath).is_displayed()
            orderProvider = driver.find_element(By.XPATH, xpath.orderProviderXpath).is_displayed()
            orderState = driver.find_element(By.XPATH, xpath.orderStateXpath).is_displayed()
            orderDate = driver.find_element(By.XPATH, xpath.orderDateXpath).is_displayed()
            airline = driver.find_element(By.XPATH, '//div[text()="Flight Information"]/../..//span[text()="Airline "]').is_displayed()
            flight = driver.find_element(By.XPATH, '//div[text()="Flight Information"]/../..//span[contains(text(),"Flight")]').is_displayed()
            netTotal = driver.find_element(By.XPATH, '//div[text()="Order Summary"]/../..//span[text()="Net Total "]').is_displayed()
            assert [orderKey,orderDate, orderState, orderProvider, externalId, airline, flight, netTotal]
            logging.info('001. "Following fields should be visible to admin in grid: Order Key, Order Provider, '
                         'Order State, Order Date, Customer Firstname and Lastname, Airline, Flight, Net Total, Store, '
                         'Shipping method or speed for Product, Payment Status, Order Status Last Updated, '
                         'Number of days since Order Placement, Order Status Last Updated Date"')
            # 002. Verify by clicking on the CSV Export button
            # click at Children Grid
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrderItem',Keys.TAB))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="CSV Export"]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="CSV Export"]/../../../../..//span[text()="OK"]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.homeDeliveryXpath + '[2]'))))
            homeDelivery = driver.find_element(By.XPATH, xpath.homeDeliveryXpath + '[2]')
            actions = ActionChains(driver)
            actions.context_click(homeDelivery).perform()
            open1 = driver.find_element(By.XPATH, xpath.openXpathContains)
            open1.click()
            utils.click_on_yes_no(driver)
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, xpath.generalInformationXpath))).is_displayed())
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_EXPORT)
            logging.info(' 002. Admin should be able to export CSV of order information which is present in grid')
            logging.info(' 003. Admin should be able to navigate to the order detail page from the grid')
            Success_List_Append("test_MKTPL_562_OrderGrid_001_002_003",
                                "Verify by clicking on the order folder", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_562_OrderGrid_001_002_003",
                            "Verify by clicking on the order folder", "Fail")
        Failure_Cause_Append("test_MKTPL_562_OrderGrid_001_002_003",
                             "Verify by clicking on the order folder", e)
        raise e

def test_MKTPL_514_OrderGrid_001_002_003():
    RANDOM_NAME = "Frm_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_514_OrderGrid_001_002_003.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            orderKey = driver.find_element(By.XPATH, xpath.orderKeyXpath).is_displayed()
            externalId = driver.find_element(By.XPATH, xpath.externalIdXpath).is_displayed()
            orderProvider = driver.find_element(By.XPATH, xpath.orderProviderXpath).is_displayed()
            orderState = driver.find_element(By.XPATH, xpath.orderStateXpath).is_displayed()
            orderDate = driver.find_element(By.XPATH, xpath.orderDateXpath).is_displayed()
            airline = driver.find_element(By.XPATH, '//div[text()="Flight Information"]/../..//span[text()="Airline "]').is_displayed()
            flight = driver.find_element(By.XPATH, '//div[text()="Flight Information"]/../..//span[contains(text(),"Flight")]').is_displayed()
            netTotal = driver.find_element(By.XPATH, '//div[text()="Order Summary"]/../..//span[text()="Net Total "]').is_displayed()
            assert [orderKey,orderDate, orderState, orderProvider, externalId, airline, flight, netTotal]
            logging.info('001. "Following fields should be visible to admin in grid: Order Key, Order Provider, '
                         'Order State, Order Date, Customer Firstname and Lastname, Airline, Flight, Net Total, Store, '
                         'Shipping method or speed for Product, Payment Status, Order Status Last Updated, '
                         'Number of days since Order Placement, Order Status Last Updated Date"')
            # 002. Verify by clicking on the CSV Export button
            # click at Children Grid
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrderItem',Keys.TAB))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="CSV Export"]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="CSV Export"]/../../../../..//span[text()="OK"]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.homeDeliveryXpath + '[2]'))))
            homeDelivery = driver.find_element(By.XPATH, xpath.homeDeliveryXpath + '[2]')
            actions = ActionChains(driver)
            actions.context_click(homeDelivery).perform()
            open1 = driver.find_element(By.XPATH, xpath.openXpathContains)
            open1.click()
            utils.click_on_yes_no(driver)
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, xpath.generalInformationXpath))).is_displayed())
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_EXPORT)
            logging.info(' 002. Admin should be able to export CSV of order information which is present in grid')
            logging.info(' 003. Admin should be able to navigate to the order detail page from the grid')
            Success_List_Append("test_MKTPL_514_OrderGrid_001_002_003",
                                "Verify by clicking on the order folder", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_514_OrderGrid_001_002_003",
                            "Verify by clicking on the order folder", "Fail")
        Failure_Cause_Append("test_MKTPL_514_OrderGrid_001_002_003",
                             "Verify by clicking on the order folder", e)
        raise e

def test_MKTPL_342_OrderGrid_001_002_003():
    RANDOM_NAME = "Frm_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_342_OrderGrid_001_002_003.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            orderKey = driver.find_element(By.XPATH, xpath.orderKeyXpath).is_displayed()
            externalId = driver.find_element(By.XPATH, xpath.externalIdXpath).is_displayed()
            orderProvider = driver.find_element(By.XPATH, xpath.orderProviderXpath).is_displayed()
            orderState = driver.find_element(By.XPATH, xpath.orderStateXpath).is_displayed()
            orderDate = driver.find_element(By.XPATH, xpath.orderDateXpath).is_displayed()
            airline = driver.find_element(By.XPATH, '//div[text()="Flight Information"]/../..//span[contains(text(),"Airline")]').is_displayed()
            flight = driver.find_element(By.XPATH, '//div[text()="Flight Information"]/../..//span[contains(text(),"Flight")]').is_displayed()
            netTotal = driver.find_element(By.XPATH, '//div[text()="Order Summary"]/../..//span[contains(text(),"Net Total")]').is_displayed()
            assert [orderKey,orderDate, orderState, orderProvider, externalId, airline, flight, netTotal]
            logging.info('001. "Following fields should be visible to admin in grid: Order Key, Order Provider, '
                         'Order State, Order Date, Customer Firstname and Lastname, Airline, Flight, Net Total, Store, '
                         'Shipping method or speed for Product, Payment Status, Order Status Last Updated, '
                         'Number of days since Order Placement, Order Status Last Updated Date"')
            # 002. Verify by clicking on the CSV Export button
            # click at Children Grid
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrderItem',Keys.TAB))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="CSV Export"]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="CSV Export"]/../../../../..//span[text()="OK"]'))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.homeDeliveryXpath + '[2]'))))
            homeDelivery = driver.find_element(By.XPATH, xpath.homeDeliveryXpath + '[2]')
            actions = ActionChains(driver)
            actions.context_click(homeDelivery).perform()
            open1 = driver.find_element(By.XPATH, xpath.openXpathContains)
            open1.click()
            utils.click_on_yes_no(driver)
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, xpath.generalInformationXpath))).is_displayed())
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_EXPORT)
            logging.info(' 002. Admin should be able to export CSV of order information which is present in grid')
            logging.info(' 003. Admin should be able to navigate to the order detail page from the grid')
            Success_List_Append("test_MKTPL_342_OrderGrid_001_002_003",
                                "Verify by clicking on the order folder", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_342_OrderGrid_001_002_003",
                            "Verify by clicking on the order folder", "Fail")
        Failure_Cause_Append("test_MKTPL_342_OrderGrid_001_002_003",
                             "Verify by clicking on the order folder", e)
        raise e

def test_MKTPL_359_OrderlineitemGrid_001_002():
    try:
        with utils.services_context_wrapper("test_MKTPL_359_OrderlineitemGrid_001_002.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrderItem',Keys.TAB))
            idXpath = driver.find_element(By.XPATH, xpath.idXpath+'[2]').is_displayed()
            dateXpath = driver.find_element(By.XPATH, xpath.dateXpath).is_displayed()
            statusXpath = driver.find_element(By.XPATH, xpath.statusXpath).is_displayed()
            productXpath = driver.find_element(By.XPATH, xpath.productXpath).is_displayed()
            pacVariantIdXpath = driver.find_element(By.XPATH, xpath.pacVariantIdXpath).is_displayed()
            assert [idXpath, dateXpath,statusXpath, productXpath, pacVariantIdXpath]
            logging.info('"Following fields should be visible to admin in grid')
            # 002. Verify by clicking on the CSV Export button
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.csvExportButtonXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="CSV Export"]/../../../../..//span[text()="OK"]'))).click())
            time.sleep(10)
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_EXPORT)
            logging.info(' 002."Admin should be able to export CSV of order information which is present in grid'
                         'Admin should be able to multi select the order and export CSV of order information which is '
                         'present in grid"')
            Success_List_Append("test_MKTPL_359_OrderlineitemGrid_001_002",
                                "Verify by clicking on the order folder", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_359_OrderlineitemGrid_001_002",
                            "Verify by clicking on the order folder", "Fail")
        Failure_Cause_Append("test_MKTPL_359_OrderlineitemGrid_001_002",
                             "Verify by clicking on the order folder", e)
        raise e

def test_MKTPL_319_OrderlineitemGrid_001_002():
    try:
        with utils.services_context_wrapper("test_MKTPL_319_OrderlineitemGrid_001_002.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrderItem',Keys.TAB))
            idXpath = driver.find_element(By.XPATH, xpath.idXpath+'[2]').is_displayed()
            dateXpath = driver.find_element(By.XPATH, xpath.dateXpath).is_displayed()
            statusXpath = driver.find_element(By.XPATH, xpath.statusXpath).is_displayed()
            productXpath = driver.find_element(By.XPATH, xpath.productXpath).is_displayed()
            pacVariantIdXpath = driver.find_element(By.XPATH, xpath.pacVariantIdXpath).is_displayed()
            assert [idXpath, dateXpath,statusXpath, productXpath, pacVariantIdXpath]
            logging.info('"Following fields should be visible to admin in grid')
            # 002. Verify by clicking on the CSV Export button
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.csvExportButtonXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[text()="CSV Export"]/../../../../..//span[text()="OK"]'))).click())
            time.sleep(2)
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_EXPORT)
            logging.info(' 002."Admin should be able to export CSV of order information which is present in grid'
                         'Admin should be able to multi select the order and export CSV of order information which is '
                         'present in grid"')
            Success_List_Append("test_MKTPL_319_OrderlineitemGrid_001_002",
                                "Verify by clicking on the order folder", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_319_OrderlineitemGrid_001_002",
                            "Verify by clicking on the order folder", "Fail")
        Failure_Cause_Append("test_MKTPL_319_OrderlineitemGrid_001_002",
                             "Verify by clicking on the order folder", e)
        raise e

def test_MKTPL_344_OrderlineitemGrid_001_002():
    try:
        with utils.services_context_wrapper("test_MKTPL_344_OrderlineitemGrid_001_002.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)
            # 001. Verify by clicking on the order
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrderItem',Keys.TAB))
            idXpath = driver.find_element(By.XPATH, xpath.idXpath+'[2]').is_displayed()
            dateXpath = driver.find_element(By.XPATH, xpath.dateXpath).is_displayed()
            statusXpath = driver.find_element(By.XPATH, xpath.statusXpath).is_displayed()
            productXpath = driver.find_element(By.XPATH, xpath.productXpath).is_displayed()
            pacVariantIdXpath = driver.find_element(By.XPATH, xpath.pacVariantIdXpath).is_displayed()
            assert [idXpath, dateXpath,statusXpath, productXpath, pacVariantIdXpath]
            logging.info('"Following fields should be visible to admin in grid')
            # 002. Verify by clicking on the CSV Export button
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.csvExportButtonXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//span[text()="OK"])[last()]'))).click())
            time.sleep(10)
            os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_EXPORT)
            logging.info(' 002."Admin should be able to export CSV of order information which is present in grid'
                         'Admin should be able to multi select the order and export CSV of order information which is '
                         'present in grid"')

            Success_List_Append("test_MKTPL_344_OrderlineitemGrid_001_002",
                                "Verify by clicking on the order folder", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_344_OrderlineitemGrid_001_002",
                            "Verify by clicking on the order folder", "Fail")
        Failure_Cause_Append("test_MKTPL_344_OrderlineitemGrid_001_002",
                             "Verify by clicking on the order folder", e)
        raise e

def test_MKTPL_523_Trackinginfoimport_001_002_003_004_005_006_007_008():
    ACTUAL_CSV_DATA= []
    try:
        with utils.services_context_wrapper("test_MKTPL_523_Trackinginfoimport_001_002_003_004_005_006_007_008.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.managestock(driver, 'yes')
            # 001 002 003 004 005
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(1)
            driver.find_element(By.XPATH,"//div[contains(@id,'storeList-trigger-picker')]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//li[text()='"+DEDICATED_STORE+"']")))
            driver.find_element(By.XPATH,"//li[text()='"+DEDICATED_STORE+"']").click()
            time.sleep(5)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Download Sample File Of TrackingInfo"]')))
            driver.find_element(By.XPATH, '//span[text()="Download Sample File Of TrackingInfo"]').click()
            time.sleep(10)
            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_TRACKINGINFO)
            with open(
                    LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_TRACKINGINFO, "r", encoding='utf-8-sig'
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert ACTUAL_CSV_DATA == CSV_SAMPLE_OREDER_TRACKINGINFO
            logging.info(" 001...005 is successfully ")

            # 006
            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '(//div[@class="x-autocontainer-innerCt"])[5]')))
            upload = driver.find_element(By.XPATH, '(//div[@class="x-autocontainer-innerCt"])[5]').text
            logging.info(upload)
            assert upload in "Please fill all mandatory fields"

            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            logging.info(" 006 Validation message should display")

            # 007
            path = os.path.join(os.getcwd(), "credencys_test_ui", "invalid_file.ppt")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(6)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[@class="x-component x-window-text x-box-item x-component-default"]')))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "(" + xpath.okButtonXpath + ")[last()]")))
            upload = driver.find_element(By.XPATH, '//div[@class="x-component x-window-text x-box-item x-component-default"]').text
            logging.info(upload)
            assert "File Import: Uploaded file should be CSV file with .csv extension." in upload

            driver.find_element(By.XPATH, "(" + xpath.okButtonXpath + ")[last()]").click()
            logging.info(" 007 Validation message should display")

            # 008
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "")
            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()

            utils.Assets_import_Pac_admin(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[@class="ace_line"])[4]')))
            mass = driver.find_element(By.XPATH,
                                         '(//div[@class="ace_line"])[4]').text
            logging.info(mass)
            assert "Shipping ID cannot be empty." in mass
            logging.info(" 008 Validation message should display")

            utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")

            # utils.close_All_Tabs(driver)

            # # 009
            # utils.Trackinginfoimport_log_admin("OrderId", "2222", "Order object not found ")
            # logging.info(" 009 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "OrderId", "2341")
            # utils.close_All_Tabs(driver)

            # # 010
            # utils.Trackinginfoimport_log_admin("OrderId", "9977", "Order object not found ")
            # logging.info(" 010 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "OrderId", "2341")
            # utils.close_All_Tabs(driver)

            # # 011
            # utils.Trackinginfoimport_log_admin("ShippingId", "2222", "Invalid Shipping ID.")
            # logging.info(" 011 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")
            # utils.close_All_Tabs(driver)

            # # 012
            # utils.Trackinginfoimport_log_admin("ShippingId", "9979", "Invalid Shipping ID.")
            # logging.info(" 012 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")
            # utils.close_All_Tabs(driver)

            # # 013
            # utils.Trackinginfoimport_log_admin("OrderLineItemId", "2222", "Invalid Order Line Item ID.")
            # logging.info(" 013 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "2431")
            # utils.close_All_Tabs(driver)

            # # 014
            # utils.Trackinginfoimport_log_admin("OrderLineItemId", "86609541", "Line item 86609541 does not belong to this shipping")
            # logging.info(" 014 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "2431")
            # utils.close_All_Tabs(driver)

            # # 015
            # utils.Trackinginfoimport_log_admin("InventoryLocationId", "22222",
            #                              "Invalid inventory location ID.")
            # logging.info(" 015 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "1233")
            # utils.close_All_Tabs(driver)

            # # 016
            # utils.Trackinginfoimport_log_admin("InventoryLocationId", "40454",
            #                              "Invalid inventory location ID.")
            # logging.info(" 016 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "1233")
            # utils.close_All_Tabs(driver)

            # # 017
            # utils.Trackinginfoimport_log_admin("OrderLineStatus", "PASSING",
            #                              "Invalid Order Line Status")
            # logging.info(" 017 Validation message should display")
            # utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")
            # utils.close_All_Tabs(driver)

            Success_List_Append("test_MKTPL_523_Trackinginfoimport_001_002_003_004_005_006_007_008",
                                "Verify the the manage shipping setting",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_523_Trackinginfoimport_001_002_003_004_005_006_007_008",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_523_Trackinginfoimport_001_002_003_004_005_006_007_008",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_523_Trackinginfoimport_019_MKTPL_495_Trackinginfoimport_018():

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_523_Trackinginfoimport_019_MKTPL_495_Trackinginfoimport_018.png"
        ) as driver:
            # --------
            driver.maximize_window()
            role = ["Login_Pac_Admin", "Login_Store"]
            for i in role:
                RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
                utils.Pac_Credentials.Login_Pac_Admin(driver)
                act_title = driver.find_element(
                    By.XPATH, xpath.logout
                ).get_attribute("id")
                assert act_title == "pimcore_logout"
                logging.info("Login is successful.")
                logging.info("PAC ADMIN")

                payload = json.dumps({
                    "meta": {
                        "airlineCodeICAO": "CRE",
                        "flightNumber": "111",
                        "arrivalAirportCodeIATA": "",
                        "departureAirportCodeIATA": "",
                        "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                        "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
                    },
                    "orders": [
                            {
                            "orderKey": "Order_key_From_Automation_" + RANDOM_NUMBER,
                            "externalId": "From_Automation_" + RANDOM_NUMBER,
                            "basketKey": "From_Automation_" + RANDOM_NUMBER,
                            "orderProvider": "From_Automation_" + RANDOM_NUMBER,
                            "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                            "createdTime": "2024-05-24T17:39:51.287+00:00",
                            "lineItems": [
                                {
                                    "title": "Line_item",
                                    "PACVariantID": "1hospitality:mktplvb48943-var",
                                    "retailerCode": "random",
                                    "associateStore": int(DEDICATED_STORE_ID),
                                    "fulfillmentType": "inHouse",
                                    "alwaysInStock": False,
                                    "imageUrl": "https://images.test/file.jpg",
                                    "vendorProductVariantID": str(DEDICATED_PRODUCT_9_ID),
                                    "unitPrice": {
                                        "value": 49,
                                        "currency": "USD"
                                    },
                                    "unitTax": {
                                        "value": 10,
                                        "currency": "USD"
                                    },
                                    "unitDiscount": {
                                        "value": 4.9,
                                        "currency": "USD"
                                    },
                                    "unitNet": {
                                        "value": 44.1,
                                        "currency": "USD"
                                    },
                                    "unitGross": {
                                        "value": 44.1,
                                        "currency": "USD"
                                    },
                                    "quantity": 2,
                                    "status": "DELIVERED",
                                    "discountTotal": {
                                        "currency": "USD",
                                        "value": 9.8
                                    },
                                    "taxTotal": {
                                        "value": 10,
                                        "currency": "USD"
                                    },
                                    "discountAdjustments": [
                                        {
                                            "discountAmount": {
                                                "currency": "USD",
                                                "value": 12
                                            },
                                            "adjustType": "DISCOUNT",
                                            "rate": 10,
                                            "promotionCode": "asd",
                                            "promotionName": "Percentage discount 10%"
                                        }
                                    ],
                                    "taxAdjustments": [
                                        {
                                            "type": "shipping_rule_tax",
                                            "taxAmount": {
                                                "currency": "USD",
                                                "value": 12
                                            },
                                            "rate": 7
                                        }
                                    ],
                                    "salePrice": {
                                        "value": 49,
                                        "currency": "USD"
                                    },
                                    "key": "key"
                                }
                            ],
                            "status": "CALL_CREW",
                            "payment": [
                                {
                                    "paymentService": "INTERNAL",
                                    "paymentMethod": "CARD",
                                    "authAmount": {
                                        "currency": "USD",
                                        "value": 200
                                    },
                                    "paymentId": "BBAA-0324111-88",
                                    "technicalServiceProviderTransactionId": "20220324173949153132",
                                    "gatewayTransactionId": "A60087687",
                                    "status": "AUTHORIZED",
                                    "billingAddress": {
                                        "firstName": "auty",
                                        "lastName": "matt",
                                        "address1": "34k",
                                        "city": "arizona",
                                        "state": "Los angeles",
                                        "postalCode": "78945",
                                        "countryCode": "US",
                                        "email": "<EMAIL>"
                                    }
                                }
                            ],
                            "shipments": [
                                {
                                    "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                                    "rate": {
                                        "currency": "USD",
                                        "value": 50
                                    },
                                    "shippingMethod": "STANDARD",
                                    "carrier": "DHL",
                                    "taxTotal": [
                                        {
                                            "currency": "USD",
                                            "value": 12
                                        }
                                    ],
                                    "address": {
                                        "firstName": "ismail",
                                        "lastName": "rangwala",
                                        "address1": "456",
                                        "city": "Manchester",
                                        "state": "England",
                                        "postalCode": "12345",
                                        "countryCode": "GB",
                                        "email": "<EMAIL>"
                                    },
                                    "itemKeys": [
                                        "key"
                                    ],
                                    "item": [
                                        "21074012"
                                    ],
                                    "mode": "home_delivery"
                                }
                            ],
                            "orderSummary": {
                                "grossTotal": {
                                    "currency": "USD",
                                    "value": 233
                                },
                                "discounts": [
                                    {
                                        "discountAmount": {
                                            "value": 20,
                                            "currency": "USD"
                                        },
                                        "promotionCode": "New",
                                        "promotionName": "Percentage discount 10%",
                                        "rate": 10
                                    }
                                ],
                                "adjustmentTotal": {
                                    "currency": "USD",
                                    "value": 20
                                },
                                "taxes": [
                                    {
                                        "type": "shipping_rule_tax",
                                        "taxAmount": {
                                            "currency": "USD",
                                            "value": "12"
                                        },
                                        "rate": 7
                                    }
                                ],
                                "totalTaxAmount": {
                                    "currency": "USD",
                                    "value": 12
                                },
                                "shippingTotal": {
                                    "currency": "USD",
                                    "value": 50
                                },
                                "currency": "USD",
                                "netTotal": {
                                    "currency": "USD",
                                    "value": 200
                                }
                            },
                            "user": {
                                "firstName": "ram",
                                "lastName": "ram",
                                "email": "<EMAIL>",
                                 "memberships": [  {
                                "loyaltyNumber": "1223",
                                "loyaltyType": "eeded"
                                }]
                            },
                            "itinerary": {
                                "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                                "confirmationCode": "6kcb2s",
                                "airline": "SQ322",
                                "firstName": "RAJU",
                                "lastName": "sasi",
                                "middleName": "s"
                            },
                            "seatInfo": {
                                "seatClass": "Outside demo",
                                "seatNumber": "Outside demo"
                            },
                            "groundSystem": "GA"
                        }
                    ]
                }
                )
            
                headers = {
                        'Authorization': ORDER_JWT_TOKEN,
                        'Content-Type': 'application/json'
                    }
                response = requests.request('POST', CONSUME_ORDER_URL, headers=headers, data=payload)
                data = response.json()
                logging.info(data)

                assert response.status_code == 200 or response.status_code == 201
                assert "Successful request to sync orders." == data["message"]
                logging.info("Successful request to sync orders.")
                utils.wait_for_style_attribute(driver, 40)
                utils.managestock(driver, 'yes')
                utils.manageshipping(driver, 'yes')

                utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
                
                (WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                                Keys.TAB))
                (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
                send_keys("From_Automation_" + RANDOM_NUMBER, Keys.ENTER))
                (WebDriverWait(driver, 60).until
                    (EC.presence_of_element_located(
                    (By.XPATH, '(//div[contains(text(),"Order_key_From_Automation_")])[1]'))))
                order = driver.find_element(By.XPATH, '(//div[contains(text(),"Order_key_From_Automation_")])[1]')
                actions = ActionChains(driver)
                actions.context_click(order).perform()
                time.sleep(3)
                open1 = driver.find_element(By.XPATH, xpath.open)
                open1.click()
                utils.click_on_yes_no(driver)
                WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                    (By.XPATH, "//div[text()='OnlineShopOrder']")))
                WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, "(//div[contains(text(),'ID ') and (@class='x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default')])[last()]")))
                orderId = driver.find_element(
                    By.XPATH, "(//div[contains(text(),'ID ') and (@class='x-toolbar-text x-box-item x-toolbar-item x-toolbar-text-default')])[last()]").text
                orderId = orderId.split()
                orderId = orderId[1]
                logging.info(f"Unique ID: {orderId}")
                logging.info(orderId)
                shipment_id = int(int(orderId) + 2)
                item1_id = int(shipment_id) + 2
                logging.info(shipment_id)
                logging.info(item1_id)
                if i is "Login_Pac_Admin":
                    utils.close_All_Tabs(driver)
                elif i is "Login_Store":
                    #Logout
                    driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                    logging.info("Logged out")
                    WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("AIRLINE ADMIN")

                # 019 - Verify by importing valid inventory location id
                # Inventory should be deducted from products for selected inventory location
                utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
                driver.find_element(By.XPATH, "//span[text()='Inventory']").click()
                WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//td[text()='Inventory Locations']")))
                quantity = driver.find_element(By.XPATH, "(//td[contains(text(),'Default Inventory')]//parent::tr//td)[2]").text
                logging.info(quantity)
                WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
                driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
                driver.find_element(By.XPATH, xpath.csvImport).click()

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
                data_type = driver.find_element(By.NAME, xpath.importTypeName)
                data_type.send_keys("TrackingInfo")
                time.sleep(1)
                data_type.send_keys(Keys.ENTER)
                if i is "Login_Pac_Admin":
                    time.sleep(1)
                    driver.find_element(By.XPATH,"//div[contains(@id,'storeList-trigger-picker')]").click()
                    driver.find_element(By.NAME, "storeList").send_keys(DEDICATED_STORE)
                    time.sleep(2)
                    WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//li[text()='"+DEDICATED_STORE+"']")))
                    driver.find_element(By.XPATH,"//li[text()='"+DEDICATED_STORE+"']").click()
                    time.sleep(5)
                
                utils.update_csv("order_trackingInfo.csv", "OrderId", orderId)
                utils.update_csv("order_trackingInfo.csv", "ShippingId", shipment_id)
                utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", item1_id)
                utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", DEDICATED_STORE_LOCATION_1_ID)
                utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")

                path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
                driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

                driver.find_element(By.XPATH, xpath.upload).click()
                if i is "Login_Pac_Admin":
                    utils.close_All_Tabs(driver)

                    utils.Assets_import_Pac_admin(driver)
                elif i is "Login_Store":
                    #Logout
                    driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                    logging.info("Logged out from Store Admin")
                    WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("STORE")
                    utils.Assets_import_Store(driver)
                time.sleep(5)
                list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
                num = len(list1)
                time.sleep(5)
                row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
                actions = ActionChains(driver)
                actions.double_click(row).perform()

                try:
                    WebDriverWait(driver, 60).until(
                        EC.element_to_be_clickable((By.XPATH, xpath.yes))
                    )
                    driver.find_element((By.XPATH, xpath.yes)).click()
                    logging.info("Click on message")
                except:
                    pass
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//div[@class="ace_line"])[4]')))
                mass = driver.find_element(By.XPATH,
                                        '(//div[@class="ace_line"])[4]').text
                logging.info(mass)
                assert "Row Inserted\/updated Successfully" in mass
                utils.close_All_Tabs(driver)
                utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
                driver.find_element(By.XPATH, "//span[text()='Inventory']").click()
                WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//td[text()='Inventory Locations']")))
                quantity_updated = driver.find_element(By.XPATH, "(//td[contains(text(),'Default Inventory')]//parent::tr//td)[2]").text
                logging.info(quantity_updated)
                assert quantity_updated == str(int(quantity)-2)
                driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                logging.info("Logged out")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))

                logging.info("19 pass")
            Success_List_Append("test_MKTPL_523_Trackinginfoimport_019_MKTPL_495_Trackinginfoimport_018",
                                "Verify by importing valid inventory location id",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_523_Trackinginfoimport_019_MKTPL_495_Trackinginfoimport_018",
                            'Verify by importing valid inventory location id', "Fail", )
        Failure_Cause_Append("test_MKTPL_523_Trackinginfoimport_019_MKTPL_495_Trackinginfoimport_018",
                             'Verify by importing valid inventory location id', e, )
        raise e

def test_MKTPL_495_Trackinginfoimport_001_002_003_004_005_006_007_020():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_495_Trackinginfoimport_001_002_003_004_005_006_007_020.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.managestock(driver, 'yes')
            # 001 002 003 004
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            logging.info("csvImport")

            driver.find_element(By.XPATH, '//span[text()="Download Sample File Of TrackingInfo"]').click()
            time.sleep(5)
            logging.info("Download Sample File Of TrackingInfo")

            logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_TRACKINGINFO)
            with open(
                    LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_TRACKINGINFO, "r", encoding='utf-8-sig'
            ) as file:
                csvFile = csv.reader(file)
                for lines in csvFile:
                    logging.info(lines)
                    ACTUAL_CSV_DATA.append(lines)
            assert ACTUAL_CSV_DATA == CSV_SAMPLE_OREDER_TRACKINGINFO
            logging.info(" 001...004 success ")

            # 005
            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//div[@class="x-autocontainer-innerCt"])[5]')))
            upload = driver.find_element(By.XPATH, '(//div[@class="x-autocontainer-innerCt"])[5]').text
            logging.info(upload)
            assert upload in "Please fill all mandatory fields"

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            logging.info(" 005 Validation message should display")

            # 006
            path = os.path.join(os.getcwd(), "credencys_test_ui", "invalid_file.ppt")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()
            time.sleep(6)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[@class="x-component x-window-text x-box-item x-component-default"]')))
            time.sleep(10)
            upload = driver.find_element(By.XPATH,
                                         '//div[@class="x-component x-window-text x-box-item x-component-default"]').text
            logging.info(upload)
            assert "File Import: Uploaded file should be CSV file with .csv extension." in upload

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()
            logging.info(" 006 Validation message should display")

            # 007
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "")
            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.Assets_import_Store(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[@class="ace_line"])[4]')))
            mass = driver.find_element(By.XPATH,
                                       '(//div[@class="ace_line"])[4]').text
            logging.info(mass)
            assert "Shipping ID cannot be empty." in mass
            logging.info(" 007 Validation message should display")

            utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            
            # 020
            utils.manageshipping(driver, "no")
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.ID, "importType-trigger-picker")))
            driver.find_element(By.ID, "importType-trigger-picker").click()

            # WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//li[text()="TrackingInfo"]')))
            assert not len(driver.find_elements(By.XPATH, '//li[text()="TrackingInfo"]')) 
            utils.manageshipping(driver, "yes")
            logging.info(' 020 "TrackingInfo" import data type should be hidden')
            Success_List_Append(
                "test_MKTPL_495_Trackinginfoimport_001_002_003_004_005_006_007_020",
                "Verify the the manage shipping setting",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_495_Trackinginfoimport_001_002_003_004_005_006_007_020",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_495_Trackinginfoimport_001_002_003_004_005_006_007_020",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_4073_OrderAPI_001_002_003_004_005_006_MKTPL_2827_OrderAPI_025():
    RANDOM_NUMBER = ("".join([str(randint(0, 9)) for i in range(7)]))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_4073_OrderAPI_001_002_003_004_005_006_MKTPL_2827_OrderAPI_025.png"
        ) as driver:
            # 001.
            api_response, externalID = utils.order_api_payload("John", "Doe", "<EMAIL>", "ismail", "rangwala",
                                                   "456",
                                                   "Manchester", "England", "12345", "GB",
                                                   "<EMAIL>",
                                                   "", "sx", "13KNTV34", "CRE", RANDOM_NUMBER+"  From_Automation_",
                                                   "2024-05-24T17:39:51.287+00:00",
                                                   "random", DEDICATED_STORE_ID, "DELIVERED", "CALL_CREW", "INTERNAL",
                                                   "home_delivery",
                                                   "Outside demo", "Outside demo", "GA", "USD", ORDER_JWT_TOKEN, "POST",
                                                   CONSUME_ORDER_URL)

            logging.info(externalID)
            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            # # ----------------------------------------------------------------------
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
            ID = externalID.split()
            ID = ID[0]
            logging.info(ID)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
             send_keys(ID, Keys.ENTER))
            (WebDriverWait(driver, 60).until
                (EC.presence_of_element_located(
                (By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            orderId = driver.find_element(By.XPATH,
                                          '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            logging.info(orderId)

            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[@class="x-tab-close-btn"])[1]'))).click())
            # ----------
            utils.search_by_id(driver, orderId)
            redIcon = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '((//span[text()="'+externalID+'"])/..//span)[1]'))).value_of_css_property(
                "background-image"))
            logging.info(redIcon)
            logging.info('The manual action order is visually marked with a red cross icon .')
            assert 'delete.svg' in redIcon
            errorMessage = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '//p[text()="There are issues with the following fields: "]'))).is_displayed())
            assert errorMessage
            logging.info('The offloading process completes without crashing. The order with the schema error is marked '
                         'for manual action. The order"s data is stored within the PIMCORE.')
            logging.info('The system does not mark the order with schema errors as successful .')
            errorMessageField = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '//li[text()=" departureAirportCodeIATA "]'))).is_displayed())
            assert errorMessageField
            logging.info('The system detect the missing fields, mark the order for manual review, and ensure '
                         'the incomplete data is saved for future correction.')
            # #----------
            utils.close_All_Tabs(driver)
            # -
            utils.Assets_Failed_Order_Logs(driver)
            time.sleep(3)
            list1 = driver.find_elements(By.XPATH, "(//div[contains(text(),'/Log/Failed Order Logs')])")
            num = len(list1)
            time.sleep(3)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'/Log/Failed Order Logs')])[{num}]")
            actions = ActionChains(driver)
            actions.context_click(row).perform()
            time.sleep(3)
            open1 = driver.find_element(By.XPATH, "//span[contains(text(),'Open')]")
            open1.click()
            wait = WebDriverWait(driver, 60)
            utils.click_on_yes_no(driver)
            logging.info("Opened the log file - json")
            logging.info('"Failed Order Log" is created for the order with invalid json.')
            # //span[contains(text(),"am orderFailure.log")]
            keyvalue =  WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '//span[contains(text(),"am orderFailure.log")]')))
            verifyKeyValue = keyvalue.is_displayed()
            logging.info(verifyKeyValue)
            assert verifyKeyValue
            # keyvalueText = keyvalue.text
            # logging.info(keyvalueText)
            # current_date = today()
            # logging.info(current_date)
            # stringDate = (str(current_date))
            # date = stringDate.split()
            # date = date[0]
            # logging.info(date)
            # assert date in keyvalueText
            logging.info('The key of the log entry contain a valid date and timestamp.')
            Success_List_Append("test_MKTPL_4073_OrderAPI_001_002_003_004_005_006_MKTPL_2827_OrderAPI_025",
                                "To verify that orders with schema errors are still offloaded and stored for"
                                " manual action.",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_4073_OrderAPI_001_002_003_004_005_006_MKTPL_2827_OrderAPI_025",
                            "To verify that orders with schema errors are still offloaded and stored for manual action.", "Fail",)
        Failure_Cause_Append("test_MKTPL_4073_OrderAPI_001_002_003_004_005_006_MKTPL_2827_OrderAPI_025",
                             "To verify that orders with schema errors are still offloaded and stored for manual action.", e,)
        raise e

def test_MKTPL_4073_OrderAPI_012_013():
    RANDOM_NAME = "(From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    RANDOM_NUMBER = "From_Automation_" + ("".join([str(randint(0, 9)) for i in range(7)]))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_4073_OrderAPI_012_013.png"
        ) as driver:
            api_response, externalID = utils.order_api_payload("John", "Doe", "<EMAIL>", "ismail", "rangwala",
                                                               "456",
                                                               "Manchester", "England", "12345", "GB",
                                                               "<EMAIL>",
                                                               "", "", "13KNTV34", "CRE",
                                                               RANDOM_NUMBER + "  From_Automation_",
                                                               "2024-05-24T17:39:51.287+00:00",
                                                               "random", DEDICATED_STORE_ID, "DELIVERED", "CALL_CREW",
                                                               "INTERNAL",
                                                               "home_delivery",
                                                               "Outside demo", "Outside demo", "GA", "USD",
                                                               ORDER_JWT_TOKEN, "POST",
                                                               CONSUME_ORDER_URL)

            logging.info(externalID)
            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            # # ----------------------------------------------------------------------
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
            ID = externalID.split()
            ID = ID[0]
            logging.info(ID)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
             send_keys(ID, Keys.ENTER))
            (WebDriverWait(driver, 60).until
                (EC.presence_of_element_located(
                (By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            orderId = driver.find_element(By.XPATH,
                                          '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            logging.info(orderId)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[@class="x-tab-close-btn"])[1]'))).click())
            # ----------
            utils.search_by_id(driver, orderId)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrderItem',
                                                                                              Keys.TAB))
            sameNameAsTitle = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH,
                                                '//div[contains(text(),"KRIS ⋮ JUNI12OR POLAR1 BEAR BY JELLYCAT®")]'))).is_displayed())
            assert sameNameAsTitle
            logging.info('The key (name) of the Pimcore folder matches the "Title" provided in the order payload.')
            logging.info('A folder in Pimcore, corresponding to the Line item , is present. '
                         'The key (name) of the folder matches the Title provided in the order payload.')
            Success_List_Append("test_MKTPL_4073_OrderAPI_012_013",
                                "To verify mismatched Key in Pimcore Order ID Folder", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_4073_OrderAPI_012_013",
                            "To verify mismatched Key in Pimcore Order ID Folder", "Fail",)
        Failure_Cause_Append("test_MKTPL_4073_OrderAPI_012_013",
                             "To verify mismatched Key in Pimcore Order ID Folder", e,)
        raise e

def test_MKTPL_3783_4441_order_001_002_003():
    try:
        with utils.services_context_wrapper("test_MKTPL_3783_4441_order_001_002_003.png") as driver:

            # 001
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            payload = json.dumps({
            "meta": {
                "airlineCodeICAO": "CRE",
                "flightNumber": "111",
                "arrivalAirportCodeIATA": "",
                "departureAirportCodeIATA": "",
                "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
            },
            "orders": [
                {
                    "orderKey": "From_Automation_3328 improvement current flight shipment",
                    "externalId": "From_Automation_" + RANDOM_NUMBER,
                    "basketKey": "From_Automation_3328 improvement current flight shipment",
                    "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                    "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                    "createdTime": "2024-05-24T17:39:51.287+00:00",
                    "lineItems": [
                        {
                            "title": "Demo Line Item",
                            "PACVariantID": "1hospitality:mktplvb48943-var",
                            "retailerCode": "retailerCode",
                            "associateStore": str(DEDICATED_STORE_ID),
                            "fulfillmentType": "inHouse",
                            "alwaysInStock": False,
                            "imageUrl": "https://images.test/file.jpg",
                            "vendorProductVariantID": str(DEDICATED_SKU_1_ID),
                            "unitPrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "unitTax": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "unitDiscount": {
                                "value": 4.9,
                                "currency": "USD"
                            },
                            "unitNet": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "unitGross": {
                                "value": 44.1,
                                "currency": "USD"
                            },
                            "quantity": 2,
                            "status": "DELIVERED",
                            "discountTotal": {
                                "currency": "USD",
                                "value": 9.8
                            },
                            "taxTotal": {
                                "value": 10,
                                "currency": "USD"
                            },
                            "discountAdjustments": [
                                {
                                    "discountAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "adjustType": "DISCOUNT",
                                    "rate": 10,
                                    "promotionCode": "asd",
                                    "promotionName": "Percentage discount 10%"
                                }
                            ],
                            "taxAdjustments": [
                                {
                                    "type": "shipping_rule_tax",
                                    "taxAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "rate": 7
                                }
                            ],
                            "salePrice": {
                                "value": 49,
                                "currency": "USD"
                            },
                            "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                        }
                    ],
                    "status": "CALL_CREW",
                    "payment": [
                        {
                            "paymentService": "INTERNAL",
                            "paymentMethod": "CARD",
                            "authAmount": {
                                "currency": "USD",
                                "value": 230
                            },
                            "paymentId": "BBAA-0324111-88",
                            "technicalServiceProviderTransactionId": "20220324173949153132",
                            "gatewayTransactionId": "A60087687",
                            "status": "AUTHORIZED",
                            "billingAddress": {
                                "firstName": "karan",
                                "lastName": "bhatt",
                                "address1": "123",
                                "city": "arizona",
                                "state": "Los angeles",
                                "postalCode": "78945",
                                "countryCode": "US",
                                "email": "<EMAIL>"
                            }
                        }
                    ],
                    "shipments": [
                        {
                            "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                            "rate": {
                                "currency": "USD",
                                "value": 50
                            },
                            "shippingMethod": "STANDARD",
                            "carrier": "DHL",
                            "taxTotal": [
                                {
                                    "currency": "USD",
                                    "value": 12
                                }
                            ],
                            "address": {
                                "firstName": "ismail",
                                "lastName": "rangwala",
                                "address1": "456",
                                "city": "Manchester",
                                "state": "England",
                                "postalCode": "12345",
                                "countryCode": "GB",
                                "email": "<EMAIL>"
                            },
                            "itemKeys": [
                                
                            ],
                            "item": [
                                "21074012"
                            ],
                            "mode": "home_delivery"
                        }
                    ],
                    "orderSummary": {
                        "grossTotal": {
                            "currency": "USD",
                            "value": 200
                        },
                        "discounts": [
                            {
                                "discountAmount": {
                                    "value": 20,
                                    "currency": "USD"
                                },
                                "promotionCode": "New",
                                "promotionName": "Percentage discount 10%",
                                "rate": 0
                            }
                        ],
                        "adjustmentTotal": {
                            "currency": "USD",
                            "value": 20
                        },
                        "taxes": [
                            {
                                "type": "shipping_rule_tax",
                                "taxAmount": {
                                    "currency": "USD",
                                    "value": 12
                                },
                                "rate": 7
                            }
                        ],
                        "totalTaxAmount": {
                            "currency": "USD",
                            "value": 12
                        },
                        "shippingTotal": {
                            "currency": "USD",
                            "value": 50
                        },
                        "currency": "USD",
                        "netTotal": {
                            "currency": "USD",
                            "value": 230
                        }
                    },
                    "user": {
                        "firstName": "ram",
                        "lastName": "ram",
                        "email": "<EMAIL>"
                    },
                    "itinerary": {
                        "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                        "confirmationCode": "6kcb2s",
                        "airline": "SQ322",
                        "firstName": "RAJU",
                        "lastName": "sasi",
                        "middleName": "s"
                    },
                    "seatInfo": {
                        "seatClass": "Outside demo",
                        "seatNumber": "Outside demo"
                    },
                    "groundSystem": "GA"
                    }
                ]
            })
            headers = {
                'Authorization': ORDER_JWT_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", CONSUME_ORDER_URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)

            assert 103 == data["error_code"]
            assert "VALIDATION_FAILED" in data["text_code"]
            assert "In Order [1] => shipments [1] itemKeys data can not be empty if shipment Mode is home_delivery. It is a mandatory Field." in data["error_message"]

            logging.info('001 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "<FieldName> data can not be empty. It is a mandatory Field."')

            # 002
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.search_by_id(driver, DEDICATED_ORDER_1_LINE_ITEM_ID)

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Retailer Code "]'))).is_displayed()

            logging.info(" 002 retailerCode data should be stored in line item object in the pimcore")

            # 003
            payload = json.dumps({
                "meta": {
                    "airlineCodeICAO": "CRE",
                    "flightNumber": "13KNTV34",
                    "arrivalAirportCodeIATA": "",
                    "departureAirportCodeIATA": "sx",
                    "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                    "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
                },
                "orders": [
                    {
                        "orderKey": "From_Automation_2dsed improvement current flight shipment",
                        "externalId": "From_Automation_kdkskjsk",
                        "basketKey": "From_Automation_3328 improvement current flight shipment",
                        "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                        "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                        "createdTime": "2024-05-24T17:39:51.287+00:00",
                        "lineItems": [
                            {
                                "title": "KRIS ⋮ JUNI12OR POLAR1 BEAR BY JELLYCAT®",
                                "PACVariantID": "1hospitality:mktplvb48943-var",
                                "retailerCode": "random",
                                "associateStore": "40643",
                                "fulfillmentType": "inHouse",
                                "alwaysInStock": False,
                                "imageUrl": "https://images.test/file.jpg",
                                "vendorProductVariantID": str(DEDICATED_SKU_1_ID),
                                "unitPrice": {
                                    "value": 10,
                                    "currency": "USD"
                                },
                                "unitTax": {
                                    "value": 10,
                                    "currency": "USD"
                                },
                                "unitDiscount": {
                                    "value": 4.9,
                                    "currency": "USD"
                                },
                                "unitNet": {
                                    "value": 44.1,
                                    "currency": "USD"
                                },
                                "unitGross": {
                                    "value": 44.1,
                                    "currency": "USD"
                                },
                                "quantity": 2,
                                "status": "DELIVERED",
                                "discountTotal": {
                                    "currency": "USD",
                                    "value": 9.8
                                },
                                "taxTotal": {
                                    "value": 10,
                                    "currency": "USD"
                                },
                                "discountAdjustments": [
                                    {
                                        "discountAmount": {
                                            "currency": "USD",
                                            "value": 12
                                        },
                                        "adjustType": "DISCOUNT",
                                        "rate": 10,
                                        "promotionCode": "asd",
                                        "promotionName": "Percentage discount 10%"
                                    }
                                ],
                                "taxAdjustments": [
                                    {
                                        "type": "shipping_rule_tax",
                                        "taxAmount": {
                                            "currency": "USD",
                                            "value": 12
                                        },
                                        "rate": 7
                                    }
                                ],
                                "salePrice": {
                                    "value": 49,
                                    "currency": "USD"
                                },
                                "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                            }
                        ],
                        "status": "CALL_CREW",
                        "payment": [
                            {
                                "paymentService": "INTERNAL",
                                "paymentMethod": "CARD",
                                "authAmount": {
                                    "currency": "USD",
                                    "value": 230
                                },
                                "paymentId": "ededed-edejdnx",
                                "technicalServiceProviderTransactionId": "20220324173949153132",
                                "gatewayTransactionId": "A60087687",
                                "status": "AUTHORIZED",
                                "billingAddress": {
                                    "firstName": "karan",
                                    "lastName": "bhatt",
                                    "address1": "123",
                                    "city": "arizona",
                                    "state": "Los angeles",
                                    "postalCode": "78945",
                                    "countryCode": "US",
                                    "email": "<EMAIL>"
                                }
                            }
                        ],
                        "shipments": [
                            {
                                "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                                "rate": {
                                    "currency": "USD",
                                    "value": 0
                                },
                                "shippingMethod": "STANDARD",
                                "carrier": "DHL",
                                "taxTotal": [
                                    {
                                        "currency": "USD",
                                        "value": 12
                                    }
                                ],
                                "address": {
                                    "firstName": "ismail",
                                    "lastName": "rangwala",
                                    "address1": "456",
                                    "city": "Manchester",
                                    "state": "England",
                                    "postalCode": "12345",
                                    "countryCode": "GB",
                                    "email": "<EMAIL>"
                                },
                                "itemKeys": [
                                    "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                                ],
                                "item": [
                                    "21074012"
                                ],
                                "mode": "home_delivery"
                            }
                        ],
                        "orderSummary": {
                            "grossTotal": {
                                "currency": "USD",
                                "value": 200
                            },
                            "discounts": [
                                {
                                    "discountAmount": {
                                        "value": 20,
                                        "currency": "USD"
                                    },
                                    "promotionCode": "New",
                                    "promotionName": "Percentage discount 10%",
                                    "rate": 0
                                }
                            ],
                            "adjustmentTotal": {
                                "currency": "USD",
                                "value": 20
                            },
                            "taxes": [
                                {
                                    "type": "shipping_rule_tax",
                                    "taxAmount": {
                                        "currency": "USD",
                                        "value": 12
                                    },
                                    "rate": 7
                                }
                            ],
                            "totalTaxAmount": {
                                "currency": "USD",
                                "value": 12
                            },
                            "shippingTotal": {
                                "currency": "USD",
                                "value": 50
                            },
                            "currency": "USD",
                            "netTotal": {
                                "currency": "USD",
                                "value": 230
                            }
                        },
                        "user": {
                            "firstName": "John",
                            "lastName": "Doe",
                            "email": "<EMAIL>"
                        },
                        "itinerary": {
                            "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                            "confirmationCode": "6kcb2s",
                            "airline": "SQ322",
                            "firstName": "RAJU",
                            "lastName": "sasi",
                            "middleName": "s"
                        },
                        "seatInfo": {
                            "seatClass": "Outside demo",
                            "seatNumber": "Outside demo"
                        },
                        "groundSystem": "GA"
                    }
                ]
            })
            headers = {
                'Authorization': ORDER_JWT_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", CONSUME_ORDER_URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)

            assert response.status_code == 200 or response.status_code == 201

            logging.info(" 003 value, rate=0 should be allowed from API in order")

            Success_List_Append("test_MKTPL_3783_4441_order_001_002_003",
                                "As a store admin, i should be able to manage shipping on the ground tool",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3783_4441_order_001_002_003",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_3783_4441_order_001_002_003",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_5503_floatingpoint_001_03_05_07_09_011_013_015_017_019_021_023_025_027_029_031_033_043():

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5503_floatingpoint_001_03_05_07_09_011_013_015_017_019_021_023_025_027_029_031_033_043.png"
        ) as driver:
            # 001.
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                                  "20.222", "33.444", "21.343", "200.332",
                                                                  "67.555", "12.123", "12.122", "23.345",
                                                                  "34.333", "34.444", "56.234",
                                                                  "34.434", "23.232", "23.555",
                                                                  "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            logging.info("POST - Value with upto 3 decimal precision is allowed from Post Order API")
            logging.info(externalId)
            # -------
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, ORDER_FOLDER)   # Order folder ID
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
             send_keys(externalId, Keys.ENTER))
            # (WebDriverWait(driver, 60).until
            #  (EC.presence_of_element_located((By.XPATH,'(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            # orderId = driver.find_element(By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            # logging.info(orderId)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"(//table[contains(@id,'patchedgridview')]//div[contains(@class,'x-grid-cell-inner ')])[2]")))
            order = driver.find_element(By.XPATH, "(//table[contains(@id,'patchedgridview')]//div[contains(@class,'x-grid-cell-inner ')])[2]")
            actions = ActionChains(driver)
            actions.context_click(order).perform()
            driver.find_element(By.XPATH, "(//span[contains(text(),'Open')])[last()]").click()
            utils.click_on_yes_no(driver)
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH,"//span[text()='Print E-Receipt']")))
            UID = driver.find_element(By.XPATH, "((//a[@data-qtip='Show in Tree'])[last()]//following-sibling::div)[2]").text
            UID = UID.split()
            order_id = UID[1]
            logging.info(order_id)
            driver.find_element(By.XPATH, xpath.logout).click()

            # GET BY ID - 043. Verify the GET Order By ID API
            response = requests.request("GET", GET_ORDER_URL + str(order_id), headers={
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }, data={})
            data = response.json()
            logging.info(data)
            # Check if the required fields exist in the response
            assert data["data"]["orders"]["lineItemArray"][0]["unitPrice"]["value"] == 20.222
            assert data["data"]["orders"]["lineItemArray"][0]["unitTax"]["value"] == 33.444
            assert data["data"]["orders"]["lineItemArray"][0]["unitDiscount"]["value"] == 21.343
            assert data["data"]["orders"]["lineItemArray"][0]["unitNet"]["value"] == 200.332
            assert data["data"]["orders"]["lineItemArray"][0]["unitGross"]["value"] == 67.555
            assert data["data"]["orders"]["lineItemArray"][0]["discountTotal"]["value"] == 12.123
            assert data["data"]["orders"]["lineItemArray"][0]["taxTotal"]["value"] == 12.122
            assert data["data"]["orders"]["lineItemArray"][0]["taxAdjustments"][0]["taxAmount"][
                       "value"] == '23.345'
            assert data["data"]["orders"]["lineItemArray"][0]["taxAdjustments"][0]["rate"] == '34.333'
            assert data["data"]["orders"]["payment"][0]["authAmount"]["value"] == 34.444
            assert data["data"]["orders"]["orderSummary"]["grossTotal"]["value"] == '56.234'
            assert data["data"]["orders"]["orderSummary"]["netTotal"]["value"] == '34.434'
            assert data["data"]["orders"]["orderSummary"]["shippingTotal"]["value"] == '23.232'
            assert data["data"]["orders"]["orderSummary"]["adjustmentTotal"]["value"] == 23.555
            assert data["data"]["orders"]["orderSummary"]["totalTaxAmount"]["value"] == '32.443'
            assert data["data"]["orders"]["orderSummary"]["taxes"][0]["taxAmount"]["value"] == '43.444'
            assert data["data"]["orders"]["orderSummary"]["taxes"][0]["rate"] == '34.344'
            Success_List_Append("test_MKTPL_5503_floatingpoint_001_03_05_07_09_011_013_015_017_019_021_023_025_027_029_031_033_043",
                                "Verify if user enters all value with upto 3 decimal precision in line item",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5503_floatingpoint_001_03_05_07_09_011_013_015_017_019_021_023_025_027_029_031_033_043",
                            "Verify if user enters all value with upto 3 decimal precision in line item",
                            "Fail",)
        Failure_Cause_Append("test_MKTPL_5503_floatingpoint_001_03_05_07_09_011_013_015_017_019_021_023_025_027_029_031_033_043",
                             "Verify if user enters all value with upto 3 decimal precision in line item", e,)
        raise e

def test_MKTPL_5503_floatingpoint_044_045_046_047_048_049_050_051_052_053_054_055_056_057_058_059():
    try:
        with utils.services_context_wrapper("test_MKTPL_5503_floatingpoint_044_045_046_047_048_049_050_051_052_053_054_055_056_057_058_059.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_LINE_ITEM_ID)
            time.sleep(1)
            # 044. Verify if user enters unitPrice with upto 3 decimal precision in line item object
            unitPriceField = driver.find_element(By.XPATH, '('+xpath.unitPriceXpath+'/../../..//input)[1]')
            driver.execute_script("arguments[0].scrollIntoView();", unitPriceField)
            unitPriceField.clear()
            time.sleep(1)
            unitPriceField.send_keys('45.333')
            time.sleep(1)
            utils.save_and_publish(driver)
            logging.info('044. unitPrice with upto 3 decimal precision be allowed from UI in line item object')
            # 045. Verify if user enters unitPrice with more than 3 decimal precision in line item object
            unitPriceField.clear()
            time.sleep(1)
            unitPriceField.send_keys('45.3333333')
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, xpath.validationMessUnitPriceXpath))).is_displayed())
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, xpath.validationMessUnitPriceXpath+"/../../.."+xpath.okButtonXpath))).click())
            unitPriceField.clear()
            time.sleep(1)
            unitPriceField.send_keys('45.33')
            logging.info('045. Validation message display')
            # 46. Verify if user enters unitTax with upto 3 decimal precision in line item object
            unitTaxField = driver.find_element(By.XPATH, '('+xpath.unitTaxXpath+'/../../..//input)[1]')
            driver.execute_script("arguments[0].scrollIntoView();", unitTaxField)
            unitTaxField.clear()
            time.sleep(1)
            unitTaxField.send_keys('45.333')
            time.sleep(1)
            utils.save_and_publish(driver)
            logging.info('046. unitTax with upto 3 decimal precision allowed from UI in line item object')
            # 047. Verify if user enters unitTax with more than 3 decimal precision in line item object
            unitTaxField.clear()
            time.sleep(1)
            unitTaxField.send_keys('45.3333333')
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessUnitTaxXpath))).is_displayed())
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessUnitTaxXpath+"/../../.."+xpath.okButtonXpath))).click())
            unitTaxField.clear()
            time.sleep(1)
            unitTaxField.send_keys('45.3')
            logging.info('047. Validation message should display')
            # 048. Verify if user enters unitDiscount with upto 3 decimal precision in line item object
            unitDiscountField = driver.find_element(By.XPATH, '(' + xpath.unitDiscountsXpath + '/../../..//input)[1]')
            driver.execute_script("arguments[0].scrollIntoView();", unitDiscountField)
            unitDiscountField.clear()
            time.sleep(1)
            unitDiscountField.send_keys('45.333')
            time.sleep(1)
            utils.save_and_publish(driver)
            logging.info('048. unitDiscount with upto 3 decimal precision should be allowed from UI in line item object')
            # 049. Verify if user enters unitDiscount with more than 3 decimal precision in line item object
            unitDiscountField.clear()
            time.sleep(1)
            unitDiscountField.send_keys('45.3333333')
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessUnitDiscountsXpath))).is_displayed())
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessUnitDiscountsXpath + "/../../.." + xpath.okButtonXpath))).click())
            unitDiscountField.clear()
            time.sleep(1)
            unitDiscountField.send_keys('45.3')
            logging.info('049. Validation message should display')
            # 050. Verify if user enters unitNet with upto 3 decimal precision in line item object
            unitNetField = driver.find_element(By.XPATH, '(' + xpath.unitNetTotalXpath + '/../../..//input)[1]')
            driver.execute_script("arguments[0].scrollIntoView();", unitNetField)
            unitNetField.clear()
            time.sleep(1)
            unitNetField.send_keys('5.333')
            time.sleep(1)
            utils.save_and_publish(driver)
            logging.info(
                '050. unitNet with upto 3 decimal precision allowed from UI in line item object')
            # 051. Verify if user enters unitNet with more than 3 decimal precision in line item object
            unitNetField.clear()
            time.sleep(1)
            unitNetField.send_keys('5.3333333')
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessUnitNetXpath))).is_displayed())
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessUnitNetXpath + "/../../.." + xpath.okButtonXpath))).click())
            unitNetField.clear()
            time.sleep(1)
            unitNetField.send_keys('5.3')
            logging.info('051. Validation message should display')
            # 052. Verify if user enters unitGross with upto 3 decimal precision in line item object
            unitGrossField = driver.find_element(By.XPATH, '(' + xpath.unitGrossTotalXpath + '/../../..//input)[1]')
            driver.execute_script("arguments[0].scrollIntoView();", unitGrossField)
            unitGrossField.clear()
            time.sleep(1)
            unitGrossField.send_keys('6.333')
            time.sleep(1)
            utils.save_and_publish(driver)
            logging.info(
                '052. unitGross with upto 3 decimal precision allowed from UI in line item object')
            # 053. Verify if user enters unitGross with more than 3 decimal precision in line item object
            unitGrossField.clear()
            time.sleep(1)
            unitGrossField.send_keys('7.3333333')
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessUnitGrossXpath))).is_displayed())
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessUnitGrossXpath + "/../../.." + xpath.okButtonXpath))).click())
            unitGrossField.clear()
            time.sleep(1)
            unitGrossField.send_keys('6.3')
            logging.info('053. Validation message should display')
            # 054. Verify if user enters discountTotal with upto 3 decimal precision in line item object
            discountTotalField = driver.find_element(By.XPATH, '(' + xpath.unitDiscountsXpath + '/../../..//input)[1]')
            driver.execute_script("arguments[0].scrollIntoView();", discountTotalField)
            discountTotalField.clear()
            time.sleep(1)
            discountTotalField.send_keys('6.333')
            time.sleep(1)
            utils.save_and_publish(driver)
            logging.info(
                '054. discountTotal with upto 3 decimal precision allowed from UI in line item object')
            # 055. Verify if user enters discountTotal with more than 3 decimal precision in line item object
            discountTotalField.clear()
            time.sleep(1)
            discountTotalField.send_keys('7.3333333')
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessDiscountTotalXpath))).is_displayed())
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessDiscountTotalXpath + "/../../.." + xpath.okButtonXpath))).click())
            discountTotalField.clear()
            time.sleep(1)
            discountTotalField.send_keys('6.3')
            logging.info('055. Validation message should display')
            # 056. Verify if user enters taxTotal with upto 3 decimal precision in line item object
            taxTotalField = driver.find_element(By.XPATH, '(' + xpath.taxTotalXpath + '/../../..//input)[1]')
            driver.execute_script("arguments[0].scrollIntoView();", taxTotalField)
            taxTotalField.clear()
            time.sleep(1)
            taxTotalField.send_keys('6.333')
            time.sleep(1)
            utils.save_and_publish(driver)
            logging.info(
                '056. taxTotal with upto 3 decimal precision allowed from UI in line item object')
            # 057. Verify if user enters taxTotal with more than 3 decimal precision in line item object
            taxTotalField.clear()
            time.sleep(1)
            taxTotalField.send_keys('7.3333333')
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessTaxTotalXpath))).is_displayed())
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessTaxTotalXpath + "/../../.." + xpath.okButtonXpath))).click())
            taxTotalField.clear()
            time.sleep(1)
            taxTotalField.send_keys('6.3')
            logging.info('057. Validation message should display')
            # 058. Verify if user enters taxAmount with upto 3 decimal precision in line item>>taxAdjustments
            taxAmountAdjustmentField = driver.find_element(By.XPATH, '(' + xpath.taxAmountXpath + '/../../..//input)[1]')
            driver.execute_script("arguments[0].scrollIntoView();", taxAmountAdjustmentField)
            taxAmountAdjustmentField.clear()
            time.sleep(1)
            taxAmountAdjustmentField.send_keys('6.333')
            time.sleep(1)
            utils.save_and_publish(driver)
            logging.info(
                '058. taxAmount with upto 3 decimal precision should be allowed from UI in line item object')
            # 059. Verify if user enters taxAmount with more than 3 decimal precision in line item>>taxAdjustments
            taxAmountAdjustmentField.clear()
            time.sleep(1)
            taxAmountAdjustmentField.send_keys('7.3333333')
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessTaxAmountXpath))).is_displayed())
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH,
                                              xpath.validationMessTaxAmountXpath + "/../../.." + xpath.okButtonXpath))).click())
            taxAmountAdjustmentField.clear()
            time.sleep(1)
            taxAmountAdjustmentField.send_keys('6.3')
            logging.info('059. Validation message should display')

            Success_List_Append("test_MKTPL_5503_floatingpoint_044_045_046_047_048_049_050_051_052_053_054_055_056_057_058_059",
                                "Verify if user enters unitPrice with upto and more than 3 decimal precision"
                                " in line item object", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5503_floatingpoint_044_045_046_047_048_049_050_051_052_053_054_055_056_057_058_059",
                            "Verify if user enters unitPrice with upto and more than 3 decimal precision in "
                            "line item object", "Fail")
        Failure_Cause_Append("test_MKTPL_5503_floatingpoint_044_045_046_047_048_049_050_051_052_053_054_055_056_057_058_059",
                             "Verify if user enters unitPrice with upto and more than 3 decimal precision in "
                             "line item object", e)
        raise e

def test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091():
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            # 060
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_LINE_ITEM_ID)
            utils.click_on_yes_no(driver)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Rate:"]/../../..//input')))
            rate = driver.find_element(By.XPATH, '//span[text()="Rate:"]/../../..//input')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 060 rate with upto 3 decimal precision should be allowed from UI in line item object")

            # 061
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Rate:"]/../../..//input')))
            rate = driver.find_element(By.XPATH, '//span[text()="Rate:"]/../../..//input')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for rate Field"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 061 Validation message should display")
            utils.close_All_Tabs(driver)
            # 062

            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_ID)
            utils.click_on_yes_no(driver)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//span[text()="Auth Amount "]/../../..//input)[1]')))
            Auth_Amount = driver.find_element(By.XPATH, '(//span[text()="Auth Amount "]/../../..//input)[1]')
            Auth_Amount.clear()
            Auth_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 062 authAmount with upto 3 decimal precision should be allowed from UI in order object")

            # 063
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Auth Amount "]/../../..//input)[1]')))
            Auth_Amount = driver.find_element(By.XPATH,
                                              '(//span[text()="Auth Amount "]/../../..//input)[1]')
            Auth_Amount.clear()
            Auth_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Auth Amount"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 063 Validation message should display")

            # 064
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Gross Total "]/../../..//input)[1]')))
            Gross_Total = driver.find_element(By.XPATH,
                                              '(//span[text()="Gross Total "]/../../..//input)[1]')
            Gross_Total.clear()
            Gross_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 064 grossTotal with upto 3 decimal precision should be allowed from UI in order object")

            # 065
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Gross Total "]/../../..//input)[1]')))
            Gross_Total = driver.find_element(By.XPATH,
                                              '(//span[text()="Gross Total "]/../../..//input)[1]')
            Gross_Total.clear()
            Gross_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Gross Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 065 Validation message should display")

            # 066
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Net Total "]/../../..//input)[1]')))
            netTotal = driver.find_element(By.XPATH,
                                           '(//span[text()="Net Total "]/../../..//input)[1]')
            netTotal.clear()
            netTotal.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 066 netTotal with upto 3 decimal precision should be allowed from UI in order object")

            # 067
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Net Total "]/../../..//input)[1]')))
            netTotal = driver.find_element(By.XPATH,
                                           '(//span[text()="Net Total "]/../../..//input)[1]')
            netTotal.clear()
            netTotal.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Net Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 067 Validation message should display")

            # 068
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Shipping Total "]/../../..//input)[1]')))
            Shipping_Total = driver.find_element(By.XPATH,
                                                 '(//span[text()="Shipping Total "]/../../..//input)[1]')
            Shipping_Total.clear()
            Shipping_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 068 shippingTotal with upto 3 decimal precision should be allowed from UI in order object")

            # 069
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Shipping Total "]/../../..//input)[1]')))
            Shipping_Total = driver.find_element(By.XPATH,
                                                 '(//span[text()="Shipping Total "]/../../..//input)[1]')
            Shipping_Total.clear()
            Shipping_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Shipping Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 069 Validation message should display")

            # 070
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Adjustment Total "]/../../..//input)[1]')))
            Adjustment_Total = driver.find_element(By.XPATH,
                                                   '(//span[text()="Adjustment Total "]/../../..//input)[1]')
            Adjustment_Total.clear()
            Adjustment_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 070 adjustmentTotal with upto 3 decimal precision should be allowed from UI in order object")

            # 071
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Adjustment Total "]/../../..//input)[1]')))
            Adjustment_Total = driver.find_element(By.XPATH,
                                                   '(//span[text()="Adjustment Total "]/../../..//input)[1]')
            Adjustment_Total.clear()
            Adjustment_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Adjustment Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 071 Validation message should display")

            # 072
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Total Tax Amount "]/../../..//input)[1]')))
            Total_Tax_Amount = driver.find_element(By.XPATH,
                                                   '(//span[text()="Total Tax Amount "]/../../..//input)[1]')
            Total_Tax_Amount.clear()
            Total_Tax_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 072 totalTaxAmount with upto 3 decimal precision should be allowed from UI in order object")

            # 073
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Total Tax Amount "]/../../..//input)[1]')))
            Total_Tax_Amount = driver.find_element(By.XPATH,
                                                   '(//span[text()="Total Tax Amount "]/../../..//input)[1]')
            Total_Tax_Amount.clear()
            Total_Tax_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Total Tax Amount"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 073 Validation message should display")

            # 074
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Tax Amount "]/../../..//input)[1]')))
            Tax_Amount = driver.find_element(By.XPATH,
                                             '(//span[text()="Tax Amount "]/../../..//input)[1]')
            Tax_Amount.clear()
            Tax_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 074 taxAmount with upto 3 decimal precision should be allowed from UI in order object")

            # 075
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Tax Amount "]/../../..//input)[1]')))
            Tax_Amount = driver.find_element(By.XPATH,
                                             '(//span[text()="Tax Amount "]/../../..//input)[1]')
            Tax_Amount.clear()
            Tax_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for tax Amount"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 075 Validation message should display")

            # 076
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Rate "]/../../..//input)[2]')))
            rate = driver.find_element(By.XPATH,
                                       '(//span[text()="Rate "]/../../..//input)[2]')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 076 rate with upto 3 decimal precision should be allowed from UI in order object")

            # 077
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Rate "]/../../..//input)[2]')))
            rate = driver.find_element(By.XPATH,
                                       '(//span[text()="Rate "]/../../..//input)[2]')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for rate Field"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 077 Validation message should display")

            utils.close_All_Tabs(driver)
            # 078
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_ORDER_SHIPMENT_ID)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Tax:"]/../../..//input)[1]')))
            Tax = driver.find_element(By.XPATH,
                                      '(//span[text()="Tax:"]/../../..//input)[1]')
            Tax.clear()
            Tax.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 078 tax with upto 3 decimal precision should be allowed from UI in shipment object")

            # 079
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Tax:"]/../../..//input)[1]')))
            Tax = driver.find_element(By.XPATH,
                                      '(//span[text()="Tax:"]/../../..//input)[1]')
            Tax.clear()
            Tax.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Tax"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 079 Validation message should display")

            # 080
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Rate:"]/../../..//input)[1]')))
            rate = driver.find_element(By.XPATH,
                                       '(//span[text()="Rate:"]/../../..//input)[1]')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 080 rate with upto 3 decimal precision should be allowed from UI in shipment object")

            # 081
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Rate:"]/../../..//input)[1]')))
            rate = driver.find_element(By.XPATH,
                                       '(//span[text()="Rate:"]/../../..//input)[1]')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Rate"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 081 Validation message should display")
            utils.close_All_Tabs(driver)

            # 082
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_ID)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Discount Amount:"]/../../..//input)[1]')))
            Discount_Amount = driver.find_element(By.XPATH,
                                                  '(//span[text()="Discount Amount:"]/../../..//input)[1]')
            Discount_Amount.clear()
            Discount_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 082 discountAmount with upto 3 decimal precision should be allowed from UI in order object")

            # 083
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Discount Amount:"]/../../..//input)[1]')))
            Discount_Amount = driver.find_element(By.XPATH,
                                                  '(//span[text()="Discount Amount:"]/../../..//input)[1]')
            Discount_Amount.clear()
            Discount_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for discount Amount"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 083 Validation message should display")

            # 084
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '//span[text()="Rate:"]/../../..//input')))
            Rate = driver.find_element(By.XPATH,
                                       '//span[text()="Rate:"]/../../..//input')
            Rate.clear()
            Rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 084 rate with upto 3 decimal precision should be allowed from UI in order object")

            # 085
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '//span[text()="Rate:"]/../../..//input')))
            Rate = driver.find_element(By.XPATH,
                                       '//span[text()="Rate:"]/../../..//input')
            Rate.clear()
            Rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for rate Field"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 085 Validation message should display")
            utils.close_All_Tabs(driver)

            # 086
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_LINE_ITEM_ID)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Discount Total "]/../../..//input)[1]')))
            Discount_Amount = driver.find_element(By.XPATH,
                                                  '(//span[text()="Discount Total "]/../../..//input)[1]')
            Discount_Amount.clear()
            Discount_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(
                " 086 discountAmount with upto 3 decimal precision should be allowed from UI in line item object")

            # 087

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Discount Total "]/../../..//input)[1]')))
            Discount_Amount = driver.find_element(By.XPATH,
                                                  '(//span[text()="Discount Total "]/../../..//input)[1]')
            Discount_Amount.clear()
            Discount_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Discount Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 087 Validation message should display")

            # 088
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '//span[text()="Rate:"]/../../..//input')))
            Rate = driver.find_element(By.XPATH,
                                       '//span[text()="Rate:"]/../../..//input')
            Rate.clear()
            Rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 088 rate with upto 3 decimal precision should be allowed from UI in line item object")

            # 089
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '//span[text()="Rate:"]/../../..//input')))
            Rate = driver.find_element(By.XPATH,
                                       '//span[text()="Rate:"]/../../..//input')
            Rate.clear()
            Rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for rate Field"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 089 Validation message should display")

            # 090
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Sale Price:"]/../../..//input)[1]')))
            Sale_Price = driver.find_element(By.XPATH,
                                             '(//span[text()="Sale Price:"]/../../..//input)[1]')
            Sale_Price.clear()
            Sale_Price.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 090 salePrice with upto 3 decimal precision should be allowed from UI in line item object")

            # 091
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Sale Price:"]/../../..//input)[1]')))
            Sale_Price = driver.find_element(By.XPATH,
                                             '(//span[text()="Sale Price:"]/../../..//input)[1]')
            Sale_Price.clear()
            Sale_Price.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Sale Price"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 091 Validation message should display")

            Success_List_Append(
                "test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091",
                "Verify if user enters rate with upto 3 decimal precision in line item>>taxAdjustments",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091",
            "Verify if user enters rate with upto 3 decimal precision in line item>>taxAdjustments",
            "Fail")
        Failure_Cause_Append(
            "test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091",
            "Verify if user enters rate with upto 3 decimal precision in line item>>taxAdjustments",
            e)

def test_MKTPL_4840_5043_itemkey_001_004_005():
    try:
        with utils.services_context_wrapper("test_MKTPL_4840_5043_itemkey_001_004_005.png") as driver:

            # 001
            RANDOM_NUMBER = "From_Automation_"+("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John", "Doe", "<EMAIL>", "ismail", "rangwala",
                                                   "456", "Manchester", "England", "12345", "GB",
                                                   "<EMAIL>", "", "sx", "13KNTV34", "CRE",
                                                   RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00", "random", "40643",
                                                   "DELIVERED", "CALL_CREW", "INTERNAL", "home_delivery",
                                                   "Outside demo", "Outside demo", "GA", "USD", ORDER_JWT_TOKEN, "POST",
                                                   CONSUME_ORDER_URL)
            assert 200 == api_response["code"]
            logging.info(RANDOM_NUMBER)

            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.search_by_id(driver, ORDER_FOLDER)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, xpath.selectClassName)))
            select_order = driver.find_element(By.NAME, xpath.selectClassName)
            select_order.send_keys("OnlineShopOrder")
            select_order.send_keys(Keys.ENTER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, xpath.queryName)))
            search_id = driver.find_element(By.NAME, xpath.queryName)
            search_id.send_keys(RANDOM_NUMBER)
            search_id.send_keys(Keys.ENTER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[contains(text(),"/Order/Cred Automation Airline")]')))
            path = driver.find_element(By.XPATH, '//div[contains(text(),"/Order/Cred Automation Airline")]')
            actions = ActionChains(driver)
            actions.double_click(path).perform()

            utils.click_on_yes_no(driver)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Children Grid"]')))
            driver.find_element(By.XPATH, '//span[text()="Children Grid"]').click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//div[text()="Please select a type"]/..//input)[2]')))
            select_type = driver.find_element(By.XPATH, '(//div[text()="Please select a type"]/..//input)[2]')
            select_type.send_keys("orderShipment")
            select_type.send_keys(Keys.ENTER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[contains(text(),"/Order/Cred Automation Airline")])[3]')))
            path1 = driver.find_element(By.XPATH, '(//div[contains(text(),"/Order/Cred Automation Airline")])[3]')
            actions = ActionChains(driver)
            actions.double_click(path1).perform()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'shipmentMode')))
            mode = driver.find_element(By.NAME, 'shipmentMode').get_attribute("value")

            assert "Home Delivery" == mode

            logging.info("001 the item key " " is successfully mapped to the corresponding line items in the shipment")

            # 004
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload_4840_5043("KRIS ⋮ JUNI12OR POLAR1 BEAR BY JELLYCAT®", RANDOM_NAME,
                                                             "", "Next Flight", ORDER_JWT_TOKEN, "POST",
                                                             CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => shipments [1] itemKeys data can not be empty if shipment Mode is Next Flight. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info('004 System should validate with error "item key is a mandatory field"')

            # 005
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload_4840_5043("KRIS ⋮ JUNI12OR POLAR1 BEAR BY JELLYCAT®", RANDOM_NAME,
                                                             "", "Home Delivery", ORDER_JWT_TOKEN, "POST",
                                                             CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => shipments [1] itemKeys data can not be empty if shipment Mode is Home Delivery. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info(' 005 System should validate with error "item key is a mandatory field"')

            Success_List_Append("test_MKTPL_4840_5043_itemkey_001_004_005",
                                "As a store admin, i should be able to manage shipping on the ground tool",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_4840_5043_itemkey_001_004_005",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_4840_5043_itemkey_001_004_005",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_563_Order_004_005_006():
    RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_563_Order_004_005_006.png") as driver:
            driver.maximize_window()
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.222", "33.444", "21.343", "200.332",
                                                                              "67.555", "12.123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            logging.info("POST - Value with upto 3 decimal precision is allowed from Post Order API")
            logging.info(externalId)
            # -------
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.manageshipping(driver, "yes")
            utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
             send_keys(externalId, Keys.ENTER))
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located(
                 (By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            orderId = driver.find_element(By.XPATH,
                                          '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            logging.info(orderId)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[@class="x-tab-close-btn"])[1]'))).click())  # close order folder
            # -------------------------------------
            utils.search_by_id(driver, orderId)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('orderShipment',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(" + xpath.homeDeliveryXpath + ")[2]"))))
            homeDelivery = driver.find_element(By.XPATH, "(" + xpath.homeDeliveryXpath + ")[2]")
            actions = ActionChains(driver)
            actions.double_click(homeDelivery).perform()
            # open1 = driver.find_element(By.XPATH, xpath.openXpathContains)
            # open1.click()
            utils.click_on_yes_no(driver)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'Inventory Location ')]//ancestor::label//parent::div//div[contains(@id,'trigger-picker')]"))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//ul[contains(@id,'picker-list')]//li)[last()]"))).click())
            # (WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, xpath.inventoryLocationFieldXpath))).send_keys(
            #     '9999999999', Keys.ENTER)) # Insufficient Inventory
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH,
                                                '//div[contains(text(),"Insufficient Inventory in the location for line item")]'))).is_displayed())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH,
                                                '//div[contains(text(),"Insufficient Inventory")]/../../../..//span[text()="OK"]'))).click())
            logging.info('006. Validation message display')
            driver.find_element(By.XPATH, xpath.inventoryLocationFieldXpath).clear()
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.inventoryLocationFieldXpath))).send_keys('Default Inventory From_Automation_vcRU', Keys.ENTER))
            logging.info('004. Admin should be able to select inventory location')
            utils.save_and_publish(driver)
            logging.info('005. Inventory location field should become ready only after admin clicks on save and '
                         'publish button and inventory should be deducted')

            Success_List_Append("test_MKTPL_563_Order_004_005_006",
                                "Verify the able to view line items details", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_563_Order_004_005_006",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_563_Order_004_005_006",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_515_Order_004_005_006():
    RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_515_Order_004_005_006.png") as driver:
            driver.maximize_window()
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.222", "33.444", "21.343", "200.332",
                                                                              "67.555", "12.123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            logging.info("POST - Value with upto 3 decimal precision is allowed from Post Order API")
            logging.info(externalId)
            # -------
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.manageshipping(driver, "yes")
            utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
             send_keys(externalId, Keys.ENTER))
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located(
                 (By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            orderId = driver.find_element(By.XPATH,
                                          '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            logging.info(orderId)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[@class="x-tab-close-btn"])[1]'))).click())  # close order folder
            # -------------------------------------
            utils.search_by_id(driver, orderId)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('orderShipment',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(" + xpath.homeDeliveryXpath + ")[2]"))))
            homeDelivery = driver.find_element(By.XPATH, "(" + xpath.homeDeliveryXpath + ")[2]")
            actions = ActionChains(driver)
            actions.double_click(homeDelivery).perform()
            # open1 = driver.find_element(By.XPATH, xpath.openXpathContains)
            # open1.click()
            # open1.click()
            utils.click_on_yes_no(driver)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'Inventory Location ')]//ancestor::label//parent::div//div[contains(@id,'trigger-picker')]"))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "(//ul[contains(@id,'picker-list')]//li)[last()]"))).click())
            # (WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, xpath.inventoryLocationFieldXpath))).send_keys(
            #     '9999999999', Keys.ENTER)) # Insufficient Inventory
            driver.find_element(By.XPATH, '//span[text()="Save & Publish"]').click()
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH,
                                                '//div[contains(text(),"Insufficient Inventory in the location for line item")]'))).is_displayed())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH,
                                                '//div[contains(text(),"Insufficient Inventory")]/../../../..//span[text()="OK"]'))).click())
            logging.info('006. Validation message display')
            driver.find_element(By.XPATH, xpath.inventoryLocationFieldXpath).clear()
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.inventoryLocationFieldXpath))).send_keys('Default Inventory From_Automation_vcRU', Keys.ENTER))
            logging.info('004. Admin should be able to select inventory location')
            utils.save_and_publish(driver)
            logging.info('005. Inventory location field should become ready only after admin clicks on save and publish button and inventory should be deducted')

            Success_List_Append("test_MKTPL_515_Order_004_005_006",
                                "Verify the able to view line items details", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_515_Order_004_005_006",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_515_Order_004_005_006",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091():
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            # 060
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_LINE_ITEM_ID)
            utils.click_on_yes_no(driver)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Rate:"]/../../..//input')))
            rate = driver.find_element(By.XPATH, '//span[text()="Rate:"]/../../..//input')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 060 rate with upto 3 decimal precision should be allowed from UI in line item object")

            # 061
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Rate:"]/../../..//input')))
            rate = driver.find_element(By.XPATH, '//span[text()="Rate:"]/../../..//input')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for rate Field"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 061 Validation message should display")
            utils.close_All_Tabs(driver)
            # 062

            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_ID)
            utils.click_on_yes_no(driver)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//span[text()="Auth Amount "]/../../..//input)[1]')))
            Auth_Amount = driver.find_element(By.XPATH, '(//span[text()="Auth Amount "]/../../..//input)[1]')
            Auth_Amount.clear()
            Auth_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 062 authAmount with upto 3 decimal precision should be allowed from UI in order object")

            # 063
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Auth Amount "]/../../..//input)[1]')))
            Auth_Amount = driver.find_element(By.XPATH,
                                              '(//span[text()="Auth Amount "]/../../..//input)[1]')
            Auth_Amount.clear()
            Auth_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Auth Amount"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 063 Validation message should display")

            # 064
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Gross Total "]/../../..//input)[1]')))
            Gross_Total = driver.find_element(By.XPATH,
                                              '(//span[text()="Gross Total "]/../../..//input)[1]')
            Gross_Total.clear()
            Gross_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 064 grossTotal with upto 3 decimal precision should be allowed from UI in order object")

            # 065
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//span[text()="Gross Total "]/../../..//input)[1]')))
            Gross_Total = driver.find_element(By.XPATH,
                                              '(//span[text()="Gross Total "]/../../..//input)[1]')
            Gross_Total.clear()
            Gross_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Gross Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 065 Validation message should display")

            # 066
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Net Total "]/../../..//input)[1]')))
            netTotal = driver.find_element(By.XPATH,
                                           '(//span[text()="Net Total "]/../../..//input)[1]')
            netTotal.clear()
            netTotal.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 066 netTotal with upto 3 decimal precision should be allowed from UI in order object")

            # 067
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Net Total "]/../../..//input)[1]')))
            netTotal = driver.find_element(By.XPATH,
                                           '(//span[text()="Net Total "]/../../..//input)[1]')
            netTotal.clear()
            netTotal.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Net Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 067 Validation message should display")

            # 068
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Shipping Total "]/../../..//input)[1]')))
            Shipping_Total = driver.find_element(By.XPATH,
                                                 '(//span[text()="Shipping Total "]/../../..//input)[1]')
            Shipping_Total.clear()
            Shipping_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 068 shippingTotal with upto 3 decimal precision should be allowed from UI in order object")

            # 069
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Shipping Total "]/../../..//input)[1]')))
            Shipping_Total = driver.find_element(By.XPATH,
                                                 '(//span[text()="Shipping Total "]/../../..//input)[1]')
            Shipping_Total.clear()
            Shipping_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Shipping Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 069 Validation message should display")

            # 070
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Adjustment Total "]/../../..//input)[1]')))
            Adjustment_Total = driver.find_element(By.XPATH,
                                                   '(//span[text()="Adjustment Total "]/../../..//input)[1]')
            Adjustment_Total.clear()
            Adjustment_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 070 adjustmentTotal with upto 3 decimal precision should be allowed from UI in order object")

            # 071
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Adjustment Total "]/../../..//input)[1]')))
            Adjustment_Total = driver.find_element(By.XPATH,
                                                   '(//span[text()="Adjustment Total "]/../../..//input)[1]')
            Adjustment_Total.clear()
            Adjustment_Total.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Adjustment Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 071 Validation message should display")

            # 072
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Total Tax Amount "]/../../..//input)[1]')))
            Total_Tax_Amount = driver.find_element(By.XPATH,
                                                   '(//span[text()="Total Tax Amount "]/../../..//input)[1]')
            Total_Tax_Amount.clear()
            Total_Tax_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 072 totalTaxAmount with upto 3 decimal precision should be allowed from UI in order object")

            # 073
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Total Tax Amount "]/../../..//input)[1]')))
            Total_Tax_Amount = driver.find_element(By.XPATH,
                                                   '(//span[text()="Total Tax Amount "]/../../..//input)[1]')
            Total_Tax_Amount.clear()
            Total_Tax_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Total Tax Amount"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 073 Validation message should display")

            # 074
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Tax Amount "]/../../..//input)[1]')))
            Tax_Amount = driver.find_element(By.XPATH,
                                             '(//span[text()="Tax Amount "]/../../..//input)[1]')
            Tax_Amount.clear()
            Tax_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 074 taxAmount with upto 3 decimal precision should be allowed from UI in order object")

            # 075
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Tax Amount "]/../../..//input)[1]')))
            Tax_Amount = driver.find_element(By.XPATH,
                                             '(//span[text()="Tax Amount "]/../../..//input)[1]')
            Tax_Amount.clear()
            Tax_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for tax Amount"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 075 Validation message should display")

            # 076
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Rate "]/../../..//input)[2]')))
            rate = driver.find_element(By.XPATH,
                                       '(//span[text()="Rate "]/../../..//input)[2]')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 076 rate with upto 3 decimal precision should be allowed from UI in order object")

            # 077
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Rate "]/../../..//input)[2]')))
            rate = driver.find_element(By.XPATH,
                                       '(//span[text()="Rate "]/../../..//input)[2]')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for rate Field"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 077 Validation message should display")

            utils.close_All_Tabs(driver)
            # 078
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_ORDER_SHIPMENT_ID)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Tax:"]/../../..//input)[1]')))
            Tax = driver.find_element(By.XPATH,
                                      '(//span[text()="Tax:"]/../../..//input)[1]')
            Tax.clear()
            Tax.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 078 tax with upto 3 decimal precision should be allowed from UI in shipment object")

            # 079
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Tax:"]/../../..//input)[1]')))
            Tax = driver.find_element(By.XPATH,
                                      '(//span[text()="Tax:"]/../../..//input)[1]')
            Tax.clear()
            Tax.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Tax"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 079 Validation message should display")

            # 080
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Rate:"]/../../..//input)[1]')))
            rate = driver.find_element(By.XPATH,
                                       '(//span[text()="Rate:"]/../../..//input)[1]')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 080 rate with upto 3 decimal precision should be allowed from UI in shipment object")

            # 081
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Rate:"]/../../..//input)[1]')))
            rate = driver.find_element(By.XPATH,
                                       '(//span[text()="Rate:"]/../../..//input)[1]')
            rate.clear()
            rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Rate"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 081 Validation message should display")
            utils.close_All_Tabs(driver)

            # 082
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_ID)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Discount Amount:"]/../../..//input)[1]')))
            Discount_Amount = driver.find_element(By.XPATH,
                                                  '(//span[text()="Discount Amount:"]/../../..//input)[1]')
            Discount_Amount.clear()
            Discount_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 082 discountAmount with upto 3 decimal precision should be allowed from UI in order object")

            # 083
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Discount Amount:"]/../../..//input)[1]')))
            Discount_Amount = driver.find_element(By.XPATH,
                                                  '(//span[text()="Discount Amount:"]/../../..//input)[1]')
            Discount_Amount.clear()
            Discount_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for discount Amount"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 083 Validation message should display")

            # 084
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '//span[text()="Rate:"]/../../..//input')))
            Rate = driver.find_element(By.XPATH,
                                       '//span[text()="Rate:"]/../../..//input')
            Rate.clear()
            Rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 084 rate with upto 3 decimal precision should be allowed from UI in order object")

            # 085
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '//span[text()="Rate:"]/../../..//input')))
            Rate = driver.find_element(By.XPATH,
                                       '//span[text()="Rate:"]/../../..//input')
            Rate.clear()
            Rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for rate Field"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 085 Validation message should display")
            utils.close_All_Tabs(driver)

            # 086
            utils.search_by_id(driver, DEDICATED_FAILED_ORDER_LINE_ITEM_ID)

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Discount Total "]/../../..//input)[1]')))
            Discount_Amount = driver.find_element(By.XPATH,
                                                  '(//span[text()="Discount Total "]/../../..//input)[1]')
            Discount_Amount.clear()
            Discount_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(
                " 086 discountAmount with upto 3 decimal precision should be allowed from UI in line item object")

            # 087

            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Discount Total "]/../../..//input)[1]')))
            Discount_Amount = driver.find_element(By.XPATH,
                                                  '(//span[text()="Discount Total "]/../../..//input)[1]')
            Discount_Amount.clear()
            Discount_Amount.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Discount Total"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 087 Validation message should display")

            # 088
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '//span[text()="Rate:"]/../../..//input')))
            Rate = driver.find_element(By.XPATH,
                                       '//span[text()="Rate:"]/../../..//input')
            Rate.clear()
            Rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 088 rate with upto 3 decimal precision should be allowed from UI in line item object")

            # 089
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '//span[text()="Rate:"]/../../..//input')))
            Rate = driver.find_element(By.XPATH,
                                       '//span[text()="Rate:"]/../../..//input')
            Rate.clear()
            Rate.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for rate Field"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 089 Validation message should display")

            # 090
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(3)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Sale Price:"]/../../..//input)[1]')))
            Sale_Price = driver.find_element(By.XPATH,
                                             '(//span[text()="Sale Price:"]/../../..//input)[1]')
            Sale_Price.clear()
            Sale_Price.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath))).is_displayed()

            logging.info(" 090 salePrice with upto 3 decimal precision should be allowed from UI in line item object")

            # 091
            RANDOM_NUMBER = ("7" + "." + "".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     '(//span[text()="Sale Price:"]/../../..//input)[1]')))
            Sale_Price = driver.find_element(By.XPATH,
                                             '(//span[text()="Sale Price:"]/../../..//input)[1]')
            Sale_Price.clear()
            Sale_Price.send_keys(RANDOM_NUMBER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '//div[text()="Allowed upto 3 decimal places for Sale Price"]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.okButtonXpath).click()

            logging.info(" 091 Validation message should display")

            Success_List_Append(
                "test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091",
                "Verify if user enters rate with upto 3 decimal precision in line item>>taxAdjustments",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append(
            "test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091",
            "Verify if user enters rate with upto 3 decimal precision in line item>>taxAdjustments",
            "Fail")
        Failure_Cause_Append(
            "test_MKTPL_5503_floatingpoint_060_061_062_063_064_065_066_067_068_069_070_071_072_073_074_075_076_077_078_079_080_081_082_083_084_085_086_087_088_089_090_091",
            "Verify if user enters rate with upto 3 decimal precision in line item>>taxAdjustments",
            e)
        raise e

def test_MKTPL_519_Ordershipping_001_002_003_004_005():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_519_Ordershipping_001_002_003_004_005.png"
        ) as driver:
            login_user = ""
            Year = "2024"
            Month = "05"
            Date = "24"
            Next_Date = "25"

            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.orderShippingInformation)))
            driver.find_element(By.XPATH, xpath.orderShippingInformation).click()

            # 001. "Verify by clicking on the 'Order Shipping Information' icon at left side bar"
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.store)))
            data_type = driver.find_element(By.NAME, xpath.store)
            data_type.click()
            data_type.send_keys(DEDICATED_STORE)
            time.sleep(2)
            # # data_type.send_keys(Keys.ENTER)
            # driver.find_element(By.XPATH, xpath.credAutomationStoreListing).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.store)))
            # Store
            assert driver.find_element(By.XPATH, '//span[contains(text(), "Store")]//ancestor::label//following-sibling::div//input[@name="store"]')
            # From Date
            assert driver.find_element(By.XPATH, '//span[text()="From Date:"]//ancestor::label//following-sibling::div//input[@name="from_date"]')
            # To Date
            assert driver.find_element(By.XPATH, '//span[text()="To Date:"]//ancestor::label//following-sibling::div//input[@name="to_date"]')
            logging.info("Verified User able to redirect to the Order Shipping screen and is able to select the store, from date and to date")

            # 002. "Verify the store list in the Order Shipping screen"
            # Store
            driver.find_element(By.XPATH, '//input[@name="store"]')
            # From Date
            assert driver.find_element(By.XPATH, f'//li[text()="{DEDICATED_STORE}"]')

            utils.search_by_id(driver, DEDICATED_STORE_ID)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.configuration_tab))
            )
            driver.find_element(By.XPATH, xpath.configuration_tab).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Manage Shipping "]'))
            )
            manage_shipping_input = driver.find_element(By.XPATH, '//span[text()="Manage Shipping "]//ancestor::label//following-sibling::div//input[@name="manageShipping"]')
            # Manage shipping Yes
            logging.info(manage_shipping_input.get_attribute("value"))
            assert manage_shipping_input.get_attribute("value") == "Yes"
            logging.info("Verified Store list include that store whose manage shipping is Yes")

            # close store tab
            utils.close_All_Tabs(driver)

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.orderShippingInformation)))
            driver.find_element(By.XPATH, xpath.orderShippingInformation).click()

            # 003. "Verify the grid view in the Order Shipping screen"
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.store)))
            data_type = driver.find_element(By.NAME, xpath.store)
            data_type.click()
            data_type.send_keys(DEDICATED_STORE)
            time.sleep(2)
            driver.find_element(By.XPATH, f'//li[text()="{DEDICATED_STORE}"]').click()
            # From Date
            from_date_input = driver.find_element(By.XPATH, '//input[@name="from_date"]')
            from_date_input.send_keys(utils.datetime.datetime.today().strftime("%m/%d/%Y"))
            time.sleep(2)
            from_date_input.send_keys(Keys.TAB)
            # To Date
            to_date_input = driver.find_element(By.XPATH, '//input[@name="to_date"]')
            to_date_input.send_keys(f"{Month}/{Next_Date}/{Year}")
            time.sleep(2)
            to_date_input.send_keys(Keys.TAB)
            # click on Submit
            driver.find_element(By.XPATH, '//span[text()="Submit"]').click()
            # WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="OK"]')))
            # driver.find_element(By.XPATH, '//span[text()="OK"]').click()
            # time.sleep(2)
            # Order ID
            assert driver.find_element(By.XPATH, '//span[text()="Order ID"]')
            # # Fulfillment ID
            # assert driver.find_element(By.XPATH, '//span[text()="Order ID"]')
            # Order Line item
            assert driver.find_element(By.XPATH, '//span[text()="Order Line item"]')
            # Shipping ID
            assert driver.find_element(By.XPATH, '//span[text()="Shipping ID"]')
            # Tracking ID
            assert driver.find_element(By.XPATH, '//span[text()="Tracking ID"]')
            # Order Line items status
            assert driver.find_element(By.XPATH, '//span[text()="Order Line items status"]')
            logging.info("Verified User able to view the order shipping information in the grid and View the following attributes in the grid: \
                            Order ID, Fulfillment ID, Order Line item, Shipping ID, Tracking ID and Order Line items status")

            # 004. "Verify the orders in the list"
            try:
                driver.find_element(By.XPATH, '//span[text()="Order ID"]')
            except:
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.store)))
                data_type = driver.find_element(By.NAME, xpath.store)
                data_type.click()
                data_type.send_keys(DEDICATED_STORE)
                time.sleep(2)
                driver.find_element(By.XPATH, f'//li[text()="{DEDICATED_STORE}"]').click()
                # From Date
                from_date_input = driver.find_element(By.XPATH, '//input[@name="from_date"]')
                from_date_input.send_keys(utils.datetime.datetime.today().strftime("%m/%d/%Y"))
                time.sleep(2)
                from_date_input.send_keys(Keys.TAB)
                # To Date
                to_date_input = driver.find_element(By.XPATH, '//input[@name="to_date"]')
                to_date_input.send_keys(f"{Month}/{Next_Date}/{Year}")
                time.sleep(2)
                to_date_input.send_keys(Keys.TAB)
                # click on Submit
                driver.find_element(By.XPATH, '//span[text()="Submit"]').click()
            page_count = int((driver.find_element(By.XPATH, '(//div[contains(text(), "of ")])[1]').text).split(" ")[1])
            for i in range(page_count):
                orderList = []
                orderID_list = driver.find_elements(By.XPATH, '//div[@id="custumGrid"]//div[contains(@class, "x-grid-item-container")]//tr//td[1]//div')
                for orderID in orderID_list:
                    orderList.append(orderID.text)
                assert DEDICATED_DIFFERENT_STORE_ORDER_ID not in orderList
                driver.find_element(By.XPATH, '(//a[@data-qtip="Next Page"]//span[contains(@class, "page-next")])[2]').click()
            logging.info("3")
            logging.info("Verified Only orders that are associated with selected store and fall between from & to date are coming in the list")

            # 005. "Verify by clicking on the download button"
            driver.find_element(By.XPATH, '//span[text()="Download"]').click()
            assert driver.find_element(By.XPATH, '//span[text()="Download"]')
            if utils.os.name == "posix":
                home = utils.os.path.expanduser("~")
                os_download_path = utils.os.path.join(home, "Downloads")
                logging.info(os_download_path)
                files = utils.os.listdir(os_download_path)
                paths = [utils.os.path.join(os_download_path, basename) for basename in files]
                download_file_path = max(paths, key=os.path.getctime)
                logging.info(download_file_path)
                assert "OrderShippingInfo" in download_file_path
            elif utils.os.name == "nt":
                download_file_path = LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE
                assert download_file_path
            # To Date
            # assert driver.find_element(By.XPATH, '//span[text()="To Date:"]//ancestor::label//following-sibling::div//input[@name="to_date"]')
            logging.info("Verified user able to download the order shipping information in the CSV")

            time.sleep(10)

            Success_List_Append("test_MKTPL_519_Ordershipping_001_002_003_004_005",
                                "Verify by clicking on the 'Order Shipping Information' icon at left side bar",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_519_Ordershipping_001_002_003_004_005",
                            "Verify by clicking on the 'Order Shipping Information' icon at left side bar",
                            "Fail",)
        Failure_Cause_Append("test_MKTPL_519_Ordershipping_001_002_003_004_005",
                             "Verify by clicking on the 'Order Shipping Information' icon at left side bar",
                             e,)
        raise e

def test_MKTPL_497_Ordershipping_001_002_003_004_005():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_497_Ordershipping_001_002_003_004_005.png"
        ) as driver:
            login_user = ""
            Year = "2024"
            Month = "05"
            Date = "24"
            Next_Date = "25"

            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            # 001. "Verify if manage shipping is No for the store"
            try:
                assert not driver.find_element(By.XPATH, xpath.orderShippingInformation)
            except:
                pass
            logging.info("Verified Order Shipping Information icon at left side bar is hidden")

            # 002. "Verify by clicking on the "Order Shipping Information" icon at left side bar"
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.orderShippingInformation)))
            driver.find_element(By.XPATH, xpath.orderShippingInformation).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="From Date:"]')))
            # From Date
            assert driver.find_element(By.XPATH, '//span[text()="From Date:"]//ancestor::label//following-sibling::div//input[@name="from_date"]')
            # To Date
            assert driver.find_element(By.XPATH, '//span[text()="To Date:"]//ancestor::label//following-sibling::div//input[@name="to_date"]')
            logging.info("Verified User is redirect to the Order Shipping screen and user is able to select the from date and to date")

            utils.search_by_id(driver, DEDICATED_STORE_ID)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.configuration_tab))
            )
            driver.find_element(By.XPATH, xpath.configuration_tab).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Manage Shipping "]'))
            )
            manage_shipping_input = driver.find_element(By.XPATH, '//span[text()="Manage Shipping "]//ancestor::label//following-sibling::div//input[@name="manageShipping"]')
            # Manage shipping Yes
            logging.info(manage_shipping_input.get_attribute("value"))
            assert manage_shipping_input.get_attribute("value") == "Yes"

            # close store tab
            utils.close_All_Tabs(driver)

            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.orderShippingInformation)))
            driver.find_element(By.XPATH, xpath.orderShippingInformation).click()

            # 003. "Verify the grid view in the Order Shipping screen"
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="From Date:"]')))

            # From Date
            from_date_input = driver.find_element(By.XPATH, '//input[@name="from_date"]')
            from_date_input.send_keys(utils.datetime.datetime.today().strftime("%m/%d/%Y"))
            time.sleep(2)
            from_date_input.send_keys(Keys.TAB)
            # To Date
            to_date_input = driver.find_element(By.XPATH, '//input[@name="to_date"]')
            to_date_input.send_keys(f"{Month}/{Next_Date}/{Year}")
            time.sleep(2)
            to_date_input.send_keys(Keys.TAB)
            # click on Submit
            driver.find_element(By.XPATH, '//span[text()="Submit"]').click()
            # Order ID
            assert driver.find_element(By.XPATH, '//span[text()="Order ID"]')
            # # Fulfillment ID
            # assert driver.find_element(By.XPATH, '//span[text()="Order ID"]')
            # Order Line item
            assert driver.find_element(By.XPATH, '//span[text()="Order Line item"]')
            # Shipping ID
            assert driver.find_element(By.XPATH, '//span[text()="Shipping ID"]')
            # Tracking ID
            assert driver.find_element(By.XPATH, '//span[text()="Tracking ID"]')
            # Order Line items status
            assert driver.find_element(By.XPATH, '//span[text()="Order Line items status"]')
            logging.info("Verified User able to view the order shipping information in the grid and View the following attributes in the grid: \
                            Order ID, Fulfillment ID, Order Line item, Shipping ID, Tracking ID and Order Line items status")

            # 004. "Verify the orders in the list"
            try:
                driver.find_element(By.XPATH, '//span[text()="Order ID"]')
            except:
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.store)))
                data_type = driver.find_element(By.NAME, xpath.store)
                data_type.click()
                data_type.send_keys(DEDICATED_STORE)
                time.sleep(2)
                driver.find_element(By.XPATH, f'//li[text()="{DEDICATED_STORE}"]').click()
                # From Date
                from_date_input = driver.find_element(By.XPATH, '//input[@name="from_date"]')
                from_date_input.send_keys(utils.datetime.datetime.today().strftime("%m/%d/%Y"))
                time.sleep(2)
                from_date_input.send_keys(Keys.TAB)
                # To Date
                to_date_input = driver.find_element(By.XPATH, '//input[@name="to_date"]')
                to_date_input.send_keys(f"{Month}/{Next_Date}/{Year}")
                time.sleep(2)
                to_date_input.send_keys(Keys.TAB)
                # click on Submit
                driver.find_element(By.XPATH, '//span[text()="Submit"]').click()
            page_count = int((driver.find_element(By.XPATH, '(//div[contains(text(), "of ")])[1]').text).split(" ")[1])
            for i in range(page_count):
                orderList = []
                orderID_list = driver.find_elements(By.XPATH, '//div[@id="custumGrid"]//div[contains(@class, "x-grid-item-container")]//tr//td[1]//div')
                for orderID in orderID_list:
                    orderList.append(orderID.text)
                assert DEDICATED_DIFFERENT_STORE_ORDER_ID not in orderList
                driver.find_element(By.XPATH, '//a[@data-qtip="Next Page"]//span[contains(@class, "page-next")]').click()
            logging.info("Verified Only orders that are associated with selected store and fall between from & to date come in the list")

            # 005. "Verify by clicking on the download button"
            driver.find_element(By.XPATH, '//span[text()="Download"]').click()
            assert driver.find_element(By.XPATH, '//span[text()="Download"]')
            if utils.os.name == "posix":
                home = utils.os.path.expanduser("~")
                os_download_path = utils.os.path.join(home, "Downloads")
                logging.info(os_download_path)
                files = utils.os.listdir(os_download_path)
                paths = [utils.os.path.join(os_download_path, basename) for basename in files]
                download_file_path = max(paths, key=os.path.getctime)
                logging.info(download_file_path)
                assert "OrderShippingInfo" in download_file_path
            elif utils.os.name == "nt":
                download_file_path = LOCAL_DOWNLOADABLE_FILE_PATH_AIRLINE_CATEGORY_SAMPLE
                assert download_file_path
            # To Date
            # assert driver.find_element(By.XPATH, '//span[text()="To Date:"]//ancestor::label//following-sibling::div//input[@name="to_date"]')
            logging.info("Verified user able to download the order shipping information in the CSV")

            time.sleep(10)

            Success_List_Append("test_MKTPL_497_Ordershipping_001_002_003_004_005",
                                "Verify if manage shipping is No for the store",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_497_Ordershipping_001_002_003_004_005",
                            "Verify if manage shipping is No for the store",
                            "Fail",)
        Failure_Cause_Append("test_MKTPL_497_Ordershipping_001_002_003_004_005",
                             "Verify if manage shipping is No for the store",
                             e,)
        raise e

# Need to move this test case to P1.2
def test_MKTPL_1861_PDF_001_002():
    try:
        with utils.services_context_wrapper("test_MKTPL_1861_PDF_001_002.png") as driver:
            driver.maximize_window()
            role = ["Login_Pac_Admin", "Login_Store"]
            for i in role:
                if i is "Login_Pac_Admin":
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("PAC ADMIN")
                elif i is "Login_Store":
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                utils.wait_for_style_attribute(driver, 40)
                utils.search_by_id(driver,  DEDICATED_ORDER_1_ID)
                WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.printEReceipt)))
                driver.find_element(By.XPATH, xpath.printEReceipt).click()
                logging.info(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_RECEIPT)
                time.sleep(10)
                assert "OrderReceipt_" in LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_RECEIPT
                os.remove(LOCAL_DOWNLOADABLE_FILE_PATH_ORDER_RECEIPT)
                logging.info('able to view and download the E-Receipt that is auto-generated on the ground '
                            'tool on the Order in the PDF format')
                #Logout
                driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                logging.info("Logged out")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))

            Success_List_Append("test_MKTPL_1861_PDF_001_002",
                                "Verify by clicking on the Print E-Receipt button", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1861_PDF_001_002",
                            "Verify by clicking on the Print E-Receipt button", "Fail")
        Failure_Cause_Append("test_MKTPL_1861_PDF_001_002",
                             "Verify by clicking on the Print E-Receipt button",
                             e)
        raise e

