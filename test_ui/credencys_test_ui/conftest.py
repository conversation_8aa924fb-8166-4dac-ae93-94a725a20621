import os, pytest, logging
from jamatest import jamatest
from credencys_test_ui.credencys_project_configs.Report_Mail import Error_Table_Generation
from credencys_test_ui.credencys_project_configs.Report_Mail import TestReport_Generation
from credencys_test_ui.credencys_project_configs.Report_Mail import Summary_Table_Formation
from credencys_test_ui.credencys_project_configs.Report_Mail import Send_Mail
from credencys_test_ui.credencys_project_configs.Delete_Automation_Data import Deletion_Automation_Data

# from credencys_test_ui.credencys_project_configs.Report_Mail import *

@pytest.fixture(scope="session", autouse=True)
def jama_init(request):
    print(f"{jama_init.__name__}: Checking if JAMA is being used or not...")
    CLIENT_ID = os.getenv("JAMA_CLIENT_ID")
    CLIENT_SECRET = os.getenv("JAMA_CLIENT_SECRET")
    TEST_PLAN_ID = 17125021

    if TEST_PLAN_ID and CLIENT_ID is not None and os.getenv("JAMA_ENABLED") == "true":
        cycle_id = os.getenv("TEST_CYCLE_ID")
        # cycle_id = "16463243" #it can be gathered inspecting the page for an specific commit
        print(f"JAMA_CLIENT: {cycle_id}")
        jamatest.init(CLIENT_ID, CLIENT_SECRET, TEST_PLAN_ID, cycle_id)
        request.addfinalizer(publish_results)

def publish_results():
    if os.getenv("JAMA_ENABLED") == "true":
        jamatest.publish()

def pytest_addoption(parser):
    parser.addoption("--env", action="store")

def pytest_configure(config):
    os.environ["env"] = config.getoption("env")


###### These automation is taking random screenshots... need to investigate
# from credencys_test_ui.credencys_project_configs.Delete_Automation_Data import *
def pytest_unconfigure(config):
    Deletion_Automation_Data()
    Error_Table_Generation()
    TestReport_Generation()
    Summary_Table_Formation()
    Send_Mail()
    logging.info("finally")

# ----------------------------------------------PDC conftest

# from credencys_test_ui.PDC.page_object.env_login import logger

# from credencys_test_ui.PDC.page_object.env import get_environment
# from selenium.webdriver.chrome.options import Options

# def pytest_addoption(parser):
#     parser.addoption("--env", action="store", default="dev", help="Specify the environment")

# @pytest.fixture
# def environment(request):
#     return request.config.getoption("--env")
#
# @pytest.fixture
# def e(environment):
#     return get_environment(environment)

# # ----for manual run----
# @pytest.fixture
# def driver():
#     options = Options()
#     options.add_argument("start-maximized")
#     driver = webdriver.Chrome()
#     yield driver


# ----for pipeline setup----
# def setup_driver():
#     try:
#         c = Options()
#         # not working - size issue
#         # c.add_argument("--start-maximized")
#
#         c.add_argument("--window-size=1920,1080")
#         driver = webdriver.Remote(
#             command_executor="http://selenium__standalone-chrome:4444/wd/hub", options=c
#         )
#         return driver
#     except WebDriverException as e:
#         print("An error occurred while initializing the WebDriver")
#         # logging.info("An error occurred while initializing the WebDriver")
#         return None
#
# @pytest.fixture
# def driver():
#     return setup_driver()
#
