from random import randint
import credencys_test_ui.xpath as xpath
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import credencys_test_ui.credencys_project_configs.utils as utils
import requests, json, string, random, time, logging, os
from selenium.webdriver.support.wait import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from credencys_test_ui.credencys_project_configs.Report_Mail import Success_List_Append, Failure_Cause_Append

if os.environ["env"] == "QA":
    from credencys_test_ui.credencys_project_configs.constants_qa import *
elif os.environ["env"] == "DEV":
    from credencys_test_ui.credencys_project_configs.constants_dev import *
elif os.environ["env"] == "TRIAL":
    from credencys_test_ui.credencys_project_configs.constants_trial import *
elif os.environ["env"] == "TEST":
    from credencys_test_ui.credencys_project_configs.constants_test import *

def test_MKTPL_1608_Manageshipping_001_002_003_004_005_015():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1608_Manageshipping_001_002_003_004_005_015.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.configuration_tab)))
            driver.find_element(By.XPATH, xpath.configuration_tab).click()
            manageShippingField = driver.find_element(By.XPATH, xpath.manageShippingXpath).is_displayed()
            assert manageShippingField
            logging.info('User see the added new field "Mark Manage Shipping"')
            valueYes = driver.find_element(By.XPATH, '//input[@name="manageStock" and @value ="Yes"]').is_displayed()
            assert valueYes
            manageShipping = driver.find_element(By.NAME, xpath.manageShippingName)
            manageShipping.clear()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.validationMessManageShippingXpath))).is_displayed()
            logging.info("Validation shown for mandatory field - manageshipping")
            logging.info('User see the added new field "Mark Manage Shipping" with default select "YES" is store config')
            Success_List_Append("test_MKTPL_1608_Manageshipping_001_002_003_004_005_015",
                                'Verify the added new field "Mark Manage Shipping"',"Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1608_Manageshipping_001_002_003_004_005_015",
                            'Verify the added new field "Mark Manage Shipping"',"Fail",)
        Failure_Cause_Append("test_MKTPL_1608_Manageshipping_001_002_003_004_005_015",
                             'Verify the added new field "Mark Manage Shipping"',e,)
        raise e

def test_MKTPL_1608_Manageshipping_006_007():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1608_Manageshipping_006_007.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            configuration_tab = utils.store_configuration(driver, DEDICATED_STORE_ID)
            assert configuration_tab
            # 006. Verify the option for the select boolean data type like "Yes", "No"
            driver.find_element(By.XPATH, '('+xpath.manageShippingXpath+'/../..//div)[2]').click()
            yes_option = driver.find_element(By.XPATH, xpath.listYesOptionXpath+"[1]").is_displayed()
            no_option = driver.find_element(By.XPATH, xpath.listNoOptionXpath+"[1]").is_displayed()
            assert [yes_option, no_option]
            logging.info('User have option for the select boolean data type like "Yes", "No"')
            # 007. Check that the action when select "Yes" option
            driver.find_element(By.XPATH, xpath.manageShippingXpath).clear()
            driver.find_element(By.XPATH, xpath.manageShippingXpath).send_keys("Yes", Keys.TAB)
            verify_value = driver.find_element(By.XPATH, xpath.manageShippingXpath).get_attribute("value")
            assert "Yes" == verify_value
            utils.search_by_id(driver, DEDICATED_ORDER_1_LINE_ITEM_ID)
            val = driver.find_element(By.NAME,"status").get_attribute("value")
            if val == "Pending":
                logging.info("Status is Pending")
                driver.find_element(By.NAME,"status").clear()
                driver.find_element(By.NAME,"status").send_keys("Delivered")
                driver.find_element(By.NAME,"status").send_keys(Keys.ENTER)
                utils.save_and_publish(driver)
                updated_val = driver.find_element(By.NAME,"status").get_attribute("value")
                assert updated_val == "Delivered"
                logging.info("Status changed to Delivered")
            elif val == "Delivered":
                logging.info("Status is Delivered")
                driver.find_element(By.NAME,"status").clear()
                driver.find_element(By.NAME,"status").send_keys("Pending")
                driver.find_element(By.NAME,"status").send_keys(Keys.ENTER)
                utils.save_and_publish(driver)
                updated_val = driver.find_element(By.NAME,"status").get_attribute("value")
                assert updated_val == "Pending"
                logging.info("Status changed to Pending")
            logging.info('User is able to make sure action when select "Yes"')
            configuration_tab = utils.store_configuration(driver, DEDICATED_STORE_ID)
            assert configuration_tab
            # # 012. Check that the action when select "No" option
            # driver.find_element(By.XPATH, xpath.manageShippingXpath).clear()
            # driver.find_element(By.XPATH, xpath.manageShippingXpath).send_keys("No", Keys.TAB)
            # verify_value = driver.find_element(By.XPATH, xpath.manageShippingXpath).get_attribute("value")
            # assert "No" == verify_value
            # utils.search_by_id(driver, DEDICATED_ORDER_1_LINE_ITEM_ID)
            # val = driver.find_element(By.NAME,"status").get_attribute("value")
            # if val == "Pending":
            #     logging.info("Status is Pending")
            #     driver.find_element(By.NAME,"status").clear()
            #     driver.find_element(By.NAME,"status").send_keys("Delivered")
            #     driver.find_element(By.NAME,"status").send_keys(Keys.ENTER)
            #     utils.save_and_publish(driver)
            #     updated_val = driver.find_element(By.NAME,"status").get_attribute("value")
            #     assert updated_val == "Pending"
            #     logging.info("Status is still Pending")
            # elif val == "Delivered":
            #     logging.info("Status is Delivered")
            #     driver.find_element(By.NAME,"status").clear()
            #     driver.find_element(By.NAME,"status").send_keys("Pending")
            #     driver.find_element(By.NAME,"status").send_keys(Keys.ENTER)
            #     utils.save_and_publish(driver)
            #     updated_val = driver.find_element(By.NAME,"status").get_attribute("value")
            #     assert updated_val == "Delivered"
            #     logging.info("Status is still Delivered")
            # logging.info('User is able to make sure action when select "No"')
            Success_List_Append("test_MKTPL_1608_Manageshipping_006_007","Verify the the manage shipping setting","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1608_Manageshipping_006_007","Verify the the manage shipping setting","Fail",)
        Failure_Cause_Append("test_MKTPL_1608_Manageshipping_006_007", "Verify the the manage shipping setting",e,)
        raise e

def test_MKTPL_1607_Manageshipping_001_002_003_004_005_006_007_009_011():
    try:
        with utils.services_context_wrapper("test_MKTPL_1607_Manageshipping_001_002_003_004_005_006_007_009_011.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.search_by_id(driver, DEDICATED_STORE_ID)
            # 001 002 003 004
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Configuration"]')))
            driver.find_element(By.XPATH, '//span[text()="Configuration"]').click()

            assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//span[text()="Manage Shipping "]'))).is_displayed()

            logging.info(' 001...004 successfuly ')
            time.sleep(3)
            value = driver.find_element(By.XPATH, xpath.manageShippingXpath).get_attribute("value")
            assert "Yes" in value or "No" in value

            logging.info(' 005 User see the added new field "Mark Manage Shipping" with default select "YES" is store config')

            # 006. Verify the option for the select boolean data type like "Yes", "No"
            driver.find_element(By.XPATH, '(' + xpath.manageShippingXpath + '/../..//div)[2]').click()
            yes_option = driver.find_element(By.XPATH, xpath.listYesOptionXpath + "[1]").is_displayed()
            no_option = driver.find_element(By.XPATH, xpath.listNoOptionXpath + "[1]").is_displayed()
            assert [yes_option, no_option]

            logging.info(' 006 User have option for the select boolean data type like "Yes", "No"')

            # 007. Check that the action when select "Yes" option
            driver.find_element(By.XPATH, xpath.manageShippingXpath).send_keys("Yes")
            time.sleep(1)
            driver.find_element(By.XPATH, xpath.manageShippingXpath).send_keys(Keys.ENTER)
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveSuccessfullyMessageXpath)))
            utils.click_on_yes_no(driver)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.manageShippingXpath)))
            verify_value = driver.find_element(By.XPATH, xpath.manageShippingXpath).get_attribute("value")
            assert "Yes" in verify_value
            logging.info('007 User is able to make sure action when select "Yes"')

            # 009
            manageShipping = driver.find_element(By.XPATH, xpath.manageShippingXpath)
            manageShipping.clear()
            manageShipping.send_keys("Yes", Keys.TAB)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()

            utils.search_by_id(driver, DEDICATED_ORDER_1_LINE_ITEM_ID)
            utils.click_on_yes_no(driver)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Order Shipment"]')))
            driver.find_element(By.XPATH, '//span[text()="Order Shipment"]').click()
            utils.click_on_yes_no(driver)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[@class="x-grid-cell-inner x-grid-cell-inner-action-col"]')))
            driver.find_element(By.XPATH, '//div[@class="x-grid-cell-inner x-grid-cell-inner-action-col"]').click()

            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'trackingId')))
            trackingId = driver.find_element(By.NAME, 'trackingId')
            trackingId.send_keys(RANDOM_NUMBER)
            trackingId.clear()

            logging.info(' 009 Select "Yes" able to Update Tracking ID via CSV and on order line item UI')
            utils.close_All_Tabs(driver)

            # 011
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            utils.click_on_yes_no(driver)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Configuration"]')))
            driver.find_element(By.XPATH, '//span[text()="Configuration"]').click()

            manageShipping = driver.find_element(By.XPATH, xpath.manageShippingXpath)
            manageShipping.clear()
            manageShipping.send_keys("No", Keys.TAB)
            verify_value = driver.find_element(By.XPATH, xpath.manageShippingXpath).get_attribute("value")
            assert "No" in verify_value
            logging.info('011 User is able to make sure action when select "No"')



            Success_List_Append("test_MKTPL_1607_Manageshipping_001_002_003_004_005_006_007_009_011",
                                "As a store admin, i should be able to manage shipping on the ground tool",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1607_Manageshipping_001_002_003_004_005_006_007_009_011",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_1607_Manageshipping_001_002_003_004_005_006_007_009_011",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_1607_Manageshipping_010_013_014():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1607_Manageshipping_010_013_014.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.managestock(driver, 'yes')
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(1)
            assert driver.find_element(By.XPATH, '//span[text()="Download Sample File Of TrackingInfo"]').is_displayed()
            logging.info('Select "Yes" able to Download Shipping Manifesto')
            #
            utils.close_All_Tabs(driver)
            utils.managestock(driver, 'No')
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(1)
            driver.find_element(By.XPATH, "//div[contains(@id,'storeList-trigger-picker')]").click()
            try:
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, "//li[text()='" + DEDICATED_STORE + "']")))
                driver.find_element(By.XPATH, "//li[text()='" + DEDICATED_STORE + "']").is_displayed()
            except:
                pass
                Success_List_Append("test_MKTPL_1607_Manageshipping_010_013_014",
                                'Verify the When select option is "Yes"', "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1607_Manageshipping_010_013_014",
                            'Verify the When select option is "Yes"', "Fail", )
        Failure_Cause_Append("test_MKTPL_1607_Manageshipping_010_013_014",
                             'Verify the When select option is "Yes"', e, )
        raise e

def test_MKTPL_1607_Manageshipping_015():
    try:
        with utils.services_context_wrapper("test_MKTPL_1607_Manageshipping_015.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//span[text()="Cred Automation Store"])[1]')))
            driver.find_element(By.XPATH, '(//span[text()="Cred Automation Store"])[1]').click()
            utils.click_on_yes_no(driver)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Configuration"]')))
            driver.find_element(By.XPATH, '//span[text()="Configuration"]').click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'manageShipping')))
            manageShipping = driver.find_element(By.NAME, 'manageShipping')
            manageShipping.clear()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Validation failed: Empty mandatory field [ manageShipping ]"]'))).is_displayed()
            logging.info("Validation shown")
        Success_List_Append("test_MKTPL_1607_Manageshipping_015",
                                "Verify the the manage shipping setting",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1607_Manageshipping_015",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_1607_Manageshipping_015",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_1265_OrderAPI_004_005_007_010_011_012_013_014_015():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1265_OrderAPI_004_005_007_010_011_012_013_014_015.png"
        ) as driver:
            # 004. Verify the BASIC order components
            response = requests.request("GET", GET_ORDER_URL+str(DEDICATED_ORDER_1_ID), headers= {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }, data={})
            resp_data = response.json()
            logging.info(resp_data)
            # 004. Verify the BASIC order components
            basic_order_components = [
                'payment',
                'shipments',
                'orderSummary',
                'itinerary',
                'meta',
                'user'
            ]
            data_order = resp_data['data']
            assert "orders" in data_order
            data_section = resp_data['data']['orders']
            for component in basic_order_components:
                assert component in data_section, f"Component '{component}' is missing."
            logging.info("004. GET-ID All required fields present in BASIC order components")
            # 005. Verify the Order information fields in API
            orders_data = resp_data.get('data', {}).get('orders', {})
            basic_order_components = [
                'orderKey',
                'externalId',
                'basketKey',
                'orderProvider',
                'orderState',
                'orderdate',
                'orderModificationDate'
            ]
            assert all(component in orders_data for component in basic_order_components)
            logging.info("005. GET-ID All required fields present in Order information.")
            # 007. Verify the Items required fields
            line_items = resp_data.get('data', {}).get('orders', {}).get('lineItemArray', [])
            required_fields = [
                'status',
                'productId',
                'retailerCode',
                'pacVariantID',
                'unitPrice',
                'quantity',
                'storeId',
                'discountTotal',
                'taxTotal',
                'salePrice',
                'unitTax',
                'unitDiscount',
                'unitGross',
                'unitNet',
                'discountAdjustments',
                'taxAdjustments'
            ]
            for item in line_items:
                assert all(field in item for field in required_fields), "Missing required field"
            logging.info("007. GET-ID All required fields present in Items required")
            # 010. Verify the Delivery Address and Shipment fields
            required_fields = [
                'salutation',
                'title',
                'firstName',
                'middleName',
                'lastName',
                'address1',
                'address2',
                'postalCode',
                'city',
                'state',
                'countryCode',
                'email',
                'phone'
            ]
            billing_address = resp_data.get('data', {}).get('orders', {}).get('payment', [{}])[0].get('billingAddress', {})
            for field in required_fields:
                assert field in billing_address, f"Field '{field}' is missing in billing address."
            logging.info("010. GET-ID All required fields present in Delivery Address and Shipment fields")
            #  011. Check that Payment field in API
            payment_data = resp_data['data']['orders']['payment'][0]
            fields_to_check = ['paymentMethod', 'authAmount', 'paymentId', 'technicalServiceProviderTransactionId',
                               'gatewayTransactionId', 'status']
            assert all(field in payment_data for field in fields_to_check)
            logging.info("011. GET-ID All required fields present in Payment fields")
            # 012. Check that Order summary field in API
            order_summary = resp_data.get('data', {}).get('orders', {}).get('orderSummary', {})
            required_fields = [
                'grossTotal',
                'discounts',
                'adjustmentTotal',
                'taxes',
                'totalTaxAmount',
                'shippingTotal',
                'currency',
                'netTotal'
            ]
            for field in required_fields:
                assert field in order_summary, f"Field '{field}' is missing in order summary."
            logging.info("012. GET-ID All required fields present in Order Summary ")
            # 013. Check that Itinerary field in API
            itinerary = resp_data['data']["orders"]['itinerary']
            fields_to_check = ['identifier', 'confirmationCode', 'airline', 'firstName', 'lastName', 'middleName']
            assert all(field in itinerary for field in fields_to_check)
            logging.info("013. GET-ID All required fields are present in the itinerary.")
            # 014. Check that Flight information field in API
            meta_info = resp_data['data']['orders']['meta']
            fields_to_check = ['airlineCodeICAO', 'flightNumber', 'departureAirportCodeIATA', 'arrivalAirportCodeIATA',
                               'departTimeStamp', 'arrivalTimeStamp']
            assert all(field in meta_info for field in fields_to_check)
            logging.info("014. GET-ID All required fields present in Flight information")
            # 015. Check that User info and customer info field in API
            user_info = resp_data['data']['orders']['user']
            fields_to_check = ['id', 'userName', 'salutation', 'firstName', 'lastName', 'middleName', 'email', 'phone']
            assert all(field in user_info for field in fields_to_check)
            logging.info("015. GET-ID All required fields present in User info and customer")
            Success_List_Append("test_MKTPL_1265_OrderAPI_004_005_007_010_011_012_013_014_015","Verify the BASIC order components","Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1265_OrderAPI_004_005_007_010_011_012_013_014_015","Verify the BASIC order components","Fail",)
        Failure_Cause_Append("test_MKTPL_1265_OrderAPI_004_005_007_010_011_012_013_014_015", "Verify the BASIC order components",e,)
        raise e

def test_MKTPL_1265_OrderAPI_009_017_019_021():
    RANDOM_NAME = "(From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    RANDOM_NUMBER = ("".join([str(randint(0, 9)) for i in range(7)]))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1265_OrderAPI_009_017_019_021.png"
        ) as driver:
            # 009.
            api_response = utils.order_api_payload("John", "Doe", "<EMAIL>", "ismail", "rangwala",
                                                   "456",
                                                   "Manchester", "England", "12345", "GB",
                                                   "<EMAIL>",
                                                   "", "sx", "13KNTV34", "CRE", RANDOM_NUMBER+"  From_Automation_",
                                                   "2024-05-24T17:39:51.287+00:00",
                                                   "random", DEDICATED_STORE_ID, "DELIVERED", "CALL_CREW", "INTERNAL",
                                                   "home_delivery",
                                                   "Outside demo", "Outside demo", "GA", "USD", ORDER_JWT_TOKEN, "POST",
                                                   CONSUME_ORDER_URL)

            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            RANDOM_NUMBER = ("".join([str(randint(0, 9)) for i in range(7)]))
            api_response = utils.order_api_payload("John", "Doe", "<EMAIL>", "ismail", "rangwala",
                                                   "456",
                                                   "Manchester", "England", "12345", "GB",
                                                   "<EMAIL>",
                                                   "", "sx", "13KNTV34", "CRE", RANDOM_NUMBER + "  From_Automation_",
                                                   "2024-05-24T17:39:51.287+00:00",
                                                   "random", DEDICATED_STORE_ID, "PENDING", "CALL_CREW", "INTERNAL",
                                                   "home_delivery",
                                                   "Outside demo", "Outside demo", "GA", "USD", ORDER_JWT_TOKEN, "POST",
                                                   CONSUME_ORDER_URL)

            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            logging.info(' 009. OrderStatus verify with API : Pending, Delivered')
            logging.info('017. Valid Datatypes of the every fields')

            # 019. Check validation in unique fields: orderKey,basketKey,orderProvider,externalId
            # Validation for orderKey
            api_response = utils.order_api_payload_1265('',
                                                        'From_Automation 3328 improvement current flight',
                                                        'From_Automation 3328 improvemen',
                                                        'kdksk From_Automation',DEDICATED_PRODUCT_9_ID,
                                                        'CRE', 'DELIVERED', ORDER_JWT_TOKEN,
                                                        "POST", CONSUME_ORDER_URL)
            assert api_response != False
            assert 103 == api_response["error_code"]
            assert "In Order [1]  orderKey data can not be empty. It is a mandatory Field." in api_response["error_message"]
            #  validation for basketKey : Validation not coming
            # api_response = utils.order_api_payload_1265('From_Automation 2dsed improvement current flight shipment',
            #                                             '',
            #                                             'From_Automation 3328 improvemen',
            #                                             'kdksk From_Automation', DEDICATED_PRODUCT_9_ID,
            #                                             'CRE', 'DELIVERED', ORDER_JWT_TOKEN,
            #                                             "POST", CONSUME_ORDER_URL)
            # assert api_response != False
            # assert 103 == api_response["error_code"]
            # assert "In Order [1]  basketKey data can not be empty. It is a mandatory Field." in api_response[
            #     "error_message"]
            # validation for orderProvider
            api_response = utils.order_api_payload_1265('From_Automation 2dsed improvement current flight shipment',
                                                        'From_Automation 3328 improvement current flight',
                                                        '',
                                                        'kdksk From_Automation', DEDICATED_PRODUCT_9_ID,
                                                        'CRE', 'DELIVERED', ORDER_JWT_TOKEN,
                                                        "POST", CONSUME_ORDER_URL)
            assert api_response != False
            assert 103 == api_response["error_code"]
            assert "In Order [1]  orderProvider data can not be empty. It is a mandatory Field." in api_response[
                "error_message"]
            # validation for externalId
            api_response = utils.order_api_payload_1265('From_Automation 2dsed improvement current flight shipment',
                                                        'From_Automation 3328 improvement current flight',
                                                        'From_Automation 3328 improvement current fligh',
                                                        '', DEDICATED_PRODUCT_9_ID,
                                                        'CRE', 'DELIVERED', ORDER_JWT_TOKEN,
                                                        "POST", CONSUME_ORDER_URL)
            assert api_response != False
            assert 103 == api_response["error_code"]
            assert "In Order [1] externalId data can not be empty. It is a mandatory Field" in api_response[
                "error_message"]
            logging.info('019. orderKey, orderProvider, externalId is unique for the every order')
            # validation for status
            api_response = utils.order_api_payload_1265('From_Automation 2dsed improvement current flight shipment',
                                                        'From_Automation 3328 improvement current flight',
                                                        'From_Automation 3328 improvement current fligh',
                                                        '123 kdksk From_Automation', DEDICATED_PRODUCT_9_ID,
                                                        'CRE', '', ORDER_JWT_TOKEN,
                                                        "POST", CONSUME_ORDER_URL)
            assert api_response != False
            assert 103 == api_response["error_code"]
            assert "In Order [1] => lineItems\r\n                     1 => status data can not be empty. It is a mandatory Field." in api_response[
                "error_message"]
            # validation for product id
            api_response = utils.order_api_payload_1265('From_Automation 2dsed improvement current flight shipment',
                                                        'From_Automation 3328 improvement current flight',
                                                        'From_Automation 3328 improvement current fligh',
                                                        '123 kdksk From_Automation', '',
                                                        'CRE', 'DELIVERED', ORDER_JWT_TOKEN,
                                                        "POST", CONSUME_ORDER_URL)
            assert api_response != False
            assert 103 == api_response["error_code"]
            assert "In Order [1] => lineItems\r\n                     1 => vendorProductVariantID data can not be empty. It is a mandatory Field." in \
                   api_response[
                       "error_message"]
            # validation for airline data
            api_response = utils.order_api_payload_1265('From_Automation 2dsed improvement current flight shipment',
                                                        'From_Automation 3328 improvement current flight',
                                                        'From_Automation 3328 improvement current fligh',
                                                        '123 kdksk From_Automation', DEDICATED_PRODUCT_9_ID,
                                                        '', 'DELIVERED', ORDER_JWT_TOKEN,
                                                        "POST", CONSUME_ORDER_URL)
            assert api_response != False
            assert 103 == api_response["error_code"]
            assert "airlineCodeICAO data can not be empty. It is a mandatory Field" in api_response["error_message"]
            logging.info('021. Checked Validation when required fields is blank like status,product id,airline data')
            # -------
            # driver.maximize_window()
            # utils.Pac_Credentials.Login_Pac_Admin(driver)
            # # driver.implicitly_wait(10)
            # act_title = driver.find_element(
            #     By.XPATH, xpath.logout
            # ).get_attribute("id")
            # assert act_title == "pimcore_logout"
            # logging.info("Login is successful.")
            # utils.wait_for_style_attribute(driver, 40)
            # utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
            # (WebDriverWait(driver, 60).until(
            #     EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
            #                                                                                   Keys.TAB))
            # (WebDriverWait(driver, 60).until
            #  (EC.presence_of_element_located(
            #      (By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            # orderId = driver.find_element(By.XPATH,
            #                               '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            # logging.info(orderId)
            # logging.info("001. POST - Pimcore Groundtool should be able to consume the order payload sent from the"
            #              " airside and save the orders in the ground tool so that the airlines, stores and PAC admins"
            #              " can view them in the order grids")
            # driver.find_element(By.XPATH, xpath.logout).click()
            Success_List_Append("test_MKTPL_1265_OrderAPI_009_017_019_021",
                                "Verify the POST order API", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1265_OrderAPI_009_017_019_021",
                            "Verify the POST order API.", "Fail",)
        Failure_Cause_Append("test_MKTPL_1265_OrderAPI_009_017_019_021",
                             "Verify the POST order API", e,)
        raise e

def test_MKTPL_1265_OrderAPI_022():
    RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_1265_OrderAPI_022.png") as driver:
            driver.maximize_window()
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.222", "33.444", "21.343", "200.332",
                                                                              "67.555", "12.123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["code"] == 200
            assert "Successful request to sync orders." == api_response["message"]
            logging.info("POST - Value with upto 3 decimal precision is allowed from Post Order API")
            logging.info(externalId)
            # -------
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
             send_keys(externalId, Keys.ENTER))
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located(
                 (By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            orderId = driver.find_element(By.XPATH,
                                          '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            logging.info(orderId)
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, '(//span[@class="x-tab-close-btn"])[1]'))).click())  # close order folder
            # -------------------------------------
            utils.search_by_id(driver, orderId)
            orderKeyValue =(WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                    (By.NAME, 'orderKey'))).get_attribute('value'))
            externalIdValue = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.NAME, 'externalId'))).get_attribute('value'))
            logging.info(orderKeyValue)
            logging.info(externalIdValue)
            basketKeyValue = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.NAME, 'basketKey'))).get_attribute('value'))
            logging.info(basketKeyValue)
            orderProviderValue = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.NAME, 'orderProvider'))).get_attribute('value'))
            logging.info(orderProviderValue)
            # orderProvider
            assert "From_Automation_" + externalId == externalIdValue
            assert 'From_Automation flight shipment' == orderKeyValue
            assert 'From_Automation current flight shipment' == basketKeyValue
            assert 'From_Automation improvement 1234bcc10' == orderProviderValue
            logging.info('022. All filled up data in UI by API post')

            Success_List_Append("test_MKTPL_1265_OrderAPI_022",
                                "Verify the able to view line items details", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1265_OrderAPI_022",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_1265_OrderAPI_022",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_394_Ordersplitting_001_002_004_005_006_007_008_009():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_394_Ordersplitting_001_002_004_005_006_007_008_009.png"
        ) as driver:
            # login_user = ""
            Year = "2024"
            Month = "05"
            Date = "24"

            driver.maximize_window()
            for user_login in [utils.Pac_Credentials.Login_store, utils.Pac_Credentials.Login_Airline, utils.Pac_Credentials.Login_Pac_Admin]:
                user_login(driver)
                act_title = driver.find_element(
                    By.XPATH, xpath.logout
                ).get_attribute("id")
                assert act_title == "pimcore_logout"
                logging.info("Login is successful.")
                # login_user = "pac user"
                # login_user = "store user"

                utils.search_by_id(driver, DEDICATED_ORDER_1_ID)

                # 001. "Verify the order structure in Airline and Store and PAC"
                WebDriverWait(driver, 60).until(
                    (EC.presence_of_element_located((By.XPATH, '//div[text()="orderShipment"]'))))
                order_shipment_text = driver.find_element(By.XPATH, '//div[text()="orderShipment"]//ancestor::tr//td[2]//div').text
                logging.info(order_shipment_text)
                order_shipment_list = order_shipment_text.split("/")
                # Store
                assert order_shipment_list[6] == DEDICATED_ORDER_1_NAME
                # Store
                assert order_shipment_list[7] == DEDICATED_STORE
                logging.info("Verfied The Order created in the air can have multiple stores so when the Order is synched up in the Ground, \
                    every Order have their store")

                # 002. "Check that order splitting folder by PAC"
                # Order
                assert order_shipment_list[1] == "Order"
                # Airline
                assert order_shipment_list[2] == DEDICATED_AIRLINE
                # Store
                assert order_shipment_list[7] == DEDICATED_STORE
                logging.info("Verified PAC are able to view all orders like Order>>Airline>>Order respective Store")

                # 005. "Verify the Folder Hierarchy"
                WebDriverWait(driver, 60).until(
                    (EC.presence_of_element_located((By.XPATH, '//span[text()="Children Grid"]'))))
                driver.find_element(By.XPATH, '//span[text()="Children Grid"]').click()
                WebDriverWait(driver, 60).until(
                    (EC.presence_of_element_located((By.XPATH, '//input[@name="selectClass"]'))))
                children_grid_input = driver.find_element(By.XPATH, '//input[@name="selectClass"]')
                children_grid_input.click()
                children_grid_input.send_keys("orderShipment")
                children_grid_input.send_keys(Keys.ENTER)
                WebDriverWait(driver, 60).until(
                    (EC.presence_of_element_located((By.XPATH, '//div[text()="Home Delivery"]'))))
                x = driver.find_element(By.XPATH, '//div[text()="Home Delivery"]//ancestor::tr//td[3]//div')
                action = ActionChains(driver)
                action.double_click(x).perform()
                shiping_mode_text = driver.find_element(By.XPATH, '//div[text()="Home Delivery"]//ancestor::tr//td[3]//div').text
                shiping_mode_list = shiping_mode_text.split("/")
                # Airline
                assert shiping_mode_list[2] == DEDICATED_AIRLINE
                # Year
                assert shiping_mode_list[3] == Year
                # Month
                assert shiping_mode_list[4] == Month
                # Date
                assert shiping_mode_list[5] == Date
                # Order
                assert shiping_mode_list[6] == DEDICATED_ORDER_1_NAME
                # Shipment Folder
                assert shiping_mode_list[8] == DEDICATED_ORDER_SHIPMENT_1_NAME
                logging.info("Folder Hierarchy 2 >>Airline > Year > Month > Date > Order > Shipment Folder")

                # 006. "Verify the show line items with specific store"
                # Store
                assert shiping_mode_list[7] == DEDICATED_STORE
                # Store Line Item
                assert shiping_mode_list[8] == DEDICATED_ORDER_SHIPMENT_1_NAME
                logging.info("Verified Follow same folder but Store's are able to view Store Specific shipment")
                utils.click_on_yes_no(driver)
                # 003. "Verify the Folder Hierarchy"
                WebDriverWait(driver, 60).until(
                    (EC.presence_of_element_located((By.XPATH, '(//span[text()="Children Grid"])[2]'))))
                driver.find_element(By.XPATH, '(//span[text()="Children Grid"])[2]').click()
                WebDriverWait(driver, 60).until(
                    (EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Lineitem")])[last()]'))))
                store_line_item_text = driver.find_element(By.XPATH, '//div[contains(text(),"Lineitem")]//ancestor::tr//td[3]//div').text
                store_line_item_list = store_line_item_text.split("/")
                # Airline
                assert store_line_item_list[2] == DEDICATED_AIRLINE
                # Year
                assert store_line_item_list[3] == Year
                # Month
                assert store_line_item_list[4] == Month
                # Date
                assert store_line_item_list[5] == Date
                # Order
                assert store_line_item_list[6] == DEDICATED_ORDER_1_NAME
                # Fulfillment Object
                assert store_line_item_list[9] == "STANDARD"
                # Store Line Item
                assert store_line_item_list[10] == DEDICATED_ONLINE_SHOP_ORDER_ITEM_1_NAME
                logging.info("Verified Folder Hierarchy 1 >>Airline > Year > Month > Date > Order > Fulfillment Object > Store Line Item")

                # 004. "Verify the show line items with specific store"
                # Store
                assert store_line_item_list[7] == DEDICATED_STORE
                # Store Line Item
                assert store_line_item_list[10] == DEDICATED_ONLINE_SHOP_ORDER_ITEM_1_NAME
                logging.info("Verified Follow same folder but Store's are able to view Store Specific line items")

                # 007. "Verify the show folder with specific Airline"
                # Airline
                assert store_line_item_list[2] == DEDICATED_AIRLINE
                # Order
                assert store_line_item_list[6] == DEDICATED_ORDER_1_NAME
                # Store
                assert store_line_item_list[7] == DEDICATED_STORE
                logging.info("Verified Airline will view all same folder Hierarchy orders associated with stores")

                # 008. "Verify the Folder Hierarchy with specific store"
                # Store
                assert store_line_item_list[7] == DEDICATED_STORE
                # Store Line Item
                assert store_line_item_list[10] == DEDICATED_ONLINE_SHOP_ORDER_ITEM_1_NAME
                logging.info("Verified Stores will view same folder Hierarchy only their order line items in the orders")

                # 009. "Verify the shipment folder in Airline and Store"
                # Airline
                assert shiping_mode_list[2] == DEDICATED_AIRLINE
                # Store
                assert shiping_mode_list[7] == DEDICATED_STORE
                # Store Line Item
                assert shiping_mode_list[8] == DEDICATED_ORDER_SHIPMENT_1_NAME
                logging.info("Verified Shipment folder hierarchy for airline and store also with respective store")
                # Logout user
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.logout)))
                driver.find_element(By.XPATH, xpath.logout).click()
        Success_List_Append("test_MKTPL_394_Ordersplitting_001_002_004_005_006_007_008_009",
                                "Verify the order structure in Airline and Store and PAC",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_394_Ordersplitting_001_002_004_005_006_007_008_009",
                            "Verify the order structure in Airline and Store and PAC",
                            "Fail",)
        Failure_Cause_Append("test_MKTPL_394_Ordersplitting_001_002_004_005_006_007_008_009",
                             "Verify the order structure in Airline and Store and PAC",
                             e,)
        raise e

def test_MKTPL_520_Orderlineitem_001_002():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_520_Orderlineitem_001_002.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            # 001. Verify the able to view line items details
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver,  DEDICATED_ORDER_1_ID)
            # click at Children Grid
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('orderShipment', Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.homeDeliveryXpath+'[2]'))))
            homeDelivery = driver.find_element(By.XPATH,
                                               xpath.homeDeliveryXpath+'[2]')
            actions = ActionChains(driver)
            actions.context_click(homeDelivery).perform()
            open1 = driver.find_element(By.XPATH, xpath.openXpathContains)
            open1.click()
            utils.click_on_yes_no(driver)
            # opend home_delivery ID: 28988 orderShipment
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, '('+xpath.childrenGridXpath+')[2]'))).click())
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, xpath.homeDeliveryXpath+'[2]'))))
            line_item = driver.find_element(By.XPATH, xpath.homeDeliveryXpath+'[3]')
            actions = ActionChains(driver)
            actions.double_click(line_item).perform()
            time.sleep(1)
            utils.click_on_yes_no(driver)
            # line item opened
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, xpath.generalInformationXpath))).is_displayed())
            logging.info('"User able to Open Order Line Detail page via, Order Line item Grid, Order Folder Structure, '
                         'Order Detail Page(onlneshopfulfillment)"')
            logging.info("User able to view detailed order line item information and update the status.")
            Success_List_Append("test_MKTPL_520_Orderlineitem_001_002",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_520_Orderlineitem_001_002",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_520_Orderlineitem_001_002",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_520_Orderlineitem_003_004():
    try:
        with utils.services_context_wrapper("test_MKTPL_520_Orderlineitem_003_004.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver,  DEDICATED_FAILED_ORDER_LINE_ITEM_ID)
            statusField = (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'status'))).is_displayed())
            driver.find_element(By.XPATH, '(//input[@name = "status"]/../..//div)[2]').click()
            pendingOption = driver.find_element(By.XPATH, xpath.pendingOptionListXpath).is_displayed()
            deliveredOption = driver.find_element(By.XPATH, xpath.deliveredOptionListXpath).is_displayed()
            requiredField = driver.find_element(By.XPATH, '//span[text()="Status "]//span[text()="*"]').is_displayed()
            assert [statusField, pendingOption, deliveredOption, requiredField]
            logging.info('Status fields is required field and That is editable like : 1.Delivered, 2.Pending')

            Success_List_Append("test_MKTPL_520_Orderlineitem_003_004",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_520_Orderlineitem_003_004",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_520_Orderlineitem_003_004",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_520_Orderlineitem_005_006_007_008_009_010_011_012_013_014():
    # 003, 004 - Status fields all options not visible,only pending and deliverd is visible  - Not Done
    # 005- FulfillmentType - Read Only is not present
    # 005- PACVariantID - Read Only is not there it can be editable
    # 006- Unit Price and its non mandatory - but it's mandatory
    # 007- all non-mandatory fields expect Quantity are mandatory
    # 008- store field is not there - Not Done
    # 009- Discount Adjustments block is not in read only mode
    # 010- Tax Adjustments block is not in read only mode

    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_520_Orderlineitem_005_006_007_008_009_010_011_012_013_014.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # 005. Check that view item Details
            utils.search_by_id(driver,  DEDICATED_ORDER_1_LINE_ITEM_ID)
            productName = (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//span[text()="Product "]'))).is_displayed())
            vendorProductVariantID = (WebDriverWait(driver, 60).
                                      until(EC.presence_of_element_located((By.XPATH, '//span[@class="pimcore_object_label_icon pimcore_icon_gray_lock"]/../..//span[contains(text(),"Vendor Product Variant ID")]'))).is_displayed())
            pacVariantId = (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//span[contains(text(),"PAC Variant ID")]'))).is_displayed())
            assert [productName, vendorProductVariantID, pacVariantId]
            logging.info('005. "View Item Details which is Received from airside, Product Name - Can be opened in the new '
                         'product tab, PACVariantID - Read Only, FulfillmentType - Read Only, vendorProductVariantID - '
                         'Read Only"')
            # Check that price fields
            unitPrice = driver.find_element(By.XPATH, xpath.unitPriceXpath).is_displayed()
            assert unitPrice
            logging.info("006. Price Information which is Received from airside unit Price and its non mandatory")
            # 007. Check that non-mandatory fields
            quantity = driver.find_element(By.XPATH, xpath.quantityXpath).is_displayed()
            discountTotal = driver.find_element(By.XPATH, xpath.discountTotalXpath).is_displayed()
            taxTotal = driver.find_element(By.XPATH, xpath.taxTotalXpath).is_displayed()
            salePrice = driver.find_element(By.XPATH, xpath.salePriceXpath).is_displayed()
            unitTax = driver.find_element(By.XPATH, xpath.unitTaxXpath).is_displayed()
            unitDiscounts = driver.find_element(By.XPATH, xpath.unitDiscountsXpath).is_displayed()
            unitGrossTotal  = driver.find_element(By.XPATH, xpath.unitGrossTotalXpath).is_displayed()
            unitNetTotal = driver.find_element(By.XPATH, xpath.unitNetTotalXpath).is_displayed()
            assert [quantity, discountTotal, taxTotal, salePrice, unitTax, unitDiscounts, unitGrossTotal, unitNetTotal]
            logging.info("007. Fields below are and all are non mandatory, Quantity is number datatype, Discount total, "
                         "Tax Total, Sale Price, Unit Tax, Unit Discounts, Unit Gross total, Unit Net total")
            # 008. Verify the mandatory field
            (WebDriverWait(driver, 60).until
                (EC.presence_of_element_located((By.XPATH, '//span[text()="Store "]//span[text()="*"]'))))
            logging.info('Store is mandatory field and is fetch based on vendor product variant ID')
            # 009. Verify the discount adjustments block
            discountAdjustments  = driver.find_element(By.XPATH, xpath.discountAdjustmentsXpath).is_displayed()
            amount  = driver.find_element(By.XPATH, xpath.amountXpath).is_displayed()
            adjustmentType  = driver.find_element(By.XPATH, xpath.ajustmentTypeXpath).is_displayed()
            promotionCode  = driver.find_element(By.XPATH, xpath.promotionCodeXpath).is_displayed()
            promotionName = driver.find_element(By.XPATH, xpath.promotionNameXpath).is_displayed()
            rate = driver.find_element(By.XPATH, '//span[contains(text(),"Rate")]/../../../../../../../../../../../../../../../../../..//div[contains(text(),"Discount Adjustments")]').is_displayed()
            # rate = driver.find_element(By.XPATH, '//span[text()="Rate:"]').is_displayed()
            assert [discountAdjustments, amount, adjustmentType, promotionCode, promotionName, rate]
            logging.info("'009. Discount Adjustments block with below read only mode, Amount, Adjustment Type, "
                         "Promotion Code, Promotion Name, Rate'")
            # 010. Verify the Tax adjustments block
            taxAdjustments = driver.find_element(By.XPATH, xpath.taxAdjustmentsXpath).is_displayed()
            type  = driver.find_element(By.XPATH, xpath.typeXpath).is_displayed()
            taxAmount  = driver.find_element(By.XPATH, xpath.taxAmountXpath).is_displayed()
            taxAdjustmentsRate  = driver.find_element(By.XPATH, xpath.rateXpath).is_displayed()
            assert [taxAdjustments, type, taxTotal, taxAmount, taxAdjustmentsRate]
            logging.info(" 010. Tax Adjustments block with below read only mode, Type, Tax Amount, Rate")
            # 011. Verify the line item page redirection by shipment
            # click at order shipment
            driver.find_element(By.XPATH, xpath.orderShipmentXpath).click()
            orderShipmentId = driver.find_element(By.XPATH, "//span[text()='ID']//ancestor::div[@role='columnheader']").is_displayed()
            orderShipmentId = driver.find_element(By.XPATH, "//div[text()='"+str(DEDICATED_ORDER_1_ORDER_SHIPMENT_ID)+"']").is_displayed()
            orderShipmentReference = driver.find_element(By.XPATH, "//span[text()='Reference']//ancestor::div[@role='columnheader']").is_displayed()
            assert [orderShipmentId, orderShipmentReference]
            logging.info("011. Order shipment tab in order line items and its redirect to ordershipment object")
            # 012. Verify the shipment object
            driver.find_element(By.XPATH, xpath.openButtonXpath).click()
            utils.click_on_yes_no(driver)
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.baseDataDivXpath))))
            oli = driver.find_element(By.XPATH, xpath.orderLineItemXpath).is_displayed()
            oliId = driver.find_element(By.XPATH, "(//span[text()='ID']//ancestor::div[@role='columnheader'])[2]").is_displayed()
            assert [oli, oliId]
            logging.info(' 012. Open shipment object and view Shipping Information which is Received from airside, Order ID, '
                         'Order Line items')
            # 013. Verify the shipping fields\
            shipmentID  = driver.find_element(By.XPATH, xpath.shipmentIdXpath).is_displayed()
            shipmentMethod  = driver.find_element(By.XPATH, xpath.shipmentMethodXpath).is_displayed()
            shipmentMode  = driver.find_element(By.XPATH, xpath.shipmentModeXpath).is_displayed()
            trackingId = driver.find_element(By.XPATH, xpath.trackingIdXpath).is_displayed()
            trackingCarrier = driver.find_element(By.XPATH, xpath.trackingCarrierXpath).is_displayed()
            trackingLink = driver.find_element(By.XPATH, xpath.trackingLinkXpath).is_displayed()
            inventoryLocation = driver.find_element(By.XPATH, xpath.inventoryLocationXpath).is_displayed()
            assert [shipmentID, shipmentMethod, shipmentMode, trackingId, trackingCarrier, trackingLink, inventoryLocation]
            logging.info('013. Fields are :'
                         'Shipping ID - NON EDITABLE, '
                         'shipping Method - EDITABLE, '
                         'carrier - EDITABLE, '
                         'mode : "home_delivery/inflight" - NON EDITABLE, '
                         'Delivery Address Information - EDITABLE, '
                         'Tracking Number - Enable only in mode - '
                         'home_Delivery - Editable Text Entry,'
                         'Tracking Link - URl Inventory '
                         'Inventory Location - Single Select')
            # 014. Verify the Delivery address fields
            driver.find_element(By.XPATH, xpath.deliveryInformatioXpath).click()
            salutation = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.salutationXpath)))).is_displayed()
            firstName  = driver.find_element(By.XPATH, xpath.firstNameXpath).is_displayed()
            lastName  = driver.find_element(By.XPATH, xpath.lastNameXpath).is_displayed()
            address1  = driver.find_element(By.XPATH, xpath.address1Xpath).is_displayed()
            postalCode  = driver.find_element(By.XPATH, xpath.postalCodeXpath).is_displayed()
            shippingState  = driver.find_element(By.XPATH, xpath.shippingStateXpath).is_displayed()
            email = driver.find_element(By.XPATH, xpath.emailXpath).is_displayed()
            title = driver.find_element(By.XPATH, xpath.titleXpath).is_displayed()
            middleName = driver.find_element(By.XPATH, xpath.middleNameXpath).is_displayed()
            address2 = driver.find_element(By.XPATH, xpath.address2Xpath).is_displayed()
            shippingCity = driver.find_element(By.XPATH, xpath.shippingCityXpath).is_displayed()
            shippingCountry = driver.find_element(By.XPATH, xpath.shippingCountryXpath).is_displayed()
            shippingPhone = driver.find_element(By.XPATH, xpath.shippingPhoneXpath).is_displayed()
            assert [salutation, firstName, lastName, address1, postalCode, shippingState, email, title, middleName,
                    address2, shippingCity, shippingCountry, shippingPhone]
            logging.info('014. "Delivery address fields like : salutation, title, firstName, middleName, lastName, address1, '
                         'address2, city, state, postal Code, country Code, email, phone"')
            Success_List_Append("test_MKTPL_520_Orderlineitem_005_006_007_008_009_010_011_012_013_014",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_520_Orderlineitem_005_006_007_008_009_010_011_012_013_014",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_520_Orderlineitem_005_006_007_008_009_010_011_012_013_014",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_323_Orderlineitem_001_002():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_323_Orderlineitem_001_002.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            # 001. Verify the able to view line items details
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver,  DEDICATED_ORDER_1_ID)
            # click at Children Grid
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('orderShipment', Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.homeDeliveryXpath+'[2]'))))
            homeDelivery = driver.find_element(By.XPATH, xpath.homeDeliveryXpath+'[2]')
            actions = ActionChains(driver)
            actions.context_click(homeDelivery).perform()
            open1 = driver.find_element(By.XPATH, xpath.openXpathContains)
            open1.click()
            utils.click_on_yes_no(driver)
            # opend home_delivery ID: 28988 orderShipment
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, '('+xpath.childrenGridXpath+')[2]'))).click())
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, xpath.homeDeliveryXpath+'[2]'))))
            line_item = driver.find_element(By.XPATH, xpath.homeDeliveryXpath+'[3]')
            actions = ActionChains(driver)
            actions.double_click(line_item).perform()
            time.sleep(1)
            utils.click_on_yes_no(driver)
            # line item opened
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, xpath.generalInformationXpath))).is_displayed())

            logging.info('"User able to Open Order Line Detail page via, Order Line item Grid, Order Folder Structure, '
                         'Order Detail Page(onlneshopfulfillment)"')
            logging.info("User able to view detailed order line item information and update the status.")
            Success_List_Append("test_MKTPL_323_Orderlineitem_001_002",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_323_Orderlineitem_001_002",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_323_Orderlineitem_001_002",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_323_Orderlineitem_005_006_007_008_009_010_011_012_013_014():
    # 005- PACVariantID - Read Only is not there it can be editable
    # 006- Unit Price and its non-mandatory - but it's mandatory
    # 007- all non-mandatory fields expect Quantity are mandatory
    # 008- store field is not there - Not Done
    # 009- Discount Adjustments block is not in read only mode
    # 010- Tax Adjustments block is not in read only mode

    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_323_Orderlineitem_005_006_007_008_009_010_011_012_013_014.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            # 005. Check that view item Details
            utils.search_by_id(driver, DEDICATED_ORDER_1_LINE_ITEM_ID)
            productName = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[text()="Product "]'))).is_displayed())
            vendorProductVariantID = (WebDriverWait(driver, 60).
                                      until(EC.presence_of_element_located((By.XPATH,
                                                                            xpath.readModeLock+'/../..//span[contains(text(),"Vendor Product Variant ID")]'))).is_displayed())
            pacVariantId = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//span[contains(text(),"PAC Variant ID")]'))).is_displayed())
            assert [productName, vendorProductVariantID, pacVariantId]
            logging.info(
                '005. "View Item Details which is Received from airside, Product Name - Can be opened in the new '
                'product tab, PACVariantID - Read Only, FulfillmentType - Read Only, vendorProductVariantID - '
                'Read Only"')
            # Check that price fields
            unitPrice = driver.find_element(By.XPATH, xpath.unitPriceXpath).is_displayed()
            assert unitPrice
            logging.info("006. Price Information which is Received from airside unit Price and its non mandatory")
            # 007. Check that non-mandatory fields
            quantity = driver.find_element(By.XPATH, xpath.quantityXpath).is_displayed()
            discountTotal = driver.find_element(By.XPATH, xpath.discountTotalXpath).is_displayed()
            taxTotal = driver.find_element(By.XPATH, xpath.taxTotalXpath).is_displayed()
            salePrice = driver.find_element(By.XPATH, xpath.salePriceXpath).is_displayed()
            unitTax = driver.find_element(By.XPATH, xpath.unitTaxXpath).is_displayed()
            unitDiscounts = driver.find_element(By.XPATH, xpath.unitDiscountsXpath).is_displayed()
            unitGrossTotal = driver.find_element(By.XPATH, xpath.unitGrossTotalXpath).is_displayed()
            unitNetTotal = driver.find_element(By.XPATH, xpath.unitNetTotalXpath).is_displayed()
            assert [quantity, discountTotal, taxTotal, salePrice, unitTax, unitDiscounts, unitGrossTotal, unitNetTotal]
            logging.info(
                "007. Fields below are and all are non mandatory, Quantity is number datatype, Discount total, "
                "Tax Total, Sale Price, Unit Tax, Unit Discounts, Unit Gross total, Unit Net total")
            # 008. Verify the mandatory field
            (WebDriverWait(driver, 60).until
             (EC.presence_of_element_located((By.XPATH, '//span[text()="Store "]//span[text()="*"]'))))
            logging.info('Store is mandatory field and is fetch based on vendor product variant ID')
            # 009. Verify the discount adjustments block
            discountAdjustments = driver.find_element(By.XPATH, xpath.discountAdjustmentsXpath).is_displayed()
            amount = driver.find_element(By.XPATH, xpath.amountXpath).is_displayed()
            adjustmentType = driver.find_element(By.XPATH, xpath.ajustmentTypeXpath).is_displayed()
            promotionCode = driver.find_element(By.XPATH, xpath.promotionCodeXpath).is_displayed()
            promotionName = driver.find_element(By.XPATH, xpath.promotionNameXpath).is_displayed()
            rate = driver.find_element(By.XPATH,
                                       '//span[contains(text(),"Rate")]/../../../../../../../../../../../../../../../../../..//div[contains(text(),"Discount Adjustments")]').is_displayed()
            # rate = driver.find_element(By.XPATH, '//span[text()="Rate:"]').is_displayed()
            assert [discountAdjustments, amount, adjustmentType, promotionCode, promotionName, rate]
            logging.info("'009. Discount Adjustments block with below read only mode, Amount, Adjustment Type, "
                         "Promotion Code, Promotion Name, Rate'")
            # 010. Verify the Tax adjustments block
            taxAdjustments = driver.find_element(By.XPATH, xpath.taxAdjustmentsXpath).is_displayed()
            type = driver.find_element(By.XPATH, xpath.typeXpath).is_displayed()
            taxAmount = driver.find_element(By.XPATH, xpath.taxAmountXpath).is_displayed()
            taxAdjustmentsRate = driver.find_element(By.XPATH, xpath.rateXpath).is_displayed()
            assert [taxAdjustments, type, taxTotal, taxAmount, taxAdjustmentsRate]
            logging.info(" 010. Tax Adjustments block with below read only mode, Type, Tax Amount, Rate")
            # 011. Verify the line item page redirection by shipment
            # click at order shipment
            driver.find_element(By.XPATH, xpath.orderShipmentXpath).click()
            orderShipmentId = driver.find_element(By.XPATH,
                                                  "//div[text()='"+DEDICATED_ORDER_1_ORDER_SHIPMENT_ID+"']").is_displayed()
            orderShipmentReference = driver.find_element(By.XPATH,
                                                         "//span[text()='Reference']").is_displayed()
            assert [orderShipmentId, orderShipmentReference]
            logging.info("011. Order shipment tab in order line items and its redirect to ordershipment object")
            # 012. Verify the shipment object
            driver.find_element(By.XPATH, xpath.openButtonXpath).click()
            utils.click_on_yes_no(driver)
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.baseDataDivXpath))))
            oli = driver.find_element(By.XPATH, xpath.orderLineItemXpath).is_displayed()
            oliId = driver.find_element(By.XPATH,
                                        '//span[text()="ID"]').is_displayed()
            assert [oli, oliId]
            logging.info(
                ' 012. Open shipment object and view Shipping Information which is Received from airside, Order ID, '
                'Order Line items')
            # 013. Verify the shipping fields\
            shipmentID = driver.find_element(By.XPATH, xpath.shipmentIdXpath).is_displayed()
            shipmentMethod = driver.find_element(By.XPATH, xpath.shipmentMethodXpath).is_displayed()
            shipmentMode = driver.find_element(By.XPATH, xpath.shipmentModeXpath).is_displayed()
            trackingId = driver.find_element(By.XPATH, xpath.trackingIdXpath).is_displayed()
            trackingCarrier = driver.find_element(By.XPATH, xpath.trackingCarrierXpath).is_displayed()
            trackingLink = driver.find_element(By.XPATH, xpath.trackingLinkXpath).is_displayed()
            inventoryLocation = driver.find_element(By.XPATH, xpath.inventoryLocationXpath).is_displayed()
            assert [shipmentID, shipmentMethod, shipmentMode, trackingId, trackingCarrier, trackingLink,
                    inventoryLocation]
            logging.info('013. Fields are :'
                         'Shipping ID - NON EDITABLE, '
                         'shipping Method - EDITABLE, '
                         'carrier - EDITABLE, '
                         'mode : "home_delivery/inflight" - NON EDITABLE, '
                         'Delivery Address Information - EDITABLE, '
                         'Tracking Number - Enable only in mode - '
                         'home_Delivery - Editable Text Entry,'
                         'Tracking Link - URl Inventory '
                         'Inventory Location - Single Select')
            # 014. Verify the Delivery address fields
            driver.find_element(By.XPATH, xpath.deliveryInformatioXpath).click()
            salutation = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.salutationXpath)))).is_displayed()
            firstName = driver.find_element(By.XPATH, xpath.firstNameXpath).is_displayed()
            lastName = driver.find_element(By.XPATH, xpath.lastNameXpath).is_displayed()
            address1 = driver.find_element(By.XPATH, xpath.address1Xpath).is_displayed()
            postalCode = driver.find_element(By.XPATH, xpath.postalCodeXpath).is_displayed()
            shippingState = driver.find_element(By.XPATH, xpath.shippingStateXpath).is_displayed()
            email = driver.find_element(By.XPATH, xpath.emailXpath).is_displayed()
            title = driver.find_element(By.XPATH, xpath.titleXpath).is_displayed()
            middleName = driver.find_element(By.XPATH, xpath.middleNameXpath).is_displayed()
            address2 = driver.find_element(By.XPATH, xpath.address2Xpath).is_displayed()
            shippingCity = driver.find_element(By.XPATH, xpath.shippingCityXpath).is_displayed()
            shippingCountry = driver.find_element(By.XPATH, xpath.shippingCountryXpath).is_displayed()
            shippingPhone = driver.find_element(By.XPATH, xpath.shippingPhoneXpath).is_displayed()
            assert [salutation, firstName, lastName, address1, postalCode, shippingState, email, title, middleName,
                    address2, shippingCity, shippingCountry, shippingPhone]
            logging.info('014. "Delivery address fields like : salutation, title, firstName, middleName, lastName, address1, '
                         'address2, city, state, postal Code, country Code, email, phone"')
            Success_List_Append("test_MKTPL_323_Orderlineitem_005_006_007_008_009_010_011_012_013_014",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_323_Orderlineitem_005_006_007_008_009_010_011_012_013_014",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_323_Orderlineitem_005_006_007_008_009_010_011_012_013_014",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_330_Order_004_563_Order_007_515_Order_007():
    try:
        with utils.services_context_wrapper("test_MKTPL_330_Order_004_563_Order_007_515_Order_007.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.search_by_id(driver, DEDICATED_STORE_ID)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Configuration"]')))
            driver.find_element(By.XPATH, '//span[text()="Configuration"]').click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'manageShipping')))
            manageShipping = driver.find_element(By.NAME, 'manageShipping')
            manageShipping.clear()
            manageShipping.send_keys("No")
            manageShipping.send_keys(Keys.ENTER)

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()

            utils.search_by_id(driver, DEDICATED_ORDER_1_ORDER_SHIPMENT_ID)

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '(//span[@class="pimcore_object_label_icon pimcore_icon_gray_lock"])[8]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Airline(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.search_by_id(driver, DEDICATED_ORDER_1_ORDER_SHIPMENT_ID)

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,
                                                  '(//span[@class="pimcore_object_label_icon pimcore_icon_gray_lock"])[5]'))).is_displayed()

            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Airline_Manager(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.search_by_id(driver, DEDICATED_ORDER_1_ORDER_SHIPMENT_ID)

            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,
                                                  '(//span[@class="pimcore_object_label_icon pimcore_icon_gray_lock"])[5]'))).is_displayed()

            logging.info(" 004 Inventory location field should be hidden")

            Success_List_Append("test_MKTPL_330_Order_004_563_Order_007_515_Order_007",
                                "As a store admin, i should be able to manage shipping on the ground tool",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_330_Order_004_563_Order_007_515_Order_007",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_330_Order_004_563_Order_007_515_Order_007",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_523_Trackinginfoimport_009_011_013_015_017():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_523_Trackinginfoimport_009_011_013_015_017.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.managestock(driver, 'yes')
            
            # 009 - Invalid Order
            # 011 - Invalid Shipment
            # 013 - Invalid Line item
            # 015 - Invalid inventory
            # 017 - Invalid line status
            
            WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            
            time.sleep(1)
            driver.find_element(By.XPATH,"//div[contains(@id,'storeList-trigger-picker')]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//li[text()='"+DEDICATED_STORE+"']")))
            driver.find_element(By.XPATH,"//li[text()='"+DEDICATED_STORE+"']").click()
            time.sleep(5)
            
            utils.update_csv("order_trackingInfo.csv", "OrderId", "2222222")
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "2222222")
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "2222222")
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "2222222")
            utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "2222222")

            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()

            utils.close_All_Tabs(driver)

            utils.Assets_import_Pac_admin(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[@class="ace_line"])[4]')))
            mass = driver.find_element(By.XPATH,
                                    '(//div[@class="ace_line"])[4]').text
            logging.info(mass)
            assert "Order object not found" in mass
            assert "Invalid Order Line Item ID" in mass
            assert "Invalid Shipping ID" in mass
            assert "Invalid inventory location ID" in mass
            assert "Invalid Order Line Status" in mass

            utils.update_csv("order_trackingInfo.csv", "OrderId", "2341")
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "2431")
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "1233")
            utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")
    
            Success_List_Append(
                "test_MKTPL_523_Trackinginfoimport_009_011_013_015_017",
                "Verify the the manage shipping setting",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_523_Trackinginfoimport_009_011_013_015_017",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_523_Trackinginfoimport_009_011_013_015_017",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_523_Trackinginfoimport_010_012_014_016():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_523_Trackinginfoimport_010_012_014_016.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.managestock(driver, 'yes')
            
            # 010 - other store - ORDER
            # 012 - other store - SHIPMENT
            # 014 - other store - LINE ITEM
            # 016 - other store - INVENTORY
            
            WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            time.sleep(1)
            driver.find_element(By.XPATH,"//div[contains(@id,'storeList-trigger-picker')]").click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//li[text()='"+DEDICATED_STORE+"']")))
            driver.find_element(By.XPATH,"//li[text()='"+DEDICATED_STORE+"']").click()
            time.sleep(5)
            
            utils.update_csv("order_trackingInfo.csv", "OrderId", DEDICATED_DIFFERENT_STORE_ORDER_ID)
            utils.update_csv("order_trackingInfo.csv", "ShippingId", DEDICATED_DIFFERENT_STORE_SHIPMENT_ID)
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", DEDICATED_DIFFERENT_STORE_LINE_ITEM_ID)
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", DEDICATED_DIFFERENT_STORE_INVENTORY_LOCATION_ID)

            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()

            utils.close_All_Tabs(driver)

            utils.Assets_import_Pac_admin(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[@class="ace_line"])[4]')))
            mass = driver.find_element(By.XPATH,
                                    '(//div[@class="ace_line"])[4]').text
            logging.info(mass)
            assert "Order object not found" in mass
            assert "Invalid Order Line Item ID" in mass
            assert "Invalid Shipping ID" in mass
            assert "Invalid inventory location ID" in mass

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()
            utils.update_csv("order_trackingInfo.csv", "OrderId", "2341")
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "2431")
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "1233")
            utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")
    
            Success_List_Append(
                "test_MKTPL_523_Trackinginfoimport_010_012_014_016",
                "Verify the the manage shipping setting",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_523_Trackinginfoimport_010_012_014_016",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_523_Trackinginfoimport_010_012_014_016",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_495_Trackinginfoimport_008_010_012_014_016():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_495_Trackinginfoimport_008_010_012_014_016.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.managestock(driver, 'yes')
            
            # 008 - Invalid Order
            # 010 - Invalid Shipment
            # 012 - Invalid Line item
            # 014 - Invalid inventory
            # 016 - Invalid line status
            
            WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            utils.update_csv("order_trackingInfo.csv", "OrderId", "22222")
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "22222")
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "22222")
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "22222")
            utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "22222")

            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.Assets_import_Store(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[@class="ace_line"])[4]')))
            mass = driver.find_element(By.XPATH,
                                    '(//div[@class="ace_line"])[4]').text
            logging.info(mass)
            assert "Order object not found" in mass
            assert "Invalid Order Line Item ID" in mass
            assert "Invalid Shipping ID" in mass
            assert "Invalid inventory location ID" in mass
            assert "Invalid Order Line Status" in mass

            utils.update_csv("order_trackingInfo.csv", "OrderId", "2341")
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "2431")
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "1233")
            utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")
    
            Success_List_Append(
                "test_MKTPL_495_Trackinginfoimport_008_010_012_014_016",
                "Verify the the manage shipping setting",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_495_Trackinginfoimport_008_010_012_014_016",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_495_Trackinginfoimport_008_010_012_014_016",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_495_Trackinginfoimport_009_011_013_015():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_495_Trackinginfoimport_009_011_013_015.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.managestock(driver, 'yes')
            
            # 009 - other store - ORDER
            # 011 - other store - SHIPMENT
            # 013 - other store - LINE ITEM
            # 015 - other store - INVENTORY
            
            WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()

            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)

            utils.update_csv("order_trackingInfo.csv", "OrderId", DEDICATED_DIFFERENT_STORE_ORDER_ID)
            utils.update_csv("order_trackingInfo.csv", "ShippingId", DEDICATED_DIFFERENT_STORE_SHIPMENT_ID)
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", DEDICATED_DIFFERENT_STORE_LINE_ITEM_ID)
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", DEDICATED_DIFFERENT_STORE_INVENTORY_LOCATION_ID)

            path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
            driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            driver.find_element(By.XPATH, xpath.upload).click()

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()

            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            utils.Assets_import_Store(driver)

            list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
            num = len(list1)
            time.sleep(5)
            row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
            actions = ActionChains(driver)
            actions.double_click(row).perform()

            try:
                WebDriverWait(driver, 60).until(
                    EC.element_to_be_clickable((By.XPATH, xpath.yes))
                )
                driver.find_element((By.XPATH, xpath.yes)).click()
                logging.info("Click on message")
            except:
                pass
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, '(//div[@class="ace_line"])[4]')))
            mass = driver.find_element(By.XPATH,
                                    '(//div[@class="ace_line"])[4]').text
            logging.info(mass)
            assert "Order object not found" in mass
            assert "Invalid Order Line Item ID" in mass
            assert "Invalid Shipping ID" in mass
            assert "Invalid inventory location ID" in mass

            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.logout)))
            driver.find_element(By.XPATH, xpath.logout).click()
            utils.update_csv("order_trackingInfo.csv", "OrderId", "2341")
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "2431")
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "1233")
            utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")
    
            Success_List_Append(
                "test_MKTPL_495_Trackinginfoimport_009_011_013_015",
                "Verify the the manage shipping setting",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_495_Trackinginfoimport_009_011_013_015",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_495_Trackinginfoimport_009_011_013_015",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_523_Trackinginfoimport_020_MKTPL_495_Trackinginfoimport_019():
    ACTUAL_CSV_DATA = []
    try:
        with utils.services_context_wrapper("test_MKTPL_523_Trackinginfoimport_020_MKTPL_495_Trackinginfoimport_019.png") as driver:
            driver.maximize_window()
            role = ["Login_Pac_Admin", "Login_Store"]
            for i in role:
                if i is "Login_Pac_Admin":
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("PAC ADMIN")
                elif i is "Login_Store":
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                utils.wait_for_style_attribute(driver, 40)
                utils.managestock(driver, 'yes')
                utils.manageshipping(driver, 'yes')
            # # 019 - Verify by importing valid inventory location id
            # # Inventory should be deducted from products for selected inventory location
            # utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
            # driver.find_element(By.XPATH, "//span[text()='Inventory']").click()
            # WebDriverWait(driver, 60).until(
            # EC.presence_of_element_located((By.XPATH, "//td[text()='Inventory Locations']")))
            # quantity = driver.find_element(By.XPATH, "(//td[contains(text(),'Default Inventory')]//parent::tr//td)[2]").text
            # logging.info(quantity)
            # WebDriverWait(driver, 60).until(
            # EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            # driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

            # WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            # driver.find_element(By.XPATH, xpath.csvImport).click()

            # WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            # data_type = driver.find_element(By.NAME, xpath.importTypeName)
            # data_type.send_keys("TrackingInfo")
            # time.sleep(1)
            # data_type.send_keys(Keys.ENTER)

            # time.sleep(1)
            # driver.find_element(By.XPATH,"//div[contains(@id,'storeList-trigger-picker')]").click()
            # driver.find_element(By.NAME, "storeList").send_keys(DEDICATED_STORE)
            # time.sleep(2)
            # WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//li[text()='"+DEDICATED_STORE+"']")))
            # driver.find_element(By.XPATH,"//li[text()='"+DEDICATED_STORE+"']").click()
            # time.sleep(5)
            
            # utils.update_csv("order_trackingInfo.csv", "OrderId", DEDICATED_ORDER_2_ID)
            # utils.update_csv("order_trackingInfo.csv", "ShippingId", DEDICATED_ORDER_2_ORDER_SHIPMENT_ID)
            # utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", DEDICATED_ORDER_2_LINE_ITEM_ID)
            # utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", DEDICATED_STORE_LOCATION_2_ID)
            # utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")

            # path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
            # driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

            # driver.find_element(By.XPATH, xpath.upload).click()

            # utils.close_All_Tabs(driver)

            # utils.Assets_import_Pac_admin(driver)
            # time.sleep(5)
            # list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
            # num = len(list1)
            # time.sleep(5)
            # row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
            # actions = ActionChains(driver)
            # actions.double_click(row).perform()

            # try:
            #     WebDriverWait(driver, 60).until(
            #         EC.element_to_be_clickable((By.XPATH, xpath.yes))
            #     )
            #     driver.find_element((By.XPATH, xpath.yes)).click()
            #     logging.info("Click on message")
            # except:
            #     pass
            # WebDriverWait(driver, 60).until(
            #     EC.visibility_of_element_located(
            #         (By.XPATH, '(//div[@class="ace_line"])[4]')))
            # mass = driver.find_element(By.XPATH,
            #                         '(//div[@class="ace_line"])[4]').text
            # logging.info(mass)
            # assert "Row Inserted\/updated Successfully" in mass
            # utils.search_by_id(driver, DEDICATED_PRODUCT_9_ID)
            # driver.find_element(By.XPATH, "//span[text()='Inventory']").click()
            # WebDriverWait(driver, 60).until(
            # EC.presence_of_element_located((By.XPATH, "//td[text()='Inventory Locations']")))
            # quantity_updated = driver.find_element(By.XPATH, "(//td[contains(text(),'Default Inventory')]//parent::tr//td)[2]").text
            # logging.info(quantity_updated)
            # assert quantity_updated == str(int(quantity)-2)
            
            # logging.info("19 pass")
            # 020 - Verify if inventory location does not have sufficient inventory available for products
            # Validation message should display
            
                WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
                driver.find_element(By.ID, xpath.pimcoreMenu_id).click()

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
                driver.find_element(By.XPATH, xpath.csvImport).click()

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
                data_type = driver.find_element(By.NAME, xpath.importTypeName)
                data_type.send_keys("TrackingInfo")
                time.sleep(1)
                data_type.send_keys(Keys.ENTER)
                if i is "Login_Pac_Admin":
                    time.sleep(1)
                    driver.find_element(By.XPATH,"//div[contains(@id,'storeList-trigger-picker')]").click()
                    driver.find_element(By.NAME, "storeList").send_keys(DEDICATED_STORE)
                    time.sleep(2)
                    WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, "//li[text()='"+DEDICATED_STORE+"']")))
                    driver.find_element(By.XPATH,"//li[text()='"+DEDICATED_STORE+"']").click()
                    time.sleep(5)

                utils.update_csv("order_trackingInfo.csv", "OrderId", DEDICATED_ORDER_2_ID)
                utils.update_csv("order_trackingInfo.csv", "ShippingId", DEDICATED_ORDER_2_ORDER_SHIPMENT_ID)
                utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", DEDICATED_ORDER_2_LINE_ITEM_ID)
                utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", DEDICATED_STORE_LOCATION_2_ID)
                utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")

                path = os.path.join(os.getcwd() + "/credencys_test_ui/" + "order_trackingInfo.csv")
                driver.find_element(By.XPATH, xpath.csvFileSelectButton).send_keys(path)

                driver.find_element(By.XPATH, xpath.upload).click()
                if i is "Login_Pac_Admin":
                    utils.close_All_Tabs(driver)

                    utils.Assets_import_Pac_admin(driver)
                elif i is "Login_Store":
                    #Logout
                    driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                    logging.info("Logged out")
                    WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("STORE")
                    utils.Assets_import_Store(driver)
                time.sleep(5)
                list1 = driver.find_elements(By.XPATH, "//div[contains(text(),'tracking_details')]")
                num = len(list1)
                time.sleep(5)
                row = driver.find_element(By.XPATH, f"(//div[contains(text(),'tracking_details')])[{num - 1}]")
                actions = ActionChains(driver)
                actions.double_click(row).perform()

                try:
                    WebDriverWait(driver, 60).until(
                        EC.element_to_be_clickable((By.XPATH, xpath.yes))
                    )
                    driver.find_element((By.XPATH, xpath.yes)).click()
                    logging.info("Click on message")
                except:
                    pass
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, '(//div[@class="ace_line"])[4]')))
                mass = driver.find_element(By.XPATH,
                                        '(//div[@class="ace_line"])[4]').text
                logging.info(mass)
                assert "Insufficient Inventory in the location for line item" in mass

                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, xpath.logout)))
                driver.find_element(By.XPATH, xpath.logout).click()
            utils.update_csv("order_trackingInfo.csv", "OrderId", "2341")
            utils.update_csv("order_trackingInfo.csv", "ShippingId", "2341")
            utils.update_csv("order_trackingInfo.csv", "OrderLineItemId", "2431")
            utils.update_csv("order_trackingInfo.csv", "InventoryLocationId", "1233")
            utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")
        
            Success_List_Append(
                "test_MKTPL_523_Trackinginfoimport_020_MKTPL_495_Trackinginfoimport_019",
                "Verify if inventory location does not have sufficient inventory available for products",
                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_523_Trackinginfoimport_020_MKTPL_495_Trackinginfoimport_019",
                            "Verify if inventory location does not have sufficient inventory available for products", "Fail")
        Failure_Cause_Append("test_MKTPL_523_Trackinginfoimport_020_MKTPL_495_Trackinginfoimport_019",
                             "Verify if inventory location does not have sufficient inventory available for products", e)
        raise e

def test_MKTPL_2827_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018_019_020_021_022_023_024():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    RANDOM_NUMBER = ("".join([str(randint(0, 9)) for i in range(7)]))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_2827_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018_019_020_021_022_023_024.png"
        ) as driver:
            # 001. Verify if airlineCodeICAO is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "airlineCodeICAO data can not be empty. It is a mandatory Field." == api_response["error_message"]
            logging.info("001. POST - API display validation message and order is not be created in the system")
            # 002. Verify if airlineCodeICAO is invalid
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN, 
                                                    "*(&", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 405
            assert "Invalid Token!" == api_response["error_message"]
            logging.info("002. POST - API  display validation message and order not be created in the system")
            # 003. Verify if orderKey is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", "",
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1]  orderKey data can not be empty. It is a mandatory Field." == api_response["error_message"]
            logging.info("003. POST - API  display validation message and order not be created in the system")
            # 004. Verify if externalId is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    "", "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] externalId data can not be empty. It is a mandatory Field." == api_response["error_message"]
            logging.info("004. POST - API  display validation message and order not be created in the system")
            # 005. Verify if paymentId is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => payment\r\n                     [1] paymentId data can not be empty. It is a mandatory Field." == api_response["error_message"]
            logging.info("005. POST - API  display validation message and order not be created in the system")
            # 006. Verify if authAmount is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "", 0, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => payment [1] => authAmount currency data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("006. POST - API  display validation message and order not be created in the system")
            # 007. Verify if currency/value in authAmount is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", "", "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => payment [1] => authAmount value data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("007. POST - API  display validation message and order not be created in the system")
            # 008. Verify if value in authAmount is non-numeric
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", "dd", "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for auth Amount" == \
                   api_response["error_message"]
            logging.info("008. POST - API  display validation message and order not be created in the system")
            # 009. Verify if billingAddress is empty
            # 010. Verify if salutation/title/firstName/middleName/lastName/address1/address2/city/state/postalCode/countryCode/email/phone in billingAddress is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "", "", "",
                                                    "", '',
                                                    '', '',
                                                    '', 'USD', 233, 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => payment\r\n                     [1] => billingAddress firstName data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("009. POST - API  display validation message and order not be created in the system")
            logging.info("010. POST - API  display validation message and order not be created in the system")
            # In Order [1] => payment\r\n                     [1] billingAddress data can not be empty. It is a mandatory Field.
            # 011. Verify if grossTotal is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', '', '', 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1]  => orderSummary => grossTotal currency data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("011. POST - API  display validation message and order not be created in the system")
            # 012. Verify if currency/value in grossTotal is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', '', 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1]  => orderSummary => grossTotal value data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("012. POST - API  display validation message and order not be created in the system")
            # 013. Verify if value in grossTotal is non-numeric
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 'rf', 'shipping_rule_tax', 'USD', '12', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1]  => orderSummary => grossTotal value field should be numeric type of data. Invalid type of data provided." == \
                   api_response["error_message"]
            logging.info("013. POST - API  display validation message and order not be created in the system")
            # 014. Verify if taxes is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, '', '', '', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => orderSummary => taxes [1] type data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("014. POST - API  display validation message and order not be created in the system")
            # 015. Verify if type/taxAmount/rate in taxes is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, '', 'USD', 200, 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => orderSummary => taxes [1] type data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("015. POST - API  display validation message and order not be created in the system")
            # 016. Verify if currency/value in taxAmount is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', '', 200, 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => orderSummary => taxes [1] => taxAmount currency data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("016. POST - API  display validation message and order not be created in the system")
            # 017. Verify if value in taxAmount is non-numeric
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', 'USD', 'GG', 7, 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => orderSummary => taxes [1] => taxAmount value field should be numeric type of data. Invalid type of data provided." == \
                   api_response["error_message"]
            logging.info("017. POST - API  display validation message and order not be created in the system")
            # 018. Verify if value in rate is non-numeric
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', 'USD', '28', 'dd', 'ram', 'ram', '<EMAIL>', 'USD', 200)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] rate field should be numeric type of data. Invalid type of data provided." == \
                   api_response["error_message"]
            logging.info("018. POST - API  display validation message and order not be created in the system")
            # 019. Verify if netTotal is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', 'USD', '28',
                                                    'dd', 'ram', 'ram', '<EMAIL>', '', '')
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1]  => orderSummary => netTotal currency data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("019. POST - API  display validation message and order not be created in the system")
            # 020. Verify if currency/value in netTotal is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', 'USD', '28',
                                                    'dd', 'ram', 'ram', '<EMAIL>', 'USD', '')
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1]  => orderSummary => netTotal value data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("020. POST - API  display validation message and order not be created in the system")
            # 021. Verify if value in netTotal is non-numeric
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', 'USD', '28',
                                                    'dd', 'ram', 'ram', '<EMAIL>', 'USD', 'FF')
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1]  => orderSummary => netTotal value field should be numeric type of data. Invalid type of data provided." == \
                   api_response["error_message"]
            logging.info("021. POST - API  display validation message and order not be created in the system")
            # 022. Verify if firstName in user is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', 'USD', '28',
                                                    'dd', '', 'ram', '<EMAIL>', 'USD', 20)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => user => firstName data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("022. POST - API  display validation message and order not be created in the system")
            # 023. Verify if lastName in user is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', 'USD', '28',
                                                    'dd', 'ram', '',  '<EMAIL>', 'USD', 20)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => user => lastName data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("023. POST - API  display validation message and order not be created in the system")
            # 024. Verify if email in user is empty
            api_response = utils.order_post_payload("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                    "CRE", "13KNTV34",
                                                    "", "", RANDOM_NAME,
                                                    RANDOM_NAME, "USD", 200, "BBAA-0324111-88",
                                                    "auty", "matt", "34k",
                                                    "arizona", 'Los angeles',
                                                    '78945', 'US',
                                                    '<EMAIL>', 'USD', 20, 'shipping_rule_tax', 'USD', '28',
                                                    'dd', 'ram', 'ram', '', 'USD', 20)
            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => user => email data can not be empty. It is a mandatory Field." == \
                   api_response["error_message"]
            logging.info("024. POST - API  display validation message and order not be created in the system")

            Success_List_Append("test_MKTPL_2827_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018_019_020_021_022_023_024",
                                "Ground Tool should create Failed Orders,Order Items, Order Fulfillment and Order Shipment if the order payload received from Airside has any errors or validations.", "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_2827_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018_019_020_021_022_023_024",
                            "Ground Tool should create Failed Orders,Order Items, Order Fulfillment and Order Shipment if the order payload received from Airside has any errors or validations.", "Fail",)
        Failure_Cause_Append("test_MKTPL_2827_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012_013_014_015_016_017_018_019_020_021_022_023_024",
                             "Ground Tool should create Failed Orders,Order Items, Order Fulfillment and Order Shipment if the order payload received from Airside has any errors or validations.", e,)
        raise e

def test_MKTPL_2827_OrderAPI_26_027_028():
    RANDOM_NAME = "From_Automation_"+"".join(random.choices(string.ascii_letters, k=7))
    try:
        with utils.services_context_wrapper("test_MKTPL_2827_OrderAPI_26.png") as driver:
            driver.maximize_window()
            role = ["Login_Pac_Admin", "Login_Store", "Login_Airline"]
            for i in role:
                if i is "Login_Pac_Admin":
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("PAC ADMIN")
                elif i is "Login_Store":
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                elif i is "Login_Airline":
                    utils.Pac_Credentials.Login_Airline(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                # 026. Verify the Order Fulfillment and Order Shipment fields in order object
                utils.wait_for_style_attribute(driver, 40)
                utils.search_by_id(driver,  DEDICATED_ORDER_2_ID)
                # click at Children Grid
                (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
                (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, '//div[text()="Order Shipment "]'))))
                # Fulfillment is not there
                logging.info("026. Fulfillment & Shipment data should auto-populate in the Order Fulfillment and Order Shipment fields")
                # 027. Verify the Order Key, External ID, Order Fulfillment, Order Shipment, Airline, Flight fields for
                # pac/airline/store users in order object
                orderKey = driver.find_element(By.XPATH, xpath.readModeLock+'/../..//span[contains(text(),"Order Key")]').is_displayed()
                externalID = driver.find_element(By.XPATH, xpath.readModeLock+'/../..//span[contains(text(),"External ID")]').is_displayed()
                flightInformation = driver.find_element(By.XPATH, '//div[contains(text(),"Flight Information")]').is_displayed()
                airline = driver.find_element(By.XPATH, xpath.readModeLock+'/../..//span[contains(text(),"Airline")]').is_displayed()
                flight = driver.find_element(By.XPATH, '//span[contains(text(),"Flight")]').is_displayed()
                assert [orderKey, externalID, flightInformation, airline, flight]
                logging.info('027. Order Key, External ID, Order Fulfillment, Order Shipment, Airline, Flight fields'
                            ' should be in read only mode for store users')
                # 028. Verify if selected flight is not associated with the airline in order object
                button = driver.find_element(By.XPATH, '//span[contains(text(),"Flight")]/../../..//span[contains(@class,"small pimcore_icon_search")]')
                driver.execute_script("arguments[0].scrollIntoView();", button)
                driver.execute_script("arguments[0].click();", button)
                (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))))
                (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                            (By.XPATH, '//span[text()="Path"]/../../../../../../../../..//td[2]'))))
                select_store = driver.find_element(
                    By.XPATH, '//span[text()="Path"]/../../../../../../../../..//td[2]')
                action = ActionChains(driver)
                action.double_click(select_store).perform()
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
                driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
                assert WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, '//div[text()="Flight Does not Belong to the Selected Airline"]'))).is_displayed()
                logging.info(' 028. Validation message display')
                #Logout
                driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                logging.info("Logged out")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))

            Success_List_Append("test_MKTPL_2827_OrderAPI_26_027_028",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_2827_OrderAPI_26_027_028",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_2827_OrderAPI_26_027_028",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_2827_OrderAPI_029():
    try:
        with utils.services_context_wrapper("test_MKTPL_2827_OrderAPI_029.png") as driver:
            driver.maximize_window()
            role = ["Login_Pac_Admin", "Login_Store"]
            for i in role:
                if i is "Login_Pac_Admin":
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("PAC ADMIN")
                elif i is "Login_Store":
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("STORE")
                utils.wait_for_style_attribute(driver, 40)
                utils.search_by_id(driver,  DEDICATED_INVALID_ORDER_ID)
                WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.printEReceipt)))
                driver.find_element(By.XPATH, xpath.printEReceipt).click()
                WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, "//div[text()='Order is Not Valid']")))
                logging.info("Validation displayed")
                driver.find_element(By.XPATH, "(" + xpath.okButtonXpath +")[last()]").click()
                #Logout
                driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                logging.info("Logged out")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))

            Success_List_Append("test_MKTPL_2827_OrderAPI_029",
                                "Verify by clicking on the Print E-Receipt button", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_2827_OrderAPI_029",
                            "Verify by clicking on the Print E-Receipt button", "Fail")
        Failure_Cause_Append("test_MKTPL_2827_OrderAPI_029",
                             "Verify by clicking on the Print E-Receipt button",
                             e)
        raise e

def test_MKTPL_3328_OrderAPI_001_002_003_004_007_008_010_011_013_016_017_018_022_023_026_030_031_033_038_041():
    try:
        with utils.services_context_wrapper("test_MKTPL_3328_OrderAPI_001_002_003_004_007_008_010_011_013_016_017_018_022_023_026_030_031_033_038_041.png") as driver:
            # 001
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER,"2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID,"PENDING","CALL_CREW","INTERNAL","home_delivery",
                                                   "","Outside demo","GA","USD",JWT_TOKEN,"POST",CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "In Order [1] seatInfo => seatClass data can not be empty. It is a mandatory Field." in api_response["error_message"]

            logging.info(" 001 API should display validation message and order should not be created in the system ")

            # 002
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))

            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER,"2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID,"PENDING","CALL_CREW","INTERNAL","home_delivery",
                                                   "Outside demo","","GA","USD",JWT_TOKEN,"POST",CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "In Order [1] seatInfo => seatNumber data can not be empty. It is a mandatory Field." in api_response[
                "error_message"]

            logging.info(" 002 API should display validation message and order should not be created in the system")
            # 003
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))

            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery","Outside demo","Outside demo", "", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] groundSystem data can not be empty. It is a mandatory Field." in api_response["error_message"]

            logging.info(" 003 API should display validation message and order should not be created in the system")

            # 004
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery","Outside demo","Outside demo", RANDOM_NAME, "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] groundSystem can only be GA" in api_response[
                "error_message"]

            logging.info(" 004 API should display validation message and order should not be created in the system")

            # 007
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, "DELIVERED",
                                                   "CALL_CREW", "", "home_delivery", "Outside demo","Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => payment [1] paymentService data can not be empty. It is a mandatory Field." in api_response[
                "error_message"]

            logging.info(" 007 API should display validation message and order should not be created in the system")

            # 008
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, "DELIVERED",
                                                   "CALL_CREW", RANDOM_NAME, "home_delivery", "Outside demo","Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => payment [1] => paymentService is not Valid " in \
                   api_response["error_message"]

            logging.info(" 008 API should display validation message and order should not be created in the system")

            # 010
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, RANDOM_NAME,
                                                   "CALL_CREW", "INTERNAL", "home_delivery", "Outside demo","Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => lineItems\r\n                 [1] => Status is Not Valid" in api_response["error_message"]
            logging.info(" 010 API should display validation message and order should not be created in the system")

            # 011
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, "DELIVERED",
                                                   RANDOM_NAME, "INTERNAL", "home_delivery", "Outside demo","Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] =>  Status is Not Valid" in api_response[
                "error_message"]
            logging.info(" 011 API should display validation message and order should not be created in the system")

            # 013
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random",  DEDICATED_STORE_ID, "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery", "",
                                                   "",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] seatInfo => seatClass data can not be empty. It is a mandatory Field." in api_response[
                "error_message"]

            logging.info(" 013 API should display validation message and order should not be created in the system ")
            
            # 016
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00", "random",
                                                   DEDICATED_STORE_ID, "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "current_flight", "",
                                                   "",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => shipments [1]  => address seatClass data can not be empty. It is a mandatory Field." in api_response[
                "error_message"]

            logging.info(" 016 Vaidation message should display in shipment object")

            # 017
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","", "CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00",
                                                   "random",
                                                   DEDICATED_STORE_ID, "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "next_flight", "Outside demo",
                                                   "Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => shipments [1]  => address flightNumber data can not be empty. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info(" 017 flightNumber, scheduledDepartureTime, departureAirportCodeIATA and arrivalAirportCodeIATA parameters should be mandatory in shipment")

            # 018
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","", "", "", "CRE", RANDOM_NUMBER,
                                                   "2024-05-24T17:39:51.287+00:00", "random", DEDICATED_STORE_ID,
                                                   "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "next_flight", "Outside demo",
                                                   "Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => shipments [1]  => address flightNumber data can not be empty. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info(" 018 API should display validation message and order should not be created in the system")

            # 022
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","","rangwala","456","Manchester","England","12345","GB","<EMAIL>", "", "sx", "13KNTV34", "CRE", RANDOM_NUMBER,
                                                   "2024-05-24T17:39:51.287+00:00", "random", DEDICATED_STORE_ID,
                                                   "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery", "Outside demo",
                                                   "Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => shipments [1]  => address firstName data can not be empty. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info(" 022 firstName/lastName/address1/city/state/postalCode/countryCode/email parameters should be mandatory in shipment")

            # 023
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","", "", "", "", "", "", "",
                                                   "", "", "sx", "13KNTV34", "CRE",
                                                   RANDOM_NUMBER,
                                                   "2024-05-24T17:39:51.287+00:00", "random", DEDICATED_STORE_ID,
                                                   "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery", "Outside demo",
                                                   "Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => shipments [1]  => address firstName data can not be empty. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info(" 023 API should display validation message and order should not be created in the system")

            # 026
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("", "", "", "ismail", "rangwala", "456", "Manchester", "England", "12345",
                                                   "GB",
                                                   "<EMAIL>", "", "sx", "13KNTV34", "CRE",
                                                   RANDOM_NUMBER,
                                                   "2024-05-24T17:39:51.287+00:00", "random", DEDICATED_STORE_ID,
                                                   "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery", "Outside demo",
                                                   "Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => user => firstName data can not be empty. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info(' 026 User parameter should be mandatory if mode is "home_delivery" or "next_flight" or "next_flight_short_notice" in any one shipment')

            # 031
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00", "random", "",
                                                   "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery", "Outside demo",
                                                   "Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => lineItems\r\n                     1 => associateStore data can not be empty. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info(" 031 API should display validation message and order should not be created in the system")

            # 030
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00", "",DEDICATED_STORE_ID,
                                                   "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery", "Outside demo",
                                                   "Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => lineItems\r\n                     1 => retailerCode data can not be empty. It is a mandatory Field." in api_response[
                "error_message"]

            logging.info(" 030 API should display validation message and order should not be created in the system")

            # 033
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00", "random", RANDOM_NUMBER,
                                                   "DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery", "Outside demo",
                                                   "Outside demo",
                                                   "GA", "USD",
                                                   ORDER_JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => lineItems\r\n                [1] => associateStore is Not Valid" in \
                   api_response["error_message"]

            logging.info(" 033 API should display validation message and order should not be created in the system")

            # 038
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }
            response = requests.request("GET", GET_ORDER_URL+str(DEDICATED_ORDER_1_ID), headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            
            assert response.status_code == 200 or response.status_code == 201
            paymentService = resp_data["data"]["orders"]["payment"][0]["paymentService"]
            seatClass = resp_data["data"]["orders"]["seatInfo"]["seatClass"]
            seatNumber = resp_data["data"]["orders"]["seatInfo"]["seatNumber"]
            # logging.info(paymentService)
            # logging.info(seatClass)
            # logging.info(seatNumber)
            assert paymentService
            assert seatClass
            assert seatNumber

            logging.info(" 038 API should respond with 200 status code and it should display following parameters in the response: - paymentService- seatInfo")

            # 041
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN
            }
            response = requests.request("GET", GET_ORDER_URL+str(DEDICATED_ORDER_1_ID), headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "salutation" in resp_data["data"]["orders"]["shipments"][0]["address"]
            # salutation = ["data"]["orders"]["shipments"][0]["address"]["salutation"]
            assert "firstName" in resp_data["data"]["orders"]["shipments"][0]["address"]
            # firstName = resp_data["data"]["orders"]["shipments"][0]["address"]["firstName"]
            assert "title" in resp_data["data"]["orders"]["shipments"][0]["address"]
            # title = ["data"]["orders"]["shipments"][0]["address"]["title"]
            assert "middleName" in resp_data["data"]["orders"]["shipments"][0]["address"]
            # middleName = ["data"]["orders"]["shipments"][0]["address"]["middleName"]
            lastName = resp_data["data"]["orders"]["shipments"][0]["address"]["lastName"]
            address1 = resp_data["data"]["orders"]["shipments"][0]["address"]["address1"]
            assert "address2" in resp_data["data"]["orders"]["shipments"][0]["address"]
            # address2 = ["data"]["orders"]["shipments"][0]["address"]["address2"]
            city = resp_data["data"]["orders"]["shipments"][0]["address"]["city"]
            state = resp_data["data"]["orders"]["shipments"][0]["address"]["state"]
            postalCode = resp_data["data"]["orders"]["shipments"][0]["address"]["postalCode"]
            countryCode = resp_data["data"]["orders"]["shipments"][0]["address"]["countryCode"]
            email = resp_data["data"]["orders"]["shipments"][0]["address"]["email"]
            assert "phone" in resp_data["data"]["orders"]["shipments"][0]["address"]
            # phone = ["data"]["orders"]["shipments"][0]["address"]["phone"]
            # logging.info(salutation)
            # logging.info(firstName)
            # logging.info(title)
            # logging.info(middleName)
            # logging.info(lastName)
            # logging.info(address1)
            # logging.info(address2)
            # logging.info(city)
            # logging.info(state)
            # logging.info(postalCode)
            # logging.info(countryCode)
            # logging.info(email)
            # logging.info(phone)
            
            assert lastName
            assert address1
            assert city
            assert state
            assert postalCode
            assert countryCode
            assert email

            logging.info(" 041 API should respond with 200 status code and it should display following parameters in the response:")

        Success_List_Append("test_MKTPL_3328_OrderAPI_001_002_003_004_007_008_010_011_013_016_017_018_022_023_026_030_031_033_038_041",
                                "Verify the the manage shipping setting",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3328_OrderAPI_001_002_003_004_007_008_010_011_013_016_017_018_022_023_026_030_031_033_038_041",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_3328_OrderAPI_001_002_003_004_007_008_010_011_013_016_017_018_022_023_026_030_031_033_038_041",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_3328_OrderAPI_024_025_048():
    try:
        with utils.services_context_wrapper("test_MKTPL_3328_OrderAPI_024_025_048.png") as driver:
            driver.maximize_window()
            role = ["Login_Pac_Admin", "Login_Store", "Login_Airline"]
            for i in role:
                if i is "Login_Pac_Admin":
                    utils.Pac_Credentials.Login_Pac_Admin(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                    logging.info("PAC ADMIN")
                elif i is "Login_Store":
                    utils.Pac_Credentials.Login_store(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")
                elif i is "Login_Airline":
                    utils.Pac_Credentials.Login_Airline(driver)
                    act_title = driver.find_element(
                        By.XPATH, xpath.logout
                    ).get_attribute("id")
                    assert act_title == "pimcore_logout"
                    logging.info("Login is successful.")

                # 024 025
                utils.search_by_id(driver, DEDICATED_ORDER_1_ORDER_SHIPMENT_ID)
                logging.info("Searched")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'shipmentMode')))
                shipmentMode = driver.find_element(By.NAME, 'shipmentMode').get_attribute("value")
                assert "Home Delivery" in shipmentMode
                logging.info("shipmentMode")
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, '//span[text()="Delivery Information"]')))
                Delivery_Information = driver.find_element(By.XPATH, '//span[text()="Delivery Information"]')
                Delivery_Information.click()
                logging.info("Delivery Information")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'firstName')))
                firstName = driver.find_element(By.NAME, 'firstName').get_attribute("value")
                assert "ismail" in firstName
                if i is "Login_Pac_Admin" or i is "Login_Store":
                    driver.find_element(By.NAME, 'firstName').clear()
                logging.info("firstName")

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'lastName')))
                lastName = driver.find_element(By.NAME, 'lastName').get_attribute("value")
                assert "rangwala" in lastName
                
                logging.info("lastName")

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'address1')))
                address1 = driver.find_element(By.NAME, 'address1').text
                assert "456" in address1
                
                logging.info("address1")

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'postalCode')))
                postalCode = driver.find_element(By.NAME, 'postalCode').get_attribute("value")
                assert "12345" in postalCode
                
                logging.info("postalCode")

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'shippingCity')))
                shippingCity = driver.find_element(By.NAME, 'shippingCity').get_attribute("value")
                assert "Manchester" in shippingCity
                
                logging.info("shippingCity")

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'shippingState')))
                shippingState = driver.find_element(By.NAME, 'shippingState').get_attribute("value")
                assert "England" in shippingState
                
                logging.info("shippingState")

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'shippingCountry')))
                shippingCountry = driver.find_element(By.NAME, 'shippingCountry').get_attribute("value")
                assert "United Kingdom" in shippingCountry
                logging.info("shippingCountry")

                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, 'email')))
                email = driver.find_element(By.NAME, 'email').get_attribute("value")
                assert "<EMAIL>" in email
                logging.info("email")
                if i is "Login_Pac_Admin" or i is "Login_Store":
                    WebDriverWait(driver, 60).until(
                        EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
                    driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
                    logging.info("saveAndPublish")
                    
                    assert WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, '//div[text()="firstName is Mandatory if Shipment Mode is home_delivery"]'))).is_displayed()
                    driver.find_element(By.XPATH, "("+xpath.okButtonXpath+")[last()]").click()
                    logging.info(" 024 025 is successfully ")
                utils.close_All_Tabs(driver)
                # 048
                if i is "Login_Store":
                    RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))

                    utils.Trackinginfoimport_log_store("OrderLineStatus", RANDOM_NAME,
                                                    "Invalid Order Line Status")

                    logging.info(" 048 Validation message should display")
                    utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")
                #Logout
                driver.find_element(By.XPATH,"//a[@id='pimcore_logout']").click()
                logging.info("Logged out")
                WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME,"username")))

            Success_List_Append("test_MKTPL_3328_OrderAPI_024_025_048",
                                "Verify the the manage shipping setting",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3328_OrderAPI_024_025_048",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_3328_OrderAPI_024_025_048",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_3336_JWTToken_002_004():
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_3336_JWTToken_002_004.png") as driver:

            # 002 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34",RANDOM_NAME,RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", "40643","DELIVERED", "CALL_CREW",
                                                   "INTERNAL", "home_delivery","Outside demo","Outside demo", "GA", "USD",ORDER_JWT_TOKEN , "POST",
                                                   CONSUME_ORDER_URL)
            assert 405 == api_response["error_code"]
            assert "AUTHORIZATION_FAILED" in api_response["text_code"]
            assert "Invalid Token!" in api_response["error_message"]
            logging.info(' 002 Respone code should be 401, Error_code should be 405, text_code should be "AUTHORIZATION_FAILED" and Error_message should be "Invalid Token!"')

            # 004 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE", RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", "40643", "DELIVERED",
                                                   "CALL_CREW",
                                                   "INTERNAL", "home_delivery","Outside demo","Outside demo", "GA", "USD",
                                                   "", "POST",
                                                   CONSUME_ORDER_URL)
            assert 405 == api_response["error_code"]
            assert "AUTHORIZATION_FAILED" in api_response["text_code"]
            assert "Authorization Header Missing" in api_response["error_message"]
            logging.info(' 004 Respone code should be 401, Error_code should be 405, text_code should be "AUTHORIZATION_FAILED" and Error_message should be "Authorization Header Missing"')

            Success_List_Append("test_MKTPL_3336_JWTToken_002_004",
                                "Verify the the manage shipping setting",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3336_JWTToken_002_004",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_3336_JWTToken_002_004",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_3327_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012():
    try:
        with utils.services_context_wrapper(
                "test_MKTPL_3327_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012.png") as driver:

            # 001 POST
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456",
                                                   "Manchester","England","12345","GB","<EMAIL>",
                                                   "","sx","13KNTV34","CRE","926574","2024-05-24T17:39:51.287+00:00",
                                                   "random", DEDICATED_STORE_ID,"DELIVERED","CALL_CREW","INTERNAL","home_delivery",
                                                   "Outside demo","Outside demo","GA","USD" ,JWT_TOKEN,"POST",
                                                   CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1]  Order Already Exists with the Same ExternalId" in api_response["error_message"]

            logging.info(' 001 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "Order [1] =>  Order Already Exists with the Same ExternalId"')

            # 002 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))

            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID,"DELIVERED", RANDOM_NAME, "INTERNAL", "home_delivery","Outside demo",
                                                   "Outside demo","GA","USD", JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] =>  Status is Not Valid" in api_response["error_message"]

            logging.info(' 002 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "Order [1] => Status is Not Valid"')

            # 003 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER,"2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID,RANDOM_NAME,"CALL_CREW","INTERNAL","home_delivery","Outside demo","Outside demo","GA","USD",JWT_TOKEN,"POST",CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => lineItems\r\n                 [1] => Status is Not Valid" in api_response["error_message"]

            logging.info(' 003 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "lineItems [1] => Status is Not Valid"')

            # 004 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))

            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID,"DELIVERED", "CALL_CREW", RANDOM_NAME, "home_delivery","Outside demo","Outside demo",
                                                   "GA","USD", JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => payment [1] => paymentService is not Valid" in api_response["error_message"]

            logging.info(' 004 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "payment [1] => paymentService is not Valid"')

            # 005 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))

            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID,"DELIVERED", "CALL_CREW", "INTERNAL", RANDOM_NAME,
                                                   "Outside demo","Outside demo","GA","USD", JWT_TOKEN, "POST", CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => shipments [1] => Invalid order Shipment Mode value." in api_response["error_message"]

            logging.info(' 005 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "shipments [1] => Invalid order Shipment Mode value."')

            # 006 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))

            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER,"2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, "DELIVERED", "CALL_CREW", "INTERNAL", "home_delivery",
                                                   "Outside demo","Outside demo",RANDOM_NAME,"USD", JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] groundSystem can only be GA" in api_response["error_message"]
            logging.info(' 006 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "groundSystem can only be GA"')

            # 007 POST
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE","","2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, "DELIVERED", "CALL_CREW", "INTERNAL", "home_delivery",
                                                   "Outside demo","Outside demo","GA","USD", JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] externalId data can not be empty. It is a mandatory Field." in api_response["error_message"]
            logging.info('007 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "<FieldName> data can not be empty. It is a mandatory Field."')

            # 008 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID,"DELIVERED",
                                                   "CALL_CREW", "INTERNAL", "home_delivery",
                                                   "Outside demo","Outside demo","GA", 111, JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1]  => orderSummary => netTotal currency field should be string type of data. Invalid type of data provided." in api_response["error_message"]
            logging.info(' 008 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "<FeildName>  field should be <type> type of data. Invalid type of data provided."')

            # 009 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER,"2024-05-24T17:39:51.287+00:00","random", DEDICATED_STORE_ID, "DELIVERED", "CALL_CREW", "INTERNAL", "home_delivery",
                                                   "Outside demo","Outside demo","GA", "ABC", JWT_TOKEN, "POST", CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1]  => orderSummary => netTotal The entered Currency is not set as default for the store" in api_response["error_message"]
            logging.info(' 009 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "<FeildName> The entered Currency is not set as default for the store"')

            # 010 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER, "2024-05-24T17:39:51.287+00:00", "random", DEDICATED_STORE_ID,"DELIVERED", "CALL_CREW",
                                                   "INTERNAL", "home_delivery","Outside demo","Outside demo", "GA", "", JWT_TOKEN, "POST",
                                                   CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1]  => orderSummary => netTotal currency data can not be empty. It is a mandatory Field." in \
                   api_response["error_message"]
            logging.info(' 010 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "<FeildName> Currency and Value Both should be present."')

            # 011 POST
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload("John","Doe","<EMAIL>","ismail","rangwala","456","Manchester","England","12345","GB","<EMAIL>","","sx","13KNTV34","CRE",RANDOM_NUMBER, RANDOM_NAME,"random", DEDICATED_STORE_ID, "DELIVERED", "CALL_CREW",
                                                   "INTERNAL", "home_delivery", "Outside demo","Outside demo","GA", "USD", JWT_TOKEN, "POST",
                                                   CONSUME_ORDER_URL)
            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] Created Time format is not valid" in api_response["error_message"]
            logging.info(' 011 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "Created Time format is not valid"')

            # 012 POST
            payload = ""
            headers = {
                'Authorization': JWT_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("POST",CONSUME_ORDER_URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert 103 == data["error_code"]
            assert "VALIDATION_FAILED" in data["text_code"]
            assert "Request Body Cannot Be Empty" in data["error_message"]
            logging.info(' 012 Respone code should be 400, Error_code should be 103, text_code should be "VALIDATION_FAILED" and Error_message should be "Request Body Cannot Be Empty."')

            Success_List_Append("test_MKTPL_3327_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012", "Verify the the manage shipping setting",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3327_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012",
                            "Verify the active catalog assignments associated", "Fail")
        Failure_Cause_Append("test_MKTPL_3327_OrderAPI_001_002_003_004_005_006_007_008_009_010_011_012",
                             "Verify the active catalog assignments associated", e)
        raise e

def test_MKTPL_4073_OrderAPI_007():
    RANDOM_NUMBER = ("".join([str(randint(0, 9)) for i in range(7)]))
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_4073_OrderAPI_007.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, DEDICATED_ORDER_1_ID)  # Order folder ID
            redIcon = (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                (By.XPATH, '((//span[text()="Automation_Order_1"])/..//span)[1]'))).value_of_css_property("background-image"))
            logging.info(redIcon)
            assert 'approve.svg' in redIcon
            logging.info('The successful order is displayed with a green tick icon, indicating successful processing.')
            Success_List_Append("test_MKTPL_4073_OrderAPI_007",
                                "To verify incorrect Icon Display for Successful Orders",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_4073_OrderAPI_007",
                            "To verify incorrect Icon Display for Successful Orders", "Fail",)
        Failure_Cause_Append("test_MKTPL_4073_OrderAPI_007",
                             "To verify incorrect Icon Display for Successful Orders", e,)
        raise e

def test_MKTPL_4073_order_014():
    try:
        with utils.services_context_wrapper("test_MKTPL_4073_order_014.png") as driver:

            # 014
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))
            api_response = utils.order_api_payload_4840_5043("", RANDOM_NAME,
                                                             "45052f76-fcc3-4e15-bdee-439f1b9410512third",
                                                             "Home Delivery", ORDER_JWT_TOKEN, "POST",
                                                             CONSUME_ORDER_URL)

            assert 103 == api_response["error_code"]
            assert "VALIDATION_FAILED" in api_response["text_code"]
            assert "In Order [1] => lineItems\r\n                     1 => title data can not be empty. It is a mandatory Field." in \
                   api_response["error_message"]

            logging.info(
                ' 014 In response it should give error message :"title data can not be empty. It is a mandatory Field.""')

            Success_List_Append("test_MKTPL_4073_order_014",
                                "As a store admin, i should be able to manage shipping on the ground tool",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_4073_order_014",
                            "verify the active calatlog assokciated",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_4073_order_014",
                             "verify the active catalog assignments associated",
                             e)
        raise e

def test_MKTPL_5503_floatingpoint_002_004_006_008_010_12_014_016_018_020_022_024_026_028_030_032_034():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_5503_floatingpoint_002_004_006_008_010_12_014_016_018_020_022_024_026_028_030_032_034.png"
        ) as driver:
            # 002. Verify if user enters unitPrice with more than 3 decimal precision in line item
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL, ORDER_JWT_TOKEN,
                                                                  "20.2227", "33.444", "21.343", "200.332",
                                                                  "67.555", "12.123", "12.122", "23.345",
                                                                  "34.333", "34.444", "56.234",
                                                                  "34.434", "23.232", "23.555",
                                                                  "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => lineItems\r\n                     [1] => unitPrice Allowed upto 3 decimal places for unit Price" in api_response["error_message"]
            logging.info("002. - Post API should display validation message")
            # 004. Verify if user enters unitTax with more than 3 decimal precision in line item
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.4423234", "21.343", "200.332",
                                                                              "67.555", "12.123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => lineItems\r\n                     [1] => unitTax Allowed upto 3 decimal places for unit Tax" in \
                   api_response["error_message"]
            logging.info("004. - Post API should display validation message")
            # 006. Verify if user enters unitDiscount with more than 3 decimal precision in line item
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.22", "21.33343",
                                                                              "200.332",
                                                                              "67.555", "12.123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => lineItems\r\n                     [1] => unitDiscount Allowed upto 3 decimal places for unit Discount" in \
                   api_response["error_message"]
            logging.info("006. - Post API should display validation message")
            # 008. Verify if user enters unitNet with more than 3 decimal precision in line item
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.333332",
                                                                              "67.555", "12.123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => lineItems\r\n                     [1] => unitNet Allowed upto 3 decimal places for unit Net" in \
                   api_response["error_message"]
            logging.info("008. - Post API should display validation message")
            # 010. Verify if user enters unitGross with more than 3 decimal precision in line item
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54555", "12.123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => lineItems\r\n                     [1] => unitGross Allowed upto 3 decimal places for unit Gross" in \
                   api_response["error_message"]
            logging.info("010. - Post API should display validation message")
            # 012. Verify if user enters discountTotal with more than 3 decimal precision in line item
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33123", "12.122", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => lineItems\r\n                     [1] => discountTotal Allowed upto 3 decimal places for discount Total" in \
                   api_response["error_message"]
            logging.info("012. - Post API should display validation message")
            # 014. Verify if user enters taxTotal with more than 3 decimal precision in line item
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.12222", "23.345",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "In Order [1] => lineItems\r\n                     [1] => taxTotal Allowed upto 3 decimal places for tax Total" in \
                   api_response["error_message"]
            logging.info("014. - Post API should display validation message")
            # 016. Verify if user enters taxAmount with more than 3 decimal precision in line item>>taxAdjustments
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.38465",
                                                                              "34.333", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for tax Amount" in api_response["error_message"]
            logging.info("016. - Post API should display validation message")
            # 018. Verify if user enters rate with more than 3 decimal precision in line item>>taxAdjustments
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34545", "34.444", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for rate" in api_response["error_message"]
            logging.info("018. - Post API should display validation message")
            # 020. Verify if user enters authAmount with more than 3 decimal precision in payment
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34", "34.447774", "56.234",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for auth Amount" in api_response["error_message"]
            logging.info("020. - Post API should display validation message")
            # 022. Verify if user enters grossTotal with more than 3 decimal precision in order Summary
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34", "34.4", "56.237884",
                                                                              "34.434", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for gross Total" in api_response["error_message"]
            logging.info("022. - Post API should display validation message")
            # 024. Verify if user enters netTotal with more than 3 decimal precision in order Summary
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34", "34.4", "56.23",
                                                                              "34.43884", "23.232", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for net Total" in api_response["error_message"]
            logging.info("024. - Post API should display validation message")
            # 026. Verify if user enters shippingTotal with more than 3 decimal precision in order Summary
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34", "34.4", "56.23",
                                                                              "34.4", "23.23782", "23.555",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for shipping Total" in api_response["error_message"]
            logging.info("026. - Post API should display validation message")
            # 028. Verify if user enters adjustmentTotal with more than 3 decimal precision in order Summary
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34", "34.4", "56.23",
                                                                              "34.4", "23.2", "23.343435",
                                                                              "32.443", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for adjustment Total" in api_response["error_message"]
            logging.info("028. - Post API should display validation message")
            # 030. Verify if user enters totalTaxAmount with more than 3 decimal precision in order Summary
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34", "34.4", "56.23",
                                                                              "34.4", "23.2", "23.34",
                                                                              "32.46643", "43.444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for total Tax Amount" in api_response["error_message"]
            logging.info("030. - Post API should display validation message")
            # 032. Verify if user enters taxAmount with more than 3 decimal precision in order Summary>>taxes
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34", "34.4", "56.23",
                                                                              "34.4", "23.2", "23.34",
                                                                              "32.46", "43.444444", "34.344")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for tax Amount" in api_response["error_message"]
            logging.info("032. - Post API should display validation message")
            # 034. Verify if user enters rate with more than 3 decimal precision in order Summary>>taxes
            api_response, externalId = utils.order_post_payload_for_5503("POST", CONSUME_ORDER_URL,
                                                                              ORDER_JWT_TOKEN,
                                                                              "20.27", "33.44", "21.344",
                                                                              "200.3",
                                                                              "67.54", "12.33", "12.1", "23.3",
                                                                              "34.34", "34.4", "56.23",
                                                                              "34.4", "23.2", "23.34",
                                                                              "32.46", "43.44", "34.3444444")[0:2]

            assert api_response != False
            assert api_response["error_code"] == 103
            assert "Allowed upto 3 decimal places for rate" in api_response["error_message"]
            logging.info("034. - Post API should display validation message")

            Success_List_Append("test_MKTPL_5503_floatingpoint_002_004_006_008_010_12_014_016_018_020_022_024_026_028_030_032_034",
                                "Verify if user enters all value with upto 3 decimal precision in line item",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5503_floatingpoint_002_004_006_008_010_12_014_016_018_020_022_024_026_028_030_032_034",
                            "Verify if user enters all value with upto 3 decimal precision in line item",
                            "Fail",)
        Failure_Cause_Append("test_MKTPL_5503_floatingpoint_002_004_006_008_010_12_014_016_018_020_022_024_026_028_030_032_034",
                             "Verify if user enters all value with upto 3 decimal precision in line item", e)
        raise e

def test_MKTPL_5503_floatingpoint_035_036_037_038_039_040_041():
    try:
        RANDOM_NAME = "From_Automation_" + "".join(random.choices(string.ascii_letters, k=7))
        with utils.services_context_wrapper(
            "test_MKTPL_5503_floatingpoint_035_036_037_038_039_040_041.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            payload = json.dumps({
                "meta": {
                    "airlineCodeICAO": "CRE",
                    "flightNumber": "111",
                    "arrivalAirportCodeIATA": "",
                    "departureAirportCodeIATA": "",
                    "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                    "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
                },
                "orders": [
                    {
                        "orderKey": "From_Automation_improvement current flight shipment",
                        "externalId": RANDOM_NAME,
                        "basketKey": "From_Automation_3328 improvement current flight shipment",
                        "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                        "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                        "createdTime": "2024-05-24T17:39:51.287+00:00",
                        "lineItems": [
                            {
                                "title": "KRIS JUNI12OR POLAR1 BEAR BY JELLYCAT",
                                "PACVariantID": "1hospitality:mktplvb48943-var",
                                "retailerCode": "random",
                                "associateStore": str(DEDICATED_STORE_ID),
                                "fulfillmentType": "inHouse",
                                "alwaysInStock": False,
                                "imageUrl": "https://images.test/file.jpg",
                                "vendorProductVariantID": str(DEDICATED_SKU_1_ID),
                                "unitPrice": {
                                    "value": 49,
                                    "currency": "USD"
                                },
                                "unitTax": {
                                    "value": 10,
                                    "currency": "USD"
                                },
                                "unitDiscount": {
                                    "value": 4.9,
                                    "currency": "USD"
                                },
                                "unitNet": {
                                    "value": 44.1,
                                    "currency": "USD"
                                },
                                "unitGross": {
                                    "value": 44.1,
                                    "currency": "USD"
                                },
                                "quantity": 2,
                                "status": "DELIVERED",
                                "discountTotal": {
                                    "currency": "USD",
                                    "value": 9.8
                                },
                                "taxTotal": {
                                    "value": 10,
                                    "currency": "USD"
                                },
                                "discountAdjustments": [
                                    {
                                        "discountAmount": {
                                            "currency": "USD",
                                            "value": 12.12345
                                        },
                                        "adjustType": "DISCOUNT",
                                        "rate": 10.12345,
                                        "promotionCode": "asd",
                                        "promotionName": "Percentage discount 10%"
                                    }
                                ],
                                "taxAdjustments": [
                                    {
                                        "type": "shipping_rule_tax",
                                        "taxAmount": {
                                            "currency": "USD",
                                            "value": 12
                                        },
                                        "rate": 7
                                    }
                                ],
                                "salePrice": {
                                    "value": 49.12345,
                                    "currency": "USD"
                                },
                                "key": "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                            }
                        ],
                        "status": "CALL_CREW",
                        "payment": [
                            {
                                "paymentService": "INTERNAL",
                                "paymentMethod": "CARD",
                                "authAmount": {
                                    "currency": "USD",
                                    "value": 200
                                },
                                "paymentId": "BBAA-0324111-88",
                                "technicalServiceProviderTransactionId": "20220324173949153132",
                                "gatewayTransactionId": "A60087687",
                                "status": "AUTHORIZED",
                                "billingAddress": {
                                    "firstName": "auty",
                                    "lastName": "matt",
                                    "address1": "34k",
                                    "city": "arizona",
                                    "state": "Los angeles",
                                    "postalCode": "78945",
                                    "countryCode": "US",
                                    "email": "<EMAIL>"
                                }
                            }
                        ],
                        "shipments": [
                            {
                                "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                                "rate": {
                                    "currency": "USD",
                                    "value": 50.12345
                                },
                                "shippingMethod": "STANDARD",
                                "carrier": "DHL",
                                "taxTotal": [
                                    {
                                        "currency": "USD",
                                        "value": 12.12345
                                    }
                                ],
                                "address": {
                                    "firstName": "ismail",
                                    "lastName": "rangwala",
                                    "address1": "456",
                                    "city": "Manchester",
                                    "state": "England",
                                    "postalCode": "12345",
                                    "countryCode": "GB",
                                    "email": "<EMAIL>"
                                },
                                "itemKeys": [
                                    "45052f76-fcc3-4e15-bdee-439f1b9410512third"
                                ],
                                "item": [
                                    "21074012"
                                ],
                                "mode": "home_delivery"
                            }
                        ],
                        "orderSummary": {
                            "grossTotal": {
                                "currency": "USD",
                                "value": 233
                            },
                            "discounts": [
                                {
                                    "discountAmount": {
                                        "value": 20.12345,
                                        "currency": "USD"
                                    },
                                    "promotionCode": "New",
                                    "promotionName": "Percentage discount 10%",
                                    "rate": 10.12345
                                }
                            ],
                            "adjustmentTotal": {
                                "currency": "USD",
                                "value": 20
                            },
                            "taxes": [
                                {
                                    "type": "shipping_rule_tax",
                                    "taxAmount": {
                                        "currency": "USD",
                                        "value": "12"
                                    },
                                    "rate": 7
                                }
                            ],
                            "totalTaxAmount": {
                                "currency": "USD",
                                "value": 12
                            },
                            "shippingTotal": {
                                "currency": "USD",
                                "value": 50
                            },
                            "currency": "USD",
                            "netTotal": {
                                "currency": "USD",
                                "value": 200
                            }
                        },
                        "user": {
                            "firstName": "ram",
                            "lastName": "ram",
                            "email": "<EMAIL>"
                        },
                        "itinerary": {
                            "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                            "confirmationCode": "6kcb2s",
                            "airline": "SQ322",
                            "firstName": "RAJU",
                            "lastName": "sasi",
                            "middleName": "s"
                        },
                        "seatInfo": {
                            "seatClass": "Outside demo",
                            "seatNumber": "Outside demo"
                        },
                        "groundSystem": "GA"
                    }
                ]
            })
            headers = {
            'Authorization': JWT_TOKEN,
            'Content-Type': 'application/json'
            }
            response = requests.request("POST", CONSUME_ORDER_URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)
            assert response.status_code == 200 or response.status_code == 201
            utils.wait_for_style_attribute(driver,40)
            WebDriverWait(driver, 60).until(
                (EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'Assets')]")))
            )
            time.sleep(5)
            driver.find_element(By.XPATH, "//div[contains(text(),'Assets')]").click()
            logexpander = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]")
                )
            )
            driver.execute_script("arguments[0].scrollIntoView();", logexpander)

            driver.find_element(
                By.XPATH,
                "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]",
            ).click()
            logging.info("Clicked on Log")
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'Orders')]")
                )
            )
            driver.find_element(By.XPATH, "//span[contains(text(),'Orders')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//span[contains(text(),'List')]")
                )
            )
            driver.find_element(By.XPATH, "//span[contains(text(),'List')]").click()
            logging.info("Navigated to Orders - List View")
            time.sleep(10)
            try:
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH,
                                                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]")))
                driver.find_element(By.XPATH,
                                    "(//a[@data-qtip='Last Page' and @class='x-btn x-unselectable x-box-item x-toolbar-item x-btn-plain-toolbar-small'])[2]").click()
                logging.info("Navigated to last page")
            except Exception as e:
                logging.info("last page error")
                logging.info(e)
                pass
            time.sleep(10)
            last_order = driver.find_element(By.XPATH, "(//div[contains(text(),'/Log/Orders')])[last()]")
            action = ActionChains(driver)
            action.double_click(last_order).perform()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located(
                    (By.XPATH, xpath.saveAndPublishXpath)
                )
            )
            # 035 Verify if user enters taxTotal with more than 3 decimal precision in shipments
            assert driver.find_element(By.XPATH, "//div[contains(text(),'Allowed upto 3 decimal places for Shipment taxTotal')]").is_displayed()
            logging.info("Asserted Shipment taxTotal")
            # 036 Verify if user enters rate with more than 3 decimal precision in shipments
            assert driver.find_element(By.XPATH, "//div[contains(text(),'Allowed upto 3 decimal places for Shipment Rate')]").is_displayed()
            logging.info("Asserted Shipment Rate")
            # 037 Verify if user enters discountAmount with more than 3 decimal precision in order Summary>>discounts
            assert driver.find_element(By.XPATH, "(//div[contains(text(),'Allowed upto 3 decimal places for discountAmount')])[1]").is_displayed()
            logging.info("Asserted discountAmount")
            # 038 Verify if user enters rate with more than 3 decimal precision in order Summary>>discounts
            assert driver.find_element(By.XPATH, "(//div[contains(text(),'Allowed upto 3 decimal places for discountRate')])[1]").is_displayed()
            logging.info("Asserted discountRate")
            # 039 Verify if user enters discountAmount with more than 3 decimal precision in line item>>discountAdjustments
            assert driver.find_element(By.XPATH, "(//div[contains(text(),'Allowed upto 3 decimal places for discountAmount')])[2]").is_displayed()
            logging.info("Asserted discountAmount")
            # 040 Verify if user enters rate with more than 3 decimal precision in line item>>discountAdjustments
            assert driver.find_element(By.XPATH, "(//div[contains(text(),'Allowed upto 3 decimal places for discountRate')])[2]").is_displayed()
            logging.info("Asserted discountRate")
            # 041 Verify if user enters salePrice with more than 3 decimal precision in line item
            assert driver.find_element(By.XPATH, "//div[contains(text(),'Allowed upto 3 decimal places for salePrice')]").is_displayed()
            logging.info("Asserted salePrice")
        Success_List_Append("test_MKTPL_5503_floatingpoint_035_036_037_038_039_040_041",
                                'Post API should give success and validation message should display in order logs"',"Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_5503_floatingpoint_035_036_037_038_039_040_041",
                            'Post API should give success and validation message should display in order logs',"Fail",)
        Failure_Cause_Append("test_MKTPL_5503_floatingpoint_035_036_037_038_039_040_041",
                             'Post API should give success and validation message should display in order logs',e,)
        raise e

def test_MKTPL_1608_Manageshipping_009_011():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1608_Manageshipping_009_011.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.configuration_tab)))
            driver.find_element(By.XPATH, xpath.configuration_tab).click()
            manageShipping = driver.find_element(By.XPATH, xpath.manageShippingXpath)
            manageShipping.clear()
            manageShipping.send_keys("Yes", Keys.TAB)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, xpath.saveAndPublishXpath)))
            driver.find_element(By.XPATH, xpath.saveAndPublishXpath).click()
            assert WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[text()="Saved successfully!"]'))).is_displayed()
            utils.search_by_id(driver, DEDICATED_ORDER_1_LINE_ITEM_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Order Shipment"]')))
            driver.find_element(By.XPATH, '//span[text()="Order Shipment"]').click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//div[@class="x-grid-cell-inner x-grid-cell-inner-action-col"]')))
            driver.find_element(By.XPATH, '//div[@class="x-grid-cell-inner x-grid-cell-inner-action-col"]').click()
            RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.NAME, 'trackingId')))
            trackingId = driver.find_element(By.NAME, 'trackingId')
            trackingId.send_keys(RANDOM_NUMBER)
            trackingId.clear()
            logging.info(' 009 Select "Yes" able to Update Tracking ID via CSV and on order line item UI')
            utils.close_All_Tabs(driver)
            # --------
            utils.search_by_id(driver, DEDICATED_STORE_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Configuration"]')))
            driver.find_element(By.XPATH, '//span[text()="Configuration"]').click()
            manageShipping = driver.find_element(By.XPATH, xpath.manageShippingXpath)
            manageShipping.clear()
            manageShipping.send_keys("No", Keys.TAB)
            verify_value = driver.find_element(By.XPATH, xpath.manageShippingXpath).get_attribute("value")
            assert "No" == verify_value
            logging.info('011 User is able to make sure action when select "No"')
            Success_List_Append("test_MKTPL_1608_Manageshipping_009_011",
                        'Verify the When select option is "Yes"', "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1608_Manageshipping_009_011",
                            'Verify the When select option is "Yes"', "Fail", )
        Failure_Cause_Append("test_MKTPL_1608_Manageshipping_009_011",
                             'Verify the When select option is "Yes"', e, )
        raise e

def test_MKTPL_1608_Manageshipping_010_013_014():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1608_Manageshipping_010_013_014.png"
        ) as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.managestock(driver, 'yes')
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(1)
            driver.find_element(By.XPATH, "//div[contains(@id,'storeList-trigger-picker')]").click()
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, "//li[text()='" + DEDICATED_STORE + "']")))
            assert driver.find_element(By.XPATH, "//li[text()='" + DEDICATED_STORE + "']").is_displayed()
            logging.info('Select "Yes" able to Download Shipping Manifesto')
            #
            utils.close_All_Tabs(driver)
            utils.manageshipping(driver, 'No')
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.ID, xpath.pimcoreMenu_id)))
            driver.find_element(By.ID, xpath.pimcoreMenu_id).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.XPATH, xpath.csvImport)))
            driver.find_element(By.XPATH, xpath.csvImport).click()
            WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.NAME, xpath.importTypeName)))
            data_type = driver.find_element(By.NAME, xpath.importTypeName)
            data_type.send_keys("TrackingInfo")
            time.sleep(1)
            data_type.send_keys(Keys.ENTER)
            time.sleep(1)
            driver.find_element(By.XPATH, "//div[contains(@id,'storeList-trigger-picker')]").click()
            try:
                WebDriverWait(driver, 60).until(
                    EC.visibility_of_element_located((By.XPATH, "//li[text()='" + DEDICATED_STORE + "']")))
                driver.find_element(By.XPATH, "//li[text()='" + DEDICATED_STORE + "']").is_displayed()
            except:
                pass
                Success_List_Append("test_MKTPL_1608_Manageshipping_010_013_014",
                                'Verify the When select option is "Yes"', "Pass")



    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1608_Manageshipping_010_013_014",
                            'Verify the When select option is "Yes"', "Fail", )
        Failure_Cause_Append("test_MKTPL_1608_Manageshipping_010_013_014",
                             'Verify the When select option is "Yes"', e, )
        raise e

def test_MKTPL_323_Orderlineitem_003_004():
    try:
        with utils.services_context_wrapper("test_MKTPL_323_Orderlineitem_003_004.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.wait_for_style_attribute(driver, 40)
            utils.search_by_id(driver,  DEDICATED_FAILED_ORDER_LINE_ITEM_ID)
            statusField = (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'status'))).is_displayed())
            driver.find_element(By.XPATH, '(//input[@name = "status"]/../..//div)[2]').click()
            pendingOption  = driver.find_element(By.XPATH, xpath.pendingOptionListXpath).is_displayed()
            deliveredOption = driver.find_element(By.XPATH, xpath.deliveredOptionListXpath).is_displayed()
            requiredField = driver.find_element(By.XPATH, '//span[text()="Status "]//span[text()="*"]').is_displayed()
            assert [statusField, pendingOption, deliveredOption, requiredField]
            logging.info('Status fields is required field and That is editable like : 1.Delivered, 2.Pending')

            Success_List_Append("test_MKTPL_323_Orderlineitem_003_004",
                                "Verify the able to view line items details", "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_323_Orderlineitem_003_004",
                            "Verify the able to view line items details", "Fail")
        Failure_Cause_Append("test_MKTPL_323_Orderlineitem_003_004",
                             "Verify the able to view line items details",
                             e)
        raise e

def test_MKTPL_1265_OrderAPI_008_016_020():
    RANDOM_NUMBER = ("".join([str(randint(1, 9)) for i in range(4)]))

    try:
        with utils.services_context_wrapper(
            "test_MKTPL_1265_OrderAPI_008_016_020.png"
        ) as driver:
            payload = json.dumps({
                    "meta": {
                        "airlineCodeICAO": "CRE",
                        "flightNumber": "111",
                        "arrivalAirportCodeIATA": "",
                        "departureAirportCodeIATA": "",
                        "departTimeStamp": "2024-03-24T17:39:51.590+00:00",
                        "arrivalTimeStamp": "2024-03-24T17:39:51.590+00:00"
                    },
                    "orders": [
                        {
                            "orderKey": "without_required_information",
                            "externalId": RANDOM_NUMBER+" _From_Automation",
                            "basketKey": "From_Automation_3328 improvement current flight shipment",
                            "orderProvider": "From_Automation_3328 improvement 1234bcc10",
                            "modifiedTime": "2024-05-24T17:39:51.590+00:00",
                            "createdTime": "2024-05-24T17:39:51.287+00:00",
                            "lineItems": [
                                {
                                    "title": "Line_item",
                                    "PACVariantID": "1hospitality:mktplvb48943-var",
                                    "retailerCode": "random",
                                    "associateStore": int(DEDICATED_STORE_ID),
                                    "fulfillmentType": "inHouse",
                                    "alwaysInStock": False,
                                    "imageUrl": "https://images.test/file.jpg",
                                    "vendorProductVariantID": str(DEDICATED_PRODUCT_3_ID),
                                    "unitPrice": {
                                        "value": 49,
                                        "currency": "USD"
                                    },
                                    "unitTax": {
                                        "value": 10,
                                        "currency": "USD"
                                    },
                                    "unitDiscount": {
                                        "value": 4.9,
                                        "currency": "USD"
                                    },
                                    "unitNet": {
                                        "value": 44.1,
                                        "currency": "USD"
                                    },
                                    "unitGross": {
                                        "value": 44.1,
                                        "currency": "USD"
                                    },
                                    "quantity": 2,
                                    "status": "DELIVERED",
                                    "discountTotal": {
                                        "currency": "USD",
                                        "value": 9.8
                                    },
                                    "taxTotal": {
                                        "value": 10,
                                        "currency": "USD"
                                    },
                                    "discountAdjustments": [
                                        {
                                            "discountAmount": {
                                                "currency": "USD",
                                                "value": 12
                                            },
                                            "adjustType": "DISCOUNT",
                                            "rate": 10,
                                            "promotionCode": "asd",
                                            "promotionName": "Percentage discount 10%"
                                        }
                                    ],
                                    "taxAdjustments": [
                                        {
                                            "type": "shipping_rule_tax",
                                            "taxAmount": {
                                                "currency": "USD",
                                                "value": 12
                                            },
                                            "rate": 7
                                        }
                                    ],
                                    "salePrice": {
                                        "value": 49,
                                        "currency": "USD"
                                    },
                                    "key": "key"
                                },
                                {
                                    "title": "Line_item_1",
                                    "PACVariantID": "1hospitality:mktplvb48943-var",
                                    "retailerCode": "random",
                                    "associateStore": int(DEDICATED_STORE_ID),
                                    "fulfillmentType": "inHouse",
                                    "alwaysInStock": False,
                                    "imageUrl": "https://images.test/file.jpg",
                                    "vendorProductVariantID": str(DEDICATED_SKU_1_ID),
                                    "unitPrice": {
                                        "value": 49,
                                        "currency": "USD"
                                    },
                                    "unitTax": {
                                        "value": 10,
                                        "currency": "USD"
                                    },
                                    "unitDiscount": {
                                        "value": 4.9,
                                        "currency": "USD"
                                    },
                                    "unitNet": {
                                        "value": 44.1,
                                        "currency": "USD"
                                    },
                                    "unitGross": {
                                        "value": 44.1,
                                        "currency": "USD"
                                    },
                                    "quantity": 2,
                                    "status": "DELIVERED",
                                    "discountTotal": {
                                        "currency": "USD",
                                        "value": 9.8
                                    },
                                    "taxTotal": {
                                        "value": 10,
                                        "currency": "USD"
                                    },
                                    "discountAdjustments": [
                                        {
                                            "discountAmount": {
                                                "currency": "USD",
                                                "value": 12
                                            },
                                            "adjustType": "DISCOUNT",
                                            "rate": 10,
                                            "promotionCode": "asd",
                                            "promotionName": "Percentage discount 10%"
                                        }
                                    ],
                                    "taxAdjustments": [
                                        {
                                            "type": "shipping_rule_tax",
                                            "taxAmount": {
                                                "currency": "USD",
                                                "value": 12
                                            },
                                            "rate": 7
                                        }
                                    ],
                                    "salePrice": {
                                        "value": 49,
                                        "currency": "USD"
                                    },
                                    "key": "key_1"
                                }
                            ],
                            "status": "CALL_CREW",
                            "payment": [
                                {
                                    "paymentService": "INTERNAL",
                                    "paymentMethod": "CARD",
                                    "authAmount": {
                                        "currency": "USD",
                                        "value": 200
                                    },
                                    "paymentId": "BBAA-0324111-88",
                                    "technicalServiceProviderTransactionId": "20220324173949153132",
                                    "gatewayTransactionId": "A60087687",
                                    "status": "AUTHORIZED",
                                    "billingAddress": {
                                        "firstName": "auty",
                                        "lastName": "matt",
                                        "address1": "34k",
                                        "city": "arizona",
                                        "state": "Los angeles",
                                        "postalCode": "78945",
                                        "countryCode": "US",
                                        "email": "<EMAIL>"
                                    }
                                }
                            ],
                            "shipments": [
                                {
                                    "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                                    "rate": {
                                        "currency": "USD",
                                        "value": 50
                                    },
                                    "shippingMethod": "STANDARD",
                                    "carrier": "DHL",
                                    "taxTotal": [
                                        {
                                            "currency": "USD",
                                            "value": 12
                                        }
                                    ],
                                    "address": {
                                        "firstName": "ismail",
                                        "lastName": "rangwala",
                                        "address1": "456",
                                        "city": "Manchester",
                                        "state": "England",
                                        "postalCode": "12345",
                                        "countryCode": "GB",
                                        "email": "<EMAIL>"
                                    },
                                    "itemKeys": [
                                        "key"
                                    ],
                                    "item": [
                                        "21074012"
                                    ],
                                    "mode": "home_delivery"
                                },
                                {
                                    "id": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                                    "rate": {
                                        "currency": "USD",
                                        "value": 50
                                    },
                                    "shippingMethod": "STANDARD",
                                    "carrier": "DHL",
                                    "taxTotal": [
                                        {
                                            "currency": "USD",
                                            "value": 12
                                        }
                                    ],
                                    "address": {
                                        "firstName": "ismail",
                                        "lastName": "rangwala",
                                        "address1": "456",
                                        "city": "Manchester",
                                        "state": "England",
                                        "postalCode": "12345",
                                        "countryCode": "GB",
                                        "email": "<EMAIL>"
                                    },
                                    "itemKeys": [
                                        "key_1"
                                    ],
                                    "item": [
                                        "21074012"
                                    ],
                                    "mode": "home_delivery"
                                }
                            ],
                            "orderSummary": {
                                "grossTotal": {
                                    "currency": "USD",
                                    "value": 233
                                },
                                "discounts": [
                                    {
                                        "discountAmount": {
                                            "value": 20,
                                            "currency": "USD"
                                        },
                                        "promotionCode": "New",
                                        "promotionName": "Percentage discount 10%",
                                        "rate": 10
                                    }
                                ],
                                "adjustmentTotal": {
                                    "currency": "USD",
                                    "value": 20
                                },
                                "taxes": [
                                    {
                                        "type": "shipping_rule_tax",
                                        "taxAmount": {
                                            "currency": "USD",
                                            "value": "12"
                                        },
                                        "rate": 7
                                    }
                                ],
                                "totalTaxAmount": {
                                    "currency": "USD",
                                    "value": 12
                                },
                                "shippingTotal": {
                                    "currency": "USD",
                                    "value": 50
                                },
                                "currency": "USD",
                                "netTotal": {
                                    "currency": "USD",
                                    "value": 200
                                }
                            },
                            "user": {
                                "firstName": "ram",
                                "lastName": "ram",
                                "email": "<EMAIL>",
                                 "memberships": [  {
                                "loyaltyNumber": "1223",
                                "loyaltyType": "eeded"
                                }]
                            },
                            "itinerary": {
                                "identifier": "13932d21-ed33-441d-ad66-aef2df203044",
                                "confirmationCode": "6kcb2s",
                                "airline": "SQ322",
                                "firstName": "RAJU",
                                "lastName": "sasi",
                                "middleName": "s"
                            },
                            "seatInfo": {
                                "seatClass": "Outside demo",
                                "seatNumber": "Outside demo"
                            },
                            "groundSystem": "GA"
                        }
                    ]
                }
                        )
            headers = {
                'Authorization': ORDER_JWT_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request('POST', CONSUME_ORDER_URL, headers=headers, data=payload)
            data = response.json()
            logging.info(data)

            assert response.status_code == 200 or response.status_code == 201
            assert "Successful request to sync orders." == data["message"]
            logging.info("The Membership field are Loyalty Number, Loyalty Type.")
            # --------
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            # driver.implicitly_wait(10)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            utils.search_by_id(driver, ORDER_FOLDER)  # Order folder ID
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrder',
                                                                                              Keys.TAB))
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.NAME, 'query'))).
             send_keys(RANDOM_NUMBER, Keys.ENTER))
            (WebDriverWait(driver, 60).until
                (EC.presence_of_element_located(
                (By.XPATH, '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]'))))
            orderId = driver.find_element(By.XPATH,
                                          '(//span[text()="ID"]/../../../../../../../../..//div[@class="x-grid-cell-inner "])[1]').text
            logging.info(orderId)
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located(
                    (By.XPATH, '(//span[@class="x-tab-close-btn"])[1]'))).click())
            # open the order
            utils.search_by_id(driver, orderId)
            (WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath.childrenGridXpath))).click())
            (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, xpath.selectClassXpath))).send_keys('OnlineShopOrderItem', Keys.TAB))
            # verify multiple line item
            item1 = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '(//div[contains(text(),"Line_item")])[1]'))).is_displayed())
            item2 = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, '//div[contains(text(),"Line_item_1")]'))).is_displayed())
            assert item1, item2
            logging.info('Created multiple line items for the same store or different store')
            # OPEN THE LINE ITEM AND VERIFY THE STORE
            lineItem1 = driver.find_element(By.XPATH, '(//div[contains(text(),"Line_item")])[1]')
            actions = ActionChains(driver)
            actions.context_click(lineItem1).perform()
            open1 = driver.find_element(By.XPATH, xpath.openXpathContains)
            open1.click()
            utils.click_on_yes_no(driver)
            verifyStore = (WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//input[@value='/Stores/Cred Automation Store']"))).is_displayed())
            assert verifyStore
            logging.info('Base on the PAC variant id and product variant id define store')
            Success_List_Append("test_MKTPL_1265_OrderAPI_008_016_020",
                                "Verify the multiple line items in order",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_1265_OrderAPI_008_016_020",
                            'Verify the multiple line items in order', "Fail", )
        Failure_Cause_Append("test_MKTPL_1265_OrderAPI_008_016_020",
                             'Verify the multiple line items in order', e, )
        raise e

def test_MKTPL_3328_OrderAPI_005_006_009_012_014_015_019_020_021_027_028_029_034_035_036_037_039_040_042_043_044_045_046_047_049():
    try:
        with utils.services_context_wrapper("\
            test_MKTPL_3328_OrderAPI_005_006_009_012_014_015_019_020_021_027_028_029_034_035_036_037_039_040_042_043_044_045_046_047_049.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_Pac_Admin(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")
            
            search_path = "/Order/Cred Automation Airline/2024/05/24/"
            
            # 005 "Verify the seatInfo data"
            RANDOM_NUMBER = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "home_delivery",
                                                    seatClass= "Outside demo",
                                                    seatNumber= "Outside demo",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            time.sleep(2)
            new_element_path_1 = search_path + RANDOM_NUMBER + "_From_Automation"
            # new_element_path_1 = "/Order/Cred Automation Airline/2024/05/24/338777_From_Automation"
            logging.info(new_element_path_1)
            utils.search_by_path(driver, new_element_path_1)
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//div[text()="Seat Info"]')))
            )
            assert driver.find_element(By.XPATH, '//div[text()="Seat Info"]')
            assert driver.find_element(By.XPATH, '//span[text()="Seat Class "]')
            assert driver.find_element(By.XPATH, '//span[text()="Seat Number "]')
            logging.info("Verfied seatInfo data is stored in order object in the pimcore")
            
            # # 006 "Verify the groundSystem data"
            legend_list = driver.find_elements(By.XPATH, '//legend//div')
            legends = []
            for legend in legend_list:
                legends.append(legend.text)
            logging.info(legends)
            assert "Ground System" not in legends
            logging.info("Verfied groundSystem data is not stored in the pimcore")
            
            # 009 "Verify if paymentService is 'EXTERNAL'"
            RANDOM_NUMBER_1 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_1 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "EXTERNAL",
                                                    mode= "home_delivery",
                                                    seatClass= "Outside demo",
                                                    seatNumber= "Outside demo",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            new_element_2= RANDOM_NUMBER_1 + "_From_Automation"
            # new_element_2 = "337189_From_Automation"
            logging.info(new_element_2)
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//a[@data-qtip="Show in Tree"]')))
            )
            driver.find_element(By.XPATH, '//a[@data-qtip="Show in Tree"]').click()
            logging.info("1")
            time.sleep(10)
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//span[text()="Order"]')))
            )
            try:
                driver.find_element(By.XPATH, '//span[text()="24"]')
            except:
                time.sleep(5)
                driver.find_element(By.XPATH, '//a[@data-qtip="Show in Tree"]').click()
                logging.info("2")
                # time.sleep(10)
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//span[text()="24"]')))
            )
            filter_element = driver.find_element(By.XPATH, '//span[text()="24"]//parent::div//a[@data-qtip="Filter"]')
            filter_element.click()
            filter_input = driver.find_element(By.XPATH, '//span[text()="24"]//parent::div//input[contains(@class, "form-empty")]')
            filter_input.send_keys(new_element_2)
            filter_input.send_keys(Keys.ENTER)
            driver.find_element(By.XPATH, f'//span[text()="{new_element_2}"]').click()
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//div[text()="Payment Information"]')))
            )
            driver.find_element(By.XPATH, '//span[contains(@class, "close-btn")][1]').click()
            # //span[text()="Auth Amount "]
            logging.info(driver.find_element(By.XPATH, '//span[text()="Auth Amount "]//ancestor::label//parent::div//input').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="paymentMethod"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="paymentId"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="serviceProviderTransactionID"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="gatewayTransactionId"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="status"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="salutation"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="title"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="firstName"][1]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="middleName"][1]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="lastName"][1]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//textarea[@name="address1"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//textarea[@name="address2"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="city"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="state"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="countryCode"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="postalCode"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="email"]').get_attribute("value"))
            logging.info(driver.find_element(By.XPATH, '//input[@name="phone"]').get_attribute("value"))
            
            assert "" in str(driver.find_element(By.XPATH, '//span[text()="Auth Amount "]//ancestor::label//parent::div//input').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="paymentMethod"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="paymentId"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="serviceProviderTransactionID"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="gatewayTransactionId"]').get_attribute("value"))
            assert "(Empty)" in str(driver.find_element(By.XPATH, '//input[@name="status"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="salutation"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="title"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="firstName"][1]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="middleName"][1]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="lastName"][1]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//textarea[@name="address1"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//textarea[@name="address2"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="city"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="state"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="countryCode"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="postalCode"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="email"]').get_attribute("value"))
            assert "" in str(driver.find_element(By.XPATH, '//input[@name="phone"]').get_attribute("value"))

            logging.info("Verfied paymentMethod, authAmount, paymentId, technicalServiceProviderTransactionId, gatewayTransactionId, \
                status, salutation, title, firstName, middleName, lastName, address1, address2, city, state, postalCode, countryCode, \
                    email, phone fields are hidden in order object")
            
            # 012 "Verify if mode is 'current_flight'"
            RANDOM_NUMBER_2 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            logging.info("012_1")
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_2 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="Outside Deno",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            try:
                assert api_response != False
                assert api_response["error_code"] == 103
                assert "In Order [1] => shipments [1]  => address seatClass data can not be empty. It is a mandatory Field." in api_response["error_message"]
            except:
                pass
            RANDOM_NUMBER_2 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            logging.info("012_2")
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_2 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="Outside Deno",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            try:
                assert api_response != False
                assert api_response["error_code"] == 103
                logging.info(api_response["error_message"])
                assert "In Order [1] => shipments [1]  => address seatNumber data can not be empty. It is a mandatory Field." in api_response["error_message"]
            except:
                pass
            logging.info("Verfied seatClass and seatNumber parameters is mandatory in shipment")
            
            # 014 "Verify if mode is 'current_flight'"
            try:
                utils.close_All_Tabs(driver= driver)
            except:
                pass
            check_list = ["Shipment Method", "Tracking ID", "Tracking Carrier", "Tracking Link", "Inventory Location", "Rate", "Tax"]
            # /Order/Cred Automation Airline/2024/05/24/47624_From_Automation/Cred Automation Store/current_flight
            RANDOM_NUMBER_2 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_2 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="Outside Deno",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            time.sleep(5)
            current_flight_path = "/Cred Automation Store/current_flight"
            new_element_path_2 = search_path + RANDOM_NUMBER_2 + "_From_Automation"
            utils.search_by_path(driver= driver,
                                 path= new_element_path_2)
            new_element_order_shippment_path_2 = search_path + RANDOM_NUMBER_2 + "_From_Automation" + current_flight_path
            utils.search_by_path(driver= driver,
                                 path= new_element_order_shippment_path_2)
            driver.find_element(By.XPATH, '//span[contains(@class, "close-btn")][1]').click()
            # //div[contains(@class, "object_field")]//label//span[(contains(@class, "label-text")) and (contains(text(), " "))]
            # //span[(contains(@class, "label-text")) and (contains(text(), " "))]//ancestor::label//parent::div[contains(@class, "object_field")]
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//div[text()="Base Data"]')))
            )
            label_check_list = driver.find_elements(By.XPATH, '//div[(contains(@class, "object_field")) and contains(@style, "display: none")]\
                //span[contains(@class, "label-text") and (contains(text(), ":"))]')
            for label_check in label_check_list:
                for check in check_list:
                    if check in label_check.text:
                        assert check in label_check.text
                        break
            logging.info("Verfied Shipment Method, Tracking ID, Tracking Carrier, Tracking Link, Inventory Location, Rate and Tax fields \
                is hidden in shipment object")
            
            # 015 "Verify the seatClass and seatNumber data in address if mode is 'current_flight'"
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//span[text()="Delivery Information"]')))
            )
            driver.find_element(By.XPATH, '//span[text()="Delivery Information"]').click()
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//div[text()="Delivery Information"]')))
            )
            assert "Outside Demo" in driver.find_element(By.XPATH, '//input[@name="seatClass"]').get_attribute("value")
            assert "Outside Demo" in driver.find_element(By.XPATH, '//input[@name="seatNumber"]').get_attribute("value")
            logging.info("Verfied seatClass and seatNumber data is stored in shipment object")
            
            # 019 "Verify if mode is 'next_flight' or 'next_flight_short_notice'"
            utils.close_All_Tabs(driver= driver)
            check_list = ["Shipment Method", "Tracking ID", "Tracking Carrier", "Tracking Link", "Inventory Location", "Rate", "Tax"]
            # /Order/Cred Automation Airline/2024/05/24/47624_From_Automation/Cred Automation Store/current_flight
            RANDOM_NUMBER_3 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "123",
                                                    departureAirportCodeIATA= "JFK",
                                                    flightNumber= "GGH1232",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_3 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "next_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            time.sleep(5)
            next_flight_path = "/Cred Automation Store/next_flight"
            new_element_path_3 = search_path + RANDOM_NUMBER_3 + "_From_Automation"
            logging.info(new_element_path_3)
            utils.search_by_path(driver= driver,
                                 path= new_element_path_3)
            new_element_order_shippment_path_3 = search_path + RANDOM_NUMBER_3 + "_From_Automation" + next_flight_path
            utils.search_by_path(driver= driver,
                                 path= new_element_order_shippment_path_3)
            driver.find_element(By.XPATH, '//span[contains(@class, "close-btn")][1]').click()
            # //div[contains(@class, "object_field")]//label//span[(contains(@class, "label-text")) and (contains(text(), " "))]
            # //span[(contains(@class, "label-text")) and (contains(text(), " "))]//ancestor::label//parent::div[contains(@class, "object_field")]
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//div[text()="Base Data"]')))
            )
            label_check_list = driver.find_elements(By.XPATH, '//div[(contains(@class, "object_field")) and contains(@style, "display: none")]\
                //span[contains(@class, "label-text") and (contains(text(), ":"))]')
            for label_check in label_check_list:
                for check in check_list:
                    if check in label_check.text:
                        assert check in label_check.text
                        break
            logging.info("Verfied Shipment Method, Tracking ID, Tracking Carrier, Tracking Link, Inventory Location, Rate and Tax fields \
                is hidden in shipment object")
            
            # 020 "Verify the flightNumber, scheduledDepartureTime, departureAirportCodeIATA and arrivalAirportCodeIATA data in address \
            #       if mode is 'next_flight' or 'next_flight_short_notice'"
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//span[text()="Flight Information"]')))
            )
            driver.find_element(By.XPATH, '//span[text()="Flight Information"]').click()
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//span[text()="Flight Number "]')))
            )
            assert "GGH1232" in driver.find_element(By.XPATH, '//span[text()="Flight Number "]//ancestor::label//parent::div//div//div//div//div//div//div//div').text
            assert "2024-03-24" in driver.find_element(By.XPATH, '//input[contains(@name, "datefield")]').get_attribute("value")
            assert "23:09" in driver.find_element(By.XPATH, '//input[contains(@name, "timefield")]').get_attribute("value")
            assert "JFK" in driver.find_element(By.XPATH, '//span[text()="Departure Airport "]//ancestor::label//parent::div//div//div//div//div//div//div//div').text
            assert "DEL" in driver.find_element(By.XPATH, '//span[text()="Arrival Airport  "]//ancestor::label//parent::div//div//div//div//div//div//div//div').text
            logging.info("Verfied flightNumber, scheduledDepartureTime, departureAirportCodeIATA and arrivalAirportCodeIATA data is stored \
                in shipment object")
            
            # 021 "Verify if Flight Number, Scheduled Departure Time, Departure Airport and Arrival Airport fields are empty when mode is \
            #        'next_flight' or 'next_flight_short_notice'"
            RANDOM_NUMBER_4 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "",
                                                    departureAirportCodeIATA= "",
                                                    flightNumber= "",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_4 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "next_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            assert api_response != False
            logging.info(api_response["message"])
            assert "Internal server error" in api_response["message"]
            logging.info("Verfied Vaidation message is displayed in shipment object")
            
            # 027 "Verify the user parameter if mode is 'current_flight' in all shipment"
            RANDOM_NUMBER_5 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload_current_flight(firstName1="",
                                                    lastName1= "",
                                                    email1= "",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "",
                                                    departureAirportCodeIATA= "",
                                                    flightNumber= "",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_5 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            assert api_response != False
            assert api_response["code"] == 200
            logging.info(api_response["message"])
            assert "Successful request to sync orders." in api_response["message"]
            logging.info("Verfied User parameter is optional if mode is 'current_flight' in all shipment")
            
            # # 028 "Verify the Customer information and User Info tab in order object if mode is 'current_flight' in all shipment"
            # logging.info("Verfied Customer information and User Info tab is disable in order object")
            
            # 029 "Verify if First Name/Last Name/Email fields are empty in Customer information and User Info tab in order object"
            utils.close_All_Tabs(driver= driver)
            new_element_path_5 = search_path + RANDOM_NUMBER_5 + "_From_Automation"
            # new_element_path_1 = "/Order/Cred Automation Airline/2024/05/24/338777_From_Automation"
            time.sleep(3)
            logging.info(new_element_path_5)
            utils.search_by_path(driver, new_element_path_5)
            WebDriverWait(driver, 60).until(
                (utils.EC.element_to_be_clickable((By.XPATH, '//div[text()="Customer Information"]')))
            )
            assert "" in driver.find_element(By.XPATH, '//input[@name="customerFirstname"]').get_attribute("value")
            assert "" in driver.find_element(By.XPATH, '//input[@name="customerLastname"]').get_attribute("value")
            assert "" in driver.find_element(By.XPATH, '//input[@name="customerEmail"]').get_attribute("value")
            logging.info("Verfied Vaidation message is displayed in order object")
            
            # 034 "Verify if retailerCode is not associated with airline"
            RANDOM_NUMBER_6 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "sx",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_6 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="Outside Demo",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            assert api_response != False
            logging.info(api_response["message"])
            assert "Internal server error" in api_response["message"]
            logging.info("Verfied API has displayed validation message and order is not created in the system")
            
            # 035 "Verify if associateStore is not associated with airline"
            RANDOM_NUMBER_6 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "sx",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_6 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= "",
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="Outside Demo",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            assert api_response != False
            logging.info(api_response["message"])
            assert "Internal server error" in api_response["message"]
            logging.info("Verfied API has displayed validation message and order is not created in the system")
            
            # 036 "Verify if PACVariantID is not associated with retailerCode"
            RANDOM_NUMBER_6 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "sx",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_6 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_1_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "",
                                                    scheduledDepartureTime="Outside Demo",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            assert api_response != False
            logging.info(api_response["message"])
            assert "Internal server error" in api_response["message"]
            logging.info("Verfied API has displayed validation message and order is not created in the system")
            
            # 037 "Verify if PACVariantID is not associated with associateStore"
            RANDOM_NUMBER_6 = str("".join([str(randint(1, 9)) for i in range(6)])) 
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "sx",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_6 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_1_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "",
                                                    scheduledDepartureTime="Outside Demo",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            assert api_response != False
            logging.info(api_response["message"])
            assert "Internal server error" in api_response["message"]
            logging.info("Verfied API has displayed validation message and order is not created in the system")
            
            # 039 "Verify the newly added parameters in the response of GET All Orders/GET Order by Id API if mode is 'current_flight'"
            utils.close_All_Tabs(driver= driver)
            RANDOM_NUMBER_6 = str("".join([str(randint(1, 9)) for i in range(6)]))
            logging.info(RANDOM_NUMBER_6)
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "123",
                                                    departureAirportCodeIATA= "JFK",
                                                    flightNumber= "GGH1232",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_6 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            new_element_path_6 = search_path + RANDOM_NUMBER_6 + "_From_Automation"
            time.sleep(2)
            logging.info(new_element_path_6)
            
            utils.search_by_path(driver= driver, path= new_element_path_6)
            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,'//div[text()="OnlineShopOrder"]'))
            )
            UID = driver.find_element(By.XPATH, '((//a[@data-qtip="Show in Tree"])[last()]//following-sibling::div)[2]').text
            UID = UID.split()
            order_id = UID[1]
            logging.info(order_id)
            # driver.find_element(By.XPATH, xpath.logout).click()
            
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN
            }
            url = str(GET_ORDER_URL+str(order_id))
            response = requests.request("GET", url, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "seatClass" in resp_data["data"]["orders"]["shipments"][0]["address"]
            assert "seatNumber" in resp_data["data"]["orders"]["shipments"][0]["address"]
            assert "rate" not in resp_data["data"]["orders"]["shipments"][0]
            assert "shippingMethod" not in resp_data["data"]["orders"]["shipments"][0]
            assert "taxTotal" not in resp_data["data"]["orders"]["shipments"][0]
            assert "carrier" not in resp_data["data"]["orders"]["shipments"][0]
            logging.info("Verfied 'API has responded with 200 status code and it has displayed following parameters in the response: \
                - seatClass \
                - seatNumber \
                rate, shippingMethod, taxTotal, carrier parameters are hidden in the response of GET All Orders/GET Order by Id API'")
            
            # 040 "Verify the newly added parameter in the response of GET All Orders/GET Order by Id API if mode is 'next_flight' or 'next_flight_short_notice'"
            utils.close_All_Tabs(driver= driver)
            RANDOM_NUMBER_7 = str("".join([str(randint(1, 9)) for i in range(6)]))
            logging.info(RANDOM_NUMBER_7)
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "123",
                                                    departureAirportCodeIATA= "JFK",
                                                    flightNumber= "GGH1232",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_7 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "next_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            new_element_path_6 = search_path + RANDOM_NUMBER_7 + "_From_Automation"
            time.sleep(2)
            logging.info(new_element_path_6)
            
            utils.search_by_path(driver= driver, path= new_element_path_6)
            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,'//div[text()="OnlineShopOrder"]'))
            )
            UID = driver.find_element(By.XPATH, '((//a[@data-qtip="Show in Tree"])[last()]//following-sibling::div)[2]').text
            UID = UID.split()
            order_id = UID[1]
            logging.info(order_id)
            # driver.find_element(By.XPATH, xpath.logout).click()
            
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN
            }
            url = str(GET_ORDER_URL+str(order_id))
            response = requests.request("GET", url, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "flightNumber" in resp_data["data"]["orders"]["shipments"][0]["address"]
            assert "scheduledDepartureTime" in resp_data["data"]["orders"]["shipments"][0]["address"]
            assert "departureAirportCodeIATA" in resp_data["data"]["orders"]["shipments"][0]["address"]
            assert "arrivalAirportCodeIATA" in resp_data["data"]["orders"]["shipments"][0]["address"]
            assert "rate" not in resp_data["data"]["orders"]["shipments"][0]
            assert "shippingMethod" not in resp_data["data"]["orders"]["shipments"][0]
            assert "taxTotal" not in resp_data["data"]["orders"]["shipments"][0]
            assert "carrier" not in resp_data["data"]["orders"]["shipments"][0]
            logging.info("Verfied 'API has responded with 200 status code and it has displayed following parameters in the response: \
                - flightNumber \
                - scheduledDepartureTime \
                - departureAirportCodeIATA \
                - arrivalAirportCodeIATA \
                rate, shippingMethod, taxTotal, carrier parameters are hidden in the response of GET All Orders/GET Order by Id API'")
            
            # 042 "Verify the user parameter in the response of GET All Orders/GET Order by Id API if mode is 'current_flight' in all shipment"
            utils.close_All_Tabs(driver= driver)
            RANDOM_NUMBER_8 = str("".join([str(randint(1, 9)) for i in range(6)]))
            logging.info(RANDOM_NUMBER_8)
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "123",
                                                    departureAirportCodeIATA= "JFK",
                                                    flightNumber= "GGH1232",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_8 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "current_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            new_element_path_6 = search_path + RANDOM_NUMBER_8 + "_From_Automation"
            time.sleep(2)
            logging.info(new_element_path_6)
            
            utils.search_by_path(driver= driver, path= new_element_path_6)
            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,'//div[text()="OnlineShopOrder"]'))
            )
            UID = driver.find_element(By.XPATH, '((//a[@data-qtip="Show in Tree"])[last()]//following-sibling::div)[2]').text
            UID = UID.split()
            order_id = UID[1]
            logging.info(order_id)
            # driver.find_element(By.XPATH, xpath.logout).click()
            
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN
            }
            url = str(GET_ORDER_URL+str(order_id))
            response = requests.request("GET", url, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "user" not in resp_data["data"]['orders']
            logging.info("Verfied user parameter are hidden in the response of GET All Orders/GET Order by Id API")
            
            # 043 "Verify the user parameter in the response of GET All Orders/GET Order by Id API if mode is 'home_delivery' or 'next_flight' \
            #       or 'next_flight_short_notice' in any one shipment"
            utils.close_All_Tabs(driver= driver)
            RANDOM_NUMBER_9 = str("".join([str(randint(1, 9)) for i in range(6)]))
            logging.info(RANDOM_NUMBER_9)
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "123",
                                                    departureAirportCodeIATA= "JFK",
                                                    flightNumber= "GGH1232",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_9 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "home_delivery",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            new_element_path_6 = search_path + RANDOM_NUMBER_9 + "_From_Automation"
            time.sleep(2)
            logging.info(new_element_path_6)
            
            utils.search_by_path(driver= driver, path= new_element_path_6)
            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,'//div[text()="OnlineShopOrder"]'))
            )
            UID = driver.find_element(By.XPATH, '((//a[@data-qtip="Show in Tree"])[last()]//following-sibling::div)[2]').text
            UID = UID.split()
            order_id = UID[1]
            logging.info(order_id)
            # driver.find_element(By.XPATH, xpath.logout).click()
            
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN
            }
            url = str(GET_ORDER_URL+str(order_id))
            response = requests.request("GET", url, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "user" in resp_data["data"]["orders"]
            
            utils.close_All_Tabs(driver= driver)
            RANDOM_NUMBER_10 = str("".join([str(randint(1, 9)) for i in range(6)]))
            logging.info(RANDOM_NUMBER_10)
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "123",
                                                    departureAirportCodeIATA= "JFK",
                                                    flightNumber= "GGH1232",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_10 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "next_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            new_element_path_6 = search_path + RANDOM_NUMBER_10 + "_From_Automation"
            time.sleep(2)
            logging.info(new_element_path_6)
            
            utils.search_by_path(driver= driver, path= new_element_path_6)
            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,'//div[text()="OnlineShopOrder"]'))
            )
            UID = driver.find_element(By.XPATH, '((//a[@data-qtip="Show in Tree"])[last()]//following-sibling::div)[2]').text
            UID = UID.split()
            order_id = UID[1]
            logging.info(order_id)
            # driver.find_element(By.XPATH, xpath.logout).click()
            
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN
            }
            url = str(GET_ORDER_URL+str(order_id))
            response = requests.request("GET", url, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert "user" in resp_data["data"]["orders"]
            
            logging.info("Verfied user parameter are displayed in the response of GET All Orders/GET Order by Id API")
            
            # 044 "Verify the newly added parameter in the response of GET All Orders/GET Order by Id API if paymentService is 'EXTERNAL'"
            utils.close_All_Tabs(driver= driver)
            RANDOM_NUMBER_11 = str("".join([str(randint(1, 9)) for i in range(6)]))
            logging.info(RANDOM_NUMBER_11)
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "sx",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_11 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "EXTERNAL",
                                                    mode= "home_delivery",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            new_element_path_6 = search_path + RANDOM_NUMBER_11 + "_From_Automation"
            time.sleep(2)
            logging.info(new_element_path_6)
            
            utils.search_by_path(driver= driver, path= new_element_path_6)
            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,'//div[text()="OnlineShopOrder"]'))
            )
            UID = driver.find_element(By.XPATH, '((//a[@data-qtip="Show in Tree"])[last()]//following-sibling::div)[2]').text
            UID = UID.split()
            order_id = UID[1]
            logging.info(order_id)
            # driver.find_element(By.XPATH, xpath.logout).click()
            
            payload = {}
            headers = {
                'Authorization': CRED_AUTOMATION_AIRLINE_TOKEN
            }
            url = str(GET_ORDER_URL+str(order_id))
            response = requests.request("GET", url, headers=headers, data=payload)
            resp_data = response.json()
            logging.info(resp_data)
            assert response.status_code == 200 or response.status_code == 201
            assert "paymentMethod" not in resp_data["data"]["orders"]["payment"][0]
            assert "authAmount" not in resp_data["data"]["orders"]["payment"][0]
            assert "paymentId" not in resp_data["data"]["orders"]["payment"][0]
            assert "technicalServiceProviderTransactionId" not in resp_data["data"]["orders"]["payment"][0]
            assert "gatewayTransactionId" not in resp_data["data"]["orders"]["payment"][0]
            assert "status" not in resp_data["data"]["orders"]["payment"][0]
            assert "salutation" not in resp_data["data"]["orders"]["payment"][0]
            assert "title" not in resp_data["data"]["orders"]["payment"][0]
            assert "firstName" not in resp_data["data"]["orders"]["payment"][0]
            assert "middleName" not in resp_data["data"]["orders"]["payment"][0]
            assert "lastName" not in resp_data["data"]["orders"]["payment"][0]
            assert "address1" not in resp_data["data"]["orders"]["payment"][0]
            assert "address2" not in resp_data["data"]["orders"]["payment"][0]
            assert "city" not in resp_data["data"]["orders"]["payment"][0]
            assert "state" not in resp_data["data"]["orders"]["payment"][0]
            assert "postalCode" not in resp_data["data"]["orders"]["payment"][0]
            assert "countryCode"not in resp_data["data"]["orders"]["payment"][0]
            assert "email" not in resp_data["data"]["orders"]["payment"][0]
            assert "phone" not in resp_data["data"]["orders"]["payment"][0]
            logging.info("Verfied paymentMethod, authAmount, paymentId, technicalServiceProviderTransactionId, gatewayTransactionId, status, \
                salutation, title, firstName, middleName, lastName, address1, address2, city, state, postalCode, countryCode, email, phone \
                    parameters are hidden in the response of GET All Orders/GET Order by Id API")
            
            # 045 "Verify if user enters any data except 'PENDING' or 'DELIVERED' in status in Update Line Item Status API"
            RANDOM_NUMBER_12 = str("".join([str(randint(1, 9)) for i in range(6)]))
            logging.info(RANDOM_NUMBER_12)
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "sx",
                                                    departureAirportCodeIATA= "sx",
                                                    flightNumber= "13KNTV34",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_12 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "INITIATED",
                                                    status= "CALL_CREW",
                                                    paymentService= "EXTERNAL",
                                                    mode= "home_delivery",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            assert api_response != False
            logging.info(api_response["message"])
            assert "Internal server error" in api_response["message"]
            logging.info("Verfied API has displayed validation message")
            
            # 046 "Verify when user clicks on the Print E-Receipt button and paymentService is 'EXTERNAL'"
            reader = utils.PdfReader((os.getcwd() + "/credencys_test_ui/" + 'OrderReceipt_next_flight.pdf'))
            for page in reader.pages:
                # print(page.extract_text(), type(page.extract_text()))
                plist = page.extract_text().split()
                print(plist)
            assert "Payment ID" not in plist
            assert "Payment Status" not in plist
            logging.info("Verfied Payment ID, Payment Status fields are hidden in Order E-Receipt")
            
            # 047 "Verify if user enters shipment id whose mode is 'next_flight' or 'next_flight_short_notice' or 'current_flight' in Update Shipment API"
            utils.close_All_Tabs(driver= driver)
            RANDOM_NUMBER_12 = str("".join([str(randint(1, 9)) for i in range(6)]))
            logging.info(RANDOM_NUMBER_12)
            api_response = utils.order_api_payload_current_flight(firstName1="John",
                                                    lastName1= "Doe",
                                                    email1= "<EMAIL>",
                                                    firstName= "ismail",
                                                    lastName= "rangwala",
                                                    address1= "456",
                                                    city= "Manchester",
                                                    state= "England",
                                                    postalCode= "12345",
                                                    countryCode= "GB",
                                                    email= "<EMAIL>",
                                                    arrivalAirportCodeIATA= "123",
                                                    departureAirportCodeIATA= "JFK",
                                                    flightNumber= "GGH1232",
                                                    airlineCodeICAO= "CRE",
                                                    externalId= RANDOM_NUMBER_12 + "_From_Automation",
                                                    createdTime= "2024-05-24T17:39:51.287+00:00",
                                                    retailerCode= "random",
                                                    associateStore= DEDICATED_STORE_ID,
                                                    status1= "PENDING",
                                                    status= "CALL_CREW",
                                                    paymentService= "INTERNAL",
                                                    mode= "next_flight",
                                                    seatClass= "Outside Demo",
                                                    seatNumber= "Outside Demo",
                                                    PACVariantID= "1hospitality:mktplvb48943-var",
                                                    scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
                                                    groundSystem= "GA",
                                                    currency= "USD",
                                                    token= JWT_TOKEN,
                                                    request= "POST",
                                                    url= CONSUME_ORDER_URL
                                                    )
            logging.info(api_response)
            next_flight_path = "/Cred Automation Store/next_flight"
            new_element_path_6 = search_path + RANDOM_NUMBER_12 + "_From_Automation" + next_flight_path
            time.sleep(2)
            logging.info(new_element_path_6)
            
            utils.search_by_path(driver= driver, path= new_element_path_6)
            
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH,'//div[text()="orderShipment"]'))
            )
            UID = driver.find_element(By.XPATH, '((//a[@data-qtip="Show in Tree"])[last()]//following-sibling::div)[2]').text
            UID = UID.split()
            shipment_id = UID[1]
            logging.info(shipment_id)
            # driver.find_element(By.XPATH, xpath.logout).click()
            
            payload = json.dumps({
                "shipmentId": "cf57e776-4ede-462b-b88a-ff9a9d79a0e12third",
                "trackingId": "12345678",
                "trackingLink": "BBB",
                "inventoryLocation": ""
            })
            headers = {
                'Authorization': CRED_AUTOMATION_STORE_TOKEN,
                'Content-Type': 'application/json'
            }
            response = requests.request("PUT", UPDATE_SHIPMENT + str(shipment_id),
                                        headers=headers, data=payload)
            resp = response.json()
            logging.info(resp)
            assert "Shipment is invalid" in resp["message"]
            logging.info("Verfied API has displayed validation message")
            
            # 049 "Verify if user enters shipment id whose mode is 'next_flight' or 'next_flight_short_notice' or 'current_flight' in Tracking Info CSV"
            RANDOM_NUMBER_12 = str("".join([str(randint(1, 9)) for i in range(6)]))

            utils.Trackinginfoimport_log_store("shiomentID", RANDOM_NUMBER_12,
                                               "Invalid shipment id")

            logging.info(" 048 Validation message should display")
            utils.update_csv("order_trackingInfo.csv", "OrderLineStatus", "PENDING")
            logging.info("Verfied Validation message is displayed")
            

        Success_List_Append("test_MKTPL_3328_OrderAPI_005_006_009_012_014_015_019_020_021_027_028_029_034_035_036_037_039_040_042_043_044_045_046_047_049",
                                "Verify the seatInfo data",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3328_OrderAPI_005_006_009_012_014_015_019_020_021_027_028_029_034_035_036_037_039_040_042_043_044_045_046_047_049",
                            "Verify the seatInfo data",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_3328_OrderAPI_005_006_009_012_014_015_019_020_021_027_028_029_034_035_036_037_039_040_042_043_044_045_046_047_049",
                             "Verify the seatInfo data",
                             e)
        raise e

def test_MKTPL_3336_JWTToken_001_003():
    try:
        with utils.services_context_wrapper(
            "test_MKTPL_3336_JWTToken_001_003.png"
        ) as driver:
            RANDOM_NAME = "".join(random.choices(string.ascii_lowercase, k=5))

            # 001. "Verify the Post Order API"
            api_response = utils.order_api_payload(firstName1= "John",
                                                   lastName1= "Doe",
                                                   email1= "<EMAIL>",
                                                   firstName= "ismail",
                                                   lastName= "rangwala",
                                                   address1= "456",
                                                   city= "Manchester",
                                                   state= "England",
                                                   postalCode= "12345",
                                                   countryCode= "GB",
                                                   email= "<EMAIL>",
                                                   arrivalAirportCodeIATA= "",
                                                   departureAirportCodeIATA= "sx",
                                                   flightNumber= "13KNTV34",
                                                   airlineCodeICAO= RANDOM_NAME,
                                                   externalId= "926574",
                                                   createdTime= "2024-05-24T17:39:51.287+00:00",
                                                   retailerCode= "random", 
                                                   associateStore= DEDICATED_STORE_ID,
                                                   status1= "DELIVERED",
                                                   status= "CALL_CREW",
                                                   paymentService= "INTERNAL",
                                                   mode= "home_delivery",
                                                   seatClass= "Outside demo",
                                                   seatNumber= "Outside demo",
                                                   groundSystem= "GA",
                                                   currency= "USD",
                                                   token= "",
                                                   request= "POST",
                                                   url= CONSUME_ORDER_URL)
            logging.info(api_response["error_code"])
            assert 405 == api_response["error_code"]
            assert "AUTHORIZATION_FAILED" in api_response["text_code"]
            assert "Authorization Header Missing" in api_response["error_message"]
            logging.info("Verified Post Order API should not work without JWT token")

            # 003. "Verify the Post Inventory Check API for airside"
            inventroy_check_payload = json.dumps({
                "home": {
                    "icaoCode": "",
                    "items": [
                        {
                            "vendorProductVariantId": "72372",
                            "quantity": 25
                        }
                    ]
                },
                "inflightFutureStandard": {
                    "icaoCode": "",
                    "items": [
                        {
                            "vendorProductVariantId": "123",
                            "quantity": 5
                        }
                    ]
                },
                "inflightFutureExpress": {
                    "icaoCode": "",
                    "items": [
                        {
                            "vendorProductVariantId": "123",
                            "quantity": 5
                        }
                    ]
                }
            })
            headers = {
                'Authorization': "",
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", INVENTORY_CHECK_URL, headers=headers, data=inventroy_check_payload)
            api_response = response.json()
            logging.info(api_response)
            logging.info(api_response["error_code"])
            assert 405 == api_response["error_code"]
            assert "AUTHORIZATION_FAILED" in api_response["text_code"]
            assert "Authorization Header Missing" in api_response["error_message"]
            logging.info("Verfied Post Inventory Check API for airside should not work without JWT token")

            # # 005. "Verify if aud is not found in the pimcore in Post Order API or Post Inventory Check API"
            # inventroy_check_payload = json.dumps({
            #     "home": {
            #         "icaoCode": "",
            #         "items": [
            #             {
            #                 "vendorProductVariantId": "72372",
            #                 "quantity": 25
            #             }
            #         ]
            #     },
            #     "inflightFutureStandard": {
            #         "icaoCode": "",
            #         "items": [
            #             {
            #                 "vendorProductVariantId": "123",
            #                 "quantity": 5
            #             }
            #         ]
            #     },
            #     "inflightFutureExpress": {
            #         "icaoCode": "",
            #         "items": [
            #             {
            #                 "vendorProductVariantId": "123",
            #                 "quantity": 5
            #             }
            #         ]
            #     }
            # })
            # headers = {
            #     'Authorization': JWT_TOKEN,
            #     'Content-Type': 'application/json'
            # }
            # response = requests.request("POST", INVENTORY_CHECK_URL, headers=headers, data=inventroy_check_payload)
            # api_response = response.json()
            # logging.info(api_response)
            # logging.info(api_response["error_code"])
            # assert 405 == api_response["error_code"]
            # assert "AUTHORIZATION_FAILED" in api_response["text_code"]
            # assert "Authorization Header Missing" in api_response["error_message"]
            # logging.info("Verfied Respone code should be 401, Error_code should be 405, text_code should be 'AUTHORIZATION_FAILED' \
            #     and Error_message should be 'Unauthorised Access!'")

            # # 006. "Verify if aud is empty in Post Order API or Post Inventory Check API"
            # logging.info("Verfied Respone code should be 401, Error_code should be 405, text_code should be 'AUTHORIZATION_FAILED' \
            #     and Error_message should be 'Invalid Token!'")

            # # 007. "Verify if Secret value is not found in the pimcore in Post Order API or Post Inventory Check API"
            # logging.info("Verfied Respone code should be 401, Error_code should be 405, text_code should be 'AUTHORIZATION_FAILED' \
            #     and Error_message should be 'Invalid Token!'")

            # # 008. "Verify if exp is zero in Post Order API or Post Inventory Check API"
            # logging.info("Verfied Respone code should be 401, Error_code should be 405, text_code should be 'AUTHORIZATION_FAILED' \
            #     and Error_message should be 'Expired token'")

            # # 009. "Verify if airline is not associated with any store in Post Inventory Check API"
            # logging.info("Verfied Respone code should be 400, Error_code should be 103, text_code should be 'VALIDATION_FAILED' \
            #     and Error_message should be 'No Stores Associated with the Airline'")

            # # 010. "Verify if all airline associated store's has manage stock "No" in Post Inventory Check API"
            # logging.info("Verfied Respone code should be 400, Error_code should be 103, text_code should be 'VALIDATION_FAILED' \
            #     and Error_message should be 'No Stores available with Inventory for the Airline'")

            time.sleep(3)

            Success_List_Append("test_MKTPL_3336_JWTToken_001_003",
                                "Verify the Post Order API",
                                "Pass")
    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3336_JWTToken_001_003",
                            "Verify the Post Order API",
                            "Fail",)
        Failure_Cause_Append("test_MKTPL_3336_JWTToken_001_003",
                             "Verify the Post Order API",
                             e,)
        raise e

def test_MKTPL_3671_Order_001_002_003_004_005_006():
    try:
        with utils.services_context_wrapper("test_MKTPL_3671_Order_001_002_003_004_005_006.png") as driver:
            driver.maximize_window()
            utils.Pac_Credentials.Login_store(driver)
            act_title = driver.find_element(
                By.XPATH, xpath.logout
            ).get_attribute("id")
            assert act_title == "pimcore_logout"
            logging.info("Login is successful.")

            # # 001. "Verify if vendorProductVariantID is not associated with retailerCode"
            # RANDOM_NUMBER = str("".join([str(randint(1, 9)) for i in range(6)]))
            # logging.info(RANDOM_NUMBER)
            # api_response = utils.order_api_payload_current_flight(firstName1="John",
            #                                         lastName1= "Doe",
            #                                         email1= "<EMAIL>",
            #                                         firstName= "ismail",
            #                                         lastName= "rangwala",
            #                                         address1= "456",
            #                                         city= "Manchester",
            #                                         state= "England",
            #                                         postalCode= "12345",
            #                                         countryCode= "GB",
            #                                         email= "<EMAIL>",
            #                                         arrivalAirportCodeIATA= "123",
            #                                         departureAirportCodeIATA= "JFK",
            #                                         flightNumber= "GGH1232",
            #                                         airlineCodeICAO= "CRE",
            #                                         externalId= RANDOM_NUMBER + "_From_Automation",
            #                                         createdTime= "2024-05-24T17:39:51.287+00:00",
            #                                         retailerCode= "random",
            #                                         associateStore= DEDICATED_STORE_ID,
            #                                         status1= "PENDING",
            #                                         status= "CALL_CREW",
            #                                         paymentService= "INTERNAL",
            #                                         mode= "next_flight",
            #                                         seatClass= "Outside Demo",
            #                                         seatNumber= "Outside Demo",
            #                                         PACVariantID= "1hospitality:mktplvb48943-var",
            #                                         scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
            #                                         groundSystem= "GA",
            #                                         currency= "USD",
            #                                         token= JWT_TOKEN,
            #                                         request= "POST",
            #                                         url= CONSUME_ORDER_URL
            #                                         )
            # logging.info(api_response)
            # assert api_response != False
            # assert api_response["error_code"] == 103
            # assert "VALIDATION_FAILED" in api_response["text_code"]
            # assert "retailerCode Line Item Belongs to another Store" in api_response["error_message"]

            # logging.info("Verfied Respone code is 400, Error_code is 103, text_code is 'VALIDATION_FAILED' \
            #       and Error_message is 'retailerCode Line Item Belongs to another Store'")

            # # 002. "Verify if vendorProductVariantID is not associated with associateStore"
            # RANDOM_NUMBER = str("".join([str(randint(1, 9)) for i in range(6)]))
            # logging.info(RANDOM_NUMBER)
            # api_response = utils.order_api_payload_current_flight(firstName1="John",
            #                                         lastName1= "Doe",
            #                                         email1= "<EMAIL>",
            #                                         firstName= "ismail",
            #                                         lastName= "rangwala",
            #                                         address1= "456",
            #                                         city= "Manchester",
            #                                         state= "England",
            #                                         postalCode= "12345",
            #                                         countryCode= "GB",
            #                                         email= "<EMAIL>",
            #                                         arrivalAirportCodeIATA= "123",
            #                                         departureAirportCodeIATA= "JFK",
            #                                         flightNumber= "GGH1232",
            #                                         airlineCodeICAO= "CRE",
            #                                         externalId= RANDOM_NUMBER + "_From_Automation",
            #                                         createdTime= "2024-05-24T17:39:51.287+00:00",
            #                                         retailerCode= "random",
            #                                         associateStore= DEDICATED_STORE_1_ID,
            #                                         status1= "PENDING",
            #                                         status= "CALL_CREW",
            #                                         paymentService= "INTERNAL",
            #                                         mode= "next_flight",
            #                                         seatClass= "Outside Demo",
            #                                         seatNumber= "Outside Demo",
            #                                         PACVariantID= "1hospitality:mktplvb48943-var",
            #                                         scheduledDepartureTime="2024-03-24T17:39:51.590+00:00",
            #                                         groundSystem= "GA",
            #                                         currency= "USD",
            #                                         token= JWT_TOKEN,
            #                                         request= "POST",
            #                                         url= CONSUME_ORDER_URL
            #                                         )
            # logging.info(api_response)
            # assert api_response != False
            # assert api_response["error_code"] == 103
            # assert "VALIDATION_FAILED" in api_response["text_code"]
            # assert "associateStore Line Item Belongs to another Store" in api_response["error_message"]
            # logging.info("Verfied 'Respone code is 400, Error_code is 103, text_code is 'VALIDATION_FAILED' \
            #       and Error_message is 'associateStore Line Item Belongs to another Store'")

            # 003. "Verify the vendorProductVariantID data"
            utils.search_by_id(driver, DEDICATED_ONLINE_SHOP_ORDER_ITEM_1_ID)
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="Vendor Product Variant ID "]')))
            assert driver.find_element(By.XPATH, '//span[text()="Vendor Product Variant ID "]')
            logging.info("Verfied vendorProductVariantID data is stored in line item object in the pimcore")

            # 004. "Verify the PACVariantID data"
            WebDriverWait(driver, 60).until(
                EC.visibility_of_element_located((By.XPATH, '//span[text()="PAC Variant ID "]')))
            assert driver.find_element(By.XPATH, '//span[text()="PAC Variant ID "]')
            logging.info("Verfied PACVariantID data is stored in line item object in the pimcore")

            # 005. "Verify the vendorProductVariantID field"
            vendorProductVariantID_input = driver.find_element(By.XPATH, '//input[@name= "vendorProductVariantID"]')
            vendorProductVariantID_input.send_keys("updated")
            assert not "updated" == driver.find_element(By.XPATH, '//input[@name= "vendorProductVariantID"]').text
            logging.info("Verfied vendorProductVariantID field is non-editable in line item object in the pimcore")

            # # 006. "Verify if user updates product data in line item object"
            # logging.info("Verfied vendorProductVariantID data is automatically updated")

            Success_List_Append("test_MKTPL_3671_Order_001_002_003_004_005_006",
                                "Verify if vendorProductVariantID is not associated with retailerCode",
                                "Pass")

    except Exception as e:
        logging.info(f"Error- {e}")
        Success_List_Append("test_MKTPL_3671_Order_001_002_003_004_005_006",
                            "Verify if vendorProductVariantID is not associated with retailerCode",
                            "Fail")
        Failure_Cause_Append("test_MKTPL_3671_Order_001_002_003_004_005_006",
                             "Verify if vendorProductVariantID is not associated with retailerCode",
                             e)
        raise e
