import pytest
from jamatest import jamatest
from requests.adapters import HTTPAdapter
from selenium import webdriver
from selenium.webdriver.common.by import By
import matplotlib.pyplot as plt
import yaml
from datetime import time
# from credencys_test_ui.credencys_project_configs.Delete_Automation_Data import Deletion_Automation_Data
# from credencys_test_ui.credencys_project_configs.Report_Mail import Error_Table_Generation
# from credencys_test_ui.credencys_project_configs.Report_Mail import TestReport_Generation
# from credencys_test_ui.credencys_project_configs.Report_Mail import Summary_Table_Formation
# from credencys_test_ui.credencys_project_configs.Report_Mail import Send_Mail
from project_configs.csv_config import Config
from project_configs.login_helpers import logger
from project_configs.env_config import get_environment
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from datetime import date, datetime
from driver_setup.config import *
from driver_setup.helper import *
from project_configs.ui_helper import check_succesfull_login
from users.pacAdmin import pacAdmin
from users.storeAdmin import storeAdmin

logging.getLogger('matplotlib').setLevel(logging.INFO)
logging.getLogger('selenium').setLevel(logging.WARNING)

def pytest_addoption(parser):
    parser.addoption("--env", action="store", default="pac", help="Specify the environment")

@pytest.fixture(scope="session", autouse=True)
def set_mode():
    Config.set_path()

@pytest.fixture(scope="session")
def environment(request):
    return request.config.getoption("--env")

@pytest.fixture(scope="session")
def env(environment):
    return get_environment(environment)

@pytest.fixture(scope="session")
def storeAdminUser(store_user_login_timer, env) -> storeAdmin: 
    driver = create_driver()
    return storeAdmin(driver, STORE_USERNAME, STORE_PASSWORD, env)

@pytest.fixture(scope="session")
def storeManagerUser(store_user_login_timer, env) -> storeAdmin: 
    driver = create_driver()
    return storeAdmin(driver, STORE_USERNAME_MANAGER, STORE_PASSWORD_MANAGER, env)

@pytest.fixture(scope="session")
def airlineManagerUser(store_user_login_timer, env) -> storeAdmin: 
    driver = create_driver()
    return storeAdmin(driver, AIRLINE_USERNAME_MANAGER, AIRLINE_PASSWORD_MANAGER, env)

@pytest.fixture(scope="session")
def airlineAdminUser(store_user_login_timer, env) -> storeAdmin: 
    driver = create_driver()
    return storeAdmin(driver, AIRLINE_USERNAME, AIRLINE_PASSWORD, env)

@pytest.fixture(scope="session")
def pacAdminUser( env, request) -> pacAdmin: 
    driver = create_driver()
    return pacAdmin(driver, PAC_USER_USERNAME, PAC_USER_PASSWORD, env)

@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    outcome = yield
    rep = outcome.get_result()
    if rep.when == "call" and rep.failed: # Check if a test passed/failed during call phase
        user_fixtures = {"storeAdminUser", "storeManagerUser", "airlineManagerUser", "pacAdminUser", "airlineAdminUser"}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_name = item.nodeid.replace("/", "_").replace(":", "_").replace("::", "_")
        # Check each user fixture
        for fixture_name in user_fixtures:
            if fixture_name in item.fixturenames:
                try:
                    user = item._request.getfixturevalue(fixture_name)
                    driver = user.driver
                    # screenshot_path = f"{test_name}_{fixture_name}_{timestamp}.png"
                    # Save the screenshot
                    screenshot_path = os.path.join(Config.get_path("results_folder_path"), f"{test_name}_{fixture_name}_{timestamp}.png")
                    driver.save_screenshot(screenshot_path)
                    print(f"Test failed. Screenshot saved to {screenshot_path}")
                    driver.refresh()
                    print("Reloaded page. Waiting 15 seconds to continue the next test")
                    sleep(15)
                except Exception as e:
                    print(f"Failed to capture screenshot for {fixture_name}: {str(e)}")

def create_driver():
    for i in range(3):
        try:
            browser_options = Options()
            if os.getenv("ENV") == "local":
                logger.info("\nusing the local driver setup")
                if os.getenv("CURRRENT_USER_UI") == 'CL':
                    SELENIUM_DRIVER_PATH = '/Users/<USER>/Documents/GitLab Projects/marketplace-ground-pim-server-test/test/utils/chromedriver'
                # else:
                #     SELENIUM_DRIVER_PATH = '/Users/<USER>/Documents/Marketplace_project/2025-2026/automation_ui/test_ui/project_configs/chromedriver'
                else:
                    SELENIUM_DRIVER_PATH = '/Users/<USER>/automation_ui_brand/test_ui/project_configs/chromedriver'
                browser_options.add_experimental_option( "prefs", {"download.default_directory": DOWNLOAD_PATH,
                                                            "goog:loggingPrefs": {"browser":"ALL"}
                                                                })
                browser_options.add_argument("--window-size=1920,1080")
                browser_options.add_argument("--no-sandbox")
                browser_options.add_argument("--disable-dev-shm-usage")
                driver = webdriver.Chrome(service=Service(SELENIUM_DRIVER_PATH),options=browser_options)
            else:
                CI_PROJECT_DIR = os.getenv("CI_PROJECT_DIR")
                logger.info("\nThe download path: %s", DOWNLOAD_PATH)
                SELENIUM_DRIVER_PATH = '/usr/local/bin/chromedriver'
                logger.info("\nThe SELENIUM_DRIVER_PATH: %s ", SELENIUM_DRIVER_PATH)
                browser_options.add_experimental_option( "prefs", {"download.default_directory": DOWNLOAD_PATH, "goog:loggingPrefs": {"browser":"ALL"}})
                browser_options.add_argument("--headless")
                browser_options.add_argument("--window-size=1920,1080")
                browser_options.add_argument("--no-sandbox")
                browser_options.add_argument("--disable-dev-shm-usage")
                driver = webdriver.Chrome(service=Service(SELENIUM_DRIVER_PATH),options=browser_options)
            driver.implicitly_wait(20)
            return driver
        except Exception as e:
            logger.critical("Creating the driver failed on attempt %s", i)
            logger.critical("EXCEPTION:")
            logger.critical(e)
            sleep(20)
    raise Exception("Exceded all the retries without getting a succesfull driver connection")

@pytest.fixture
def driver():
    return create_driver()

# def read_data(file_name):
#     try:
#         with open(file_name, 'r') as file:
#             return json.load(file)
#     except:
#         logger.debug(f"File '{file_name}' does not exist")
#         return []
# def write_data(file_name, data):
#     with open(file_name, "w") as file:
#         json.dump(data, file, indent=4 )

# to uncomment for pipeline
@pytest.fixture(scope="session", autouse=True)
def jama_init(request):
    print(f"{jama_init.__name__}: Checking if JAMA is being used or not...")
    CLIENT_ID = os.getenv("JAMA_CLIENT_ID")
    CLIENT_SECRET = os.getenv("JAMA_CLIENT_SECRET")
    # pdc code run -
    # TEST_PLAN_ID = 20774738
    # credencys code run -
    TEST_PLAN_ID = 17125021

    if TEST_PLAN_ID and CLIENT_ID is not None and os.getenv("ENV") != "local" and os.getenv("SKIP_JAMA") != "1":
        cycle_id = os.getenv("TEST_CYCLE_ID")
        # cycle_id = "16463243" #it can be gathered inspecting the page
        print(f"JAMA_CLIENT: {cycle_id}")
        jamatest.init(CLIENT_ID, CLIENT_SECRET, TEST_PLAN_ID, cycle_id, cycle_name="TEST_CYCLE")
        os.environ['TEST_CYCLE_ID'] = str(jamatest.JamaTest.test_cycle.ID)
        with open("variables.env", "w") as f:
            f.write(f"TEST_CYCLE_ID={str(jamatest.JamaTest.test_cycle.ID)}")
        request.addfinalizer(publish_results)

def publish_results():
    if os.getenv("ENV") != "local" and os.getenv("SKIP_JAMA") != "1":
        jamatest.publish()

def pytest_configure(config):
    os.environ["env"] = config.getoption("env")
    os.environ["TRIGGERED_JOB"] = config.getoption("env")

###### These automation is taking random screenshots... need to investigate 
# def pytest_unconfigure(config):
#     Deletion_Automation_Data()
#     Error_Table_Generation()
#     TestReport_Generation()
#     Summary_Table_Formation()
#     Send_Mail()
#     logging.info("finally")
###### These automation is taking random screenshots... need to investigate 
# def pytest_unconfigure(config):
#     Deletion_Automation_Data()
#     Error_Table_Generation()
#     TestReport_Generation()
#     Summary_Table_Formation()
#     Send_Mail()
#     logging.info("finally")

@pytest.fixture(scope="session")
def Flight_save_timer():
    file_name = "Flight_save_latency_list.json"
    results = read_data(file_name)
    yield results
    write_data(file_name, results)
    logger.debug("Number of Flight latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title("Flight Saving Latency")
    plt.xlabel("Flight number")
    plt.ylabel("Time (Seconds)")
    plt.savefig("Flight_save_latency.png", dpi=300)

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)
    
def update_nullable(data):
    if isinstance(data, dict):
        nullable_value = data.get('nullable')
        if str(nullable_value).lower() == 'true' and 'type' in data:
            original_type = data['type']
            if not isinstance(original_type, list):
                original_type = [original_type]
            if 'null' not in original_type:
                original_type.append('null')
            data['type'] = original_type
            del data['nullable']
        for key, value in data.items():
            update_nullable(value)
    elif isinstance(data, list):
        for item in data:
            update_nullable(item)

def remove_field(schema, fieldRemove='productCategoryAttributes'):
    if isinstance(schema, dict):
        schema.pop(fieldRemove, None)
        for value in schema.values():
            remove_field(value)
    elif isinstance(schema, list):
        for item in schema:
            remove_field(item)

@pytest.fixture(scope="session")
def json_schema_file():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    for _ in range(1):
        current_dir = os.path.dirname(current_dir)
    schema_json_file_path = os.path.join(current_dir, 'groundside-openapi.yml')

    with open(schema_json_file_path, 'r') as file:
        openapi_spec = yaml.safe_load(file)
    update_nullable(openapi_spec)
    remove_field(openapi_spec)
    # print("data")
    # print(json.dumps(openapi_spec, indent=5, cls=CustomJSONEncoder))
    return openapi_spec

@pytest.fixture(scope="session")
def ref_resolver(json_schema_file):
    return RefResolver.from_schema(json_schema_file)

# @pytest.fixture(scope="session", autouse=True)
# def jama_init(request):
#     print(f"{jama_init.__name__}: Checking if JAMA is being used or not...")
#     CLIENT_ID = os.getenv("JAMA_CLIENT_ID")
#     CLIENT_SECRET = os.getenv("JAMA_CLIENT_SECRET")
#     TEST_PLAN_ID = 17494322
#
#     if TEST_PLAN_ID and CLIENT_ID is not None and os.getenv("ENV") != "local" and os.getenv("SKIP_JAMA") != "1":
#         cycle_id = os.getenv("TEST_CYCLE_ID")
#         # cycle_id = "16463243" #it can be gathered inspecting the page
#         print(f"JAMA_CLIENT: {cycle_id}")
#         jamatest.init(CLIENT_ID, CLIENT_SECRET, TEST_PLAN_ID, cycle_id, cycle_name="TEST_CYCLE")
#         os.environ['TEST_CYCLE_ID'] = str(jamatest.JamaTest.test_cycle.ID)
#         update_env_variable('variables.env', 'TEST_CYCLE_ID', str(jamatest.JamaTest.test_cycle.ID))
#         request.addfinalizer(publish_results)
#
# def publish_results():
#     if os.getenv("ENV") != "local" and os.getenv("SKIP_JAMA") != "1":
#         jamatest.publish()

@pytest.fixture(scope="session")
def token_airline():
    return get_token('airline', base_url_env)

@pytest.fixture(scope="session")
def token_store():
    print("getting token...")
    return get_token('store', base_url_env)

@pytest.fixture(scope="session")
def header_store(token_store):
    return {"Content-Type": "application/json", "Authorization": token_store}

@pytest.fixture(scope="session")
def header_airline(token_airline):
    return {"Content-Type": "application/json", "Authorization": token_airline}

@pytest.fixture(scope="session")
def store_http_session():
    token, expires_at = get_token('store', base_url_env, True)
    session = requests.Session()
    adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({"Content-Type": "application/json", "Authorization": token})
    
    original_request = session.request
    
    def refresh_token_if_needed():
        nonlocal token, expires_at 
        if datetime.now() >= expires_at - timedelta(seconds=45):
            token, expires_at = get_token('store', base_url_env, True)
            session.headers.update({"Content-Type": "application/json", "Authorization": token})
            
    def request_with_refresh(*args, **kwargs):
        refresh_token_if_needed()
        return original_request(*args, **kwargs)
    
    session.request = request_with_refresh
    yield session
    session.close()

@pytest.fixture(scope="session")
def airline_session():
    token, expires_at = get_token('airline', base_url_env, True)
    session = requests.Session()
    adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({"Content-Type": "application/json", "Authorization": token})
    
    original_request = session.request
    
    def refresh_token_if_needed():
        nonlocal token, expires_at 
        if datetime.now() >= expires_at - timedelta(seconds=45):
            token, expires_at = get_token('airline', base_url_env, True)
            session.headers.update({"Content-Type": "application/json", "Authorization": token})
            
    def request_with_refresh(*args, **kwargs):
        refresh_token_if_needed()
        return original_request(*args, **kwargs)
    
    session.request = request_with_refresh
    yield session
    session.close()
    
@pytest.fixture(scope="session")
def json_reference_p_customizer() -> CG_type:
    print("in json_reference_p_customizer...")

    json_file = open("json_payloads/product_customizer.json", 'r', encoding='utf-8')
    new_customizer = json.load(json_file) 
    json_file.close()
    #Setting some specific name for verifying in the GET api call
    new_customizer['title']['en'] = "Crust - " + date_string
    new_customizer['title']['es'] = "Crustá en español - " + date_string
    for option in new_customizer['customizerOptions']: #iterate over the options
        for lenguage in option['label']: #iterate over the lenguages
            option['label'][lenguage] += date_string    
    arr = [new_customizer]
    return new_customizer

@pytest.fixture(scope="session")
def track_customizer_groups_input_data() -> List[CG_type]:
    print("in track_customizer_groups_input_data...")

    json_file = open("json_payloads/product_customizer.json", 'r', encoding='utf-8')
    new_customizer = json.load(json_file) 
    json_file.close()
    #Setting some specific name for verifying in the GET api call
    new_customizer['title']['en'] += date_string
    new_customizer['title']['es'] += date_string
    new_customizer['title']['zh_Hans'] += date_string
    #change the labels:
    for option in new_customizer['customizerOptions']:
        for lenguage in option['label']:
            option['label'][lenguage] += date_string    
    arr = [new_customizer]
    # print("the json_data::", json.dumps(arr, indent=3, ensure_ascii=False))
    yield arr
    with open("track_customizer_groups_input_data.json", 'w', encoding='utf-8') as file: 
        json.dump(arr, file, ensure_ascii=False)

@pytest.fixture(scope="session")
def track_customizers_IDs():
    print("in track_customizers_IDs")
    tracking_arr = []
    yield tracking_arr
    logger.info(f"There was a total of {str(len(tracking_arr))} product customizers created during the test ")
    with open("track_customizers_IDs.json", 'w', encoding='utf-8') as file: 
        json.dump(tracking_arr, file, ensure_ascii=False)
    
@pytest.fixture(scope="session")
def schema_loader():
    schemas_dir = 'utils/schema_files'
    schemas = {}
    for filename in os.listdir(schemas_dir):
        if filename.endswith('.json'):
             with open(os.path.join(schemas_dir, filename), 'r') as file:
                schema_key = filename.replace(".json", "")
                schemas[schema_key] = json.load(file)
    def get_schema(key):
        print ("THE KEY PASSED:", key)
        print(schemas.get(key))
        return schemas.get(key)
    return get_schema

##### PRODUCT FIXTURES
@pytest.fixture(scope="session")
def json_reference_product():
    json_v2 = open("json_payloads/productV2.json", 'r', encoding='utf-8')
    data_v2 = json.load(json_v2) 
    json_v2.close()
    json_file = open("json_payloads/product.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    
    return {
            "v1": data,
            "v2": data_v2
            }

@pytest.fixture(scope="session")
def track_brands():
    track_arr = {}
    yield track_arr
    with open("brands.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)

@pytest.fixture(scope="session")
def track_product_input_data():
    track_arr = []
    yield track_arr
    with open("track_product_input_data.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)
        
@pytest.fixture(scope="session")
def track_product_input_data_map():
    track_arr = {}
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} products created during the test ")
    with open("track_product_input_data_map.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)        

@pytest.fixture(scope="session")
def track_product_IDs():
    track_arr = []
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} products created during the test ")
    with open("track_product_IDs.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)
        
#### CATALOG FIXTURES
@pytest.fixture(scope="session")
def json_reference_catalog():
    json_file = open("json_payloads/catalog.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    
    json_file_v2 = open("json_payloads/catalogV2.json", 'r', encoding='utf-8')
    data_v2 = json.load(json_file_v2) 
    json_file_v2.close()

    return {
        "v1": data,
        "v2": data_v2
        }

@pytest.fixture(scope="session")
def track_catalog_input_data():
    track_list = {}
    yield track_list
    logger.info(f"There was a total of {str(len(track_list))} catalogs created during the test ")
    with open("track_catalog_input_data.json", 'w', encoding='utf-8') as file: 
        json.dump(track_list, file, ensure_ascii=False)

@pytest.fixture(scope="session")
def track_catalog_IDs():
    track_arr = []
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} catalogs created during the test ")
    with open("track_catalog_IDs.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)

#### Sector
@pytest.fixture(scope="session")
def json_reference_sector():
    json_file = open("json_payloads/sector.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    return data

@pytest.fixture(scope="session")
def track_sector_IDs():
    track_arr = []
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} catalogs created during the test ")
    with open("track_sectors_IDs.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)

##### category structure
@pytest.fixture(scope="session")
def track_airline_categories():
    category = CategoryTree(1)
    yield category

    category_structure = category.to_dict()
    with open("track_airline_categories.json", 'w', encoding='utf-8') as file:
        json.dump(category_structure, file, indent=4, ensure_ascii=False)

@pytest.fixture(scope="session")
def json_reference_airline_category():
    json_file = open("json_payloads/airline-category.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    return data

#### Route catalog
@pytest.fixture(scope="session")
def json_reference_route_catalog():
    json_file = open("json_payloads/route-catalog.json", 'r', encoding='utf-8')
    data = json.load(json_file) 
    json_file.close()
    return data    

@pytest.fixture(scope="session")
def airline_driver(airline_user_login_timer):
    try:
        driver = create_driver()
        driver.get(LOGIN_URL)
        sleep(2)
        username_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[1]')
        password_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[2]')  # Adjust the selector as needed
        login_button = driver.find_element(By.XPATH,'//*[@id="form-element"]/button')  # Adjust the selector as needed
        username_field.send_keys(AIRLINE_USERNAME)
        password_field.send_keys(AIRLINE_PASSWORD)
        start_time = time.time()
        login_button.click()
        check_succesfull_login(driver, timer_arr=airline_user_login_timer, start_time=start_time, user="Airline")
        sleep(5)
        return driver
    except Exception as e:
        capture_failed_details(driver, "airline_driver", e )

@pytest.fixture(scope="session")
def pac_user_driver(pac_login_timer):
    try:
        driver = create_driver()
        driver.get(LOGIN_URL)
        sleep(2)
        username_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[1]')
        password_field = driver.find_element(By.XPATH,'//*[@id="form-element"]/input[2]')  # Adjust the selector as needed
        login_button = driver.find_element(By.XPATH,'//*[@id="form-element"]/button')  # Adjust the selector as needed
        sleep(1)
        username_field.send_keys(PAC_USER_USERNAME)
        password_field.send_keys(PAC_USER_PASSWORD)
        start_time = time.time()
        login_button.click()
        check_succesfull_login(driver, 200, pac_login_timer, start_time, user="PAC_ADMIN") ### Need to do this for 
        sleep(10)
        navigate_to_sns(driver)  ##### new change.. need to be commented
        return driver 
    except Exception as e:
        capture_failed_details(driver, "pac_user_driver", e )

@pytest.fixture(scope="session")
def pac_login_timer():
    user = "Pac_user"
    file_name = user+"_login_latency_list.json"
    results = read_data(file_name)
    yield results
    create_login_graph(user,file_name, results )
    
@pytest.fixture(scope="session")
def airline_user_login_timer():
    user = "Airline_user"
    file_name = user+"_login_latency_list.json"
    results = read_data(file_name)
    yield results
    create_login_graph(user,file_name, results )
    
@pytest.fixture(scope="session")
def store_user_login_timer():
    user = "Store_user"
    file_name = user+"_login_latency_list.json"
    results = read_data(file_name)
    yield results
    create_login_graph(user,file_name, results )

def create_login_graph(user, file_name, results):
    write_data(file_name, results)
    logger.debug("Number of latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title(user+" login latency")
    plt.xlabel("Login number")
    plt.ylabel("Time (Seconds)")
    plt.savefig(user+"_login_latency.png", dpi=300)
    
@pytest.fixture(scope="session")
def product_save_timer():
    file_name = "product_save_latency_list.json"
    results = read_data(file_name)
    yield results
    write_data(file_name, results)
    logger.debug("Number of latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title("Product save latency")
    plt.xlabel("Product number")
    plt.ylabel("Time (Seconds)")
    plt.savefig("product_save_latency.png", dpi=300)
    
@pytest.fixture(scope="session")
def CA_save_timer():
    file_name = "CA_save_latency_list.json"
    results = read_data(file_name)
    yield results
    write_data(file_name, results)
    logger.debug("Number of Catalog assigment latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title("Catalog Assigment Saving Latency")
    plt.xlabel("Catalog Assigment number")
    plt.ylabel("Time (Seconds)")
    plt.savefig("CA_save_latency.png", dpi=300)

@pytest.fixture(scope="session")
def Catalog_save_timer():
    file_name = "Catalog_save_latency_list.json"
    results = read_data(file_name)
    yield results
    write_data(file_name, results)
    logger.debug("Number of Catalogs latency results tracked: "+ str(len(results)))
    plt.clf() #clear figure and axes
    plt.cla()
    plt.plot(results, marker='o')
    plt.title("Catalogs Saving Latency")
    plt.xlabel("Catalogs number")
    plt.ylabel("Time (Seconds)")
    plt.savefig("Catalog_save_latency.png", dpi=300)

def read_data(file_name):
    try:
        with open(file_name, 'r') as file:
            return json.load(file)
    except:
        logger.debug(f"File '{file_name}' does not exist")
        return []
def write_data(file_name, data):
    with open(file_name, "w") as file:
        json.dump(data, file, indent=4 )

##### category structure
@pytest.fixture(scope="session")
def track_catalog_assigments():
    track_arr = {}
    yield track_arr
    logger.info(f"There was a total of {str(len(track_arr))} catalog assigments created during the test ")
    with open("track_catalog_assigments.json", 'w', encoding='utf-8') as file: 
        json.dump(track_arr, file, ensure_ascii=False)        


