pytest-json
pytest
pytest-html
selenium
Pillow
pandas
webdriver-manager
matplotlib
boto3
jamatest
openapi-schema-validator
imagesize

#credencys
beautifulsoup4==4.12.2
black==23.3.0
colorama==0.4.6
coverage==7.2.7
cryptography==41.0.0
cssselect==1.2.0
decorator==5.1.1
deepdiff==6.3.0
docker==6.1.3
Faker~=37.0.2
filelock==3.12.2
flake8==6.0.0
gitdb==4.0.10
GitPython==3.1.31
google-api-core==2.11.0
google-api-python-client==2.88.0
google-auth==2.19.0
google-auth-httplib2==0.1.0
google-auth-oauthlib==1.0.0
googleapis-common-protos==1.59.0
googlesearch-python==1.2.3
gspread==5.9.0
httpcore==0.17.2
jsonschema~=4.23.0
jsonschema-spec==0.2.3
jsonschema-specifications==2023.7.1
matplotlib-inline
multidict==6.0.4
nodeenv==1.8.0
numpy
openpyxl~=3.1.5
oauth2client==4.1.3
oauthlib==3.2.2
Office365-REST-Python-Client==2.4.1
plotly==5.15.0
pretty-html-table
py==1.11.0
PyAutoIt==0.6.5
pycparser==2.21
pydata-google-auth==1.8.0
PyDrive2==1.15.4
pyflakes==3.0.1
PyGetWindow==0.0.9
pyinstaller
pyinstaller-hooks-contrib==2023.3
PyJWT==2.7.0
pylatexenc==2.10
PyMsgBox==1.0.9
pyOpenSSL==23.2.0
pyotp==2.8.0
pyparsing==3.0.9
PyPDF2==3.0.1
pyscreenshot==3.1
pytest-asyncio==0.21.0
pytest-base-url==2.0.0
pytest-benchmark==4.0.0
pytest-cov==4.1.0
pytest-integration==0.2.3
pytest-json-report==1.5.0
pytest-metadata==2.0.4
pytest-mock==3.11.1
pytest-selenium==4.0.1
pytest-variables==3.0.0
pytest-xdist==3.3.1
python-dateutil==2.8.2
python-docx==0.8.11
python-dotenv==1.0.0
PyYAML~=6.0.2
pyyaml_env_tag==0.1
readability-lxml==0.8.1
regex==2023.6.3
requests~=2.32.3
requests-oauthlib==1.3.1
selenium-webdriver-extender==0.0.0
sqlparse==0.4.4
tabulate==0.9.0
types-beautifulsoup4==4.12.0.5
types-colorama==0.4.15.11
types-html5lib==1.1.11.14
uritemplate==4.1.1
urllib3==1.26.15
urllib3-secure-extra==0.1.0
virtualenv==20.23.1
websocket-client==1.6.0
xlrd==2.0.1
xmldiff==2.6.3
xmltodict==0.13.0
imagesize~=1.4.1
pytz~=2024.2
jsondiff~=2.2.1
chardet~=5.2.0
pillow~=11.1.0
