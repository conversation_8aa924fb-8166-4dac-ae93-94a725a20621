class Config:
    def __init__(self, e):
        self.e = e

    def env_aws_flight(self):
        env = self.e.get('environment')
        global FLIGHT_SQS_QUEUE_URL
        if env == "dev":
            FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testFlightQueue-dev"
        elif env == "qa":
            FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testFlightQueue-qa"
        elif env == "test":
            FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testFlightQueue-test"
        else:
            print("Invalid env")
        return FLIGHT_SQS_QUEUE_URL

    def env_aws_pdt_spa_ca(self):
        env = self.e.get('environment')
        global CATALOG_SQS_QUEUE_URL
        if env == "dev":
            CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testCatalogQueue-dev"
        elif env == "qa":
            CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testCatalogQueue-qa"
        elif env == "test":
            CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testCatalogQueue-test"
        else:
            print("Invalid env")
        return CATALOG_SQS_QUEUE_URL






