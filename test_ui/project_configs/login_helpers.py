import glob

import PIL.Image, logging
from selenium import webdriver
import os, pytest, pandas as pd
import time, csv, random, string
from selenium.webdriver.common.by import By

from driver_setup.config import DOWNLOAD_PATH
from project_configs.csv_config import Config
from selenium.webdriver import Action<PERSON>hains, Keys
from selenium.common import NoSuchElementException
from project_configs.env_ui_constants import constant
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# wait function declaration
def wait(seconds):
    start_time = time.time()
    while time.time() - start_time < seconds:
        pass

# generate_random_string function
def generate_random_string(length):
    letters = string.ascii_letters
    return ''.join(random.choice(letters) for _ in range(length))

# generate_random_integer function
def generate_random_integer(length):
    digits = string.digits
    return ''.join(random.choice(digits) for _ in range(length))

# -- for manual run use e like this --
# e = get_environment()

def get_store(driver, e):
    action = ActionChains(driver)
    select_store = WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, e.get('store_xpath'))))
    action.double_click(select_store).perform()

def get_uitemp(driver, e):
    action = ActionChains(driver)
    uitemp = WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, e.get('uitemp_created_id_xpath'))))
    action.double_click(uitemp).perform()

def get_spa(driver, e):
    action = ActionChains(driver)
    WebDriverWait(driver, constant.wait_time_qa).until(
        EC.element_to_be_clickable((By.NAME, constant.spa_name_text))).click()
    wait(2)
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.NAME, constant.spa_name_text))).send_keys(e.get('spa_name'))
    action.send_keys(Keys.RETURN).perform()

def get_screenshot(script_name, driver):
    script_name_without_extension = os.path.splitext(script_name)[0]
    screenshot_path = os.path.join(Config.get_path("results_folder_path"), f"{script_name_without_extension}_{constant.dt_string}.png")
    # screenshot_path = os.path.join(f"{script_name_without_extension}_{constant.dt_string}.png")
    driver.save_screenshot(screenshot_path)

# re-writing csv file contents required
def airlinecategory_add_csv():
    try:
        new_name = 'Airline_'
        new_description = 'Airline'
        csv_file = Config.get_path("airlinecategory_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_name + generate_random_string(3)
            data[1][1] = new_description
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("airlinecategory_sample_csv_path"))
        name_1 = df['name_en'].tolist()
        desc_1 = df['description_en'].tolist()
        print("\n")
        print("name_en in csv: " + (name_1[0]).lower())
        print("description_en in csv: " + (desc_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def airlinecategory_update_csv():
    try:
        new_description = 'Airline_'
        csv_file = Config.get_path("airlinecategory_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][1] = new_description + generate_random_string(3) + "_Updated"
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("airlinecategory_sample_csv_path"))
        desc_1 = df['description_en'].tolist()
        print("\n")
        print("description_en in csv: " + (desc_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def flight_add_csv():
    try:
        new_flightRouteNumber = 'Flight_'
        csv_file = Config.get_path("flight_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_flightRouteNumber + generate_random_string(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("flight_sample_csv_path"))
        routeno_1 = df['flightRouteNumber'].tolist()
        print("\n")
        print("flightRouteNumber in csv: " + (routeno_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def flight_pac_add_csv():
    try:
        new_flightRouteNumber = 'Flight_'
        # new_begindate = constant.current_date
        # new_begintime = constant.current_time
        # new_enddate = constant.next_date
        # new_endtime = constant.next_minute
        csv_file = Config.get_path("flight_pac_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_flightRouteNumber + generate_random_string(3)
            # data[1][1] = new_begindate + " " + new_begintime
            # data[1][2] = new_enddate + " " + new_endtime
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("flight_pac_sample_csv_path"))
        routeno_1 = df['flightRouteNumber'].tolist()
        # flightbegin_1 = df['flightBeginDateTime'].tolist()
        # flightend_1 = df['flightEndDateTime'].tolist()
        print("\n")
        print("flightRouteNumber in csv: " + (routeno_1[0]).lower())
        # print("flightBeginDateTime in csv: " + (flightbegin_1[0]).lower())
        # print("flightEndDateTime in csv: " + (flightend_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def flight_update_csv():
    try:
        new_sectorsName = "sector_1,"
        csv_file = Config.get_path("flight_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][3] = new_sectorsName + generate_random_integer(3) + ";"
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("flight_sample_csv_path"))
        sectorsName_1 = df['sectorsName'].tolist()
        print("\n")
        print("Sector sequence in csv: " + (sectorsName_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def routegrp_update_csv():
    try:
        new_code = "RG_"
        csv_file = Config.get_path("routegrp_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][1] = new_code + generate_random_integer(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("routegrp_sample_csv_path"))
        code_1 = df['code'].tolist()
        print("\n")
        print("Code in csv: " + (code_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def airport_update_csv():
    try:
        new_airportName = "Airport_"
        new_icao_code = "A"
        new_iata_code = "A"
        csv_file = Config.get_path("airport_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_airportName + generate_random_string(3)
            data[1][1] = new_icao_code + generate_random_integer(3)
            data[1][2] = new_iata_code + generate_random_integer(2)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("airport_sample_csv_path"))
        airportName = df['airportName_en'].tolist()
        icao_code = df['icao_code'].tolist()
        iata_code = df['IATACode'].tolist()
        print("\n")
        print("Airport name in csv: " + (airportName[0]).lower())
        print("ICAO code in csv: " + (icao_code[0]).lower())
        print("IATA code in csv: " + (iata_code[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def flight_pac_update_csv():
    try:
        new_sectorsName = "sector_1,"
        csv_file = Config.get_path("flight_pac_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][3] = new_sectorsName + generate_random_integer(3) + ";"
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("flight_pac_sample_csv_path"))
        sectorsName_1 = df['sectorsName'].tolist()
        print("\n")
        print("Sector sequence in csv: " + (sectorsName_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def brand_add_csv(csv_path=None):
    try:
        new_name = 'Pac_'
        print("csv ", csv_path)
        csv_file = Config.get_path(csv_path)
        print("csv ", csv_file)
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_name + constant.dt_string
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path(csv_path))
        name_1 = df['name_en'].tolist()
        print("\n")
        print("Name in csv: " + (name_1[0]).lower())
        return name_1
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def product_csv_update_brand_id(csv_path=None, brand_id=None, same_brand=True):
    try:
        print("csv ", csv_path)
        new_name = 'Pac_'
        csv_file = Config.get_path(csv_path)
        print("csv ", csv_file)
        df = pd.read_csv(csv_file)
        df['brand_id'] = df['brand_id'].astype(object)
        sku_col_index = df.columns.get_loc('sku')
        # print(sku_col_index, "sku-----")
        if same_brand:
            df.at[0, 'brand_id'] = str(brand_id)
            df.at[1, 'brand_id'] = str(brand_id)
            df.at[0, 'name_en'] = new_name + generate_random_string(3)
            df.at[1, 'name_en'] = new_name + generate_random_string(3)
            df.at[0, 'sku'] = 'SKU_' + generate_random_string(5)
            df.at[1, 'sku'] = 'SKU_' + generate_random_string(5)
        else:
            df.at[0, 'brand_id'] = str(brand_id)
        df.to_csv(csv_file, index=False)
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path(csv_path))
        names = df['name_en'].tolist()
        print("\n")
        skus = df['sku'].tolist()
        print("\n")
        print("Name in csv: " + (names[0]).lower() + ", " + (names[1]).lower())
        print("SKUs in csv: " + (skus[0]).lower() + ", " + (skus[1]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def brand_update_csv(id=None, csv_path=None):
    try:
        new_name = 'Pac_'
        id_new = id
        csv_file = Config.get_path(csv_path)
        df = pd.read_csv(csv_file)
        df['brand_id'] = df['brand_id'].astype(object)
        df.at[0, 'brand_id'] = str(id_new)
        df.at[0, 'name_en'] = new_name + generate_random_string(3)
        df.to_csv(csv_file, index=False)
        df_updated = pd.read_csv(csv_file)
        name_1 = df_updated['name_en'].tolist()
        id_list = df_updated['brand_id'].tolist()

        print("\n")
        print(id_list, "id")
        print("Name in csv: " + (name_1[0]).lower())
        print("Id in csv: " + str(id_list[0]))
        return name_1
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def aircraft_add_csv():
    try:
        new_oemname = 'Airbus A380_'
        csv_file = Config.get_path("aircraft_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_oemname + generate_random_string(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("aircraft_sample_csv_path"))
        name_1 = df['OEMName_en'].tolist()
        print("\n")
        print("Name in csv: " + (name_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def aircraft_update_csv():
    try:
        new_desc = 'Airbus is an international pioneer in the aerospace industry_'
        csv_file = Config.get_path("aircraft_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][1] = new_desc + generate_random_string(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("aircraft_sample_csv_path"))
        desc_1 = df['description_en'].tolist()
        print("\n")
        print("name in csv: " + (desc_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def sector_add_csv():
    try:
        new_name = 'sector_'
        csv_file = Config.get_path("sector_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_name + generate_random_string(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("sector_sample_csv_path"))
        name_1 = df['sectorName_en'].tolist()
        print("\n")
        print("name in csv: " + (name_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def sector_update_csv():
    try:
        new_summary = 'sector_'
        csv_file = Config.get_path("sector_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][1] = new_summary + generate_random_string(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("sector_sample_csv_path"))
        summary_1 = df['summary_en'].tolist()
        print("\n")
        print("summary in csv: " + (summary_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def catalog_add_csv(csv_path=None, multiple_import=None):
    global name_1
    try:
        new_name = 'Catalog_'
        new_product = 'product1_storeautomation'
        csv_file = Config.get_path(csv_path)
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            if multiple_import:
                data[1][0] = new_name + constant.dt_string + generate_random_string(3)
                data[3][0] = new_name + constant.dt_string + generate_random_string(3)
                data[5][0] = new_name + constant.dt_string + generate_random_string(3)
            else:
                data[1][0] = new_name + generate_random_string(3)
                data[1][2] = new_product
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path(csv_path))
        name_1 = df['name_en'].tolist()
        pdt_1 = df['products'].tolist()
        print("\n")
        print("name in csv: " + (name_1[0]).lower())
        print("first product in csv: " + (pdt_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))
    print("names: ", name_1)
    print("products: ", pdt_1)
    return name_1, pdt_1

def catalog_update_csv():
    try:
        new_product = 'product4_storeautomation'
        csv_file = Config.get_path("catalog_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][2] = new_product
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("catalog_sample_csv_path"))
        pdt_1 = df['products'].tolist()
        print("\n")
        print("updated first product in csv: " + (pdt_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def category_add_csv_with_images_zip():
    try:
        new_name = 'Exclusively cooked at home_'
        new_disclaimer = 'Exclusively cooked at home'
        csv_file = Config.get_path("category_sample_csv_path_with_images_zip")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_name + generate_random_string(3)
            data[1][2] = new_disclaimer
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("category_sample_csv_path_with_images_zip"))
        name_1 = df['name_en'].tolist()
        disc_1 = df['disclaimer_en'].tolist()
        print("\n")
        print("name in csv: " + (name_1[0]).lower())
        print("disclaimer in csv: " + (disc_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def category_add_multilevel_values_csv():
    global name_1
    try:
        new_name = 'Category_'
        csv_file = Config.get_path("category_multilevel_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_name + "1_" +constant.dt_string
            data[2][0] = new_name + "2_" + constant.dt_string
            data[3][0] = new_name + "3_" + constant.dt_string
            data[2][8] = "/" + data[1][0]
            data[3][8] = "/" + data[1][0]
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("category_multilevel_path"))
        name_1 = df['name_en'].tolist()
        print("\n")
        print(name_1)
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))
    return name_1

def category_add_csv():
    try:
        new_name = 'Exclusively cooked at home_'
        new_disclaimer = 'Exclusively cooked at home'
        csv_file = Config.get_path("category_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_name + generate_random_string(3)
            data[1][2] = new_disclaimer
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("category_sample_csv_path"))
        name_1 = df['name_en'].tolist()
        disc_1 = df['disclaimer_en'].tolist()
        print("\n")
        print("name in csv: " + (name_1[0]).lower())
        print("disclaimer in csv: " + (disc_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def category_update_csv():
    try:
        new_disclaimer = 'Exclusively cooked at home_'
        csv_file = Config.get_path("category_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][2] = new_disclaimer + generate_random_string(3) + "_Updated"
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("category_sample_csv_path"))
        disc_1 = df['disclaimer_en'].tolist()
        print("\n")
        print("disclaimer in csv: " + (disc_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def product_add_csv(e):
    try:
        # print("env: ", e.get('environment'))
        env = e.get('environment')
        new_name = 'mobile_'
        new_sku = 'mobile_'
        # for different speciality attribute id's, csv's are different to upload
        global csv_file
        if env == "dev":
            csv_file = Config.get_path("product_sample_dev_csv_path")
        elif env == "qa":
            csv_file = Config.get_path("product_sample_qa_csv_path")
        elif env == "test":
            csv_file = Config.get_path("product_sample_test_csv_path")
        else:
            print("Invalid product sample file")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_name + generate_random_string(3)
            data[1][8] = new_sku + generate_random_string(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(csv_file)
        name_1 = df['name_en'].tolist()
        sku_1 = df['sku'].tolist()
        print("\n")
        print("name in csv: " + (name_1[0]).lower())
        print("sku in csv: " + (sku_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def product_update_csv(e):
    try:
        new_shortDescription = 'mobile_'
        env = e.get('environment')
        global csv_file
        # for different speciality attribute id's, csv's are different to upload
        if env == "dev":
            csv_file = Config.get_path("product_sample_dev_csv_path")
        elif env == "qa":
            csv_file = Config.get_path("product_sample_qa_csv_path")
        elif env == "test":
            csv_file = Config.get_path("product_sample_test_csv_path")
        else:
            print("Invalid product sample file")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][2] = new_shortDescription + generate_random_string(3) + "_Updated"
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(csv_file)
        shortDesc_1 = df['shortDescription_en'].tolist()
        print("\n")
        print("shortDescription in csv: " + (shortDesc_1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def prepare_spa_csv_for_upload():
    try:
        new_description = 'Cooked at home_'
        new_desc_ja = '家で調理した_'
        csv_file = Config.get_path("specialityattribute_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][0] = new_description + generate_random_string(3)
            data[1][1] = new_desc_ja + generate_random_string(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("specialityattribute_sample_csv_path"))
        desc_1 = df['description_en'].tolist()
        desc_ja1 = df['description_ja'].tolist()
        print("\n")
        print("description in csv: " + (desc_1[0]).lower())
        print("description_ja in csv: " + (desc_ja1[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def spa_update_csv():
    try:
        new_disclaimer = 'Cooked at home_'
        new_desc_ja = '家で調理した_'
        csv_file = Config.get_path("specialityattribute_sample_csv_path")
        with open(csv_file, 'r') as file:
            reader = csv.reader(file)
            data = list(reader)
            data[1][3] = new_disclaimer + generate_random_string(3) + "_Updated"
            data[1][1] = new_desc_ja + generate_random_string(3)
        with open(csv_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(data)
        df = pd.read_csv(Config.get_path("specialityattribute_sample_csv_path"))
        disc_1 = df['disclaimer_en'].tolist()
        desc_2 = df['description_ja'].tolist()
        print("\n")
        print("disclaimer in csv: " + (disc_1[0]).lower())
        print("description in csv: " + (desc_2[0]).lower())
    except Exception as E:
        print("Issue occured in sample csv file changes")
        print("\n" + str(E))

def automap_off(driver):
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, constant.airline_name_xpath))).click()
    wait(10)
    try:
        # WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.LINK_TEXT, constant.yes_btn_text))).click()
        element = driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
        element.click()
    except NoSuchElementException:
        pass
    wait(5)
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.LINK_TEXT, constant.stores_tab_text))).click()
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL, 'a')
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.BACK_SPACE * 3)
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
    # wait(50)
    wait(10)
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL,'a')
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys("No",Keys.ENTER)
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
    # wait(70)
    wait(25)
    # driver.refresh()

def automap_on(driver):
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, constant.airline_name_xpath))).click()
    time.sleep(10)
    try:
        element = driver.find_element(By.LINK_TEXT, constant.yes_btn_text)
        element.click()
    except NoSuchElementException:
        pass
    time.sleep(5)
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.LINK_TEXT, constant.stores_tab_text))).click()
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL, 'a')
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.BACK_SPACE * 3)
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
    # wait(50)
    time.sleep(15)
    # WebDriverWait(driver, constant.wait_time_qa).until(EC.presence_of_element_located((By.LINK_TEXT, "Saved Successfully!")))
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]"))).click()
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys(Keys.CONTROL,'a')
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[4]//input"))).send_keys("Yes",Keys.ENTER)
    # WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
    # wait(20)
    time.sleep(10)
    WebDriverWait(driver, constant.wait_time_qa).until(EC.element_to_be_clickable((By.XPATH, "//td[5]"))).click()
    WebDriverWait(driver, constant.wait_time_qa).until(
        EC.element_to_be_clickable((By.XPATH, "//td[5]//input"))).send_keys(Keys.CONTROL, 'a')
    WebDriverWait(driver, constant.wait_time_qa).until(
        EC.element_to_be_clickable((By.XPATH, "//td[5]//input"))).send_keys("Yes", Keys.ENTER)
    WebDriverWait(driver, constant.wait_time_qa).until(
        EC.element_to_be_clickable((By.LINK_TEXT, constant.save_and_publish_btn_text))).click()
    # wait(70)
    time.sleep(25)
    driver.refresh()




