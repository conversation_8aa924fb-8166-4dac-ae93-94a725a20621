import os
import time
import pytest
from time import sleep
from selenium.webdriver.common.by import By
from project_configs.csv_config import Config
from project_configs.login_helpers import logger
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def check_succesfull_login(driver, wait_time=120, timer_arr=None, start_time=time.time(), user_login=""):
    LOGIN_PAGE_RETRIES_ENABLED = "1"
    if LOGIN_PAGE_RETRIES_ENABLED == "1" or LOGIN_PAGE_RETRIES_ENABLED.lower() == "yes" or LOGIN_PAGE_RETRIES_ENABLED.lower() == "true" or LOGIN_PAGE_RETRIES_ENABLED.lower() == "y":
        maxRetries = 5
    else:
        maxRetries = 1
    for i in range(maxRetries):
        try:
            act_title = WebDriverWait(driver, wait_time).until(EC.visibility_of_element_located((By.XPATH, "//a[@id='pimcore_logout']"))).get_attribute("id")
            assert act_title == "pimcore_logout"
            logger.debug("Login succesfull")
            if timer_arr is not None:
                end_time = time.time()
                logger.info("Ended the timer: %s", end_time)
                save_time = end_time-start_time
                logger.debug("Login timer is " + str(save_time))
                pytest.assume(save_time<100, f"{user_login} user took more than 200 seconds to login... Total save time: {str(save_time)}")
                timer_arr.append(save_time)
            break
        except Exception as e:
            logger.info("ERR: Failed to get the logged in page in try:" + str(i))
            logger.debug(e)
            end_time = time.time()
            save_time = end_time-start_time
            folder_path = Config.get_path("results_folder_path")
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
            screenshot_path = os.path.join(folder_path,
                                           f"failed_{user_login}_driver_login_failure_try_{i}_{save_time}_sec.png")
            driver.save_screenshot(screenshot_path)
            # driver.save_screenshot(Config.get_path("results_folder_path"),"failed_"+user_login+"_driver_login_failure_try_"+str(i)+"_"+str(save_time)+"_sec"+ ".png")
            # sleep(0.5)
            sleep(3 ** maxRetries)
            driver.refresh()
            start_time = time.time() # reset the timer
            if i+1 == maxRetries:
                logger.info("There was an issue loggin in... Please check credentials")
                raise