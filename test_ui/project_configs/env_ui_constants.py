import os
from datetime import datetime, timedelta
# ----Command to run manually----
# pytest /Users/<USER>/test_ui/test_modules/test_airline_pac_ui.py --env dev --html=report.html --log-level=INFO -s

# jama testcases and testplan
# https://panasonicavionics.jamacloud.com/perspective.req#/containers/16284270?projectId=2422
# https://panasonicavionics.jamacloud.com/perspective.req#/testPlans/20774738/testRuns?projectId=2422
# pytest test_modules/test_brand_default_language.py  -m reg --env pac -s

class constant:
    # ----waiting time----
    wait_time_qa = "180"

    # ---store&airline---
    store_value_name = "store_automation"
    airline_value = "airline_gui_automation"

    # ---user ids & passwords---
    pac_user_username = "<EMAIL>"
    pac_user_password = "<EMAIL>"
    store_user_username = "<EMAIL>"
    store_user_password = "<EMAIL>"
    airline_user_username = "<EMAIL>"
    airline_user_password = "<EMAIL>"

    # ---locators---
    now = datetime.now()
    print("now =", now)
    dt_string = now.strftime("%d%m%Y%H%M%S")

    # dt_string specific ones
    monthlycatalogload_name = "MonthlyCatalogsLoad_" + dt_string
    spa_value = "SpecialityAttribute_" + dt_string
    catalog_value = "Catalog_" + dt_string
    airline_name = "Airline_" + dt_string
    icaocode_value = "icaocode_" + dt_string
    iatacode_value = "iatacode_" + dt_string
    email_id_value = "email_id." + dt_string + "@gmail.com"
    airlinecategory_name = "AirlineCategory_" + dt_string
    category_value = "Category_" + dt_string
    aircraft_value = "Aircraft_" + dt_string
    cabinclass_value = "Cabinclass_" + dt_string
    mealcode_name = "MealCode_" + dt_string
    store_value = "Store_" + dt_string
    storeloc_value = "StoreLocation_" + dt_string
    pdtsku_value = "Product_" + dt_string
    barcode_value = "Product" + dt_string
    flight_name = "flight_" + dt_string
    catalog_assignment_name = "CA_" + dt_string
    sector_name = "sector_" + dt_string
    fulfillment_value_add = "Fulfillment_" + dt_string
    roottype_value_add = "RootType_" + dt_string

    # sns paths
    triggeredsns_folder_xpath = "//span[contains(text(),'Triggered SNS')]//parent::div//div[contains(@class,'pimcore_icon_folder')]"
    sns_download_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-medium pimcore_material_icon_download pimcore_material_icon ')])"
    triggered_sns_tab_xpath = "(//span[contains(text(),'Triggered SNS')])[2]"
    triggered_sns1_tab_xpath = "(//span[contains(text(),'Triggered SNS')])[1]"
    flight_sns_id = "(//span[contains(@class,'ace_string')])[1]"
    flight_unpublish_sns_id = "(//span[contains(@class,'ace_constant ace_numeric')])"
    sns_id_aircat_xpath = "(//span[contains(@class,'ace_constant ace_numeric')])[2]"
    ca_sns_id = "(//span[contains(@class,'ace_constant ace_numeric')])[1]"
    sns_id_chk_xpath = "(//span[contains(@class,'ace_string')])[1]"
    sns_id_check_xpath = "(//div[5][contains(@class,'ace_line')]//child::span[contains(@class,'ace_string')])"
    unpublish_sns_id = "(//span[contains(@class,'ace_constant ace_numeric')])"
    flight_unpublish_sns_id_chk_xpath = "(//span[3])[5]"
    sns_id_xpath = "(//span[contains(@class,'ace_constant ace_numeric')])[2]"
    sns_id_chk = "(//span[contains(@class,'ace_constant ace_numeric')])[1]"
    airline_sns_latest_published_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/airline_publish')])[1]"
    spa_sns_latest_published_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/speciality_attribute_publish')])[1]"
    pdt_sns_latest_published_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/product_publish')])[1]"
    airlinecategory_sns_latest_published_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/airline_category_publish')])[1]"
    flight_sns_latest_published_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/flight_publish')])"
    ca_sns_latest_published_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/catalog_assignment_publish')])[1]"
    store_sns_latest_published_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/store_publish')])[1]"
    sns_spa_latest_xpath = "(//div[contains(text(),'/Log/Triggered SNS/speciality_attribute_publish_')])[1]"
    airline_sns_latest_unpublished_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/airline_unpublish')])[1]"
    ca_sns_latest_unpublished_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/catalog_assignment_unpublish')])[1]"
    spa_sns_latest_unpublished_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/speciality_attribute_unpublish')])[1]"
    pdt_sns_latest_unpublished_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/product_unpublish')])[1]"
    flight_sns_latest_unpublished_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/flight_unpublish')])[1]"
    airlinecategory_sns_latest_unpublished_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]//child::div[contains(text(),'/Log/Triggered SNS/airline_category_unpublish')])[1]"

    roottype_text = "RootType"
    roottype_xpath = "//input[contains(@name, 'rootTypeName')]"
    catalog_management_tab_xpath = "//div[contains(text(),'Catalog Management')]"
    csvimport_icon_id = "pimcore_menu_massupdate"
    airline_csvfield_name = "airlineList"
    csvimport_btn_text = "CSV Import"
    mealcode_btn_text = "Open Mealcode Module"
    importdatatype_id = "importType-inputEl"
    importdatatype_value = "SpecialityAttributes"
    downloadcsv_btn_id = "download_CSV_button"
    selectfile_id = "csvFile-button-fileInputEl"
    upload_btn_text = "Upload"
    airline_textbox = "airline"
    getcadata_btn_text = "Get Data"
    monthlycatalogload_text = "MonthlyCatalogsLoads"
    close_reload_btn_text = "Close & Reload"
    spa_folder_xpath = "//span[contains(text(),'SpecialityAttributes')]"
    log_table_xpath = "//div[contains(@class,'x-grid-cell-inner x-grid-cell-inner-treecolumn')]//child::span[contains(text(),'Log')]//parent::div//div[contains(@class,'expander')]"
    creationdate_column_xpath_update = "//span[contains(text(),'Creation Date (System)')]"
    logout_id = "pimcore_logout"
    assets_tab_id = "pimcore_panel_tree_assets_header-title-textEl"
    list_btn_text = "List"
    log_folder_expander = "//span[(text()='Log')]//parent::div//div[contains(@class,'expander')]"
    creation_date_column_xpath = "//div[contains(@id, 'headercontainer-')]//child::div//span[contains(text(),'Creation Date')]"
    creation_date_column_after_update_xpath ="(//div[contains(@id, 'headercontainer-')]//child::div//span[contains(text(),'Creation Date')])[2]"
    open_btn_text ="Open"
    store_name = "storeList"
    cabinclass_folder_xpath = "//span[contains(text(),'Cabin Class')]"
    fulfillment_folder_xpath = "//span[contains(text(),'Fulfillment')]"
    speciality_attribute_btn_text = "SpecialityAttributes"
    add_speciality_attribute_id = "messagebox-1001-textfield-inputEl"
    ok_btn_id = "button-1005-btnEl"
    store_id_xpath = "//div[contains(text(),'ID')]"
    store_id_update_xpath = "(//div[contains(text(),'ID')])[2]"
    short_desc_name = "description"
    disclaimer_xpath = "(//div[contains(@class,'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr cke_show_borders')])[1]"
    disclaimer_read_spa_xpath = "//div[contains(@class,'pimcore_editable_wysiwyg') and contains(@id, 'object_wysiwyg')]/text()"
    store_search_xpath = "//span[contains(@class,'pimcore_icon_search')]"
    store_search_pac_xpath = "(//span[contains(@class,'pimcore_icon_search')])[3]"
    assets_tab_text = "Assets"
    pimcore_icon_search_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search ')]"
    uitemp_search_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search ')]"
    search_input_name = "query"
    catalog_delta_qa_value = "220501"
    catalog_delta_dev_value = "69189795"
    catalog_delta_test_value = "86632747"
    search_btn_text = "Search"
    select_img_name = "Image"
    image_value = "256x256"
    save_and_publish_btn_text = "Save & Publish"
    unpublish_btn_text = "Unpublish"
    search_home_xpath_update = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter ')]"
    filterfromhome_name = "filter"
    airline_folder_filter_xpath = "(//input[contains(@class,'x-form-field x-form-text x-form-text-default  x-form-empty-field x-form-empty-field-default')])[1]"
    master_data_text = "Master Data"
    airline_folder_addobj_text = "Airline"
    airline_folder_text = "Airlines"
    filter_close_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-cancel-filter ')]"
    airline_folder_search_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter ')])[2]"
    home_btn_xpath = "//span[contains(text(),'Home')]"
    add_obj_btn_text = "Add Object"
    username_txt_name = "username"
    password_txt_name = "password"
    login_btn_xpath = "//button[text()='login']"
    catalog_name_value = "name"
    catalog_btn_text = "Catalog"
    add_catalog_id = "messagebox-1001-textfield-inputEl"
    catalog_folder_xpath = "//span[contains(text(),'Catalog')]"
    creation_date_xpath = "//span[contains(text(),'Creation Date')]"
    open_ca_tab_xpath = "//span[contains(text(),'CA_')]"
    open_store_xpath = "(//span[contains(text(),'store_automation')])[2]"
    open_category_tab_text = "//span[contains(text(),'category_5level_5')]"
    airline_from_dropdown_xpath = "//li[contains(text(),'airline_gui_automation')]"
    airline_search_dropdown_xpath = "//div[contains(@id,'airlineComboBox-trigger-picker')]"
    catalog_delta_value_path = "(//div[contains(text(),'/store_automation/Catalog/catalog_delta')])[1]"
    latest_id_xpath = "//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn')]"
    catalog_id_xpath = "//div[contains(@class,'x-panel pimcore_class_Catalog x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')]"
    spa_id_xpath = "//div[contains(@class,'x-panel pimcore_class_SpecialityAttributes x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')]"
    inventorylocation_latest_xpath = "(//div[contains(text(),'/store_automation/Inventory Location/')])[1]"
    name_xpath1 = "(//input[contains(@name,'name')])[1]"
    name_xpath2 = "(//input[contains(@name,'name')])[2]"
    name_name = "name"
    code_name = "code"
    sector_name_name = "sectorName"
    summary_name = "summary"
    oem_name = "OEMName"
    family_name = "familyName"
    model_name = "modelName"
    products_tab_text = "Products"
    delta_tab_text = "Delta Products"
    search_button_xpath = "//span[contains(@class,'pimcore_icon_search')]"
    select_btn_text = "Select"
    save_btn_text = "Save"
    delete_xpath = "//img[contains(@data-qtip, 'Remove')]"
    yes_btn_text = "Yes"
    home_xpath = "//span[contains(text(),'Home')]"
    add_object_text = "Add Object"
    catalog_folder_text = "Catalog"
    search_box_xpath = "//input[contains(@name,'query')]"
    search_icon_xpath = "//span[contains(text(),'Search')]"
    ca_latest_store_xpath = "(//div[contains(text(),'/Catalog Assignment/airline_gui_automation/store_automation/CA_')])[1]"
    store_choose_xpath = "(//div[contains(@class,'x-form-trigger x-form-trigger-default x-form-arrow-trigger x-form-arrow-trigger-default ')])[2]"
    store_category_open_xpath = " //div[contains(@class,'x-tree-icon x-tree-icon-custom x-tree-icon-parent-expanded pimcore_icon_folder')]"
    catalog_folder_xpath_pac = "(//span[contains(text(),'Catalog')])[1]"
    importdatatype_value_c = "Catalog"
    store_box_name = "storeList"
    airline_main_folder_expander_xpath = "//span[contains(text(),'Airlines')]//parent::div//div[contains(@class,'-expander')]"
    ca_folder_expander_xpath = "//span[contains(text(),'Catalog Assignment')]//parent::div//div[contains(@class,'-expander')]"
    airline_foler_expander_xpath = "//span[contains(text(),'airline_gui_automation')]//parent::div//div[contains(@class,'-expander')]"
    airline_field_name = "airline"
    airline_name_textbox_id = "messagebox-1001-textfield-inputEl"
    ok_button_text = "OK"
    airline_id_xpath = "//div[contains(@class,'x-panel pimcore_class_Airline x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')]"
    airlinename_box_name = "airlineName"
    description_box_xpath = "//textarea[contains(@name,'description')]"
    description_box_aircat_xpath = "(//textarea[contains(@name,'description')])[2]"
    icaocode_box_name = "ICAOCode"
    iatacode_box_name = "iataCode"
    logo_search_xpath = "//span[contains(@class,'pimcore_icon_search')]"
    searchbox_name = "query"
    search_value = "flight"
    searchbutton_text = "Search"
    img_grid_xpath = "//div[contains(@name,'Image')]"
    contact_info_tab_name = "Contact & Address Information"
    firstname_box_name = "firstName"
    company_name = "company"
    address_name = "addressLine2"
    state_name = "state"
    country_name = "country"
    country_value = "India"
    email_box_name = "email"
    lastname_name = "lastName"
    addressline1_name = "addressLine1"
    city_name = "city"
    zipcode_name = "zipCode"
    phone_name = "phone"
    phone_number = "+91 14567890"
    aircrafttab_text = "AirCrafts"
    select_button_text = "Select"
    stores_tab_text = "Stores"
    save_tab_name = "Save & Publish"
    airline_folder_value = "Airlines"
    airline_folder_open_xpath = "//div[contains(@class,'  x-tree-icon x-tree-icon-custom x-tree-icon-parent pimcore_icon_folder')]"
    airline_folder_expander_xpath = "//div[contains(@class,'  x-tree-icon x-tree-icon-custom x-tree-icon-parent pimcore_icon_folder')]//parent::div//div[contains(@class,'-expander')]"
    # airline_folder_open_xpath = "//div[contains(@class,'  x-tree-icon x-tree-icon-custom x-tree-icon-parent-expanded pimcore_icon_folder')]"
    latest_id_xpath_update_airline = "(//div[contains(@class,'x-grid-cell-inner ')])[6]"
    airline_profile_xpath = "//div[contains(text(),'Airline Profile')]"
    category_management_tab_xpath = "//div[contains(text(), 'Category Management')]"
    category_folder_xpath = "//span[contains(text(),'Category')]"
    routegroup_folder_xpath = "//span[contains(text(),'RouteGroup')]"
    aircraft_folder_xpath = "//span[contains(text(),'Aircraft')]"
    flight_folder_xpath = "//span[contains(text(),'Flight')]"
    category_box_xpath = "//*[@id='messagebox-1001-textfield-inputEl']"
    airline_click_xpath = "//span[contains(text(),'airline_gui_automation')]"
    ok_btn_text = "OK"
    airport_name="airportName"
    disclaimer_name = "disclaimer"
    shortDescription_name = "shortDescription"
    description_name = "description"
    url_name = "url"
    img_2_dropdown_xpath = "(//div[contains(@id,'_header-innerCt')]//child::div[contains(@class,'x-tool-tool-el x-tool-img x-tool-expand-bottom')])[1]"
    img_2_folder_xpath = "//span[contains(@id,'button-1344-btnIconEl')]"
    img_3_dropdown_xpath = "(//div[contains(@id,'_header-innerCt')]//child::div[contains(@class,'x-tool-tool-el x-tool-img x-tool-expand-bottom ')])[1]"
    img_3_folder_xpath = "//span[contains(@id,'button-1353-btnIconEl')]"
    img_4_dropdown_xpath = "(//div[contains(@id,'_header-innerCt')]//child::div[contains(@class,'x-tool-tool-el x-tool-img x-tool-expand-bottom ')])[1]"
    img_5test_dropdown_xpath = "(//div[contains(@id,'_header-innerCt')]//child::div[contains(@class,'x-tool-tool-el x-tool-img x-tool-expand-bottom ')])[1]"
    img_4_folder_xpath = "//span[contains(@id,'button-1362-btnIconEl')]"
    img_5_folder_xpath = "//span[contains(@id,'button-1371-btnIconEl')]"
    img_6_folder_xpath = "//span[contains(@id,'button-1380-btnIconEl')]"
    img_1_search_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[1]"
    img_1_search_aircat_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[2]"
    img_2_search_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[2]"
    img_2_search_aircat_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[3]"
    img_3_search_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[3]"
    img_3_search_aircat_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[4]"
    img_4_search_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[4]"
    img_4_search_aircat_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[5]"
    img_5_search_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[5]"
    img_5_search_aircat_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[6]"
    img_6_search_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[6]"
    img_6_search_aircat_xpath = "(//div[contains(@class,'x-box-inner x-box-menu-body-horizontal')]//child::a[4][contains(@class,'x-btn x-unselectable x-box-item x-toolbar-item x-btn-default-toolbar-small')])[7]"
    img_5_dropdown_xpath = "(//div[contains(@id,'_header-innerCt')]//child::div[contains(@class,'x-tool x-box-item x-tool-default x-tool-after-title')])[12]"
    img_5_dropdown_store_xpath = "(//div[contains(@id,'_header-innerCt')]//child::div[contains(@class,'x-tool x-box-item x-tool-default x-tool-after-title')])[30]"
    img5_store_dropdown_xpath = "(//div[contains(@id,'_header-innerCt')]//child::div[contains(@class,'x-tool x-box-item x-tool-default x-tool-after-title')])[28]"
    img6_expand_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_plus ')]"
    search_xpath = "//input[contains(@name,'query')]"
    search_catalog_pdt_xpath = "(//input[contains(@name,'query')])[1]"
    search_xpath_ca =  "(//input[contains(@name,'query')])[2]"
    imgposition_name = "imagePosition"
    imgposition_value = "1"
    storeloc_text = "StoreLocation"
    search_btn_xpath = "//span[contains(text(),'Search')]"
    img1_search_value = "1200X1200"
    img2_search_value = "1480x740"
    img3_search_value_pdt = "944X1416"
    img3_search_value = "crct_760x570"
    img5_search_value_pdt = "670X804"
    img4_search_value = "665X798"
    img6_value = "1200x1200"
    img5_search_value = "3160X632"
    img6_search_value = "3840X2160"
    airlinecategory_id_xpath = "//div[contains(@class,'x-panel pimcore_class_AirlineCategory x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')]"
    rg_id_xpath = "(//div[contains(@class,'x-panel pimcore_class_RouteGroup x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')])[1]"
    category_text = "Category"
    mealcode_text = "MealCode"
    aircraft_text = "AirCraft"
    cabincls_text = "CabinClass"
    fulfillment_text = "Fulfillment"
    flight_text = "Flight"
    airport_text = "Airports"
    airlinecategory_text = "AirlineCategory"
    routegrp_text = "RouteGroup"
    routegrp_value = "routegroup_1"
    add_category_id = "messagebox-1001-textfield-inputEl"
    add_aircraft_id = "messagebox-1001-textfield-inputEl"
    add_object_name_id = "messagebox-1001-textfield-inputEl"
    mealc_name = "mealCode"
    category_id_xpath = "//div[contains(@class,'x-panel pimcore_class_Category x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')]"
    aircraft_id_xpath = "(//div[contains(@class,'x-panel-body x-panel-body-default x-border-layout-ct x-closable x-panel-body-closable x-panel-body-default-closable x-panel-body-default x-panel-body-default-closable x-noborder-trbl')]//child::div[contains(text(),'ID')])[1]"
    aircraft_csv_id_xpath = "(//div[contains(@class,'x-panel-body x-panel-body-default x-border-layout-ct x-closable x-panel-body-closable x-panel-body-default-closable x-panel-body-default x-panel-body-default-closable x-noborder-trbl')]//child::div[contains(text(),'ID')])[2]"
    url = "https://i-converter.com/files/jpg-to-ttttrr"
    sales_strategy_xpath = "//input[contains(@name,'salesStrategy')]"
    sales_name = "D2C"
    store_category_xpath = "//input[contains(@name,'store')]"
    attributes_tab_xpath = "//span[contains(text(),'Attributes')]"
    attribute_set_xpath = "//input[contains(@name,'attributeSet')]"
    attribute_value = "Mobile Phones"
    variant_expand_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_plus ')]"
    theme_name_xpath = "//input[contains(@name,'themeName')]"
    variant_attribute_xpath = "//input[contains(@name,'variantAttribute')]"
    varaint_attribute_value = "Display"
    rg_search_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search ')])[1]"
    airline_search_rg_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search ')])[2]"
    airport1_search_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search ')])[3]"
    airport2_search_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search ')])[4]"
    pac_pdt_search_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search ')]"
    catalog_delta_search = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search ')])[2]"
    category_tab_xpath = "//div[contains(text(),'Categories')]"
    masterdata_folder_expander_xpath = "//span[contains(text(),'Master Data')]//parent::div//div[contains(@class,'-expander')]"
    category_expand_xpath = "//span[contains(text(),'Category')]//parent::div//div[contains(@class,'-expander')]"
    roottype_expand_xpath = "//span[contains(text(),'Root Type')]//parent::div//div[contains(@class,'-expander')]"
    airport_folder_xpath = "//span[contains(text(),'Airports')]"
    aircraft_expand_xpath = "//span[contains(text(),'Aircraft')]//parent::div//div[contains(@class,'-expander')]"
    pdt_expand_xpath = "//span[contains(text(),'Products')]//parent::div//div[contains(@class,'-expander')]"
    category_filter_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter ')])[2]"
    roottype_filter_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter ')])[2]"
    airport_filter_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter ')])[2]"
    aircraft_filter_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter ')])[2]"
    category_filter_store_send_xpath = "(//input[contains(@name,'filter')])[2]"
    aircraft_filter_store_send_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small x-tbar-page-filter ')])[2]"
    sales_name_xpath = "salesStrategy"
    store_text = "Stores"
    storelocname = "storeId"
    add_store_id = "messagebox-1001-textfield-inputEl"
    storename_name = "storeName"
    storeloc_name= "locationName"
    store_img_name = "store"
    addressinfo_tab_text = "Address Information"
    config_tab_text = "Configuration"
    autoapprove_chk_xpath = "//input[contains(@name,'autoapprovecatalog')]"
    managestock_name = "manageStock"
    taxpercent_name = "taxPercentage"
    tax_value = "1"
    orderprovider_name = "orderProvider"
    orderprovider_value = "store"
    orderthreshold_value_xpath = "(//div[contains(@class,'x-form-spinner x-form-spinner-default x-form-spinner-up x-form-spinner-up-default ')])[3]"
    selectlang_name = "storeLanguage"
    selectairlinelang_name = "airlineLanguage"
    lang_value = "Japanese"
    dispcurrency_name = "storeCurrency"
    dispcurrency_value = "US Dollar (USD)"
    payconfig_tab_text = "Payment Configuration"
    bypasspacpay_name = "bypassPACPayment"
    associatedairline_tab_text = "Associated Airline"
    storesfolder_xpath = "//span[contains(text(),'Stores')]"
    store_close = "(//span[contains(text(),'Stores')])[2]"
    storeselect_name= "selectClass"
    store_latest_from_folder_xpath = "(//table[contains(@id,'patchedgridview-')]//child::div[contains(@class,'x-grid-cell-inner ')])[2]"
    productcreate_btn_text = "Create New Product"
    product_sku_name = "sku"
    category_options_xpath = "(//div[contains(@class,'x-form-trigger x-form-trigger-default x-form-arrow-trigger x-form-arrow-trigger-default ')])[3]"
    category_options_store_xpath = "(//div[contains(@class,'x-form-trigger x-form-trigger-default x-form-arrow-trigger x-form-arrow-trigger-default ')])[2]"
    product_set_select_name = "type"
    pdt_set_value = "Simple"
    ok_btn_value = "OK"
    product_name = "name"
    delivery_type_select_name = "deliveryType"
    product_type = "productType"
    deltype_value = "Onboard"
    pdttype_value = "Duty Free"
    pricing_and_taxation_tab_text = "Pricing & Taxation"
    price_name = "price_USD"
    weight_and_shipping_details_tab_text = "Weight & Shipping Details"
    weight_xpath = "(//input[contains(@name, 'numberfield-')])[2]"
    weight_store_xpath = "(//input[contains(@name, 'numberfield-')])[1]"
    wt_unit_value = "LB"
    wt_unit_xpath = "(//input[contains(@name, 'combobox-')])[1]"
    shipl_xpath = "(//input[contains(@name, 'numberfield-')])[3]"
    shipl_store_xpath = "(//input[contains(@name, 'numberfield-')])[2]"
    shipl_unit_xpath = "(//input[contains(@name, 'combobox-')])[2]"
    ship_unit_value = "IN"
    shipw_xpath = "(//input[contains(@name, 'numberfield-')])[4]"
    shipw_store_xpath = "(//input[contains(@name, 'numberfield-')])[3]"
    shipw_unit_xpath = "(//input[contains(@name, 'combobox-')])[3]"
    shiph_xpath = "(//input[contains(@name, 'numberfield-')])[5]"
    shiph_store_xpath = "(//input[contains(@name, 'numberfield-')])[4]"
    shiph_unit_xpath = "(//input[contains(@name, 'combobox-')])[4]"
    shortdesc_pdt_xpath = "(//div[contains(@class,'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr cke_show_borders')])[1]"
    desc_pdt_xpath = "(//div[contains(@class,'pimcore_editable_wysiwyg cke_editable cke_editable_inline cke_contents_ltr cke_show_borders')])[2]"
    brand_name = "brand"
    barcode_name = "barcode"
    pdtid_name = "productId"
    pdt_sku_xpath = "(//input[contains(@name,'sku')])[1]"
    deliverymethod_xpath = "(//div[contains(@class,'x-tagfield-item-text')])[2]"
    ca_catalog_xpath = "(//div[contains(@id,'displayfield-')])[1]"
    pdttype_xpath = "//input[contains(@name,'productType')]"
    spa_value_xpath = "(//div[contains(@class,'x-tagfield-item-text')])[4]"
    na_country_name = "notApplicableCountries"
    newfrm_name = "newFrom"
    newfrm_value = "2025-01-18"
    newto_name = "newTo"
    newto_value = "2025-01-19"
    nextflight_name = "nextFlightLeadTime"
    nextflight_value = "1"
    specialPrice_name = "specialPrice_USD"
    taxtick_name = "isTaxable"
    maxqty_name = "maxQuantityAllowed"
    inventory_tab_text = "Inventory"
    inventory_loc_text = "Inventory Location"
    invloc_folder_xpath = "//span[contains(text(),'Inventory Location')]"
    invloc_tab_xpath = "//div[contains(text(),'Inventory Location')]"
    categoryspecific_tab_text = "Category Specific Attributes"
    display_name = "Display"
    ram_name = "RAM"
    os_name = "OS"
    phonefield_name = "Phone"
    display_value = "LCD"
    ram_value = "6GB"
    os_value = "Android"
    phone_value = "Samsung M31"
    storespecific_tab_text = "Store Specific Attributes"
    storespecific_tab_xpath = "//div[contains(@id, 'tabbar-')]//child::a//span[contains(text(),'Store Specific Attributes')]"
    spa_tab_xpath = "//div[contains(@id, 'tabbar-')]//child::a//span[contains(text(),'Speciality Attributes')]"
    pdt_basedata_tab_xpath = "//div[contains(@id, 'tabbar-')]//child::a//span[contains(text(),'Base Data')]"
    spa_name_text = "specialityAttribute"
    basedata_xpath = "//span[contains(text(),'Base Data')]"
    pdt_id_xpath = "(//div[contains(@class,'x-panel pimcore_class_Products x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')])[1]"
    pdt_id_store_xpath = "//div[contains(text(),'ID')]"
    pdtcsv_value = "Products"
    pdt_folder_xpath = "(//span[contains(text(),'Products')])[1]"
    categorycsv_id = "categoryField-trigger-picker"
    autoupdatecatalog_xpath = "(//span[contains(@class,'x-form-field x-form-checkbox x-form-checkbox-default x-form-cb x-form-cb-default ')])[1]"
    autoupdate_xpath = "//input[contains(@name,'autoUpdateCatalog')]"
    updatecatalog_chkbox = "(//div[contains(@id,'checkcolumn-')])[1]"
    updatecatalog_chkbox_off_xpath = "(//div[contains(@id,'checkcolumn-')])[10]"
    updatecatalog_text = "Update Catalog"
    onboarddelivery_name = "airlineOrderLimitMax"
    onboarddelivery_value = "1"
    roottype_name = "rootType"
    uitemp_created_value = "UItemp"
    roottype_value = "rootType_2"
    store_button_xpath = "//span[contains(text(),'store_automation')]//parent::div//div[contains(@class,'folder')]"
    latest_id_xpath_update = "(//div[contains(text(),'/store_automation/SpecialityAttributes/')]//parent::td//div[contains(@class,'x-grid-cell-inner')])[1]"
    store_expand_xpath = "//span[contains(text(),'store_automation')]//parent::div//div[contains(@class,'expander')]"
    storeoptions_xpath = "//li[contains(text(),'store_automation')]"
    pdt1 = "product1_automation"
    pdt2 = "product2_automation"
    pdt3 = "product3_automation"
    pdt4 = "product4_automation"
    pdt5 = "product5_automation"
    export_csv_btn_text = "Export CSV"
    rg_click_xpath = "//div[contains(text(),'/airline_gui_automation/RouteGroup/routegroup_1')]"
    product1_click_xpath_pac = "//div[contains(text(),'/store_automation/Products/category_1/product_1storeautomation')]"
    product2_click_xpath_pac = "//div[contains(text(),'/store_automation/Products/category_1/product_2storeautomation')]"
    product3_click_xpath_pac = "//div[contains(text(),'/store_automation/Products/category_1/product_3storeautomation')]"
    pdt_catalog_select_xpath = "//div[contains(text(),'40238']"
    product4_click_xpath_pac = "//div[contains(text(),'/store_automation/Products/category_1/product_4storeautomation')]"
    product5_click_xpath_pac = "//div[contains(text(),'/store_automation/Products/category_1/product_5storeautomation')]"
    pdt1_click_xpath = "//div[contains(text(),'/product1_automation')]"
    pdt2_click_xpath = "//div[contains(text(),'/product2_automation')]"
    pdt3_click_xpath = "//div[contains(text(),'/product3_automation')]"
    pdt4_click_xpath = "//div[contains(text(),'/product4_automation')]"
    pdt5_click_xpath = "//div[contains(text(),'/product5_automation')]"
    latest_category_xpath = "(//div[contains(text(),'/Master Data/Category/store_automation/')])[1]"
    latest_roottype_xpath = "(//div[contains(text(),'/Master Data/Root Type/airline_gui_automation/')])[1]"
    latest_airport_xpath = "(//table[contains(@id,'patchedgridview-')])[1]"
    latest_cabinclass_xpath = "(//div[contains(text(),'/Master Data/Cabin Class/')])[1]"
    latest_fulfillment_xpath = "(//div[contains(text(),'/Master Data/Fulfillment/')])[1]"
    ca_latest_xpath = "(//div[contains(text(),'/Catalog Assignment/airline_gui_automation/store_automation/')])[1]"
    latest_aircraft_xpath = "(//table[contains(@id,'patchedgridview-')])[1]"
    store_category_folder_xpath = "(//span[contains(text(),'store_automation')])[2]"
    store_ca_chk_folder_xpath = "(//span[contains(text(),'store_automation')])[1]"
    store_folder_xpath = "//span[contains(text(), 'store_automation')]"
    categorypdt_value_xpath = "//li[contains(text(),'category_1 (store_automation/category_1)')]"
    pdtlatest_id_xpath = "(//div[contains(text(),'/store_automation/Products/')])[1]"
    product1_click_xpath_store = "//div[contains(text(),'/store_automation/Products/category_1/product_1storeautomation')]"
    product2_click_xpath_store = "//div[contains(text(),'/store_automation/Products/category_1/product_2storeautomation')]"
    product3_click_xpath_store = "//div[contains(text(),'/store_automation/Products/category_1/product_3storeautomation')]"
    product4_click_xpath_store = "//div[contains(text(),'/store_automation/Products/category_1/product_4storeautomation')]"
    product5_click_xpath_store = "//div[contains(text(),'/store_automation/Products/category_1/product_5storeautomation')]"
    latest_airlinecategory_xpath = "(//div[contains(text(),'/airline_gui_automation/Category/')])[1]"
    ac_search_xpath = "(//div[contains(text(),'/airline_gui_automation/Category/')])[1]"
    ac_search_xpath_sixth = "(//div[contains(text(),'/airline_gui_automation/Category/')])[6]"
    latest_routegroup_xpath = "(//table[contains(@id,'patchedgridview-')])[1]"
    latest_flight_xpath = "(//div[contains(text(),'/airline_gui_automation/Flight/')])[1]"
    airline_name_xpath = "(//span[contains(text(),'airline_gui_automation')])[1]"
    airline_xpath = "(//div[contains(text(),'/Airlines/airline_gui_automation')])[1]"
    airline_operation_tab_xpath = "//div[contains(text(),'Airline Operation')]"
    flight_id_xpath = "//div[contains(@class,'x-panel pimcore_class_Flight x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')]"
    airport_id_xpath = "(//div[contains(text(),'ID')])[2]"
    flight_add_obj = "Flight"
    flight_routeno_name = "flightRouteNumber"
    current_date = now.strftime('%Y-%m-%d')
    current_time = now.strftime('%H:%M')
    next_date = now + timedelta(days=1)
    next_date_str = next_date.strftime('%Y-%m-%d')
    next_minute = now + timedelta(minutes=1)
    next_minute_str = next_minute.strftime('%H:%M')
    begin_date_xpath = "(//input[contains(@name,'datefield-')])[1]"
    end_date_xpath = "(//input[contains(@name,'datefield-')])[2]"
    begin_time_xpath = "(//input[contains(@name,'timefield-')])[1]"
    end_time_xpath = "(//input[contains(@name,'timefield-')])[2]"
    fulfillment_name = "fulfillmentCutOffTimeLimit"
    fulfillment_value = "1"
    sectorinflight_add_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_plus ')]"
    sectorfield_name = "sector"
    sector_seq_name = "sector_sequence"
    sector_id_xpath = "(//div[contains(@class,'x-panel pimcore_class_Sector x-tabpanel-child x-panel-default x-closable x-panel-closable x-panel-default-closable')]//child::div[contains(text(),'ID')])"
    sector_value = "sector_1"
    sector_folder_xpath="//span[contains(text(),'Sectors')]"
    catalog_assignment_folder = "Catalog Assignment"
    catalog_assignment_tab_xpath = "//div[contains(text(), 'Catalog Assignment')]"
    ca_id_xpath = "(//div[contains(@class,'x-toolbar pimcore_main_toolbar x-border-item x-box-item x-toolbar-default x-box-layout-ct')]//child::div[contains(text(),'ID')])[2]"
    ca_id_pac_xpath = "(//div[contains(@class,'x-toolbar pimcore_main_toolbar x-border-item x-box-item x-toolbar-default x-box-layout-ct x-scroller x-toolbar-scroller x-toolbar-default-scroller')]//child::div[contains(text(),'ID')])"
    ca_id_pac2_xpath = "(//div[contains(@class,'x-toolbar pimcore_main_toolbar x-border-item x-box-item x-toolbar-default x-box-layout-ct x-scroller x-toolbar-scroller x-toolbar-default-scroller')]//child::div[contains(text(),'ID')])[2]"
    ca_catalog_search_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search')] "
    "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search')] "
    ca_catalog_value = "//div[contains(text(),'/store_automation/Catalog/catalog_1')]"
    ca_catalog_off_value = "//div[contains(text(),'/store_automation/Catalog/catalog_2')]"
    ca_catalog_off_value_text = "catalog_2"
    # ca_value = "//div[contains(text(),'/store_automation/Catalog/catalog_5level_test')]"
    ca_value = "//div[contains(text(),'/store_automation/Catalog/catalog5_level_5')]"
    ca_delta_value = "//div[contains(text(),'/store_automation/Catalog/catalog_delta')]"
    ca_namechange_value = "//div[contains(text(),'/store_automation/Catalog/catalog_fornamechange')]"
    # ca_delta_value = "//div[contains(text(),'/store_automation/Catalog/catalog_addupdate_delta')]"
    # ca_delta_value = "ca_delatchange"
    catalogfrom_name = "catalogFromDate"
    catalogto_name = "catalogToDate"
    cabincls_name = "cabinClass"
    cabincls_value = "Economy"
    assignmenttype_name = "assignmentType"
    assignmenttype_value_R = "RouteGroup"
    assignmenttype_value_F = "Flight"
    assignmenttype_value_S = "Sector"
    rg_name = "associateRouteGroup"
    rg_code_ui = "code"
    rg_value = "routegroup_1"
    actions_text = "Actions"
    edit_Airline_Category_text = "(//div[contains(text(),'Edit Airline Category')])[1]"
    req_assoc_text = "Request for Association"
    rej_assoc_text = "Reject for Association"
    req_dissoc_text = "Request for Dissociation to Store"
    approve_dissoc_text = "Approve for Dissociation"
    approve_assoc_text = "Approve For Association"
    editairlinecat_text = "Edit Airline Category"
    category_namechangecase_text = "category_5level_5"
    category_namechangecase_id_dev = "69189783"
    category_namechangecase_id_qa = "217090"
    category_namechangecase_id_test = "86909707"
    catalog_namechange_text = "catalog_fornamechange"
    ca_airline_folder_xpath = "(//span[contains(text(),'airline_gui_automation')])[2]"
    airline_tab_xpath = "//span[contains(text(),'airline_gui_automation')]"
    airline_first_xpath = "(//div[contains(@id,'pimcore_panel_tree_object_')]//child::span[contains(text(),'airline_gui_automation')])"
    ca_flight_search_xpath = "(//span[contains(@class, 'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search')])[3]"
    ca_sector_search_xpath = "(//span[contains(@class, 'x-btn-icon-el x-btn-icon-el-default-toolbar-small pimcore_icon_search ')])[2]"
    ca_sector_value = "//div[contains(text(),'/airline_gui_automation/Sectors/routegroup_1/sector_1')]"
    ca_flight_value = "//div[contains(text(),'/airline_gui_automation/Flight/flight_1')]"
    ca_obj_add_text = "CatalogAssignment"
    flight_value = "flight_1"
    flight_for_ca = "flight"
    ca_select_catalog = "catalog_1"
    ca_select_catalog_off = "catalog_2"
    # ca_select_catalog_5level = "catalog_5level_test"
    ca_select_catalog_5level = "catalog5_level_5"
    # ca_select_catalog_delta = "catalog_addupdate_delta"
    ca_select_catalog_delta = "catalog_delta"
    ca_delta_tab_xpath = "//span[contains(text(),'catalog_delta')]"
    catalog_5lvl_id = "54797"
    choose_action_dropdown_xpath = "(//div[contains(@class,'x-form-trigger x-form-trigger-default x-form-arrow-trigger x-form-arrow-trigger-default ')])[4]"
    choose_map_option_text = "Map Airline Categories"
    map_option_xpath = "//li[contains(text(),'Map Airline Categories')]"
    load_update_option_xpath = "//li[contains(text(),'Load Updated Catalog')]"
    update_inventory_option_xpath = "//li[contains(text(),'Update Deployment Inventory')]"
    csv_update_inventory_xpath = "//li[contains(text(),'Import Deployment Inventory CSV')]"
    deployment_inventory1_xpath = "(//div[contains(@id,'products_addQty_')]//td[3])[1]"
    deployment_inventory2_xpath = "(//div[contains(@id,'products_addQty_')]//td[3])[2]"
    deployment_inventory3_xpath = "(//div[contains(@id,'products_addQty_')]//td[3])[3]"
    deployment_inventory4_xpath = "(//div[contains(@id,'products_addQty_')]//td[3])[4]"
    deployment_inventory5_xpath = "(//div[contains(@id,'products_addQty_')]//td[3])[5]"
    choose_map_box_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[14]"
    choose_map_pac_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[18]"
    choose_map_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[21]"
    choose_map_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[21]//input"
    choose_map_xpath_after_update = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[14]"
    choose_map_xpath_after_update_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[14]//input"
    choose_map_delta_pac_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[20]"
    choose_map_delta_pac_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[20]//input"
    choose_map_delta_pac_off_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[5]"
    choose_map_delta_pac_off_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[5]//input"
    choose_map_airlineadmin_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[13]"
    choose_map_airlineadmin_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[13]//input"
    choose_map_airlineoff_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[17]"
    choose_map_airlineoff_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[17]//input"
    choose_map_airline_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[10]"
    choose_map_pac_dropdown_xpath = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[24]"
    choose_map_box_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[14]//input"
    choose_map_airline_box_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[10]//input"
    choose_map_pac_box_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[18]//input"
    dropdown_click_xpath = "//div[contains(@class,'x-panel objectlayout_element_Products objectlayout_element_panel x-tabpanel-child x-panel-default')]//div[contains(@id,'-trigger-picker')]"
    choose_map_pac_box_dropdown_xpath_with_input = "(//div[contains(@class,'x-form-text-wrap x-form-text-wrap-default')])[24]//input"
    cat_map_first_value_rg_click_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn-1707 x-grid-cell-last x-unselectable')])"
    cat_map_first_value_s_click_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn-')])[18]"
    cat_map_first_value_pac_click_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn-')])[22]"
    cat_map_first_value_pac_click_flight_sector_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn-')])[18]"
    cat_map_first_dropdown_xpath = "(//li[contains(@class,'x-boundlist-item')])[2]"
    submit_btn_text = "Submit"
    airlineCategoryMap_action_text = "Airline Category Mapped"
    pdt_value1_aircat5lvl_dev_xpath = "(//table[contains(@id,'tableview')]//td[contains(@class,'x-grid-cell-first x-unselectable')])[1]"
    pdt_value2_aircat5lvl_dev_xpath = "(//table[contains(@id,'tableview')]//td[contains(@class,'x-grid-cell-first x-unselectable')])[2]"
    pdt_value3_aircat5lvl_dev_xpath = "(//table[contains(@id,'tableview')]//td[contains(@class,'x-grid-cell-first x-unselectable')])[3]"
    pdt_value4_aircat5lvl_dev_xpath = "(//table[contains(@id,'tableview')]//td[contains(@class,'x-grid-cell-first x-unselectable')])[4]"
    pdt_value5_aircat5lvl_dev_xpath = "(//table[contains(@id,'tableview')]//td[contains(@class,'x-grid-cell-first x-unselectable')])[5]"
    aircat_value1_5lvl_dev_xpath = "(//td[7]//div[contains(@class,'x-grid-cell-inner ')])[1]"
    aircat_value2_5lvl_dev_xpath = "(//td[7]//div[contains(@class,'x-grid-cell-inner ')])[2]"
    aircat_value3_5lvl_dev_xpath = "(//td[7]//div[contains(@class,'x-grid-cell-inner ')])[3]"
    aircat_value4_5lvl_dev_xpath = "(//td[7]//div[contains(@class,'x-grid-cell-inner ')])[4]"
    aircat_value5_5lvl_dev_xpath = "(//td[7]//div[contains(@class,'x-grid-cell-inner ')])[5]"
    airline_search_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_search ')])[1]"
    airport1_select_xpath = "(//div[contains(text(),'/Master Data/Airports/')])[1]"
    airport2_select_xpath = "(//div[contains(text(),'/Master Data/Airports/')])[4]"
    airline_select_xpath = "(//div[contains(text(),'/Airlines/airline_gui_automation')])"
    sequence_value = "10"
    cat_map_xpath = "(//div[contains(@class,'x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-unselectable x-scroller x-focus x-grid-view-focus x-grid-view-default-focus')]//td[6])[1]"
    cat_map_value = "69086760"
    cat_map_value5cat_click_xpath = "(//li[contains(@class,'x-boundlist-item')])[5]"
    cat_map_value_xpath = "//li[contains(text(),'69086760')]"
    cat_seq1_xpath = "(//td[contains(@class,'x-grid-cell x-grid-td x-grid-cell-gridcolumn-2672 x-grid-cell-last x-unselectable')])[1]"
    catseq_tab_text = "Categories Sequence"
    pdtseq_tab_text = "Products Sequence"
    subs_tab_text = "Substitutions"
    managesubs_btn_text = "Manage Substitutions"
    seq1_column_xpath = "(//table[contains(@id, 'tableview-')]//td[4])[2]"
    csv_import_text = "Import CSV"
    cat_select_text = "category_1"
    selectfile_text = "Select File..."
    selectfile_name = "categoryfile"
    selectfilecat_name = "categorysequencefile"
    selectfilepdt_name = "productsequencefile"
    selectinventoryfile_name = "pricefile"
    cat_map = "(//div[contains(@class,'x-grid-view x-grid-with-col-lines x-grid-with-row-lines x-fit-item x-grid-view-default x-unselectable x-scroller x-focus x-grid-view-focus x-grid-view-default-focus')]//td[6])[2]"
    ca_latest_from_folder_xpath = "(//div[contains(text(),'/Catalog Assignment/airline_gui_automation/store_automation/CA_')])"
    aircat_map_subs_value_xpath = "//li[contains(text(),'category 5')]"
    del_subs_xpath = "//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_delete ')]"
    del_uitemp_xpath = "(//span[contains(@class,'x-btn-icon-el x-btn-icon-el-default-small pimcore_icon_delete ')])[2]"
    subs1_value_xpath = "//li[contains(text(),'product1_level_5')]"
    subs2_value_xpath = "(//li[contains(text(),'product2_level_5')])[2]"
    subs2_update_value_xpath ="//li[contains(text(),'product3_level_5')]"
    subs1_update_value_xpath = "(//li[contains(text(),'product4_level_5')])[2]"
    aircat_map_xpath = "(//input[contains(@name,'combobox')])[1]"
    subs1_xpath = "(//input[contains(@name,'combobox')])[2]"
    subs2_xpath = "(//input[contains(@name,'combobox')])[3]"
    aircat_dropdown_xpath = "(//div[contains(@id,'trigger-picker')])[1]"
    subs1_dropdown_xpath = "(//div[contains(@id,'trigger-picker')])[2]"
    subs2_dropdown_xpath = "(//div[contains(@id,'trigger-picker')])[3]"
    aircat_dropdown_update_xpath = "(//div[contains(@id,'trigger-picker')])[7]"
    subs1_dropdown_update_xpath = "(//div[contains(@id,'trigger-picker')])[8]"
    subs2_dropdown_update_xpath = "(//div[contains(@id,'trigger-picker')])[9]"
    aircat_airline_dropdown_update_xpath = "(//div[contains(@id,'trigger-picker')])[6]"
    subs1_airline_dropdown_update_xpath = "(//div[contains(@id,'trigger-picker')])[7]"
    subs2_airline_dropdown_update_xpath = "(//div[contains(@id,'trigger-picker')])[8]"
    brand_text = "Brands"
    brand_id_xpath = "(//div[contains(text(),'ID')])[2]"
    download_csv_brand = "Download Sample File Of Brands"
    store_brand_xpath = "//span[contains(text(), 'Flipkart')]"
    Brand_field = "//input[contains(@name,'brandSelect')]"



    # -----AWS-----
    # for dev use - 924166462419 aws-developer
    # FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testFlightQueue-dev"
    # CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testCatalogQueue-dev"
    # INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testInventoryQueue-dev"

    # for qa/test use - 009093030873 aws-devops - no access for me
    # CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testCatalogQueue-qa"
    # FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testFlightQueue-qa"
    # INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testInventoryQueue-qa"

    # older airline used
    # airline_value = "airline_automation"
    # ca_latest_from_folder_xpath = "(//div[contains(text(),'/Catalog Assignment/airline_automation/store_automation/CA_')])"
    # airline_select_xpath = "(//div[contains(text(),'/Airlines/airline_automation')])"
    # airline_user_username = "<EMAIL>"
    # airline_user_password = "airline_automation@gmail.com123"
    # latest_airlinecategory_xpath = "(//div[contains(text(),'/airline_automation/Category/')])[1]"
    # latest_flight_xpath = "(//div[contains(text(),'/airline_automation/Flight/')])[1]"
    # airline_name_xpath = "(//span[contains(text(),'airline_automation')])[1]"
    # airline_xpath = "(//div[contains(text(),'/Airlines/airline_automation')])[1]"
    # ca_airline_folder_xpath = "(//span[contains(text(),'airline_automation')])[2]"
    # airline_tab_xpath = "//span[contains(text(),'airline_automation')]"
    # ca_sector_value = "//div[contains(text(),'/airline_automation/Sectors/routegroup_1/sector_1')]"
    # ca_flight_value = "//div[contains(text(),'/airline_automation/Flight/flight')]"
    # airline_from_dropdown_xpath = "//li[contains(text(),'airline_automation')]"
    # ca_latest_store_xpath = "(//div[contains(text(),'/Catalog Assignment/airline_automation/store_automation/CA_')])[1]"
    # airline_foler_expander_xpath = "//span[contains(text(),'airline_automation')]//parent::div//div[contains(@class,'-expander')]"
    # rg_click_xpath = "//div[contains(text(),'/airline_automation/RouteGroup/routegroup_1')]"
    # latest_roottype_xpath = "(//div[contains(text(),'/Master Data/Root Type/airline_automation/')])[1]"
    # ca_latest_xpath = "(//div[contains(text(),'/Catalog Assignment/airline_automation/store_automation/')])[1]"


    # from_date_field.send_keys(current_date)
    # to_date_field.send_keys(next_date_str)
    # from_time_field.send_keys(current_time)
    # to_time_field.send_keys(next_minute_str)

    # csv old way of running
    # "manual":
    #     results_folder_path = os.getcwd() + "/screenshots/"
    #     airlinecategory_sample_csv_path = os.getcwd() + "/pdc_csv_files/Airline_Category_Sample.csv"
    #     aircraft_sample_csv_path = os.getcwd() + "/pdc_csv_files/AirCraft_Sample.csv"
    #     catalog_sample_csv_path = os.getcwd() + "/pdc_csv_files/Catalog_Sample.csv"
    #     sector_sample_csv_path = os.getcwd() + "/pdc_csv_files/Sector_add.csv"
    #     category_sample_csv_path = os.getcwd() + "/pdc_csv_files/Category_Sample.csv"
    #     product_sample_qa_csv_path = os.getcwd() + "/pdc_csv_files/Product_Sample_qa.csv"
    #     product_sample_test_csv_path = os.getcwd() + "/pdc_csv_files/Product_Sample_test.csv"
    #     product_sample_dev_csv_path = os.getcwd() + "/pdc_csv_files/Product_Sample_dev.csv"
    #     specialityattribute_sample_csv_path = os.getcwd() + "/pdc_csv_files/SpecialityAttributes_Sample.csv"
    #     flight_sample_csv_path = os.getcwd() + "/pdc_csv_files/Flight_Sample.csv"
    #     flight_pac_sample_csv_path = os.getcwd() + "/pdc_csv_files/Flight_pac_Sample.csv"
    #     ca_add_catseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/CategorySequences_add.csv"
    #     ca_update_catseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/CategorySequences_update.csv"
    #     ca_invalid_catseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/CategorySequences_invalid.csv"
    #     ca_invalid_pdtseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/ProductSequences_invalid.csv"
    #     ca_add_qa_mapaircat_csv_path = os.getcwd() + "/pdc_csv_files/catalog_product_ac_qaenv.csv"
    #     ca_add_dev_mapaircat_csv_path = os.getcwd() + "/pdc_csv_files/catalog_product_ac_devenv.csv"
    #     ca_add_dev_pdtseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/ProductSequences_devenv.csv"
    #     ca_add_dev_catseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/CategorySequences_dev.csv"
    #     ca_add_qa_pdtseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/ProductSequences_qa_add.csv"
    #     ca_update_qa_pdtseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/ProductSequences_update.csv"
    #     ca_update_dev_pdtseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/ProductSequences_dev_update.csv"
    #     ca_remove_catseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/CategorySequences_empty.csv"
    #     ca_remove_qa_pdtseq_sample_csv_path = os.getcwd() + "/pdc_csv_files/ProductSequences_empty.csv"
    #     airport_sample_csv_path = os.getcwd() + "/pdc_csv_files/Airports_Sample_update.csv"
    #     routegrp_sample_csv_path = os.getcwd() + "/pdc_csv_files/RouteGroup_Sample_update.csv"
    #     ca_inventory_csv_xpath = os.getcwd() + "/pdc_csv_files/QtyUpdate.csv"
    #     mealcode_qa_csv_xpath = os.getcwd() + "/pdc_csv_files/Mealcode_import_qa.csv"
    # "pipeline":
    #     results_folder_path = os.getcwd() + "/test_run_screenshots/"
    #     sector_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Sector_add.csv"
    #     aircraft_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/AirCraft_Sample.csv"
    #     airlinecategory_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Airline_Category_Sample.csv"
    #     catalog_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Catalog_Sample.csv"
    #     category_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Category_Sample.csv"
    #     product_sample_qa_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Product_Sample_qa.csv"
    #     product_sample_test_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Product_Sample_test.csv"
    #     product_sample_dev_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Product_Sample_dev.csv"
    #     specialityattribute_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/SpecialityAttributes_Sample.csv"
    #     flight_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Flight_Sample.csv"
    #     flight_pac_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Flight_pac_Sample.csv"
    #     ca_add_catseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/CategorySequences_add.csv"
    #     ca_update_catseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/CategorySequences_update.csv"
    #     ca_remove_catseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/CategorySequences_empty.csv"
    #     ca_add_qa_mapaircat_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/catalog_product_ac_qaenv.csv"
    #     ca_add_qa_pdtseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/ProductSequences_qa_add.csv"
    #     ca_update_qa_pdtseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/ProductSequences_update.csv"
    #     ca_remove_qa_pdtseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/ProductSequences_empty.csv"
    #     airport_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/Airports_Sample_update.csv"
    #     routegrp_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/RouteGroup_Sample_update.csv"
    #     ca_invalid_catseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/CategorySequences_invalid.csv"
    #     ca_invalid_pdtseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/ProductSequences_invalid.csv"
    #     ca_add_dev_mapaircat_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/catalog_product_ac_devenv.csv"
    #     ca_add_dev_pdtseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/ProductSequences_devenv.csv"
    #     ca_add_dev_catseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/CategorySequences_dev.csv"
    #     ca_update_dev_pdtseq_sample_csv_path = os.getcwd() + "/test_ui/pdc_csv_files/ProductSequences_dev_update.csv"
    #     ca_inventory_csv_xpath = os.getcwd() + "/test_ui/pdc_csv_files/QtyUpdate.csv"
    #     mealcode_qa_csv_xpath = os.getcwd() + "/test_ui/pdc_csv_files/Mealcode_import_qa.csv"





