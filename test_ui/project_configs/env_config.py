def get_environment(environment):
    # below line for manual run
    # environment = input("\nEnter environment (dev/qa/test): ").lower()

    if environment == 'dev':
        return{
            'environment': 'dev',
            'qa_home_page_url' : "https://marketplace-dev.nextcloud.aero/admin/login?perspective=",
            'admin_username' : "admin",
            'admin_password' : "Albert@123",
            'store_xpath' : "//div[contains(text(),'69023141')]",
            'uitemp_created_id_xpath' : "//div[contains(text(),'69189900')]",
            'spa_name' : "69108063 - specialityattribute_1",
            'log_table_xpath' : "//table[contains(@id,'pimcoretreeview-1096-record-')]//child::span[contains(text(),'Log')]//parent::div//div[contains(@class,'expander')]"
            # 'cat_map_value_xpath' : "//li[contains(text(),'69086760')]"
        }
    elif environment == 'local':
        return{
            'environment' : 'local',
            'qa_home_page_url' : "http://localhost/admin",
            'admin_username' : "admin",
            'admin_password' : "admin",
            'store_xpath' : "//div[contains(text(),'37015')]",
            'uitemp_created_id_xpath' : "//div[contains(text(),'216309')]",
            'spa_name' : "37058 - specialityattribute_1",
            'log_table_xpath' : "//table[contains(@id,'pimcoretreeview-1096-record-')]//child::span[contains(text(),'Log')]//parent::div//div[contains(@class,'expander')]"
        }
    elif environment == 'pac':
        return{
            'environment' : 'pac',
            'qa_home_page_url' : "https://pac.marketplace-dev.nextcloud.aero/admin/login",
            'admin_username' : "admin",
            'admin_password' : "admin"
        }
    elif environment == 'qa':
        return{
            'environment' : 'qa',
            'qa_home_page_url' : "https://qa.marketplace-qa.nextcloud.aero/admin/login?perspective=",
            'admin_username' : "admin",
            'admin_password' : "admin@123",
            'store_xpath' : "//div[contains(text(),'37015')]",
            'uitemp_created_id_xpath' : "//div[contains(text(),'216309')]",
            'spa_name' : "37058 - specialityattribute_1",
            'log_table_xpath' : "//table[contains(@id,'pimcoretreeview-1096-record-')]//child::span[contains(text(),'Log')]//parent::div//div[contains(@class,'expander')]"
            # 'cat_map_value_xpath': "//li[contains(text(),'54752')]"
        }
    elif environment == 'test':
        return{
            'environment': 'test',
            'qa_home_page_url' : "https://marketplace-test.nextcloud.aero/admin/login?perspective=",
            'admin_username' : "admin",
            'admin_password' : "Panas0n!c123",
            'store_xpath' : "//div[contains(text(),'86605856')]",
            'spa_name' : "86605994 - specialityattribute_1",
            'uitemp_created_id_xpath' : "//div[contains(text(),'86693322')]",
            'log_table_xpath': "//table[contains(@id,'pimcoretreeview-1096-record-')]//child::span[contains(text(),'Log')]//parent::div//div[contains(@class,'expander')]"
            }
    raise ValueError(f"\nUnknown environment given: {environment}. Enter valid environments - dev/qa/test")