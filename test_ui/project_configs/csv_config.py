import os

class Config:
    _paths = {}
    base_path = "/pdc_csv_files/"
    _mode = None

    @classmethod
    def set_path(cls):
        cls._paths = cls._get_common_paths()

    @classmethod
    def _get_common_paths(cls):
        return {
            "results_folder_path": os.getcwd() + "/screenshots/",
            "airlinecategory_sample_csv_path": os.getcwd() + cls.base_path + "Airline_Category_Sample.csv",
            "aircraft_sample_csv_path": os.getcwd() + cls.base_path + "AirCraft_Sample.csv",
            "catalog_sample_csv_path": os.getcwd() + cls.base_path + "Catalog_Sample.csv",
            "catalog_multiple_sample_csv_path": os.getcwd() + cls.base_path + "Catalog_multiple_Sample.csv",
            "sector_sample_csv_path": os.getcwd() + cls.base_path + "Sector_add.csv",
            "category_sample_csv_path": os.getcwd() + cls.base_path + "Category_Sample.csv",
            "product_sample_dev_csv_path": os.getcwd() + cls.base_path + "Product_Sample_dev.csv",
            "product_sample_qa_csv_path": os.getcwd() + cls.base_path + "Product_Sample_qa.csv",
            "product_sample_test_csv_path": os.getcwd() + cls.base_path + "Product_Sample_test.csv",
            "specialityattribute_sample_csv_path": os.getcwd() + cls.base_path + "SpecialityAttributes_Sample.csv",
            "flight_sample_csv_path": os.getcwd() + cls.base_path + "Flight_Sample.csv",
            "flight_pac_sample_csv_path": os.getcwd() + cls.base_path + "Flight_pac_Sample.csv",
            "ca_add_qa_mapaircat_csv_path": os.getcwd() + cls.base_path + "catalog_product_ac_qaenv.csv",
            "ca_add_dev_mapaircat_csv_path": os.getcwd() + cls.base_path + "catalog_product_ac_devenv.csv",
            "ca_add_test_mapaircat_csv_path": os.getcwd() + cls.base_path + "Catalog_product_ac_testenv.csv",
            "airport_sample_csv_path": os.getcwd() + cls.base_path + "Airports_Sample_update.csv",
            "routegrp_sample_csv_path": os.getcwd() + cls.base_path + "RouteGroup_Sample_update.csv",
            "ca_inventory_csv_xpath": os.getcwd() + cls.base_path + "QtyUpdate.csv",
            "mealcode_dev_csv_xpath": os.getcwd() + cls.base_path + "Mealcode_import_dev.csv",
            "mealcode_qa_csv_xpath": os.getcwd() + cls.base_path + "Mealcode_import_qa.csv",
            "mealcode_test_csv_xpath": os.getcwd() + cls.base_path + "Mealcode_import_test.csv",
            "ca_add_catseq_sample_csv_path": os.getcwd() + cls.base_path + "CategorySequences_add.csv",
            "ca_update_catseq_sample_csv_path": os.getcwd() + cls.base_path + "CategorySequences_update.csv",
            "ca_invalid_catseq_sample_csv_path": os.getcwd() + cls.base_path + "CategorySequences_invalid.csv",
            "ca_remove_catseq_sample_csv_path": os.getcwd() + cls.base_path + "CategorySequences_empty.csv",
            "ca_add_dev_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_dev_add.csv",
            "ca_add_qa_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_qa_add.csv",
            "ca_add_test_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_test_add.csv",
            "ca_update_dev_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_dev_update.csv",
            "ca_update_qa_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_qa_update.csv",
            "ca_update_test_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_test_update.csv",
            "ca_remove_dev_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_dev_empty.csv",
            "ca_remove_qa_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_qa_empty.csv",
            "ca_remove_test_pdtseq_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_test_empty.csv",
            "ca_invalid_pdtseq_dev_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_dev_invalid.csv",
            "ca_invalid_pdtseq_qa_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_qa_invalid.csv",
            "ca_invalid_pdtseq_test_sample_csv_path": os.getcwd() + cls.base_path + "ProductSequences_test_invalid.csv",
            "brand_add_sample_csv_path": os.getcwd() + cls.base_path + "Brand_add_Sample.csv",
            "brand_update_sample_csv_path": os.getcwd() + cls.base_path + "Brand_update_Sample.csv",
            "brand_name_empty_sample_csv_path": os.getcwd() + cls.base_path + "Brand_name_empty_Sample.csv",
            "brand_invalid_image_sample_csv_path": os.getcwd() + cls.base_path + "Brand_invalid_img_Sample.csv",
            "brand_add_multilang_sample_csv_path": os.getcwd() + cls.base_path + "Brand_add_Sample_multilang.csv",
            "brand_update_multilang_sample_csv_path": os.getcwd() + cls.base_path + "Brand_update_Sample_multilang.csv",
            "brand_duplicate_entry_sample_csv_path": os.getcwd() + cls.base_path + "Brand_name_duplicate_Sample.csv",
            "brand_empty_image_sample_csv_path": os.getcwd() + cls.base_path + "Brand_empty_img_Sample.csv",
            "brand_one_image_sample_csv_path": os.getcwd() + cls.base_path + "Brand_one_img_Sample.csv",
            "brand_new_image_singlelang_ratios_sample_csv_path": os.getcwd() + cls.base_path + "Brand_new_images_singlelang_Sample.csv",
            "brand_new_image_multilang_ratios_sample_csv_path": os.getcwd() + cls.base_path + "Brand_new_images_multilang_Sample.csv",
            "product_with_brand_and_without_brand": os.getcwd() + cls.base_path + "Products_with_and_without_brand.csv",
            "product_csv_path_after_name_update": os.getcwd() + cls.base_path + "testfile_Products_with_and_without_brand.csv",
            "csv_with_two_products_with_same_brand": os.getcwd() + cls.base_path + "Products_same_brand.csv",
            "product_csv_path_after_brand_update": os.getcwd() + cls.base_path + "testfile_Products_same_brand.csv",
            "product_with_brand_and_without_brand_with_multi_language": os.getcwd() + cls.base_path + "Products_with_and_without_brand_with_multi_language.csv",
            "product_csv_path_after_name_update_with_multi_language": os.getcwd() + cls.base_path + "testfile_Products_with_and_without_brand_with_multi_language.csv",
            "csv_with_two_products_with_same_brand_with_multi_language": os.getcwd() + cls.base_path + "Products_same_brand_with_multi_language.csv",
            "product_csv_path_after_brand_update_with_multi_language": os.getcwd() + cls.base_path + "testfile_Products_same_brand_with_multi_language.csv",
            "category_sample_csv_path_with_images_zip": os.getcwd() + cls.base_path + "Category_Sample_zip_images.csv",
            "category_multilevel_path": os.getcwd() + cls.base_path + "Category_multilevel_Sample.csv"
        }

    @classmethod
    def get_path(cls, key):
        return cls._paths.get(key)
