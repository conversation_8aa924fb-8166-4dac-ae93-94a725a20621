import os
import logging
from datetime import datetime

def pacMultiLanguage():
    logger.info("pac multi-language ")
    global STORE_USERNAME_MANAGER,STORE_PASSWORD_MANAGER, AIRLINE_USERNAME_MANAGER, AIRLINE_PASSWORD_MANAGER, STORE_CLIENT_ID, STORE_CLIENT_SECRET, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET, TESTING_CATEGORY_ID, STORE_USERNAME, STORE_PASSWORD, AIRLINE_USERNAME, AIRLINE_PASSWORD, STORE_NAME, AIRLINE_NAME, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE, TEST_SECTOR__DEFAULT_CURR_1, TEST_SECTOR__DEFAULT_CURR_2, TEST_SECTOR__DEFAULT_CURR_3, TEST_SECTOR_4_DISPLAY_CURR_1, TEST_SECTOR_4_DISPLAY_CURR_2, TEST_SECTOR_4_DISPLAY_CURR_3, TEST_SECTOR_ALL_DISPLAY_CURR_1, TEST_SECTOR_ALL_DISPLAY_CURR_2, TEST_SECTOR_ALL_DISPLAY_CURR_3, GET_PRODUCT_ID, sector_id, ORIGIN_AIRPORT, airlinecategory_1var, DESTINATION_AIRPORT, LANGUAGES_CODES_CONFIGURED, CATEGORY_PATH, CATEGORY_NAME_VALUE
    STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_pac", "pac")                              ### PAC env ###
    STORE_CLIENT_SECRET = get_env_variable("STORE_CLIENT_SECRET_pac", "pac")
    AIRLINE_CLIENT_ID = get_env_variable("AIRLINE_CLIENT_ID_pac", "pac")         
    AIRLINE_CLIENT_SECRET = get_env_variable("AIRLINE_CLIENT_SECRET_pac", "pac")
    TESTING_CATEGORY_ID = 80
    STORE_USERNAME = '<EMAIL>'
    STORE_PASSWORD = 'Test123456789!!'
    AIRLINE_USERNAME = "<EMAIL>"
    AIRLINE_PASSWORD = "Test123456789!!"
    STORE_USERNAME_MANAGER = '<EMAIL>'
    STORE_PASSWORD_MANAGER = '<EMAIL>'
    AIRLINE_USERNAME_MANAGER = "<EMAIL>"
    AIRLINE_PASSWORD_MANAGER = "<EMAIL>"
    STORE_NAME = "Flipkart"
    AIRLINE_NAME= "Airlines"
    STORE_CATALOG_ASSIGMENT_AUTOAPPROVE = True
    TEST_SECTOR__DEFAULT_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_2=TEST_SECTOR_4_DISPLAY_CURR_3="sec1"
    TEST_SECTOR__DEFAULT_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_1=TEST_SECTOR_ALL_DISPLAY_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_3="sec1"
    TEST_SECTOR__DEFAULT_CURR_3="sec1"
    GET_PRODUCT_ID = 67
    sector_id= 25
    airlinecategory_1var= 70
    ORIGIN_AIRPORT = 'ATQ'
    DESTINATION_AIRPORT = 'ORD'
    CATEGORY_NAME_VALUE='CAT1'
    LANGUAGES_CODES_CONFIGURED = ['en', 'es', 'ja', 'ms', 'zh_Hans', "is", "el", "ru", "pt_BR", "id", "mn", "fil", "de", "it", "fr" ]
    # CATEGORY_PATH = f"//li[contains(text(),'{CATEGORY_NAME_VALUE} ({STORE_NAME}/{CATEGORY_NAME_VALUE})')]"
    CATEGORY_PATH = f"//li[contains(text(),'Singapore Airlines ({STORE_NAME}/{CATEGORY_NAME_VALUE})')]"

def pacDefaultLanguage():
    global STORE_CLIENT_ID, STORE_CLIENT_SECRET, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET, TESTING_CATEGORY_ID, STORE_USERNAME, STORE_PASSWORD, AIRLINE_USERNAME, AIRLINE_PASSWORD, STORE_NAME, AIRLINE_NAME, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE, TEST_SECTOR__DEFAULT_CURR_1, TEST_SECTOR__DEFAULT_CURR_2, TEST_SECTOR__DEFAULT_CURR_3, TEST_SECTOR_4_DISPLAY_CURR_1, TEST_SECTOR_4_DISPLAY_CURR_2, TEST_SECTOR_4_DISPLAY_CURR_3, TEST_SECTOR_ALL_DISPLAY_CURR_1, TEST_SECTOR_ALL_DISPLAY_CURR_2, TEST_SECTOR_ALL_DISPLAY_CURR_3, GET_PRODUCT_ID, sector_id, ORIGIN_AIRPORT, airlinecategory_1var, DESTINATION_AIRPORT, LANGUAGES_CODES_CONFIGURED, CATEGORY_PATH, CATEGORY_NAME_VALUE
    logger.info("pac default language ")
    STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_pac_lang", "pac")                              ### PAC env ###
    STORE_CLIENT_SECRET = get_env_variable("STORE_CLIENT_SECRET_pac_lang", "pac")
    AIRLINE_CLIENT_ID = get_env_variable("AIRLINE_CLIENT_ID_pac_lang", "pac")         
    AIRLINE_CLIENT_SECRET = get_env_variable("AIRLINE_CLIENT_SECRET_pac_lang", "pac")
    TESTING_CATEGORY_ID = 64416
    STORE_USERNAME = '<EMAIL>'
    STORE_PASSWORD = 'Test123456789$'
    AIRLINE_USERNAME = "<EMAIL>"
    AIRLINE_PASSWORD = "Test123456789$"
    STORE_NAME = "PC Store"
    AIRLINE_NAME= "Airmax"
    STORE_CATALOG_ASSIGMENT_AUTOAPPROVE = False
    TEST_SECTOR__DEFAULT_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_2=TEST_SECTOR_4_DISPLAY_CURR_3="LAX-SFO"
    TEST_SECTOR__DEFAULT_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_1=TEST_SECTOR_ALL_DISPLAY_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_3="LAX-SFO"
    TEST_SECTOR__DEFAULT_CURR_3="LAX-SFO"   
    sector_id = 64418
    airlinecategory_1var = 70
    ORIGIN_AIRPORT = 'ATQ'
    DESTINATION_AIRPORT = 'ORD'
    LANGUAGES_CODES_CONFIGURED = ['en']
    CATEGORY_NAME_VALUE = 'Food & Drinks'
    CATEGORY_PATH = f"//li[contains(text(),'{CATEGORY_NAME_VALUE} ({STORE_NAME}/{CATEGORY_NAME_VALUE})')]"

def devMultiLanguage():
    logger.info("dev multi-language ")
    global STORE_USERNAME_MANAGER,STORE_PASSWORD_MANAGER, AIRLINE_USERNAME_MANAGER, STORE_ID, AIRLINE_PASSWORD_MANAGER, STORE_CLIENT_ID, STORE_CLIENT_SECRET, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET, CATEGORY_PATH, TESTING_CATEGORY_ID, STORE_USERNAME, STORE_PASSWORD, AIRLINE_USERNAME, AIRLINE_PASSWORD, STORE_NAME, AIRLINE_NAME, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE, TEST_SECTOR__DEFAULT_CURR_1, TEST_SECTOR__DEFAULT_CURR_2, TEST_SECTOR__DEFAULT_CURR_3, TEST_SECTOR_4_DISPLAY_CURR_1, TEST_SECTOR_4_DISPLAY_CURR_2, TEST_SECTOR_4_DISPLAY_CURR_3, TEST_SECTOR_ALL_DISPLAY_CURR_1, TEST_SECTOR_ALL_DISPLAY_CURR_2, TEST_SECTOR_ALL_DISPLAY_CURR_3, GET_PRODUCT_ID, sector_id, ORIGIN_AIRPORT, airlinecategory_1var, DESTINATION_AIRPORT, LANGUAGES_CODES_CONFIGURED, CATEGORY_NAME_VALUE
    STORE_CLIENT_SECRET = get_env_variable("STORE_CLIENT_SECRET_dev", "dev") 
    STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_dev", "dev")                                #### dev env ### STM
    AIRLINE_CLIENT_ID = get_env_variable("AIRLINE_CLIENT_ID_dev", "dev") 
    AIRLINE_CLIENT_SECRET = get_env_variable("AIRLINE_CLIENT_SECRET_dev", "dev")
    TESTING_CATEGORY_ID = 69066902   #testing store category
    ### UI AUTOMATION
    STORE_USERNAME = '<EMAIL>'
    STORE_PASSWORD = 'Test$1234567'
    AIRLINE_USERNAME = '<EMAIL>'
    AIRLINE_PASSWORD = 'Panasonic123!'
    STORE_USERNAME_MANAGER = '<EMAIL>'
    STORE_PASSWORD_MANAGER = '<EMAIL>'
    AIRLINE_USERNAME_MANAGER = "<EMAIL>"
    AIRLINE_PASSWORD_MANAGER = "<EMAIL>"
    STORE_NAME = "STM store"
    STORE_ID = 10483
    AIRLINE_NAME="DEMO Airline"
    STORE_CATALOG_ASSIGMENT_AUTOAPPROVE = True
    TEST_SECTOR__DEFAULT_CURR_1="LAX-IND"
    TEST_SECTOR__DEFAULT_CURR_2="LAX-JFK"
    TEST_SECTOR__DEFAULT_CURR_3="LAX-NRT"
    TEST_SECTOR_4_DISPLAY_CURR_1="JFK-ATL"
    TEST_SECTOR_4_DISPLAY_CURR_2="JFK-LHR"
    TEST_SECTOR_4_DISPLAY_CURR_3="JFK-NRT"
    TEST_SECTOR_ALL_DISPLAY_CURR_1="LHR-JFK"
    TEST_SECTOR_ALL_DISPLAY_CURR_2="LHR-CUN"
    TEST_SECTOR_ALL_DISPLAY_CURR_3="LHR-NRT"
    GET_PRODUCT_ID = 22464
    sector_id=69066904
    airlinecategory_1var= 69066903
    ORIGIN_AIRPORT = 'LAX'
    DESTINATION_AIRPORT = 'IND'
    LANGUAGES_CODES_CONFIGURED = ['en', 'ja', 'he', "hi", "it" ]
    CATEGORY_NAME_VALUE = 'CAT1'
    CATEGORY_PATH = f"//li[contains(text(),'{CATEGORY_NAME_VALUE} ({STORE_NAME}/{CATEGORY_NAME_VALUE})')]"

def devDefaultLanguage():
    logger.info("dev default language ")
    global STORE_CLIENT_ID, STORE_CLIENT_SECRET, AIRLINE_CLIENT_ID, STORE_ID, AIRLINE_CLIENT_SECRET, CATEGORY_PATH, TESTING_CATEGORY_ID, STORE_USERNAME, STORE_PASSWORD, AIRLINE_USERNAME, AIRLINE_PASSWORD, STORE_NAME, AIRLINE_NAME, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE, TEST_SECTOR__DEFAULT_CURR_1, TEST_SECTOR__DEFAULT_CURR_2, TEST_SECTOR__DEFAULT_CURR_3, TEST_SECTOR_4_DISPLAY_CURR_1, TEST_SECTOR_4_DISPLAY_CURR_2, TEST_SECTOR_4_DISPLAY_CURR_3, TEST_SECTOR_ALL_DISPLAY_CURR_1, TEST_SECTOR_ALL_DISPLAY_CURR_2, TEST_SECTOR_ALL_DISPLAY_CURR_3, GET_PRODUCT_ID, sector_id, ORIGIN_AIRPORT, airlinecategory_1var, DESTINATION_AIRPORT, LANGUAGES_CODES_CONFIGURED, CATEGORY_NAME_VALUE
    logger.info("dev without multi-language ")
    STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_dev_lang", "dev")                              ### LEW ###
    STORE_CLIENT_SECRET = get_env_variable("STORE_CLIENT_SECRET_dev_lang", "dev")
    AIRLINE_CLIENT_ID = get_env_variable("AIRLINE_CLIENT_ID_dev_lang", "dev")         
    AIRLINE_CLIENT_SECRET = get_env_variable("AIRLINE_CLIENT_SECRET_dev_lang", "dev")
    TESTING_CATEGORY_ID = 69251349
    STORE_USERNAME = '<EMAIL>'
    STORE_PASSWORD = 'Test123456789!!'
    AIRLINE_USERNAME = "<EMAIL>"
    AIRLINE_PASSWORD = "Test123456789!!"
    STORE_NAME = "CI Store"
    STORE_ID = 69251324
    AIRLINE_NAME= "CI Airline"
    STORE_CATALOG_ASSIGMENT_AUTOAPPROVE = True
    TEST_SECTOR__DEFAULT_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_2=TEST_SECTOR_4_DISPLAY_CURR_3="LAX-SFO"
    TEST_SECTOR__DEFAULT_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_1=TEST_SECTOR_ALL_DISPLAY_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_3="LAX-SFO"
    TEST_SECTOR__DEFAULT_CURR_3="LAX-SFO"   
    sector_id=    64418
    airlinecategory_1var= 70
    ORIGIN_AIRPORT = 'ATQ'
    DESTINATION_AIRPORT = 'ORD'
    LANGUAGES_CODES_CONFIGURED = ['en']
    CATEGORY_NAME_VALUE = 'Food & Drinks'
    CATEGORY_PATH = f"//li[contains(text(),'Food & Drinks ({STORE_NAME}/{CATEGORY_NAME_VALUE})')]"


def qaMultiLanguage():
    logger.info("qa multi-language ")
    global STORE_USERNAME_MANAGER,STORE_PASSWORD_MANAGER, AIRLINE_USERNAME_MANAGER, AIRLINE_PASSWORD_MANAGER, STORE_CLIENT_ID, STORE_CLIENT_SECRET, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET, TESTING_CATEGORY_ID, STORE_USERNAME, CATEGORY_NAME_VALUE, STORE_PASSWORD, AIRLINE_USERNAME, AIRLINE_PASSWORD, STORE_NAME, AIRLINE_NAME, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE, TEST_SECTOR__DEFAULT_CURR_1, TEST_SECTOR__DEFAULT_CURR_2, TEST_SECTOR__DEFAULT_CURR_3, TEST_SECTOR_4_DISPLAY_CURR_1, TEST_SECTOR_4_DISPLAY_CURR_2, TEST_SECTOR_4_DISPLAY_CURR_3, TEST_SECTOR_ALL_DISPLAY_CURR_1, TEST_SECTOR_ALL_DISPLAY_CURR_2, TEST_SECTOR_ALL_DISPLAY_CURR_3, GET_PRODUCT_ID, sector_id, ORIGIN_AIRPORT, airlinecategory_1var, DESTINATION_AIRPORT, LANGUAGES_CODES_CONFIGURED
    STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_qa", "qa")             #### QA env ### PC Store  
    STORE_CLIENT_SECRET = get_env_variable("STORE_CLIENT_SECRET_qa", "qa")             
    AIRLINE_CLIENT_ID = get_env_variable("AIRLINE_CLIENT_ID_qa", "qa")         #### QA env ### AirMax
    AIRLINE_CLIENT_SECRET = get_env_variable("AIRLINE_CLIENT_SECRET_qa", "qa")
    TESTING_CATEGORY_ID = 34330   #testing category
    ### UI AUTOMATION
    STORE_NAME = "PC Store"
    AIRLINE_NAME="AirMax"
    STORE_USERNAME = '<EMAIL>'
    STORE_PASSWORD = 'asdfASDF1234!@#$'
    AIRLINE_USERNAME = '<EMAIL>'
    AIRLINE_PASSWORD = 'asdfASDF1234$'
    STORE_USERNAME_MANAGER = '<EMAIL>'
    STORE_PASSWORD_MANAGER = '<EMAIL>'
    AIRLINE_USERNAME_MANAGER = "<EMAIL>"
    AIRLINE_PASSWORD_MANAGER = "<EMAIL>"
    STORE_CATALOG_ASSIGMENT_AUTOAPPROVE = True
    TEST_SECTOR__DEFAULT_CURR_1 = TEST_SECTOR__DEFAULT_CURR_2 = TEST_SECTOR__DEFAULT_CURR_3 ="LAX-NRT"
    TEST_SECTOR_4_DISPLAY_CURR_1 = TEST_SECTOR_4_DISPLAY_CURR_2 = TEST_SECTOR_4_DISPLAY_CURR_3 ="JFK-LHR"
    TEST_SECTOR_ALL_DISPLAY_CURR_1 =TEST_SECTOR_ALL_DISPLAY_CURR_2 = TEST_SECTOR_ALL_DISPLAY_CURR_3 ="LHR-NRT" 
    GET_PRODUCT_ID=38067
    sector_id = 41063
    airlinecategory_1var= 41059
    ORIGIN_AIRPORT = 'LAX'
    DESTINATION_AIRPORT = 'NRT'
    LANGUAGES_CODES_CONFIGURED = ['en', 'es', 'ja', "fr"]
    CATEGORY_NAME_VALUE = 'CAT1'

def qaDefaultLanguage():
    logger.info("qa default language ")
    global STORE_CLIENT_ID, STORE_CLIENT_SECRET, AIRLINE_CLIENT_ID, AIRLINE_CLIENT_SECRET, TESTING_CATEGORY_ID, STORE_USERNAME, STORE_PASSWORD, AIRLINE_USERNAME, AIRLINE_PASSWORD, STORE_NAME, AIRLINE_NAME, STORE_CATALOG_ASSIGMENT_AUTOAPPROVE, CATEGORY_NAME_VALUE, TEST_SECTOR__DEFAULT_CURR_1, TEST_SECTOR__DEFAULT_CURR_2, TEST_SECTOR__DEFAULT_CURR_3, TEST_SECTOR_4_DISPLAY_CURR_1, TEST_SECTOR_4_DISPLAY_CURR_2, TEST_SECTOR_4_DISPLAY_CURR_3, TEST_SECTOR_ALL_DISPLAY_CURR_1, TEST_SECTOR_ALL_DISPLAY_CURR_2, TEST_SECTOR_ALL_DISPLAY_CURR_3, GET_PRODUCT_ID, sector_id, ORIGIN_AIRPORT, airlinecategory_1var, DESTINATION_AIRPORT, LANGUAGES_CODES_CONFIGURED
    logger.info("qa without multi-language ")
    STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_qa_lang", "qa")              ### QA CI Store/airline ###
    STORE_CLIENT_SECRET = get_env_variable("STORE_CLIENT_SECRET_qa_lang", "qa")
    AIRLINE_CLIENT_ID = get_env_variable("AIRLINE_CLIENT_ID_qa_lang", "qa")         
    AIRLINE_CLIENT_SECRET = get_env_variable("AIRLINE_CLIENT_SECRET_qa_lang", "qa")
    TESTING_CATEGORY_ID = 47565
    STORE_USERNAME = '<EMAIL>'
    STORE_PASSWORD = 'Test123456789$'
    AIRLINE_USERNAME = "<EMAIL>"
    AIRLINE_PASSWORD = "asdfASDF1234!@#$"
    STORE_NAME = "CI Store"
    AIRLINE_NAME= "CI Airline"
    STORE_CATALOG_ASSIGMENT_AUTOAPPROVE = False
    TEST_SECTOR__DEFAULT_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_2=TEST_SECTOR_4_DISPLAY_CURR_3="LAX-SFO"
    TEST_SECTOR__DEFAULT_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_1=TEST_SECTOR_ALL_DISPLAY_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_3="LAX-SFO"
    TEST_SECTOR__DEFAULT_CURR_3="LAX-SFO"   
    sector_id=    64418
    airlinecategory_1var= 70
    ORIGIN_AIRPORT = 'ATQ'
    DESTINATION_AIRPORT = 'ORD'
    LANGUAGES_CODES_CONFIGURED = ['en']
    CATEGORY_NAME_VALUE = 'Food & Drinks'

def get_env_variable(key, env):
    secret = os.environ.get(key)
    if secret is None:
        raise Exception(f"{key} enviroment variable was not found in {env} enviroment")
    return secret

current_date = datetime.now()
date_string = current_date.strftime('%Y-%m-%d %H:%M:%S')
logger = logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger(__name__)
DOWNLOAD_PATH = os.getenv("SELENIUM_DOWNLOAD_PATH", "")
MULTI_LANGUAGES_DISABLED = os.getenv("MULTI_LANGUAGES_DISABLED", "")
TRIGGERED_JOB = os.getenv("TRIGGERED_JOB", "")  # Assumes TRIGGERED_JOB is in CI environment
CI_COMMIT_BRANCH = os.getenv("CI_COMMIT_BRANCH", "")
logger.info("TRIGGERED_JOB IS: "+ TRIGGERED_JOB)
logger.info("CI_COMMIT_BRANCH IS: "+ CI_COMMIT_BRANCH)
LANGUAGE_MAP = {
    "zh_Hans": "Simplified Chinese",
    "zh_Hant": "Chinese(zh_Hant)",
    "ja": "Japanese",
    "es": "Spanish",
    "fr": "French",
    "de": "German",
    "it": "Italian",
    "pt_BR": "Portuguese (Brazil)",
    "ru": "Russian",
    "fil": "Filipino",
    "el": "Greek",
    "is": "Icelandic",
    "mn": "Mongolian",
    "id": "Indonesian",
    "ms": "Malay",
    "en": "English",
    "ko": "Korean",
    "th": "Thai",
    "vi": "Vietnamese",
    "ar": "Arabic",
    "hi": "Hindi",
    "he": "Hebrew",
    "nl": "Dutch",
    "sw": "Swahili",
    "ta": "Tamil",
    "tr": "Turkish",
    "ur": "Urdu"
}

if CI_COMMIT_BRANCH.lower() == "p_cust_p" or TRIGGERED_JOB.lower() == "pac":
    logger.info("Testing in pre-DEV enviroment")
    JAMA_CYCLE_NAME = "PAC env -"
    base_url_env = "https://pac.marketplace-dev.nextcloud.aero/"
    LOGIN_URL = "https://pac.marketplace-dev.nextcloud.aero/admin/?perspective="
    CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testCatalogQueue-pac"
    FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testFlightQueue-pac"
    INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testInventoryQueue-pac"
    PAC_USER_USERNAME = "admin"
    PAC_USER_PASSWORD = "admin"
    AWS_SNS_CONFIGURE=True
    pacMultiLanguage()
    if MULTI_LANGUAGES_DISABLED:
        pacDefaultLanguage()
        
    
elif TRIGGERED_JOB.lower() == "dev":
    logger.info("Testing in DEV enviroment")
    JAMA_CYCLE_NAME = "DEV -"
    base_url_env = "https://api.marketplace-dev.nextcloud.aero/"
    LOGIN_URL = 'https://marketplace-dev.nextcloud.aero/admin/login?perspective='  ### UI automation using panasky store....
    CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testCatalogQueue-dev"
    FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testFlightQueue-dev"
    INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testInventoryQueue-dev"
    PAC_USER_USERNAME = "admin"
    PAC_USER_PASSWORD = get_env_variable("PAC_USER_PASSWORD", "DEV")
    AWS_SNS_CONFIGURE = True
    devMultiLanguage()
    if MULTI_LANGUAGES_DISABLED:
        devDefaultLanguage()

    
elif TRIGGERED_JOB.lower() == "qa":
    logger.info("Using data from QA enviroment")
    JAMA_CYCLE_NAME = "QA -"
    base_url_env = "https://api.marketplace-qa.nextcloud.aero/"
    LOGIN_URL = 'https://qa.marketplace-qa.nextcloud.aero/admin/login?perspective='  ### UI automation using panasky store....
    CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testCatalogQueue-qa"
    FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testFlightQueue-qa"
    INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testInventoryQueue-qa"
    PAC_USER_USERNAME = "admin"
    PAC_USER_PASSWORD = get_env_variable("PAC_USER_PASSWORD_QA", "QA")
    AWS_SNS_CONFIGURE=True
    qaMultiLanguage()
    if MULTI_LANGUAGES_DISABLED:
        qaDefaultLanguage()

elif TRIGGERED_JOB.lower() == "test":
    raise Exception("Please enter test credentials")

else:
    # logger.info("There wasn't a ENV_TEST variable data entered. Using data from LOCAL enviroment")
    # CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testCatalogQueue-pac"
    # FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testFlightQueue-pac"
    # INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testInventoryQueue-pac"

    # STORE_CLIENT_ID = 'bxdodc0jq2'                                ### local gitenv ###
    # STORE_CLIENT_SECRET = "kwmpslafvbsd-mm09qvf"
    # AIRLINE_CLIENT_ID = '1mt3q3nrep'              
    # AIRLINE_CLIENT_SECRET = get_env_variable("AIRLINE_CLIENT_SECRET", "LOCAL")
    # base_url_env = "http://127.0.0.1/"
    # TESTING_CATEGORY_ID = 80
    # JAMA_CYCLE_NAME = "local env -"
    # LOGIN_URL = "http://localhost/admin/"
    # STORE_USERNAME = '<EMAIL>'
    # STORE_PASSWORD = 'Test123456789$'
    # AIRLINE_USERNAME = "<EMAIL>"
    # AIRLINE_PASSWORD = "Test123456789$"
    # STORE_USERNAME_MANAGER = '<EMAIL>'
    # STORE_PASSWORD_MANAGER = '<EMAIL>'
    # AIRLINE_USERNAME_MANAGER = "<EMAIL>"
    # AIRLINE_PASSWORD_MANAGER = "<EMAIL>"
    # STORE_NAME = "Flipkart"
    # AIRLINE_NAME="Singapore Airlines"
    # STORE_CATALOG_ASSIGMENT_AUTOAPPROVE = False
    # PAC_USER_USERNAME = "admin"
    # PAC_USER_PASSWORD = "admin"
    # TEST_SECTOR__DEFAULT_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_1=TEST_SECTOR_4_DISPLAY_CURR_2=TEST_SECTOR_4_DISPLAY_CURR_3="sec1"
    # TEST_SECTOR__DEFAULT_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_1=TEST_SECTOR_ALL_DISPLAY_CURR_2=TEST_SECTOR_ALL_DISPLAY_CURR_3="sec1"
    # TEST_SECTOR__DEFAULT_CURR_3="sec1"
    
    # logger.info("TESTING IN PAC MANUAL")
    # base_url_env = "https://pac.marketplace-dev.nextcloud.aero/"
    # LOGIN_URL = "https://pac.marketplace-dev.nextcloud.aero/admin/?perspective="
    # CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testCatalogQueue-pac"
    # FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testFlightQueue-pac"
    # INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testInventoryQueue-pac"
    # PAC_USER_USERNAME = "admin"
    # PAC_USER_PASSWORD = "admin"
    # AWS_SNS_CONFIGURE=True
    # #pacMultiLanguage()
    # pacDefaultLanguage()
    
    
    
    logger.info("TESTING IN DEV MANUAL")
    JAMA_CYCLE_NAME = "DEV -"
    base_url_env = "https://api.marketplace-dev.nextcloud.aero/"
    LOGIN_URL = 'https://marketplace-dev.nextcloud.aero/admin/login?perspective='  ### UI automation using panasky store....
    CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testCatalogQueue-dev"
    FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testFlightQueue-dev"
    INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/924166462419/testInventoryQueue-dev"
    PAC_USER_USERNAME = "admin"
    PAC_USER_PASSWORD = get_env_variable("PAC_USER_PASSWORD", "DEV")
    STORE_CLIENT_ID = get_env_variable("STORE_CLIENT_ID_dev", "dev")                                #### dev env ### STM
    AWS_SNS_CONFIGURE = True
    # #devMultiLanguage()
    devDefaultLanguage()
    
    
    # logger.info("TESTING IN QA MANUAL")
    # JAMA_CYCLE_NAME = "QA -"
    # LOGIN_URL = 'https://qa.marketplace-qa.nextcloud.aero/admin/login?perspective='  ### UI automation using panasky store....
    # AWS_SNS_CONFIGURE=True
    # CATALOG_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testCatalogQueue-qa"
    # FLIGHT_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testFlightQueue-qa"
    # INVENTORY_SQS_QUEUE_URL = f"https://sqs.us-west-2.amazonaws.com/009093030873/testInventoryQueue-qa"
    # PAC_USER_USERNAME = "admin"
    # qaMultiLanguage()
    # # qaDefaultLanguage()

