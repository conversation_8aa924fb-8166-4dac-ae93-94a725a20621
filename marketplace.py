import os
import json
import sys
import datetime

from jamatest import jamatest

# Check if command-line arguments are provided
if len(sys.argv) < 2:
    print("Report file path is not provided.")
    sys.exit(1)
    
report_file = sys.argv[1]

# Read the JSON data from the report file
with open(report_file) as f:
    data = json.load(f)

# Determine the cycle name based on the triggered job
triggered_job = os.getenv("TRIGGERED_JOB", "")  # Assumes TRIGGERED_JOB is in CI environment

# Customize the cycle name based on the triggered job
if triggered_job == "dev":
    cycle_name = f'marketplace-ground-dev'
elif triggered_job == "qa":
    cycle_name = f'marketplace-ground-qa'
elif triggered_job.lower() == "pac":
  cycle_name = f'marketplace-ground-pre-dev'
else:
    cycle_name = 'TEST'

CLIENT_ID = os.getenv("JAMA_CLIENT_ID")
CLIENT_SECRET = os.getenv("JAMA_CLIENT_SECRET")

jamatest.init(CLIENT_ID, CLIENT_SECRET, '17050505', cycle_name=cycle_name)

#finding jason id along with its jama id 
dict_jason_jama={}
for dict_keys in data.keys():
  if dict_keys == "collection":
    for item_keys in data[dict_keys]["item"]:
      
      for cat_keys in item_keys.keys():
        
          
          if cat_keys =="item" and "item" in item_keys[cat_keys][0].keys() :
            for k in range (len(item_keys[cat_keys])):
              if "item" in item_keys[cat_keys][k]:
                for sub_keys  in item_keys[cat_keys][k]["item"]:
                  id =sub_keys["id"]
                  name=sub_keys["name"]
                  dict_jason_jama[id]=name
          
              
          elif cat_keys =="item":
            id = item_keys[cat_keys][0]["id"]
            name= item_keys[cat_keys][0]["name"]
            dict_jason_jama[id]=name
            

print(len(dict_jason_jama))



#link jama ids with the test case reults
result = {}
stack_message_dict = {}
for values in data["run"]["executions"]:
    if "assertions" in values and len(values["assertions"]) > 0:
        if "error" in values["assertions"][0].keys():
            if values["id"] in dict_jason_jama.keys() and dict_jason_jama[values["id"]] not in result.keys():
                result[dict_jason_jama[values["id"]]] = "test case failed"
                if "JAMA" in dict_jason_jama[values["id"]]:
                    jama_id = dict_jason_jama[values["id"]].split("_", 2)[1]
                else:
                    continue
                stack_message = values["assertions"][0]["error"]["stack"]
                stack_message_dict[jama_id] = stack_message
        else:
            if values["id"] in dict_jason_jama.keys() and dict_jason_jama[values["id"]] not in result.keys():
                result[dict_jason_jama[values["id"]]] = "test case passed"
# print(result)

# counting how many test cases failed and how many are passed ad print hte overall result
failed=0
passed=0

for keys in result.keys():
  if result[keys] =="test case failed":
    failed+=1

  else:
    passed+=1

print("total results ",len(result))
print("No of test case failed " ,failed)
print("No of test case passed ",passed)

#percentage of testcases passed
print("percentage of test case passed ", passed / (passed + failed) * 100)

#percentage of testcases failed
print("percentage of test case failed ", failed / (passed + failed) * 100)

for keys in result.keys():
  if "JAMA" in keys:
    jama_id=keys.split("_",2)[1]
  else:
    continue

    #integrating with jama
  @jamatest.test(int(jama_id))
  def foo():
    print("test case ",jama_id)
    if  result[keys]=="test case passed":
      print("test case passed ",jama_id)
    
    else:
      # print("Test case failed for ",jama_id,end=" ")
      print(stack_message_dict[jama_id])
      raise Exception(stack_message_dict[jama_id])
  try:
    foo()
  except Exception as e:
    print(str(e))

jamatest.publish()
