{"id": "751a6b93-cca7-40ae-914c-148ebd56db74", "name": "PAC_DEV_Postman_API_environment_OFF", "values": [{"key": "apiUrl", "value": "https://api.marketplace-dev.nextcloud.aero", "type": "default", "enabled": true}, {"key": "airlineClientId", "value": "a0dfqz0xfl", "type": "default", "enabled": true}, {"key": "airlineClientSecret", "value": "fd$2&du6r1jf4g3#3e$8", "type": "default", "enabled": true}, {"key": "storeClientId", "value": "h7cc8hq2el", "type": "default", "enabled": true}, {"key": "storeClientSecret", "value": "$2lc00mxzvqlkl43s&a-", "type": "default", "enabled": true}, {"key": "airlineToken", "value": "", "type": "default", "enabled": true}, {"key": "storeToken", "value": "", "type": "default", "enabled": true}, {"key": "mytwitterID", "value": "", "type": "any", "enabled": true}, {"key": "testid", "value": "", "type": "any", "enabled": true}, {"key": "id", "value": "", "type": "any", "enabled": true}, {"key": "sector_id", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "sector_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "flight_id", "value": "", "type": "any", "enabled": true}, {"key": "flight_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_id", "value": "", "type": "any", "enabled": true}, {"key": "product_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "fulfillment_id", "value": "", "type": "any", "enabled": true}, {"key": "fulfillment_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "delivery_rule_id", "value": "", "type": "any", "enabled": true}, {"key": "delivery_rule_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "category_id", "value": "", "type": "any", "enabled": true}, {"key": "category_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_type", "value": "", "type": "any", "enabled": true}, {"key": "reservation_start_at ", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end_1 ", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_sku1", "value": "", "type": "any", "enabled": true}, {"key": "product_sku2", "value": "", "type": "any", "enabled": true}, {"key": "campaign_id", "value": "", "type": "any", "enabled": true}, {"key": "campaign_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "campaign_type", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku1", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku2", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method", "value": "", "type": "any", "enabled": true}, {"key": "promotion_type_id", "value": "", "type": "any", "enabled": true}, {"key": "promotion_type_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "promotion_id", "value": "", "type": "any", "enabled": true}, {"key": "promotion_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method1", "value": "", "type": "any", "enabled": true}, {"key": "airline_id", "value": "", "type": "any", "enabled": true}, {"key": "future-date", "value": "", "type": "any", "enabled": true}, {"key": "future_date", "value": "", "type": "any", "enabled": true}, {"key": "future_date1", "value": "", "type": "any", "enabled": true}, {"key": "route_group_name", "value": "", "type": "any", "enabled": true}, {"key": "product_id_exclude", "value": "", "type": "any", "enabled": true}, {"key": "sector_id_exclude", "value": "", "type": "any", "enabled": true}, {"key": "cabinclass_id", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id_get", "value": "", "type": "any", "enabled": true}, {"key": "delivery_type", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_1", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get1", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_delta", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id_delta", "value": "", "type": "any", "enabled": true}, {"key": "delta_product", "value": "", "type": "any", "enabled": true}, {"key": "sku_delta_product", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get2", "value": "", "type": "any", "enabled": true}, {"key": "product_id_from_catalog", "value": "", "type": "any", "enabled": true}, {"key": "count", "value": "", "type": "default", "enabled": true}, {"key": "destination_var", "value": "SCA", "type": "default", "enabled": true}, {"key": "origin_var", "value": "OMAA", "type": "default", "enabled": true}, {"key": "flightBeginDateTime", "value": "2025-11-05 21:00", "type": "default", "enabled": true}, {"key": "flightEndDateTime", "value": "2026-12-06 17:35", "type": "default", "enabled": true}, {"key": "uitemplate_var", "value": "demoairlineuitemp", "type": "default", "enabled": true}, {"key": "roottype_var", "value": "demoairroottype", "type": "default", "enabled": true}, {"key": "bannerimage_var", "value": "https://placehold.jp/3160x632.png", "type": "default", "enabled": true}, {"key": "backgroundimage_var", "value": "https://placehold.jp/3840x2160.png", "type": "default", "enabled": true}, {"key": "storeid_var", "value": "270", "type": "default", "enabled": true}, {"key": "inventorycheckicao1_var", "value": "OMAM", "type": "default", "enabled": true}, {"key": "inventorycheckicao2_var", "value": "WSSS", "type": "default", "enabled": true}, {"key": "storelocationicao_var", "value": "LAX", "type": "default", "enabled": true}, {"key": "specialityimage_var", "value": "https://placehold.jp/256x256.png", "type": "default", "enabled": true}, {"key": "campaigntype_var", "value": "Category", "type": "default", "enabled": true}, {"key": "orderairlineicao1_var", "value": "111111", "type": "default", "enabled": true}, {"key": "orderpacvariantid_var", "value": "11463", "type": "default", "enabled": true}, {"key": "orderretailercode_var", "value": "270", "type": "default", "enabled": true}, {"key": "orderfulfillmenttype_var", "value": "inHouse", "type": "default", "enabled": true}, {"key": "orderassociatestoreid_var", "value": "270", "type": "default", "enabled": true}, {"key": "orderid_var", "value": "73715", "type": "default", "enabled": true}, {"key": "ordershipmentid_var", "value": "73717", "type": "default", "enabled": true}, {"key": "deliveryruletype_var", "value": "shipping", "type": "default", "enabled": true}, {"key": "shipmentgroup_var", "value": "fulfillment_type", "type": "default", "enabled": true}, {"key": "shippingDestination_var", "value": "domestic", "type": "default", "enabled": true}, {"key": "deliverytype_var", "value": "homeShippingDomesticStandard", "type": "default", "enabled": true}, {"key": "productnewfrom_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "productnewto_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "catalog_var", "value": "69691", "type": "default", "enabled": true}, {"key": "productmealcode_var", "value": "qa_mealcode", "type": "default", "enabled": true}, {"key": "product1_var", "value": "68236", "type": "default", "enabled": true}, {"key": "product2_var", "value": "68239", "type": "default", "enabled": true}, {"key": "product3_var", "value": "68253", "type": "default", "enabled": true}, {"key": "product4_var", "value": "68255", "type": "default", "enabled": true}, {"key": "product5_var", "value": "68240", "type": "default", "enabled": true}, {"key": "product6_var", "value": "68242", "type": "default", "enabled": true}, {"key": "product7_var", "value": "68256", "type": "default", "enabled": true}, {"key": "product8_var", "value": "68257", "type": "default", "enabled": true}, {"key": "airlinecategory_1var", "value": "68224", "type": "default", "enabled": true}, {"key": "airlinecategory_2var", "value": "68232", "type": "default", "enabled": true}, {"key": "flight_var", "value": "1314", "type": "default", "enabled": true}, {"key": "sector_var", "value": "10668", "type": "default", "enabled": true}, {"key": "catalogfromdate_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "catalogtodate_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "cabinclass_var", "value": "194", "type": "default", "enabled": true}, {"key": "category_var", "value": "68216", "type": "default", "enabled": true}, {"key": "arrivalorderairportiata_var", "value": "DXB", "type": "default", "enabled": true}, {"key": "departureorderairportiata_var", "value": "BOB", "type": "default", "enabled": true}, {"key": "orderitemid_var", "value": "73719", "type": "default", "enabled": true}, {"key": "sector_id_2", "value": "", "type": "any", "enabled": true}, {"key": "CAflightid_var", "value": "69001391", "type": "default", "enabled": true}, {"key": "CAsectorid_var", "value": "69001374", "type": "default", "enabled": true}, {"key": "CAroutegroupid_var", "value": "19291", "type": "default", "enabled": true}, {"key": "CAincludedsectorid_var", "value": "69001366", "type": "default", "enabled": true}, {"key": "sector_id_1", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id_1", "value": "", "type": "any", "enabled": true}, {"key": "JWT_TOKEN_DEV", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiIxMTExMTEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNzA5MjA1Njc3LCJleHAiOjI1MzY0MzE0MDB9.tNEHK-igiBQZSvMUOihJ7l-3ypJHOZ2I6UIALyJs954", "type": "default", "enabled": true}, {"key": "image1_1", "value": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA", "type": "default", "enabled": true}, {"key": "image2_1", "value": "https://fastly.picsum.photos/id/1/1480/740.jpg?hmac=9H7izvZT7_JeVx6iDGGFTzbHdr5xLC3RbGvz75R_bjU", "type": "default", "enabled": true}, {"key": "image4_3", "value": "https://fastly.picsum.photos/id/1/760/570.jpg?hmac=R_Y34CQ1kbkt1vW54-BFc2oQZrTDpD80w-SSD3hb4Xw", "type": "default", "enabled": true}, {"key": "image5_6", "value": "https://fastly.picsum.photos/id/1/665/798.jpg?hmac=E7CMCI14bkK0UElhCYadh4GEQnQfoh1INct8z_TmWBE", "type": "default", "enabled": true}, {"key": "image2_3", "value": "https://fastly.picsum.photos/id/1/944/1416.jpg?hmac=0pk64dMmzosfKEgQ-UwNhbVZCeYcPqQbGBz7gi4PlOM", "type": "default", "enabled": true}, {"key": "image5_6_prod", "value": "https://fastly.picsum.photos/id/1/670/804.jpg?hmac=yID1nnXEygTnOk94LZ8-MRj8JFJRQqIzop_FY8CIA3U", "type": "default", "enabled": true}, {"key": "sku", "value": "", "type": "any", "enabled": true}, {"key": "variable_key", "value": "", "type": "any", "enabled": true}, {"key": "randomSkuFirstName", "value": "", "type": "any", "enabled": true}, {"key": "randomSkuInt", "value": "", "type": "any", "enabled": true}, {"key": "existingProductSku", "value": "", "type": "default", "enabled": true}, {"key": "Store_Name_OFF", "value": "<PERSON><PERSON><PERSON><PERSON>", "type": "default", "enabled": true}, {"key": "Airline_Name_OFF", "value": "DEMO_Airline", "type": "default", "enabled": true}, {"key": "Product_ID_01", "value": "", "type": "any", "enabled": true}, {"key": "currentId", "value": "", "type": "any", "enabled": true}, {"key": "currency", "value": "", "type": "any", "enabled": true}, {"key": "currentRetry", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_01", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_02", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_02", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_03", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_03", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_04", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_04", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_05", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_05", "value": "", "type": "any", "enabled": true}, {"key": "MultiProduct_Catalog_ID", "value": "", "type": "any", "enabled": true}, {"key": "ui_Template_AC", "value": "", "type": "any", "enabled": true}, {"key": "rootType_AC", "value": "", "type": "any", "enabled": true}, {"key": "UI Template", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level1", "value": "", "type": "any", "enabled": true}, {"key": "orderid", "value": "", "type": "any", "enabled": true}, {"key": "shipmentid", "value": "", "type": "any", "enabled": true}, {"key": "lineitemid", "value": "", "type": "any", "enabled": true}, {"key": "image1200_1", "value": "", "type": "any", "enabled": true}, {"key": "store_Catalog_id", "value": "", "type": "any", "enabled": true}, {"key": "secondProductId", "value": "", "type": "any", "enabled": true}, {"key": "productid_exclude", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level2", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level3", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level4", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level5", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level6", "value": "", "type": "any", "enabled": true}, {"key": "polling_start_time", "value": "", "type": "any", "enabled": true}, {"key": "uniqueString", "value": "", "type": "any", "enabled": true}, {"key": "categoryid_new", "value": "", "type": "any", "enabled": true}, {"key": "product_id1", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_1", "value": "", "type": "any", "enabled": true}, {"key": "product_id1_2", "value": "", "type": "any", "enabled": true}, {"key": "route_group_Id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_Id", "value": "", "type": "any", "enabled": true}, {"key": "sectorid", "value": "", "type": "any", "enabled": true}, {"key": "sectorid_new", "value": "", "type": "any", "enabled": true}, {"key": "routeGroup", "value": "", "type": "any", "enabled": true}, {"key": "sectorId", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_1", "value": "", "type": "any", "enabled": true}, {"key": "postPayload", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_1_2", "value": "", "type": "any", "enabled": true}, {"key": "product_id1_3", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_1_3", "value": "", "type": "any", "enabled": true}, {"key": "product_id1_4", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_1_4", "value": "", "type": "any", "enabled": true}, {"key": "product_id2", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_2", "value": "", "type": "any", "enabled": true}, {"key": "product_id2_2", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_2_2", "value": "", "type": "any", "enabled": true}, {"key": "product_id2_3", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_2_3", "value": "", "type": "any", "enabled": true}, {"key": "product_id2_4", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_2_4", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_parent2", "value": "", "type": "any", "enabled": true}, {"key": "flightId", "value": "", "type": "any", "enabled": true}, {"key": "productID_01", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id22", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id_2", "value": "", "type": "any", "enabled": true}, {"key": "errorBypassed", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-03-05T11:14:38.143Z", "_postman_exported_using": "Postman/11.34.5"}