{"id": "242f3ac6-606d-42ea-9319-671f9d84f6d6", "name": "PAC PAC - SIN/Flip", "values": [{"key": "apiUrl", "value": "https://pac.marketplace-dev.nextcloud.aero", "type": "default", "enabled": true}, {"key": "airlineClientId", "value": "1mt3q3nrep", "type": "default", "enabled": true}, {"key": "airlineClientSecret", "value": "e0nu-cqz0ciglv45bztr", "type": "default", "enabled": true}, {"key": "storeClientId", "value": "bxdodc0jq2", "type": "default", "enabled": true}, {"key": "storeClientSecret", "value": "kwmpslafvbsd-mm09qvf", "type": "default", "enabled": true}, {"key": "airlineToken", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJTSUEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNzE5OTY0OTEyLCJleHAiOjI1MzY0MzE0MDB9.M5OcffQlJaeIx9fxL6ZhI7r7H-4eW479YBYGcJjTJAY", "type": "default", "enabled": true}, {"key": "storeToken", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJTSUEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNzE5OTY0OTEyLCJleHAiOjI1MzY0MzE0MDB9.M5OcffQlJaeIx9fxL6ZhI7r7H-4eW479YBYGcJjTJAY", "type": "default", "enabled": true}, {"key": "category_var", "value": "59", "type": "default", "enabled": true}, {"key": "productcategory1", "value": "60", "type": "default", "enabled": true}, {"key": "productcategory2", "value": "1369", "type": "default", "enabled": true}, {"key": "airlinecategory_1var", "value": "61", "type": "default", "enabled": true}, {"key": "airlinecategory_2var", "value": "1370", "type": "default", "enabled": true}, {"key": "store_id", "value": "31", "type": "any", "enabled": true}, {"key": "orderairlineicao1_var", "value": "SIA", "type": "default", "enabled": true}, {"key": "JWT_TOKEN_DEV", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJTSUEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNzIwODE3MDI2LCJleHAiOjI1MzY0MzE0MDB9.kIipu96_eOTCly9_SIg0SuhsXmsrXC12th2JlC4oRLs", "type": "default", "enabled": true}, {"key": "catalog_assign_id_delta", "value": "55", "type": "any", "enabled": true}, {"key": "/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/", "value": "\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\\n", "type": "any", "enabled": true}, {"key": "SETUP VARIABLES ABOVE THIS LINE", "value": "SETUP VARIABLES ABOVE THIS LINE", "type": "any", "enabled": true}, {"key": "", "value": "", "type": "default", "enabled": true}, {"key": "", "value": "", "type": "default", "enabled": true}, {"key": "id", "value": "", "type": "any", "enabled": true}, {"key": "sector_id", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id", "value": 69046670, "type": "any", "enabled": true}, {"key": "route_group_id_delete", "value": 69040116, "type": "any", "enabled": true}, {"key": "sector_id_delete", "value": 69040130, "type": "any", "enabled": true}, {"key": "flight_id", "value": 69046675, "type": "any", "enabled": true}, {"key": "flight_id_delete", "value": 69046692, "type": "any", "enabled": true}, {"key": "airline_category_id", "value": 66645, "type": "any", "enabled": true}, {"key": "airline_category_id_delete", "value": 69040551, "type": "any", "enabled": true}, {"key": "product_id", "value": 69046679, "type": "any", "enabled": true}, {"key": "product_id_delete", "value": 69040141, "type": "any", "enabled": true}, {"key": "catalog_assign_id", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id", "value": 69046712, "type": "any", "enabled": true}, {"key": "store_location_id_delete", "value": 69040114, "type": "any", "enabled": true}, {"key": "fulfillment_id", "value": 154, "type": "any", "enabled": true}, {"key": "fulfillment_id_delete", "value": 157, "type": "any", "enabled": true}, {"key": "delivery_rule_id", "value": 69046711, "type": "any", "enabled": true}, {"key": "delivery_rule_id_delete", "value": 69046711, "type": "any", "enabled": true}, {"key": "category_id", "value": 69040126, "type": "any", "enabled": true}, {"key": "category_id_delete", "value": 69040126, "type": "any", "enabled": true}, {"key": "product_type", "value": "Duty Free", "type": "any", "enabled": true}, {"key": "reservation_start_at ", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end_1 ", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id", "value": 69046701, "type": "any", "enabled": true}, {"key": "catalog_id_delete", "value": 69040152, "type": "any", "enabled": true}, {"key": "product_sku1", "value": "productElfriedaxxc1-VG", "type": "any", "enabled": true}, {"key": "product_sku2", "value": "productJason03897604", "type": "any", "enabled": true}, {"key": "campaign_id", "value": "", "type": "any", "enabled": true}, {"key": "campaign_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "campaign_type", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku1", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku2", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method", "value": "Onboard", "type": "any", "enabled": true}, {"key": "promotion_type_id", "value": 390, "type": "any", "enabled": true}, {"key": "promotion_type_id_delete", "value": 390, "type": "any", "enabled": true}, {"key": "promotion_id", "value": 69046683, "type": "any", "enabled": true}, {"key": "promotion_id_delete", "value": 69040159, "type": "any", "enabled": true}, {"key": "delivery_method1", "value": null, "type": "any", "enabled": true}, {"key": "airline_id", "value": 69040095, "type": "any", "enabled": true}, {"key": "future-date", "value": "", "type": "any", "enabled": true}, {"key": "future_date", "value": "2024-04-10", "type": "any", "enabled": true}, {"key": "future_date1", "value": "2024-04-12", "type": "any", "enabled": true}, {"key": "route_group_name", "value": "RG01Hermannhard drive", "type": "any", "enabled": true}, {"key": "product_id_exclude", "value": "69040150", "type": "any", "enabled": true}, {"key": "sector_id_exclude", "value": 69040120, "type": "any", "enabled": true}, {"key": "cabinclass_id", "value": 192, "type": "any", "enabled": true}, {"key": "speciality_attribute_id_delete", "value": 69046682, "type": "any", "enabled": true}, {"key": "speciality_attribute_id", "value": 69046682, "type": "any", "enabled": true}, {"key": "catalog_assign_id_get", "value": 69046710, "type": "any", "enabled": true}, {"key": "route_group_id_get", "value": 69040117, "type": "any", "enabled": true}, {"key": "delivery_type", "value": "homeShippingDomesticStandard", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_1", "value": 69046682, "type": "any", "enabled": true}, {"key": "catalog_assign_id_get1", "value": 69046795, "type": "any", "enabled": true}, {"key": "catalog_id_delta", "value": "", "type": "any", "enabled": true}, {"key": "delta_product", "value": "", "type": "any", "enabled": true}, {"key": "sku_delta_product", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get2", "value": "48366", "type": "any", "enabled": true}, {"key": "product_id_from_catalog", "value": 69046680, "type": "any", "enabled": true}, {"key": "count", "value": "NaN", "type": "default", "enabled": true}, {"key": "destination_var", "value": "SCA", "type": "default", "enabled": true}, {"key": "origin_var", "value": "OMAA", "type": "default", "enabled": true}, {"key": "flightBeginDateTime", "value": "2024-04-10 14:28", "type": "default", "enabled": true}, {"key": "flightEndDateTime", "value": "2024-04-16 14:28", "type": "default", "enabled": true}, {"key": "uitemplate_var", "value": "null", "type": "default", "enabled": true}, {"key": "roottype_var", "value": "null", "type": "default", "enabled": true}, {"key": "bannerimage_var", "value": "https://placehold.jp/3160x632.png", "type": "default", "enabled": true}, {"key": "backgroundimage_var", "value": "https://placehold.jp/3840x2160.png", "type": "default", "enabled": true}, {"key": "storeid_var", "value": "270", "type": "default", "enabled": true}, {"key": "inventorycheckicao1_var", "value": "OMAM", "type": "default", "enabled": true}, {"key": "inventorycheckicao2_var", "value": "WSSS", "type": "default", "enabled": true}, {"key": "storelocationicao_var", "value": "LAX", "type": "default", "enabled": true}, {"key": "specialityimage_var", "value": "https://placehold.jp/256x256.png", "type": "default", "enabled": true}, {"key": "campaigntype_var", "value": "Category", "type": "default", "enabled": true}, {"key": "orderpacvariantid_var", "value": "11463", "type": "default", "enabled": true}, {"key": "orderretailercode_var", "value": "270", "type": "default", "enabled": true}, {"key": "orderfulfillmenttype_var", "value": "inHouse", "type": "default", "enabled": true}, {"key": "orderassociatestoreid_var", "value": "270", "type": "default", "enabled": true}, {"key": "orderid_var", "value": "73715", "type": "default", "enabled": true}, {"key": "ordershipmentid_var", "value": "73717", "type": "default", "enabled": true}, {"key": "deliveryruletype_var", "value": "shipping", "type": "default", "enabled": true}, {"key": "shipmentgroup_var", "value": "fulfillment_type", "type": "default", "enabled": true}, {"key": "shippingDestination_var", "value": "domestic", "type": "default", "enabled": true}, {"key": "deliverytype_var", "value": "homeShippingDomesticStandard", "type": "default", "enabled": true}, {"key": "productnewfrom_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "productnewto_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "catalog_var", "value": "69691", "type": "default", "enabled": true}, {"key": "productmealcode_var", "value": "qa_mealcode", "type": "default", "enabled": true}, {"key": "product1_var", "value": 69046693, "type": "default", "enabled": true}, {"key": "product2_var", "value": 69046694, "type": "default", "enabled": true}, {"key": "product3_var", "value": 69046695, "type": "default", "enabled": true}, {"key": "product4_var", "value": 69046696, "type": "default", "enabled": true}, {"key": "product5_var", "value": 69046697, "type": "default", "enabled": true}, {"key": "product6_var", "value": 69046698, "type": "default", "enabled": true}, {"key": "product7_var", "value": 69046699, "type": "default", "enabled": true}, {"key": "product8_var", "value": 69046700, "type": "default", "enabled": true}, {"key": "flight_var", "value": "1314", "type": "default", "enabled": true}, {"key": "sector_var", "value": "10668", "type": "default", "enabled": true}, {"key": "catalogfromdate_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "catalogtodate_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "cabinclass_var", "value": "194", "type": "default", "enabled": true}, {"key": "arrivalorderairportiata_var", "value": "DXB", "type": "default", "enabled": true}, {"key": "departureorderairportiata_var", "value": "BOB", "type": "default", "enabled": true}, {"key": "orderitemid_var", "value": "73719", "type": "default", "enabled": true}, {"key": "sector_id_2", "value": 69046674, "type": "any", "enabled": true}, {"key": "CAflightid_var", "value": "69001391", "type": "default", "enabled": true}, {"key": "CAsectorid_var", "value": "69001374", "type": "default", "enabled": true}, {"key": "CAroutegroupid_var", "value": "19293", "type": "default", "enabled": true}, {"key": "CAincludedsectorid_var", "value": "69001366", "type": "default", "enabled": true}, {"key": "sector_id_1", "value": 69046672, "type": "any", "enabled": true}, {"key": "airline_category_id_1", "value": 69040551, "type": "any", "enabled": true}, {"key": "image1_1", "value": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA", "type": "default", "enabled": true}, {"key": "image2_1", "value": "https://fastly.picsum.photos/id/1/1480/740.jpg?hmac=9H7izvZT7_JeVx6iDGGFTzbHdr5xLC3RbGvz75R_bjU", "type": "default", "enabled": true}, {"key": "image4_3", "value": "https://fastly.picsum.photos/id/1/760/570.jpg?hmac=R_Y34CQ1kbkt1vW54-BFc2oQZrTDpD80w-SSD3hb4Xw", "type": "default", "enabled": true}, {"key": "image5_6", "value": "https://fastly.picsum.photos/id/1/665/798.jpg?hmac=E7CMCI14bkK0UElhCYadh4GEQnQfoh1INct8z_TmWBE", "type": "default", "enabled": true}, {"key": "image2_3", "value": "https://fastly.picsum.photos/id/1/944/1416.jpg?hmac=0pk64dMmzosfKEgQ-UwNhbVZCeYcPqQbGBz7gi4PlOM", "type": "default", "enabled": true}, {"key": "image5_6_prod", "value": "https://fastly.picsum.photos/id/1/670/804.jpg?hmac=yID1nnXEygTnOk94LZ8-MRj8JFJRQqIzop_FY8CIA3U", "type": "default", "enabled": true}, {"key": "sku", "value": "productJohann412", "type": "any", "enabled": true}, {"key": "variable_key", "value": "", "type": "any", "enabled": true}, {"key": "randomSkuFirstName", "value": "Houston", "type": "any", "enabled": true}, {"key": "randomSkuInt", "value": "88231844", "type": "any", "enabled": true}, {"key": "existingProductSku", "value": "productHouston88231844", "type": "default", "enabled": true}, {"key": "product_id_1", "value": 69040140, "type": "any", "enabled": true}, {"key": "flightRouteNumber", "value": "a47327b2-4367-4dba-9f9c-153fa6f9b0bb", "type": "any", "enabled": true}, {"key": "randomRouteGroupFirstName", "value": "<PERSON>", "type": "any", "enabled": true}, {"key": "randomRouteGroupNoun", "value": "transmitter", "type": "any", "enabled": true}, {"key": "existingRouteGroupName", "value": "RG1Vanessatransmitter", "type": "any", "enabled": true}, {"key": "randomSectorFirstName", "value": "Ulices", "type": "any", "enabled": true}, {"key": "existingSectorName", "value": "ABU-DHABI-Klocko-CN", "type": "any", "enabled": true}, {"key": "randomFlightLastName", "value": "<PERSON><PERSON><PERSON><PERSON>", "type": "any", "enabled": true}, {"key": "randomFlightFirstName", "value": "<PERSON>", "type": "any", "enabled": true}, {"key": "existingFlightName", "value": "Flt1GloriaCrookscircuit", "type": "any", "enabled": true}, {"key": "randomCatalogFirstName", "value": "<PERSON><PERSON>", "type": "any", "enabled": true}, {"key": "existingCatalogName", "value": "catalog_Linnie", "type": "any", "enabled": true}, {"key": "polling_start_time", "value": 1712699353, "type": "any", "enabled": true}, {"key": "product_id_2", "value": 69046679, "type": "any", "enabled": true}, {"key": "product_sku", "value": "gymvzbj", "type": "any", "enabled": true}, {"key": "product_sku_delete", "value": 69046680, "type": "any", "enabled": true}, {"key": "icao_1", "value": "OMDB", "type": "any", "enabled": true}, {"key": "icao_2", "value": "CYYZ", "type": "any", "enabled": true}, {"key": "CABeginDate", "value": "2024-04-10", "type": "any", "enabled": true}, {"key": "CAEndDate", "value": "2024-04-16", "type": "any", "enabled": true}, {"key": "mealcode", "value": "FOOD", "type": "any", "enabled": true}, {"key": "uiTemplate", "value": "", "type": "any", "enabled": true}, {"key": "rootType", "value": "", "type": "any", "enabled": true}, {"key": "product_id_for_deletion", "value": 69040140, "type": "any", "enabled": true}, {"key": "productSkus", "value": [], "type": "any", "enabled": true}, {"key": "product_skus", "value": "[\"di83e3t\",\"6qxp4eh\",\"6e8o3l5\",\"ip1jcwz\",\"17wpuat\",\"ouj1q91\",\"gfwujv6\",\"gymvzbj\"]", "type": "any", "enabled": true}, {"key": "catalog_for_states", "value": 69046701, "type": "any", "enabled": true}, {"key": "product9_var", "value": 69041489, "type": "any", "enabled": true}, {"key": "product10_var", "value": 69041490, "type": "any", "enabled": true}, {"key": "product11_var", "value": 69041491, "type": "any", "enabled": true}, {"key": "product12_var", "value": 69041492, "type": "any", "enabled": true}, {"key": "product13_var", "value": 69041493, "type": "any", "enabled": true}, {"key": "product14_var", "value": 69041494, "type": "any", "enabled": true}, {"key": "productIds", "value": [], "type": "any", "enabled": true}, {"key": "product15_var", "value": 69041495, "type": "any", "enabled": true}, {"key": "product16_var", "value": 69041496, "type": "any", "enabled": true}, {"key": "orderid", "value": 69040790, "type": "any", "enabled": true}, {"key": "shipmentid", "value": 69040792, "type": "any", "enabled": true}, {"key": "lineitemid", "value": 69040794, "type": "any", "enabled": true}, {"key": "product17_var", "value": 69041314, "type": "any", "enabled": true}, {"key": "product18_var", "value": 69041315, "type": "any", "enabled": true}, {"key": "product19_var", "value": 69041316, "type": "any", "enabled": true}, {"key": "product20_var", "value": 69041318, "type": "any", "enabled": true}, {"key": "product21_var", "value": 69041319, "type": "any", "enabled": true}, {"key": "product22_var", "value": 69041320, "type": "any", "enabled": true}, {"key": "product23_var", "value": 69041321, "type": "any", "enabled": true}, {"key": "product24_var", "value": 69041322, "type": "any", "enabled": true}, {"key": "product25_var", "value": 69041352, "type": "any", "enabled": true}, {"key": "product26_var", "value": 69041353, "type": "any", "enabled": true}, {"key": "product27_var", "value": 69041354, "type": "any", "enabled": true}, {"key": "product28_var", "value": 69041355, "type": "any", "enabled": true}, {"key": "product29_var", "value": 69041356, "type": "any", "enabled": true}, {"key": "product30_var", "value": 69041357, "type": "any", "enabled": true}, {"key": "product31_var", "value": 69041358, "type": "any", "enabled": true}, {"key": "product32_var", "value": 69041359, "type": "any", "enabled": true}, {"key": "uniqueString", "value": "", "type": "any", "enabled": true}, {"key": "duplicateCatalogAssigment", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-07-12T22:11:28.989Z", "_postman_exported_using": "Postman/11.2.34"}