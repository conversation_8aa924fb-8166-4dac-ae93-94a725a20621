{"id": "65854c4a-2af5-436f-b05c-f016ab594202", "name": "QA Env _ ON Cases AUTO_TEST_AIRLINE_QA_STORE-oct", "values": [{"key": "apiUrl", "value": "https://qa.marketplace-qa.nextcloud.aero", "type": "default", "enabled": true}, {"key": "airlineClientId", "value": "wvs286athy", "type": "default", "enabled": true}, {"key": "airlineClientSecret", "value": "9b6yb&wl7l7nn$5movhv", "type": "default", "enabled": true}, {"key": "storeClientId", "value": "tis3alh9ra", "type": "default", "enabled": true}, {"key": "storeClientSecret", "value": "fl30q86dn$893necz9h7", "type": "default", "enabled": true}, {"key": "airlineToken", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjU2OTA1LCJzdWIiOiJhaXJsaW5lIiwieC1hbXpuLXRyYWNlLWlkIjoiUm9vdD0xLTY3YWYxYmI3LTQyNTk0NGMwNWI3YTgyODMyMjk2OTcyMCIsImlhdCI6MTczOTUyOTE0MywiZXhwIjoxNzcxMDY1MTQzfQ.RKgMwfHAE3qEb-vqqb4_KAkfZODiLTGMwEILjAsqgoc", "type": "default", "enabled": true}, {"key": "storeToken", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjU2OTE0LCJzdWIiOiJzdG9yZSIsIngtYW16bi10cmFjZS1pZCI6IlJvb3Q9MS02N2FmMWNhMC0zOTQ2ZDBjYjAxMDVhZmIxNzQyOTFlZjQiLCJpYXQiOjE3Mzk1MjkzNzYsImV4cCI6MTc3MTA2NTM3Nn0.Wl7lBKz3ae35AV8jmfz5O6elzyV4HaON-t6Mi-H2hDQ", "type": "default", "enabled": true}, {"key": "image1_1", "value": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA", "type": "default", "enabled": true}, {"key": "image2_1", "value": "https://fastly.picsum.photos/id/1/1480/740.jpg?hmac=9H7izvZT7_JeVx6iDGGFTzbHdr5xLC3RbGvz75R_bjU", "type": "default", "enabled": true}, {"key": "image4_3", "value": "https://fastly.picsum.photos/id/1/760/570.jpg?hmac=R_Y34CQ1kbkt1vW54-BFc2oQZrTDpD80w-SSD3hb4Xw", "type": "default", "enabled": true}, {"key": "image5_6", "value": "https://fastly.picsum.photos/id/1/665/798.jpg?hmac=E7CMCI14bkK0UElhCYadh4GEQnQfoh1INct8z_TmWBE", "type": "default", "enabled": true}, {"key": "image2_3", "value": "https://fastly.picsum.photos/id/1/944/1416.jpg?hmac=0pk64dMmzosfKEgQ-UwNhbVZCeYcPqQbGBz7gi4PlOM", "type": "default", "enabled": true}, {"key": "image5_6_prod", "value": "https://fastly.picsum.photos/id/1/670/804.jpg?hmac=yID1nnXEygTnOk94LZ8-MRj8JFJRQqIzop_FY8CIA3U", "type": "default", "enabled": true}, {"key": "mytwitterID", "value": "", "type": "any", "enabled": true}, {"key": "testid", "value": "", "type": "any", "enabled": true}, {"key": "id", "value": "", "type": "any", "enabled": true}, {"key": "sector_id", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id", "value": "12223", "type": "any", "enabled": true}, {"key": "route_group_id_delete", "value": "9185", "type": "any", "enabled": true}, {"key": "sector_id_delete", "value": "9192", "type": "any", "enabled": true}, {"key": "flight_id", "value": "11224", "type": "any", "enabled": true}, {"key": "flight_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_id", "value": "12053", "type": "any", "enabled": true}, {"key": "product_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id", "value": "12264", "type": "any", "enabled": true}, {"key": "store_location_id_delete", "value": "9121", "type": "any", "enabled": true}, {"key": "fulfillment_id", "value": "", "type": "any", "enabled": true}, {"key": "fulfillment_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "delivery_rule_id", "value": "", "type": "any", "enabled": true}, {"key": "delivery_rule_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "category_id", "value": "12278", "type": "any", "enabled": true}, {"key": "category_id_delete", "value": "9963", "type": "any", "enabled": true}, {"key": "product_type", "value": "Food and Beverage", "type": "any", "enabled": true}, {"key": "reservation_start_at ", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end_1 ", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_sku1", "value": "Product_1", "type": "any", "enabled": true}, {"key": "product_sku2", "value": "Product_2", "type": "any", "enabled": true}, {"key": "campaign_id", "value": "", "type": "any", "enabled": true}, {"key": "campaign_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "campaign_type", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku1", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku2", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method", "value": "Onboard", "type": "any", "enabled": true}, {"key": "promotion_type_id", "value": "", "type": "any", "enabled": true}, {"key": "promotion_type_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "promotion_id", "value": "", "type": "any", "enabled": true}, {"key": "promotion_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method1", "value": "", "type": "any", "enabled": true}, {"key": "airline_id", "value": "", "type": "any", "enabled": true}, {"key": "future-date", "value": "", "type": "any", "enabled": true}, {"key": "future_date", "value": "", "type": "any", "enabled": true}, {"key": "future_date1", "value": "", "type": "any", "enabled": true}, {"key": "route_group_name", "value": "RG1Haleyapplication", "type": "any", "enabled": true}, {"key": "product_id_exclude", "value": "9238", "type": "any", "enabled": true}, {"key": "sector_id_exclude", "value": "9191", "type": "any", "enabled": true}, {"key": "cabinclass_id", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get", "value": "12217", "type": "any", "enabled": true}, {"key": "route_group_id_get", "value": "9186", "type": "any", "enabled": true}, {"key": "delivery_rule_type", "value": "", "type": "any", "enabled": true}, {"key": "delivery_type", "value": "", "type": "any", "enabled": true}, {"key": "product_get_id", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_1", "value": "9357", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get2", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_delta", "value": "", "type": "any", "enabled": true}, {"key": "product_id_from_catalog", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get1", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id_delta", "value": "", "type": "any", "enabled": true}, {"key": "delta_product", "value": "", "type": "any", "enabled": true}, {"key": "sku_delta_product", "value": "", "type": "any", "enabled": true}, {"key": "aircraft_id", "value": "", "type": "any", "enabled": true}, {"key": "count", "value": "NaN", "type": "any", "enabled": true}, {"key": "sector_id_1", "value": "12228", "type": "any", "enabled": true}, {"key": "image", "value": "https://placehold.jp/", "type": "default", "enabled": true}, {"key": "destination_var", "value": "SCA", "type": "default", "enabled": true}, {"key": "origin_var", "value": "OMAA", "type": "default", "enabled": true}, {"key": "flightBeginDateTime_var", "value": "2023-01-05 21:00", "type": "default", "enabled": true}, {"key": "flightEndDateTime_var", "value": "2023-01-06 17:35", "type": "default", "enabled": true}, {"key": "uitemplate_var", "value": "null", "type": "default", "enabled": true}, {"key": "roottype_var", "value": "null", "type": "default", "enabled": true}, {"key": "bannerimage_var", "value": "https://placehold.jp/3160x632.png", "type": "default", "enabled": true}, {"key": "backgroundimage_var", "value": "https://placehold.jp/3840x2160.png", "type": "default", "enabled": true}, {"key": "storeid_var", "value": "1254", "type": "default", "enabled": true}, {"key": "inventorycheckicao1_var", "value": "OMAM", "type": "default", "enabled": true}, {"key": "inventorycheckicao2_var", "value": "WSSS", "type": "default", "enabled": true}, {"key": "storelocationicao_var", "value": "LAX", "type": "default", "enabled": true}, {"key": "specialityimage_var", "value": "https://placehold.jp/256x256.png", "type": "default", "enabled": true}, {"key": "campaigntype_var", "value": "Category", "type": "default", "enabled": true}, {"key": "orderairlineicao1_var", "value": "111", "type": "default", "enabled": true}, {"key": "orderpacvariantid_var", "value": "9238", "type": "default", "enabled": true}, {"key": "orderretailercode_var", "value": "9111", "type": "default", "enabled": true}, {"key": "orderfulfillmenttype_var", "value": "inHouse", "type": "default", "enabled": true}, {"key": "orderassociatestoreid_var", "value": "9111", "type": "default", "enabled": true}, {"key": "orderid_var", "value": "14974", "type": "default", "enabled": true}, {"key": "ordershipmentid_var", "value": "14976", "type": "default", "enabled": true}, {"key": "deliveryruletype_var", "value": "shipping", "type": "default", "enabled": true}, {"key": "shipmentgroup_var", "value": "fulfillment_type", "type": "default", "enabled": true}, {"key": "shippingDestination_var", "value": "domestic", "type": "default", "enabled": true}, {"key": "deliverytype_var", "value": "homeShippingDomesticStandard", "type": "default", "enabled": true}, {"key": "productnewfrom_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "productnewto_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "catalog_var", "value": "11068", "type": "default", "enabled": true}, {"key": "productmealcode_var", "value": "qa_mealcode", "type": "default", "enabled": true}, {"key": "product1_var", "value": "11052", "type": "default", "enabled": true}, {"key": "product2_var", "value": "11055", "type": "default", "enabled": true}, {"key": "product3_var", "value": "11058", "type": "default", "enabled": true}, {"key": "product4_var", "value": "11059", "type": "default", "enabled": true}, {"key": "product5_var", "value": "11060", "type": "default", "enabled": true}, {"key": "product6_var", "value": "11062", "type": "default", "enabled": true}, {"key": "product7_var", "value": "11063", "type": "default", "enabled": true}, {"key": "product8_var", "value": "11064", "type": "default", "enabled": true}, {"key": "airlinecategory_1var", "value": "11049", "type": "default", "enabled": true}, {"key": "airlinecategory_2var", "value": "11051", "type": "default", "enabled": true}, {"key": "flight_var", "value": "10984", "type": "default", "enabled": true}, {"key": "sector_var", "value": "9192", "type": "default", "enabled": true}, {"key": "catalogfromdate_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "catalogtodate_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "cabinclass_var", "value": "3004", "type": "default", "enabled": true}, {"key": "category_var", "value": "11047", "type": "default", "enabled": true}, {"key": "arrivalorderairportiata_var", "value": "OMAA", "type": "default", "enabled": true}, {"key": "departureorderairportiata_var", "value": "SCA", "type": "default", "enabled": true}, {"key": "orderitemid_var", "value": "14978", "type": "default", "enabled": true}, {"key": "sector_id_2", "value": "", "type": "any", "enabled": true}, {"key": "CAflightid_var", "value": "9196", "type": "default", "enabled": true}, {"key": "CAsectorid_var", "value": "9194", "type": "default", "enabled": true}, {"key": "CAroutegroupid_var", "value": "9187", "type": "default", "enabled": true}, {"key": "CAincludedsectorid_var", "value": "9204", "type": "default", "enabled": true}, {"key": "airline_category_id_1", "value": "", "type": "any", "enabled": true}, {"key": "randomSkuFirstName", "value": "", "type": "any", "enabled": true}, {"key": "randomSkuInt", "value": "", "type": "any", "enabled": true}, {"key": "existingProductSku", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_01", "value": "", "type": "any", "enabled": true}, {"key": "currency", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_01", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_02", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_02", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_03", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_03", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_04", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_04", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_05", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_05", "value": "", "type": "any", "enabled": true}, {"key": "MultiProduct_Catalog_ID", "value": "", "type": "any", "enabled": true}, {"key": "ui_Template_AC", "value": "", "type": "any", "enabled": true}, {"key": "rootType_AC", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level1", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level2", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level3", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level4", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level5", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level6", "value": "", "type": "any", "enabled": true}, {"key": "polling_start_time", "value": "", "type": "any", "enabled": true}, {"key": "currentRetry", "value": "", "type": "any", "enabled": true}, {"key": "flightId", "value": "", "type": "any", "enabled": true}, {"key": "sectorId", "value": "", "type": "any", "enabled": true}, {"key": "beginDate", "value": "", "type": "any", "enabled": true}, {"key": "endDate", "value": "", "type": "any", "enabled": true}, {"key": "cabinClassID", "value": "", "type": "any", "enabled": true}, {"key": "CABeginDate", "value": "", "type": "any", "enabled": true}, {"key": "CAEndDate", "value": "", "type": "any", "enabled": true}, {"key": "airlinecategoryid", "value": "", "type": "any", "enabled": true}, {"key": "icao_1", "value": "", "type": "any", "enabled": true}, {"key": "icao_2", "value": "", "type": "any", "enabled": true}, {"key": "existingSectorName", "value": "", "type": "any", "enabled": true}, {"key": "flightBeginDateTime", "value": "2025-11-05 21:00", "type": "any", "enabled": true}, {"key": "flightEndDateTime", "value": "2026-12-06 17:35", "type": "any", "enabled": true}, {"key": "existingFlightName", "value": "", "type": "any", "enabled": true}, {"key": "product_id_1", "value": "", "type": "any", "enabled": true}, {"key": "product_sku", "value": "", "type": "any", "enabled": true}, {"key": "JWT_TOKEN", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NTY5MDUsImlhdGFDb2RlIjoiU1hNIiwiaWNhb0NvZGUiOiJUTkNNIiwiYWlybGluZU5hbWUiOnsiZW4iOiJRQV9BVVRPX1RFU1RfQUlSTElORSJ9fQ.HCVgdlwWdmFaCN3LlVVLK64nS6W36rSI4xjtMQvL4Dg", "type": "default", "enabled": true}, {"key": "currentId", "value": "", "type": "any", "enabled": true}, {"key": "Product_Name", "value": "", "type": "any", "enabled": true}, {"key": "productIds", "value": "", "type": "any", "enabled": true}, {"key": "rootType", "value": "", "type": "any", "enabled": true}, {"key": "productSkus", "value": "", "type": "any", "enabled": true}, {"key": "product_skus", "value": "", "type": "any", "enabled": true}, {"key": "catalog_for_states", "value": "", "type": "any", "enabled": true}, {"key": "product9_var", "value": "", "type": "any", "enabled": true}, {"key": "product10_var", "value": "", "type": "any", "enabled": true}, {"key": "product11_var", "value": "", "type": "any", "enabled": true}, {"key": "product12_var", "value": "", "type": "any", "enabled": true}, {"key": "product13_var", "value": "", "type": "any", "enabled": true}, {"key": "product14_var", "value": "", "type": "any", "enabled": true}, {"key": "product15_var", "value": "", "type": "any", "enabled": true}, {"key": "product16_var", "value": "", "type": "any", "enabled": true}, {"key": "product17_var", "value": "", "type": "any", "enabled": true}, {"key": "product18_var", "value": "", "type": "any", "enabled": true}, {"key": "product19_var", "value": "", "type": "any", "enabled": true}, {"key": "product20_var", "value": "", "type": "any", "enabled": true}, {"key": "product21_var", "value": "", "type": "any", "enabled": true}, {"key": "product22_var", "value": "", "type": "any", "enabled": true}, {"key": "product23_var", "value": "", "type": "any", "enabled": true}, {"key": "product24_var", "value": "", "type": "any", "enabled": true}, {"key": "product25_var", "value": "", "type": "any", "enabled": true}, {"key": "product26_var", "value": "", "type": "any", "enabled": true}, {"key": "product27_var", "value": "", "type": "any", "enabled": true}, {"key": "product28_var", "value": "", "type": "any", "enabled": true}, {"key": "product29_var", "value": "", "type": "any", "enabled": true}, {"key": "product30_var", "value": "", "type": "any", "enabled": true}, {"key": "product31_var", "value": "", "type": "any", "enabled": true}, {"key": "product_name", "value": "", "type": "any", "enabled": true}, {"key": "image1200_1", "value": "", "type": "any", "enabled": true}, {"key": "status_ca", "value": "", "type": "any", "enabled": true}, {"key": "airline", "value": "QA_AUTO_TEST_AIRLINE", "type": "default", "enabled": true}, {"key": "Store", "value": "QA_AUTO_TEST_STORE", "type": "default", "enabled": true}, {"key": "product32_var", "value": "", "type": "any", "enabled": true}, {"key": "product33_var", "value": "", "type": "any", "enabled": true}, {"key": "product34_var", "value": "", "type": "any", "enabled": true}, {"key": "product35_var", "value": "", "type": "any", "enabled": true}, {"key": "product36_var", "value": "", "type": "any", "enabled": true}, {"key": "store_id", "value": "", "type": "any", "enabled": true}, {"key": "errorBypassed", "value": "", "type": "any", "enabled": true}, {"key": "product_to_delete", "value": "", "type": "any", "enabled": true}, {"key": "category_id_new", "value": "", "type": "any", "enabled": true}, {"key": "product_sku_delete", "value": "", "type": "any", "enabled": true}, {"key": "cata_product_id", "value": "", "type": "any", "enabled": true}, {"key": "cata_product_sku", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id1", "value": "", "type": "any", "enabled": true}, {"key": "route_group_Id", "value": "", "type": "any", "enabled": true}, {"key": "categoryid_new", "value": "", "type": "any", "enabled": true}, {"key": "product_id1", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_1", "value": "", "type": "any", "enabled": true}, {"key": "product_id1_2", "value": "", "type": "any", "enabled": true}, {"key": "product_id1_3", "value": "", "type": "any", "enabled": true}, {"key": "product_id1_4", "value": "", "type": "any", "enabled": true}, {"key": "product_id2", "value": "", "type": "any", "enabled": true}, {"key": "product_id2_2", "value": "", "type": "any", "enabled": true}, {"key": "product_id2_3", "value": "", "type": "any", "enabled": true}, {"key": "product_id2_4", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_1_2", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_1_3", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_1_4", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_2", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_2_2", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_2_3", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_2_4", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id_mult_prod", "value": "", "type": "any", "enabled": true}, {"key": "airlinecategoryId1", "value": "", "type": "any", "enabled": true}, {"key": "airlinecategoryId2", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assignid_get1", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assignid_get2", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assignid_get3", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assignid_get4", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get5", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get6", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get7", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get8", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get9", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-05-23T07:35:56.454Z", "_postman_exported_using": "Postman/11.5.1"}