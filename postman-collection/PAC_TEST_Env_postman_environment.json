{"id": "66c07690-4a6c-405b-a12d-38da0a252e7d", "name": "PAC_TEST_ENV_POSTMAN", "values": [{"key": "apiUrl", "value": "https://marketplace-test.nextcloud.aero", "type": "default", "enabled": true}, {"key": "airlineClientId", "value": "t1gz2bnlgv", "type": "default", "enabled": true}, {"key": "airlineClientSecret", "value": "y#f1-od7ujpleb2pvd#z", "type": "default", "enabled": true}, {"key": "storeClientId", "value": "2x135b7yrs", "type": "default", "enabled": true}, {"key": "storeClientSecret", "value": "75vm70y&389pflj0t3j$", "type": "default", "enabled": true}, {"key": "airlineToken", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjY5NjM4LCJzdWIiOiJhaXJsaW5lIiwiaWF0IjoxNzEwNzM1MDI4LCJleHAiOjE3MTA3MzU5Mjh9.kfAu4nbXWFCUsnjgHLtEdBLlNnGTMdTXV5P7RBDmcm8", "type": "default", "enabled": true}, {"key": "storeToken", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjY5NjI3LCJzdWIiOiJzdG9yZSIsImlhdCI6MTcxMDczNTM3NCwiZXhwIjoxNzEwNzM2Mjc0fQ.oy8pMuNHJb7PiDxF3mHY7an9gU3t7YXcGLrBYzwN9wY", "type": "default", "enabled": true}, {"key": "mytwitterID", "value": "", "type": "any", "enabled": true}, {"key": "testid", "value": "", "type": "any", "enabled": true}, {"key": "id", "value": "", "type": "any", "enabled": true}, {"key": "sector_id", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id", "value": 86606790, "type": "any", "enabled": true}, {"key": "route_group_id_delete", "value": 69648, "type": "any", "enabled": true}, {"key": "sector_id_delete", "value": 69654, "type": "any", "enabled": true}, {"key": "flight_id", "value": 86606793, "type": "any", "enabled": true}, {"key": "flight_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id", "value": 86606717, "type": "any", "enabled": true}, {"key": "airline_category_id_delete", "value": 74234, "type": "any", "enabled": true}, {"key": "product_id", "value": 69723, "type": "any", "enabled": true}, {"key": "product_id_delete", "value": 69724, "type": "any", "enabled": true}, {"key": "catalog_assign_id", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id", "value": 86606811, "type": "any", "enabled": true}, {"key": "store_location_id_delete", "value": 69637, "type": "any", "enabled": true}, {"key": "fulfillment_id", "value": 61, "type": "any", "enabled": true}, {"key": "fulfillment_id_delete", "value": 62, "type": "any", "enabled": true}, {"key": "delivery_rule_id", "value": 86606810, "type": "any", "enabled": true}, {"key": "delivery_rule_id_delete", "value": 86606810, "type": "any", "enabled": true}, {"key": "category_id", "value": "86603764", "type": "any", "enabled": true}, {"key": "category_id_delete", "value": "86603764", "type": "any", "enabled": true}, {"key": "product_type", "value": "Duty Free", "type": "any", "enabled": true}, {"key": "reservation_start_at ", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end_1 ", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id", "value": 86606804, "type": "any", "enabled": true}, {"key": "catalog_id_delete", "value": 69739, "type": "any", "enabled": true}, {"key": "product_sku1", "value": "productEmilie", "type": "any", "enabled": true}, {"key": "product_sku2", "value": "productHollis", "type": "any", "enabled": true}, {"key": "campaign_id", "value": "", "type": "any", "enabled": true}, {"key": "campaign_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "campaign_type", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku1", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku2", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method", "value": "Onboard", "type": "any", "enabled": true}, {"key": "promotion_type_id", "value": 59, "type": "any", "enabled": true}, {"key": "promotion_type_id_delete", "value": 7352, "type": "any", "enabled": true}, {"key": "promotion_id", "value": 86606806, "type": "any", "enabled": true}, {"key": "promotion_id_delete", "value": 108852, "type": "any", "enabled": true}, {"key": "delivery_method1", "value": "", "type": "any", "enabled": true}, {"key": "airline_id", "value": 69638, "type": "any", "enabled": true}, {"key": "future-date", "value": "", "type": "any", "enabled": true}, {"key": "future_date", "value": "2024-03-19", "type": "any", "enabled": true}, {"key": "future_date1", "value": "2024-03-21", "type": "any", "enabled": true}, {"key": "route_group_name", "value": "RG1Destineysystem", "type": "any", "enabled": true}, {"key": "product_id_exclude", "value": "86601370", "type": "any", "enabled": true}, {"key": "sector_id_exclude", "value": 69652, "type": "any", "enabled": true}, {"key": "cabinclass_id", "value": 82, "type": "any", "enabled": true}, {"key": "speciality_attribute_id_delete", "value": 69673, "type": "any", "enabled": true}, {"key": "speciality_attribute_id", "value": 86606805, "type": "any", "enabled": true}, {"key": "catalog_assign_id_get", "value": 86606802, "type": "any", "enabled": true}, {"key": "route_group_id_get", "value": 69650, "type": "any", "enabled": true}, {"key": "delivery_rule_type", "value": "", "type": "any", "enabled": true}, {"key": "delivery_type", "value": "homeShippingDomesticStandard", "type": "any", "enabled": true}, {"key": "product_get_id", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_1", "value": 69673, "type": "any", "enabled": true}, {"key": "catalog_assign_id_get2", "value": 72656, "type": "any", "enabled": true}, {"key": "catalog_assign_id_delta", "value": 69739, "type": "any", "enabled": true}, {"key": "product_id_from_catalog", "value": 69725, "type": "any", "enabled": true}, {"key": "catalog_assign_id_get1", "value": 86606807, "type": "any", "enabled": true}, {"key": "catalog_id_delta", "value": "", "type": "any", "enabled": true}, {"key": "delta_product", "value": "", "type": "any", "enabled": true}, {"key": "sku_delta_product", "value": "", "type": "any", "enabled": true}, {"key": "aircraft_id", "value": "", "type": "any", "enabled": true}, {"key": "count", "value": "NaN", "type": "any", "enabled": true}, {"key": "sector_id_1", "value": 86606791, "type": "any", "enabled": true}, {"key": "image", "value": "https://placehold.jp/", "type": "default", "enabled": true}, {"key": "destination_var", "value": "BEL", "type": "default", "enabled": true}, {"key": "origin_var", "value": "BOM", "type": "default", "enabled": true}, {"key": "flightBeginDateTime_var", "value": "2023-01-05 21:00", "type": "default", "enabled": true}, {"key": "flightEndDateTime_var", "value": "2023-01-06 17:35", "type": "default", "enabled": true}, {"key": "uitemplate_var", "value": "null", "type": "default", "enabled": true}, {"key": "roottype_var", "value": "null", "type": "default", "enabled": true}, {"key": "bannerimage_var", "value": "https://placehold.jp/3160x632.png", "type": "default", "enabled": true}, {"key": "backgroundimage_var", "value": "https://placehold.jp/3840x2160.png", "type": "default", "enabled": true}, {"key": "storeid_var", "value": "69627", "type": "default", "enabled": true}, {"key": "inventorycheckicao1_var", "value": "OMAM", "type": "default", "enabled": true}, {"key": "inventorycheckicao2_var", "value": "WSSS", "type": "default", "enabled": true}, {"key": "storelocationicao_var", "value": "BEL", "type": "default", "enabled": true}, {"key": "specialityimage_var", "value": "https://placehold.jp/256x256.png", "type": "default", "enabled": true}, {"key": "campaigntype_var", "value": "Category", "type": "default", "enabled": true}, {"key": "orderairlineicao1_var", "value": "AUTO", "type": "default", "enabled": true}, {"key": "orderpacvariantid_var", "value": "74347", "type": "default", "enabled": true}, {"key": "orderretailercode_var", "value": "9111", "type": "default", "enabled": true}, {"key": "orderfulfillmenttype_var", "value": "inHouse", "type": "default", "enabled": true}, {"key": "orderassociatestoreid_var", "value": "69627", "type": "default", "enabled": true}, {"key": "orderid_var", "value": "80421", "type": "default", "enabled": true}, {"key": "ordershipmentid_var", "value": "86595169", "type": "default", "enabled": true}, {"key": "deliveryruletype_var", "value": "shipping", "type": "default", "enabled": true}, {"key": "shipmentgroup_var", "value": "fulfillment_type", "type": "default", "enabled": true}, {"key": "shippingDestination_var", "value": "domestic", "type": "default", "enabled": true}, {"key": "deliverytype_var", "value": "homeShippingDomesticStandard", "type": "default", "enabled": true}, {"key": "productnewfrom_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "productnewto_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "catalog_var", "value": "86593449", "type": "default", "enabled": true}, {"key": "productmealcode_var", "value": "qa_mealcode", "type": "default", "enabled": true}, {"key": "product1_var", "value": "86593054", "type": "default", "enabled": true}, {"key": "product2_var", "value": "86593057", "type": "default", "enabled": true}, {"key": "product3_var", "value": "86593455", "type": "default", "enabled": true}, {"key": "product4_var", "value": "86593457", "type": "default", "enabled": true}, {"key": "product5_var", "value": "86593463", "type": "default", "enabled": true}, {"key": "product6_var", "value": "86593469", "type": "default", "enabled": true}, {"key": "product7_var", "value": "86593473", "type": "default", "enabled": true}, {"key": "product8_var", "value": "86593476", "type": "default", "enabled": true}, {"key": "airlinecategory_1var", "value": "86604420", "type": "default", "enabled": true}, {"key": "airlinecategory_2var", "value": "86604421", "type": "default", "enabled": true}, {"key": "flight_var", "value": "69915", "type": "default", "enabled": true}, {"key": "sector_var", "value": "69913", "type": "default", "enabled": true}, {"key": "catalogfromdate_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "catalogtodate_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "cabinclass_var", "value": "104976", "type": "default", "enabled": true}, {"key": "category_var", "value": "86593067", "type": "default", "enabled": true}, {"key": "arrivalorderairportiata_var", "value": "abc", "type": "default", "enabled": true}, {"key": "departureorderairportiata_var", "value": "LUL", "type": "default", "enabled": true}, {"key": "orderitemid_var", "value": "80424", "type": "default", "enabled": true}, {"key": "sector_id_2", "value": 86606792, "type": "any", "enabled": true}, {"key": "CAflightid_var", "value": "69915", "type": "default", "enabled": true}, {"key": "CAsectorid_var", "value": "69913", "type": "default", "enabled": true}, {"key": "CAroutegroupid_var", "value": "69648", "type": "default", "enabled": true}, {"key": "CAincludedsectorid_var", "value": "69913", "type": "default", "enabled": true}, {"key": "airline_category_id_1", "value": 86606717, "type": "any", "enabled": true}, {"key": "JWT_TOKEN", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJBVVRPIiwic3ViIjoiYWlybGluZV9lbmRfdXNlciIsImlhdCI6MTcwMjQ0ODk5NiwiZXhwIjoyNTM2NDMxNDAwfQ.s20r9AhlFrXt7kKriv2bnhOpni52DHAqLf8r0SvDyRc", "type": "default", "enabled": true}, {"key": "randomSkuInt", "value": "03a57cec-29ba-4f62-a5d4-2b797cc7f434", "type": "any", "enabled": true}, {"key": "product_id_1", "value": 69723, "type": "any", "enabled": true}, {"key": "randomSkuFirstName", "value": "<PERSON><PERSON><PERSON>", "type": "any", "enabled": true}, {"key": "existingProductSku", "value": "productShyann03a57cec-29ba-4f62-a5d4-2b797cc7f434", "type": "any", "enabled": true}, {"key": "image1_1", "value": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA", "type": "default", "enabled": true}, {"key": "image2_1", "value": "https://fastly.picsum.photos/id/1/1480/740.jpg?hmac=9H7izvZT7_JeVx6iDGGFTzbHdr5xLC3RbGvz75R_bjU", "type": "default", "enabled": true}, {"key": "image4_3", "value": "https://fastly.picsum.photos/id/1/760/570.jpg?hmac=R_Y34CQ1kbkt1vW54-BFc2oQZrTDpD80w-SSD3hb4Xw", "type": "default", "enabled": true}, {"key": "image5_6", "value": "https://fastly.picsum.photos/id/1/665/798.jpg?hmac=E7CMCI14bkK0UElhCYadh4GEQnQfoh1INct8z_TmWBE", "type": "default", "enabled": true}, {"key": "image2_3", "value": "https://fastly.picsum.photos/id/1/944/1416.jpg?hmac=0pk64dMmzosfKEgQ-UwNhbVZCeYcPqQbGBz7gi4PlOM", "type": "default", "enabled": true}, {"key": "image5_6_prod", "value": "https://fastly.picsum.photos/id/1/670/804.jpg?hmac=yID1nnXEygTnOk94LZ8-MRj8JFJRQqIzop_FY8CIA3U", "type": "default", "enabled": true}, {"key": "JWT_TOKEN_DEV", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJBVVRPIiwic3ViIjoiYWlybGluZV9lbmRfdXNlciIsImlhdCI6MTcxMDc1OTM0OSwiZXhwIjoyNTM2NDMxNDAwfQ.bjddDvMsvrh-JJUSrO82VCUAnBxlakkVium0UllDTXw", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-03-18T20:45:16.627Z", "_postman_exported_using": "Postman/10.24.3"}