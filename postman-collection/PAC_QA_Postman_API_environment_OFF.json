{"id": "d2b12311-74fc-4ed6-bfe7-cd492e630ab7", "name": "QA Env_OFF Cases Demo CI Store n Airline_Common_DEC'24", "values": [{"key": "apiUrl", "value": "https://qa.marketplace-qa.nextcloud.aero", "type": "default", "enabled": true}, {"key": "airlineClientId", "value": "24qbrqhp94", "type": "default", "enabled": true}, {"key": "airlineClientSecret", "value": "k1dl8f0nibftrcvxpj55", "type": "default", "enabled": true}, {"key": "storeClientId", "value": "lk4cjane9y", "type": "default", "enabled": true}, {"key": "storeClientSecret", "value": "jlbo4sy85x$4ppcfu7u3", "type": "default", "enabled": true}, {"key": "airlineToken", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjkxMjIsInN1YiI6ImFpcmxpbmUiLCJ4LWFtem4tdHJhY2UtaWQiOiJSb290PTEtNjc2MmE0MjgtNjdlZTFkZWQ1MmFmNTVkNzAzZWUxNzY3IiwiaWF0IjoxNzM0NTE3ODAwLCJleHAiOjE3NjYwNTM4MDB9.ba4n8Gak_9kFf5n00FX0ta0rnpReqwMqwuzp52Z5SXc", "type": "default", "enabled": true}, {"key": "storeToken", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOjkxMTEsInN1YiI6InN0b3JlIiwieC1hbXpuLXRyYWNlLWlkIjoiUm9vdD0xLTY3NjJhNDJhLTRhN2Q1ZTE1MTA1ZTkwOTcwNGFiNmZmMiIsImlhdCI6MTczNDUxNzgwMiwiZXhwIjoxNzY2MDUzODAyfQ.V9DX1otpl7LshawbZq05jIUXKHw1XjF-EumuqEMA9_8", "type": "default", "enabled": true}, {"key": "image1_1", "value": "https://fastly.picsum.photos/id/1/1200/1200.jpg?hmac=86-RABGZulIOYqQuojaGpxQdNNd8rbgTN7kIe2znjSA", "type": "default", "enabled": true}, {"key": "image2_1", "value": "https://fastly.picsum.photos/id/1/1480/740.jpg?hmac=9H7izvZT7_JeVx6iDGGFTzbHdr5xLC3RbGvz75R_bjU", "type": "default", "enabled": true}, {"key": "image4_3", "value": "https://fastly.picsum.photos/id/1/760/570.jpg?hmac=R_Y34CQ1kbkt1vW54-BFc2oQZrTDpD80w-SSD3hb4Xw", "type": "default", "enabled": true}, {"key": "image5_6", "value": "https://fastly.picsum.photos/id/1/665/798.jpg?hmac=E7CMCI14bkK0UElhCYadh4GEQnQfoh1INct8z_TmWBE", "type": "default", "enabled": true}, {"key": "image2_3", "value": "https://fastly.picsum.photos/id/1/944/1416.jpg?hmac=0pk64dMmzosfKEgQ-UwNhbVZCeYcPqQbGBz7gi4PlOM", "type": "default", "enabled": true}, {"key": "image5_6_prod", "value": "https://fastly.picsum.photos/id/1/670/804.jpg?hmac=yID1nnXEygTnOk94LZ8-MRj8JFJRQqIzop_FY8CIA3U", "type": "default", "enabled": true}, {"key": "mytwitterID", "value": "", "type": "any", "enabled": true}, {"key": "testid", "value": "", "type": "any", "enabled": true}, {"key": "id", "value": "", "type": "any", "enabled": true}, {"key": "sector_id", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id", "value": "12223", "type": "any", "enabled": true}, {"key": "route_group_id_delete", "value": "9185", "type": "any", "enabled": true}, {"key": "sector_id_delete", "value": "9192", "type": "any", "enabled": true}, {"key": "flight_id", "value": "11224", "type": "any", "enabled": true}, {"key": "flight_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_id", "value": "12053", "type": "any", "enabled": true}, {"key": "product_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id", "value": "12264", "type": "any", "enabled": true}, {"key": "store_location_id_delete", "value": "9121", "type": "any", "enabled": true}, {"key": "fulfillment_id", "value": "", "type": "any", "enabled": true}, {"key": "fulfillment_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "delivery_rule_id", "value": "", "type": "any", "enabled": true}, {"key": "delivery_rule_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "category_id", "value": "12278", "type": "any", "enabled": true}, {"key": "category_id_delete", "value": "9963", "type": "any", "enabled": true}, {"key": "product_type", "value": "Food and Beverage", "type": "any", "enabled": true}, {"key": "reservation_start_at ", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end_1 ", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_sku1", "value": "Product_1", "type": "any", "enabled": true}, {"key": "product_sku2", "value": "Product_2", "type": "any", "enabled": true}, {"key": "campaign_id", "value": "", "type": "any", "enabled": true}, {"key": "campaign_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "campaign_type", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku1", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku2", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method", "value": "Onboard", "type": "any", "enabled": true}, {"key": "promotion_type_id", "value": "", "type": "any", "enabled": true}, {"key": "promotion_type_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "promotion_id", "value": "", "type": "any", "enabled": true}, {"key": "promotion_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method1", "value": "", "type": "any", "enabled": true}, {"key": "airline_id", "value": "", "type": "any", "enabled": true}, {"key": "future-date", "value": "", "type": "any", "enabled": true}, {"key": "future_date", "value": "", "type": "any", "enabled": true}, {"key": "future_date1", "value": "", "type": "any", "enabled": true}, {"key": "route_group_name", "value": "RG1Haleyapplication", "type": "any", "enabled": true}, {"key": "product_id_exclude", "value": "9238", "type": "any", "enabled": true}, {"key": "sector_id_exclude", "value": "9191", "type": "any", "enabled": true}, {"key": "cabinclass_id", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get", "value": "12217", "type": "any", "enabled": true}, {"key": "route_group_id_get", "value": "9186", "type": "any", "enabled": true}, {"key": "delivery_rule_type", "value": "", "type": "any", "enabled": true}, {"key": "delivery_type", "value": "", "type": "any", "enabled": true}, {"key": "product_get_id", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_1", "value": "9357", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get2", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_delta", "value": "", "type": "any", "enabled": true}, {"key": "product_id_from_catalog", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get1", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id_delta", "value": "", "type": "any", "enabled": true}, {"key": "delta_product", "value": "", "type": "any", "enabled": true}, {"key": "sku_delta_product", "value": "", "type": "any", "enabled": true}, {"key": "aircraft_id", "value": "", "type": "any", "enabled": true}, {"key": "count", "value": "1", "type": "any", "enabled": true}, {"key": "sector_id_1", "value": "12228", "type": "any", "enabled": true}, {"key": "image", "value": "https://placehold.jp/", "type": "default", "enabled": true}, {"key": "destination_var", "value": "SCA", "type": "default", "enabled": true}, {"key": "origin_var", "value": "OMAA", "type": "default", "enabled": true}, {"key": "flightBeginDateTime_var", "value": "2023-01-05 21:00", "type": "default", "enabled": false}, {"key": "flightEndDateTime_var", "value": "2023-01-06 17:35", "type": "default", "enabled": true}, {"key": "uitemplate_var", "value": "null", "type": "default", "enabled": true}, {"key": "roottype_var", "value": "null", "type": "default", "enabled": true}, {"key": "bannerimage_var", "value": "https://placehold.jp/3160x632.png", "type": "default", "enabled": true}, {"key": "backgroundimage_var", "value": "https://placehold.jp/3840x2160.png", "type": "default", "enabled": true}, {"key": "storeid_var", "value": "1254", "type": "default", "enabled": true}, {"key": "inventorycheckicao1_var", "value": "OMAM", "type": "default", "enabled": true}, {"key": "inventorycheckicao2_var", "value": "WSSS", "type": "default", "enabled": true}, {"key": "storelocationicao_var", "value": "LAX", "type": "default", "enabled": true}, {"key": "specialityimage_var", "value": "https://placehold.jp/256x256.png", "type": "default", "enabled": true}, {"key": "campaigntype_var", "value": "Category", "type": "default", "enabled": true}, {"key": "orderairlineicao1_var", "value": "111", "type": "default", "enabled": true}, {"key": "orderpacvariantid_var", "value": "9238", "type": "default", "enabled": true}, {"key": "orderretailercode_var", "value": "9111", "type": "default", "enabled": true}, {"key": "orderfulfillmenttype_var", "value": "inHouse", "type": "default", "enabled": true}, {"key": "orderassociatestoreid_var", "value": "9111", "type": "default", "enabled": true}, {"key": "orderid_var", "value": "14974", "type": "default", "enabled": true}, {"key": "ordershipmentid_var", "value": "14976", "type": "default", "enabled": true}, {"key": "deliveryruletype_var", "value": "shipping", "type": "default", "enabled": true}, {"key": "shipmentgroup_var", "value": "fulfillment_type", "type": "default", "enabled": true}, {"key": "shippingDestination_var", "value": "domestic", "type": "default", "enabled": true}, {"key": "deliverytype_var", "value": "homeShippingDomesticStandard", "type": "default", "enabled": true}, {"key": "productnewfrom_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "productnewto_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "catalog_var", "value": "11068", "type": "default", "enabled": true}, {"key": "productmealcode_var", "value": "qa_mealcode", "type": "default", "enabled": true}, {"key": "product1_var", "value": "11052", "type": "default", "enabled": true}, {"key": "product2_var", "value": "11055", "type": "default", "enabled": true}, {"key": "product3_var", "value": "11058", "type": "default", "enabled": true}, {"key": "product4_var", "value": "11059", "type": "default", "enabled": true}, {"key": "product5_var", "value": "11060", "type": "default", "enabled": true}, {"key": "product6_var", "value": "11062", "type": "default", "enabled": true}, {"key": "product7_var", "value": "11063", "type": "default", "enabled": true}, {"key": "product8_var", "value": "11064", "type": "default", "enabled": true}, {"key": "airlinecategory_1var", "value": "11049", "type": "default", "enabled": true}, {"key": "airlinecategory_2var", "value": "11051", "type": "default", "enabled": true}, {"key": "flight_var", "value": "10984", "type": "default", "enabled": true}, {"key": "sector_var", "value": "9192", "type": "default", "enabled": true}, {"key": "catalogfromdate_var", "value": "2024-12-30", "type": "default", "enabled": true}, {"key": "catalogtodate_var", "value": "2024-12-31", "type": "default", "enabled": true}, {"key": "cabinclass_var", "value": "3004", "type": "default", "enabled": true}, {"key": "category_var", "value": "11047", "type": "default", "enabled": true}, {"key": "arrivalorderairportiata_var", "value": "OMAA", "type": "default", "enabled": true}, {"key": "departureorderairportiata_var", "value": "SCA", "type": "default", "enabled": true}, {"key": "orderitemid_var", "value": "14978", "type": "default", "enabled": true}, {"key": "sector_id_2", "value": "", "type": "any", "enabled": true}, {"key": "CAflightid_var", "value": "9196", "type": "default", "enabled": true}, {"key": "CAsectorid_var", "value": "9194", "type": "default", "enabled": true}, {"key": "CAroutegroupid_var", "value": "9187", "type": "default", "enabled": true}, {"key": "CAincludedsectorid_var", "value": "9204", "type": "default", "enabled": true}, {"key": "airline_category_id_1", "value": "", "type": "any", "enabled": true}, {"key": "randomSkuFirstName", "value": "", "type": "any", "enabled": true}, {"key": "randomSkuInt", "value": "", "type": "any", "enabled": true}, {"key": "existingProductSku", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_01", "value": "", "type": "any", "enabled": true}, {"key": "currency", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_01", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_02", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_02", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_03", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_03", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_04", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_04", "value": "", "type": "any", "enabled": true}, {"key": "Product_ID_05", "value": "", "type": "any", "enabled": true}, {"key": "Product_SKU_05", "value": "", "type": "any", "enabled": true}, {"key": "MultiProduct_Catalog_ID", "value": "", "type": "any", "enabled": true}, {"key": "ui_Template_AC", "value": "", "type": "any", "enabled": true}, {"key": "rootType_AC", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level1", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level2", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level3", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level4", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level5", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_level6", "value": "", "type": "any", "enabled": true}, {"key": "polling_start_time", "value": "", "type": "any", "enabled": true}, {"key": "currentRetry", "value": "", "type": "any", "enabled": true}, {"key": "currentId", "value": "14336", "type": "any", "enabled": true}, {"key": "JWT_TOKEN_DEV", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiIxMTEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNzM0NTE1NTI5LCJleHAiOjI1MzY0MzE0MDB9.Lsyi0OGetsxtnOm0fm6BcLPE_vuhNYhcqYwIa4zCBMM", "type": "default", "enabled": true}, {"key": "product_id1", "value": "57951", "type": "default", "enabled": true}, {"key": "count", "value": "", "type": "any", "enabled": false}, {"key": "errorHandled", "value": "", "type": "any", "enabled": true}, {"key": "store_Catalog_id", "value": "", "type": "any", "enabled": true}, {"key": "productid_exclude", "value": "", "type": "any", "enabled": true}, {"key": "image1200_1", "value": "", "type": "any", "enabled": true}, {"key": "store_Name", "value": "Demo CI Store", "type": "default", "enabled": true}, {"key": "AirlineName", "value": "Demo CI Airline", "type": "default", "enabled": true}, {"key": "product_name", "value": "", "type": "any", "enabled": true}, {"key": "flightId", "value": "", "type": "any", "enabled": true}, {"key": "sectorId", "value": "", "type": "any", "enabled": true}, {"key": "CABeginDate", "value": "", "type": "any", "enabled": true}, {"key": "CAEndDate", "value": "", "type": "any", "enabled": true}, {"key": "airlinecategoryid", "value": "", "type": "any", "enabled": true}, {"key": "airline", "value": "", "type": "default", "enabled": true}, {"key": "errorBypassed", "value": "", "type": "any", "enabled": true}, {"key": "store_id", "value": "", "type": "any", "enabled": true}, {"key": "storeNameMismatch", "value": "", "type": "any", "enabled": true}, {"key": "productID_01", "value": "", "type": "any", "enabled": true}, {"key": "prev_store_id", "value": "", "type": "any", "enabled": true}, {"key": "storeID", "value": "", "type": "any", "enabled": true}, {"key": "storeName1:", "value": "", "type": "any", "enabled": true}, {"key": "storeName1", "value": "", "type": "any", "enabled": true}, {"key": "catalog_Id", "value": "", "type": "any", "enabled": true}, {"key": "routeGroup", "value": "", "type": "any", "enabled": true}, {"key": "sectorid_var", "value": "9204", "type": "default", "enabled": true}, {"key": "sectorid", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_1", "value": "", "type": "any", "enabled": true}, {"key": "sectorid_new", "value": "", "type": "any", "enabled": true}, {"key": "order_id", "value": "", "type": "any", "enabled": true}, {"key": "orderObjectId", "value": "", "type": "any", "enabled": true}, {"key": "lineItemObjectId", "value": "", "type": "any", "enabled": true}, {"key": "ordershippmentId", "value": "", "type": "any", "enabled": true}, {"key": "order_id_2", "value": "", "type": "any", "enabled": true}, {"key": "ordershipmentID", "value": "", "type": "any", "enabled": true}, {"key": "orderObjectID", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id_extra", "value": "", "type": "any", "enabled": true}, {"key": "uniqueString", "value": "", "type": "any", "enabled": true}, {"key": "flightBeginDateTime", "value": "2025-11-05 21:00", "type": "default", "enabled": true}, {"key": "flightEndDateTime", "value": "2026-12-06 17:35", "type": "default", "enabled": true}, {"key": "store_languages", "value": "", "type": "any", "enabled": true}, {"key": "post_request_body", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-05-23T07:34:12.904Z", "_postman_exported_using": "Postman/11.5.1"}