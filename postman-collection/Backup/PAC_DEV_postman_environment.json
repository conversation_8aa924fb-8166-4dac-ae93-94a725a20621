{"id": "52b38f7d-8cdc-45ba-9361-7779fdc97b3b", "name": "PAC DEV", "values": [{"key": "apiUrl", "value": "https://marketplace-dev.nextcloud.aero", "type": "default", "enabled": true}, {"key": "airlineClientId", "value": "a0dfqz0xfl", "type": "default", "enabled": true}, {"key": "airlineClientSecret", "value": "fd$2&du6r1jf4g3#3e$8", "type": "default", "enabled": true}, {"key": "storeClientId", "value": "h7cc8hq2el", "type": "default", "enabled": true}, {"key": "storeClientSecret", "value": "$2lc00mxzvqlkl43s&a-", "type": "default", "enabled": true}, {"key": "airlineToken", "value": "", "type": "default", "enabled": true}, {"key": "storeToken", "value": "", "type": "default", "enabled": true}, {"key": "mytwitterID", "value": "", "type": "any", "enabled": true}, {"key": "testid", "value": "", "type": "any", "enabled": true}, {"key": "id", "value": "", "type": "any", "enabled": true}, {"key": "sector_id", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "sector_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "flight_id", "value": "", "type": "any", "enabled": true}, {"key": "flight_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id", "value": "", "type": "any", "enabled": true}, {"key": "airline_category_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_id", "value": "", "type": "any", "enabled": true}, {"key": "product_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id", "value": "", "type": "any", "enabled": true}, {"key": "store_location_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "fulfillment_id", "value": "", "type": "any", "enabled": true}, {"key": "fulfillment_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "delivery_rule_id", "value": "", "type": "any", "enabled": true}, {"key": "delivery_rule_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "category_id", "value": "", "type": "any", "enabled": true}, {"key": "category_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_type", "value": "", "type": "any", "enabled": true}, {"key": "reservation_start_at ", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end", "value": "", "type": "any", "enabled": true}, {"key": "reservation_end_1 ", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "product_sku1", "value": "", "type": "any", "enabled": true}, {"key": "product_sku2", "value": "", "type": "any", "enabled": true}, {"key": "campaign_id", "value": "", "type": "any", "enabled": true}, {"key": "campaign_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "campaign_type", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku1", "value": "", "type": "any", "enabled": true}, {"key": "campaign_sku2", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method", "value": "", "type": "any", "enabled": true}, {"key": "promotion_type_id", "value": "", "type": "any", "enabled": true}, {"key": "promotion_type_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "promotion_id", "value": "", "type": "any", "enabled": true}, {"key": "promotion_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "delivery_method1", "value": "", "type": "any", "enabled": true}, {"key": "airline_id", "value": "", "type": "any", "enabled": true}, {"key": "future-date", "value": "", "type": "any", "enabled": true}, {"key": "future_date", "value": "", "type": "any", "enabled": true}, {"key": "future_date1", "value": "", "type": "any", "enabled": true}, {"key": "route_group_name", "value": "", "type": "any", "enabled": true}, {"key": "product_id_exclude", "value": "", "type": "any", "enabled": true}, {"key": "sector_id_exclude", "value": "", "type": "any", "enabled": true}, {"key": "cabinclass_id", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id_delete", "value": "", "type": "any", "enabled": true}, {"key": "speciality_attribute_id", "value": "", "type": "any", "enabled": true}, {"key": "catalog_assign_id_get", "value": "", "type": "any", "enabled": true}, {"key": "route_group_id_get", "value": "", "type": "any", "enabled": true}, {"key": "delivery_type", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2023-03-13T08:06:41.001Z", "_postman_exported_using": "Postman/9.24.2"}