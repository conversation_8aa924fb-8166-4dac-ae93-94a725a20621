{"info": {"_postman_id": "b48b7042-e849-46b2-8a73-d41a64dafd59", "name": "Panasonic Core Commerce APIs - DEV", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "23420024"}, "item": [{"name": "Authentication", "item": [{"name": "JAMA_12668089_Authentication - GET", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\":\"{{storeClientId}}\",\r\n    \"clientSecret\":\"{{storeClientSecret}}\"\r\n}\r\n\r\n", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/auth/token"}, "response": []}]}, {"name": "Airline", "item": [{"name": "Route Group", "item": [{"name": "JAMA_12668126_Route Group - GET", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json(); let route_group_id_delete = response.data.route_groups[0].id;console.log(route_group_id_delete);pm.environment.set('route_group_id_delete',route_group_id_delete);", "let route_group_id_get = response.data.route_groups[1].id;console.log(route_group_id_get);pm.environment.set('route_group_id_get',route_group_id_get);", "let route_group_name = response.data.route_groups[2].name;console.log(route_group_name);pm.environment.set('route_group_name',route_group_name);", "", "//let route_group_name = response.data.route_groups[0].name;console.log(route_group_name);pm.environment.set('route_group_name',route_group_name);", "", "", "//let response1 = pm.response.json();let route_group_id_delete = response1.data.route_groups[1].id;console.log(route_group_id_delete);pm.environment.set('route_group_id_delete',route_group_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/routegroups"}, "response": []}, {"name": "JAMA_12668127_Route Group - GET by ID", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/routegroups/{{route_group_id_get}}"}, "response": []}, {"name": "JAMA_12668128_Route Group - ADD", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["let response = pm.response.json();let route_group_id = response.id;console.log(route_group_id);pm.environment.set('route_group_id',route_group_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"RG1{{$randomFirstName}}{{$randomNoun}}\"\r\n  }", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/routegroups"}, "response": []}, {"name": "JAMA_12668130_Route Group - PATCH", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"RG01{{$randomLastName}}{{$randomNoun}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/routegroups/{{route_group_id}}"}, "response": []}, {"name": "JAMA_12668131_Route Group - DELETE", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/routegroups/{{route_group_id}}"}, "response": []}]}, {"name": "Sector", "item": [{"name": "JAMA_12668114_Sector - GET", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "let sector_id_exclude = response.data.sectors[0].id;console.log(sector_id_exclude);pm.environment.set('sector_id_exclude',sector_id_exclude);", "let sector_id_delete = response.data.sectors[1].id;console.log(sector_id_delete);pm.environment.set('sector_id_delete',sector_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/sector"}, "response": []}, {"name": "JAMA_12668116_Sector - ADD", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let sector_id = response.id;console.log(sector_id);pm.environment.set('sector_id',sector_id);", "//let sector_id_delete = response.data.sectors[1].id;console.log(sector_id_delete);pm.environment.set('sector_id_delete',sector_id_delete);", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{airlineToken}}"}}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sectorName\": \"ABU-DHABI-DUBAI{{$randomFirstName}}\",\n  \"summary\": \"summary\",\n  \"routeGroup\": \"{{route_group_name}}\",\n  \"destination\": \"SCA\",\n  \"origin\": \"OMAA\",\n  \"distance\": 900\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/sector"}, "response": []}, {"name": "JAMA_12668117_Sector - PUT", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"sectorName\": \"ABU-DHABI-{{$randomLastName}}-{{count}}\",\r\n  \"summary\": \"summary\",\r\n  \"routeGroup\": \"{{route_group_name}}\",\r\n  \"destination\": \"SCA\",\r\n  \"origin\": \"OMAA\",\r\n  \"distance\": 900\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/sector/{{sector_id}}"}, "response": []}, {"name": "JAMA_12668118_Sector - PATCH", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"routeName\": \"GOA-KOL-2\",\r\n  \"summary\": \"test summary\",\r\n  \"routeGroup\": \"{{route_group_name}}\",\r\n  \"destination\": \"SCA\",\r\n  \"origin\": \"OMAA\",\r\n  \"distance\": 900\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/sector/{{sector_id}}"}, "response": []}, {"name": "JAMA_12668115_Sector - GET by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/sector/{{sector_id}}"}, "response": []}, {"name": "JAMA_12668119_Sector - DELETE", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/sector/{{sector_id}}"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var currentCount = pm.environment.get(\"count\");", "", "// Convert the current count value to an integer", "currentCount = parseInt(currentCount);", "currentCount+=1;", "// Update the environment variable with the new count", "pm.environment.set(\"count\", currentCount.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Flight", "item": [{"name": "JAMA_12668120_Flight - GET", "event": [{"listen": "test", "script": {"exec": ["", "//let response1 = pm.response.json();let flight_id_delete = response1.data.flights[0].id;console.log(flight_id_delete);pm.environment.set('flight_id_delete',flight_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": {"raw": "{{apiUrl}}/marketplace/v1/ams/flight?offset=1", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "ams", "flight"], "query": [{"key": "offset", "value": "1"}]}}, "response": []}, {"name": "JAMA_12668122_Flight - ADD", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let flight_id = response.id;console.log(flight_id);pm.environment.set('flight_id',flight_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"flightRouteNumber\": \"Flt1{{$randomLastName}}{{$randomFirstName}}\",\r\n  \"flightBeginDateTime\": \"2023-01-05 21:00\",\r\n  \"flightEndDateTime\": \"2023-01-06 17:35\",\r\n  \"sectors\": [\r\n    {\r\n      \"id\": {{sector_id_delete}},\r\n      \"sequence\": 1\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/flight"}, "response": []}, {"name": "JAMA_12668123_Flight - PUT", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"flightRouteNumber\": \"Flt2{{$randomFirstName}}{{$randomLastName}}{{$randomNoun}}\",\r\n    \"flightBeginDateTime\": \"2023-01-05 21:00\",\r\n  \"flightEndDateTime\": \"2023-01-06 17:35\",\r\n  \"sectors\": [\r\n    {\r\n      \"id\": {{sector_id_delete}},\r\n      \"sequence\": 1\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/flight/{{flight_id}}"}, "response": []}, {"name": "JAMA_12668124_Flight - PATCH", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"flightRouteNumber\": \"Flt1{{$randomFirstName}}{{$randomLastName}}{{$randomNoun}}\",\r\n     \"flightBeginDateTime\": \"2023-01-05 21:00\",\r\n  \"flightEndDateTime\": \"2023-01-06 17:35\",\r\n  \"sectors\": [\r\n    {\r\n      \"id\": {{sector_id_delete}},\r\n      \"sequence\": 1\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/flight/{{flight_id}}"}, "response": []}, {"name": "JAMA_12668121_Flight - GET by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/flight/{{flight_id}}"}, "response": []}, {"name": "JAMA_12668125_Flight - DELETE", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/flight/{{flight_id}}"}, "response": []}]}, {"name": "AirlineCategory", "item": [{"name": "JAMA_12668104_Airline Category - GET", "event": [{"listen": "test", "script": {"exec": ["let response1 = pm.response.json();let airline_category_id_delete = response1.data.airlineCategories[1].id;console.log(airline_category_id_delete);pm.environment.set('airline_category_id_delete',airline_category_id_delete);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"formatVersion\": \"2\",\r\n    \"name\": {\r\n        \"en\": \"Emirates{{$randomFirstName}}\",\r\n        \"ja\": \"エミレーツ{{$randomFirstName}}\",\r\n        \"de\": \"Emirates{{$randomFirstName}}\"\r\n    },\r\n    \"disclaimer\": {\r\n        \"en\": \"not use in cold {{$randomFirstName}}\",\r\n        \"ja\": \"寒冷地では使用しない {{$randomFirstName}}\",\r\n        \"de\": \"nicht kalt verwenden {{$randomFirstName}}\"\r\n    },\r\n    \"shortDescription\": {\r\n        \"en\": \"Good {{$randomFirstName}}\",\r\n        \"ja\": \"良い {{$randomFirstName}}\",\r\n        \"de\": \"Gut {{$randomFirstName}}\"\r\n    },\r\n    \"description\": {\r\n        \"en\": \"Very Good {{$randomFirstName}}\",\r\n        \"ja\": \"とても良い {{$randomFirstName}}\",\r\n        \"de\": \"sehr gut{{$randomFirstName}}\"\r\n    },\r\n    \"url\": \"http://test22.com{{$randomFirstName}}\",\r\n    \"uiTemplate\": null,\r\n    \"rootType\": null,\r\n    \"sequenceNumber\": 1,\r\n    \"bannerImage\": \"https://placehold.jp/3160x632.png\",\r\n    \"backgroundImage\": \"https://placehold.jp/3840x2160.png\",\r\n    \"parentCategory\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/airline-category"}, "response": []}, {"name": "JAMA_12668105_Airline Category - ADD", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let airline_category_id = response.id;console.log(airline_category_id);pm.environment.set('airline_category_id',airline_category_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"formatVersion\": \"2\",\r\n    \"name\": {\r\n        \"en\": \"Emirates{{$randomFirstName}}\",\r\n        \"ja\": \"エミレーツ{{$randomFirstName}}\",\r\n        \"de\": \"Emirates{{$randomFirstName}}\"\r\n    },\r\n    \"disclaimer\": {\r\n        \"en\": \"not use in cold {{$randomFirstName}}\",\r\n        \"ja\": \"寒冷地では使用しない {{$randomFirstName}}\",\r\n        \"de\": \"nicht kalt verwenden {{$randomFirstName}}\"\r\n    },\r\n    \"shortDescription\": {\r\n        \"en\": \"Good {{$randomFirstName}}\",\r\n        \"ja\": \"良い {{$randomFirstName}}\",\r\n        \"de\": \"Gut {{$randomFirstName}}\"\r\n    },\r\n    \"description\": {\r\n        \"en\": \"Very Good {{$randomFirstName}}\",\r\n        \"ja\": \"とても良い {{$randomFirstName}}\",\r\n        \"de\": \"sehr gut{{$randomFirstName}}\"\r\n    },\r\n    \"url\": \"http://test22.com{{$randomFirstName}}\",\r\n    \"uiTemplate\": null,\r\n    \"rootType\": null,\r\n    \"sequenceNumber\": 1,\r\n    \"bannerImage\": \"https://placehold.jp/3160x632.png\",\r\n    \"backgroundImage\": \"https://placehold.jp/3840x2160.png\",\r\n    \"parentCategory\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/airline-category"}, "response": []}, {"name": "JAMA_12668106_Airline Category - PUT", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"formatVersion\": \"2\",\r\n    \"name\": {\r\n        \"en\": \"Emirates{{$randomLastName}}\",\r\n        \"ja\": \"エミレーツ{{$randomLastName}}\",\r\n        \"de\": \"Emirates{{$randomLastName}}\"\r\n    },\r\n    \"disclaimer\": {\r\n        \"en\": \"not use in cold {{$randomLastName}}\",\r\n        \"ja\": \"寒冷地では使用しない {{$randomLastName}}\",\r\n        \"de\": \"nicht kalt verwenden {{$randomLastName}}\"\r\n    },\r\n    \"shortDescription\": {\r\n        \"en\": \"Good {{$randomLastName}}\",\r\n        \"ja\": \"良い {{$randomLastName}}\",\r\n        \"de\": \"Gut {{$randomLastName}}\"\r\n    },\r\n    \"description\": {\r\n        \"en\": \"Very Good {{$randomLastName}}\",\r\n        \"ja\": \"とても良い {{$randomLastName}}\",\r\n        \"de\": \"sehr gut{{$randomLastName}}\"\r\n    },\r\n    \"url\": \"http://test22.com{{$randomLastName}}\",\r\n    \"uiTemplate\": null,\r\n    \"rootType\": null,\r\n    \"sequenceNumber\": 1,\r\n     \"bannerImage\": \"https://placehold.jp/3160x632.png\",\r\n    \"backgroundImage\": \"https://placehold.jp/3840x2160.png\",\r\n    \"parentCategory\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/airline-category/{{airline_category_id}}"}, "response": []}, {"name": "JAMA_12668107_Airline Category - PATCH", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"url\": \"http://test22da.com/{{$randomFirstName}}\",\r\n    \"bannerImage\": \"https://placehold.jp/3160x632.png\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/airline-category/{{airline_category_id}}"}, "response": []}, {"name": "JAMA_12704015_Get Airline Category By Id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/airline-category/{{airline_category_id}}"}, "response": []}, {"name": "JAMA_12668108_Airline Category - DELETE", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/airline-category/{{airline_category_id}}"}, "response": []}]}, {"name": "Cabin Class", "item": [{"name": "JAMA_12668154_Cabin Class - GET", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "let cabinclass_id = response.data.cabinClasses[0].id;console.log(cabinclass_id);pm.environment.set('cabinclass_id',cabinclass_id);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/cabinClass"}, "response": []}]}, {"name": "Catalog", "item": [{"name": "JAMA_12704021_Get Store Catalog", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/store/1254/catalog"}, "response": []}]}, {"name": "Aircraft", "item": [{"name": "JAMA_12668132_Aircraft - GET", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{apiUrl}}/marketplace/v1/ams/aircrafts?offset=1", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "ams", "aircrafts"], "query": [{"key": "offset", "value": "1"}]}}, "response": []}]}, {"name": "Airport", "item": [{"name": "JAMA_12668134_Get All Airports", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/airports"}, "response": []}]}, {"name": "JAMA_12668134_Airline - GET", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let airline_id = response.data.airline.id;console.log(airline_id);pm.environment.set('airline_id',airline_id);", "//let response1 = pm.response.json();let airline_id_delete = response1.data.products[1].id;console.log(airline_id_delete);pm.environment.set('airline_id_delete',airline_id_delete);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/airline"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const options = {", "    'method': 'POST',", "    'url': `${pm.environment.get('apiUrl')}/marketplace/v1/auth/token`,", "    'header': {", "        'Content-Type': 'application/x-www-form-urlencoded'", "    },", "    'body' : {", "        mode: 'raw',", "        raw: {", "            'clientId': pm.environment.get('airlineClientId'),", "            'clientSecret': pm.environment.get('airlineClientSecret')", "        }", "    }", "};", "pm.sendRequest(options, function (err, response) {", "    // console.log(\"get accesstoken\");", "    // console.log(response.json()[\"data\"][\"token\"]);", "", "    pm.environment.set('airlineToken', response.json()['data']['token']);", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Store", "item": [{"name": "Fulfillment", "item": [{"name": "JAMA_12668092_Fulfillment - GET", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let fulfillment_id = response.data.fulfillments[0].id;console.log(fulfillment_id);pm.environment.set('fulfillment_id',fulfillment_id);", "let response1 = pm.response.json();let fulfillment_id_delete = response1.data.fulfillments[1].id;console.log(fulfillment_id_delete);pm.environment.set('fulfillment_id_delete',fulfillment_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/fulfillment"}, "response": []}, {"name": "Get Products", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "let response2 = pm.response.json();let product_type = response.data.products[0].productType;console.log(product_type);pm.environment.set('product_type',product_type);", "let response3 = pm.response.json();let product_sku1 = response.data.products[0].sku;console.log(product_sku1);pm.environment.set('product_sku1',product_sku1);", "let response4 = pm.response.json();let product_sku2 = response.data.products[1].sku;console.log(product_sku2);pm.environment.set('product_sku2',product_sku2);", "let response5 = pm.response.json();let delivery_method = response.data.products[0].deliveryMethod[0];console.log(delivery_method);pm.environment.set('delivery_method',delivery_method);", "let response6 = pm.response.json();let delivery_method1 = response.data.products[0].deliveryMethod[1];console.log(delivery_method1);pm.environment.set('delivery_method1',delivery_method1);", "let response7 = pm.response.json();let product_id_exclude = response.data.products[0].id;console.log(product_id_exclude);pm.environment.set('product_id_exclude',product_id_exclude);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": {"raw": "{{apiUrl}}/marketplace/v1/pim/product", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "pim", "product"], "query": [{"key": "offset", "value": "2", "disabled": true}]}}, "response": []}]}, {"name": "Inventoy Check Airside", "item": [{"name": "JAMA_12668153_Inventory Check Airside - GET", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiIxMTEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNjgxODk5MTM3LCJleHAiOjI1MzY0MzE0MDB9.JKr-0lM3B_h-IB_LZxXKxsZSOAEuwnFH3rPCX96rGfk", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"home\": {\n        \"icaoCode\": \"OMAM\",\n        \"items\": [\n            {\n                \"vendorProductVariantId\": \"123\",\n                \"quantity\": 5\n            },\n            {\n                \"vendorProductVariantId\": \"456\",\n                \"quantity\": 5\n            },\n            {\n                \"vendorProductVariantId\": \"789\",\n                \"quantity\": 15\n            },\n            {\n                \"vendorProductVariantId\": \"888\",\n                \"quantity\": 15\n            }\n        ]\n    },\n    \"inflightFutureStandard\": {\n        \"icaoCode\": \"OMAM\",\n        \"items\": [\n            {\n                \"vendorProductVariantId\": \"123\",\n                \"quantity\": 5\n            },\n            {\n                \"vendorProductVariantId\": \"456\",\n                \"quantity\": 5\n            }\n        ]\n    },\n    \"inflightFutureExpress\": {\n        \"icaoCode\": \"WSSS\",\n        \"items\": [\n            {\n                \"vendorProductVariantId\": \"123\",\n                \"quantity\": 5\n            },\n            {\n                \"vendorProductVariantId\": \"456\",\n                \"quantity\": 5\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/inventoryCheck"}, "response": []}]}, {"name": "Store Location", "item": [{"name": "JAMA_12668147_Store Location - GET", "event": [{"listen": "test", "script": {"exec": ["let response1 = pm.response.json();let store_location_id_delete = response1.data.storeLocation[0].id;console.log(store_location_id_delete);pm.environment.set('store_location_id_delete',store_location_id_delete);", "//let response1 = pm.response.json();let store_location_id_delete = response1.data.storeLocation[1].id;console.log(store_location_id_delete);pm.environment.set('store_location_id_delete',store_location_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": {"raw": "{{apiUrl}}/marketplace/v1/store/location?offset=1", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "store", "location"], "query": [{"key": "offset", "value": "1"}]}}, "response": []}, {"name": "JAMA_12668149_Store Location - ADD", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let store_location_id = response.data.storeLocation[0].id;console.log(store_location_id);pm.environment.set('store_location_id',store_location_id);", "", "let response = pm.response.json();let store_location_id = response.id;console.log(store_location_id);pm.environment.set('store_location_id',store_location_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Defaulaaat{{$randomFirstName}}x-{{count}}\",\r\n    \"fulfilmentOption\": [\r\n        \"{{fulfillment_id}}\"\r\n    ],\r\n    \"icaoCode\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/location"}, "response": []}, {"name": "JAMA_12668150_Store Location - PUT", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Defaulaaat{{$randomLastName}}xx\",\r\n    \"fulfilmentOption\": [\r\n        {{fulfillment_id_delete}}\r\n    ],\r\n    \"icaoCode\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/location/{{store_location_id}}"}, "response": []}, {"name": "JAMA_12668148_Store Location - GET by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/store/location/{{store_location_id}}"}, "response": []}, {"name": "JAMA_12668151_Store Location - PATCH", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>{{$randomFirstName}}zz-001\",\r\n    \"icaoCode\": \"LAX\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/location/{{store_location_id}}"}, "response": []}, {"name": "JAMA_12668152_Store Location - DELETE", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"De<PERSON><PERSON>aat{{$randomLastName}}\",\r\n    \"fulfilmentOption\": [\r\n        {{fulfillment_id_delete}}\r\n    ],\r\n    \"icaoCode\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/location/{{store_location_id}}"}, "response": []}], "auth": {"type": "bearer", "bearer": {}}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var currentCount = pm.environment.get(\"count\");", "", "// Convert the current count value to an integer", "currentCount = parseInt(currentCount);", "currentCount+=1;", "// Update the environment variable with the new count", "pm.environment.set(\"count\", currentCount.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Categories", "item": [{"name": "JAMA_12668091_Categories - GET", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let category_id = response.data.categories[0].id;console.log(category_id);pm.environment.set('category_id',category_id);", "let response1 = pm.response.json();let category_id_delete = response1.data.categories[1].id;console.log(category_id_delete);pm.environment.set('category_id_delete',category_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": {"raw": "{{apiUrl}}/marketplace/v1/pim/categories?formatVersion=2", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "pim", "categories"], "query": [{"key": "formatVersion", "value": "2"}]}}, "response": []}]}, {"name": "Catalog", "item": [{"name": "JAMA_12668109_Catalog - GET", "event": [{"listen": "test", "script": {"exec": ["let response1 = pm.response.json();let catalog_id_delete = response1.data.catalogs[1].id;console.log(catalog_id_delete);pm.environment.set('catalog_id_delete',catalog_id_delete);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/catalog"}, "response": []}, {"name": "JAMA_12668110_Catalog - ADD", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["let response = pm.response.json();let catalog_id = response.id;console.log(catalog_id);pm.environment.set('catalog_id',catalog_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"formatVersion\": \"2\",\r\n    \"name\": {\r\n        \"en\": \"catalog_{{$randomFirstName}}\",\r\n        \"ja\": \"catalog-test-ja\",\r\n        \"it\": \"catalog-test-it\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"products\": [\r\n        \"{{product_sku2}}\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/catalog"}, "response": []}, {"name": "JAMA_12668111_Catalog - PUT", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"catalog 01{{$randomLastName}}\",\r\n  \"products\": [\r\n    \"{{product_sku2}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/catalog/{{catalog_id}}"}, "response": []}, {"name": "JAMA_12668112_Catalog - PATCH", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n   \"products\": [\r\n    \"{{product_sku2}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/catalog/{{catalog_id}}"}, "response": []}, {"name": "JAMA_12704026_Get Catalog By Id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/catalog/{{catalog_id}}"}, "response": []}, {"name": "JAMA_12668113_Catalog - DELETE", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "default"}], "url": "{{apiUrl}}/marketplace/v1/pim/catalog/{{catalog_id}}"}, "response": []}]}, {"name": "Promotion Type", "item": [{"name": "JAMA_12668161_Promotion Type - GET", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let promotion_type_id = response.data.promotionType[0].id;console.log(promotion_type_id);pm.environment.set('promotion_type_id',promotion_type_id);", "let response1 = pm.response.json();let promotion_type_id_delete = response1.data.promotionType[1].id;console.log(promotion_type_id_delete);pm.environment.set('promotion_type_id_delete',promotion_type_id_delete);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6OTU0LCJ0eXBlIjoiYWlybGluZSIsImNyZWF0ZWQiOjE2Mjg2NjEyODEsImV4cGlyeSI6MTYyODY2MjE4MSwiZG9tYWluIjoiIn0.EDFSGlIZ9j6GXT9WRt_LnIWI4IeQM2pgdCaE_ejiuCQ", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/price/promotionType"}, "response": []}, {"name": "JAMA_12668162_Promotion Type - GET by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6OTU0LCJ0eXBlIjoiYWlybGluZSIsImNyZWF0ZWQiOjE2Mjg2NjEyODEsImV4cGlyeSI6MTYyODY2MjE4MSwiZG9tYWluIjoiIn0.EDFSGlIZ9j6GXT9WRt_LnIWI4IeQM2pgdCaE_ejiuCQ", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/price/promotionType/{{promotion_type_id}}"}, "response": []}], "auth": {"type": "bearer", "bearer": {"token": "{{storeToken}}"}}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "SpecialityAttribute", "item": [{"name": "JAMA_12668098_Speciality Attribute - GET", "event": [{"listen": "test", "script": {"exec": ["let response1 = pm.response.json();let speciality_attribute_id_delete = response1.data.SpecialityAttributes[1].id;console.log(speciality_attribute_id_delete);pm.environment.set('speciality_attribute_id_delete',speciality_attribute_id_delete);", "let speciality_attribute_id_1 = response1.data.SpecialityAttributes[1].id;console.log(speciality_attribute_id_1);pm.environment.set('speciality_attribute_id_1',speciality_attribute_id_1);", "//let speciality_attribute_id_2 = response1.data.SpecialityAttributes[2].id;console.log(speciality_attribute_id_2);pm.environment.set('speciality_attribute_id_2',speciality_attribute_id_2);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": "{{apiUrl}}/marketplace/v1/pim/specialityattributes"}, "response": []}, {"name": "JAMA_12668099_Speciality Attribute - ADD", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let speciality_attribute_id = response.id;console.log(speciality_attribute_id);pm.environment.set('speciality_attribute_id',speciality_attribute_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"formatVersion\": 2,\n    \"shortDescription\": {\n        \"en\": \"Store category publis{{$randomFirstName}}{{$randomLastName}}\",\n        \"ar\": \"Shampoos zijn een essentieel ondl {{$randomLastName}}{{$randomFirstName}}\",\n        \"el\": \"Shampoos zijn eenverwijderen en je schoon fsdf fsdf sfsdf{{$randomLastName}}{{$randomFirstName}}\",\n        \"nl\": \"Shampoos zij onderdeel van alle haarverze hoofdhuid reinigen haarophoping {{$randomLastName}}verwijderen en je schoon{{$randomFirstName}}\",\n        \"he\": \"Shampnhoofdhuid reinigen haarophopinon fsdf dffsd sdfsdf sdfsd {{$randomLastName}} fsdf sfsdf{{$randomFirstName}}\",\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों क ने {{$randomLastName}}पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।{{$randomFirstName}}\"\n    },\n    \"disclaimer\": {\n        \"en\": \"ojfojw\",\n        \"ja\": \"nbbn\"\n    },\n    \"image\": \"https://placehold.jp/256x256.png\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/specialityattributes"}, "response": []}, {"name": "JAMA_12668100_Speciality Attribute - PUT", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"formatVersion\": 2,\n    \"shortDescription\": {\n         \"en\": \"Store category publis{{$randomLastName}}\",\n        \"ar\": \"Shampoos zijn een essentieel ondl van alle haarverzorgingsregimes{{$randomLastName}}\",\n        \"el\": \"Shampoos zijn eenverwijderen en je schoon fsdf fsdf sfsdf{{$randomLastName}}\",\n        \"nl\": \"Shampoos zij onderdeel van alle haarverze hoofdhuid reinigen haarophoping verwijderen en je schoon{{$randomLastName}}\",\n        \"he\": \"Shampnhoofdhuid reinigen haarophopinon fsdf dffsd sdfsdf sdfsd sdfsdv sdfsdv sdfsdvsdfsd fsdf sfsdf{{$randomLastName}}\",\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों क ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।{{$randomLastName}}\"\n    },\n    \"disclaimer\": {\n        \"en\": \"ojfojw\",\n        \"ja\": \"nbbn\"\n    },\n    \"image\": \"https://placehold.jp/256x256.png\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/specialityattributes/{{speciality_attribute_id}}"}, "response": []}, {"name": "JAMA_12668101_Speciality Attribute - PATCH", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"image\": \"https://placehold.jp/256x256.png\" \n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/specialityattributes/{{speciality_attribute_id}}"}, "response": []}, {"name": "JAMA_12704031_Get  SpecialityAttribute By Id", "request": {"method": "GET", "header": [], "url": "{{apiUrl}}/marketplace/v1/pim/specialityattributes/{{speciality_attribute_id}}"}, "response": []}, {"name": "JAMA_12668102_Speciality Attribute - DELETE", "request": {"method": "DELETE", "header": [], "url": "{{apiUrl}}/marketplace/v1/pim/specialityattributes/{{speciality_attribute_id}}"}, "response": []}], "auth": {"type": "bearer", "bearer": {"token": "{{storeToken}}"}}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Promotion", "item": [{"name": "JAMA_12668155_GET Promotion", "event": [{"listen": "test", "script": {"exec": ["let response1 = pm.response.json();let promotion_id_delete = response1.data.promotions[1].id;console.log(promotion_id_delete);pm.environment.set('promotion_id_delete',promotion_id_delete);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": "{{apiUrl}}/marketplace/v1/price/promotion"}, "response": []}, {"name": "JAMA_12668157_Promotion - ADD", "event": [{"listen": "prerequest", "script": {"exec": ["var dateNow = new Date();  ", "//var twoWeeksFutureDate = new Date(dateNow.setDate(dateNow.getDate() + 1));", "var FutureDate = new Date(dateNow.setDate(dateNow.getDate() + 1)).toISOString().slice(0, 10);", "", "postman.setEnvironmentVariable(\"future_date\", FutureDate);", "", "", "var FutureDate1 = new Date(dateNow.setDate(dateNow.getDate() + 2)).toISOString().slice(0, 10);", "", "postman.setEnvironmentVariable(\"future_date1\", FutureDate1);", "//postman.globals.set(\"today\", twoWeeksFutureDate().format(\"MM/DD/YYYY\"));", ""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["let response = pm.response.json();let promotion_id = response.id;console.log(promotion_id);pm.environment.set('promotion_id',promotion_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"FP51{{$randomFirstName}}\",\n    \"description\": \"Buy One Veg Sandwich and Get One Sandwich Free{{$randomFirstName}}\",\n    \"disclaimer\": \"You need to Order 2 Quantity{{$randomFirstName}}\",\n    \"airline\": {{airline_id}},\n    \"startDate\": \"{{future_date}}\",\n    \"endDate\": \"{{future_date1}}\",\n    \"isActive\": false,\n    \"couponType\": \"One Time\",\n    \"couponCode\": \"BOGO\",\n    \"couponQuantityLimit\": \"6\",\n    \"oneRedemptionPerCustomer\": true,\n    \"PromotionCombinability\": false,\n    \"promotionType\": 390,\n    \"promotionApplicableOn\": \"Catalog\",\n    \"applicableCatalog\": 334,\n    \"applicableProducts\": [\n        {{product_id}}\n    ],\n    \"productBundle\": [],\n    \"productDisclaimer\": \"string\",\n    \"exclusionList\": [\n        \n    ],\n    \"minimumAmountSpent\": 7,\n    \"minimumQuantityBought\": 1,\n    \"maximumQuantityBought\": 1,\n    \"maxQuantityPassengersCanPurchase\": 1,\n    \"qualifyingItem\": 862,\n    \"qualifyingItemDescription\": \"test\",\n    \"qualifyingItemQuantity\": 12,\n    \"itemDiscount\": {\n        \"value\": 12,\n        \"discountType\": \"Amount\"\n    },\n    \"cartDiscount\": {\n        \"value\": 12,\n        \"discountType\": \"Amount\"\n    },\n    \"freeProduct\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/price/promotion"}, "response": []}, {"name": "JAMA_13072959_Promotion - PUT", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"FP{{$randomFirstName}}\",\n    \"description\": \"Buy One Veg Sandwich and Get{{$randomFirstName}}\",\n    \"disclaimer\": \"You need to Order 2 Quantity{{$randomFirstName}}\",\n    \"airline\": {{airline_id}},\n    \"startDate\": \"{{future_date}}\",\n    \"endDate\": \"{{future_date1}}\",\n    \"isActive\": false,\n    \"couponType\": \"One Time\",\n    \"couponCode\": \"BOGO\",\n    \"couponQuantityLimit\": \"6\",\n    \"oneRedemptionPerCustomer\": true,\n    \"PromotionCombinability\": false,\n    \"promotionType\": 390,\n    \"promotionApplicableOn\": \"Catalog\",\n    \"applicableCatalog\": 334,\n    \"applicableProducts\": [\n        {{product_id}}\n    ],\n    \"productBundle\": [],\n    \"productDisclaimer\": \"string\",\n    \"exclusionList\": [\n        \n    ],\n    \"minimumAmountSpent\": 7,\n    \"minimumQuantityBought\": 1,\n    \"maximumQuantityBought\": 1,\n    \"maxQuantityPassengersCanPurchase\": 1,\n    \"qualifyingItem\": 862,\n    \"qualifyingItemDescription\": \"test\",\n    \"qualifyingItemQuantity\": 12,\n    \"itemDiscount\": {\n        \"value\": 12,\n        \"discountType\": \"Amount\"\n    },\n    \"cartDiscount\": {\n        \"value\": 12,\n        \"discountType\": \"Amount\"\n    },\n    \"freeProduct\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/price/promotion/{{promotion_id}}"}, "response": []}, {"name": "JAMA_13072958_Promotion - PATCH", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n                \"name\": \"FLAT 30% on BUY 2{{$randomLastName}}\"\n                \n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/price/promotion/{{promotion_id}}"}, "response": []}, {"name": "JAMA_12668156_Promotion - GET by ID", "request": {"method": "GET", "header": [], "url": "{{apiUrl}}/marketplace/v1/price/promotion/{{promotion_id}}"}, "response": []}, {"name": "JAMA_12668160_Promotion - DELETE", "request": {"method": "DELETE", "header": [], "url": "{{apiUrl}}/marketplace/v1/price/promotion/{{promotion_id}}"}, "response": []}], "auth": {"type": "bearer", "bearer": {"token": "{{storeToken}}"}}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Campaign", "item": [{"name": "JAMA_12668163_Get Campaign", "event": [{"listen": "test", "script": {"exec": ["let response1 = pm.response.json();let campaign_id_delete = response1.data.campaigns[1].id;console.log(campaign_id_delete);pm.environment.set('campaign_id_delete',campaign_id_delete);", "let response2 = pm.response.json();let campaign_type = response1.data.campaigns[0].campaignType;console.log(campaign_type);pm.environment.set('campaign_type',campaign_type);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": "{{apiUrl}}/marketplace/v1/price/campaign"}, "response": []}, {"name": "JAMA_12668165_Add Campaign", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let campaign_id = response.id;console.log(campaign_id);pm.environment.set('campaign_id',campaign_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"campaignName\": \"new 1122{{$randomFirstName}}\",\n    \"description\": \"test123{{$randomFirstName}}\",\n    \"startDate\": \"{{future_date}}\",\n    \"endDate\": \"{{future_date1}}\",\n    \"isActive\": \"\",\n    \"airline\": {{airline_id}},\n    \"campaignType\": \"Category\",\n    \"products\": [\n        {{product_id}}\n    ],\n    \"category\": {{category_id}},\n    \"catalog\": [\n        {{catalog_id}}\n    ],\n    \"promotion\": [\n        {{promotion_id}}\n    ],\n    \"campaignBanners\": [\n        {\n            \"url\": \"https://images.pexels.com/photos/1214259/pexels-photo-1214259.jpeg\",\n            \"bannerPosition\": \"Top\"\n        }\n    ],\n    \"bannerPosition\": \"Middle\",\n    \"displayDevices\": [\n        \"PED\",\n        \"Seatback\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/price/campaign"}, "response": []}, {"name": "JAMA_12668164_Get Campaign By Id", "request": {"method": "GET", "header": [], "url": "{{apiUrl}}/marketplace/v1/price/campaign/{{campaign_id}}"}, "response": []}, {"name": "JAMA_12668166_Update Campaign", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"campaignName\": \"new 1122{{$randomLastName}}xx\",\n    \"description\": \"test123{{$randomLastName}}ss\",\n    \"startDate\": \"{{future_date}}\",\n    \"endDate\": \"{{future_date1}}\",\n    \"isActive\": \"\",\n    \"airline\": {{airline_id}},\n    \"campaignType\": \"Category\",\n    \"products\": [\n        {{product_id}}\n    ],\n    \"category\": {{category_id}},\n    \"catalog\": [\n        {{catalog_id}}\n    ],\n    \"promotion\": [\n        {{promotion_id}}\n    ],\n    \"campaignBanners\": [\n        {\n            \"url\": \"https://images.pexels.com/photos/1214259/pexels-photo-1214259.jpeg\",\n            \"bannerPosition\": \"Top\"\n        }\n    ],\n    \"bannerPosition\": \"Middle\",\n    \"displayDevices\": [\n        \"PED\",\n        \"Seatback\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/price/campaign/{{campaign_id}}"}, "response": []}, {"name": "JAMA_12668167_PATCH Campaign", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"campaignName\": \"Festival1122{{$randomFirstName}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/price/campaign/{{campaign_id}}"}, "response": []}, {"name": "JAMA_12668168_Campaign Delete", "event": [{"listen": "test", "script": {"exec": ["//let response1 = pm.response.json();let campaign_id_delete = response1.data.campaigns[1].id;console.log(campaign_id_delete);pm.environment.set('campaign_id_delete',campaign_id_delete);", "//let response2 = pm.response.json();let campaign_type = response1.data.campaigns[0].campaignType;console.log(campaign_type);pm.environment.set('campaign_type',campaign_type);", ""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": "{{apiUrl}}/marketplace/v1/price/campaign/{{campaign_id}}"}, "response": []}, {"name": "JAMA_12668143_Add/Update Tax percent", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"taxPercentage\": 2\r\n \r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/tax"}, "response": []}], "auth": {"type": "bearer", "bearer": {"token": "{{storeToken}}"}}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var currentCount = pm.environment.get(\"count\");", "", "// Convert the current count value to an integer", "currentCount = parseInt(currentCount);", "currentCount+=1;", "// Update the environment variable with the new count", "pm.environment.set(\"count\", currentCount.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Orders", "item": [{"name": "JAMA_12668175_Consumer Order", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiIxMTEiLCJzdWIiOiJhaXJsaW5lX2VuZF91c2VyIiwiaWF0IjoxNjgxODk5MTM3LCJleHAiOjI1MzY0MzE0MDB9.JKr-0lM3B_h-IB_LZxXKxsZSOAEuwnFH3rPCX96rGfk", "type": "default"}], "body": {"mode": "raw", "raw": "{\r\n    \"meta\": {\r\n        \"airlineCodeICAO\": \"111\",\r\n        \"flightNumber\": \"\",\r\n        \"arrivalAirportCodeIATA\": \"DXB\",\r\n        \"departureAirportCodeIATA\": \"BOB\",\r\n        \"departTimeStamp\": \"2023-03-24T17:39:51.590+00:00\",\r\n        \"arrivalTimeStamp\": \"2023-03-24T17:39:51.590+00:00\"\r\n    },\r\n    \"orders\": [\r\n        {\r\n            \"orderKey\": \"3328 Failed flipkart order 7 with INTERNAL payment{{$randomFirstName}}\",\r\n            \"externalId\": \"3328 flipkart order 7{{$randomFirstName}}\",\r\n            \"basketKey\": \"3328 flipkart order 7{{$randomFirstName}}\",\r\n            \"orderProvider\": \"3328 1234bcc7\",\r\n            \"modifiedTime\": \"2023-05-24T17:39:51.590+00:00\",\r\n            \"createdTime\": \"2023-05-24T17:39:51.287+00:00\",\r\n            \"lineItems\": [\r\n                {\r\n                    \"title\": \"KRIS ⋮ JUNIOR POLAR BEAR BY JELLYCAT®{{$randomFirstName}}\",\r\n                    \"PACVariantID\": \"11463\",\r\n                    \"retailerCode\": \"270\",\r\n                    \"fulfillmentType\": \"inHouse\",\r\n                    \"associateStore\": \"270\",\r\n                    \"alwaysInStock\": false,\r\n                    \"imageUrl\": \"https://urldefense.com/v3/__https://images.test/file.jpg__;!!CHviNMnO-bCPlQ!YQQYcn0k-FkgZAxjY4diKLfYFLpw--MNuY6FP7kTSlRxzDTJdKXJmj_JTuY8QT59WP65gaNm3hXQtZo7TW3uqgVa7azD8StAuQ$ \",\r\n                    \"vendorProductVariantID\": \"11463\",\r\n                    \"unitPrice\": {\r\n                        \"value\": 49,\r\n                        \"currency\": \"USD\"\r\n                    },\r\n                    \"unitTax\": {\r\n                        \"value\": 10,\r\n                        \"currency\": \"USD\"\r\n                    },\r\n                    \"unitDiscount\": {\r\n                        \"value\": 4.9,\r\n                        \"currency\": \"USD\"\r\n                    },\r\n                    \"unitNet\": {\r\n                        \"value\": 44.1,\r\n                        \"currency\": \"USD\"\r\n                    },\r\n                    \"unitGross\": {\r\n                        \"value\": 44.1,\r\n                        \"currency\": \"USD\"\r\n                    },\r\n                    \"quantity\": 2,\r\n                    \"status\": \"PENDING\",\r\n                    \"discountTotal\": {\r\n                        \"currency\": \"USD\",\r\n                        \"value\": 9.8\r\n                    },\r\n                    \"taxTotal\": {\r\n                        \"value\": 10,\r\n                        \"currency\": \"USD\"\r\n                    },\r\n                    \"discountAdjustments\": [\r\n                        {\r\n                            \"discountAmount\": {\r\n                                \"currency\": \"USD\",\r\n                                \"value\": 12\r\n                            },\r\n                            \"adjustType\": \"DISCOUNT\",\r\n                            \"rate\": 10,\r\n                            \"promotionCode\": \"asd\",\r\n                            \"promotionName\": \"Percentage discount 10%\"\r\n                        }\r\n                    ],\r\n                    \"taxAdjustments\": [\r\n                        {\r\n                            \"type\": \"shipping_rule_tax\",\r\n                            \"taxAmount\": {\r\n                                \"currency\": \"USD\",\r\n                                \"value\": 12\r\n                            },\r\n                            \"rate\": 7\r\n                        }\r\n                    ],\r\n                    \"salePrice\": {\r\n                        \"value\": 49,\r\n                        \"currency\": \"USD\"\r\n                    },\r\n                    \"key\": \"45052f76-fcc3-4e15-bdee-439f1b94105\"\r\n                }\r\n            ],\r\n            \"status\": \"CALL_CREW\",\r\n            \"payment\": [\r\n                {\r\n                    \"paymentService\": \"INTERNAL\",\r\n                    \"paymentMethod\": \"CARD\",\r\n                    \"authAmount\": {\r\n                        \"currency\": \"USD\",\r\n                        \"value\": 230\r\n                    },\r\n                    \"paymentId\": \"BBAA-0324111-88\",\r\n                    \"status\": \"AUTHORIZED\",\r\n                    \"billingAddress\": {\r\n                        \"salutation\": \"dsa\",\r\n                        \"title\": \"MR\",\r\n                        \"firstName\": \"p\",\r\n                        \"middleName\": \"dsa\",\r\n                        \"lastName\": \"m\",\r\n                        \"address1\": \"5\",\r\n                        \"address2\": \"dsa\",\r\n                        \"city\": \"r\",\r\n                        \"state\": \"state1\",\r\n                        \"postalCode\": \"8\",\r\n                        \"countryCode\": \"US\",\r\n                        \"email\": \"<EMAIL>\",\r\n                        \"phone\": \"+39 2344\"\r\n                    },\r\n                    \"technicalServiceProviderTransactionId\": \"20220324173949153132\",\r\n                    \"gatewayTransactionId\": \"*********\"\r\n                }\r\n            ],\r\n            \"shipments\": [\r\n                {\r\n                    \"id\": \"cf57e776-4ede-462b-b88a-ff9a9d79a0e\",\r\n                    \"rate\": {\r\n                        \"currency\": \"USD\",\r\n                        \"value\": 50\r\n                    },\r\n                    \"shippingMethod\": \"STANDARD\",\r\n                    \"carrier\": \"DHL\",\r\n                    \"taxTotal\": [\r\n                        {\r\n                            \"currency\": \"USD\",\r\n                            \"value\": 12\r\n                        }\r\n                    ],\r\n                    \"address\": {\r\n                        \"salutation\": \"\",\r\n                        \"title\": \"\",\r\n                        \"firstName\": \"p\",\r\n                        \"middleName\": \"\",\r\n                        \"lastName\": \"m\",\r\n                        \"address1\": \"5\",\r\n                        \"address2\": \"\",\r\n                        \"city\": \"r\",\r\n                        \"state\": \"cal\",\r\n                        \"postalCode\": \"8\",\r\n                        \"countryCode\": \"US\",\r\n                        \"email\": \"<EMAIL>\",\r\n                        \"phone\": \"+39 2345\",\r\n                        \"seatClass\":\"demo\",\r\n                        \"seatNumber\":\"demo\"\r\n                    },\r\n                    \"itemKeys\": [\r\n                        \"45052f76-fcc3-4e15-bdee-439f1b94105\"\r\n                    ],\r\n                    \"item\": [\r\n                        \"210740\"\r\n                    ],\r\n                    \"mode\": \"home_delivery\"\r\n                }\r\n            ],\r\n            \"orderSummary\": {\r\n                \"grossTotal\": {\r\n                    \"currency\": \"USD\",\r\n                    \"value\": 200\r\n                },\r\n                \"discounts\": [\r\n                    {\r\n                        \"discountAmount\": {\r\n                            \"value\": 20,\r\n                            \"currency\": \"USD\"\r\n                        },\r\n                        \"promotionCode\": \"New\",\r\n                        \"promotionName\": \"Percentage discount 10%\",\r\n                        \"rate\": 0\r\n                    }\r\n                ],\r\n                \"adjustmentTotal\": {\r\n                    \"currency\": \"USD\",\r\n                    \"value\": 20\r\n                },\r\n                \"taxes\": [\r\n                    {\r\n                        \"type\": \"shipping_rule_tax\",\r\n                        \"taxAmount\": {\r\n                            \"currency\": \"USD\",\r\n                            \"value\": 12\r\n                        },\r\n                        \"rate\": 7\r\n                    }\r\n                ],\r\n                \"totalTaxAmount\": {\r\n                    \"currency\": \"USD\",\r\n                    \"value\": 12\r\n                },\r\n                \"shippingTotal\": {\r\n                    \"currency\": \"USD\",\r\n                    \"value\": 50\r\n                },\r\n                \"currency\": \"USD\",\r\n                \"netTotal\": {\r\n                    \"currency\": \"USD\",\r\n                    \"value\": 230\r\n                }\r\n            },\r\n            \"user\": {\r\n                \"id\": \"3e4b3cbc-dde4-4da3-9957-631efd37c3d0\",\r\n                \"userName\": \"John\",\r\n                \"salutation\": \"Mr.\",\r\n                \"firstName\": \"John\",\r\n                \"lastName\": \"Doe\",\r\n                \"middleName\": \"p\",\r\n                \"email\": \"<EMAIL>\",\r\n                \"phone\": \"+91 8511417954\",\r\n                \"memberships\": [\r\n                    {\r\n                        \"loyaltyNumber\": \"\",\r\n                        \"loyaltyType\": \"\"\r\n                    }\r\n                ]\r\n            },\r\n            \"itinerary\": {\r\n                \"identifier\": \"13932d21-ed33-441d-ad66-aef2df203044\",\r\n                \"confirmationCode\": \"6kcb2s\",\r\n                \"airline\": \"SQ322\",\r\n                \"firstName\": \"RAJU\",\r\n                \"lastName\": \"sasi\",\r\n                \"middleName\": \"\"\r\n            },\r\n            \"seatInfo\": {\r\n               \"seatClass\": \"demo\",\r\n                \"seatNumber\": \"demo\"\r\n            },\r\n            \"groundSystem\": \"GA\"\r\n                        \r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/orders"}, "response": []}, {"name": "JAMA_13318965_Orders", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{storeToken}}"}}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "https://marketplace-dev.nextcloud.aero/marketplace/v1/oms/order"}, "response": []}, {"name": "JAMA_13318876_Order BY ID", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/oms/order/70682"}, "response": []}, {"name": "JAMA_13318875_Order SHIPMENT", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\r\n  \"trackingId\": \"string\",\r\n  \"trackingLink\": \"string\",\r\n  \"inventoryLocation\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/oms/ordershipment/70687"}, "response": []}, {"name": "JAMA_13318874_Order ITEM STATUS BY ID", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\r\n  \"orderItemId\": [\r\n    70685\r\n  ],\r\n  \"status\": \"PENDING\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/oms/orderItemStatus/70682"}, "response": []}]}, {"name": "JAMA_12668142_Get Stores", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": {"raw": "{{apiUrl}}/marketplace/v1/store/information?formatVersion=2", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "store", "information"], "query": [{"key": "formatVersion", "value": "2"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const options = {", "    'method': 'POST',", "    'url': `${pm.environment.get('apiUrl')}/marketplace/v1/auth/token`,", "    'header': {", "        'Content-Type': 'application/x-www-form-urlencoded'", "    },", "    'body' : {", "        mode: 'raw',", "        raw: {", "            'clientId': pm.environment.get('storeClientId'),", "            'clientSecret': pm.environment.get('storeClientSecret')", "        }", "    }", "};", "pm.sendRequest(options, function (err, response) {", "    // console.log(\"get accesstoken\");", "    // console.log(response.json()[\"data\"][\"token\"]);", "", "    pm.environment.set('storeToken', response.json()['data']['token']);", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Airline Support APIs", "item": [{"name": "Flight", "item": [{"name": "JAMA_12668154_Get Cabin Class", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "let cabinclass_id = response.data.cabinClasses[0].id;console.log(cabinclass_id);pm.environment.set('cabinclass_id',cabinclass_id);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/cabinClass"}, "response": []}, {"name": "JAMA_12668120_Get All Flights", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let flight_id_delete_ = response.data.flights[0].id;console.log(flight_id_delete);pm.environment.set('flight_id_delete',flight_id_delete);", "", "//let response = pm.response.json();let flight_id_include = response.data.flights[0].id;console.log(flight_id_include);pm.environment.set('flight_id_include',flight_id_include);", "", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/flight"}, "response": []}, {"name": "JAMA_13072982_Get UI Template", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let flight_id_delete_ = response.data.flights[0].id;console.log(flight_id_delete);pm.environment.set('flight_id_delete',flight_id_delete);", "", "//let response = pm.response.json();let flight_id_include = response.data.flights[0].id;console.log(flight_id_include);pm.environment.set('flight_id_include',flight_id_include);", "", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/ui-template"}, "response": []}, {"name": "JAMA_13072985_Get Root Type", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let flight_id_delete_ = response.data.flights[0].id;console.log(flight_id_delete);pm.environment.set('flight_id_delete',flight_id_delete);", "", "//let response = pm.response.json();let flight_id_include = response.data.flights[0].id;console.log(flight_id_include);pm.environment.set('flight_id_include',flight_id_include);", "", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/root-type"}, "response": []}, {"name": "JAMA_13318976_Get Meal Code", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let flight_id_delete_ = response.data.flights[0].id;console.log(flight_id_delete);pm.environment.set('flight_id_delete',flight_id_delete);", "", "//let response = pm.response.json();let flight_id_include = response.data.flights[0].id;console.log(flight_id_include);pm.environment.set('flight_id_include',flight_id_include);", "", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/meal-code"}, "response": []}, {"name": "JAMA_13318975_Get Meal Code Products", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let flight_id_delete_ = response.data.flights[0].id;console.log(flight_id_delete);pm.environment.set('flight_id_delete',flight_id_delete);", "", "//let response = pm.response.json();let flight_id_include = response.data.flights[0].id;console.log(flight_id_include);pm.environment.set('flight_id_include',flight_id_include);", "", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/meal-code/products"}, "response": []}, {"name": "JAMA_13318974_Patch Meal Product Configurations", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let flight_id_delete_ = response.data.flights[0].id;console.log(flight_id_delete);pm.environment.set('flight_id_delete',flight_id_delete);", "", "//let response = pm.response.json();let flight_id_include = response.data.flights[0].id;console.log(flight_id_include);pm.environment.set('flight_id_include',flight_id_include);", "", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"productId\": 58628,\n    \"productMealCode\": [\n      \"qa_mealcode\"\n    ]\n  }\n]", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/product-configuration"}, "response": []}, {"name": "JAMA_12668114_Get All Sectors", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "let sector_id_exclude = response.data.sectors[0].id;console.log(sector_id_exclude);pm.environment.set('sector_id_exclude',sector_id_exclude);", "let sector_id_delete = response.data.sectors[1].id;console.log(sector_id_delete);pm.environment.set('sector_id_delete',sector_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/sector"}, "response": []}, {"name": "JAMA_12668122_Flight - ADD", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let flight_id_delete = response.id;console.log(flight_id_delete);pm.environment.set('flight_id_delete',flight_id_delete);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"flightRouteNumber\": \"Fltdel{{$randomLastName}}{{$randomFirstName}}\",\r\n  \"flightBeginDateTime\": \"2023-01-05 21:00\",\r\n  \"flightEndDateTime\": \"2023-01-06 17:35\",\r\n  \"sectors\": [\r\n    {\r\n      \"id\": {{sector_id_delete}},\r\n      \"sequence\": 1\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/flight"}, "response": []}, {"name": "JAMA_12668121_Flight - GET by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/flight/{{flight_id_delete}}"}, "response": []}]}, {"name": "Catalog Assignment", "item": [{"name": "JAMA_12668136_Get All Route Catalog", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let catalog_assign_id_get2 = response.data.catalogsAssignments[0].id;console.log(catalog_assign_id_get2);pm.environment.set('catalog_assign_id_get2',catalog_assign_id_get2);", "", "let catalog_assign_id_delta = response.data.catalogsAssignments[0].catalog.id;console.log(catalog_assign_id_delta);pm.environment.set('catalog_assign_id_delta',catalog_assign_id_delta);", "let product_id_from_catalog = response.data.catalogsAssignments[0].catalog.products[0].productId;console.log(product_id_from_catalog);pm.environment.set('product_id_from_catalog',product_id_from_catalog);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{airlineToken}}"}}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiUrl}}/marketplace/v1/pim/route-catalog?limit=100", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "pim", "route-catalog"], "query": [{"key": "limit", "value": "100"}]}}, "response": []}, {"name": "JAMA_12668138_Add Route Catalog Route Group", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let catalog_assign_id_get1 = response.id;console.log(catalog_assign_id_get1);pm.environment.set('catalog_assign_id_get1',catalog_assign_id_get1);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"eiusog4{{$randomFirstName}}\",\n    \"catalog\": {\n        \"id\": {{catalog_assign_id_delta}}\n        \n    },\n   \"catalogFromDate\": \"2024-12-30\",\n    \"catalogToDate\": \"2024-12-31\",\n    \"flight\": [\n    {\n      \"id\": 1314,\n      \"sectors\": [\n        10668\n      ]\n    }\n  ],\n    \"cabinClass\": [\n        {{cabinclass_id}}\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog"}, "response": []}, {"name": "JAMA_12668140_Add Route Catalog Sector", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"eiusmodcg4{{$randomFirstName}}\",\r\n    \"catalog\": {\r\n        \"id\": {{catalog_assign_id_delta}}\r\n       \r\n    },\r\n\"catalogFromDate\": \"2024-12-30\",\r\n    \"catalogToDate\": \"2024-12-31\",\r\n    \"sector\": [\r\n    {\r\n      \"id\": 10668,\r\n      \"flights\": [\r\n        1314\r\n      ]\r\n    }\r\n  ],\r\n    \"cabinClass\": [\r\n        {{cabinclass_id}}\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog"}, "response": []}, {"name": "JAMA_12668141_Add Route Catalog Flight", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"eiusog4{{$randomFirstName}}\",\r\n    \"catalog\": {\r\n        \"id\": {{catalog_assign_id_delta}}\r\n        \r\n    },\r\n   \"catalogFromDate\": \"2024-12-30\",\r\n    \"catalogToDate\": \"2024-12-31\",\r\n    \"flight\": [\r\n    {\r\n      \"id\": 1314,\r\n      \"sectors\": [\r\n        10668\r\n      ]\r\n    }\r\n  ],\r\n    \"cabinClass\": [\r\n        {{cabinclass_id}}\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog"}, "response": []}, {"name": "JAMA_12668137_Get Route Catalog By Id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog/{{catalog_assign_id_get1}}"}, "response": []}, {"name": "JAMA_14315842_Workflow Edit Airline category", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{airlineToken}}"}}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"catalogAssignmentId\" : {{catalog_assign_id_get1}},\n    \"status\" : \"editAirlineCategory\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog-workflow"}, "response": []}, {"name": "JAMA_14315843_Workflow Edit Base Data", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{airlineToken}}"}}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"catalogAssignmentId\" : {{catalog_assign_id_get1}},\n    \"status\" : \"editBaseData\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog-workflow"}, "response": []}, {"name": "JAMA_12668139_Update Route Catalog", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"eiusog4{{$randomFirstName}}\",\r\n    \"catalog\": {\r\n        \"id\": {{catalog_assign_id_delta}}\r\n        \r\n    },\r\n   \"catalogFromDate\": \"2024-12-30\",\r\n    \"catalogToDate\": \"2024-12-31\",\r\n    \"flight\": [\r\n    {\r\n      \"id\": 1314,\r\n      \"sectors\": [\r\n        10668\r\n      ]\r\n    }\r\n  ],\r\n    \"cabinClass\": [\r\n        {{cabinclass_id}}\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog/{{catalog_assign_id_get1}}"}, "response": []}, {"name": "JAMA_12904168_PATCH Route Catalog", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"eiusog4{{$randomFirstName}}\",\r\n    \"catalog\": {\r\n        \"id\": {{catalog_assign_id_delta}}\r\n        \r\n    },\r\n   \"catalogFromDate\": \"2024-12-30\",\r\n    \"catalogToDate\": \"2024-12-31\",\r\n    \"flight\": [\r\n    {\r\n      \"id\": 1314,\r\n      \"sectors\": [\r\n        10668\r\n      ]\r\n    }\r\n  ],\r\n    \"cabinClass\": [\r\n        {{cabinclass_id}}\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog/{{catalog_assign_id_get1}}"}, "response": []}, {"name": "JAMA_13318987_PUT Deployment Inventory", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"catalog\": {\r\n    \"id\": {{catalog_assign_id_delta}},\r\n    \"products\": [\r\n      {\r\n        \"productId\": {{product_id_from_catalog}},\r\n        \"productQuantity\": {{$randomInt}}\r\n      }\r\n    ]\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog/{{catalog_assign_id_get1}}/deploymentInventory"}, "response": []}, {"name": "JAMA_13318972_PUT Route catalog Product Price", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"catalog\": {\r\n    \"id\": {{catalog_assign_id_delta}},\r\n    \"products\": [\r\n      {\r\n        \"productId\": {{product_id_from_catalog}},\r\n        \"productPrice\": 110,\r\n        \"productQuantity\": {{$randomInt}}\r\n      }\r\n    ]\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog/{{catalog_assign_id_get1}}/price"}, "response": []}, {"name": "JAMA_12704045_Workflow Airline Category Mapped", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{airlineToken}}"}}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"catalogAssignmentId\" : {{catalog_assign_id_get1}},\n    \"status\" : \"airlineCategoryMapped\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog-workflow"}, "response": []}, {"name": "JAMA_12779849_Get Delta", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog/getdelta/{{catalog_assign_id_get1}}"}, "response": []}, {"name": "JAMA_12779856_Approve Delta", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "default"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/route-catalog/approvedelta/{{catalog_assign_id_get1}}"}, "response": []}, {"name": "JAMA_12668103_Get Airlines", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let airline_id = response.data.airline.id;console.log(airline_id);pm.environment.set('airline_id',airline_id);", "//let response1 = pm.response.json();let airline_id_delete = response1.data.products[1].id;console.log(airline_id_delete);pm.environment.set('airline_id_delete',airline_id_delete);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/ams/airline"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// // Set the delay time in milliseconds (e.g., 5000 ms for a 5-second delay)", "// const delayTime = 3000;", "", "// // Use setTimeout to add the delay", "// setTimeout(function() {", "//     // Continue with the request", "//     postman.setNextRequest(null);", "// }, delayTime);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const options = {", "    'method': 'POST',", "    'url': `${pm.environment.get('apiUrl')}/marketplace/v1/auth/token`,", "    'header': {", "        'Content-Type': 'application/x-www-form-urlencoded'", "    },", "    'body' : {", "        mode: 'raw',", "        raw: {", "            'clientId': pm.environment.get('airlineClientId'),", "            'clientSecret': pm.environment.get('airlineClientSecret')", "        }", "    }", "};", "pm.sendRequest(options, function (err, response) {", "    // console.log(\"get accesstoken\");", "    // console.log(response.json()[\"data\"][\"token\"]);", "", "    pm.environment.set('airlineToken', response.json()['data']['token']);", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Delivery Rule", "item": [{"name": "JAMA_12779400_Add Delivery Rule", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let delivery_rule_id = response.data.deliveryRule[0].id;console.log(delivery_rule_id);pm.environment.set('delivery_rule_id',delivery_rule_id);", "", "let response = pm.response.json();let delivery_rule_id = response.id;console.log(delivery_rule_id);pm.environment.set('delivery_rule_id',delivery_rule_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"deliveryRuleType\": \"shipping\",\r\n  \"shipmentGroupingType\": \"fulfillment_type\",\r\n  \"shippingDestination\": \"domestic\",\r\n  \"deliveryType\": [\r\n    \"homeShippingDomesticStandard\"\r\n  ],\r\n  \"title\": \"string{{$randomFirstName}}\",\r\n  \"description\": \"string\",\r\n  \"price\": 45,\r\n  \"priceUnit\": \"USD\",\r\n  \"duration\": 44,\r\n  \"durationType\": \"days\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/delivery-rule"}, "response": []}, {"name": "JAMA_14320886_Get All Delivery Rule (Shipping)", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let delivery_rule_id = response.data.deliveryRule[0].id;console.log(delivery_rule_id);pm.environment.set('delivery_rule_id',delivery_rule_id);", "let response = pm.response.json(); let delivery_type = response.data.deliveryRule[0].deliveryType[0];console.log(delivery_type);pm.environment.set('delivery_type',delivery_type);", "//let response1 = pm.response.json();let delivery_rule_id_delete = response1.data.deliveryRule[1].id;console.log(delivery_rule_id_delete);pm.environment.set('delivery_rule_id_delete',delivery_rule_id_delete);", "let delivery_rule_id_delete = response.data.deliveryRule[0].id;console.log(delivery_rule_id_delete);pm.environment.set('delivery_rule_id_delete',delivery_rule_id_delete);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/store/delivery-rule"}, "response": []}, {"name": "JAMA_14320885_Update Delivery Rule", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"deliveryRuleType\": \"shipping\",\r\n  \"shipmentGroupingType\": \"fulfillment_type\",\r\n  \"shippingDestination\": \"domestic\",\r\n  \"deliveryType\": [\r\n    \"{{delivery_type}}\"\r\n  ],\r\n  \"title\": \"str{{$randomFirstName}}\",\r\n  \"description\": \"str{{$randomFirstName}}\",\r\n  \"price\": 46.3,\r\n  \"priceUnit\": \"USD\",\r\n  \"duration\": 44,\r\n  \"durationType\": \"days\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/delivery-rule/{{delivery_rule_id}}"}, "response": []}, {"name": "JAMA_14320882_Patch Delivery Rule", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n \r\n  \"title\": \"str{{$randomLastName}}\"\r\n \r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/delivery-rule/{{delivery_rule_id}}"}, "response": []}, {"name": "JAMA_14320884_Get By ID Delivery Rule", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/store/delivery-rule/{{delivery_rule_id}}"}, "response": []}, {"name": "JAMA_14319219_Delete Delivery Rule", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/store/delivery-rule/{{delivery_rule_id}}"}, "response": []}]}, {"name": "<PERSON>", "item": [{"name": "JAMA_12668146_Prepare Kit", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let airline_id = response.data.airline.id;console.log(airline_id);pm.environment.set('airline_id',airline_id);", "//let response1 = pm.response.json();let airline_id_delete = response1.data.products[1].id;console.log(airline_id_delete);pm.environment.set('airline_id_delete',airline_id_delete);", "var dateNow = new Date(); ", "var FutureDate = new Date(dateNow.setDate(dateNow.getDate() + 1)).toISOString().slice(0, 10);postman.setEnvironmentVariable(\"future_date\", FutureDate);", "var FutureDate1 = new Date(dateNow.setDate(dateNow.getDate() + 2)).toISOString().slice(0, 10);postman.setEnvironmentVariable(\"future_date1\", FutureDate1);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{airlineToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"startDate\":\"\",\n    \"endDate\":\"\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/ams/prepare-kit"}, "response": []}]}, {"name": "Store  Support APIs", "item": [{"name": "Store Location", "item": [{"name": "JAMA_12668092_Get Fulfillment", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let fulfillment_id = response.data.fulfillments[0].id;console.log(fulfillment_id);pm.environment.set('fulfillment_id',fulfillment_id);", "let response1 = pm.response.json();let fulfillment_id_delete = response1.data.fulfillments[1].id;console.log(fulfillment_id_delete);pm.environment.set('fulfillment_id_delete',fulfillment_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/fulfillment"}, "response": []}, {"name": "JAMA_12668147_Get All Store Locations", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let store_location_id = response.data.storeLocation[0].id;console.log(store_location_id);pm.environment.set('store_location_id',store_location_id);", "let response1 = pm.response.json();let store_location_id_delete = response1.data.storeLocation[0].id;console.log(store_location_id_delete);pm.environment.set('store_location_id_delete',store_location_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": {"raw": "{{apiUrl}}/marketplace/v1/store/location?offset=1", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "store", "location"], "query": [{"key": "offset", "value": "1"}]}}, "response": []}, {"name": "JAMA_12668149_Add New Store Location", "event": [{"listen": "test", "script": {"exec": ["//let response = pm.response.json();let store_location_id = response.data.storeLocation[0].id;console.log(store_location_id);pm.environment.set('store_location_id',store_location_id);", "", "let response = pm.response.json();let store_location_id = response.id;console.log(store_location_id);pm.environment.set('store_location_id',store_location_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"ABC{{$randomFirstName}}-{{count}}\",\r\n    \"fulfilmentOption\": [\r\n        \"{{fulfillment_id}}\"\r\n    ],\r\n    \"icaoCode\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/location"}, "response": []}, {"name": "JAMA_12668150_Update Store Location", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"De<PERSON><PERSON>aat{{$randomLastName}}\",\r\n    \"fulfilmentOption\": [\r\n        {{fulfillment_id_delete}}\r\n    ],\r\n    \"icaoCode\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/location/{{store_location_id}}"}, "response": []}, {"name": "JAMA_12668151_Patch Update Store Location", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>{{$randomWeekday}}-{{count}}\",\r\n    \"icaoCode\": \"LAX\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/location/{{store_location_id}}"}, "response": []}, {"name": "JAMA_12668148_Get By Id Store Location", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/store/location/{{store_location_id}}"}, "response": []}, {"name": "JAMA_12668152_Delete Store Location", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/store/location/{{store_location_id}}"}, "response": []}], "auth": {"type": "bearer", "bearer": {}}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var currentCount = pm.environment.get(\"count\");", "", "// Convert the current count value to an integer", "currentCount = parseInt(currentCount);", "currentCount+=1;", "// Update the environment variable with the new count", "pm.environment.set(\"count\", currentCount.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Product", "item": [{"name": "JAMA_12668091_Categories", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();let category_id = response.data.categories[0].id;console.log(category_id);pm.environment.set('category_id',category_id);", "let response1 = pm.response.json();let category_id_delete = response1.data.categories[1].id;console.log(category_id_delete);pm.environment.set('category_id_delete',category_id_delete);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": {"raw": "{{apiUrl}}/marketplace/v1/pim/categories?formatVersion=2", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "pim", "categories"], "query": [{"key": "formatVersion", "value": "2"}]}}, "response": []}, {"name": "JAMA_12668093_Get Products", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "let response2 = pm.response.json();let product_type = response.data.products[0].productType;console.log(product_type);pm.environment.set('product_type',product_type);", "let response3 = pm.response.json();let product_sku1 = response.data.products[0].sku;console.log(product_sku1);pm.environment.set('product_sku1',product_sku1);", "let response4 = pm.response.json();let product_sku2 = response.data.products[1].sku;console.log(product_sku2);pm.environment.set('product_sku2',product_sku2);", "let response5 = pm.response.json();let delivery_method = response.data.products[0].deliveryMethod[0];console.log(delivery_method);pm.environment.set('delivery_method',delivery_method);", "let response6 = pm.response.json();let delivery_method1 = response.data.products[0].deliveryMethod[1];console.log(delivery_method1);pm.environment.set('delivery_method1',delivery_method1);", "let response7 = pm.response.json();let product_id_exclude = response.data.products[0].id;console.log(product_id_exclude);pm.environment.set('product_id_exclude',product_id_exclude);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": {"raw": "{{apiUrl}}/marketplace/v1/pim/product", "host": ["{{apiUrl}}"], "path": ["marketplace", "v1", "pim", "product"], "query": [{"key": "offset", "value": "2", "disabled": true}]}}, "response": []}, {"name": "JAMA_12668094_Add Product-datevar", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["//var moment = require('moment');", "//pm.globals.set(\"todayDate\",moment().add(3, 'day').toISOString());", "//pm.globals.set(\"endDate\", moment().add(4, 'day').toISOString());", "//var moment = require('moment');", "//pm.globals.set(\"todayDate\", moment().add(3, 'day').format(\"YYYY-MM-DD HH:mm\"));", "//pm.globals.set(\"endDate\", moment().add(4, 'day').format(\"YYYY-MM-DD HH:mm\"));", "//let response = pm.response.json();let product_id = response.id;console.log(product_id);pm.environment.set('product_id',product_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "language", "value": "en", "type": "text"}, {"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"formatVersion\": \"2\",\r\n    \"sku\": \"product{{$randomFirstName}}{{$randomInt}}\",\r\n    \"name\": {\r\n        \"en\": \"Loreal Shampoo for Hair Growth\",\r\n        \"zh_<PERSON>\": \"欧莱雅头发生长洗发水\",\r\n        \"ms\": \"Syampu Loreal untuk Pertumbuhan Rambut\",\r\n        \"zh_Hant\": \"歐萊雅頭髮生長洗髮水\",\r\n        \"de\": \"Loreal Shampoo für das Haarwachstum\",\r\n        \"it\": \"Shampoo Loreal per la crescita dei capelli\",\r\n        \"pt_BR\": \"Shampoo Loreal para o crescimento do cabelo\",\r\n        \"fr\": \"Shampooing Loreal pour la croissance des cheveux\",\r\n        \"es\": \"Loreal Champú para el Crecimiento del Cabello\",\r\n        \"ko\": \"모발 성장을 위한 로레알 샴푸\",\r\n        \"ru\": \"Лореаль шампунь для роста волос\",\r\n        \"th\": \"ลอรีอัล แชมพูเพื่อการเจริญเติบโตของเส้นผม\",\r\n        \"vi\": \"<PERSON>ầu gội Loreal kích thích mọc tóc\",\r\n        \"fil\": \"Loreal Shampoo para sa Paglago ng Buhok\",\r\n        \"ja\": \"髪の成長のためのロレアル シャンプー\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"shortDescription\": {\r\n        \"en\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo helps fight against the five visible signs of damaged hair - Hair fall, Dryness, Roughness, Dullness and Split ends without weighing it down.\",\r\n        \"zh_Hans\": \"L'Oréal Paris Total Repair 5 修护洗发水有助于对抗头发受损的五个明显迹象 - 脱发、干燥、粗糙、暗沉和分叉，而不会使头发变重。\",\r\n        \"ms\": \"L'Oréal Paris Total Repair 5 Repair Shampoo membantu melawan lima tanda rambut rosak yang boleh dilihat - Rambut gugur, Kering, Kekasaran, Kusam dan Bercabang berakhir tanpa membebankannya.\",\r\n        \"zh_Hant\": \"L'Oréal Paris Total Repair 5 修護洗髮水有助於對抗頭髮受損的五個明顯跡象 - 脫髮、乾燥、粗糙、暗沉和分叉，而不會使頭髮變重。\",\r\n        \"de\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo bekämpft die fünf sichtbaren Anzeichen von geschädigtem Haar – Haarausfall, Trockenheit, Rauheit, Stumpfheit und Spliss, ohne es zu beschweren.\",\r\n        \"it\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo aiuta a combattere i cinque segni visibili dei capelli danneggiati: caduta, secchezza, rugosità, opacità e doppie punte senza appesantire.\",\r\n        \"pt_BR\": \"L'Oréal Paris Total Repair 5 Shampoo Reparador ajuda a combater os cinco sinais visíveis de cabelos danificados - queda, ressecamento, aspereza, embotamento e pontas duplas sem sobrecarregá-lo.\",\r\n        \"fr\": \"Le shampooing réparateur Total Repair 5 de L'Oréal Paris aide à lutter contre les cinq signes visibles des cheveux abîmés - chute des cheveux, sécheresse, rugosité, matité et pointes fourchues sans les alourdir.\",\r\n        \"es\": \"El champú reparador L'Oréal Paris Total Repair 5 ayuda a combatir los cinco signos visibles del cabello dañado: caída del cabello, sequedad, aspereza, opacidad y puntas abiertas sin apelmazarlo.\",\r\n        \"ko\": \"로레알 파리 토탈 리페어 5 리페어링 샴푸는 5가지 눈에 보이는 손상 모발 징후(탈모, 건조함, 거칠음, 칙칙함, 갈라짐)를 억제하는 데 도움이 됩니다.\",\r\n        \"ru\": \"Восстанавливающий шампунь L'Oréal Paris Total Repair 5 помогает бороться с пятью видимыми признаками поврежденных волос - выпадением, сухостью, шероховатостью, тусклостью и секущимися кончиками, не утяжеляя их.\",\r\n        \"th\": \"ลอรีอัล ปารีส โททัล รีแพร์ 5 รีแพร์ริ่ง แชมพูช่วยต่อสู้กับสัญญาณห้าประการของเส้นผมที่เสียหาย - ผมร่วง ผมแห้ง หยาบกระด้าง หมองคล้ำ และแตกปลายโดยไม่ทำให้ผมหนักใจ\",\r\n        \"vi\": \"Dầu gội phục hồi L'Oréal Paris Total Repair 5 giúp chống lại năm dấu hiệu có thể nhìn thấy của tóc hư tổn - Tóc rụng, Khô, Xơ, Xỉn màu và Chẻ ngọn mà không làm nặng tóc.\",\r\n        \"fil\": \"Ang L'Oréal Paris Total Repair 5 Repairing Shampoo ay tumutulong sa paglaban sa limang nakikitang senyales ng nasirang buhok - Nawawala ang Buhok, Pagkatuyo, Pagkagaspang, Pagkapurol at Paghati nang hindi tumitimbang.\",\r\n        \"ja\": \"ロレアル パリ トータル リペア 5 リペアリング シャンプーは、ダメージを受けた髪の 5 つの目に見える兆候 (抜け毛、乾燥、ざらつき、くすみ、枝毛) を重くすることなく防ぎます。\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"description\": {\r\n        \"en\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo helps combat the five signs of damaged hair - Hair fall, Dryness, Roughness, Dullness, and Split ends without weighing it down. Damaged hair lacks the natural cement that keeps the hair strong and resilient. To make sure the hair gets the required strength, the L'Oréal Laboratories have included the Keratin XS technology to replicate the hair's natural cement to target the five major problems. The smallest form of Keratin XS deeply penetrates the fibres to ensure faster repair.Highlights:1. Powered by up to 17% Keratin XS + Repair concentrate2. Helps fight against the five visible signs of damaged hair - hair fall, dryness, roughness, dullness, and split ends3. Delivers 5X stronger and renewed hair from the first application\",\r\n        \"zh_Hans\": \"L'Oréal Paris Total Repair 5 修護洗髮水有助於對抗頭髮受損的五個跡象 - 脫髮、乾燥、粗糙、暗淡和分叉，而不會壓低頭髮。受損的頭髮缺乏使頭髮保持強韌和彈性的天然粘合劑。為確保頭髮獲得所需的強度，歐萊雅實驗室採用了 Keratin XS 技術來複製頭髮的天然粘合劑，以解決五個主要問題。最小形式的 Keratin XS 深入滲透纖維，確保更快修復。強調：1. 由高達 17% 的角蛋白 XS + 修復濃縮物提供動力2. 幫助對抗頭髮受損的五個明顯跡象 - 掉髮、乾燥、粗糙、暗沉和分叉3. 從第一次使用開始，頭髮就會強韌 5 倍，煥然一新\",\r\n        \"ms\": \"L'Oréal Paris Total Repair 5 Repair Shampoo membantu memerangi lima tanda rambut rosak - Rambut gugur, Kering, Kekasaran, Kusam dan Berpecah tanpa membebankannya. Rambut yang rosak tidak mempunyai simen semula jadi yang memastikan rambut kuat dan berdaya tahan. Untuk memastikan rambut mendapat kekuatan yang diperlukan, L'Oréal Laboratories telah memasukkan teknologi Keratin XS untuk meniru simen semula jadi rambut untuk menyasarkan lima masalah utama. Bentuk terkecil Keratin XS menembusi dalam gentian untuk memastikan pembaikan lebih cepat. Sorotan:1. Dikuasakan sehingga 17% Keratin XS + Pekat pembaikan2. Membantu melawan lima tanda rambut rosak yang boleh dilihat - rambut gugur, kering, kekasaran, kusam dan hujung bercabang3. Menghasilkan rambut 5X lebih kuat dan diperbaharui daripada penggunaan pertama\",\r\n        \"zh_Hant\": \"L'Oréal Paris Total Repair 5 修护洗发水有助于对抗头发受损的五个迹象 - 脱发、干燥、粗糙、暗淡和分叉，而不会压低头发。受损的头发缺乏使头发保持强韧和弹性的天然粘合剂。为确保头发获得所需的强度，欧莱雅实验室采用了 Keratin XS 技术来复制头发的天然粘合剂，以解决五个主要问题。最小形式的角蛋白 XS 深入渗透纤维以确保更快的修复。亮点：1。由高达 17% 的角蛋白 XS + 修复浓缩物提供支持2。帮助对抗头发受损的五个明显迹象 - 脱发、干燥、粗糙、暗淡和分叉 3。首次使用即可使头发强韧 5 倍，焕发新生\",\r\n        \"de\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo hilft, die fünf Anzeichen von geschädigtem Haar zu bekämpfen - Haarausfall, Trockenheit, Rauheit, Mattheit und Spliss, ohne es zu beschweren. Geschädigtem Haar fehlt der natürliche Zement, der das Haar stark und widerstandsfähig hält. Um sicherzustellen, dass das Haar die erforderliche Stärke erhält, haben die L'Oréal Laboratories die Keratin XS-Technologie integriert, um den natürlichen Zement des Haares zu replizieren, um die fünf Hauptprobleme anzugehen. Die kleinste Form von Keratin XS dringt tief in die Fasern ein, um eine schnellere Reparatur zu gewährleisten.Highlights:1. Angetrieben von bis zu 17 % Keratin XS + Repair-Konzentrat2. Hilft bei der Bekämpfung der fünf sichtbaren Anzeichen von geschädigtem Haar – Haarausfall, Trockenheit, Rauheit, Mattigkeit und Spliss3. Liefert 5x kräftigeres und erneuertes Haar ab der ersten Anwendung\",\r\n        \"it\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo aiuta a combattere i cinque segni dei capelli danneggiati: caduta, secchezza, rugosità, opacità e doppie punte senza appesantire. I capelli danneggiati mancano del cemento naturale che mantiene i capelli forti e resistenti. Per assicurarsi che i capelli ricevano la forza necessaria, i Laboratori L'Oréal hanno incluso la tecnologia Keratin XS per replicare il cemento naturale dei capelli per affrontare i cinque problemi principali. La forma più piccola di Keratin XS penetra in profondità nelle fibre per garantire una riparazione più rapida.In evidenza:1. Potenziato fino al 17% di cheratina XS + concentrato riparatore2. Aiuta a combattere i cinque segni visibili dei capelli danneggiati: caduta dei capelli, secchezza, rugosità, opacità e doppie punte3. Fornisce capelli 5 volte più forti e rinnovati dalla prima applicazione\",\r\n        \"pt_BR\": \"L'Oréal Paris Total Repair 5 Shampoo Reparador ajuda a combater os cinco sinais de cabelos danificados - queda, ressecamento, aspereza, embotamento e pontas duplas sem pesar. O cabelo danificado carece do cimento natural que mantém o cabelo forte e resistente. Para garantir que o cabelo obtenha a força necessária, os Laboratórios L'Oréal incluíram a tecnologia Keratin XS para replicar o cimento natural do cabelo para resolver os cinco principais problemas. A menor forma de Keratin XS penetra profundamente nas fibras para garantir um reparo mais rápido.Destaques:1. Alimentado por até 17% de Queratina XS + Concentrado de reparação2. Ajuda a combater os cinco sinais visíveis de cabelos danificados - queda, ressecamento, aspereza, opacidade e pontas duplas3. Proporciona cabelos 5X mais fortes e renovados desde a primeira aplicação\",\r\n        \"fr\": \"Le shampooing réparateur Total Repair 5 de L'Oréal Paris aide à combattre les cinq signes des cheveux abîmés : chute des cheveux, sécheresse, rugosité, matité et pointes fourchues sans les alourdir. Les cheveux abîmés manquent du ciment naturel qui maintient les cheveux forts et résistants. Pour s'assurer que les cheveux obtiennent la force requise, les Laboratoires L'Oréal ont inclus la technologie Keratin XS pour répliquer le ciment naturel des cheveux afin de cibler les cinq problèmes majeurs. La plus petite forme de kératine XS pénètre profondément dans les fibres pour assurer une réparation plus rapide.Points forts :1. Alimenté par jusqu'à 17 % de kératine XS + concentré de réparation2. Aide à lutter contre les cinq signes visibles des cheveux abîmés - chute des cheveux, sécheresse, rugosité, matité et pointes fourchues3. Fournit des cheveux 5 fois plus forts et renouvelés dès la première application\",\r\n        \"es\": \"El champú reparador L'Oréal Paris Total Repair 5 ayuda a combatir los cinco signos del cabello dañado: caída del cabello, sequedad, aspereza, opacidad y puntas abiertas sin apelmazarlo. El cabello dañado carece del cemento natural que mantiene el cabello fuerte y resistente. Para asegurarse de que el cabello obtenga la fuerza necesaria, los Laboratorios L'Oréal han incluido la tecnología Keratin XS para replicar el cemento natural del cabello para abordar los cinco problemas principales. La forma más pequeña de Keratin XS penetra profundamente en las fibras para garantizar una reparación más rápida. Aspectos destacados: 1. Potenciado con hasta un 17 % de queratina XS + concentrado reparador2. Ayuda a luchar contra los cinco signos visibles del cabello dañado: caída del cabello, sequedad, aspereza, falta de brillo y puntas abiertas3. Brinda un cabello 5 veces más fuerte y renovado desde la primera aplicación.\",\r\n        \"ko\": \"로레알 파리 토탈 리페어 5 리페어링 샴푸는 손상된 모발의 5가지 징후(탈모, 건조함, 거칠음, 칙칙함, 갈라짐)를 억제하는 데 도움이 됩니다. 손상된 모발에는 모발을 강하고 탄력 있게 유지하는 천연 시멘트가 부족합니다. 모발이 필요한 강도를 갖도록 하기 위해 L'Oréal Laboratories는 Keratin XS 기술을 포함하여 모발의 천연 시멘트를 복제하여 5가지 주요 문제를 해결했습니다. 가장 작은 형태의 케라틴 XS가 섬유질 깊숙이 침투하여 빠른 복구를 보장합니다.하이라이트:1. 최대 17% 케라틴 XS + 리페어 컨센트레이트2로 구동됩니다. 손상된 모발의 눈에 보이는 5가지 징후(탈모, 건조함, 거칠음, 칙칙함, 모발 끝 갈라짐)를 방지합니다3. 첫 번째 적용에서 5배 더 강하고 재생된 모발 제공\",\r\n        \"ru\": \"Восстанавливающий шампунь L'Oréal Paris Total Repair 5 помогает бороться с пятью признаками поврежденных волос: выпадением, сухостью, жесткостью, тусклостью и секущимися кончиками, не утяжеляя их. Поврежденным волосам не хватает природного цемента, который делает их сильными и эластичными. Чтобы волосы приобрели необходимую силу, лаборатории L'Oréal включили технологию Keratin XS, имитирующую естественный цемент волос для решения пяти основных проблем. Самая маленькая форма кератина XS глубоко проникает в волокна, обеспечивая более быстрое восстановление. Особенности:1. Содержит до 17% кератина XS + восстанавливающий концентрат2. Помогает бороться с пятью видимыми признаками поврежденных волос - выпадением, сухостью, жесткостью, тусклостью и секущимися кончиками3. Делает волосы в 5 раз более сильными и обновленными с первого применения.\",\r\n        \"th\": \"ลอรีอัล ปารีส โททอล รีแพร์ 5 รีแพร์ริ่ง แชมพู ช่วยต่อต้านสัญญาณห้าประการของเส้นผมที่เสียหาย - ผมร่วง แห้งเสีย หยาบกระด้าง หมองคล้ำ และแตกปลายโดยไม่ทำให้ผมหนักศีรษะ ผมเสียขาดซีเมนต์ธรรมชาติที่ช่วยให้ผมแข็งแรงและยืดหยุ่น เพื่อให้แน่ใจว่าเส้นผมได้รับความแข็งแรงตามที่ต้องการ ลอรีอัล แลบอราทอรีส์ได้รวมเอาเทคโนโลยีเคราติน XS เพื่อจำลองซีเมนต์ตามธรรมชาติของเส้นผมเพื่อกำหนดเป้าหมายปัญหาหลัก 5 ประการ Keratin XS ในรูปแบบที่เล็กที่สุดจะแทรกซึมเข้าไปในเส้นใยอย่างล้ำลึกเพื่อให้แน่ใจว่าการซ่อมแซมจะเร็วขึ้นไฮไลท์:1. ขับเคลื่อนด้วย Keratin XS + Repair Concentrated มากถึง 17%2. ช่วยต่อสู้กับห้าสัญญาณที่มองเห็นได้ของเส้นผมที่เสียหาย - ผมร่วง แห้ง หยาบกร้าน หมองคล้ำ และแตกปลาย3. ให้ผมแข็งแรงขึ้นและเกิดใหม่ขึ้น 5 เท่าตั้งแต่ครั้งแรกที่ใช้\",\r\n        \"vi\": \"Dầu gội phục hồi L'Oréal Paris Total Repair 5 giúp chống lại năm dấu hiệu của tóc hư tổn - Rụng tóc, Khô, Xơ, Xỉn màu và Chẻ ngọn mà không làm tóc bị nặng. Tóc hư tổn thiếu xi măng tự nhiên giúp tóc chắc khỏe và đàn hồi. Để đảm bảo tóc có được độ chắc khỏe cần thiết, Phòng thí nghiệm L'Oréal đã đưa vào công nghệ Keratin XS để tái tạo xi măng tự nhiên của tóc nhằm giải quyết năm vấn đề chính. Dạng Keratin XS nhỏ nhất thấm sâu vào sợi vải để đảm bảo sửa chữa nhanh hơn. Điểm nổi bật:1. Được hỗ trợ lên đến 17% Keratin XS + Repair cô đặc2. Giúp chống lại năm dấu hiệu có thể nhìn thấy của tóc hư tổn - tóc gãy rụng, khô, thô ráp, xỉn màu và chẻ ngọn3. Mang lại mái tóc khỏe và mới hơn gấp 5 lần ngay từ lần sử dụng đầu tiên\",\r\n        \"fil\": \"Ang L'Oréal Paris Total Repair 5 Repairing Shampoo ay nakakatulong na labanan ang limang senyales ng nasirang buhok - Nawawala ang Buhok, Pagkatuyo, Pagkagaspang, Pagkapurol, at Paghiwa-hiwalayin nang hindi ito tinitimbang. Ang nasirang buhok ay kulang sa natural na semento na nagpapanatili sa buhok na malakas at nababanat. Upang matiyak na nakukuha ng buhok ang kinakailangang lakas, isinama ng L'Oréal Laboratories ang teknolohiyang Keratin XS upang kopyahin ang natural na semento ng buhok upang i-target ang limang pangunahing problema. Ang pinakamaliit na anyo ng Keratin XS ay malalim na tumatagos sa mga hibla upang matiyak ang mas mabilis na pagkumpuni. Mga Highlight:1. Pinapatakbo ng hanggang 17% Keratin XS + Repair concentrate2. Tumutulong na labanan ang limang nakikitang palatandaan ng nasirang buhok - pagkalagas ng buhok, pagkatuyo, pagkamagaspang, pagkapurol, at split ends3. Naghahatid ng 5X mas malakas at na-renew na buhok mula sa unang aplikasyon\",\r\n        \"ja\": \"ロレアル パリ トータル リペア 5 リペアリング シャンプーは、ダメージを受けた髪の 5 つの兆候 (抜け毛、乾燥、ざらつき、くすみ、枝毛) を重くすることなくケアします。傷んだ髪には、髪を強く弾力のある状態に保つ天然のセメントが不足しています。髪が必要な強度を確実に得るために、ロレアル ラボラトリーズはケラチン XS テクノロジーを組み込んで、髪の自然なセメントを再現し、5 つの主要な問題をターゲットにしました。最小の形のケラチン XS が繊維に深く浸透し、より迅速な修復を保証します。ハイライト:1.最大 17% のケラチン XS + リペア コンセントレート2 を搭載。目に見える傷みの5つのサイン（抜け毛・パサつき・パサつき・くすみ・枝毛）をケア。最初のアプリケーションから 5 倍の強さと再生された髪を提供します。\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"visibilityScope\": \"search\",\r\n    \"brand\": {\r\n        \"en\": \"Loreal Shampoo for Hair Growth\",\r\n        \"zh_Hans\": \"欧莱雅头发生长洗发水\",\r\n        \"ms\": \"Syampu Loreal untuk Pertumbuhan Rambut\",\r\n        \"zh_Hant\": \"歐萊雅頭髮生長洗髮水\",\r\n        \"de\": \"Loreal Shampoo für das Haarwachstum\",\r\n        \"it\": \"Shampoo Loreal per la crescita dei capelli\",\r\n        \"pt_BR\": \"Shampoo Loreal para o crescimento do cabelo\",\r\n        \"fr\": \"Shampooing Loreal pour la croissance des cheveux\",\r\n        \"es\": \"Loreal Champú para el Crecimiento del Cabello\",\r\n        \"ko\": \"모발 성장을 위한 로레알 샴푸\",\r\n        \"ru\": \"Лореаль шампунь для роста волос\",\r\n        \"th\": \"ลอรีอัล แชมพูเพื่อการเจริญเติบโตของเส้นผม\",\r\n        \"vi\": \"Dầu gội Loreal kích thích mọc tóc\",\r\n        \"fil\": \"Loreal Shampoo para sa Paglago ng Buhok\",\r\n        \"ja\": \"髪の成長のためのロレアル シャンプー\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να \",\r\n        \"nl\": \"Shampoos \",\r\n        \"he\": \"כדי להבטין לשכפול המاوف\",\r\n        \"hi\": \"केराटिन तकनीक\"\r\n    },\r\n    \"barcode\": \"TCGDA050111\",\r\n    \"deliveryMethod\": [\r\n        \"{{delivery_method}}\"\r\n    ],\r\n    \"category\": [\r\n        {{category_id}}\r\n    ],\r\n    \"shippingAttributes\": {\r\n        \"weight\": 5,\r\n        \"weightUnit\": \"LB\",\r\n        \"height\": \"\",\r\n        \"heightUnit\": \"\",\r\n        \"width\": \"\",\r\n        \"widthUnit\": \"\",\r\n        \"length\": \"\",\r\n        \"lengthUnit\": \"\"\r\n    },\r\n    \"assets\": {\r\n        \"images\": [\r\n            {\r\n                \"image1_1\": \"https://placehold.jp/1200x1200.png\",\r\n                \"image2_1\": \"https://placehold.jp/1480x740.png\",\r\n                \"image2_3\": \"https://placehold.jp/944x1416.png\",\r\n                \"image4_3\": \"https://placehold.jp/760x570.png\",\r\n                \"image5_6\": \"https://placehold.jp/670x804.png\"\r\n            }\r\n        ],\r\n        \"gallery\": [\r\n            {\r\n                \"url\": \"https://placehold.jp/1480x740.png\",\r\n                \"imagePosition\": 1\r\n            }\r\n        ]\r\n    },\r\n    \"newFrom\": \"2024-12-30\",\r\n    \"newTo\": \"2024-12-31\",\r\n    \"published_at\": \"2022-11-23 09:32\",\r\n    \"PriceTaxation\": {\r\n        \"price\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"specialPrice\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"cost\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"isTaxable\": true,\r\n        \"orderMaxQuantityAllowed\": 300,\r\n        \"orderMinQuantityAllowed\": 10\r\n    },\r\n    \"ageVerificationRequire\": true,\r\n    \"available\": true,\r\n    \"channel\": [\r\n        \"PED\"\r\n    ],\r\n    \"Attributes\": {\r\n        \"customAttributes\": [\r\n            {\r\n                \"key\": \"test\",\r\n                \"value\": \"test2\",\r\n                \"isVariantAttribute\": false\r\n            }\r\n        ],\r\n        \"productCategoryAttributes\": [],\r\n        \"airlineSpecificAttribute\": [],\r\n        \"specialityAttribute\": [\r\n            {{speciality_attribute_id_1}}\r\n        ]\r\n    },\r\n    \"notApplicableCountries\": [\r\n        \"IN\",\r\n        \"US\"\r\n    ],\r\n    \"productSet\": \"Simple\",\r\n    \"storeProductId\": \"134A34\",\r\n    \"productType\": \"{{product_type}}\",\r\n    \"spotLight\": true,\r\n    \"isPerishable\": false,\r\n    \"requireShipping\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/product"}, "response": []}, {"name": "JAMA_12668094_Add Product-datevar delete", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["var moment = require('moment');", "pm.globals.set(\"todayDate1\",moment().add(3, 'day').toISOString());", "pm.globals.set(\"endDate1\", moment().add(4, 'day').toISOString());", "let response1 = pm.response.json();let product_id_delete = response1.id;console.log(product_id_delete);pm.environment.set('product_id_delete',product_id_delete);", "let response = pm.response.json();let product_id = response.id;console.log(product_id);pm.environment.set('product_id',product_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "language", "value": "en", "type": "text"}, {"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"formatVersion\": \"2\",\r\n    \"sku\": \"product{{$randomFirstName}}rrt\",\r\n    \"name\": {\r\n        \"en\": \"Loreal Shampoo for Hair Growth\",\r\n        \"zh_<PERSON>\": \"欧莱雅头发生长洗发水\",\r\n        \"ms\": \"Syampu Loreal untuk Pertumbuhan Rambut\",\r\n        \"zh_Hant\": \"歐萊雅頭髮生長洗髮水\",\r\n        \"de\": \"Loreal Shampoo für das Haarwachstum\",\r\n        \"it\": \"Shampoo Loreal per la crescita dei capelli\",\r\n        \"pt_BR\": \"Shampoo Loreal para o crescimento do cabelo\",\r\n        \"fr\": \"Shampooing Loreal pour la croissance des cheveux\",\r\n        \"es\": \"Loreal Champú para el Crecimiento del Cabello\",\r\n        \"ko\": \"모발 성장을 위한 로레알 샴푸\",\r\n        \"ru\": \"Лореаль шампунь для роста волос\",\r\n        \"th\": \"ลอรีอัล แชมพูเพื่อการเจริญเติบโตของเส้นผม\",\r\n        \"vi\": \"Dầu gội Loreal kích thích mọc tóc\",\r\n        \"fil\": \"Loreal Shampoo para sa Paglago ng Buhok\",\r\n        \"ja\": \"髪の成長のためのロレアル シャンプー\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"shortDescription\": {\r\n        \"en\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo helps fight against the five visible signs of damaged hair - Hair fall, Dryness, Roughness, Dullness and Split ends without weighing it down.\",\r\n        \"zh_Hans\": \"L'Oréal Paris Total Repair 5 修护洗发水有助于对抗头发受损的五个明显迹象 - 脱发、干燥、粗糙、暗沉和分叉，而不会使头发变重。\",\r\n        \"ms\": \"L'Oréal Paris Total Repair 5 Repair Shampoo membantu melawan lima tanda rambut rosak yang boleh dilihat - Rambut gugur, Kering, Kekasaran, Kusam dan Bercabang berakhir tanpa membebankannya.\",\r\n        \"zh_Hant\": \"L'Oréal Paris Total Repair 5 修護洗髮水有助於對抗頭髮受損的五個明顯跡象 - 脫髮、乾燥、粗糙、暗沉和分叉，而不會使頭髮變重。\",\r\n        \"de\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo bekämpft die fünf sichtbaren Anzeichen von geschädigtem Haar – Haarausfall, Trockenheit, Rauheit, Stumpfheit und Spliss, ohne es zu beschweren.\",\r\n        \"it\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo aiuta a combattere i cinque segni visibili dei capelli danneggiati: caduta, secchezza, rugosità, opacità e doppie punte senza appesantire.\",\r\n        \"pt_BR\": \"L'Oréal Paris Total Repair 5 Shampoo Reparador ajuda a combater os cinco sinais visíveis de cabelos danificados - queda, ressecamento, aspereza, embotamento e pontas duplas sem sobrecarregá-lo.\",\r\n        \"fr\": \"Le shampooing réparateur Total Repair 5 de L'Oréal Paris aide à lutter contre les cinq signes visibles des cheveux abîmés - chute des cheveux, sécheresse, rugosité, matité et pointes fourchues sans les alourdir.\",\r\n        \"es\": \"El champú reparador L'Oréal Paris Total Repair 5 ayuda a combatir los cinco signos visibles del cabello dañado: caída del cabello, sequedad, aspereza, opacidad y puntas abiertas sin apelmazarlo.\",\r\n        \"ko\": \"로레알 파리 토탈 리페어 5 리페어링 샴푸는 5가지 눈에 보이는 손상 모발 징후(탈모, 건조함, 거칠음, 칙칙함, 갈라짐)를 억제하는 데 도움이 됩니다.\",\r\n        \"ru\": \"Восстанавливающий шампунь L'Oréal Paris Total Repair 5 помогает бороться с пятью видимыми признаками поврежденных волос - выпадением, сухостью, шероховатостью, тусклостью и секущимися кончиками, не утяжеляя их.\",\r\n        \"th\": \"ลอรีอัล ปารีส โททัล รีแพร์ 5 รีแพร์ริ่ง แชมพูช่วยต่อสู้กับสัญญาณห้าประการของเส้นผมที่เสียหาย - ผมร่วง ผมแห้ง หยาบกระด้าง หมองคล้ำ และแตกปลายโดยไม่ทำให้ผมหนักใจ\",\r\n        \"vi\": \"Dầu gội phục hồi L'Oréal Paris Total Repair 5 giúp chống lại năm dấu hiệu có thể nhìn thấy của tóc hư tổn - Tóc rụng, Khô, Xơ, Xỉn màu và Chẻ ngọn mà không làm nặng tóc.\",\r\n        \"fil\": \"Ang L'Oréal Paris Total Repair 5 Repairing Shampoo ay tumutulong sa paglaban sa limang nakikitang senyales ng nasirang buhok - Nawawala ang Buhok, Pagkatuyo, Pagkagaspang, Pagkapurol at Paghati nang hindi tumitimbang.\",\r\n        \"ja\": \"ロレアル パリ トータル リペア 5 リペアリング シャンプーは、ダメージを受けた髪の 5 つの目に見える兆候 (抜け毛、乾燥、ざらつき、くすみ、枝毛) を重くすることなく防ぎます。\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"description\": {\r\n        \"en\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo helps combat the five signs of damaged hair - Hair fall, Dryness, Roughness, Dullness, and Split ends without weighing it down. Damaged hair lacks the natural cement that keeps the hair strong and resilient. To make sure the hair gets the required strength, the L'Oréal Laboratories have included the Keratin XS technology to replicate the hair's natural cement to target the five major problems. The smallest form of Keratin XS deeply penetrates the fibres to ensure faster repair.Highlights:1. Powered by up to 17% Keratin XS + Repair concentrate2. Helps fight against the five visible signs of damaged hair - hair fall, dryness, roughness, dullness, and split ends3. Delivers 5X stronger and renewed hair from the first application\",\r\n        \"zh_Hans\": \"L'Oréal Paris Total Repair 5 修護洗髮水有助於對抗頭髮受損的五個跡象 - 脫髮、乾燥、粗糙、暗淡和分叉，而不會壓低頭髮。受損的頭髮缺乏使頭髮保持強韌和彈性的天然粘合劑。為確保頭髮獲得所需的強度，歐萊雅實驗室採用了 Keratin XS 技術來複製頭髮的天然粘合劑，以解決五個主要問題。最小形式的 Keratin XS 深入滲透纖維，確保更快修復。強調：1. 由高達 17% 的角蛋白 XS + 修復濃縮物提供動力2. 幫助對抗頭髮受損的五個明顯跡象 - 掉髮、乾燥、粗糙、暗沉和分叉3. 從第一次使用開始，頭髮就會強韌 5 倍，煥然一新\",\r\n        \"ms\": \"L'Oréal Paris Total Repair 5 Repair Shampoo membantu memerangi lima tanda rambut rosak - Rambut gugur, Kering, Kekasaran, Kusam dan Berpecah tanpa membebankannya. Rambut yang rosak tidak mempunyai simen semula jadi yang memastikan rambut kuat dan berdaya tahan. Untuk memastikan rambut mendapat kekuatan yang diperlukan, L'Oréal Laboratories telah memasukkan teknologi Keratin XS untuk meniru simen semula jadi rambut untuk menyasarkan lima masalah utama. Bentuk terkecil Keratin XS menembusi dalam gentian untuk memastikan pembaikan lebih cepat. Sorotan:1. Dikuasakan sehingga 17% Keratin XS + Pekat pembaikan2. Membantu melawan lima tanda rambut rosak yang boleh dilihat - rambut gugur, kering, kekasaran, kusam dan hujung bercabang3. Menghasilkan rambut 5X lebih kuat dan diperbaharui daripada penggunaan pertama\",\r\n        \"zh_Hant\": \"L'Oréal Paris Total Repair 5 修护洗发水有助于对抗头发受损的五个迹象 - 脱发、干燥、粗糙、暗淡和分叉，而不会压低头发。受损的头发缺乏使头发保持强韧和弹性的天然粘合剂。为确保头发获得所需的强度，欧莱雅实验室采用了 Keratin XS 技术来复制头发的天然粘合剂，以解决五个主要问题。最小形式的角蛋白 XS 深入渗透纤维以确保更快的修复。亮点：1。由高达 17% 的角蛋白 XS + 修复浓缩物提供支持2。帮助对抗头发受损的五个明显迹象 - 脱发、干燥、粗糙、暗淡和分叉 3。首次使用即可使头发强韧 5 倍，焕发新生\",\r\n        \"de\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo hilft, die fünf Anzeichen von geschädigtem Haar zu bekämpfen - Haarausfall, Trockenheit, Rauheit, Mattheit und Spliss, ohne es zu beschweren. Geschädigtem Haar fehlt der natürliche Zement, der das Haar stark und widerstandsfähig hält. Um sicherzustellen, dass das Haar die erforderliche Stärke erhält, haben die L'Oréal Laboratories die Keratin XS-Technologie integriert, um den natürlichen Zement des Haares zu replizieren, um die fünf Hauptprobleme anzugehen. Die kleinste Form von Keratin XS dringt tief in die Fasern ein, um eine schnellere Reparatur zu gewährleisten.Highlights:1. Angetrieben von bis zu 17 % Keratin XS + Repair-Konzentrat2. Hilft bei der Bekämpfung der fünf sichtbaren Anzeichen von geschädigtem Haar – Haarausfall, Trockenheit, Rauheit, Mattigkeit und Spliss3. Liefert 5x kräftigeres und erneuertes Haar ab der ersten Anwendung\",\r\n        \"it\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo aiuta a combattere i cinque segni dei capelli danneggiati: caduta, secchezza, rugosità, opacità e doppie punte senza appesantire. I capelli danneggiati mancano del cemento naturale che mantiene i capelli forti e resistenti. Per assicurarsi che i capelli ricevano la forza necessaria, i Laboratori L'Oréal hanno incluso la tecnologia Keratin XS per replicare il cemento naturale dei capelli per affrontare i cinque problemi principali. La forma più piccola di Keratin XS penetra in profondità nelle fibre per garantire una riparazione più rapida.In evidenza:1. Potenziato fino al 17% di cheratina XS + concentrato riparatore2. Aiuta a combattere i cinque segni visibili dei capelli danneggiati: caduta dei capelli, secchezza, rugosità, opacità e doppie punte3. Fornisce capelli 5 volte più forti e rinnovati dalla prima applicazione\",\r\n        \"pt_BR\": \"L'Oréal Paris Total Repair 5 Shampoo Reparador ajuda a combater os cinco sinais de cabelos danificados - queda, ressecamento, aspereza, embotamento e pontas duplas sem pesar. O cabelo danificado carece do cimento natural que mantém o cabelo forte e resistente. Para garantir que o cabelo obtenha a força necessária, os Laboratórios L'Oréal incluíram a tecnologia Keratin XS para replicar o cimento natural do cabelo para resolver os cinco principais problemas. A menor forma de Keratin XS penetra profundamente nas fibras para garantir um reparo mais rápido.Destaques:1. Alimentado por até 17% de Queratina XS + Concentrado de reparação2. Ajuda a combater os cinco sinais visíveis de cabelos danificados - queda, ressecamento, aspereza, opacidade e pontas duplas3. Proporciona cabelos 5X mais fortes e renovados desde a primeira aplicação\",\r\n        \"fr\": \"Le shampooing réparateur Total Repair 5 de L'Oréal Paris aide à combattre les cinq signes des cheveux abîmés : chute des cheveux, sécheresse, rugosité, matité et pointes fourchues sans les alourdir. Les cheveux abîmés manquent du ciment naturel qui maintient les cheveux forts et résistants. Pour s'assurer que les cheveux obtiennent la force requise, les Laboratoires L'Oréal ont inclus la technologie Keratin XS pour répliquer le ciment naturel des cheveux afin de cibler les cinq problèmes majeurs. La plus petite forme de kératine XS pénètre profondément dans les fibres pour assurer une réparation plus rapide.Points forts :1. Alimenté par jusqu'à 17 % de kératine XS + concentré de réparation2. Aide à lutter contre les cinq signes visibles des cheveux abîmés - chute des cheveux, sécheresse, rugosité, matité et pointes fourchues3. Fournit des cheveux 5 fois plus forts et renouvelés dès la première application\",\r\n        \"es\": \"El champú reparador L'Oréal Paris Total Repair 5 ayuda a combatir los cinco signos del cabello dañado: caída del cabello, sequedad, aspereza, opacidad y puntas abiertas sin apelmazarlo. El cabello dañado carece del cemento natural que mantiene el cabello fuerte y resistente. Para asegurarse de que el cabello obtenga la fuerza necesaria, los Laboratorios L'Oréal han incluido la tecnología Keratin XS para replicar el cemento natural del cabello para abordar los cinco problemas principales. La forma más pequeña de Keratin XS penetra profundamente en las fibras para garantizar una reparación más rápida. Aspectos destacados: 1. Potenciado con hasta un 17 % de queratina XS + concentrado reparador2. Ayuda a luchar contra los cinco signos visibles del cabello dañado: caída del cabello, sequedad, aspereza, falta de brillo y puntas abiertas3. Brinda un cabello 5 veces más fuerte y renovado desde la primera aplicación.\",\r\n        \"ko\": \"로레알 파리 토탈 리페어 5 리페어링 샴푸는 손상된 모발의 5가지 징후(탈모, 건조함, 거칠음, 칙칙함, 갈라짐)를 억제하는 데 도움이 됩니다. 손상된 모발에는 모발을 강하고 탄력 있게 유지하는 천연 시멘트가 부족합니다. 모발이 필요한 강도를 갖도록 하기 위해 L'Oréal Laboratories는 Keratin XS 기술을 포함하여 모발의 천연 시멘트를 복제하여 5가지 주요 문제를 해결했습니다. 가장 작은 형태의 케라틴 XS가 섬유질 깊숙이 침투하여 빠른 복구를 보장합니다.하이라이트:1. 최대 17% 케라틴 XS + 리페어 컨센트레이트2로 구동됩니다. 손상된 모발의 눈에 보이는 5가지 징후(탈모, 건조함, 거칠음, 칙칙함, 모발 끝 갈라짐)를 방지합니다3. 첫 번째 적용에서 5배 더 강하고 재생된 모발 제공\",\r\n        \"ru\": \"Восстанавливающий шампунь L'Oréal Paris Total Repair 5 помогает бороться с пятью признаками поврежденных волос: выпадением, сухостью, жесткостью, тусклостью и секущимися кончиками, не утяжеляя их. Поврежденным волосам не хватает природного цемента, который делает их сильными и эластичными. Чтобы волосы приобрели необходимую силу, лаборатории L'Oréal включили технологию Keratin XS, имитирующую естественный цемент волос для решения пяти основных проблем. Самая маленькая форма кератина XS глубоко проникает в волокна, обеспечивая более быстрое восстановление. Особенности:1. Содержит до 17% кератина XS + восстанавливающий концентрат2. Помогает бороться с пятью видимыми признаками поврежденных волос - выпадением, сухостью, жесткостью, тусклостью и секущимися кончиками3. Делает волосы в 5 раз более сильными и обновленными с первого применения.\",\r\n        \"th\": \"ลอรีอัล ปารีส โททอล รีแพร์ 5 รีแพร์ริ่ง แชมพู ช่วยต่อต้านสัญญาณห้าประการของเส้นผมที่เสียหาย - ผมร่วง แห้งเสีย หยาบกระด้าง หมองคล้ำ และแตกปลายโดยไม่ทำให้ผมหนักศีรษะ ผมเสียขาดซีเมนต์ธรรมชาติที่ช่วยให้ผมแข็งแรงและยืดหยุ่น เพื่อให้แน่ใจว่าเส้นผมได้รับความแข็งแรงตามที่ต้องการ ลอรีอัล แลบอราทอรีส์ได้รวมเอาเทคโนโลยีเคราติน XS เพื่อจำลองซีเมนต์ตามธรรมชาติของเส้นผมเพื่อกำหนดเป้าหมายปัญหาหลัก 5 ประการ Keratin XS ในรูปแบบที่เล็กที่สุดจะแทรกซึมเข้าไปในเส้นใยอย่างล้ำลึกเพื่อให้แน่ใจว่าการซ่อมแซมจะเร็วขึ้นไฮไลท์:1. ขับเคลื่อนด้วย Keratin XS + Repair Concentrated มากถึง 17%2. ช่วยต่อสู้กับห้าสัญญาณที่มองเห็นได้ของเส้นผมที่เสียหาย - ผมร่วง แห้ง หยาบกร้าน หมองคล้ำ และแตกปลาย3. ให้ผมแข็งแรงขึ้นและเกิดใหม่ขึ้น 5 เท่าตั้งแต่ครั้งแรกที่ใช้\",\r\n        \"vi\": \"Dầu gội phục hồi L'Oréal Paris Total Repair 5 giúp chống lại năm dấu hiệu của tóc hư tổn - Rụng tóc, Khô, Xơ, Xỉn màu và Chẻ ngọn mà không làm tóc bị nặng. Tóc hư tổn thiếu xi măng tự nhiên giúp tóc chắc khỏe và đàn hồi. Để đảm bảo tóc có được độ chắc khỏe cần thiết, Phòng thí nghiệm L'Oréal đã đưa vào công nghệ Keratin XS để tái tạo xi măng tự nhiên của tóc nhằm giải quyết năm vấn đề chính. Dạng Keratin XS nhỏ nhất thấm sâu vào sợi vải để đảm bảo sửa chữa nhanh hơn. Điểm nổi bật:1. Được hỗ trợ lên đến 17% Keratin XS + Repair cô đặc2. Giúp chống lại năm dấu hiệu có thể nhìn thấy của tóc hư tổn - tóc gãy rụng, khô, thô ráp, xỉn màu và chẻ ngọn3. Mang lại mái tóc khỏe và mới hơn gấp 5 lần ngay từ lần sử dụng đầu tiên\",\r\n        \"fil\": \"Ang L'Oréal Paris Total Repair 5 Repairing Shampoo ay nakakatulong na labanan ang limang senyales ng nasirang buhok - Nawawala ang Buhok, Pagkatuyo, Pagkagaspang, Pagkapurol, at Paghiwa-hiwalayin nang hindi ito tinitimbang. Ang nasirang buhok ay kulang sa natural na semento na nagpapanatili sa buhok na malakas at nababanat. Upang matiyak na nakukuha ng buhok ang kinakailangang lakas, isinama ng L'Oréal Laboratories ang teknolohiyang Keratin XS upang kopyahin ang natural na semento ng buhok upang i-target ang limang pangunahing problema. Ang pinakamaliit na anyo ng Keratin XS ay malalim na tumatagos sa mga hibla upang matiyak ang mas mabilis na pagkumpuni. Mga Highlight:1. Pinapatakbo ng hanggang 17% Keratin XS + Repair concentrate2. Tumutulong na labanan ang limang nakikitang palatandaan ng nasirang buhok - pagkalagas ng buhok, pagkatuyo, pagkamagaspang, pagkapurol, at split ends3. Naghahatid ng 5X mas malakas at na-renew na buhok mula sa unang aplikasyon\",\r\n        \"ja\": \"ロレアル パリ トータル リペア 5 リペアリング シャンプーは、ダメージを受けた髪の 5 つの兆候 (抜け毛、乾燥、ざらつき、くすみ、枝毛) を重くすることなくケアします。傷んだ髪には、髪を強く弾力のある状態に保つ天然のセメントが不足しています。髪が必要な強度を確実に得るために、ロレアル ラボラトリーズはケラチン XS テクノロジーを組み込んで、髪の自然なセメントを再現し、5 つの主要な問題をターゲットにしました。最小の形のケラチン XS が繊維に深く浸透し、より迅速な修復を保証します。ハイライト:1.最大 17% のケラチン XS + リペア コンセントレート2 を搭載。目に見える傷みの5つのサイン（抜け毛・パサつき・パサつき・くすみ・枝毛）をケア。最初のアプリケーションから 5 倍の強さと再生された髪を提供します。\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"visibilityScope\": \"search\",\r\n    \"brand\": {\r\n        \"en\": \"Loreal Shampoo for Hair Growth\",\r\n        \"zh_Hans\": \"欧莱雅头发生长洗发水\",\r\n        \"ms\": \"Syampu Loreal untuk Pertumbuhan Rambut\",\r\n        \"zh_Hant\": \"歐萊雅頭髮生長洗髮水\",\r\n        \"de\": \"Loreal Shampoo für das Haarwachstum\",\r\n        \"it\": \"Shampoo Loreal per la crescita dei capelli\",\r\n        \"pt_BR\": \"Shampoo Loreal para o crescimento do cabelo\",\r\n        \"fr\": \"Shampooing Loreal pour la croissance des cheveux\",\r\n        \"es\": \"Loreal Champú para el Crecimiento del Cabello\",\r\n        \"ko\": \"모발 성장을 위한 로레알 샴푸\",\r\n        \"ru\": \"Лореаль шампунь для роста волос\",\r\n        \"th\": \"ลอรีอัล แชมพูเพื่อการเจริญเติบโตของเส้นผม\",\r\n        \"vi\": \"Dầu gội Loreal kích thích mọc tóc\",\r\n        \"fil\": \"Loreal Shampoo para sa Paglago ng Buhok\",\r\n        \"ja\": \"髪の成長のためのロレアル シャンプー\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να \",\r\n        \"nl\": \"Shampoos \",\r\n        \"he\": \"כדי להבטין לשכפול המاوف\",\r\n        \"hi\": \"केराटिन तकनीक\"\r\n    },\r\n    \"barcode\": \"TCGDA050111\",\r\n    \"deliveryMethod\": [\r\n        \"{{delivery_method}}\"\r\n    ],\r\n    \"category\": [\r\n        {{category_id}}\r\n    ],\r\n    \"shippingAttributes\": {\r\n        \"weight\": 5,\r\n        \"weightUnit\": \"LB\",\r\n        \"height\": \"\",\r\n        \"heightUnit\": \"\",\r\n        \"width\": \"\",\r\n        \"widthUnit\": \"\",\r\n        \"length\": \"\",\r\n        \"lengthUnit\": \"\"\r\n    },\r\n    \"assets\": {\r\n        \"images\": [\r\n            {\r\n                \"image1_1\": \"https://placehold.jp/1200x1200.png\",\r\n                \"image2_1\": \"https://placehold.jp/1480x740.png\",\r\n                \"image2_3\": \"https://placehold.jp/944x1416.png\",\r\n                \"image4_3\": \"https://placehold.jp/760x570.png\",\r\n                \"image5_6\": \"https://placehold.jp/670x804.png\"\r\n            }\r\n        ],\r\n        \"gallery\": [\r\n            {\r\n                \"url\": \"https://placehold.jp/1480x740.png\",\r\n                \"imagePosition\": 1\r\n            }\r\n        ]\r\n    },\r\n    \"newFrom\": \"2024-12-30\",\r\n    \"newTo\": \"2024-12-31\",\r\n    \"published_at\": \"2022-11-23 09:32\",\r\n    \"PriceTaxation\": {\r\n        \"price\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"specialPrice\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"cost\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"isTaxable\": true,\r\n        \"orderMaxQuantityAllowed\": 300,\r\n        \"orderMinQuantityAllowed\": 10\r\n    },\r\n    \"ageVerificationRequire\": true,\r\n    \"available\": true,\r\n    \"channel\": [\r\n        \"PED\"\r\n    ],\r\n    \"Attributes\": {\r\n        \"customAttributes\": [\r\n            {\r\n                \"key\": \"test\",\r\n                \"value\": \"test2\",\r\n                \"isVariantAttribute\": false\r\n            }\r\n        ],\r\n        \"productCategoryAttributes\": [],\r\n        \"airlineSpecificAttribute\": [],\r\n        \"specialityAttribute\": [\r\n            1267,\r\n            1268\r\n        ]\r\n    },\r\n    \"notApplicableCountries\": [\r\n        \"IN\",\r\n        \"US\"\r\n    ],\r\n    \"productSet\": \"Simple\",\r\n    \"storeProductId\": \"134A34\",\r\n    \"productType\": \"{{product_type}}\",\r\n    \"spotLight\": true,\r\n    \"isPerishable\": false,\r\n    \"requireShipping\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/product"}, "response": []}, {"name": "JAMA_12704053_Get Product By Id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/product/{{product_id}}"}, "response": []}, {"name": "JAMA_12704054_Get Product Variant", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/product/{{product_id}}/variant"}, "response": []}, {"name": "JAMA_12668095_Update Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"formatVersion\": \"2\",\r\n    \"sku\": \"product{{$randomFirstName}}xxc1\",\r\n    \"name\": {\r\n        \"en\": \"Loreal Shampoo for Hair Growth\",\r\n        \"zh_<PERSON>\": \"欧莱雅头发生长洗发水\",\r\n        \"ms\": \"Syampu Loreal untuk Pertumbuhan Rambut\",\r\n        \"zh_Hant\": \"歐萊雅頭髮生長洗髮水\",\r\n        \"de\": \"Loreal Shampoo für das Haarwachstum\",\r\n        \"it\": \"Shampoo Loreal per la crescita dei capelli\",\r\n        \"pt_BR\": \"Shampoo Loreal para o crescimento do cabelo\",\r\n        \"fr\": \"Shampooing Loreal pour la croissance des cheveux\",\r\n        \"es\": \"Loreal Champú para el Crecimiento del Cabello\",\r\n        \"ko\": \"모발 성장을 위한 로레알 샴푸\",\r\n        \"ru\": \"Лореаль шампунь для роста волос\",\r\n        \"th\": \"ลอรีอัล แชมพูเพื่อการเจริญเติบโตของเส้นผม\",\r\n        \"vi\": \"Dầu gội Loreal kích thích mọc tóc\",\r\n        \"fil\": \"Loreal Shampoo para sa Paglago ng Buhok\",\r\n        \"ja\": \"髪の成長のためのロレアル シャンプー\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n\r\n    },\r\n    \"shortDescription\": {\r\n        \"en\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo helps fight against the five visible signs of damaged hair - Hair fall, Dryness, Roughness, Dullness and Split ends without weighing it down.\",\r\n        \"zh_Hans\": \"L'Oréal Paris Total Repair 5 修护洗发水有助于对抗头发受损的五个明显迹象 - 脱发、干燥、粗糙、暗沉和分叉，而不会使头发变重。\",\r\n        \"ms\": \"L'Oréal Paris Total Repair 5 Repair Shampoo membantu melawan lima tanda rambut rosak yang boleh dilihat - Rambut gugur, Kering, Kekasaran, Kusam dan Bercabang berakhir tanpa membebankannya.\",\r\n        \"zh_Hant\": \"L'Oréal Paris Total Repair 5 修護洗髮水有助於對抗頭髮受損的五個明顯跡象 - 脫髮、乾燥、粗糙、暗沉和分叉，而不會使頭髮變重。\",\r\n        \"de\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo bekämpft die fünf sichtbaren Anzeichen von geschädigtem Haar – Haarausfall, Trockenheit, Rauheit, Stumpfheit und Spliss, ohne es zu beschweren.\",\r\n        \"it\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo aiuta a combattere i cinque segni visibili dei capelli danneggiati: caduta, secchezza, rugosità, opacità e doppie punte senza appesantire.\",\r\n        \"pt_BR\": \"L'Oréal Paris Total Repair 5 Shampoo Reparador ajuda a combater os cinco sinais visíveis de cabelos danificados - queda, ressecamento, aspereza, embotamento e pontas duplas sem sobrecarregá-lo.\",\r\n        \"fr\": \"Le shampooing réparateur Total Repair 5 de L'Oréal Paris aide à lutter contre les cinq signes visibles des cheveux abîmés - chute des cheveux, sécheresse, rugosité, matité et pointes fourchues sans les alourdir.\",\r\n        \"es\": \"El champú reparador L'Oréal Paris Total Repair 5 ayuda a combatir los cinco signos visibles del cabello dañado: caída del cabello, sequedad, aspereza, opacidad y puntas abiertas sin apelmazarlo.\",\r\n        \"ko\": \"로레알 파리 토탈 리페어 5 리페어링 샴푸는 5가지 눈에 보이는 손상 모발 징후(탈모, 건조함, 거칠음, 칙칙함, 갈라짐)를 억제하는 데 도움이 됩니다.\",\r\n        \"ru\": \"Восстанавливающий шампунь L'Oréal Paris Total Repair 5 помогает бороться с пятью видимыми признаками поврежденных волос - выпадением, сухостью, шероховатостью, тусклостью и секущимися кончиками, не утяжеляя их.\",\r\n        \"th\": \"ลอรีอัล ปารีส โททัล รีแพร์ 5 รีแพร์ริ่ง แชมพูช่วยต่อสู้กับสัญญาณห้าประการของเส้นผมที่เสียหาย - ผมร่วง ผมแห้ง หยาบกระด้าง หมองคล้ำ และแตกปลายโดยไม่ทำให้ผมหนักใจ\",\r\n        \"vi\": \"Dầu gội phục hồi L'Oréal Paris Total Repair 5 giúp chống lại năm dấu hiệu có thể nhìn thấy của tóc hư tổn - Tóc rụng, Khô, Xơ, Xỉn màu và Chẻ ngọn mà không làm nặng tóc.\",\r\n        \"fil\": \"Ang L'Oréal Paris Total Repair 5 Repairing Shampoo ay tumutulong sa paglaban sa limang nakikitang senyales ng nasirang buhok - Nawawala ang Buhok, Pagkatuyo, Pagkagaspang, Pagkapurol at Paghati nang hindi tumitimbang.\",\r\n        \"ja\": \"ロレアル パリ トータル リペア 5 リペアリング シャンプーは、ダメージを受けた髪の 5 つの目に見える兆候 (抜け毛、乾燥、ざらつき、くすみ、枝毛) を重くすることなく防ぎます。\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"description\": {\r\n        \"en\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo helps combat the five signs of damaged hair - Hair fall, Dryness, Roughness, Dullness, and Split ends without weighing it down. Damaged hair lacks the natural cement that keeps the hair strong and resilient. To make sure the hair gets the required strength, the L'Oréal Laboratories have included the Keratin XS technology to replicate the hair's natural cement to target the five major problems. The smallest form of Keratin XS deeply penetrates the fibres to ensure faster repair.Highlights:1. Powered by up to 17% Keratin XS + Repair concentrate2. Helps fight against the five visible signs of damaged hair - hair fall, dryness, roughness, dullness, and split ends3. Delivers 5X stronger and renewed hair from the first application\",\r\n        \"zh_Hans\": \"L'Oréal Paris Total Repair 5 修護洗髮水有助於對抗頭髮受損的五個跡象 - 脫髮、乾燥、粗糙、暗淡和分叉，而不會壓低頭髮。受損的頭髮缺乏使頭髮保持強韌和彈性的天然粘合劑。為確保頭髮獲得所需的強度，歐萊雅實驗室採用了 Keratin XS 技術來複製頭髮的天然粘合劑，以解決五個主要問題。最小形式的 Keratin XS 深入滲透纖維，確保更快修復。強調：1. 由高達 17% 的角蛋白 XS + 修復濃縮物提供動力2. 幫助對抗頭髮受損的五個明顯跡象 - 掉髮、乾燥、粗糙、暗沉和分叉3. 從第一次使用開始，頭髮就會強韌 5 倍，煥然一新\",\r\n        \"ms\": \"L'Oréal Paris Total Repair 5 Repair Shampoo membantu memerangi lima tanda rambut rosak - Rambut gugur, Kering, Kekasaran, Kusam dan Berpecah tanpa membebankannya. Rambut yang rosak tidak mempunyai simen semula jadi yang memastikan rambut kuat dan berdaya tahan. Untuk memastikan rambut mendapat kekuatan yang diperlukan, L'Oréal Laboratories telah memasukkan teknologi Keratin XS untuk meniru simen semula jadi rambut untuk menyasarkan lima masalah utama. Bentuk terkecil Keratin XS menembusi dalam gentian untuk memastikan pembaikan lebih cepat. Sorotan:1. Dikuasakan sehingga 17% Keratin XS + Pekat pembaikan2. Membantu melawan lima tanda rambut rosak yang boleh dilihat - rambut gugur, kering, kekasaran, kusam dan hujung bercabang3. Menghasilkan rambut 5X lebih kuat dan diperbaharui daripada penggunaan pertama\",\r\n        \"zh_Hant\": \"L'Oréal Paris Total Repair 5 修护洗发水有助于对抗头发受损的五个迹象 - 脱发、干燥、粗糙、暗淡和分叉，而不会压低头发。受损的头发缺乏使头发保持强韧和弹性的天然粘合剂。为确保头发获得所需的强度，欧莱雅实验室采用了 Keratin XS 技术来复制头发的天然粘合剂，以解决五个主要问题。最小形式的角蛋白 XS 深入渗透纤维以确保更快的修复。亮点：1。由高达 17% 的角蛋白 XS + 修复浓缩物提供支持2。帮助对抗头发受损的五个明显迹象 - 脱发、干燥、粗糙、暗淡和分叉 3。首次使用即可使头发强韧 5 倍，焕发新生\",\r\n        \"de\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo hilft, die fünf Anzeichen von geschädigtem Haar zu bekämpfen - Haarausfall, Trockenheit, Rauheit, Mattheit und Spliss, ohne es zu beschweren. Geschädigtem Haar fehlt der natürliche Zement, der das Haar stark und widerstandsfähig hält. Um sicherzustellen, dass das Haar die erforderliche Stärke erhält, haben die L'Oréal Laboratories die Keratin XS-Technologie integriert, um den natürlichen Zement des Haares zu replizieren, um die fünf Hauptprobleme anzugehen. Die kleinste Form von Keratin XS dringt tief in die Fasern ein, um eine schnellere Reparatur zu gewährleisten.Highlights:1. Angetrieben von bis zu 17 % Keratin XS + Repair-Konzentrat2. Hilft bei der Bekämpfung der fünf sichtbaren Anzeichen von geschädigtem Haar – Haarausfall, Trockenheit, Rauheit, Mattigkeit und Spliss3. Liefert 5x kräftigeres und erneuertes Haar ab der ersten Anwendung\",\r\n        \"it\": \"L'Oréal Paris Total Repair 5 Repairing Shampoo aiuta a combattere i cinque segni dei capelli danneggiati: caduta, secchezza, rugosità, opacità e doppie punte senza appesantire. I capelli danneggiati mancano del cemento naturale che mantiene i capelli forti e resistenti. Per assicurarsi che i capelli ricevano la forza necessaria, i Laboratori L'Oréal hanno incluso la tecnologia Keratin XS per replicare il cemento naturale dei capelli per affrontare i cinque problemi principali. La forma più piccola di Keratin XS penetra in profondità nelle fibre per garantire una riparazione più rapida.In evidenza:1. Potenziato fino al 17% di cheratina XS + concentrato riparatore2. Aiuta a combattere i cinque segni visibili dei capelli danneggiati: caduta dei capelli, secchezza, rugosità, opacità e doppie punte3. Fornisce capelli 5 volte più forti e rinnovati dalla prima applicazione\",\r\n        \"pt_BR\": \"L'Oréal Paris Total Repair 5 Shampoo Reparador ajuda a combater os cinco sinais de cabelos danificados - queda, ressecamento, aspereza, embotamento e pontas duplas sem pesar. O cabelo danificado carece do cimento natural que mantém o cabelo forte e resistente. Para garantir que o cabelo obtenha a força necessária, os Laboratórios L'Oréal incluíram a tecnologia Keratin XS para replicar o cimento natural do cabelo para resolver os cinco principais problemas. A menor forma de Keratin XS penetra profundamente nas fibras para garantir um reparo mais rápido.Destaques:1. Alimentado por até 17% de Queratina XS + Concentrado de reparação2. Ajuda a combater os cinco sinais visíveis de cabelos danificados - queda, ressecamento, aspereza, opacidade e pontas duplas3. Proporciona cabelos 5X mais fortes e renovados desde a primeira aplicação\",\r\n        \"fr\": \"Le shampooing réparateur Total Repair 5 de L'Oréal Paris aide à combattre les cinq signes des cheveux abîmés : chute des cheveux, sécheresse, rugosité, matité et pointes fourchues sans les alourdir. Les cheveux abîmés manquent du ciment naturel qui maintient les cheveux forts et résistants. Pour s'assurer que les cheveux obtiennent la force requise, les Laboratoires L'Oréal ont inclus la technologie Keratin XS pour répliquer le ciment naturel des cheveux afin de cibler les cinq problèmes majeurs. La plus petite forme de kératine XS pénètre profondément dans les fibres pour assurer une réparation plus rapide.Points forts :1. Alimenté par jusqu'à 17 % de kératine XS + concentré de réparation2. Aide à lutter contre les cinq signes visibles des cheveux abîmés - chute des cheveux, sécheresse, rugosité, matité et pointes fourchues3. Fournit des cheveux 5 fois plus forts et renouvelés dès la première application\",\r\n        \"es\": \"El champú reparador L'Oréal Paris Total Repair 5 ayuda a combatir los cinco signos del cabello dañado: caída del cabello, sequedad, aspereza, opacidad y puntas abiertas sin apelmazarlo. El cabello dañado carece del cemento natural que mantiene el cabello fuerte y resistente. Para asegurarse de que el cabello obtenga la fuerza necesaria, los Laboratorios L'Oréal han incluido la tecnología Keratin XS para replicar el cemento natural del cabello para abordar los cinco problemas principales. La forma más pequeña de Keratin XS penetra profundamente en las fibras para garantizar una reparación más rápida. Aspectos destacados: 1. Potenciado con hasta un 17 % de queratina XS + concentrado reparador2. Ayuda a luchar contra los cinco signos visibles del cabello dañado: caída del cabello, sequedad, aspereza, falta de brillo y puntas abiertas3. Brinda un cabello 5 veces más fuerte y renovado desde la primera aplicación.\",\r\n        \"ko\": \"로레알 파리 토탈 리페어 5 리페어링 샴푸는 손상된 모발의 5가지 징후(탈모, 건조함, 거칠음, 칙칙함, 갈라짐)를 억제하는 데 도움이 됩니다. 손상된 모발에는 모발을 강하고 탄력 있게 유지하는 천연 시멘트가 부족합니다. 모발이 필요한 강도를 갖도록 하기 위해 L'Oréal Laboratories는 Keratin XS 기술을 포함하여 모발의 천연 시멘트를 복제하여 5가지 주요 문제를 해결했습니다. 가장 작은 형태의 케라틴 XS가 섬유질 깊숙이 침투하여 빠른 복구를 보장합니다.하이라이트:1. 최대 17% 케라틴 XS + 리페어 컨센트레이트2로 구동됩니다. 손상된 모발의 눈에 보이는 5가지 징후(탈모, 건조함, 거칠음, 칙칙함, 모발 끝 갈라짐)를 방지합니다3. 첫 번째 적용에서 5배 더 강하고 재생된 모발 제공\",\r\n        \"ru\": \"Восстанавливающий шампунь L'Oréal Paris Total Repair 5 помогает бороться с пятью признаками поврежденных волос: выпадением, сухостью, жесткостью, тусклостью и секущимися кончиками, не утяжеляя их. Поврежденным волосам не хватает природного цемента, который делает их сильными и эластичными. Чтобы волосы приобрели необходимую силу, лаборатории L'Oréal включили технологию Keratin XS, имитирующую естественный цемент волос для решения пяти основных проблем. Самая маленькая форма кератина XS глубоко проникает в волокна, обеспечивая более быстрое восстановление. Особенности:1. Содержит до 17% кератина XS + восстанавливающий концентрат2. Помогает бороться с пятью видимыми признаками поврежденных волос - выпадением, сухостью, жесткостью, тусклостью и секущимися кончиками3. Делает волосы в 5 раз более сильными и обновленными с первого применения.\",\r\n        \"th\": \"ลอรีอัล ปารีส โททอล รีแพร์ 5 รีแพร์ริ่ง แชมพู ช่วยต่อต้านสัญญาณห้าประการของเส้นผมที่เสียหาย - ผมร่วง แห้งเสีย หยาบกระด้าง หมองคล้ำ และแตกปลายโดยไม่ทำให้ผมหนักศีรษะ ผมเสียขาดซีเมนต์ธรรมชาติที่ช่วยให้ผมแข็งแรงและยืดหยุ่น เพื่อให้แน่ใจว่าเส้นผมได้รับความแข็งแรงตามที่ต้องการ ลอรีอัล แลบอราทอรีส์ได้รวมเอาเทคโนโลยีเคราติน XS เพื่อจำลองซีเมนต์ตามธรรมชาติของเส้นผมเพื่อกำหนดเป้าหมายปัญหาหลัก 5 ประการ Keratin XS ในรูปแบบที่เล็กที่สุดจะแทรกซึมเข้าไปในเส้นใยอย่างล้ำลึกเพื่อให้แน่ใจว่าการซ่อมแซมจะเร็วขึ้นไฮไลท์:1. ขับเคลื่อนด้วย Keratin XS + Repair Concentrated มากถึง 17%2. ช่วยต่อสู้กับห้าสัญญาณที่มองเห็นได้ของเส้นผมที่เสียหาย - ผมร่วง แห้ง หยาบกร้าน หมองคล้ำ และแตกปลาย3. ให้ผมแข็งแรงขึ้นและเกิดใหม่ขึ้น 5 เท่าตั้งแต่ครั้งแรกที่ใช้\",\r\n        \"vi\": \"Dầu gội phục hồi L'Oréal Paris Total Repair 5 giúp chống lại năm dấu hiệu của tóc hư tổn - Rụng tóc, Khô, Xơ, Xỉn màu và Chẻ ngọn mà không làm tóc bị nặng. Tóc hư tổn thiếu xi măng tự nhiên giúp tóc chắc khỏe và đàn hồi. Để đảm bảo tóc có được độ chắc khỏe cần thiết, Phòng thí nghiệm L'Oréal đã đưa vào công nghệ Keratin XS để tái tạo xi măng tự nhiên của tóc nhằm giải quyết năm vấn đề chính. Dạng Keratin XS nhỏ nhất thấm sâu vào sợi vải để đảm bảo sửa chữa nhanh hơn. Điểm nổi bật:1. Được hỗ trợ lên đến 17% Keratin XS + Repair cô đặc2. Giúp chống lại năm dấu hiệu có thể nhìn thấy của tóc hư tổn - tóc gãy rụng, khô, thô ráp, xỉn màu và chẻ ngọn3. Mang lại mái tóc khỏe và mới hơn gấp 5 lần ngay từ lần sử dụng đầu tiên\",\r\n        \"fil\": \"Ang L'Oréal Paris Total Repair 5 Repairing Shampoo ay nakakatulong na labanan ang limang senyales ng nasirang buhok - Nawawala ang Buhok, Pagkatuyo, Pagkagaspang, Pagkapurol, at Paghiwa-hiwalayin nang hindi ito tinitimbang. Ang nasirang buhok ay kulang sa natural na semento na nagpapanatili sa buhok na malakas at nababanat. Upang matiyak na nakukuha ng buhok ang kinakailangang lakas, isinama ng L'Oréal Laboratories ang teknolohiyang Keratin XS upang kopyahin ang natural na semento ng buhok upang i-target ang limang pangunahing problema. Ang pinakamaliit na anyo ng Keratin XS ay malalim na tumatagos sa mga hibla upang matiyak ang mas mabilis na pagkumpuni. Mga Highlight:1. Pinapatakbo ng hanggang 17% Keratin XS + Repair concentrate2. Tumutulong na labanan ang limang nakikitang palatandaan ng nasirang buhok - pagkalagas ng buhok, pagkatuyo, pagkamagaspang, pagkapurol, at split ends3. Naghahatid ng 5X mas malakas at na-renew na buhok mula sa unang aplikasyon\",\r\n        \"ja\": \"ロレアル パリ トータル リペア 5 リペアリング シャンプーは、ダメージを受けた髪の 5 つの兆候 (抜け毛、乾燥、ざらつき、くすみ、枝毛) を重くすることなくケアします。傷んだ髪には、髪を強く弾力のある状態に保つ天然のセメントが不足しています。髪が必要な強度を確実に得るために、ロレアル ラボラトリーズはケラチン XS テクノロジーを組み込んで、髪の自然なセメントを再現し、5 つの主要な問題をターゲットにしました。最小の形のケラチン XS が繊維に深く浸透し、より迅速な修復を保証します。ハイライト:1.最大 17% のケラチン XS + リペア コンセントレート2 を搭載。目に見える傷みの5つのサイン（抜け毛・パサつき・パサつき・くすみ・枝毛）をケア。最初のアプリケーションから 5 倍の強さと再生された髪を提供します。\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να διασφαλιστεί ότι τα μαλλιά λαμβάνουν τη δύναμη που χρειάζονται, τα εργαστήρια της L'Oréal έχουν ενσωματώσει τεχνολογία κερατίνης για την αναπαραγωγή του φυσικού τσιμέντου τω\",\r\n        \"nl\": \"Shampoos zijn een essentieel onderdeel van alle haarverzorgingsregimes omdat ze de hoofdhuid reinigen, haarophoping verwijderen en je schoon en gezond ogend haar geven\",\r\n        \"he\": \"כדי להבטיח שהשיער יקבל את החוזק הדרוש לו, מעבדות לוריאל שילבו טכנולוגיית קרטין לשכפול המاوف\",\r\n        \"hi\": \"यह सुनिश्चित करने के लिए कि बालों को आवश्यक मजबूती मिले, L'Oréal प्रयोगशालाओं ने पांच प्रमुख समस्याओं को दूर करने के लिए बालों के प्राकृतिक सीमेंट को दोहराने के लिए केराटिन तकनीक को शामिल किया है।\"\r\n    },\r\n    \"visibilityScope\": \"search\",\r\n    \"brand\": {\r\n        \"en\": \"Loreal Shampoo for Hair Growth\",\r\n        \"zh_Hans\": \"欧莱雅头发生长洗发水\",\r\n        \"ms\": \"Syampu Loreal untuk Pertumbuhan Rambut\",\r\n        \"zh_Hant\": \"歐萊雅頭髮生長洗髮水\",\r\n        \"de\": \"Loreal Shampoo für das Haarwachstum\",\r\n        \"it\": \"Shampoo Loreal per la crescita dei capelli\",\r\n        \"pt_BR\": \"Shampoo Loreal para o crescimento do cabelo\",\r\n        \"fr\": \"Shampooing Loreal pour la croissance des cheveux\",\r\n        \"es\": \"Loreal Champú para el Crecimiento del Cabello\",\r\n        \"ko\": \"모발 성장을 위한 로레알 샴푸\",\r\n        \"ru\": \"Лореаль шампунь для роста волос\",\r\n        \"th\": \"ลอรีอัล แชมพูเพื่อการเจริญเติบโตของเส้นผม\",\r\n        \"vi\": \"Dầu gội Loreal kích thích mọc tóc\",\r\n        \"fil\": \"Loreal Shampoo para sa Paglago ng Buhok\",\r\n        \"ja\": \"髪の成長のためのロレアル シャンプー\",\r\n        \"ar\": \"لضمان حصول الشعر على القوة التي يحتاجها ، قامت مخ\",\r\n        \"el\": \"Για να \",\r\n        \"nl\": \"Shampoos \",\r\n        \"he\": \"כדי להבטין לשכפול המاوف\",\r\n        \"hi\": \"केराटिन तकनीक\"\r\n    },\r\n    \"barcode\": \"TCGDA050111\",\r\n    \"deliveryMethod\": [\r\n        \"{{delivery_method}}\"\r\n    ],\r\n    \"category\": [\r\n        {{category_id}}\r\n    ],\r\n    \"shippingAttributes\": {\r\n        \"weight\": 5,\r\n        \"weightUnit\": \"LB\",\r\n        \"height\": \"\",\r\n        \"heightUnit\": \"\",\r\n        \"width\": \"\",\r\n        \"widthUnit\": \"\",\r\n        \"length\": \"\",\r\n        \"lengthUnit\": \"\"\r\n    },\r\n    \"assets\": {\r\n        \"images\": [\r\n            {\r\n                \"image1_1\": \"https://placehold.jp/1200x1200.png\",\r\n                \"image2_1\": \"https://placehold.jp/1480x740.png\",\r\n                \"image2_3\": \"https://placehold.jp/944x1416.png\",\r\n                \"image4_3\": \"https://placehold.jp/760x570.png\",\r\n                \"image5_6\": \"https://placehold.jp/670x804.png\"\r\n            }\r\n        ],\r\n        \"gallery\": [\r\n            {\r\n                \"url\": \"https://placehold.jp/1480x740.png\",\r\n                \"imagePosition\": 1\r\n            }\r\n        ]\r\n    },\r\n   \"newFrom\": \"2024-12-30\",\r\n    \"newTo\": \"2024-12-31\",\r\n    \"published_at\": \"2022-11-23 09:32\",\r\n    \"PriceTaxation\": {\r\n        \"price\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"specialPrice\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"cost\": [\r\n            {\r\n                \"value\": 55,\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"isTaxable\": true,\r\n        \"orderMaxQuantityAllowed\": 300,\r\n        \"orderMinQuantityAllowed\": 10\r\n    },\r\n    \"ageVerificationRequire\": true,\r\n    \"available\": true,\r\n    \"channel\": [\r\n        \"PED\"\r\n    ],\r\n    \"Attributes\": {\r\n        \"customAttributes\": [\r\n            {\r\n                \"key\": \"test\",\r\n                \"value\": \"test2\",\r\n                \"isVariantAttribute\": false\r\n            }\r\n        ],\r\n        \"productCategoryAttributes\": [],\r\n        \"airlineSpecificAttribute\": [],\r\n        \"specialityAttribute\": [\r\n            1267,\r\n            1268\r\n        ]\r\n    },\r\n    \"notApplicableCountries\": [\r\n        \"IN\",\r\n        \"US\"\r\n    ],\r\n    \"productSet\": \"Simple\",\r\n    \"storeProductId\": \"134A34\",\r\n    \"productType\": \"{{product_type}}\",\r\n    \"spotLight\": true,\r\n    \"isPerishable\": false,\r\n    \"requireShipping\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/product/{{product_id}}"}, "response": []}, {"name": "JAMA_12668096_Patch Product", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"deliveryMethod\": [\r\n        \"{{delivery_method}}\"\r\n    ],\r\n    \"PriceTaxation\": {\r\n        \"price\": [\r\n            {\r\n                \"value\": \"0\",\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"specialPrice\": [\r\n            {\r\n                \"value\": \"0\",\r\n                \"currency\": \"USD\"\r\n            }\r\n        ],\r\n        \"cost\": [\r\n            {\r\n                \"value\": \"0\",\r\n                \"currency\": \"USD\"\r\n            }\r\n        ]\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/product/{{product_id}}"}, "response": []}, {"name": "JAMA_12668097_Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/product/{{product_id}}"}, "response": []}, {"name": "JAMA_13318971_Add Publish Product", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["//var moment = require('moment');", "//pm.globals.set(\"todayDate\",moment().add(3, 'day').toISOString());", "//pm.globals.set(\"endDate\", moment().add(4, 'day').toISOString());", "//var moment = require('moment');", "//pm.globals.set(\"todayDate\", moment().add(3, 'day').format(\"YYYY-MM-DD HH:mm\"));", "//pm.globals.set(\"endDate\", moment().add(4, 'day').format(\"YYYY-MM-DD HH:mm\"));", "//let response = pm.response.json();let product_id = response.id;console.log(product_id);pm.environment.set('product_id',product_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "language", "value": "en", "type": "text"}, {"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"products\": [\r\n    {{product_id}}\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/publishProduct"}, "response": []}, {"name": "JAMA_13318969_Add Product Inventory", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["//var moment = require('moment');", "//pm.globals.set(\"todayDate\",moment().add(3, 'day').toISOString());", "//pm.globals.set(\"endDate\", moment().add(4, 'day').toISOString());", "//var moment = require('moment');", "//pm.globals.set(\"todayDate\", moment().add(3, 'day').format(\"YYYY-MM-DD HH:mm\"));", "//pm.globals.set(\"endDate\", moment().add(4, 'day').format(\"YYYY-MM-DD HH:mm\"));", "//let response = pm.response.json();let product_id = response.id;console.log(product_id);pm.environment.set('product_id',product_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "language", "value": "en", "type": "text"}, {"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "[\r\n  {\r\n    \"productId\": {{product_id}},\r\n    \"location\": [\r\n      {\r\n        \"locationId\": 273,\r\n        \"qty\": 100\r\n      }\r\n    ],\r\n    \"qtyThreshold\": 10,\r\n    \"alwaysInStock\": true,\r\n    \"operation\": \"add\"\r\n  }\r\n]", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/product/inventory"}, "response": []}, {"name": "JAMA_13318966_Get Product Inventory", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["//var moment = require('moment');", "//pm.globals.set(\"todayDate\",moment().add(3, 'day').toISOString());", "//pm.globals.set(\"endDate\", moment().add(4, 'day').toISOString());", "//var moment = require('moment');", "//pm.globals.set(\"todayDate\", moment().add(3, 'day').format(\"YYYY-MM-DD HH:mm\"));", "//pm.globals.set(\"endDate\", moment().add(4, 'day').format(\"YYYY-MM-DD HH:mm\"));", "//let response = pm.response.json();let product_id = response.id;console.log(product_id);pm.environment.set('product_id',product_id);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "language", "value": "en", "type": "text"}, {"key": "Authorization", "value": "Bearer {{storeToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "[\r\n  {\r\n    \"productId\": {{product_id}},\r\n    \"location\": [\r\n      {\r\n        \"locationId\": 273,\r\n        \"qty\": 100\r\n      }\r\n    ],\r\n    \"qtyThreshold\": 10,\r\n    \"alwaysInStock\": true,\r\n    \"operation\": \"add\"\r\n  }\r\n]", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/product/inventory"}, "response": []}]}, {"name": "Inventory", "item": [{"name": "JAMA_12668144_Get inventory", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MzQ0MywidHlwZSI6InN0b3JlIiwiY3JlYXRlZCI6MTY1MDQzNzE4NywiZXhwaXJ5IjoxNjgxOTczMTg3LCJkb21haW4iOiIifQ.lg9l70xdMBh1OKqoxrHvJhrKNPv4B-bqCYnoeBUrTS8", "type": "text"}], "url": "{{apiUrl}}/marketplace/v1/pim/product/inventory"}, "response": []}, {"name": "JAMA_12668145_Add/Update Inventory", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "POST", "header": [{"key": "language", "value": "en", "type": "text"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTg4MCwidHlwZSI6InN0b3JlIiwiY3JlYXRlZCI6MTY0NjcyMjU3NywiZXhwaXJ5IjoxNjQ2NzIzNDc3LCJkb21haW4iOiIifQ.NmA6v-W3TkvXkiO1fvS3A6b8lQF9wKG44MvlRBuHXmI\",\n        \"refreshToken\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9", "type": "text"}], "body": {"mode": "raw", "raw": "[\r\n    {\r\n        \"productId\": {{product_id_exclude}},\r\n        \"location\": [\r\n            {\r\n                \"locationId\": {{store_location_id_delete}},\r\n                \"qty\": 10\r\n            }\r\n                    ],\r\n        \"qtyThreshold\": 30,\r\n        \"alwaysInStock\": true,\r\n        \"operation\": \"add\"\r\n    },\r\n\r\n    {\r\n       \"productId\": {{product_id_exclude}},\r\n        \"location\": [\r\n            {\r\n                \"locationId\": {{store_location_id_delete}},\r\n                \"qty\": 100\r\n            },\r\n            {\r\n                \"locationId\": {{store_location_id_delete}},\r\n                \"qty\": 200\r\n            }\r\n        ],\r\n        \"qtyThreshold\": 20,\r\n        \"alwaysInStock\": false,\r\n        \"operation\": \"update\"\r\n    }\r\n]", "options": {"raw": {"language": "json"}}}, "url": "{{apiUrl}}/marketplace/v1/pim/product/inventory"}, "response": []}], "auth": {"type": "bearer", "bearer": {"token": "{{storeToken}}"}}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const options = {", "    'method': 'POST',", "    'url': `${pm.environment.get('apiUrl')}/marketplace/v1/auth/token`,", "    'header': {", "        'Content-Type': 'application/x-www-form-urlencoded'", "    },", "    'body' : {", "        mode: 'raw',", "        raw: {", "            'clientId': pm.environment.get('storeClientId'),", "            'clientSecret': pm.environment.get('storeClientSecret')", "        }", "    }", "};", "pm.sendRequest(options, function (err, response) {", "    // console.log(\"get accesstoken\");", "    // console.log(response.json()[\"data\"][\"token\"]);", "", "    pm.environment.set('storeToken', response.json()['data']['token']);", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"TEST CASE\", function() {", "    if (pm.response.code === 200) {", "        if (pm.response.text().includes(\"already\")) {", "            pm.expect(pm.response.text()).to.include(\"already\");", "        } else {", "            pm.expect(pm.response.code).to.equal(200);", "        }", "    } else if (pm.response.code === 400) {", "        const responseText = pm.response.text();", "                pm.expect(responseText).to.match(/No Products To Update|There are no changes in Catalog|A published store location already exists with same name/);", "", "       // pm.expect(responseText).to.include(\"Invalid Delivery Rule ID.\");", "    }else if (pm.response.code === 400){", "        pm.expect(pm.response.code).to.equal(400);", "    } else if (pm.response.code === 401) {", "        pm.expect(pm.response.code).to.equal(401);", "        // Add additional checks for 401 response if needed", "    } else if (pm.response.code === 404) {", "        pm.expect(pm.response.code).to.equal(404);", "        // Add additional checks for 404 response if needed", "    } else {", "        pm.expect(pm.response.code).to.be.oneOf([200, 400, 401, 404]);", "    }", "});", ""]}}]}