FROM --platform=linux/amd64 python:3.12-alpine

# Install required dependencies
RUN apk add --no-cache \
    nodejs \
    npm \
    curl \
    unzip \
    bash \
    groff \
    less \
    py3-pip \
    && npm install -g newman newman-reporter-htmlextra \
    && pip3 install --no-cache-dir awscli \
    && rm -rf /var/cache/apk/*

# Create working directory
WORKDIR /etc/newman

# docker build --platform linux/arm64   -t gitdock.panasonic.aero:4567/pac/tools/marketplace-ground-pim-server-test/newman-python3.12 . && docker push gitdock.panasonic.aero:4567/pac/tools/marketplace-ground-pim-server-test/newman-python3.12