
variables:
  TRIGGERED_JOB:
    value: ""
    description: "General pipeline variable to specify desired enviroment to test (pac, dev, qa, test)"
  LOGIN_PAGE_RETRIES_ENABLED: 
    value: "1"
    description: "Multi-currency - UI/CSV. If set to 1, it will use a retry mechanishm at the login page for all users"
  TESTS_MARKER:
    value: ""
    description: "Multi-currency - UI/CSV . If left emtpy ALL tests will run. Optional values to run specific tests (product_c, csv, reg)"
  SKIP_JAMA:
    value: "1"
    description: "Set to 1 to skip publishing results to JAMA"
  ENVIRONMENT: 
    value: ""
    description: "Used for nightly runs to set enviroment (dev, qa)"
  SCHEDULED_NIGHTLY:
    value: ""
    description: "Set to any value to schedule nightly runs"
  MAPPER_TEST_ENV:
    value: ""
    description: Enviroment to test mapper/event handler services. Currenlty dev is working

stages:
  - UI Test

multi_currency_ui:
  id_tokens:
    AWS_OIDC_JTW:
      aud: ************.aws-oidc-provider.git.panasonic.aero
  stage: UI Test
  timeout: 1h
  image: gitdock.panasonic.aero:4567/pac/tools/marketplace-ground-pim-server-test/selenium
  variables:
    SELENIUM_DOWNLOAD_PATH: "${CI_PROJECT_DIR}/downloads"
  before_script:
    - |
      echo "TEST... role arn from env variable, $AWS_ROLE_ARN  ***"
      echo "Setting variable AWS_ROLE_ARN"
      if [ "$TRIGGERED_JOB" = "dev" ] || [ "$TRIGGERED_JOB" = "pac" ]; then
        echo "Testing in DEV Account. Setting AWS_ROLE_ARN for that account"
        AWS_ROLE_ARN="arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-${TRIGGERED_JOB}"
      elif [ "$TRIGGERED_JOB" = "qa" ] || [ "$TRIGGERED_JOB" = "test" ]; then
        echo "Testing in TEST Account. Setting AWS_ROLE_ARN for that account"
        AWS_ROLE_ARN="arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-${TRIGGERED_JOB}"
      elif [ "$ENVIRONMENT" = "dev" ] || [ "$MAPPER_TEST_ENV" = "dev" ]; then 
        echo "Testing in DEV ENVIROMENT"
        AWS_ROLE_ARN="arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-dev"
      elif [ "$ENVIRONMENT" = "qa" ]; then 
        echo "Testing in QA ENVIROMENT"
        AWS_ROLE_ARN="arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-qa"
      elif [ "$ENVIRONMENT" = "test" ]; then 
        echo "Testing in QA ENVIROMENT"
        AWS_ROLE_ARN="arn:aws:iam::************:role/mktpl-sqs-gitlab-testing-role-test"
      else
        echo "Unknown environment variable set! Value: $TRIGGERED_JOB"
        exit 1
      fi 
    - echo "The enviroment for testing is $TRIGGERED_JOB"
    - echo "*** Getting AWS credentials using role arn, $AWS_ROLE_ARN ***"
    - >
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s" 
      $(aws sts assume-role-with-web-identity 
      --role-arn $AWS_ROLE_ARN 
      --role-session-name "GitLabSession" 
      --web-identity-token $AWS_OIDC_JTW
      --duration-seconds 18000 
      --query "Credentials.[AccessKeyId,SecretAccessKey,SessionToken]" 
      --output text)) 2>/dev/null
    - echo "AWS credentials exported successfully"
    - pip3 install numpy
    - pip3 install -r ./test/requirements.txt --trusted-host artifinity.nextcloud.aero --index-url https://${ARTIFACTORY_USER}:${ARTIFACTORY_API_KEY}@artifinity.nextcloud.aero/artifactory/api/pypi/pypi/simple
    - /etc/init.d/dbus start
    - Xvfb :99 -screen 0 1280x1024x24 > /dev/null 2>&1 &
    - export DISPLAY=:99
  script:
    - cd ./test/
    - echo "--------------------------------------------------- Starting to test_1_default_currency.py ---------------------------------------------------------" 
    - |
      if [[ -n "$TESTS_MARKER" ]]; then
        echo "Runing tests with marker : $TESTS_MARKER"
        pytest -vvv multi_currency_ui/test_1_default_currency.py -m "$TESTS_MARKER"--junitxml=test_default_currency.xml || true;
        echo "--------------------------------------------------- Starting to test_2_additional_display_currencies.py with marker : $TESTS_MARKER ---------------------------------------------------------"
        pytest -vvv multi_currency_ui/test_2_additional_display_currencies.py -m "$TESTS_MARKER" --junitxml=test_additional_display_currencies.xml || true;
        echo "------------------------------------------------------- Starting to test_3_all_display_currencies.py with marker : $TESTS_MARKER ------------------------------------------------------"
        pytest -vvv multi_currency_ui/test_3_all_display_currencies.py -m "$TESTS_MARKER" --junitxml=test_all_display_currencies.xml
      else  
        echo "Running all tests!" 
        pytest -vvv multi_currency_ui/test_1_default_currency.py --junitxml=test_default_currency.xml || true;
        echo "--------------------------------------------------- Starting to test_2_additional_display_currencies.py ---------------------------------------------------------"
        pytest -vvv multi_currency_ui/test_2_additional_display_currencies.py --junitxml=test_additional_display_currencies.xml || true;
        echo "------------------------------------------------------- Starting to test_3_all_display_currencies.py ------------------------------------------------------"
        pytest -vvv multi_currency_ui/test_3_all_display_currencies.py --junitxml=test_all_display_currencies.xml
      fi
  artifacts:
    when: always
    reports:
      junit: test/test_*.xml
    paths:
      - "./test/*.png"
      - "./test/*.log"
      - "./test/track*.json"
      - "./test/multi_currency_ui/csv_files/*.csv"
      - "**/test_*.png"
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_REF_NAME == "some_non_existant_branch"'  ### All tests will run when triggered manually/TRIGGERED_JOB does not exist. Default will be DEV enviroment
    # - if: '$CI_PIPELINE_SOURCE != "merge_request_event" && ($TRIGGERED_JOB == null || $TRIGGERED_JOB == "")'  ### All tests will run when triggered manually/TRIGGERED_JOB does not exist. Default will be DEV enviroment
    #   when: manual